{{- if not .Values.managed }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "cce-virtual-kubelet.fullname" . }}
  labels:
    {{- include "cce-virtual-kubelet.labels" . | nindent 4 }}
rules:
  - apiGroups:
      - ""
    resources:
      - namespaces
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - admissionregistration.k8s.io
    resources:
      - mutatingwebhookconfigurations
      - validatingwebhookconfigurations
    verbs:
      - create
      - get
      - list
      - watch
      - update
      - delete
      - patch
  - apiGroups:
      - authentication.k8s.io
    resources:
      - tokenreviews
    verbs:
      - create
  - apiGroups:
      - authorization.k8s.io
    resources:
      - localsubjectaccessreviews
      - subjectaccessreviews
    verbs:
      - create
  - apiGroups:
      - ""
    resources:
      - services
    verbs:
      - get
      - list
      - watch
      - create
      - update
      - delete
      - patch
  - apiGroups:
      - ""
    resources:
      - nodes
    verbs:
      - create
      - get
      - list
      - watch
  - apiGroups:
      - ""
    resources:
      - nodes/status
    verbs:
      - patch
      - update
  - apiGroups:
      - ""
    resources:
      - nodes
    verbs:
      - patch
      - update
  - apiGroups:
      - ""
    resources:
      - events
    verbs:
      - create
      - patch
      - update
  - apiGroups:
      - ""
    resources:
      - pods
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - ""
    resources:
      - pods
    verbs:
      - create
      - delete
  - apiGroups:
      - ""
    resources:
      - pods/status
    verbs:
      - patch
      - update
  - apiGroups:
      - ""
    resources:
      - pods/eviction
    verbs:
      - create
  - apiGroups:
      - ""
    resources:
      - configmaps
      - secrets
    verbs:
      - get
      - list
      - watch
      - create
      - update
      - patch
  - apiGroups:
      - ""
    resources:
      - persistentvolumeclaims
      - persistentvolumes
    verbs:
      - get
      - watch
      - list
  - apiGroups:
      - ""
    resources:
      - endpoints
    verbs:
      - get
  - apiGroups:
      - certificates.k8s.io
    resources:
      - certificatesigningrequests
    verbs:
      - create
      - get
      - list
      - watch
  - apiGroups:
      - storage.k8s.io
    resources:
      - volumeattachments
    verbs:
      - get
  - apiGroups:
      - ""
    resources:
      - persistentvolumeclaims/status
    verbs:
      - get
      - patch
      - update
  - apiGroups:
      - ""
    resources:
      - serviceaccounts/token
    verbs:
      - create
  - apiGroups:
      - storage.k8s.io
    resources:
      - csidrivers
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - storage.k8s.io
    resources:
      - csinodes
    verbs:
      - create
      - delete
      - get
      - patch
      - update
  - apiGroups:
      - coordination.k8s.io
    resources:
      - leases
    verbs:
      - create
      - delete
      - get
      - patch
      - update
  - apiGroups:
      - node.k8s.io
    resources:
      - runtimeclasses
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - "apps"
    resources:
      - daemonsets
    verbs:
      - create
      - get
      - list
      - watch
      - update
  - apiGroups:
      - "apps"
    resources:
      - daemonsets/status
    verbs:
      - patch
      - update
  - apiGroups:
      - cce.baidubce.com
    resources:
      - logconfigs
    verbs:
      - get
      - list
      - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  namespace: "kube-system"
  name: {{ include "cce-virtual-kubelet.fullname" . }}
  labels:
    {{- include "cce-virtual-kubelet.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "cce-virtual-kubelet.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "cce-virtual-kubelet.fullname" . }}
    namespace: kube-system

---
apiVersion: v1
kind: ServiceAccount
metadata:
  namespace: "kube-system"
  name: {{ include "cce-virtual-kubelet.fullname" . }}
  labels:
    {{- include "cce-virtual-kubelet.labels" . | nindent 4 }}
{{- end }}