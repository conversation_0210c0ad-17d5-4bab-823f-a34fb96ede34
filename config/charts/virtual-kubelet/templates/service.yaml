apiVersion: v1
kind: Service
metadata:
  labels:
    {{- include "cce-virtual-kubelet.labels" . | nindent 4 }}
    app: bci-virtual-kubelet    
  name: bci-virtual-kubelet-webhook
  {{- if not .Values.managed }}
  namespace: kube-system
  {{- end }}
spec:
  ports:
    - port: 443
      protocol: TCP
      targetPort: {{ .Values.virtualNode.webhookServerPort }}
  selector:
    app: bci-virtual-kubelet
  type: ClusterIP