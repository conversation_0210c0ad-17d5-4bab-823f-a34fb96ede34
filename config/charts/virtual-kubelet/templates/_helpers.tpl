{{/* vim: set filetype=mustache: */}}
{{/*
Expand the name of the chart.
*/}}
{{- define "cce-virtual-kubelet.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Create a default dnsConfig name.
*/}}
{{- define "cce-virtual-kubelet.dnsconfigname" -}}
{{- printf "%s-%s" .Values.virtualNode.nodeNamePrefix "dnsconfig" | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "cce-virtual-kubelet.fullname" -}}
{{- if .Values.virtualNode.nodeNamePrefix -}}
{{- .Values.virtualNode.nodeNamePrefix | trunc 63 | trimSuffix "-" -}}
{{- else -}}
{{- $name := default .Chart.Name .Values.nameOverride -}}
{{- if contains $name .Release.Name -}}
{{- .Release.Name | trunc 63 | trimSuffix "-" -}}
{{- else -}}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" -}}
{{- end -}}
{{- end -}}
{{- end -}}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "cce-virtual-kubelet.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Common labels
*/}}
{{- define "cce-virtual-kubelet.labels" -}}
helm.sh/chart: {{ include "cce-virtual-kubelet.chart" . }}
{{ include "cce-virtual-kubelet.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end -}}

{{/*
Selector labels
*/}}
{{- define "cce-virtual-kubelet.selectorLabels" -}}
app.kubernetes.io/name: {{ include "cce-virtual-kubelet.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end -}}


{{/*
Comma separated logicalZones list
*/}}
{{- define "cce-virtual-kubelet.logicZones" -}}
{{- range .Values.bci.subnets -}}
{{- if .zone }}
{{- .zone -}},
{{- end }}
{{- end -}}
{{- end -}}

{{/*
Comma separated subnets list
*/}}
{{- define "cce-virtual-kubelet.subnetIDs" -}}
{{- range .Values.bci.subnets -}}
{{- if .subnetID }}
{{- .subnetID -}},
{{- end }}
{{- end -}}
{{- end -}}



{{/*
Image tag
*/}}
{{/*
{{- define "cce-virtual-kubelet.imageTag" -}}
{{- if .Values.tag -}}
{{- .Values.tag -}}
{{- else -}}
latest-{{- .Capabilities.KubeVersion.Major -}}.{{- .Capabilities.KubeVersion.Minor -}}
{{- end -}}
{{- end -}}
*/}}