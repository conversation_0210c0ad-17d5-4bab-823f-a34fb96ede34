apiVersion: apps/v1
kind: StatefulSet
metadata:
  {{- if not .Values.managed }}
  namespace: kube-system
  {{- end }}
  name: {{ .Values.virtualNode.nodeNamePrefix }}
  labels:
    {{- include "cce-virtual-kubelet.labels" . | nindent 4 }}
    app: bci-virtual-kubelet
spec:
  replicas: {{ .Values.virtualNode.nodeCount }}
  serviceName: ""
  selector:
    matchLabels:
      {{- include "cce-virtual-kubelet.selectorLabels" . | nindent 6 }}
      app: bci-virtual-kubelet
  template:
    metadata:
      labels:
        {{- include "cce-virtual-kubelet.selectorLabels" . | nindent 8 }}
        app: bci-virtual-kubelet
      {{- if .Values.managed }}
      annotations:
        cross-vpc-eni.cce.io/defaultRouteExcludedCidrs: {{ .Values.crossVPCENI.defaultRouteExcludedCidrs }}
        cross-vpc-eni.cce.io/defaultRouteInterfaceDelegation: {{ .Values.crossVPCENI.defaultRouteInterfaceDelegation }}
        cross-vpc-eni.cce.io/securityGroupIDs: {{ .Values.crossVPCENI.securityGroupIDs }}
        cross-vpc-eni.cce.io/subnetID: {{ .Values.crossVPCENI.subnetID }}
        cross-vpc-eni.cce.io/userID: {{ .Values.crossVPCENI.userID }}
        cross-vpc-eni.cce.io/vpcCidr: {{ .Values.crossVPCENI.vpcCidr }}
      {{- end }}
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: type
                    operator: NotIn
                    values:
                      - virtual-kubelet
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - bci-virtual-kubelet
                topologyKey: kubernetes.io/hostname
              weight: 100
      {{- if not .Values.managed }}
      serviceAccountName: {{ include "cce-virtual-kubelet.fullname" . }}
      {{- end }}
      priorityClassName: system-node-critical
      containers:
        - name: bci-virtual-kubelet
          image: "{{ .Values.image }}"
          imagePullPolicy: Always
          args:
            - "--nodename"
            - "$(NODE_NAME)"
            - "--log-level"
            - "debug"
        {{- if .Values.virtualNode.enableNodeLease }}
            - "--enable-node-lease"
        {{- end }}
          env:
        {{- if .Values.managed }}
            - name: VIRTUAL_KUBELET_MANAGED
              value: "true"
        {{- end }}
            - name: WEBHOOK_SERVER_PORT
              value: "{{ .Values.virtualNode.webhookServerPort }}"
            - name: VNODE_LABELS
              value: "{{ .Values.virtualNode.nodeLabels}}"
            - name: AUTO_INJECT_KUBE_PROXY
              value: "{{ .Values.virtualNode.autoInjectKubeProxy }}"
            - name: POD_CACHE_MAX_REQUESTS
              value: "{{ .Values.virtualNode.podCacheMaxRequestsInFlight }}"
            - name: KUBELET_PORT
              value: "10250"
        {{- if not .Values.managed }}
            - name: VKUBELET_POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
        {{- end }}
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: LOG_ROTATE_COUNT
              value: "{{ .Values.maximumLogBackups }}"
            - name: VKUBELET_TAINT_KEY
              value: "virtual-kubelet.io/provider"
            - name: VKUBELET_TAINT_VALUE
              value: "baidu"
            - name: VKUBELET_TAINT_EFFECT
              value: "NoSchedule"
            - name: VKUBELET_POD_SYNC_WORKERS
              value: "{{ .Values.virtualNode.PodSyncWorkers }}"
            - name: VKUBELET_POD_ENABLE_SERVICE_LINKS
              value: "{{ .Values.virtualNode.enableServiceLinks }}"
            - name: HANDLE_TOPOLOGY
              value: "{{ .Values.virtualNode.handleTopology }}"
            - name: HANDLE_AFFINITY
              value: "{{ .Values.virtualNode.handleAffinity }}"
            - name: BCI_QUOTA_CPU
              value: "{{ .Values.virtualNode.cpuCapacity }}"
            - name: BCI_QUOTA_MEMORY
              value: "{{ .Values.virtualNode.memoryCapacity }}"
            - name: BCI_QUOTA_PODS
              value: "{{ .Values.virtualNode.podsCapacity }}"
            - name: BCI_QUOTA_IPS
              value: "{{ .Values.virtualNode.ipsCapacity }}"
            - name: BCI_QUOTA_ENIS
              value: "{{ .Values.virtualNode.enisCapacity }}"
            - name: BCI_QUOTA_EPHEMERAL_STORAGE
              value: "{{ .Values.virtualNode.ephemeralStorageCapacity }}"
            - name: CCE_GATEWAY_ENDPOINT
              value: "{{ .Values.endpoints.ccegateway }}"
            - name: BCI_REGION
              value: "{{ .Values.region }}"
            - name: BCI_ENDPOINT
              value: "{{ .Values.endpoints.bci }}"
            - name: BCI_LOGICAL_ZONE
              value: "{{ include "cce-virtual-kubelet.logicZones" . | trimSuffix "," }}"
            - name: CCE_CLUSTER_ID
              value: "{{ .Values.clusterID }}"
            - name: BCI_VPC_ID
              value: "{{ .Values.bci.vpcID }}"
            - name: BCI_VPC_UUID
              value: "{{ .Values.bci.vpcUUID }}"
            - name: BCI_SUBNET_ID
              value: "{{ include "cce-virtual-kubelet.subnetIDs" . | trimSuffix "," }}"
            - name: BCI_SECURITY_GROUP_ID
              value: "{{ .Values.bci.securityGroupID }}"
        {{- if not .Values.managed }}
            - name: KUBE_PROXY_CONFIG_PATH
              value: /etc/kube-proxy/kube-proxy.conf
            - name: APISERVER_CERT_LOCATION
              value: /etc/virtual-kubelet/kubelet.pem
            - name: APISERVER_KEY_LOCATION
              value: /etc/virtual-kubelet/kubelet-key.pem
            - name: APISERVER_CA_CERT_LOCATION
              value: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        {{- else }}
            - name: KUBE_PROXY_CONFIG_PATH
              value:  /etc/kubernetes/kubeconfig-vpc/config
            - name: KUBECONFIG
              value: /etc/kubernetes/kubeconfig/config
            - name: NET_INTERFACE_NAME
              value: vk0
            - name: APISERVER_CERT_LOCATION
            - name: APISERVER_KEY_LOCATION
            - name: APISERVER_CA_CERT_LOCATION
        {{- end }}
            - name: API_SERVER_ENDPOINT
              value: "{{ .Values.endpoints.apiserver }}"
        {{- if .Values.virtualNode.enablePodCache }}
            - name: "ENABLE_POD_CACHE"
              value: "true"
        {{- end }}
            - name: "ENABLE_DAEMONSET_INJECT"
              value: "{{ .Values.enableDaemonsetInject }}"
            - name: "SYNC_DAEMONSET_WORKERS"
              value: "{{ .Values.syncDaemonsetWorkers }}"
            - name: "ENABLE_DS_ROLLING_UPGRADE"
              value: "{{ .Values.enableDsRollingUpgrade }}"
            - name: "ENABLE_DEFAULT_ZERO_RESOURCE"
              value: "{{ .Values.virtualNode.enableDefaultZeroResource }}"
        {{- if .Values.multiTenantMode }}
            - name: "BCI_ACCESS_KEY"
              value: "{{ .Values.bciAccessKey }}"
            - name: "BCI_SECRET_KEY"
              value: "{{ .Values.bciSecretKey }}"
            - name: "MULTI_TENANT_MODE"
              value: "{{ .Values.multiTenantMode }}"
            - name: "ASC_RPC_ADDRESS"
              value: "{{ .Values.ascRPCAddress }}"
            - name: "ASC_STS_TOKEN"
              value: "{{ .Values.ascSTSToken }}"
            - name: "CHARGE_APPLICATION"
              value: "{{ .Values.chargeApplication }}"
            - name: "CHARGE_ACCESS_KEY"
              value: "{{ .Values.chargeAccessKey }}"
            - name: "CHARGE_SECRET_KEY"
              value: "{{ .Values.chargeSecretKey }}"
        {{- end }}
          volumeMounts:
        {{- if not .Values.managed }}
            - name: ca-volume
              mountPath: /etc/virtual-kubelet/
            - name: cce-plugin-token
              mountPath: /var/run/secrets/cce/cce-plugin-token
              readOnly: true
            - name: log-volume
              mountPath: /log-volume
            - name: kube-proxy-config-volume
              mountPath: /etc/kube-proxy/
            - name: dnsconfig
              mountPath: /etc/virtual-kubelet/dnsconfig.yaml
              subPath: dnsconfig.yaml
          {{- if .Values.multiTenantMode}}
            - name: user-storage
              mountPath: /userstorage
          {{- end }}
        {{- else }}
            - mountPath: /etc/kubernetes/kubeconfig
              name: kube-config
              readOnly: true
            - mountPath: /etc/kubernetes/kubeconfig-vpc
              name: kube-config-vpc
              readOnly: true
            - mountPath: /tmp/kubernates
              name: tmp
            - name: dnsconfig
              mountPath: /etc/virtual-kubelet/
          resources:
            requests:
              cpu: 200m
              memory: 500Mi
            limits:
              cpu: 500m
              memory: 1500Mi
        {{- end }}
      volumes:
      {{- if not .Values.managed }}
        {{- if .Values.multiTenantMode}}
        - name: user-storage
          persistentVolumeClaim:
            claimName: {{ .Values.pvcName }}
        {{- end }}
        - name: kube-proxy-config-volume
          hostPath:
            path: /etc/kubernetes
            type: Directory
        - name: ca-volume
          hostPath:
            path: /etc/kubernetes/pki/
            type: Directory
        - name: cce-plugin-token
          secret:
            defaultMode: 256
            secretName: cce-plugin-token
        - name: log-volume
          hostPath:
            path: /var/log/virtual-kubelet
            type: DirectoryOrCreate
        - name: dnsconfig
          configMap:
            name: {{ include "cce-virtual-kubelet.dnsconfigname" . }}
            items:
              - key: dnsconfig.yaml
                path: dnsconfig.yaml
      {{- else }}
        - name: kube-config
          configMap:
            name: kube-config-admin
        - name: kube-config-vpc
          configMap:
            name: kube-config-admin-vpc
        - name: tmp
          emptyDir: {}
        - name: dnsconfig
          configMap:
            name: {{ include "cce-virtual-kubelet.dnsconfigname" . }}
      {{- end }}