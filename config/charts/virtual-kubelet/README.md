CCE Virtual Kubelet
=======

CCE Virtual Kubelet在百度云CCE集群中创建[虚拟节点](https://cloud.baidu.com/doc/CCE/s/Xk9cut3x8)，将调度到虚拟节点上的Pod以[BCI](https://cloud.baidu.com/doc/BCI/s/Ujxessavb)形式启动。


## Introduction
------------

[百度智能云容器实例BCI](https://cloud.baidu.com/doc/BCI/s/Ujxessavb)提供无服务器化的容器资源。您只需提供容器镜像及启动容器所需的配置参数，即可运行容器，而无需关心这些容器如何被调度部署到底层的物理服务器资源中。BCI服务将为您完成IaaS层资源的调度和运维工作，从而简化您对容器的使用流程，降低部署和维护成本。同时BCI只会对您创建容器时申请的资源计费，因此实现真正的按需付费。

虚拟节点Virtual Node来源于Kubernetes社区的[virtual-kubelet](https://virtual-kubelet.io/)技术，实现了在CCE集群中调度BCI实例，使用户无需购买物理集群节点即可快速启动和编排容器。启动容器的数量不受物理节点的容量限制，赋予了集群极大的弹性伸缩能力。


## Configuration
---
| Parameter                            | Description                                    | Default |
|--------------------------------------|------------------------------------------------| --- |
| repository                           | virtual-kubelet镜像repository                    | `registry.baidubce.com/cce-plugin-pro/bci-virtual-kubelet` |
| tag                                  | virtual-kubelet组件镜像tag，留空表示使用默认值               | `""` |
| maximumLogBackups                    | 日志保留数                                          | 4 |
| region                               | 集群所在地域                                         | - |
| clusterID                            | CCE集群id                                        | - |
| virtualNode.nodeCount                | 虚拟节点个数                                         | 1 |
| virtualNode.nodeNamePrefix           | 虚拟节点名称前缀，生成的虚拟节点名称为 `${前缀}-0`, `${前缀}-1`,  ... | `bci-virtual-kubelet` |
| virtualNode.cpuCapacity              | 虚拟节点cpu capacity                               | 1000 |
| virtualNode.memoryCapacity           | 虚拟节点memory capacity                            | 4Ti |
| virtualNode.podsCapacity             | 虚拟节点pods capacity                              | 1000 |
| virtualNode.ipsCapacity              | 虚拟节点ip capacity                                | 1000 |
| virtualNode.enisCapacity             | 虚拟节点eni capacity                               | 1000 |
| virtualNode.ephemeralStorageCapacity | 虚拟节点ephemeral storage capacity                 | 40Ti |
| virtualNode.enableNodeLease          | 是否开启`NodeLease`                                | true |
| virtualNode.enablePodCache           | 是否开启`PodCache`                                 | true |
| bci.subnets                          | BCI实例所处子网，至少指定一个，指定多个子网时会自动进行fail over         | - |
| bci.subnets.zone                     | 子网所属可用区, 如 `zoneA`, `zoneB`, ...               | - |
| bci.subnets.subnetID                 | 子网短id                                          | - |
| bci.securityGroupID                  | BCI实例安全组，须与子网同属一个VPC                           | - |
| endpoints                            | api端点，留空表示使用默认值                                | - |

## Changelog
---
- 0.4.7
  - 合并BSC分支
- 0.4.6
  - BCI Pod支持IPv6
- 0.4.5
  - 支持通过CCE Log Operator配置日志采集
- 0.4.4
  - 支持删除pod时查看Killing container事件
- 0.4.3
  - 支持pod可用区打散及及亲和性调度
- 0.4.2
  - 解决后台资源不足vk重复创建pod问题
- 0.4.1
  - vk支持daemonset
- 0.3.27
  - 支持配置多安全组，且对安全组进行校验
- 0.3.26
  - 支持配置忽略Sidecar容器的NotReady状态
- 0.3.25
  - vk-webhook支持vk托管，兼容不阻塞旧helm版本安装vk重启
- 0.3.24
  - bci-profile支持json语法校验，支持自动注入kube-proxy开关，支持禁用自动创建并使用镜像缓存开关配置
- 0.3.23
  - 适配k8s集群service DNS解析
- 0.3.22
  - 支持Pod设置故障处理策略
- 0.3.21
  - 支持vnode节点notready时不驱逐bci pod
  - 支持通过指定标签调度到bci
- 0.3.20
  - 支持k8s 1.30.1版本适配
- 0.3.19
  - 支持用户配置NTP服务
  - 支持强制终止Sidecar容器并忽略容器退出码
- 0.3.18
  - 支持配置资源规整时忽略特定容器
- 0.3.17
  - 支持设置容器终止消息
  - 支持设置容器时区
  - 自定义设置Pod的最大Pending时长
- 0.3.16
  - 接入CCE组件中心；支持托管集群部署时VK组件托管
  - 支持bci-profile, BCI子网/安全组/自定义dnsConfig支持配置热加载
  - 修复sercret/configmap data为空的Pod创建失败的问题
- 0.3.10
    - 优化 kube-proxy 启动耗时
- 0.3.9
    - 优化pod状态同步耗时
- 0.3.8
    - 扩容性能优化，支持优先级队列，优先处理创建任务
    - 修复异常状态pod程序崩溃问题
    - 支持指定enableServiceLinks参数
- 0.3.7
    - 支持CephFS存储挂载
    - 支持指定hostAlias
- 0.3.6
    - 更新kube-proxy镜像版本
- 0.3.5
    - 支持配置自动开启注入 kube-proxy sidecar功能
    - 解决异常状态pod积压问题
- 0.3.4
    - 支持安装多个vk, VNode支持自定义labels
- 0.3.3
    - vk镜像发布改为流水线发布
- 0.3.1
    - 支持 Pod 自定义 DNSConfig
- 0.3.0
    - 支持对接 BCI 2.0
- 0.2.0
    - 去除VPCID/VPCUUID字段配置
    - 默认开启`NodeLease`和`PodCache`
    - 支持挂载CFS PVC
- 0.1.0 初始版本

