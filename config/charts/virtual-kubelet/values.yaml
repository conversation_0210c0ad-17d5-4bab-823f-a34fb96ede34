# Default values for cce-virtual-kubelet.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# Virtual kubelet image. Keep the tag empty to use the default tag.
image: bci-virtual-kubelet-IMAGEID
# Maximum log backups for virtual kubelet. Each log file will consume 256MiB disk space.
maximumLogBackups: "4"
# Region abbreviate. Valid values in baidubce are: bj,su,gz,bd,hkg,fwh
region: ""
# CCE cluster id.
clusterID: ""

# Attributes of BCI pods running on the virtual node.
bci:
  # Subnet list, specify one subnet at least.
  # If multiple subnets are specified, one subnet will be randomly chosen for each pod creation attempt.
  subnets:
    - zone: ""
      subnetID: ""
    - zone: ""
      subnetID: ""
  # Security group applied on pod. Multiple security groups are separated by commas, such as g-1xx,g-2xx
  securityGroupID: ""

# Attributes of virtual node.
virtualNode:
  # Virtual node count to create, default 1.
  nodeCount: 1
  # The virtual nodes will be named as ${nodeNamePrefix}-0, ${nodeNamePrefix}-1, ...
  nodeNamePrefix: "bci-virtual-kubelet"
  # CPU capacity.
  cpuCapacity: "5000"
  # Memory capacity.
  memoryCapacity: "20Ti"
  # Pods capacity.
  podsCapacity: "5000"
  # IPs capacity.
  ipsCapacity: "5000"
  # ENIs capacity.
  enisCapacity: "5000"
  # Ephemeral-storage capacity.
  ephemeralStorageCapacity: "200Ti"
  # Enable NodeLease or not. NodeLease is only available for k8s version > 1.14
  enableNodeLease: true
  # Enable pod cache or not.
  enablePodCache: true
  # Max requests for pod detail in flight for pod cache.
  podCacheMaxRequestsInFlight: "300"
  # VNode label, eg: type=virtual-kubelet
  nodeLabels: ""
  # inject kube-proxy sidecar into your pod, default value: false
  autoInjectKubeProxy: false
  # pod sync workers.
  PodSyncWorkers: "25"
  # Enable service links or not.
  enableServiceLinks: false
  # Enable handle TopologySpreadConstraints or not.
  # 该开关针对集群所有pod，如果非虚拟节点上的pod也使用了TopologySpreadConstraints，则该开关需要设置为false。
  handleTopology: false
  # Enable handle Affinity or not.
  # 该开关针对集群所有pod，如果非虚拟节点上的pod也使用了Affinity，则该开关需要设置为false。
  handleAffinity: false
  # Webhook server port.
  webhookServerPort: "9443"

  # Enable default resource is 0. default is false
  enableDefaultZeroResource: false


# Keep the endpoints empty to use the default values. Override them if needed.
endpoints:
  apiserver: ""
  ccegateway: ""
  bci: ""

dnsConfig: {}

# Enable daemonset inject or not, default false.
enableDaemonsetInject: false
# Enable ds rolling upgrade or not, default false.
enableDsRollingUpgrade: false
# Sync daemonset inject workers.
syncDaemonsetWorkers: "10" # Sync daemonset inject workers

managed: false

# VK pod use cross VPC ENI to access the managed pro cluster.
crossVPCENI:
  defaultRouteExcludedCidrs: ""
  defaultRouteInterfaceDelegation: ""
  securityGroupIDs: ""
  subnetID: ""
  userID: ""
  vpcCidr: ""

# Multi-tenant mode.
multiTenantMode: ""
chargeApplication: ""
chargeAccessKey: ""
chargeSecretKey: ""
# ASC Authentication Service RPC address.
ascRPCAddress: ""
# ASC Authentication Service STS token.
ascSTSToken: ""

bciAccessKey: ""
bciSecretKey: ""
pvcName: ""