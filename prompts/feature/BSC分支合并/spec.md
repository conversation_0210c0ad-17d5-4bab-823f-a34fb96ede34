# 项目目标

目前vk代码包含有两个分支，分别是 `Normal` 和 `BSC`，其中`Normal`分支是线上普通用户分支，`BSC`分支是BSC多租户定制版分支。为了方便后续VK版本管理，现需要将这两个分支合并为一个分支，并统一命名为`Normal`分支。

## Context

1. 两个分支的代码结构已完成合并
2. 
3. 构建两个分支的镜像有所区，
  3.1. 使用  ../../../baidubci/Dockerfile 构建`Normanl`分支镜像
  3.2. 使用 ../../../baidubci/Dockerfile.bsc 构建`BSC`分支镜像

## 任务清单

1. 合并构建两个分支的Dockerfile文件，在同一个文件中根据环境环境变量来区分构建镜像

## 实现文档

将所有生成的详细实现文档及其实现的总结保存到目录：[实现文档列表](./implementations/)