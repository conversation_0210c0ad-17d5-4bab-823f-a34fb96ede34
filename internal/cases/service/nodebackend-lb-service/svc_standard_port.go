// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/02 14:56:00, by lili<PERSON>@baidu.com, create
*/
/*
LB Service NodePort 使用标准端口32222(30000-32767) 集成测试
*/

package nodebackend_lb_service

import (
	"context"
	"errors"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CaseNameSvcStandardPort    cases.CaseName = "LoadBalancerService-NodeBackend-Standard-Port"
	ServiceNameSvcStandardPort                = "lb-svc-node-backend-standard-port"
	SvcYamlSvcStandardPort                    = `
apiVersion: v1
kind: Service
metadata:
  name: [SERVICE_NAME]
spec:
  selector:
    app: [SERVICE_NAME]
  type: LoadBalancer
  externalTrafficPolicy: Cluster
  sessionAffinity: None
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
      nodePort: 50293`
)

func init() {
	cases.AddCase(context.TODO(), CaseNameSvcStandardPort, NewSvcStandardPort)
}

var _ cases.Interface = &svcStandardPort{}

type svcStandardPort struct {
	base *cases.BaseClient
}

func NewSvcStandardPort(ctx context.Context) cases.Interface {
	return &svcStandardPort{}
}

func (c *svcStandardPort) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *svcStandardPort) Name() cases.CaseName {
	return CaseNameSvcStandardPort
}

func (c *svcStandardPort) Desc() string {
	return "LB Service NodePort 使用标准端口32222(30000-32767) 集成测试"
}

func (c *svcStandardPort) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	svcName := ServiceNameSvcStandardPort
	caseName := CaseNameSvcStandardPort

	// 1.部署 Service
	strYml := strings.ReplaceAll(SvcYamlSvcStandardPort, "[SERVICE_NAME]", svcName)
	err := common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, strYml)
	if err != nil {
		logger.Errorf(ctx, "deploy node-backend lb-service %s/%s failed: %s", defaultNamespace, svcName, err)
		return resources, err
	}
	var k8sService common.K8SService
	if err := k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcName); err != nil {
		return resources, err
	}
	errReady := common.WaitForResourceReady(ctx, &k8sService)

	// 2.收集创建的IaaS资源信息
	blbID, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, svcName)
	if blbID != "" {
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blbID,
		})
	}
	eip, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, svcName)
	if eip != "" {
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
	}
	if errReady != nil {
		return resources, errReady
	}

	return resources, err
}

func (c *svcStandardPort) Clean(ctx context.Context) error {
	svcName := ServiceNameSvcStandardPort

	// 1.删除 Service
	err := c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, svcName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Fail to clean svc: %v ", err)
		return err
	}

	return nil
}

func (c *svcStandardPort) Continue(ctx context.Context) bool {
	return true
}

func (c *svcStandardPort) ConfigFormat() string {
	return ""
}
