package nodebackend_lb_service

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	CaseNameSvcBLBReserve    cases.CaseName = "LoadBalancerService-NodeBackend-BLB-Reserve"
	ServiceNameSvcBLBReserve                = "lb-svc-node-backend-blb-reserve"
	SvcYamlSvcBLBReserve                    = `
apiVersion: v1
kind: Service
metadata:
  name: [SERVICE_NAME]
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-id: "[CREATED_BLB]"
    service.beta.kubernetes.io/cce-load-balancer-reserve-lb: "true"
spec:
  selector:
    app: [SERVICE_NAME]
  type: LoadBalancer
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80`
)

func init() {
	cases.AddCase(context.TODO(), CaseNameSvcBLBReserve, NewSvcBLBReserve)
}

var svcBLBIDReserve string
var _ cases.Interface = &svcBLBReserve{}

type svcBLBReserve struct {
	base *cases.BaseClient
}

func NewSvcBLBReserve(ctx context.Context) cases.Interface {
	return &svcBLBReserve{}
}

func (c *svcBLBReserve) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *svcBLBReserve) Check(ctx context.Context) ([]cases.Resource, error) {
	clusterID := c.base.ClusterID
	resources := make([]cases.Resource, 0)
	caseName := CaseNameSvcBLBReserve
	svcName := ServiceNameSvcBLBReserve

	// 1.获取 cluster 的 VPC 下的 subnet
	cluster, err := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "GetCluster %s failed: %s", clusterID, err)
		return resources, err
	}
	logger.Infof(ctx, "GetCluster %s success: %s", clusterID, utils.ToJSON(cluster))
	clusterVPCID := cluster.Cluster.Spec.VPCID
	lbServiceVPCSubnetID := cluster.Cluster.Spec.ContainerNetworkConfig.LBServiceVPCSubnetID

	// 2.创建 BLB, 统计 BLB 资源, 并等待 BLB 状态为 available
	args := &appblb.CreateAppLoadBalancerArgs{
		Name:        svcName,
		VPCID:       clusterVPCID,
		SubNetID:    lbServiceVPCSubnetID,
		AllowDelete: true,
	}
	createdBLB, err := c.base.AppBLBClient.CreateAppLoadBalancer(ctx, args, nil)
	if err != nil || createdBLB.BLBID == "" {
		logger.Errorf(ctx, "CreateBLB failed: %s", err)
		return resources, err
	}
	if createdBLB.BLBID != "" {
		svcBLBIDReserve = createdBLB.BLBID
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       createdBLB.BLBID,
		})
	}
	createdBLBID := createdBLB.BLBID
	logger.Infof(ctx, "createdBLBID: %s", createdBLBID)
	var blbStatus common.AppBLBStatus
	if err = blbStatus.NewAppBLBStatus(ctx, c.base, createdBLBID, "available"); err != nil {
		return resources, err
	}
	if err = common.WaitForResourceReady(ctx, &blbStatus); err != nil {
		return resources, err
	}

	// 3.替换YAML文件中BLB ID, 使用之前创建的BLB部署 LB Service
	svcYaml := strings.ReplaceAll(SvcYamlSvcBLBReserve, "[CREATED_BLB]", createdBLBID)
	svcYaml = strings.ReplaceAll(svcYaml, "[SERVICE_NAME]", svcName)
	err = common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, svcYaml)
	if err != nil {
		logger.Errorf(ctx, "Deploy PodBackend LB %s/%s failed: %s", defaultNamespace, svcName, err)
		return resources, err
	}

	// 4.等待 Ready
	var k8sService common.K8SService
	if err := k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcName); err != nil {
		return resources, err
	}
	errReady := common.WaitForResourceReady(ctx, &k8sService)

	// 5.收集创建的IaaS资源信息 并检验创建的BLB是不是之前指定的BLB
	eip, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, svcName)
	if eip != "" {
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
	}
	blbID, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, svcName)
	if blbID != "" && blbID != createdBLBID {
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blbID,
		})
		// 检验创建的BLB是不是之前指定的BLB
		if blbID != "" && blbID != createdBLBID {
			logger.Errorf(ctx, "Service %s/%s blb: %s not use createdBLBID: %s", defaultNamespace, svcName, blbID, createdBLBID)
			return resources, fmt.Errorf("service blb: %s not use createdBLBID: %s", blbID, createdBLBID)
		}
	}
	if errReady != nil {
		return resources, errReady
	}

	// 6.删除Service,检查BLB是否存在
	err = c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, svcName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Delete service %s/%s failed: %s", defaultNamespace, svcName, err)
	}
	logger.Infof(ctx, "Delete service %s/%s success", defaultNamespace, svcName)

	time.Sleep(15 * time.Second)

	blbInfoResp, err := c.base.AppBLBClient.DescribeAppLoadBalancerByID(ctx, blbID, nil)
	if err != nil || blbInfoResp == nil {
		logger.Errorf(ctx, "DescribeLoadBalancerByID %s failed: %s", blbID, err)
		return resources, err
	}

	logger.Infof(ctx, "Case %s: End", caseName)
	return resources, nil
}

func (c *svcBLBReserve) Name() cases.CaseName {
	return CaseNameSvcBLBReserve
}

func (c *svcBLBReserve) Desc() string {
	return "[待补充]"
}

func (c *svcBLBReserve) Clean(ctx context.Context) error {
	// 1.删除预先创建的BLB
	err := c.base.AppBLBClient.DeleteAppLoadBalancer(ctx, svcBLBIDReserve, nil)
	if err != nil {
		logger.Errorf(ctx, "Fail to clean app blb: %v ", err)
		return err
	}

	return nil
}

func (c *svcBLBReserve) Continue(ctx context.Context) bool {
	return true
}

func (c *svcBLBReserve) ConfigFormat() string {
	return ""
}
