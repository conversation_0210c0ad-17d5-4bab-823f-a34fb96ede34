// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/02 14:56:00, by l<PERSON><PERSON>@baidu.com, create
*/
/*
LB Service 指定 in_vpc blb 集成测试
*/

package nodebackend_lb_service

import (
	"context"
	"errors"
	"net"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	CaseNameSvcVPCBLB    cases.CaseName = "LoadBalancerService-NodeBackend-InternalVPC-BLB"
	ServiceNameSvcVPCBLB                = "lb-svc-node-backend-internalvpc-blb"
	SvcYamlSvcVPCBLB                    = `
kind: Service
apiVersion: v1
metadata:
  name: [SERVICE_NAME]
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-internal-vpc: "true"
spec:
  selector:
    app: [SERVICE_NAME]
  type: LoadBalancer
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80`
)

func init() {
	cases.AddCase(context.TODO(), CaseNameSvcVPCBLB, NewSvcVpcBLB)
}

var _ cases.Interface = &svcVpcBLB{}

type svcVpcBLB struct {
	base *cases.BaseClient
}

func NewSvcVpcBLB(ctx context.Context) cases.Interface {
	return &svcVpcBLB{}
}

func (c *svcVpcBLB) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *svcVpcBLB) Name() cases.CaseName {
	return CaseNameSvcVPCBLB
}

func (c *svcVpcBLB) Desc() string {
	return "LB Service 指定 in_vpc blb 集成测试"
}

func (c *svcVpcBLB) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	caseName := CaseNameSvcVPCBLB
	svcName := ServiceNameSvcVPCBLB

	// 1.部署 Service
	strYml := strings.ReplaceAll(SvcYamlSvcVPCBLB, "[SERVICE_NAME]", svcName)
	err := common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, strYml)
	if err != nil {
		logger.Errorf(ctx, "Deploy Service %s/%s failed: %s", defaultNamespace, svcName, err)
		return nil, err
	}

	// 2.收集创建的IaaS资源信息
	var k8sService common.K8SService
	if err := k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcName); err != nil {
		return nil, err
	}
	errReady := common.WaitForResourceReady(ctx, &k8sService)
	blbID, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, svcName)
	if blbID != "" {
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blbID,
		})
	}
	if errReady != nil {
		return resources, errReady
	}

	// 3.检查BLB的IP地址应该是VPC内IP
	ipInternalVpc, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, svcName)
	clusterID := c.base.ClusterID
	cluster, err := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "GetCluster %s failed: %s", clusterID, err)
		return nil, err
	}
	logger.Infof(ctx, "GetCluster %s success: %s", clusterID, utils.ToJSON(cluster))
	clusterVPCID := cluster.Cluster.Spec.VPCID
	vpcResp, err := c.base.VPCClient.DescribeVPC(ctx, clusterVPCID, nil)
	if err != nil || vpcResp == nil {
		logger.Errorf(ctx, "failed to get VPC %s %v", clusterVPCID, err)
		return resources, err
	}

	_, vpcCIDR, err := net.ParseCIDR(vpcResp.ShowVPCModel.CIDR)
	if err != nil {
		logger.Errorf(ctx, "vpc CIDR %s illegal %v", vpcResp.ShowVPCModel.CIDR, err)
		return resources, err
	}
	ipBLB := net.ParseIP(ipInternalVpc)
	if !vpcCIDR.Contains(ipBLB) {
		logger.Errorf(ctx, "the BLB IP %s is not in VPC CIDR %s", ipInternalVpc, vpcResp.ShowVPCModel.CIDR)
		return resources, err
	}

	// 4.检查InternalVPC LB Service 的地址应与 BLB 的地址一致
	blbInfo, err := c.base.AppBLBClient.DescribeAppLoadBalancerByID(ctx, blbID, nil)
	if err != nil || blbInfo == nil {
		logger.Errorf(ctx, "DescribeLoadBalancerByID %s failed: %s", blbID, err)
		return resources, err
	}
	logger.Infof(ctx, "DescribeLoadBalancerByID %s success: %s", blbID, utils.ToJSON(blbInfo))
	if ipInternalVpc != blbInfo.Address {
		logger.Errorf(ctx, "eip:%s address is not blb:%s address:%s", ipInternalVpc, blbID, blbInfo.Address)
		return resources, err
	}
	logger.Infof(ctx, "eip:%s address is blb:%s address:%s", ipInternalVpc, blbID, blbInfo.Address)

	return resources, nil
}

func (c *svcVpcBLB) Clean(ctx context.Context) error {
	svcName := ServiceNameSvcVPCBLB

	// 1.删除 Service
	err := c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, svcName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Fail to clean svc: %v ", err)
		return err
	}

	return nil
}

func (c *svcVpcBLB) Continue(ctx context.Context) bool {
	return true
}

func (c *svcVpcBLB) ConfigFormat() string {
	return ""
}
