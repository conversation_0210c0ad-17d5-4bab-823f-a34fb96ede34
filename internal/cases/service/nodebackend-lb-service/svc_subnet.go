// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/02 14:56:00, by lili<PERSON>@baidu.com, create
*/
/*
LB Service 指定 subnet 集成测试
*/

package nodebackend_lb_service

import (
	"context"
	"errors"
	"fmt"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/vpc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	CaseNameSvcSubnet    cases.CaseName = "LoadBalancerService-NodeBackend-Subnet"
	ServiceNameSvcSubnet                = "lb-svc-node-backend-subnet"
	SvcYamlSvcSubnet                    = `
apiVersion: v1
kind: Service
metadata:
  name: [SERVICE_NAME]
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-subnet-id: "[CREATED_SUBNET_ID]"
spec:
  selector:
    app: [SERVICE_NAME]
  type: LoadBalancer
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80`
)

func init() {
	cases.AddCase(context.TODO(), CaseNameSvcSubnet, NewSvcSubnet)
}

var _ cases.Interface = &svcSubnet{}

type svcSubnet struct {
	base *cases.BaseClient
}

func NewSvcSubnet(ctx context.Context) cases.Interface {
	return &svcSubnet{}
}

func (c *svcSubnet) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *svcSubnet) Name() cases.CaseName {
	return CaseNameSvcSubnet
}

func (c *svcSubnet) Desc() string {
	return "LB Service 指定 subnet 集成测试"
}

func (c *svcSubnet) Check(ctx context.Context) ([]cases.Resource, error) {
	clusterID := c.base.ClusterID
	resources := make([]cases.Resource, 0)
	caseName := CaseNameSvcSubnet
	svcName := ServiceNameSvcSubnet

	// 1. 获取 cluster 的 VPC 下的 subnet
	cluster, err := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "GetCluster %s failed: %s", clusterID, err)
		return resources, err
	}
	clusterVPCID := cluster.Cluster.Spec.VPCID
	args := &vpc.ListSubnetArgs{
		VPCID:      clusterVPCID,
		SubnetType: vpc.SubnetTypeBCC,
	}
	subnetList, err := c.base.VPCClient.ListSubnet(ctx, args, nil)
	if err != nil {
		logger.Errorf(ctx, "%s ListSubnet failed: %s", clusterVPCID, err)
		return resources, err
	}

	// 2. 查找非 LBServiceVPCSubnetID 子网
	subnetID := ""
	subnetName := ""
	lbServiceVPCSubnetID := cluster.Cluster.Spec.ContainerNetworkConfig.LBServiceVPCSubnetID
	for _, subnet := range subnetList {
		if subnet.SubnetID != lbServiceVPCSubnetID {
			subnetID = subnet.SubnetID
			subnetName = subnet.Name
			break
		}
	}
	if subnetID == "" {
		logger.Errorf(ctx, "VPC %s no suitable subnet", clusterVPCID)
		return resources, fmt.Errorf("VPC %s no suitable subnet", clusterVPCID)
	}
	logger.Infof(ctx, "lbServiceVPCSubnetID: %s, subnetID: %s", lbServiceVPCSubnetID, subnetID)

	// 3. 指定子网ID并部署 LB Service
	strYml := strings.ReplaceAll(SvcYamlSvcSubnet, "[CREATED_SUBNET_ID]", subnetID)
	strYml = strings.ReplaceAll(strYml, "[SERVICE_NAME]", svcName)
	err = common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, strYml)
	if err != nil {
		logger.Errorf(ctx, "Deploy Service %s/%s failed: %s", defaultNamespace, svcName, err)
		return resources, err
	}

	// 4.等待 LB Service BLB 和 EIP Ready, 并统计 BLB/EIP 资源
	var k8sService common.K8SService
	if err = k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcName); err != nil {
		return nil, err
	}
	errReady := common.WaitForResourceReady(ctx, &k8sService)
	blbID, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, svcName)
	if blbID != "" {
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blbID,
		})
	}
	eip, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, svcName)
	if eip != "" {
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
	}
	if errReady != nil {
		return resources, errReady
	}

	// 5.验证 BLB 创建自指定的 subnet 下
	blbInfo, err := c.base.AppBLBClient.DescribeAppLoadBalancerByID(ctx, blbID, nil)
	if err != nil {
		logger.Errorf(ctx, "DescribeAppLoadBalancerByID %s failed: %s", blbID, err)
		return resources, err
	}
	if blbInfo == nil {
		logger.Errorf(ctx, "BlbInfo %s is empty", blbID)
		return resources, fmt.Errorf("blbInfo %s is empty", blbID)
	}
	logger.Infof(ctx, "DescribeAppLoadBalancerByID %s success: %s", blbID, utils.ToJSON(blbInfo))
	if blbInfo.SubNetName != subnetName {
		logger.Errorf(ctx, "Blb %s subnet is %s, not %s", blbID, blbInfo.SubNetName, subnetName)
		return resources, fmt.Errorf("blb %s subnet is %s, not %s", blbID, blbInfo.SubNetName, subnetName)
	}
	logger.Infof(ctx, "Blb %s subnet is %s, equal to %s (SubNetID: %s)", blbID, blbInfo.SubNetName, subnetName, subnetID)

	logger.Infof(ctx, "Case %s: End", caseName)
	return resources, nil
}

func (c *svcSubnet) Clean(ctx context.Context) error {
	svcName := ServiceNameSvcSubnet

	// 1.删除 Service
	err := c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, svcName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Fail to clean svc: %v ", err)
		return err
	}

	return nil
}

func (c *svcSubnet) Continue(ctx context.Context) bool {
	return true
}

func (c *svcSubnet) ConfigFormat() string {
	return ""
}
