package nodebackend_lb_service

import (
	"context"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bccimage"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/internalvpc"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	shellPath        = "/bin/bash"
	defaultNamespace = "default"
)

func deployNewWorkerAndWaitReady(ctx context.Context, clusterID string, baseClient *cases.BaseClient) (string, error) {
	listInstanceArgs := &ccev2.ListInstancesByPageParams{
		PageSize: 10000,
		PageNo:   1,
	}
	instanceResp, err := baseClient.CCEClient.ListInstancesByPage(ctx, clusterID, listInstanceArgs, nil)
	if err != nil {
		return "", err
	}

	vpcSubnetID := instanceResp.InstancePage.InstanceList[0].Spec.VPCConfig.VPCSubnetID
	vpcID := instanceResp.InstancePage.InstanceList[0].Spec.VPCConfig.VPCID
	// bug: https://console.cloud.baidu-int.com/devops/icafe/issue/CCE-12541/show?source=copy-shortcut
	// securityGroupID := instanceResp.InstancePage.InstanceList[0].Spec.VPCConfig.SecurityGroupID
	zone := instanceResp.InstancePage.InstanceList[0].Spec.VPCConfig.AvailableZone
	instanceType := instanceResp.InstancePage.InstanceList[0].Spec.InstanceType
	instanceResource := instanceResp.InstancePage.InstanceList[0].Spec.InstanceResource
	instanceResource.CDSList = ccetypes.CDSConfigList{}

	args := make([]*ccev2.InstanceSet, 0)
	instanceSet := &ccev2.InstanceSet{
		Count: 1,
		InstanceSpec: ccetypes.InstanceSpec{
			AdminPassword: "jhkj4%w@oip",
			Existed:       false,
			MachineType:   ccetypes.MachineTypeBCC,
			InstanceType:  instanceType,
			ClusterRole:   ccetypes.ClusterRoleNode,
			VPCConfig: ccetypes.VPCConfig{
				VPCID:       vpcID,
				VPCSubnetID: vpcSubnetID,
				// SecurityGroupID: securityGroupID,
				AvailableZone: internalvpc.AvailableZone(zone),
				SecurityGroup: ccetypes.SecurityGroup{
					EnableCCERequiredSecurityGroup: true,
				},
			},
			InstanceResource: instanceResource,
			InstanceOS: ccetypes.InstanceOS{
				ImageName: "7.2 x86_64 (64bit)",
				ImageType: bccimage.ImageTypeSystem,
				OSType:    bccimage.OSTypeLinux,
				OSName:    bccimage.OSNameCentOS,
				OSVersion: "7.2",
				OSArch:    "x86_64 (64bit)",
			},
			NeedEIP:              false,
			InstanceChargingType: bcc.PaymentTimingPostpaid,
		},
	}
	createInstanceArgs := append(args, instanceSet)
	resp, err := baseClient.CCEClient.CreateInstances(ctx, clusterID, createInstanceArgs, nil)
	if err != nil {
		return "", err
	}
	if resp == nil || resp.CCEInstanceIDs == nil || len(resp.CCEInstanceIDs) == 0 {
		return "", err
	}
	createInstanceID := resp.CCEInstanceIDs[0]

	// Wait Instance Ready
	var k8sNode common.K8SNode
	if err := k8sNode.NewK8SNode(ctx, baseClient, clusterID, createInstanceID); err != nil {
		return "", err
	}
	k8sNode.SetStatus(string(ccetypes.InstancePhaseRunning))
	err = common.WaitForResourceReady(ctx, &k8sNode)
	if err != nil {
		return createInstanceID, err
	}

	return createInstanceID, nil
}

func deleteWorkerFromCluster(ctx context.Context, clusterID string, instanceID string, cceClient ccev2.Interface) error {
	deleteInstanceArgs := &ccev2.DeleteInstancesRequest{
		DeleteOption: &ccetypes.DeleteOption{
			DeleteCDSSnapshot: true,
			DeleteResource:    true,
			MoveOut:           false,
		},
		InstanceIDs: []string{instanceID},
	}
	_, err := cceClient.DeleteInstances(ctx, clusterID, deleteInstanceArgs, nil)
	return err
}
