// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/02 14:56:00, by lili<PERSON>@baidu.com, create
*/
/*
LB Service 指定 udp eip 集成测试
*/

package nodebackend_lb_service

import (
	"context"
	"errors"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	eipsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CaseNameSvcUDPEIP    cases.CaseName = "LoadBalancerService-NodeBackend-UDP-EIP"
	ServiceNameSvcUDPEIP                = "lb-svc-node-backend-udp-eip"
	SvcYamlSvcUDPEIP                    = `apiVersion: v1
kind: Service
metadata:
  name: [SERVICE_NAME]
spec:
  selector:
    app: [SERVICE_NAME]
  type: LoadBalancer
  loadBalancerIP: [CREATED_EIP]
  ports:
  - port: 3005
    targetPort: 3005
    protocol: UDP`
)

func init() {
	cases.AddCase(context.TODO(), CaseNameSvcUDPEIP, NewSvcUdpEIP)
}

var svcUdpEIPIP string
var _ cases.Interface = &svcUdpEIP{}

type svcUdpEIP struct {
	base *cases.BaseClient
}

func NewSvcUdpEIP(ctx context.Context) cases.Interface {
	return &svcUdpEIP{}
}

func (c *svcUdpEIP) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *svcUdpEIP) Name() cases.CaseName {
	return CaseNameSvcUDPEIP
}

func (c *svcUdpEIP) Desc() string {
	return "LB Service 指定 udp eip 集成测试"
}

func (c *svcUdpEIP) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	caseName := CaseNameSvcUDPEIP
	svcName := ServiceNameSvcUDPEIP

	// 1.创建并收集 EIP 资源, 等待 EIP 状态为 available
	args := &eipsdk.CreateEIPArgs{
		BandwidthInMbps: 1,
		Billing: &eipsdk.Billing{
			PaymentTiming: "Postpaid",
			BillingMethod: "ByTraffic",
		},
		Name: svcName,
	}
	createdEIP, err := c.base.EIPClient.CreateEIP(ctx, args, nil)
	if err != nil {
		logger.Errorf(ctx, "CreateEIP failed: %s", err)
		return resources, err
	}
	if createdEIP != "" {
		svcUdpEIPIP = createdEIP
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeEIP,
			ID:       createdEIP,
		})
	}
	logger.Infof(ctx, "createdEIP: %s", createdEIP)
	var eipStatus common.EIPStatus
	if err = eipStatus.NewEIPStatus(ctx, c.base, createdEIP, "available"); err != nil {
		return resources, err
	}
	if err = common.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	// 2.替换YAML文件中EIP为创建好的EIP, 部署 LB Service
	strYml := strings.ReplaceAll(SvcYamlSvcUDPEIP, "[CREATED_EIP]", createdEIP)
	strYml = strings.ReplaceAll(strYml, "[SERVICE_NAME]", svcName)
	err = common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, strYml)
	if err != nil {
		logger.Errorf(ctx, "Deploy Service %s/%s failed: %s", defaultNamespace, svcName, err)
		return resources, err
	}
	var k8sService common.K8SService
	if err := k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcName); err != nil {
		return nil, err
	}
	errReady := common.WaitForResourceReady(ctx, &k8sService)

	// 3.等待 LB Service BLB 和 EIP Ready, 并统计 BLB 资源
	logger.Infof(ctx, "Case %s: Step collect created IaaS resource", caseName)
	blbID, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, svcName)
	if blbID != "" {
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blbID,
		})
	}
	eip, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, svcName)
	if eip != "" {
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
	}
	if errReady != nil {
		return resources, errReady
	}

	// 4.检查 EIP 状态是否为 binded
	eipStatus.SetStatus("binded")
	if err = common.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	// 5.删除 service, createdEIP 保留，状态为available
	err = c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, svcName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Delete service %s/%s failed: %s", defaultNamespace, svcName, err)
	}
	logger.Infof(ctx, "Delete service %s/%s success", defaultNamespace, svcName)

	// 6.检查 EIP 状态是否为 available
	eipStatus.SetStatus("available")
	if err = common.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	return resources, nil
}

func (c *svcUdpEIP) Clean(ctx context.Context) error {
	// 1.删除创建的 EIP 资源
	err := c.base.EIPClient.DeleteEIP(ctx, svcUdpEIPIP, nil)
	if err != nil {
		logger.Errorf(ctx, "Fail to clean eip: %v ", err)
		return err
	}

	return nil
}

func (c *svcUdpEIP) Continue(ctx context.Context) bool {
	return true
}

func (c *svcUdpEIP) ConfigFormat() string {
	return ""
}
