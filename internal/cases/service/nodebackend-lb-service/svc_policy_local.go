// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/07/16 14:56:00, by <EMAIL>, create
*/
/*
LB Service externalTrafficPolicy=local 集成测试
这个case 涉及到 pod 亲和性，测试集群需保证最少两个 node 才有效
*/

package nodebackend_lb_service

import (
	"context"
	"errors"
	"strings"
	"time"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CaseNameSvcBasicPolicyLocal cases.CaseName = "LoadBalancerService-NodeBackend-PolicyLocal"
	ServiceNameSvcPolicyLocal                  = "lb-svc-node-backend-policy-local"
	SvcYamlSvcPolicyLocal                      = `
kind: Service
apiVersion: v1
metadata:
  name: [SERVICE_NAME]
spec:
  externalTrafficPolicy: Local
  selector:
    app: [SERVICE_NAME]
  type: LoadBalancer
  ports:
    - name: http
      port: 80
      targetPort: 80`
)

func init() {
	cases.AddCase(context.TODO(), CaseNameSvcBasicPolicyLocal, NewSvcPolicyLocal)
}

var _ cases.Interface = &svcPolicyLocal{}

type svcPolicyLocal struct {
	base *cases.BaseClient
}

func NewSvcPolicyLocal(ctx context.Context) cases.Interface {
	return &svcPolicyLocal{}
}

func (c *svcPolicyLocal) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base
	return nil
}

func (c *svcPolicyLocal) Name() cases.CaseName {
	return CaseNameSvcBasicPolicyLocal
}

func (c *svcPolicyLocal) Desc() string {
	return "LB Service externalTrafficPolicy=local 集成测试"
}

func (c *svcPolicyLocal) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	caseName := CaseNameSvcBasicPolicyLocal
	svcName := ServiceNameSvcPolicyLocal
	clusterID := c.base.ClusterID

	// 1.部署Pod 5个
	err := common.DeployExampleNginxDeployment(ctx, c.base, 5, svcName)
	if err != nil {
		logger.Errorf(ctx, "deploy pods fail %v", err)
		return resources, err
	}

	// 2.部署 Service
	strYml := strings.ReplaceAll(SvcYamlSvcPolicyLocal, "[SERVICE_NAME]", svcName)
	err = common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, strYml)
	if err != nil {
		logger.Errorf(ctx, "deploy node-backend lb-service %s/%s failed: %s", defaultNamespace, svcName, err)
		return resources, err
	}
	var k8sService common.K8SService
	if err := k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcName); err != nil {
		return resources, err
	}
	errReady := common.WaitForResourceReady(ctx, &k8sService)

	// 3.收集创建的IaaS资源信息
	blbID, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, svcName)
	if blbID != "" {
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blbID,
		})
	}
	eip, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, svcName)
	if eip != "" {
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
	}
	if errReady != nil {
		return resources, errReady
	}

	// 4.获取当前 pod 占据的 Node 的列表
	var appBLB common.AppBLBStatus
	if err := appBLB.NewAppBLBStatus(ctx, c.base, blbID, "available"); err != nil {
		return resources, err
	}

	var k8sPod common.K8SPod
	if err := k8sPod.NewK8SPod(ctx, c.base, defaultNamespace, nil); err != nil {
		return resources, err
	}
	k8sPod.SetMatchLabel("app", svcName)
	k8sPod.SetStatus(string(v1.PodRunning))

	var appBLBBackends common.AppBLBBackends
	if err := appBLBBackends.NewAppBLBBackends(ctx, c.base, blbID, clusterID, true, k8sPod); err != nil {
		return resources, err
	}

	// 5.获取blb backend server, 并验证是否和 Node 列表一致
	if err = common.WaitForResourceReady(ctx, &appBLBBackends); err != nil {
		return resources, err
	}

	// 6.将 deployment replicas 设置成 1
	err = common.DeployExampleNginxDeployment(ctx, c.base, 1, svcName)
	if err != nil {
		logger.Errorf(ctx, "deploy pods fail %v", err)
		return resources, err
	}

	// 7.验证是否和 Node 列表一致
	if err = common.WaitForResourceReady(ctx, &appBLBBackends); err != nil {
		return resources, err
	}

	// 8.将 deployment replicas 设置成 3
	err = common.DeployExampleNginxDeployment(ctx, c.base, 3, svcName)
	if err != nil {
		logger.Errorf(ctx, "deploy pods fail %v", err)
		return resources, err
	}

	// 9.验证是否和 Node 列表一致
	if err = common.WaitForResourceReady(ctx, &appBLBBackends); err != nil {
		return resources, err
	}

	// 10.将 deployment replicas 设置成 0 清空pod
	err = common.DeployExampleNginxDeployment(ctx, c.base, 0, svcName)
	if err != nil {
		logger.Errorf(ctx, "deploy pods fail %v", err)
		return resources, err
	}

	// 10.验证是否和 Node 列表一致
	time.Sleep(10 * time.Second)
	if err = common.WaitForResourceReady(ctx, &appBLBBackends); err != nil {
		return resources, err
	}

	return resources, err
}

func (c *svcPolicyLocal) Clean(ctx context.Context) error {
	svcName := ServiceNameSvcPolicyLocal

	// 1.删除 Service
	err := c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, svcName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Fail to clean svc: %v ", err)
		return err
	}

	return nil
}

func (c *svcPolicyLocal) Continue(ctx context.Context) bool {
	return true
}

func (c *svcPolicyLocal) ConfigFormat() string {
	return ""
}
