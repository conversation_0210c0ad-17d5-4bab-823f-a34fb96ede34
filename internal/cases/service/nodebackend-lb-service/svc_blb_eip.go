// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/02 14:56:00, by l<PERSON><PERSON>@baidu.com, create
*/
/*
LB Service 指定 blb和eip 集成测试
*/

package nodebackend_lb_service

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	eipsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	CaseNameSvcBLBEIP    cases.CaseName = "LoadBalancerService-NodeBackend-BLB-EIP"
	ServiceNameSvcBLBEIP                = "lb-svc-node-backend-blb-eip"
	SvcYamlSvcBLBEIP                    = `
apiVersion: v1
kind: Service
metadata:
  name: [SERVICE_NAME]
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-id: "[CREATED_BLB]"
    service.beta.kubernetes.io/cce-load-balancer-reserve-lb: "true"
spec:
  selector:
    app: [SERVICE_NAME]
  type: LoadBalancer
  loadBalancerIP: [CREATED_EIP]
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80`
)

func init() {
	cases.AddCase(context.TODO(), CaseNameSvcBLBEIP, NewSvcBLBEIP)
}

var svcBLBEIPBLBID string
var svcBLBEIPIP string
var _ cases.Interface = &svcBLBEIP{}

type svcBLBEIP struct {
	base *cases.BaseClient
}

func NewSvcBLBEIP(ctx context.Context) cases.Interface {
	return &svcBLBEIP{}
}

func (c *svcBLBEIP) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *svcBLBEIP) Name() cases.CaseName {
	return CaseNameSvcBLBEIP
}

func (c *svcBLBEIP) Desc() string {
	return "LB Service 指定 blb和eip 集成测试"
}

func (c *svcBLBEIP) Check(ctx context.Context) ([]cases.Resource, error) {
	clusterID := c.base.ClusterID
	resources := make([]cases.Resource, 0)
	caseName := CaseNameSvcBLBEIP
	svcName := ServiceNameSvcBLBEIP

	// 1.获取 cluster 的 VPC 下的 subnet
	cluster, err := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "GetCluster %s failed: %s", clusterID, err)
		return resources, err
	}
	logger.Infof(ctx, "GetCluster %s success: %s", clusterID, utils.ToJSON(cluster))
	clusterVPCID := cluster.Cluster.Spec.VPCID
	lbServiceVPCSubnetID := cluster.Cluster.Spec.ContainerNetworkConfig.LBServiceVPCSubnetID

	// 2.创建 BLB, 统计 BLB 资源, 并等待 BLB 状态为 available
	args := &appblb.CreateAppLoadBalancerArgs{
		Name:        svcName,
		VPCID:       clusterVPCID,
		SubNetID:    lbServiceVPCSubnetID,
		AllowDelete: true,
	}
	createdBLB, err := c.base.AppBLBClient.CreateAppLoadBalancer(ctx, args, nil)
	if err != nil {
		logger.Errorf(ctx, "CreateBLB failed: %s", err)
		return resources, err
	}
	createdBLBID := createdBLB.BLBID
	if createdBLBID != "" {
		svcBLBEIPBLBID = createdBLBID
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       createdBLBID,
		})
	}
	logger.Infof(ctx, "createdBLBID: %s", createdBLBID)
	var blbStatus common.AppBLBStatus
	if err = blbStatus.NewAppBLBStatus(ctx, c.base, createdBLBID, "available"); err != nil {
		return resources, err
	}
	if err = common.WaitForResourceReady(ctx, &blbStatus); err != nil {
		return resources, err
	}

	// 3. 创建并收集 EIP 资源, 等待 EIP 状态为 available
	bill := &eipsdk.Billing{
		PaymentTiming: "Postpaid",
		BillingMethod: "ByTraffic",
	}
	eipArgs := &eipsdk.CreateEIPArgs{
		BandwidthInMbps: 1,
		Billing:         bill,
		Name:            svcName,
	}
	createdEIP, err := c.base.EIPClient.CreateEIP(ctx, eipArgs, nil)
	if err != nil {
		logger.Errorf(ctx, "CreateEIP failed: %s", err)
		return resources, err
	}
	if createdEIP != "" {
		svcBLBEIPIP = createdEIP
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeEIP,
			ID:       createdEIP,
		})
	}
	logger.Infof(ctx, "createdEIP: %s", createdEIP)
	var eipStatus common.EIPStatus
	if err = eipStatus.NewEIPStatus(ctx, c.base, createdEIP, "available"); err != nil {
		return resources, err
	}
	if err = common.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	// 4.替换YAML文件中BLB ID 与 EIP, 使用之前创建的BLB部署 LB Service
	strYml := strings.ReplaceAll(SvcYamlSvcBLBEIP, "[CREATED_BLB]", createdBLBID)
	strYml = strings.ReplaceAll(strYml, "[CREATED_EIP]", createdEIP)
	strYml = strings.ReplaceAll(strYml, "[SERVICE_NAME]", svcName)
	err = common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, strYml)
	if err != nil {
		logger.Errorf(ctx, "Deploy Service %s/%s failed: %s", defaultNamespace, svcName, err)
		return resources, err
	}

	// 5.等待 LB Service BLB 和 EIP Ready, 并统计 BLB/EIP 资源
	var k8sService common.K8SService
	if err := k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcName); err != nil {
		return resources, err
	}
	errReady := common.WaitForResourceReady(ctx, &k8sService)
	blbID, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, svcName)
	eip, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, svcName)
	if errReady != nil {
		return resources, err
	}
	if blbID != "" && blbID != createdBLBID {
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blbID,
		})
		logger.Errorf(ctx, "Service %s/%s blb: %s not use createdBLBID: %s", defaultNamespace, svcName, blbID, createdBLBID)
		return resources, fmt.Errorf("service blb: %s not use createdBLBID: %s", blbID, createdBLBID)
	}
	if eip != "" && eip != createdEIP {
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
		logger.Errorf(ctx, "Service %s/%s eip: %s not use createdEIP: %s", defaultNamespace, svcName, eip, createdEIP)
		return resources, fmt.Errorf("service eip: %s not use createdEIP: %s", eip, createdEIP)
	}

	// 6.检查 EIP 状态是否为 binded
	eipStatus.SetStatus("binded")
	if err = common.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	// 7.删除Service,检查BLB是否存在
	err = c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, svcName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Delete service %s/%s failed: %s", defaultNamespace, svcName, err)
	}
	logger.Infof(ctx, "Delete service %s/%s success", defaultNamespace, svcName)
	time.Sleep(15 * time.Second)

	blbInfoResp, err := c.base.AppBLBClient.DescribeAppLoadBalancerByID(ctx, blbID, nil)
	if err != nil || blbInfoResp == nil {
		logger.Errorf(ctx, "DescribeLoadBalancerByID %s failed: %s", blbID, err)
		return resources, err
	}

	// 8.检查EIP是否处于Available状态
	eipStatus.SetStatus("available")
	if err = common.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	logger.Infof(ctx, "Case %s: End", caseName)
	return resources, nil
}

func (c *svcBLBEIP) Clean(ctx context.Context) error {
	// 1.删除预先创建的 BLB 与 EIP
	blbErr := c.base.AppBLBClient.DeleteAppLoadBalancer(ctx, svcBLBEIPBLBID, nil)
	eipErr := c.base.EIPClient.DeleteEIP(ctx, svcBLBEIPIP, nil)
	if blbErr != nil || eipErr != nil {
		return fmt.Errorf("delete blb %s err: %s, delete eip %s err: %s", svcBLBEIPBLBID, blbErr, svcBLBEIPIP, eipErr)
	}
	return nil
}

func (c *svcBLBEIP) Continue(ctx context.Context) bool {
	return true
}

func (c *svcBLBEIP) ConfigFormat() string {
	return ""
}
