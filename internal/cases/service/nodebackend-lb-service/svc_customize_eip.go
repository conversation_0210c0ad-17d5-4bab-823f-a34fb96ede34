// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/02 14:56:00, by l<PERSON><PERSON>@baidu.com, create
*/
/*
LB Service 自定义 eip 集成测试
*/

package nodebackend_lb_service

import (
	"context"
	"errors"
	"fmt"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CaseNameSvcEIPCustomized    cases.CaseName = "LoadBalancerService-NodeBackend-EIP-Customized"
	ServiceNameSvcEIPCustomized                = "lb-svc-node-backend-eip-customized"
	SvcYamlSvcEIPCustomized                    = `
apiVersion: v1
kind: Service
metadata:
  name: [SERVICE_NAME]
  annotations:
    service.beta.kubernetes.io/cce-elastic-ip-payment-timing: "Postpaid"
    service.beta.kubernetes.io/cce-elastic-ip-billing-method: "ByTraffic"
    service.beta.kubernetes.io/cce-elastic-ip-bandwidth-in-mbps: "[EIP_BANDWIDTH]"
spec:
  selector:
    app: [SERVICE_NAME]
  type: LoadBalancer
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80`
)

func init() {
	cases.AddCase(context.TODO(), CaseNameSvcEIPCustomized, NewSvcCustomizeEIP)
}

var _ cases.Interface = &svcCustomizeEIP{}

type svcCustomizeEIP struct {
	base *cases.BaseClient
}

func NewSvcCustomizeEIP(ctx context.Context) cases.Interface {
	return &svcCustomizeEIP{}
}

func (c *svcCustomizeEIP) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *svcCustomizeEIP) Name() cases.CaseName {
	return CaseNameSvcEIPCustomized
}

func (c *svcCustomizeEIP) Desc() string {
	return "LB Service 自定义 eip 集成测试"
}

func (c *svcCustomizeEIP) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	caseName := CaseNameSvcEIPCustomized
	svcName := ServiceNameSvcEIPCustomized

	bandwidth := "50"

	// 1.部署 LB Service
	strYml := strings.ReplaceAll(SvcYamlSvcEIPCustomized, "[EIP_BANDWIDTH]", bandwidth)
	strYml = strings.ReplaceAll(strYml, "[SERVICE_NAME]", svcName)
	err := common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, strYml)
	if err != nil {
		logger.Errorf(ctx, "Deploy Service %s/%s failed: %s", defaultNamespace, svcName, err)
		return resources, err
	}

	// 2.等待 LB Service BLB 和 EIP Ready, 并统计 BLB/EIP 资源
	var k8sService common.K8SService
	if err := k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcName); err != nil {
		return nil, err
	}
	errReady := common.WaitForResourceReady(ctx, &k8sService)
	blbID, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, svcName)
	if blbID != "" {
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blbID,
		})
	}
	eip, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, svcName)
	if eip != "" {
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
	}
	if errReady != nil {
		return resources, err
	}

	// 3.检查 EIP 信息是否符合预期(bandwidth := "50")
	var eipBandwidth common.EIPBandwidth
	if err = eipBandwidth.NewEIPBandwidth(ctx, c.base, eip, bandwidth); err != nil {
		return resources, err
	}
	currentBandwidth, err := eipBandwidth.GetEIPBandwidth(ctx, eip)
	if err != nil {
		return resources, err
	}
	if bandwidth != currentBandwidth {
		logger.Errorf(ctx, "Service %s/%s EIP %s bandwidth is %s, not %s", defaultNamespace, svcName, eip, currentBandwidth, bandwidth)
		return resources, fmt.Errorf("service %s/%s EIP %s bandwidth is %s, not %s", defaultNamespace, svcName, eip, currentBandwidth, bandwidth)
	}
	logger.Infof(ctx, "Service %s/%s EIP %s bandwidth is %s, equal to %s", defaultNamespace, svcName, eip, currentBandwidth, bandwidth)

	// 4. 改变 service 中的 EIP 的 bandwidth 等到 EIP bandwidth 更新到最新值(bandwidth := "100")
	bandwidth = "100"
	strYml = strings.ReplaceAll(SvcYamlSvcEIPCustomized, "[EIP_BANDWIDTH]", bandwidth)
	strYml = strings.ReplaceAll(strYml, "[SERVICE_NAME]", svcName)
	err = common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, strYml)
	if err != nil {
		logger.Errorf(ctx, "Deploy Service %s/%s failed: %s", defaultNamespace, svcName, err)
		return resources, err
	}
	eipBandwidth.SetBandwidth(bandwidth)
	if err = common.WaitForResourceReady(ctx, &eipBandwidth); err != nil {
		return resources, err
	}

	logger.Infof(ctx, "Case %s: End", caseName)
	return resources, nil
}

func (c *svcCustomizeEIP) Clean(ctx context.Context) error {
	svcName := ServiceNameSvcEIPCustomized

	// 1.删除 Service
	err := c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, svcName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Fail to clean svc: %v ", err)
		return err
	}

	return nil
}

func (c *svcCustomizeEIP) Continue(ctx context.Context) bool {
	return true
}

func (c *svcCustomizeEIP) ConfigFormat() string {
	return ""
}
