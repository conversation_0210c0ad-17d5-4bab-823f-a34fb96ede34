// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/07/16 14:56:00, by ch<PERSON><EMAIL>, create
2022/05/18 20:00:00, by z<PERSON><PERSON><PERSON>@baidu.com, modify
2022/08/01 12:00:00，by zhuang<PERSON><PERSON><EMAIL>, modify
*/
/*
创建 LB Service 集成测试
*/

package nodebackend_lb_service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/executor"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	// CreateLBServiceCaseName - 创建 LB Service
	CreateLBServiceCaseName cases.CaseName = "CreateLBService"

	// TODO:暂时修改lb名字,待lb-controller截断问题修复后恢复lb-service-autotest-with-default-external-traffic-policy-000000
	createLBServiceName = "lb-service-default"

	// 默认 LB Service
	cceLBServiceYAML = `apiVersion: v1
kind: Service
metadata:
  name: lb-service-default
  namespace: default
  annotations:
    prometheus.io/scrape: "true"
spec:
  selector:
    app: lb-service-default
  type: LoadBalancer
  sessionAffinity: None
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80`

	// nginx deployment yaml
	nginxDeploymentYAML = `apiVersion: apps/v1
kind: Deployment
metadata:
  name: lb-service-default
  labels:
    app: lb-service-default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: lb-service-default
  template:
    metadata:
      labels:
        app: lb-service-default
    spec:
      restartPolicy: Always
      containers:
        - name: nginx
          image: registry.baidubce.com/qa-test/nginx:1.17
          imagePullPolicy: Always
          ports:
            - name: test
              containerPort: 80`
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CreateLBServiceCaseName, NewCreateLBService)
}

/*
TestCaseCreateLoadBalancerService
测试LoadBalancerService的创建、详情、删除功能
@icase.name: CreateLBService
@icase.P0
@icase.step: 1. 创建LoadBalancer类型Service, Service名字长度为63, ExternalTrafficPolicy不填，默认为Cluster
 2. 检查Service基本信息：Name、Namespace、ExternalTrafficPolicy
 3. 检查：BLB状态是否为available，EIP状态是否为binded
 4. 检查：blb backend server是否和集群Node列表一致
 5. 对应blb修改name和description后，同步service后，不对blb进行更新
 6. 校验 TCP/UDP 监听器冲突报错
    EX1: 统计lb-service的创建控制面数据
 5. 检查：网络联通
 6. 删除service
 7. 检查：BLB、EIP是否被清理
 8. 检查：EIP是否进入回收站
    EX2: 统计lb-service的清理控制面数据
*/
func TestCaseCreateLoadBalancerService() {
	return
}

type createLBService struct {
	base   *cases.BaseClient
	shell  executor.Executor
	config lbServiceConfig
}

type lbServiceConfig struct {
	IsArm bool `json:"isArm"`

	// 预期BLB group id
	CheckBLBLayer4ClusterID string `json:"expectBLBLayer4ClusterID"`

	CheckBLBLayer7ClusterID string `json:"expectBLBLayer7ClusterID"`
}

// NewCreateLBService - 测试案例
func NewCreateLBService(ctx context.Context) cases.Interface {
	return &createLBService{
		shell: executor.NewExecutor(shellPath),
	}
}

func (c *createLBService) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base

	var cfg lbServiceConfig
	if err := json.Unmarshal(config, &cfg); err != nil {
		return fmt.Errorf("json.Unmarshal lbservice config failed: %s", err)
	}
	c.config = cfg

	return nil
}

func (c *createLBService) Name() cases.CaseName {
	return CreateLBServiceCaseName
}

func (c *createLBService) Desc() string {
	return "测试LoadBalancerService的创建、详情、删除功能"
}

func (c *createLBService) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	var err error

	// 1.创建LoadBalancer类型Service, Service名字长度为63, ExternalTrafficPolicy不填，默认为Cluster
	yaml := nginxDeploymentYAML
	if c.config.IsArm {
		yaml = strings.ReplaceAll(nginxDeploymentYAML, "nginx:", "nginx-arm64:")
	}
	// 部署pod
	_, err = c.base.AppClient.PostAppResource(ctx, c.base.ClusterID, createLBServiceName, yaml, defaultNamespace, nil)
	if err != nil {
		logger.Errorf(ctx, "Create Pod failed: %s", err)
		return nil, err
	}

	// 等待pod running
	var k8sPod resource.K8SPod
	if err := k8sPod.NewK8SPod(ctx, c.base, defaultNamespace, nil); err != nil {
		return resources, err
	}
	k8sPod.SetMatchLabel("app", createLBServiceName)
	k8sPod.SetStatus(resource.StatusRunning)
	if err := resource.WaitForResourceReady(ctx, &k8sPod); err != nil {
		return resources, err
	}

	_, err = c.base.AppClient.PostAppResource(ctx, c.base.ClusterID, createLBServiceName, cceLBServiceYAML, defaultNamespace, nil)
	if err != nil {
		logger.Errorf(ctx, "Create Service failed: %s", err)
		return nil, err
	}

	// 等待 LB Service BLB 和 EIP Ready
	var k8sService resource.K8SService
	if err := k8sService.NewK8SService(ctx, c.base, defaultNamespace, createLBServiceName); err != nil {
		return nil, err
	}
	if err := resource.WaitForResourceReady(ctx, &k8sService); err != nil {
		logger.Errorf(ctx, "wait for lb service ready failed: %v", err.Error())
		// 输出service的事件
		eventErr := k8sService.GetServiceEvents(ctx, c.base.ClusterID)
		if eventErr != nil {
			return resources, eventErr
		}
		return resources, err
	}
	blb, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, createLBServiceName)
	eip, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, createLBServiceName)

	// 统计 BLB/EIP 资源
	if blb != "" {
		resources = append(resources, cases.Resource{
			CaseName: CreateLBServiceCaseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blb,
		})
	}
	if eip != "" {
		resources = append(resources, cases.Resource{
			CaseName: CreateLBServiceCaseName,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
	}

	// 检查BLB所处4层集群信息（LCC测试）
	if c.config.CheckBLBLayer4ClusterID != "" || c.config.CheckBLBLayer7ClusterID != "" {
		blbInfo, err := c.base.AppBLBClient.DescribeAppLoadBalancerByID(ctx, blb, nil)
		if err != nil {
			logger.Errorf(ctx, "%s AppBLB DescribeAppLoadBalancerByID failed: %s", blb, err)
			return resources, fmt.Errorf("%s AppBLB DescribeAppLoadBalancerByID failed: %s", blb, err)
		}
		if blbInfo.Layer4ClusterID != nil {
			logger.Infof(ctx, "AppBLB %s Layer4ClusterId: %s", blb, *blbInfo.Layer4ClusterID)
		}
		if blbInfo.Layer7ClusterID != nil {
			logger.Infof(ctx, "AppBLB %s Layer7ClusterId: %s", blb, *blbInfo.Layer7ClusterID)
		}
		if *blbInfo.Layer4ClusterID != c.config.CheckBLBLayer4ClusterID {
			return resources, fmt.Errorf("AppBLB %s Layer4ClusterID is %s , not as expect: %s", blb, *blbInfo.Layer4ClusterID, c.config.CheckBLBLayer4ClusterID)
		}
	}

	// 2.检查Service基本信息：Name、Namespace、ExternalTrafficPolicy
	k8sService.SetExternalTrafficPolicy(corev1.ServiceExternalTrafficPolicyTypeCluster)
	if err = k8sService.CheckInfo(ctx); err != nil {
		return resources, err
	}

	// 3.检查：BLB状态是否为available，检查EIP状态是否为binded
	var blbStatus resource.AppBLBStatus
	if err = blbStatus.NewAppBLBStatus(ctx, c.base, blb, "available"); err != nil {
		return resources, err
	}
	if err = resource.WaitForResourceReady(ctx, &blbStatus); err != nil {
		return resources, err
	}
	blbName := blbStatus.BLBName

	var eipStatus resource.EIPStatus
	if err = eipStatus.NewEIPStatus(ctx, c.base, eip, "binded"); err != nil {
		return resources, err
	}
	if err = resource.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	// // 4.检查：blb backend server是否和集群Node列表一致
	// TODO:暂时取消校验，待问题修复后恢复
	// var appBLBBackends common.AppBLBBackends
	// if err := appBLBBackends.NewAppBLBBackends(ctx, c.base, blb, c.base.ClusterID, false, k8sPod); err != nil {
	//	return resources, err
	// }
	// err = common.WaitForResourceReady(ctx, &appBLBBackends)
	// if err != nil {
	//	logger.Errorf(ctx, "check blb backends is equal to cluster worker fail: %v", err)
	//	return resources, err
	// }

	// 5. 对应blb修改name和description后，同步service后，不对blb进行更新
	if err = blbStatus.UpdateAppBLB(ctx, "reg-test", "reg test description"); err != nil {
		return resources, err
	}
	_, err = c.base.K8SClient.CoreV1().Services(defaultNamespace).Patch(ctx, createLBServiceName, types.MergePatchType, []byte(`{"metadata":{"annotations":{"test1":"reg1"}}}`), metav1.PatchOptions{})
	if err != nil {
		logger.Errorf(ctx, "update lb-service faileed: %v", err.Error())
		return resources, err
	}
	if err = blbStatus.CheckAppBLBNameAndDescNotChanged(ctx, "reg-test", "reg test description"); err != nil {
		return resources, err
	}
	logger.Infof(ctx, "check blb name and desc not changed when lb-service has synced success")

	// 6. 校验 TCP/UDP 监听器冲突报错 （冲突器变更过快，关闭该用例）
	// if err = blbStatus.DeleteAppBLBListener(ctx, 80); err != nil {
	//	return resources, err
	// }
	// if err = blbStatus.AddAppHTTPListener(ctx, 80); err != nil {
	//	return resources, err
	// }
	// _, err = c.base.K8SClient.CoreV1().Services(defaultNamespace).Patch(ctx, createLBServiceName, types.MergePatchType, []byte(`{"metadata":{"annotations":{"test2":"reg2"}}}`), metav1.PatchOptions{})
	// if err != nil {
	//	logger.Errorf(ctx, "update lb-service faileed: %v", err.Error())
	//	return resources, err
	// }
	// // 使用lb-controller 日志进行事件的采集，k8s event偶尔会采集不到事件
	// var lbControllerPod common.K8SPod
	// if err := lbControllerPod.NewK8SPod(ctx, c.base, "kube-system", nil); err != nil {
	//	return resources, err
	// }
	// lbControllerPod.SetMatchLabel("k8s-app", "cce-lb-controller")
	// lbControllerLog, err := lbControllerPod.GetPodLogs(ctx, lbControllerPod.GetPodName(), &corev1.PodLogOptions{})
	// if err != nil {
	//	logger.Errorf(ctx, "get cce-lb-controller pod logs failed: %v", err)
	//	return resources, err
	// }
	// if !strings.Contains(lbControllerLog, "ListenerAlreadyExist") {
	//	logger.Errorf(ctx, "cce-lb-controller logs do not have msg \"ListenerAlreadyExist\"")
	//	return resources, nil
	// }
	// logger.Infof(ctx, "cce-lb-controller logs have msg \"ListenerAlreadyExist\"")

	// EX1: 统计lb-service的创建控制面数据
	// stableSelector := common.NewStableSelector(c.base)
	// if err := stableSelector.SelectLBServiceData(ctx, defaultNamespace, createLBServiceName, stable.CreateServiceOperation); err != nil {
	//	return resources, err
	// }

	// 5. 检查：网络联通
	ok := false
	var cmd string
	for i := 0; i < 20; i++ {
		time.Sleep(10 * time.Second)
		cmd = fmt.Sprintf("curl %s:80", eip)
		stdout, _, err := c.shell.ShellExec(ctx, cmd)
		if err != nil {
			logger.Errorf(ctx, "ShellExec[%s] failed: %v ", cmd, err)
			continue
		}
		if !strings.Contains(stdout, "Welcome to nginx") {
			logger.Errorf(ctx, "invite nginx service failed")
			continue
		}
		ok = true
		break
	}

	if !ok {
		return resources, fmt.Errorf("ShellExec[%s] failed: %v ", cmd, err)
	}

	// 6.删除service
	/* lb-service的删除默认会将eip移至回收站；
	   但回收站的默认存放数量为10，有新eip删除时，metaEIP会将回收站里最旧的eip进行删除；
	   在回归并发较高时，lb-controller里的eip的删除偶发会报错；
	   lb-controller在删除eip失败时，重试会根据blb的name删除eip（组件认为blbName=EipName）;
	   这里将blbName改为原来的Name使得组件能够正常删除以上情况下的EIP，从而不阻塞回归
	*/
	if err = blbStatus.UpdateAppBLB(ctx, blbName, "reg test description"); err != nil {
		return resources, err
	}
	err = c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, createLBServiceName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "delete service %s/%s failed:%v", defaultNamespace, createLBServiceName, err)
		return resources, err
	}
	var k8sServiceStatus resource.K8SServiceStatus
	if err := k8sServiceStatus.NewK8SServiceStatus(ctx, c.base, defaultNamespace, createLBServiceName); err != nil {
		return nil, err
	}
	if err = resource.WaitForResourceReady(ctx, &k8sServiceStatus); err != nil {
		logger.Errorf(ctx, "delete service %s/%s failed:%v", defaultNamespace, createLBServiceName, err)
		return resources, err
	}

	// 7.检查：BLB、EIP是否被清理
	blbStatus.SetStatus("deleted")
	if err = resource.WaitForResourceReady(ctx, &blbStatus); err != nil {
		return resources, err
	}
	eipStatus.SetStatus("deleted")
	if err = resource.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	// 检查：EIP是否进入回收站
	if isInRecycleBin, err := eipStatus.CheckIsInRecycleBin(ctx); err != nil || !isInRecycleBin {
		return resources, err
	}

	// EX2: 统计lb-service的清理控制面数据
	// if err := stableSelector.SelectLBServiceData(ctx, defaultNamespace, createLBServiceName, stable.DeleteServiceOperation); err != nil {
	//	return resources, err
	// }

	// 删除回收站里的EIP
	logger.Infof(ctx, "delete eip %s in recycle bin", eip)
	if isSuccessDelete, err := eipStatus.DeleteEIPInRecycleBin(ctx, eip); err != nil || !isSuccessDelete {
		return resources, err
	}
	return resources, err
}

func (c *createLBService) Clean(ctx context.Context) error {
	return c.base.K8SClient.AppsV1().Deployments(defaultNamespace).Delete(ctx, createLBServiceName, metav1.DeleteOptions{})
}

func (c *createLBService) Continue(ctx context.Context) bool {
	return true
}

func (c *createLBService) ConfigFormat() string {
	return ""
}
