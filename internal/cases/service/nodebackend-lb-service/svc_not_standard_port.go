// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/02 14:56:00, by lili<PERSON>@baidu.com, create
*/
/*
LB Service NodePort 使用非标准端口8222(30000-32767) 集成测试
*/

package nodebackend_lb_service

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
)

const (
	CaseNameSvcNotStandardPort    cases.CaseName = "LoadBalancerService-NodeBackend-Not-Standard-Port"
	ServiceNameSvcNotStandardPort                = "lb-svc-node-backend-not-standard-port"
	SvcYamlSvcNotStandardPort                    = `
apiVersion: v1
kind: Service
metadata:
  name: [SERVICE_NAME]
spec:
  selector:
    app: [SERVICE_NAME]
  type: LoadBalancer
  externalTrafficPolicy: Cluster
  sessionAffinity: None
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
      nodePort: 8222`
)

func init() {
	cases.AddCase(context.TODO(), CaseNameSvcNotStandardPort, NewSvcNotStandardPort)
}

var _ cases.Interface = &svcNotStandardPort{}

type svcNotStandardPort struct {
	base *cases.BaseClient
}

func NewSvcNotStandardPort(ctx context.Context) cases.Interface {
	return &svcNotStandardPort{}
}

func (c *svcNotStandardPort) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *svcNotStandardPort) Name() cases.CaseName {
	return CaseNameSvcNotStandardPort
}

func (c *svcNotStandardPort) Desc() string {
	return "LB Service NodePort 使用非标准端口8222(30000-32767) 集成测试"
}

func (c *svcNotStandardPort) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	svcName := ServiceNameSvcNotStandardPort

	// 1. 部署 LB Service 应该是部署失败的
	strYml := strings.ReplaceAll(SvcYamlSvcNotStandardPort, "[SERVICE_NAME]", svcName)
	err := common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, strYml)
	if err != nil {
		return resources, nil
	}
	return resources, fmt.Errorf("NodePort=8222, Deploy Service %s/%s should fail, but success", defaultNamespace, svcName)
}

func (c *svcNotStandardPort) Clean(ctx context.Context) error {
	return nil
}

func (c *svcNotStandardPort) Continue(ctx context.Context) bool {
	return true
}

func (c *svcNotStandardPort) ConfigFormat() string {
	return ""
}
