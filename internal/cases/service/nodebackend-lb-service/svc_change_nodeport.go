// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/02 14:56:00, by lili<PERSON>@baidu.com, create
*/
/*
更改 LB Service 的 node_port 集成测试
*/

package nodebackend_lb_service

import (
	"context"
	"errors"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	CaseNameSvcChangeNodePort    cases.CaseName = "LoadBalancerService-NodeBackend-Change-NodePort"
	ServiceNameSvcChangeNodePort                = "lb-svc-node-backend-change-node-port"
	SvcYamlSvcChangeNodePort                    = `
apiVersion: v1
kind: Service
metadata:
  name: [SERVICE_NAME]
spec:
  selector:
    app: [SERVICE_NAME]
  type: LoadBalancer
  ports:
  - name: http
    port: 80
    targetPort: 80`
)

func init() {
	cases.AddCase(context.TODO(), CaseNameSvcChangeNodePort, NewSvcChangeNodePort)
}

var _ cases.Interface = &svcChangeNodePort{}

type svcChangeNodePort struct {
	base *cases.BaseClient
}

func NewSvcChangeNodePort(ctx context.Context) cases.Interface {
	return &svcChangeNodePort{}
}

func (c *svcChangeNodePort) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *svcChangeNodePort) Name() cases.CaseName {
	return CaseNameSvcChangeNodePort
}

func (c *svcChangeNodePort) Desc() string {
	return "更改 LB Service 的 node_port 集成测试"
}

func (c *svcChangeNodePort) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	caseName := CaseNameSvcChangeNodePort
	svcName := ServiceNameSvcChangeNodePort

	// 1.部署 Service
	strYml := strings.ReplaceAll(SvcYamlSvcChangeNodePort, "[SERVICE_NAME]", svcName)
	err := common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, strYml)
	if err != nil {
		logger.Errorf(ctx, "deploy node-backend lb-service %s/%s failed: %s", defaultNamespace, svcName, err)
		return resources, err
	}
	var k8sService common.K8SService
	if err := k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcName); err != nil {
		return resources, err
	}
	errReady := common.WaitForResourceReady(ctx, &k8sService)

	// 2.收集创建的IaaS资源信息
	blbID, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, svcName)
	if blbID != "" {
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blbID,
		})
	}
	eip, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, svcName)
	if eip != "" {
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
	}
	if errReady != nil {
		return resources, errReady
	}

	// 3.获取原始的 NodePort 的值
	service, err := c.base.K8SClient.CoreV1().Services(defaultNamespace).Get(ctx, svcName, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "Get Service %s/%s failed: %s", defaultNamespace, svcName, err)
		return resources, err
	}
	logger.Infof(ctx, "Get service success: %s", utils.ToJSON(service))
	nodePort := service.Spec.Ports[0].NodePort
	logger.Infof(ctx, "Before change, nodePort: %d", nodePort)

	// 4.获取原始的 BLB Port 的值，并和 NodePort 比较
	var blbPort common.AppBLBPort
	if err := blbPort.NewAppBLBPort(ctx, c.base, blbID, nodePort); err != nil {
		return resources, err
	}
	if backendPort, err := blbPort.GetAppBLBPort(ctx); err != nil || int32(nodePort) != int32(backendPort) {
		return resources, err
	}

	// 5.更新 service 的 NodePort 值
	service.Spec.Ports[0].NodePort = 0
	service, err = c.base.K8SClient.CoreV1().Services(defaultNamespace).Update(ctx, service, metav1.UpdateOptions{})
	if err != nil {
		logger.Errorf(ctx, "Update Service %s/%s failed: %s", defaultNamespace, svcName, err)
		return resources, err
	}
	nodePort = service.Spec.Ports[0].NodePort
	logger.Infof(ctx, "After change, nodePort: %d", nodePort)

	// 6.检查 BLB 的 backendPort 是否同步
	if err := blbPort.NewAppBLBPort(ctx, c.base, blbID, nodePort); err != nil {
		return resources, err
	}
	if err = common.WaitForResourceReady(ctx, &blbPort); err != nil {
		return resources, err
	}

	return resources, nil
}

func (c *svcChangeNodePort) Clean(ctx context.Context) error {
	svcName := ServiceNameSvcChangeNodePort

	// 1.删除 Service
	err := c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, svcName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Fail to clean svc: %v ", err)
		return err
	}

	return nil
}

func (c *svcChangeNodePort) Continue(ctx context.Context) bool {
	return true
}

func (c *svcChangeNodePort) ConfigFormat() string {
	return ""
}
