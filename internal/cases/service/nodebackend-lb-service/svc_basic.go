package nodebackend_lb_service

import (
	"context"
	"errors"
	"strings"
	"time"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CaseNameSvcBasic    cases.CaseName = "LoadBalancerService-NodeBackend-Basic"
	ServiceNameSvcBasic                = "lb-svc-node-backend-basic"
	SvcYamlSvcBasic                    = `
apiVersion: v1
kind: Service
metadata:
  name: [SERVICE_NAME]
  namespace: default
spec:
  externalTrafficPolicy: Cluster
  selector:
    app: [SERVICE_NAME]
  type: LoadBalancer
  sessionAffinity: None
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80`
)

func init() {
	cases.AddCase(context.TODO(), CaseNameSvcBasic, NewSvcBasic)
}

/*
TestCaseLoadBalancerServicePolicyCluster
测试LoadBalancerService外部流量策略Cluster模式
@icase.name: LoadBalancerService-NodeBackend-Basic
@icase.P1
@icase.step: 1. 创建LoadBalancer类型Service, ExternalTrafficPolicy为Cluster, 等待Service相关Iaas资源Ready
 2. 收集创建的IaaS资源信息
 3. 创建Deployment, 1Pods, 等待running
 4. 检查：blb backend server是否和集群Node列表一致
 5. 集群增加一个节点，检查blb backend是否更新
 6. 扩容 Deployment为5Pod, 检查blb backend
 7. 集群删除一个节点，检查blb backend是否更新
 8. 缩容 Deployment为0Pod, 检查blb backend
 9. 删除 Service
    10.检查：BLB、EIP是否被清理
    11.Clean：删除 Deployment
*/
func TestCaseLoadBalancerServicePolicyCluster() {
	return
}

var _ cases.Interface = &svcBasic{}

type svcBasic struct {
	base *cases.BaseClient
}

func NewSvcBasic(ctx context.Context) cases.Interface {
	return &svcBasic{}
}

func (c *svcBasic) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *svcBasic) Name() cases.CaseName {
	return CaseNameSvcBasic
}

func (c *svcBasic) Desc() string {
	return "测试LoadBalancerService外部流量策略Cluster模式"
}

func (c *svcBasic) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	caseName := CaseNameSvcBasic
	svcName := ServiceNameSvcBasic
	clusterID := c.base.ClusterID

	// 1.部署 Service
	strYml := strings.ReplaceAll(SvcYamlSvcBasic, "[SERVICE_NAME]", svcName)
	err := common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, strYml)
	if err != nil {
		logger.Errorf(ctx, "deploy node-backend lb-service %s/%s failed: %s", defaultNamespace, svcName, err)
		return resources, err
	}
	var k8sService common.K8SService
	if err := k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcName); err != nil {
		return resources, err
	}
	errReady := common.WaitForResourceReady(ctx, &k8sService)

	// 2.收集创建的IaaS资源信息
	blbID, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, svcName)
	if blbID != "" {
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blbID,
		})
	}
	eip, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, svcName)
	if eip != "" {
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
	}
	if errReady != nil {
		return resources, errReady
	}

	// 3.检查：blb backend server是否和集群Node列表一致
	var k8sPod common.K8SPod
	if err := k8sPod.NewK8SPod(ctx, c.base, defaultNamespace, nil); err != nil {
		return resources, err
	}
	k8sPod.SetMatchLabel("app", svcName)
	k8sPod.SetStatus(string(v1.PodRunning))

	var appBLBBackends common.AppBLBBackends
	if err := appBLBBackends.NewAppBLBBackends(ctx, c.base, blbID, clusterID, false, k8sPod); err != nil {
		return resources, err
	}

	// 4.部署Deployment，1Pods, 等待running
	err = common.DeployExampleNginxDeployment(ctx, c.base, 1, svcName)
	if err != nil {
		logger.Errorf(ctx, "deploy pods fail %v", err)
		return resources, err
	}
	err = common.WaitForResourceReady(ctx, &appBLBBackends)
	if err != nil {
		logger.Errorf(ctx, "check blb backends is equal to cluster worker fail: %v", err)
		return resources, err
	}

	// 5.增加1个节点，检查后端
	createdInstanceID, err := deployNewWorkerAndWaitReady(ctx, clusterID, c.base)
	if err != nil {
		logger.Errorf(ctx, "deploy new worker for cluster fail: %v", err)
		return resources, err
	}
	time.Sleep(180 * time.Second)
	err = common.WaitForResourceReady(ctx, &appBLBBackends)
	if err != nil {
		logger.Errorf(ctx, "check blb backends is equal to cluster worker fail: %v", err)
		return resources, err
	}

	// 6.增加Pod，检查后端
	err = common.DeployExampleNginxDeployment(ctx, c.base, 5, svcName)
	if err != nil {
		logger.Errorf(ctx, "deploy pods fail %v", err)
		return resources, err
	}
	err = common.WaitForResourceReady(ctx, &appBLBBackends)
	if err != nil {
		logger.Errorf(ctx, "check blb backends is equal to cluster worker fail: %v", err)
		return resources, err
	}

	// 7.减少一个节点，检查后端
	err = deleteWorkerFromCluster(ctx, clusterID, createdInstanceID, c.base.CCEClient)
	if err != nil {
		logger.Errorf(ctx, "delete worker form cluster fail %v", err)
		return resources, err
	}
	err = common.WaitForResourceReady(ctx, &appBLBBackends)
	if err != nil {
		logger.Errorf(ctx, "check blb backends is equal to cluster worker fail: %v", err)
		return resources, err
	}

	// 8.清空Pod，检查后端
	err = common.DeployExampleNginxDeployment(ctx, c.base, 0, svcName)
	if err != nil {
		logger.Errorf(ctx, "clear pods fail %v", err)
		return resources, err
	}
	err = common.WaitForResourceReady(ctx, &appBLBBackends)
	if err != nil {
		logger.Errorf(ctx, "check blb backends is equal to cluster worker fail: %v", err)
		return resources, err
	}

	// 9.删除service
	err = c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, ServiceNameSvcBasic, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "delete service %s/%s failed:%v", defaultNamespace, ServiceNameSvcBasic, err)
		return resources, err
	}
	var k8sServiceStatus common.K8SServiceStatus
	if err := k8sServiceStatus.NewK8SServiceStatus(ctx, c.base, defaultNamespace, ServiceNameSvcBasic); err != nil {
		return nil, err
	}
	if err = common.WaitForResourceReady(ctx, &k8sServiceStatus); err != nil {
		logger.Errorf(ctx, "delete service %s/%s failed:%v", defaultNamespace, ServiceNameSvcBasic, err)
		return resources, err
	}

	// 10.检查：BLB、EIP是否被清理
	var blbStatus common.AppBLBStatus
	if err = blbStatus.NewAppBLBStatus(ctx, c.base, blbID, "deleted"); err != nil {
		return resources, err
	}
	if err = common.WaitForResourceReady(ctx, &blbStatus); err != nil {
		return resources, err
	}
	var eipStatus common.EIPStatus
	if err = eipStatus.NewEIPStatus(ctx, c.base, eip, "deleted"); err != nil {
		return resources, err
	}
	if err = common.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	logger.Infof(ctx, "Case %s: End", caseName)
	return resources, nil
}

func (c *svcBasic) Clean(ctx context.Context) error {
	svcName := ServiceNameSvcBasic
	return c.base.K8SClient.AppsV1().Deployments(defaultNamespace).Delete(ctx, svcName, metav1.DeleteOptions{})
}

func (c *svcBasic) Continue(ctx context.Context) bool {
	return true
}

func (c *svcBasic) ConfigFormat() string {
	return ""
}
