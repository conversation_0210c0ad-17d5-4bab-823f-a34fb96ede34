// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/02 14:56:00, by l<PERSON><PERSON>@baidu.com, create
*/
/*
LB Service 指定 udp in_vpc blb 集成测试
*/

package nodebackend_lb_service

import (
	"context"
	"errors"
	"net"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	CaseNameSvcUDPVPCBLB    cases.CaseName = "LoadBalancerService-NodeBackend-UDP-VPCBLB"
	ServiceNameSvcUDPVPCBLB                = "lb-svc-node-backend-udp-vpcblb"
	SvcYamlSvcUDPVPCBLB                    = `
apiVersion: v1
kind: Service
metadata:
  name: [SERVICE_NAME]
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-internal-vpc: "true"
spec:
  selector:
    app: [SERVICE_NAME]
  type: LoadBalancer
  ports:
  - port: 3005
    targetPort: 3005
    protocol: UDP
`
)

func init() {
	cases.AddCase(context.TODO(), CaseNameSvcUDPVPCBLB, NewSvcUdpVpcBLB)
}

var _ cases.Interface = &svcUdpVpcBLB{}

type svcUdpVpcBLB struct {
	base *cases.BaseClient
}

func NewSvcUdpVpcBLB(ctx context.Context) cases.Interface {
	return &svcUdpVpcBLB{}
}

func (c *svcUdpVpcBLB) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *svcUdpVpcBLB) Name() cases.CaseName {
	return CaseNameSvcUDPVPCBLB
}

func (c *svcUdpVpcBLB) Desc() string {
	return "LB Service 指定 udp in_vpc blb 集成测试"
}

func (c *svcUdpVpcBLB) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	caseName := CaseNameSvcUDPVPCBLB
	svcName := ServiceNameSvcUDPVPCBLB

	// 1.部署 Service
	strYml := strings.ReplaceAll(SvcYamlSvcUDPVPCBLB, "[SERVICE_NAME]", svcName)
	err := common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, strYml)
	if err != nil {
		logger.Errorf(ctx, "Deploy Service %s/%s failed: %s", defaultNamespace, svcName, err)
		return nil, err
	}

	// 2.收集创建的IaaS资源信息
	var k8sService common.K8SService
	if err := k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcName); err != nil {
		return nil, err
	}
	errReady := common.WaitForResourceReady(ctx, &k8sService)
	blbID, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, svcName)
	if blbID != "" {
		resources = append(resources, cases.Resource{
			CaseName: caseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blbID,
		})
	}
	if errReady != nil {
		return resources, errReady
	}

	// 3.检查BLB的IP地址应该是VPC内IP
	ipInternalVpc, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, svcName)
	clusterID := c.base.ClusterID
	cluster, err := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "GetCluster %s failed: %s", clusterID, err)
		return nil, err
	}
	logger.Infof(ctx, "GetCluster %s success: %s", clusterID, utils.ToJSON(cluster))
	clusterVPCID := cluster.Cluster.Spec.VPCID
	vpcResp, err := c.base.VPCClient.DescribeVPC(ctx, clusterVPCID, nil)
	if err != nil || vpcResp == nil {
		logger.Errorf(ctx, "failed to get VPC %s %v", clusterVPCID, err)
		return resources, err
	}

	_, vpcCIDR, err := net.ParseCIDR(vpcResp.ShowVPCModel.CIDR)
	if err != nil {
		logger.Errorf(ctx, "vpc CIDR %s illegal %v", vpcResp.ShowVPCModel.CIDR, err)
		return resources, err
	}
	ipBLB := net.ParseIP(ipInternalVpc)
	if !vpcCIDR.Contains(ipBLB) {
		logger.Errorf(ctx, "the BLB IP %s is not in VPC CIDR %s", ipInternalVpc, vpcResp.ShowVPCModel.CIDR)
		return resources, err
	}

	// 4.检查InternalVPC LB Service 的地址应与 BLB 的地址一致
	blbInfo, err := c.base.AppBLBClient.DescribeAppLoadBalancerByID(ctx, blbID, nil)
	if err != nil || blbInfo == nil {
		logger.Errorf(ctx, "DescribeLoadBalancerByID %s failed: %s", blbID, err)
		return resources, err
	}

	logger.Infof(ctx, "DescribeLoadBalancerByID %s success: %s", blbID, utils.ToJSON(blbInfo))
	if ipInternalVpc != blbInfo.Address {
		logger.Errorf(ctx, "eip:%s address is not blb:%s address:%s", ipInternalVpc, blbID, blbInfo.Address)
		return resources, err
	}
	logger.Infof(ctx, "eip:%s address is blb:%s address:%s", ipInternalVpc, blbID, blbInfo.Address)

	return resources, nil
}

func (c *svcUdpVpcBLB) Clean(ctx context.Context) error {
	svcName := ServiceNameSvcUDPVPCBLB

	// 1.删除 Service
	err := c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, svcName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Fail to clean svc: %v ", err)
		return err
	}

	return nil
}

func (c *svcUdpVpcBLB) Continue(ctx context.Context) bool {
	return true
}

func (c *svcUdpVpcBLB) ConfigFormat() string {
	return ""
}
