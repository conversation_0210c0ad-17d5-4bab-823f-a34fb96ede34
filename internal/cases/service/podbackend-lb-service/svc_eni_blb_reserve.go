// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/11/26 15:57:00, by <EMAIL>, create
*/
/*
测试使用指定 BLB 并在 Service 删除时保留 BLB
*/

package podbackend_lb_service

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	appblbsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

// 测试指定BLB并在Service删除时保留BLB PodBackend LoadBalancer Service
const (
	SvcEniBLBReserveCaseName    cases.CaseName = "LoadBalancerService-PodBackend-BLB-Reserve"
	svcEniBLBReserveServiceName                = "lb-svc-pod-backend-blb-reserve"
	svcEniBLBReserveYAML                       = `
apiVersion: v1
kind: Service
metadata:
  name: lb-svc-pod-backend-blb-reserve
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-id: "[CREATED_BLB]"
    service.beta.kubernetes.io/cce-load-balancer-reserve-lb: "true"
    service.beta.kubernetes.io/cce-load-balancer-backend-type: "eni"
spec:
  selector:
    app: lb-svc-pod-backend-blb-reserve
  type: LoadBalancer
  ports:
  - name: nginx
    protocol: TCP
    port: 80
    targetPort: 80`
)

func init() {
	cases.AddCase(context.TODO(), SvcEniBLBReserveCaseName, NewSvcEniBLBReserve)
}

var svcEniBLBIDReserve string
var _ cases.Interface = &svcEniBLBReserve{}

type svcEniBLBReserve struct {
	base *cases.BaseClient
}

func NewSvcEniBLBReserve(ctx context.Context) cases.Interface {
	return &svcEniBLBReserve{}
}

func (c *svcEniBLBReserve) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *svcEniBLBReserve) Name() cases.CaseName {
	return SvcEniBLBReserveCaseName
}

func (c *svcEniBLBReserve) Desc() string {
	return "测试使用指定 BLB 并在 Service 删除时保留 BLB"
}

func (c *svcEniBLBReserve) Check(ctx context.Context) ([]cases.Resource, error) {
	clusterID := c.base.ClusterID
	resources := make([]cases.Resource, 0)

	// 1.获取 cluster 的 VPC 下的 subnet
	logger.Infof(ctx, "Case %s: Step get cluster subnet", SvcEniBLBReserveCaseName)
	cluster, err := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "GetCluster %s failed: %s", clusterID, err)
		return resources, err
	}
	logger.Infof(ctx, "GetCluster %s success: %s", clusterID, utils.ToJSON(cluster))
	clusterVPCID := cluster.Cluster.Spec.VPCID
	lbServiceVPCSubnetID := cluster.Cluster.Spec.ContainerNetworkConfig.LBServiceVPCSubnetID

	// 2.创建 BLB, 统计 BLB 资源, 并等待 BLB 状态为 available
	logger.Infof(ctx, "Case %s: Step create BLB and waiting ready", SvcEniBLBReserveCaseName)
	args := &appblbsdk.CreateAppLoadBalancerArgs{
		Name:        svcEniBLBReserveServiceName,
		VPCID:       clusterVPCID,
		SubNetID:    lbServiceVPCSubnetID,
		AllowDelete: true,
	}
	createdBLB, err := c.base.AppBLBClient.CreateAppLoadBalancer(ctx, args, nil)
	if err != nil || createdBLB.BLBID == "" {
		logger.Errorf(ctx, "CreateBLB failed: %s", err)
		return resources, err
	}
	if createdBLB.BLBID != "" {
		svcEniBLBIDReserve = createdBLB.BLBID
		resources = append(resources, cases.Resource{
			CaseName: SvcEniBLBReserveCaseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       createdBLB.BLBID,
		})
	}
	createdBLBID := createdBLB.BLBID
	logger.Infof(ctx, "createdBLBID: %s", createdBLBID)
	var appBLBStatus common.AppBLBStatus
	if err = appBLBStatus.NewAppBLBStatus(ctx, c.base, createdBLBID, "available"); err != nil {
		return resources, err
	}
	if err = common.WaitForResourceReady(ctx, &appBLBStatus); err != nil {
		return resources, err
	}

	// 3.替换YAML文件中BLB ID, 使用之前创建的BLB部署 LB Service
	logger.Infof(ctx, "Case %s: Step deploy podbackend-lb-service with specific BLB", SvcEniBLBReserveCaseName)
	svcYaml := strings.ReplaceAll(svcEniBLBReserveYAML, "[CREATED_BLB]", createdBLBID)
	err = common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, svcYaml)
	if err != nil {
		logger.Errorf(ctx, "Deploy PodBackend LB %s/%s failed: %s", defaultNamespace, svcEniBLBReserveServiceName, err)
		return resources, err
	}

	// 4.等待 AppBLB 和 EIP Ready
	logger.Infof(ctx, "Case %s: Step waiting AppBLB and EIP ready", SvcEniBLBReserveCaseName)
	var k8sService common.K8SService
	if err := k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcEniBLBReserveServiceName); err != nil {
		return resources, err
	}
	errReady := common.WaitForResourceReady(ctx, &k8sService)

	// 5.收集创建的IaaS资源信息
	logger.Infof(ctx, "Case %s: Step collect created IaaS resource", SvcEniBLBReserveCaseName)
	eip, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, svcEniBLBReserveServiceName)
	if eip != "" {
		resources = append(resources, cases.Resource{
			CaseName: SvcEniBLBReserveCaseName,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
	}
	blbID, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, svcEniBLBReserveServiceName)
	if blbID != "" && blbID != createdBLBID {
		resources = append(resources, cases.Resource{
			CaseName: SvcEniBLBReserveCaseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blbID,
		})
		if blbID != "" && blbID != createdBLBID {
			logger.Errorf(ctx, "Service %s/%s blb: %s not use createdBLBID: %s", defaultNamespace, svcEniBLBReserveServiceName, blbID, createdBLBID)
			return resources, fmt.Errorf("service blb: %s not use createdBLBID: %s", blbID, createdBLBID)
		}
	}
	if errReady != nil {
		return resources, errReady
	}

	// 10.删除 Service 并检查在删除 Service 后 BLB 是否依然存在
	logger.Infof(ctx, "Case %s: Step delete LB Service and check existence of blb", SvcEniBLBReserveCaseName)
	err = c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, svcEniBLBReserveServiceName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Delete service %s/%s failed: %s", defaultNamespace, svcEniBLBReserveServiceName, err)
	}
	logger.Infof(ctx, "Delete service %s/%s success", defaultNamespace, svcEniBLBReserveServiceName)

	time.Sleep(15 * time.Second)

	describeAppBlbResp, err := c.base.AppBLBClient.DescribeAppLoadBalancerByID(ctx, createdBLBID, nil)
	if err != nil || describeAppBlbResp == nil {
		logger.Errorf(ctx, "Fail to get app blb: %v ", err)
		return resources, err
	}

	logger.Infof(ctx, "Case %s: End", SvcEniBLBReserveCaseName)
	return resources, nil
}

func (c *svcEniBLBReserve) Clean(ctx context.Context) error {
	err := c.base.AppBLBClient.DeleteAppLoadBalancer(ctx, svcEniBLBIDReserve, nil)
	if err != nil {
		logger.Errorf(ctx, "Fail to clean app blb: %v ", err)
		return err
	}

	return nil
}

func (c *svcEniBLBReserve) Continue(ctx context.Context) bool {
	return true
}

func (c *svcEniBLBReserve) ConfigFormat() string {
	return ""
}
