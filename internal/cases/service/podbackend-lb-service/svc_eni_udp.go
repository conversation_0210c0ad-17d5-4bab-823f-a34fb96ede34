// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/11/26 15:57:00, by <EMAIL>, create
*/
/*
使用 UDP 端口的 PodBackend LB Service 集成测试
*/

package podbackend_lb_service

import (
	"context"
	"errors"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	SvcEniUDPCaseName    cases.CaseName = "LoadBalancerService-PodBackend-UDP"
	svcEniUDPServiceName                = "lb-svc-pod-backend-udp"
	svcEniUDPYAML                       = `
apiVersion: v1
kind: Service
metadata:
  name: lb-svc-pod-backend-udp
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-backend-type: "eni"
spec:
  selector:
    app: lb-svc-pod-backend-udp
  type: LoadBalancer
  ports:
  - name: nginx
    protocol: UDP
    port: 80
    targetPort: 80`
)

func init() {
	cases.AddCase(context.TODO(), SvcEniUDPCaseName, NewSvcEniUDP)
}

var _ cases.Interface = &svcEniUDP{}

type svcEniUDP struct {
	base *cases.BaseClient
}

func NewSvcEniUDP(ctx context.Context) cases.Interface {
	return &svcEniUDP{}
}

func (c *svcEniUDP) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *svcEniUDP) Name() cases.CaseName {
	return SvcEniUDPCaseName
}

func (c *svcEniUDP) Desc() string {
	return "使用 UDP 端口的 PodBackend LB Service 集成测试"
}

func (c *svcEniUDP) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)

	// 1.部署 Service
	logger.Infof(ctx, "Case %s: Step deploy podbackend-lb-service", SvcEniUDPCaseName)
	err := common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, svcEniUDPYAML)
	if err != nil {
		logger.Errorf(ctx, "Deploy PodBackend LB Service %s/%s failed: %s", defaultNamespace, svcEniUDPServiceName, err)
		return resources, err
	}
	var k8sService common.K8SService
	if err := k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcEniUDPServiceName); err != nil {
		return nil, err
	}
	errReady := common.WaitForResourceReady(ctx, &k8sService)

	// 2.收集创建的IaaS资源信息
	logger.Infof(ctx, "Case %s: Step collect created IaaS resource", SvcEniUDPCaseName)
	blbID, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, svcEniUDPServiceName)
	if blbID != "" {
		resources = append(resources, cases.Resource{
			CaseName: SvcEniUDPCaseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blbID,
		})
	}
	eip, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, svcEniUDPServiceName)
	if eip != "" {
		resources = append(resources, cases.Resource{
			CaseName: SvcEniUDPCaseName,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
	}
	if errReady != nil {
		return resources, errReady
	}

	logger.Infof(ctx, "Case %s: End", SvcEniUDPCaseName)
	return resources, nil
}

func (c *svcEniUDP) Clean(ctx context.Context) error {
	err := c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, svcEniUDPServiceName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Fail to clean svc: %v ", err)
		return err
	}

	return nil
}

func (c *svcEniUDP) Continue(ctx context.Context) bool {
	return true
}

func (c *svcEniUDP) ConfigFormat() string {
	return ""
}
