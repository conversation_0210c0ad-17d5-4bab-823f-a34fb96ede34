// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/02 14:56:00, by l<PERSON><PERSON>@baidu.com, create
2020/11/26 15:57:00, by <EMAIL>, update
2025/04/16 15:57:00, by <EMAIL>, update
*/
/*
使用预设 BLB 和 EIP 实现Service复用 并在 Service 删除后保留 EIP和BLB 的 PodBackend LB Service 集成测试
1、创建BLB与EIP，并将EIP绑定BLB
2、两个service复用一个BLB,检查2个都正常使用
3、删除所有service，BLB和EIP保留，并保持绑定，检查BLB的cce-resource-name的tag自动删除
4、删除BLB和EIP
*/

package podbackend_lb_service

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	v1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/yaml"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	appblbsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	eipsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/executor"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	SvcEniBLBEIPCaseName cases.CaseName = "LoadBalancerService-PodBackend-BLB-EIP"

	shellPath = "/bin/bash"
	// service 和 deployment名字
	SvcEniBLBEIPServiceName1 = "lb-svc-pod-backend-blb-eip-1"
	SvcEniBLBEIPServiceName2 = "lb-svc-pod-backend-blb-eip-2"
	BLBDeployName1           = "deployment-for-svc-pod-blb-eip-1"
	BLBDeployName2           = "deployment-for-svc-pod-blb-eip-2"
	blbAndEIPName            = "blb-eip-test1"
	port1                    = 80
	port2                    = 81

	svcEniBLBEIPYAML = `
apiVersion: v1
kind: Service
metadata:
  name: [SERVICE_NAME]
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-id: "[CREATED_BLB]"
    service.beta.kubernetes.io/cce-load-balancer-reserve-lb: "true"
    service.beta.kubernetes.io/cce-load-balancer-backend-type: "eni"
spec:
  selector:
    app: [APP_NAME]
  type: LoadBalancer
  ports:
  - name: nginx
    protocol: TCP
    port: [Port]
    targetPort: 80`
)

func init() {
	cases.AddCase(context.TODO(), SvcEniBLBEIPCaseName, NewSvcEniBLBEIP)
}

var _ cases.Interface = &svcEniBLBEIP{}

type svcEniBLBEIP struct {
	base     *cases.BaseClient
	svcBLBID string
	svcEIPIP string
	shell    executor.Executor
}

func NewSvcEniBLBEIP(ctx context.Context) cases.Interface {
	return &svcEniBLBEIP{
		shell: executor.NewExecutor(shellPath),
	}
}

func (c *svcEniBLBEIP) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *svcEniBLBEIP) Name() cases.CaseName {
	return SvcEniBLBEIPCaseName
}

func (c *svcEniBLBEIP) Desc() string {
	return "使用预设 BLB 和 EIP 实现Service复用 并在 Service 删除后保留 EIP和BLB 的 PodBackend LB Service 集成测试"
}

func (c *svcEniBLBEIP) Check(ctx context.Context) ([]cases.Resource, error) {
	clusterID := c.base.ClusterID
	resources := make([]cases.Resource, 0)

	// 1.获取 cluster 的 VPC 下的 subnet
	logger.Infof(ctx, "Case %s: Step get cluster subnet", SvcEniBLBEIPCaseName)
	cluster, err := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "GetCluster %s failed: %s", clusterID, err)
		return resources, err
	}
	clusterVPCID := cluster.Cluster.Spec.VPCID
	lbServiceVPCSubnetID := cluster.Cluster.Spec.ContainerNetworkConfig.LBServiceVPCSubnetID

	// 2.创建 BLB, 统计 BLB 资源, 并等待 BLB 状态为 available
	logger.Infof(ctx, "Case %s: Step create BLB and waiting ready", SvcEniBLBEIPCaseName)
	args := &appblbsdk.CreateAppLoadBalancerArgs{
		Name:        blbAndEIPName,
		VPCID:       clusterVPCID,
		SubNetID:    lbServiceVPCSubnetID,
		AllowDelete: true,
	}
	createdBLB, err := c.base.AppBLBClient.CreateAppLoadBalancer(ctx, args, nil)
	if err != nil {
		logger.Errorf(ctx, "CreateBLB failed: %s", err)
		return resources, err
	}

	createdBLBID := createdBLB.BLBID
	if createdBLBID != "" {
		c.svcBLBID = createdBLBID
		resources = append(resources, cases.Resource{
			CaseName: SvcEniBLBEIPCaseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       createdBLBID,
		})
	}
	logger.Infof(ctx, "createdBLBID: %s", createdBLBID)
	var appBLBStatus common.AppBLBStatus
	if err = appBLBStatus.NewAppBLBStatus(ctx, c.base, createdBLBID, "available"); err != nil {
		return resources, err
	}
	if err = common.WaitForResourceReady(ctx, &appBLBStatus); err != nil {
		return resources, err
	}

	// 3. 创建并收集 EIP 资源, 等待 EIP 状态为 available
	logger.Infof(ctx, "Case %s: Step create EIP and waiting ready", SvcEniBLBEIPCaseName)
	createdEIP, err := c.base.EIPClient.CreateEIP(ctx, &eipsdk.CreateEIPArgs{
		BandwidthInMbps: 1,
		Billing: &eipsdk.Billing{
			PaymentTiming: "Postpaid",
			BillingMethod: "ByTraffic",
		},
		Name: blbAndEIPName,
	}, nil)
	if err != nil {
		logger.Errorf(ctx, "CreateEIP failed: %s", err)
		return resources, err
	}
	if createdEIP != "" {
		c.svcEIPIP = createdEIP
		resources = append(resources, cases.Resource{
			CaseName: SvcEniBLBEIPCaseName,
			Type:     cases.ResourceTypeEIP,
			ID:       createdEIP,
		})
	}
	logger.Infof(ctx, "createdEIP: %s", createdEIP)
	var eipStatus common.EIPStatus
	if err = eipStatus.NewEIPStatus(ctx, c.base, createdEIP, "available"); err != nil {
		return resources, err
	}
	if err = common.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	// 4、BLB绑定EIP
	logger.Infof(ctx, "Case %s: Step check EIP status", SvcEniBLBEIPCaseName)
	err = c.base.EIPClient.BindEIP(ctx, createdEIP, &eipsdk.BindEIPArgs{
		InstanceType: eipsdk.BLB,
		InstanceID:   createdBLBID,
	}, nil)
	if err != nil {
		logger.Errorf(ctx, "BindEIP failed: %s", err)
		return resources, err
	}
	if err = eipStatus.NewEIPStatus(ctx, c.base, createdEIP, "binded"); err != nil {
		return resources, err
	}
	if err = common.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	// 5.替换YAML文件中BLB ID 与 EIP, 创建两个 service，复用
	err = c.ensureServiceRunning(ctx, createdBLBID, createdEIP, BLBDeployName1, SvcEniBLBEIPServiceName1, port1)
	if err != nil {
		return resources, err
	}
	err = c.ensureServiceRunning(ctx, createdBLBID, createdEIP, BLBDeployName2, SvcEniBLBEIPServiceName2, port2)
	if err != nil {
		return resources, err
	}

	// 6.删除 service  检查 EIP 状态是否为 binded
	logger.Infof(ctx, "Case %s: Step delete LB Service and check existence & availability of EIP", SvcEniBLBEIPCaseName)
	err = c.DeleteBLBEIPService(ctx, SvcEniBLBEIPServiceName1)
	if err != nil {
		return resources, err
	}

	err = c.DeleteBLBEIPService(ctx, SvcEniBLBEIPServiceName2)
	if err != nil {
		return resources, err
	}

	time.Sleep(15 * time.Second)

	// 7.检查在删除 Service 后 BLB 是否依然存在 , BLB 资源上没有 cce-resource-name tag 且  EIP状态为binded
	logger.Infof(ctx, "Case %s: Step check BLB and EIP status", SvcEniBLBEIPCaseName)
	describeAppBlbResp, err := c.base.AppBLBClient.DescribeAppLoadBalancerByID(ctx, createdBLBID, nil)
	if err != nil || describeAppBlbResp == nil {
		logger.Errorf(ctx, "Fail to get app blb: %v ", err)
		return resources, err
	}
	for _, tag := range describeAppBlbResp.Tags {
		if tag.TagKey == "cce-resource-name" {
			return resources, fmt.Errorf("tag key %s exists, expect not exist, tag value: %s", tag.TagKey, tag.TagValue)
		}
	}
	eipStatus.SetStatus("binded")
	if err = common.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	logger.Infof(ctx, "Case %s: End", SvcEniBLBEIPCaseName)
	return resources, nil
}

func (c *svcEniBLBEIP) Clean(ctx context.Context) error {
	// 删除创建的 BLB 与 EIP 资源 (service使用BLB会自动开启BLB删除保护，所以先关闭BLB的删除保护)
	upBLBErr := c.base.AppBLBClient.UpdateAppLoadBalancer(ctx, c.svcBLBID, &appblbsdk.UpdateAppLoadBalancerArgs{
		AllowDelete: true,
	}, nil)
	if upBLBErr != nil {
		return fmt.Errorf("update blb %s err: %s", c.svcBLBID, upBLBErr)
	}
	blbErr := c.base.AppBLBClient.DeleteAppLoadBalancer(ctx, c.svcBLBID, nil)
	eipErr := c.base.EIPClient.DeleteEIP(ctx, c.svcEIPIP, nil)
	if blbErr != nil || eipErr != nil {
		return fmt.Errorf("delete blb %s err: %s, delete eip %s err: %s", c.svcBLBID, blbErr, c.svcEIPIP, eipErr)
	}

	// 删除 deployment
	if err := c.base.K8SClient.AppsV1().Deployments(defaultNamespace).Delete(ctx, BLBDeployName1, metav1.DeleteOptions{}); err != nil {
		logger.Infof(ctx, "delete deployment failed: %v", err.Error())
		return err
	}
	if err := c.base.K8SClient.AppsV1().Deployments(defaultNamespace).Delete(ctx, BLBDeployName2, metav1.DeleteOptions{}); err != nil {
		logger.Infof(ctx, "delete deployment failed: %v", err.Error())
		return err
	}
	return nil
}

func (c *svcEniBLBEIP) Continue(ctx context.Context) bool {
	return true
}

func (c *svcEniBLBEIP) ConfigFormat() string {
	return ""
}

func (c *svcEniBLBEIP) CreateBLBEIPService(ctx context.Context, createdBLBID string, createdEIP string, svcEniBLBEIPServiceName string, port string) error {
	logger.Infof(ctx, "Case %s: Step deploy podbackend-lb-service with specific EIP and BLB OD", SvcEniBLBEIPCaseName)
	strYml := strings.ReplaceAll(svcEniBLBEIPYAML, "[CREATED_BLB]", createdBLBID)
	strYml = strings.ReplaceAll(strYml, "[APP_NAME]", svcEniBLBEIPServiceName)
	strYml = strings.ReplaceAll(strYml, "[SERVICE_NAME]", svcEniBLBEIPServiceName)
	strYml = strings.ReplaceAll(strYml, "[Port]", port)

	err := common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, strYml)
	if err != nil {
		return fmt.Errorf("Deploy Frist Service %s/%s failed: %s", defaultNamespace, svcEniBLBEIPServiceName, err)
	}

	logger.Infof(ctx, "Case %s: Step collect created IaaS resource", SvcEniBLBEIPCaseName)
	var k8sService common.K8SService
	if err = k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcEniBLBEIPServiceName); err != nil {
		return err
	}
	if err = common.WaitForResourceReady(ctx, &k8sService); err != nil {
		logger.Errorf(ctx, "wait for lb service ready failed: %v", err.Error())
		// 输出service的事件
		eventErr := k8sService.GetServiceEvents(ctx, c.base.ClusterID)
		if eventErr != nil {
			return eventErr
		}
		return err
	}

	blbID, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, svcEniBLBEIPServiceName)
	eip, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, svcEniBLBEIPServiceName)
	if blbID == "" || blbID != createdBLBID {
		logger.Errorf(ctx, "Service %s/%s blb: %s not use createdBLBID: %s", defaultNamespace, svcEniBLBEIPServiceName, blbID, createdBLBID)
		return err
	}
	if eip == "" || eip != createdEIP {
		logger.Errorf(ctx, "Service %s/%s blb: %s not use createdEIP: %s", defaultNamespace, svcEniBLBEIPServiceName, eip, createdEIP)
		return err
	}

	return nil
}

func (c *svcEniBLBEIP) DeleteBLBEIPService(ctx context.Context, svcEniBLBEIPServiceName string) error {
	err := c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, svcEniBLBEIPServiceName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Delete service %s/%s failed: %s", defaultNamespace, svcEniBLBEIPServiceName, err)
		return err
	}
	logger.Infof(ctx, "Delete service %s/%s success", defaultNamespace, svcEniBLBEIPServiceName)
	return nil
}

func (c *svcEniBLBEIP) ensureDeploymentRunning(ctx context.Context, replicas int32, Port int32, testDeploymentName string, testDeploymentAPP string) error {
	logger.Infof(ctx, "start to deploy test deployment, replicas: %d", replicas)
	deployment := v1.Deployment{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Deployment",
			APIVersion: "apps/v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      testDeploymentName,
			Namespace: corev1.NamespaceDefault,
			Labels: map[string]string{
				"app": testDeploymentAPP,
			},
		},
		Spec: v1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": testDeploymentAPP,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": testDeploymentAPP,
					},
				},
				Spec: corev1.PodSpec{
					RestartPolicy: "Always",
					Containers: []corev1.Container{{
						Name:            testDeploymentAPP,
						Image:           "registry.baidubce.com/qa-test/nginx:1.17",
						ImagePullPolicy: corev1.PullIfNotPresent,
						Ports: []corev1.ContainerPort{{
							ContainerPort: Port,
						}},
					}},
				},
			},
		},
	}
	out, err := yaml.Marshal(&deployment)
	if err != nil {
		return fmt.Errorf("marshal deployment failed, err: %v", err)
	}
	deploymentYaml := string(out)
	_, err = c.base.AppClient.PostAppResource(ctx, c.base.ClusterID, testDeploymentName, deploymentYaml, corev1.NamespaceDefault, nil)
	if err != nil {
		return fmt.Errorf("create deployment failed, err: %v", err)
	}

	// 尝试部署deployment，等待所有副本running
	err = common.WaitForFunc(ctx, func(ctx context.Context) (err error) {
		deploymentResource, getErr := c.base.AppClient.GetAppDeploymentByName(ctx, c.base.ClusterID, testDeploymentName, corev1.NamespaceDefault, nil)
		if getErr != nil {
			err = fmt.Errorf("get deployment Resource: %v failed, err: %v", testDeploymentName, err)
			return
		}
		if deploymentResource.Status != "Running" || deploymentResource.StatusInfo.Available != deploymentResource.StatusInfo.Replicas {
			err = fmt.Errorf("deployment status: %v, available: %d, expect replicas: %d", deploymentResource.Status,
				deploymentResource.StatusInfo.Available, deploymentResource.StatusInfo.Replicas)
			return
		}
		return
	}, 5*time.Second, 2*time.Minute)
	if err != nil {
		return fmt.Errorf("wait for deployment running failed, err: %v", err)
	}
	logger.Infof(ctx, "deployment %s is running", testDeploymentName)
	return nil
}

func (c *svcEniBLBEIP) ensureServiceRunning(ctx context.Context, createdBLBID string, createdEIP string, BLBDeployName string, svcEniBLBEIPServiceName string, port int32) error {
	err := c.CreateBLBEIPService(ctx, createdBLBID, createdEIP, svcEniBLBEIPServiceName, strconv.Itoa(int(port)))
	if err != nil {
		logger.Errorf(ctx, "create BLBEIPservice failed, err: %v", err)
		return err
	}

	err = c.ensureDeploymentRunning(ctx, 1, port, BLBDeployName, svcEniBLBEIPServiceName)
	if err != nil {
		logger.Errorf(ctx, "create deployment failed, err: %v", err)
		return err
	}

	// 后端连通性验证
	ok := false
	var cmd string
	for i := 0; i < 20; i++ {
		time.Sleep(10 * time.Second)
		cmd = fmt.Sprintf("curl %s:%s", createdEIP, strconv.Itoa(int(port)))
		stdout, _, err := c.shell.ShellExec(ctx, cmd)
		if err != nil {
			logger.Errorf(ctx, "ShellExec[%s] failed: %v ", cmd, err)
			continue
		}
		if !strings.Contains(stdout, "Welcome to nginx") {
			logger.Errorf(ctx, "invite nginx service failed")
			continue
		}
		ok = true
		break
	}

	if !ok {
		logger.Errorf(ctx, "ShellExec[%s] failed: %v", cmd, err)
		return err
	}

	return nil
}
