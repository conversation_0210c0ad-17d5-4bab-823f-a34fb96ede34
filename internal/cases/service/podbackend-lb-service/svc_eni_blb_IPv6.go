package podbackend_lb_service

import (
	"context"
	"errors"
	"fmt"
	"net"
	"strings"
	"time"

	v1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/yaml"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	SvcEniBLBIPv6CaseName    cases.CaseName = "LoadBalancerServicePodBackendBlBIPv6"
	svcEniBLBServiceIPv6Name                = "lb-svc-pod-backend-blb-ipv6"
	statusRunning                           = "Running"
	testDeploymentName                      = "ipv6-deployment"
	IPv6image                               = "registry.baidubce.com/cce/nginx-alpine-go:ipv6"
	svcEniBLBIPv6YAML                       = `
apiVersion: v1
kind: Service
metadata:
  name: lb-svc-pod-backend-blb-ipv6
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-subnet-id: [SUBNET_ID]
    service.beta.kubernetes.io/cce-load-balancer-backend-type: "eni"
    service.beta.kubernetes.io/cce-blb-type: appblb
    service.beta.kubernetes.io/cce-load-balancer-internal-vpc: "true"
spec:
  type: LoadBalancer
  selector:
    app: lb-svc-pod-backend-blb-ipv6
  ports:
    - name: nginx
      port: 80
      targetPort: 80
      protocol: TCP
  sessionAffinity: None
  publishNotReadyAddresses: false
  externalTrafficPolicy: Cluster
  ipFamilyPolicy: PreferDualStack
  ipFamilies:
    - IPv4
    - IPv6`
)

type loadBalancerServicePodBackendBlbIpv6 struct {
	base    *cases.BaseClient
	podName string
}

func init() {
	cases.AddCase(context.TODO(), SvcEniBLBIPv6CaseName, NewLoadBalancerServicePodBackendBlbIpv6)
}

func NewLoadBalancerServicePodBackendBlbIpv6(ctx context.Context) cases.Interface {
	return &loadBalancerServicePodBackendBlbIpv6{}
}

func (c *loadBalancerServicePodBackendBlbIpv6) Name() cases.CaseName {
	return SvcEniBLBIPv6CaseName
}

func (c *loadBalancerServicePodBackendBlbIpv6) Desc() string {
	return "使用IPv6创建PodBackend"
}

func (c *loadBalancerServicePodBackendBlbIpv6) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	if base == nil {
		return errors.New("base client is nil")
	}
	c.base = base
	return
}

func (c *loadBalancerServicePodBackendBlbIpv6) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID
	resources = make([]cases.Resource, 0)

	// 1.获取 cluster 的 VPC 下的 subnet
	logger.Infof(ctx, "Case %s: Step get cluster subnet", SvcEniBLBIPv6CaseName)
	cluster, err := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "GetCluster %s failed: %s", clusterID, err)
		return resources, err
	}
	logger.Infof(ctx, "GetCluster %s success: %s", clusterID, utils.ToJSON(cluster))
	lbServiceVPCSubnetID := cluster.Cluster.Spec.ContainerNetworkConfig.LBServiceVPCSubnetID

	// 2. 部署Deployment
	logger.Infof(ctx, "Case %s: Step deploy podbackend-lb-service-ipv6", SvcEniBLBIPv6CaseName)
	svcYaml := strings.ReplaceAll(svcEniBLBIPv6YAML, "[SUBNET_ID]", lbServiceVPCSubnetID)
	err = common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, svcYaml)
	if err != nil {
		logger.Errorf(ctx, "Deploy PodBackend LB IPv6 Service %s/%s failed: %s", defaultNamespace, svcEniBLBServiceIPv6Name, err)
		return resources, err
	}
	err = c.ensureDeploymentRunning(ctx, 1)
	if err != nil {
		logger.Errorf(ctx, "Ensure Deployment Running failed: %s", err)
		return resources, err
	}

	// 3.验证service创建成功和ipv4、ipv6生成成功，失败输出事件
	logger.Infof(ctx, "Case %s: Step collect created IaaS resource", svcEniBLBServiceIPv6Name)
	var k8sService common.K8SService
	if err = k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcEniBLBServiceIPv6Name); err != nil {
		return nil, err
	}
	errReady := common.WaitForResourceReady(ctx, &k8sService)
	if errReady != nil {
		logger.Errorf(ctx, "Service %s/%s not ready: %s", defaultNamespace, svcEniBLBServiceIPv6Name, errReady)
		err = k8sService.GetServiceEvents(ctx, clusterID)
		if err != nil {
			logger.Errorf(ctx, "GetServiceEvents %s/%s failed: %s", defaultNamespace, svcEniBLBServiceIPv6Name, err)
		}
		return resources, errReady
	}
	blbID, err := k8sService.GetServiceBlB(ctx, defaultNamespace, svcEniBLBServiceIPv6Name)
	if err != nil {
		logger.Errorf(ctx, "get service LoadBalancer %s failed: %w", svcEniBLBServiceIPv6Name, err)
		return resources, err
	}
	if blbID == "" {
		logger.Errorf(ctx, "create service LoadBalancer %s failed, blbID is empty", svcEniBLBServiceIPv6Name)
	}
	resources = append(resources, cases.Resource{
		CaseName: SvcEniSubnetCaseName,
		Type:     cases.ResourceTypeAppBLB,
		ID:       blbID,
	})

	// 4. 获取Service的IPv4和IPv6地址
	var ipv4Addr, ipv6Addr string
	serviceDetails, err := c.base.AppClient.GetServiceByName(ctx, clusterID, svcEniBLBServiceIPv6Name, defaultNamespace, nil)
	if err != nil {
		return resources, fmt.Errorf("get service failed: %w", err)
	}
	if serviceDetails == nil || serviceDetails.ExternalEndpoints == nil || len(serviceDetails.ExternalEndpoints) == 0 {
		logger.Errorf(ctx, "service %s ExternalEndpoints is empty", svcEniBLBServiceIPv6Name)
		return resources, fmt.Errorf("service %s ExternalEndpoints is empty", svcEniBLBServiceIPv6Name)
	}
	for _, endpoint := range serviceDetails.ExternalEndpoints {
		hostip := net.ParseIP(endpoint.Host)
		if hostip != nil && hostip.To4() != nil {
			ipv4Addr = hostip.String()
			logger.Infof(ctx, "service %s ExternalEndpoints IPv4 address is %s", svcEniBLBServiceIPv6Name, ipv4Addr)
		}
		if hostip != nil && hostip.To16() != nil && strings.Contains(hostip.String(), ":") {
			ipv6Addr = hostip.String()
			logger.Infof(ctx, "service %s ExternalEndpoints IPv6 address is %s", svcEniBLBServiceIPv6Name, ipv6Addr)
		}
	}
	if ipv4Addr == "" || ipv6Addr == "" {
		logger.Errorf(ctx, "service %s ExternalEndpoints has empty, IPv4: %s, IPv6: %s", svcEniBLBServiceIPv6Name, ipv4Addr, ipv6Addr)
		return resources, fmt.Errorf("service %s ExternalEndpoints has empty, IPv4: %s, IPv6: %s", svcEniBLBServiceIPv6Name, ipv4Addr, ipv6Addr)
	}

	// 5. 验证IPv4和IPv6是否可以访问
	var k8sPod common.K8SPod
	if err := k8sPod.NewK8SPod(ctx, c.base, "default", nil); err != nil {
		return resources, err
	}
	checkIPv6curl := []string{"sh", "-c", fmt.Sprintf("curl --max-time 30 -6 [%s]", ipv6Addr)}
	result, err := k8sPod.ExecInPod(ctx, c.podName, defaultNamespace, checkIPv6curl)
	if err != nil {
		logger.Errorf(ctx, "exec pod %s with cmd %v failed: %v", c.podName, checkIPv6curl, err)
		return resources, err
	}
	logger.Infof(ctx, "curl ipv6: %s success, result: %s", ipv6Addr, result)

	checkIPv4curl := []string{"sh", "-c", fmt.Sprintf("curl --max-time 30 %s", ipv4Addr)}
	result, err = k8sPod.ExecInPod(ctx, c.podName, defaultNamespace, checkIPv4curl)
	if err != nil {
		logger.Errorf(ctx, "exec pod %s with cmd %v failed: %v", c.podName, checkIPv4curl, err)
		return resources, err
	}
	logger.Infof(ctx, "curl ipv4: %s success, result: %s", ipv4Addr, result)

	return resources, nil
}

func (c *loadBalancerServicePodBackendBlbIpv6) Clean(ctx context.Context) error {
	err := c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, svcEniBLBServiceIPv6Name, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Delete service %s/%s failed: %s", defaultNamespace, svcEniBLBServiceIPv6Name, err)
	}
	err = c.base.K8SClient.AppsV1().Deployments(defaultNamespace).Delete(ctx, testDeploymentName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Delete deployment %s failed: %v", testDeploymentName, err)
		return err
	}
	return nil
}

func (c *loadBalancerServicePodBackendBlbIpv6) Continue(ctx context.Context) bool {
	return true
}

func (c *loadBalancerServicePodBackendBlbIpv6) ConfigFormat() string {
	return ""
}

func (c *loadBalancerServicePodBackendBlbIpv6) ensureDeploymentRunning(ctx context.Context, replicas int32) error {
	logger.Infof(ctx, "start to deploy test deployment, replicas: %d", replicas)
	deployment := v1.Deployment{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Deployment",
			APIVersion: "apps/v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      testDeploymentName,
			Namespace: corev1.NamespaceDefault,
			Labels: map[string]string{
				"app": svcEniBLBServiceIPv6Name,
			},
		},
		Spec: v1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": svcEniBLBServiceIPv6Name,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": svcEniBLBServiceIPv6Name,
					},
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{{
						Name:            svcEniBLBServiceIPv6Name,
						Image:           IPv6image,
						ImagePullPolicy: corev1.PullIfNotPresent,
					}},
				},
			},
		},
	}
	out, err := yaml.Marshal(&deployment)
	if err != nil {
		return fmt.Errorf("marshal deployment failed, err: %v", err)
	}
	deploymentYaml := string(out)
	_, err = c.base.AppClient.PostAppResource(ctx, c.base.ClusterID, testDeploymentName, deploymentYaml, corev1.NamespaceDefault, nil)
	if err != nil {
		return fmt.Errorf("create deployment failed, err: %v", err)
	}

	// 尝试部署deploymen，等待所有副本running
	err = common.WaitForFunc(ctx, func(ctx context.Context) (err error) {
		deploymentResource, getErr := c.base.AppClient.GetAppDeploymentByName(ctx, c.base.ClusterID, testDeploymentName, corev1.NamespaceDefault, nil)
		if getErr != nil {
			err = fmt.Errorf("get deployment Resource: %v failed, err: %v", testDeploymentName, err)
			return
		}
		if deploymentResource.Status != "Running" || deploymentResource.StatusInfo.Available != deploymentResource.StatusInfo.Replicas {
			err = fmt.Errorf("deployment status: %v, available: %d, expect replicas: %d", deploymentResource.Status,
				deploymentResource.StatusInfo.Available, deploymentResource.StatusInfo.Replicas)
			return
		}
		if len(deploymentResource.PodList.Pods) == 0 || len(deploymentResource.PodList.Pods) != int(replicas) {
			err = fmt.Errorf("deployment pod number: %v, expect replicas: %d", len(deploymentResource.PodList.Pods), replicas)
		}
		c.podName = deploymentResource.PodList.Pods[0].ObjectMeta.Name
		logger.Infof(ctx, "deployment %s is running, podName: %s", testDeploymentName, c.podName)
		return
	}, 5*time.Second, 2*time.Minute)

	if err != nil {
		return fmt.Errorf("wait for deployment running failed, err: %v", err)
	}
	logger.Infof(ctx, "deployment %s is running", testDeploymentName)
	return nil
}
