// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/11/26 15:57:00, by <EMAIL>, create
*/

package podbackend_lb_service

import (
	"context"
	"errors"
	"fmt"
	"time"

	corev1 "k8s.io/api/core/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const defaultNamespace = "default"

// deployPodAndCheckServiceBackend 部署指定数量的 Pod 并检查 LB Service 的后端有无跟进更新
func deployPodAndCheckServiceBackend(ctx context.Context, baseClient *cases.BaseClient, appBLB common.AppBLBStatus,
	pods common.K8SPod, podNum int, appName string) error {
	err := common.DeployExampleNginxDeployment(ctx, baseClient, podNum, appName)
	if err != nil {
		return err
	}
	// 等待一段时间让 AppBLB 去准备后端 IP 组
	time.Sleep(90 * time.Second)

	err = checkRsOfPodBackendService(ctx, appBLB, pods)
	if err != nil {
		return err
	}

	return nil
}

// checkRsOfPodBackendService 检查 PodBackend LB Service 的后端 IP 列表是否和 Pod IP 列表一致
// error 不为 nil 说明函数运行出错; error 为 nil 时返回 bool 代表检查是否通过
func checkRsOfPodBackendService(ctx context.Context, appBLB common.AppBLBStatus, pods common.K8SPod) error {
	// 获取AppBLB的后端IP
	appBLBBackends, err := appBLB.GetAppBLBBackends(ctx)
	if err != nil {
		return err
	}
	if appBLBBackends == nil {
		return errors.New("appBLBBackends is nil")
	}
	logger.Warnf(ctx, "Number of AppBLBBackends %d %v", len(appBLBBackends), appBLBBackends)

	// 获取Service的后端Pod列表
	podListResp, err := pods.GetPodList(ctx)
	if err != nil {
		return err
	}
	if podListResp == nil {
		return errors.New("PodList Response is nil")
	}
	podList := make([]corev1.Pod, 0)
	if podListResp.Items == nil {
		logger.Warnf(ctx, "Pod selector finds no pod")
	}
	for _, pod := range podListResp.Items {
		ready := true
		for _, container := range pod.Status.ContainerStatuses {
			if !container.Ready {
				ready = false
			}
		}
		if ready {
			podList = append(podList, pod)
		}
	}

	logger.Warnf(ctx, "Number of Pod %d", len(podList))

	// 检查每个Pod的IP是否存在于AppBLB的后端IP里
	if len(podList) != len(appBLBBackends) {
		logger.Warnf(ctx, "PodList Size %d AppBLBBackends %d", len(podList), len(appBLBBackends))
		return errors.New("podList size not equal to appBLB backend list size")
	}
	for _, pod := range podList {
		if !common.Contains(appBLBBackends, pod.Status.PodIP) {
			logger.Errorf(ctx, "PodIP %s not found in LB-Service backend", pod.Status.PodIP)
			return fmt.Errorf("podIP %s not found in LB-Service backend", pod.Status.PodIP)
		}
	}

	return nil
}

func deployNotReadyPodAndCheckServiceBackend(ctx context.Context, baseClient *cases.BaseClient,
	appBLB common.AppBLBStatus, pods common.K8SPod, podNum int, appName string) error {
	// 部署 not ready 的pod
	err := common.DeployNotReadyNginxDeployment(ctx, baseClient, podNum, appName)
	// 等待一段时间让 AppBLB 去准备后端 IP 组
	time.Sleep(60 * time.Second)

	err = checkRsOfPodBackendService(ctx, appBLB, pods)
	if err != nil {
		return err
	}

	return nil

}
