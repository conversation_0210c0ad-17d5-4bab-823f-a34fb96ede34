// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/02 14:56:00, by l<PERSON><PERSON>@baidu.com, create
2020/11/26 15:57:00, by <EMAIL>, update
*/
/*
基本 PodBackend LB Service 集成测试
*/

package podbackend_lb_service

import (
	"context"
	"errors"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	SvcEniCaseName    cases.CaseName = "LoadBalancerService-PodBackend-Basic"
	svcEniServiceName                = "lb-svc-pod-backend-basic"
	svcEniYAML                       = `
apiVersion: v1
kind: Service
metadata:
  name: lb-svc-pod-backend-basic
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-backend-type: "eni"
spec:
  selector:
    app: lb-svc-pod-backend-basic
  type: LoadBalancer
  ports:
  - name: nginx
    protocol: TCP
    port: 80
    targetPort: 80`
)

func init() {
	cases.AddCase(context.TODO(), SvcEniCaseName, NewSvcEni)
}

var _ cases.Interface = &svcEni{}

type svcEni struct {
	base *cases.BaseClient
}

func NewSvcEni(ctx context.Context) cases.Interface {
	return &svcEni{}
}

func (c *svcEni) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *svcEni) Name() cases.CaseName {
	return SvcEniCaseName
}

func (c *svcEni) Desc() string {
	return "基本 PodBackend LB Service 集成测试"
}

func (c *svcEni) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)

	// 1.部署 Service
	logger.Infof(ctx, "Case %s: Step deploy podbackend-lb-service", SvcEniCaseName)
	err := common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, svcEniYAML)
	if err != nil {
		logger.Errorf(ctx, "Deploy PodBackend LB Service %s/%s failed: %s", defaultNamespace, svcEniServiceName, err)
		return resources, err
	}
	var k8sService common.K8SService
	if err := k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcEniServiceName); err != nil {
		return resources, err
	}
	errReady := common.WaitForResourceReady(ctx, &k8sService)

	// 2.收集创建的IaaS资源信息
	logger.Infof(ctx, "Case %s: Step collect created IaaS resource", SvcEniCaseName)
	blbID, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, svcEniServiceName)
	if blbID != "" {
		resources = append(resources, cases.Resource{
			CaseName: SvcEniCaseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blbID,
		})
	}
	eip, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, svcEniServiceName)
	if eip != "" {
		resources = append(resources, cases.Resource{
			CaseName: SvcEniCaseName,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
	}
	if errReady != nil {
		return resources, errReady
	}

	// 3.准备AppBLB和Pod的接口
	logger.Infof(ctx, "Case %s: Step prepare api-client for AppBLB and Pod", SvcEniCaseName)
	var appBLB common.AppBLBStatus
	if err := appBLB.NewAppBLBStatus(ctx, c.base, blbID, "available"); err != nil {
		return resources, err
	}
	var k8sPod common.K8SPod
	if err := k8sPod.NewK8SPod(ctx, c.base, defaultNamespace, nil); err != nil {
		return resources, err
	}
	k8sPod.SetMatchLabel("app", string(svcEniServiceName))
	k8sPod.SetStatus(string(v1.PodRunning))

	// 4.部署Pod并检查AppBLB后端IP是否与Pod列表一致
	logger.Infof(ctx, "Case %s: Step check AppBLB backend IPs are same with pod list", SvcEniCaseName)
	err = deployPodAndCheckServiceBackend(ctx, c.base, appBLB, k8sPod, 1, svcEniServiceName)
	if err != nil {
		return resources, err
	}

	// 5.增加Pod数并检查AppBLB后端IP是否与Pod一致
	logger.Infof(ctx, "Case %s: Step increase pod and check AppBLB backend IPs are same with pod list", SvcEniCaseName)
	err = deployPodAndCheckServiceBackend(ctx, c.base, appBLB, k8sPod, 2, svcEniServiceName)
	if err != nil {
		return resources, err
	}

	// 6.减少Pod数并检查AppBLB后端IP是否与Pod一致
	logger.Infof(ctx, "Case %s: Step decrease pod and check AppBLB backend IPs are same with pod list", SvcEniCaseName)
	err = deployPodAndCheckServiceBackend(ctx, c.base, appBLB, k8sPod, 1, svcEniServiceName)
	if err != nil {
		return resources, err
	}

	// 7.校验pod not ready情况下AppBLB后端IP不进行同步
	logger.Infof(ctx, "Case %s: Step AppBLB backend IPs will not sync pod IPs when pods are not ready", SvcEniCaseName)
	err = deployNotReadyPodAndCheckServiceBackend(ctx, c.base, appBLB, k8sPod, 2, svcEniServiceName)

	logger.Infof(ctx, "Case %s: End", SvcEniCaseName)
	return resources, nil
}

func (c *svcEni) Clean(ctx context.Context) error {
	err := c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, svcEniServiceName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Fail to clean svc: %v ", err)
		return err
	}

	return nil
}

func (c *svcEni) Continue(ctx context.Context) bool {
	return true
}

func (c *svcEni) ConfigFormat() string {
	return ""
}
