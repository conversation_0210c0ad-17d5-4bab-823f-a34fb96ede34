// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/02 14:56:00, by l<PERSON><PERSON>@baidu.com, create
2020/11/26 15:57:00, by <EMAIL>, update
*/
/*
使用预设 BLB 并在 Service 删除后保留的 PodBackend LB Service 集成测试
*/

package podbackend_lb_service

import (
	"context"
	"errors"
	"fmt"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	appblbsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

// 测试指定BLB PodBackend LoadBalancer Service
const (
	SvcEniBLBCaseName    cases.CaseName = "LoadBalancerService-PodBackend-BLB"
	svcEniBLBServiceName                = "lb-svc-pod-backend-blb"
	svcEniBLBYAML                       = `
apiVersion: v1
kind: Service
metadata:
  name: lb-svc-pod-backend-blb
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-id: "[CREATED_BLB]"
    service.beta.kubernetes.io/cce-load-balancer-backend-type: "eni"
spec:
  selector:
    app: lb-svc-pod-backend-blb
  type: LoadBalancer
  ports:
  - name: nginx
    protocol: TCP
    port: 80
    targetPort: 80`
)

func init() {
	cases.AddCase(context.TODO(), SvcEniBLBCaseName, NewSvcEniBLB)
}

var _ cases.Interface = &svcEniBLB{}

type svcEniBLB struct {
	base *cases.BaseClient
}

func NewSvcEniBLB(ctx context.Context) cases.Interface {
	return &svcEniBLB{}
}

func (c *svcEniBLB) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *svcEniBLB) Name() cases.CaseName {
	return SvcEniBLBCaseName
}

func (c *svcEniBLB) Desc() string {
	return "使用预设 BLB 并在 Service 删除后保留的 PodBackend LB Service 集成测试"
}

func (c *svcEniBLB) Check(ctx context.Context) ([]cases.Resource, error) {
	clusterID := c.base.ClusterID
	resources := make([]cases.Resource, 0)

	// 1.获取 cluster 的 VPC 下的 subnet
	logger.Infof(ctx, "Case %s: Step get cluster subnet", SvcEniBLBCaseName)
	cluster, err := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "GetCluster %s failed: %s", clusterID, err)
		return resources, err
	}
	logger.Infof(ctx, "GetCluster %s success: %s", clusterID, utils.ToJSON(cluster))
	clusterVPCID := cluster.Cluster.Spec.VPCID
	lbServiceVPCSubnetID := cluster.Cluster.Spec.ContainerNetworkConfig.LBServiceVPCSubnetID

	// 2.创建 BLB, 统计 BLB 资源, 并等待 BLB 状态为 available
	logger.Infof(ctx, "Case %s: Step create BLB and waiting ready", SvcEniBLBCaseName)
	args := &appblbsdk.CreateAppLoadBalancerArgs{
		Name:        svcEniBLBServiceName,
		VPCID:       clusterVPCID,
		SubNetID:    lbServiceVPCSubnetID,
		AllowDelete: true,
	}
	createdBLB, err := c.base.AppBLBClient.CreateAppLoadBalancer(ctx, args, nil)
	if err != nil || createdBLB.BLBID == "" {
		logger.Errorf(ctx, "CreateBLB failed: %s", err)
		return resources, err
	}
	if createdBLB.BLBID != "" {
		resources = append(resources, cases.Resource{
			CaseName: SvcEniBLBCaseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       createdBLB.BLBID,
		})
	}
	createdBLBID := createdBLB.BLBID
	logger.Infof(ctx, "createdBLBID: %s", createdBLBID)
	var appBLBStatus common.AppBLBStatus
	if err = appBLBStatus.NewAppBLBStatus(ctx, c.base, createdBLBID, "available"); err != nil {
		return resources, err
	}
	if err = common.WaitForResourceReady(ctx, &appBLBStatus); err != nil {
		return resources, err
	}

	// 3.替换YAML文件中BLB ID, 使用之前创建的BLB部署 LB Service
	logger.Infof(ctx, "Case %s: Step deploy podbackend-lb-service with specific BLB", SvcEniBLBCaseName)
	svcYaml := strings.ReplaceAll(svcEniBLBYAML, "[CREATED_BLB]", createdBLBID)
	err = common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, svcYaml)
	if err != nil {
		logger.Errorf(ctx, "Deploy PodBackend LB %s/%s failed: %s", defaultNamespace, svcEniBLBServiceName, err)
		return resources, err
	}

	// 4.等待 AppBLB 和 EIP Ready
	logger.Infof(ctx, "Case %s: Step waiting AppBLB and EIP ready", SvcEniBLBCaseName)
	var k8sService common.K8SService
	if err := k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcEniBLBServiceName); err != nil {
		return resources, err
	}
	errReady := common.WaitForResourceReady(ctx, &k8sService)

	// 5.收集创建的IaaS资源信息
	logger.Infof(ctx, "Case %s: Step collect created IaaS resource", SvcEniBLBCaseName)
	eip, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, svcEniBLBServiceName)
	if eip != "" {
		resources = append(resources, cases.Resource{
			CaseName: SvcEniBLBCaseName,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
	}
	blbID, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, svcEniBLBServiceName)
	if blbID != "" && blbID != createdBLBID {
		resources = append(resources, cases.Resource{
			CaseName: SvcEniBLBCaseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blbID,
		})
		if blbID != "" && blbID != createdBLBID {
			logger.Errorf(ctx, "Service %s/%s blb: %s not use createdBLBID: %s", defaultNamespace, svcEniBLBServiceName, blbID, createdBLBID)
			return resources, fmt.Errorf("service blb: %s not use createdBLBID: %s", blbID, createdBLBID)
		}
	}
	if errReady != nil {
		return resources, errReady
	}

	// 6.删除 Service
	logger.Infof(ctx, "Case %s: Step delete LB Service", SvcEniBLBCaseName)
	err = c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, svcEniBLBServiceName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Delete service %s/%s failed: %s", defaultNamespace, svcEniBLBServiceName, err)
	}

	logger.Infof(ctx, "Case %s: End", SvcEniBLBCaseName)
	return resources, nil
}

func (c *svcEniBLB) Clean(ctx context.Context) error {
	return nil
}

func (c *svcEniBLB) Continue(ctx context.Context) bool {
	return true
}

func (c *svcEniBLB) ConfigFormat() string {
	return ""
}
