// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/11/26 15:57:00, by <EMAIL>, create
*/
/*
使用预设 EIP 和预设带宽的 PodBackend LB Service 集成测试
*/

package podbackend_lb_service

import (
	"context"
	"errors"
	"fmt"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	SvcEniEIPCustomizedCaseName    cases.CaseName = "LoadBalancerService-PodBackend-EIP-Customized"
	svcEniEIPCustomizedServiceName                = "lb-svc-pod-backend-eip-customized"
	svcEniEIPCustomizedYAML                       = `
apiVersion: v1
kind: Service
metadata:
  name: lb-svc-pod-backend-eip-customized
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-backend-type: "eni"
    service.beta.kubernetes.io/cce-elastic-ip-payment-timing: "Postpaid"
    service.beta.kubernetes.io/cce-elastic-ip-billing-method: "ByTraffic"
    service.beta.kubernetes.io/cce-elastic-ip-bandwidth-in-mbps: "[EIP_BANDWIDTH]"
spec:
  selector:
    app: lb-svc-pod-backend-eip-customized
  type: LoadBalancer
  ports:
  - name: nginx
    protocol: TCP
    port: 80
    targetPort: 80`
)

func init() {
	cases.AddCase(context.TODO(), SvcEniEIPCustomizedCaseName, NewSvcEniEIPCustomized)
}

var _ cases.Interface = &svcEniEIPCustomized{}

type svcEniEIPCustomized struct {
	base *cases.BaseClient
}

func NewSvcEniEIPCustomized(ctx context.Context) cases.Interface {
	return &svcEniEIPCustomized{}
}

func (c *svcEniEIPCustomized) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *svcEniEIPCustomized) Name() cases.CaseName {
	return SvcEniEIPCustomizedCaseName
}

func (c *svcEniEIPCustomized) Desc() string {
	return "使用预设 EIP 和预设带宽的 PodBackend LB Service 集成测试"
}

func (c *svcEniEIPCustomized) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)

	bandwidth := "50"

	// 1.部署 LB Service
	logger.Infof(ctx, "Case %s: Step deploy podbackend-lb-service", SvcEniEIPCustomizedCaseName)
	strYml := strings.ReplaceAll(svcEniEIPCustomizedYAML, "[EIP_BANDWIDTH]", bandwidth)
	err := common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, strYml)
	if err != nil {
		logger.Errorf(ctx, "Deploy Service %s/%s failed: %s", defaultNamespace, svcEniEIPCustomizedServiceName, err)
		return resources, err
	}

	// 2.等待 LB Service BLB 和 EIP Ready, 并统计 BLB/EIP 资源
	logger.Infof(ctx, "Case %s: Step collect created IaaS resource", SvcEniEIPCustomizedCaseName)
	var k8sService common.K8SService
	if err := k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcEniEIPCustomizedServiceName); err != nil {
		return nil, err
	}
	errReady := common.WaitForResourceReady(ctx, &k8sService)
	blbID, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, svcEniEIPCustomizedServiceName)
	if blbID != "" {
		resources = append(resources, cases.Resource{
			CaseName: SvcEniEIPCustomizedCaseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blbID,
		})
	}
	eip, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, svcEniEIPCustomizedServiceName)
	if eip != "" {
		resources = append(resources, cases.Resource{
			CaseName: SvcEniEIPCustomizedCaseName,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
	}
	if errReady != nil {
		return resources, err
	}

	// 3.检查 EIP 信息是否符合预期(bandwidth := "50")
	logger.Infof(ctx, "Case %s: Step check EIP info is same as expect", SvcEniEIPCustomizedCaseName)
	var eipBandwidth common.EIPBandwidth
	if err = eipBandwidth.NewEIPBandwidth(ctx, c.base, eip, bandwidth); err != nil {
		return resources, err
	}
	currentBandwidth, err := eipBandwidth.GetEIPBandwidth(ctx, eip)
	if err != nil {
		return resources, err
	}
	if bandwidth != currentBandwidth {
		logger.Errorf(ctx, "Service %s/%s EIP %s bandwidth is %s, not %s", defaultNamespace, svcEniEIPCustomizedServiceName, eip, currentBandwidth, bandwidth)
		return resources, fmt.Errorf("service %s/%s EIP %s bandwidth is %s, not %s", defaultNamespace, svcEniEIPCustomizedServiceName, eip, currentBandwidth, bandwidth)
	}
	logger.Infof(ctx, "Service %s/%s EIP %s bandwidth is %s, equal to %s", defaultNamespace, svcEniEIPCustomizedServiceName, eip, currentBandwidth, bandwidth)

	// 4. 改变 service 中的 EIP 的 bandwidth 等到 EIP bandwidth 更新(bandwidth := "100")
	logger.Infof(ctx, "Case %s: Step change EIP bandwidth and wait&check", SvcEniEIPCustomizedCaseName)
	bandwidth = "100"
	strYml = strings.ReplaceAll(svcEniEIPCustomizedYAML, "[EIP_BANDWIDTH]", bandwidth)
	err = common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, strYml)
	if err != nil {
		logger.Errorf(ctx, "Deploy Service %s/%s failed: %s", defaultNamespace, svcEniEIPCustomizedServiceName, err)
		return resources, err
	}
	eipBandwidth.SetBandwidth(bandwidth)
	if err = common.WaitForResourceReady(ctx, &eipBandwidth); err != nil {
		return resources, err
	}

	logger.Infof(ctx, "Case %s: End", SvcEniEIPCustomizedCaseName)
	return resources, nil
}

func (c *svcEniEIPCustomized) Clean(ctx context.Context) error {
	err := c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, svcEniEIPCustomizedServiceName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Fail to clean svc: %v ", err)
		return err
	}

	return nil
}

func (c *svcEniEIPCustomized) Continue(ctx context.Context) bool {
	return true
}

func (c *svcEniEIPCustomized) ConfigFormat() string {
	return ""
}
