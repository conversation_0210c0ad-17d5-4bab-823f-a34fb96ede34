// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/02 14:56:00, by l<PERSON><PERSON>@baidu.com, create
2020/11/26 15:57:00, by <EMAIL>, update
*/
/*
使用 InternalVPC BLB 的 PodBackend LB Service 集成测试
*/

package podbackend_lb_service

import (
	"context"
	"errors"
	"net"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	SvcEniVpcBLBCaseName    cases.CaseName = "LoadBalancerService-PodBackend-InternalVPC-BLB"
	svcEniVpcBLBServiceName                = "lb-svc-pod-backend-internalvpc-blb"
	svcEniVpcBLBYAML                       = `kind: Service
apiVersion: v1
metadata:
  name: lb-svc-pod-backend-internalvpc-blb
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-internal-vpc: "true"
    service.beta.kubernetes.io/cce-load-balancer-backend-type: "eni"
spec:
  selector:
    app: lb-svc-pod-backend-internalvpc-blb
  type: LoadBalancer
  ports:
  - name: nginx
    protocol: TCP
    port: 80
    targetPort: 80`
)

func init() {
	cases.AddCase(context.TODO(), SvcEniVpcBLBCaseName, NewSvcEniVpcBLB)
}

var _ cases.Interface = &svcEniVpcBLB{}

type svcEniVpcBLB struct {
	base *cases.BaseClient
}

func NewSvcEniVpcBLB(ctx context.Context) cases.Interface {
	return &svcEniVpcBLB{}
}

func (c *svcEniVpcBLB) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *svcEniVpcBLB) Name() cases.CaseName {
	return SvcEniVpcBLBCaseName
}

func (c *svcEniVpcBLB) Desc() string {
	return "使用 InternalVPC BLB 的 PodBackend LB Service 集成测试"
}

func (c *svcEniVpcBLB) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)

	// 1.部署 Service
	logger.Infof(ctx, "Case %s: Step deploy podbackend-lb-service", SvcEniVpcBLBCaseName)
	err := common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, svcEniVpcBLBYAML)
	if err != nil {
		logger.Errorf(ctx, "Deploy Service %s/%s failed: %s", defaultNamespace, svcEniVpcBLBServiceName, err)
		return nil, err
	}

	// 2.收集创建的IaaS资源信息
	logger.Infof(ctx, "Case %s: Step collect created IaaS resource", SvcEniVpcBLBCaseName)
	var k8sService common.K8SService
	if err := k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcEniVpcBLBServiceName); err != nil {
		return nil, err
	}
	errReady := common.WaitForResourceReady(ctx, &k8sService)
	blbID, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, svcEniVpcBLBServiceName)
	if blbID != "" {
		resources = append(resources, cases.Resource{
			CaseName: SvcEniVpcBLBCaseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blbID,
		})
	}
	if errReady != nil {
		return resources, errReady
	}

	// 3. 检查BLB的IP地址应该是VPC内IP
	logger.Infof(ctx, "Case %s: Step check LB Service IP in VPC", SvcEniVpcBLBCaseName)
	ipInternalVpc, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, svcEniVpcBLBServiceName)
	clusterID := c.base.ClusterID
	cluster, err := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "GetCluster %s failed: %s", clusterID, err)
		return nil, err
	}
	logger.Infof(ctx, "GetCluster %s success: %s", clusterID, utils.ToJSON(cluster))
	clusterVPCID := cluster.Cluster.Spec.VPCID
	vpcResp, err := c.base.VPCClient.DescribeVPC(ctx, clusterVPCID, nil)
	if err != nil || vpcResp == nil {
		logger.Errorf(ctx, "failed to get VPC %s %v", clusterVPCID, err)
		return resources, err
	}

	_, vpcCIDR, err := net.ParseCIDR(vpcResp.ShowVPCModel.CIDR)
	if err != nil {
		logger.Errorf(ctx, "vpc CIDR %s illegal %v", vpcResp.ShowVPCModel.CIDR, err)
		return resources, err
	}
	ipBLB := net.ParseIP(ipInternalVpc)
	if !vpcCIDR.Contains(ipBLB) {
		logger.Errorf(ctx, "the BLB IP %s is not in VPC CIDR %s", ipInternalVpc, vpcResp.ShowVPCModel.CIDR)
		return resources, err
	}

	// 4. 检查InternalVPC LB Service 的地址应与 BLB 的地址一致
	logger.Infof(ctx, "Case %s: Step check LB Service IP is equal to BLB IP", SvcEniVpcBLBCaseName)
	blbInfo, err := c.base.AppBLBClient.DescribeAppLoadBalancerByID(ctx, blbID, nil)
	if err != nil {
		logger.Errorf(ctx, "DescribeAppLoadBalancerByID %s failed: %s", blbID, err)
		return resources, err
	}
	if blbInfo == nil {
		logger.Errorf(ctx, "BlbInfo %s is empty", blbID)
		return resources, err
	}
	logger.Infof(ctx, "DescribeAppLoadBalancerByID %s success: %s", blbID, utils.ToJSON(blbInfo))
	if ipInternalVpc != blbInfo.Address {
		logger.Errorf(ctx, "eip:%s address is not blb:%s address:%s", ipInternalVpc, blbID, blbInfo.Address)
		return resources, err
	}
	logger.Infof(ctx, "eip:%s address is blb:%s address:%s", ipInternalVpc, blbID, blbInfo.Address)

	logger.Infof(ctx, "Case %s: End", SvcEniVpcBLBCaseName)
	return resources, nil
}

func (c *svcEniVpcBLB) Clean(ctx context.Context) error {
	err := c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, svcEniVpcBLBServiceName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Fail to clean svc: %v ", err)
		return err
	}

	return nil
}

func (c *svcEniVpcBLB) Continue(ctx context.Context) bool {
	return true
}

func (c *svcEniVpcBLB) ConfigFormat() string {
	return ""
}
