// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/02 14:56:00, by l<PERSON><PERSON>@baidu.com, create
2020/11/26 15:57:00, by <EMAIL>, update
*/
/*
使用预设子网的 PodBackend LB Service 集成测试
*/

package podbackend_lb_service

import (
	"context"
	"errors"
	"fmt"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/vpc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	SvcEniSubnetCaseName    cases.CaseName = "LoadBalancerService-PodBackend-Subnet"
	svcEniSubnetServiceName                = "lb-svc-pod-backend-subnet"
	svcEniSubnetYAML                       = `
apiVersion: v1
kind: Service
metadata:
  name: lb-svc-pod-backend-subnet
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-subnet-id: "[CREATED_SUBNET_ID]"
    service.beta.kubernetes.io/cce-load-balancer-backend-type: "eni"
spec:
  selector:
    app: lb-svc-pod-backend-subnet
  type: LoadBalancer
  ports:
  - name: nginx
    protocol: TCP
    port: 80
    targetPort: 80`
)

func init() {
	cases.AddCase(context.TODO(), SvcEniSubnetCaseName, NewSvcEniSubnet)
}

var _ cases.Interface = &svcEniSubnet{}

type svcEniSubnet struct {
	base *cases.BaseClient
}

func NewSvcEniSubnet(ctx context.Context) cases.Interface {
	return &svcEniSubnet{}
}

func (c *svcEniSubnet) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *svcEniSubnet) Name() cases.CaseName {
	return SvcEniSubnetCaseName
}

func (c *svcEniSubnet) Desc() string {
	return "使用预设子网的 PodBackend LB Service 集成测试"
}

func (c *svcEniSubnet) Check(ctx context.Context) ([]cases.Resource, error) {
	clusterID := c.base.ClusterID
	resources := make([]cases.Resource, 0)

	// 1. 获取 cluster 的 VPC 下的 subnet
	logger.Infof(ctx, "Case %s: Step get cluster subnet", SvcEniSubnetCaseName)
	cluster, err := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "GetCluster %s failed: %s", clusterID, err)
		return resources, err
	}
	logger.Infof(ctx, "GetCluster %s success: %s", clusterID, utils.ToJSON(cluster))
	clusterVPCID := cluster.Cluster.Spec.VPCID
	args := &vpc.ListSubnetArgs{
		VPCID:      clusterVPCID,
		SubnetType: vpc.SubnetTypeBCC,
	}
	subnetList, err := c.base.VPCClient.ListSubnet(ctx, args, nil)
	if err != nil {
		logger.Errorf(ctx, "%s ListSubnet failed: %s", clusterVPCID, err)
		return resources, err
	}

	// 2. 查找非 LBServiceVPCSubnetID 子网
	logger.Infof(ctx, "Case %s: Step search non-LBServiceVPCSubnetID subnet", SvcEniSubnetCaseName)
	subnetID := ""
	subnetName := ""
	lbServiceVPCSubnetID := cluster.Cluster.Spec.ContainerNetworkConfig.LBServiceVPCSubnetID
	for _, subnet := range subnetList {
		if subnet.SubnetID != lbServiceVPCSubnetID {
			subnetID = subnet.SubnetID
			subnetName = subnet.Name
			break
		}
	}
	if subnetID == "" {
		logger.Errorf(ctx, "VPC %s no suitable subnet", clusterVPCID)
		return resources, fmt.Errorf("VPC %s no suitable subnet", clusterVPCID)
	}
	logger.Infof(ctx, "lbServiceVPCSubnetID: %s, subnetID: %s", lbServiceVPCSubnetID, subnetID)

	// 3. 指定子网ID并部署 LB Service
	logger.Infof(ctx, "Case %s: Step deploy podbackend-lb-service with specific SubnetID", SvcEniSubnetCaseName)
	strYml := strings.ReplaceAll(svcEniSubnetYAML, "[CREATED_SUBNET_ID]", subnetID)
	err = common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, strYml)
	if err != nil {
		logger.Errorf(ctx, "Deploy Service %s/%s failed: %s", defaultNamespace, svcEniSubnetServiceName, err)
		return resources, err
	}

	// 4.等待 LB Service BLB 和 EIP Ready, 并统计 BLB/EIP 资源
	logger.Infof(ctx, "Case %s: Step collect created IaaS resource", SvcEniSubnetCaseName)
	var k8sService common.K8SService
	if err = k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcEniSubnetServiceName); err != nil {
		return nil, err
	}
	errReady := common.WaitForResourceReady(ctx, &k8sService)
	blbID, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, svcEniSubnetServiceName)
	if blbID != "" {
		resources = append(resources, cases.Resource{
			CaseName: SvcEniSubnetCaseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blbID,
		})
	}
	eip, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, svcEniSubnetServiceName)
	if eip != "" {
		resources = append(resources, cases.Resource{
			CaseName: SvcEniSubnetCaseName,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
	}
	if errReady != nil {
		return resources, errReady
	}

	// 5.验证 BLB 创建自指定的 subnet 下
	logger.Infof(ctx, "Case %s: Step check whether AppBLB created in specific subnet.", SvcEniSubnetCaseName)
	blbInfo, err := c.base.AppBLBClient.DescribeAppLoadBalancerByID(ctx, blbID, nil)
	if err != nil {
		logger.Errorf(ctx, "DescribeAppLoadBalancerByID %s failed: %s", blbID, err)
		return resources, err
	}
	if blbInfo == nil {
		logger.Errorf(ctx, "BlbInfo %s is empty", blbID)
		return resources, fmt.Errorf("blbInfo %s is empty", blbID)
	}
	logger.Infof(ctx, "DescribeAppLoadBalancerByID %s success: %s", blbID, utils.ToJSON(blbInfo))
	if blbInfo.SubNetName != subnetName {
		logger.Errorf(ctx, "Blb %s subnet is %s, not %s", blbID, blbInfo.SubNetName, subnetName)
		return resources, fmt.Errorf("blb %s subnet is %s, not %s", blbID, blbInfo.SubNetName, subnetName)
	}
	logger.Infof(ctx, "Blb %s subnet is %s, equal to %s (SubNetID: %s)", blbID, blbInfo.SubNetName, subnetName, subnetID)

	logger.Infof(ctx, "Case %s: End", SvcEniSubnetCaseName)
	return resources, nil
}

func (c *svcEniSubnet) Clean(ctx context.Context) error {
	err := c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, svcEniSubnetServiceName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Fail to clean svc: %v ", err)
		return err
	}

	return nil
}

func (c *svcEniSubnet) Continue(ctx context.Context) bool {
	return true
}

func (c *svcEniSubnet) ConfigFormat() string {
	return ""
}
