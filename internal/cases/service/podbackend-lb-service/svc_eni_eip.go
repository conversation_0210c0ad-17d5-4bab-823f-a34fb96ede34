// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/02 14:56:00, by l<PERSON><PERSON>@baidu.com, create
2020/11/26 15:57:00, by <EMAIL>, update
*/
/*
使用预设 EIP 的 PodBackend LB Service 集成测试
*/

package podbackend_lb_service

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	eipsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	SvcEniEIPCaseName    cases.CaseName = "LoadBalancerService-PodBackend-EIP"
	svcEniEIPServiceName                = "lb-svc-pod-backend-eip"
	svcEniEIPYAML                       = `
apiVersion: v1
kind: Service
metadata:
  name: lb-svc-pod-backend-eip
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-backend-type: "eni"
spec:
  selector:
    app: lb-svc-pod-backend-eip
  type: LoadBalancer
  loadBalancerIP: [CREATED_EIP]
  ports:
  - name: nginx
    protocol: TCP
    port: 80
    targetPort: 80`
)

func init() {
	cases.AddCase(context.TODO(), SvcEniEIPCaseName, NewSvcEniEIP)
}

var svcEniEIPIP string
var _ cases.Interface = &svcEniEIP{}

type svcEniEIP struct {
	base *cases.BaseClient
}

func NewSvcEniEIP(ctx context.Context) cases.Interface {
	return &svcEniEIP{}
}

func (c *svcEniEIP) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *svcEniEIP) Name() cases.CaseName {
	return SvcEniEIPCaseName
}

func (c *svcEniEIP) Desc() string {
	return "使用预设 EIP 的 PodBackend LB Service 集成测试"
}

func (c *svcEniEIP) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)

	// 1. 创建并收集 EIP 资源, 等待 EIP 状态为 available
	logger.Infof(ctx, "Case %s: Step create EIP and waiting ready", SvcEniEIPCaseName)
	args := &eipsdk.CreateEIPArgs{
		BandwidthInMbps: 1,
		Billing: &eipsdk.Billing{
			PaymentTiming: "Postpaid",
			BillingMethod: "ByTraffic",
		},
		Name: svcEniEIPServiceName,
	}
	createdEIP, err := c.base.EIPClient.CreateEIP(ctx, args, nil)
	if err != nil {
		logger.Errorf(ctx, "CreateEIP failed: %s", err)
		return resources, err
	}
	if createdEIP != "" {
		svcEniEIPIP = createdEIP
		resources = append(resources, cases.Resource{
			CaseName: SvcEniEIPCaseName,
			Type:     cases.ResourceTypeEIP,
			ID:       createdEIP,
		})
	}
	logger.Infof(ctx, "createdEIP: %s", createdEIP)
	var eipStatus common.EIPStatus
	if err = eipStatus.NewEIPStatus(ctx, c.base, createdEIP, "available"); err != nil {
		return resources, err
	}
	if err = common.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	// 2. 替换YAML文件中EIP为创建好的EIP, 部署 LB Service
	logger.Infof(ctx, "Case %s: Step deploy podbackend-lb-service with specific EIP", SvcEniEIPCaseName)
	strYml := strings.ReplaceAll(svcEniEIPYAML, "[CREATED_EIP]", createdEIP)
	err = common.DeployYml(ctx, c.base.KubectlClient, c.base.KubeConfig, strYml)
	if err != nil {
		logger.Errorf(ctx, "Deploy Service %s/%s failed: %s", defaultNamespace, svcEniEIPServiceName, err)
		return resources, err
	}

	// 3.等待 LB Service BLB 和 EIP Ready, 并统计 BLB 资源
	logger.Infof(ctx, "Case %s: Step collect created IaaS resource", SvcEniEIPCaseName)
	var k8sService common.K8SService
	if err := k8sService.NewK8SService(ctx, c.base, defaultNamespace, svcEniEIPServiceName); err != nil {
		return nil, err
	}
	errReady := common.WaitForResourceReady(ctx, &k8sService)
	blbID, _ := k8sService.GetServiceBlB(ctx, defaultNamespace, svcEniEIPServiceName)
	if blbID != "" {
		resources = append(resources, cases.Resource{
			CaseName: SvcEniEIPCaseName,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blbID,
		})
	}
	eip, _ := k8sService.GetServiceEIP(ctx, defaultNamespace, svcEniEIPServiceName)
	if eip != "" && eip != createdEIP {
		resources = append(resources, cases.Resource{
			CaseName: SvcEniEIPCaseName,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
		if eip != createdEIP {
			logger.Errorf(ctx, "Service %s/%s EIP: %s not use createdEIP: %s", defaultNamespace, svcEniEIPServiceName, eip, createdEIP)
			return resources, fmt.Errorf("service EIP: %s not use createdEIP: %s", eip, createdEIP)
		}
	}

	if errReady != nil {
		return resources, errReady
	}

	// 4. 检查 EIP 状态是否为 binded
	logger.Infof(ctx, "Case %s: Step check EIP status", SvcEniEIPCaseName)
	eipStatus.SetStatus("binded")
	if err = common.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	// 5. 删除 service后 createdEIP 应保留且状态为 available
	logger.Infof(ctx, "Case %s: Step delete LB Service and check existence & availability of EIP", SvcEniEIPCaseName)
	err = c.base.K8SClient.CoreV1().Services(defaultNamespace).Delete(ctx, svcEniEIPServiceName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Delete service %s/%s failed: %s", defaultNamespace, svcEniEIPServiceName, err)
		return resources, err
	}

	time.Sleep(15 * time.Second)

	logger.Infof(ctx, "Delete service %s/%s success", defaultNamespace, svcEniEIPServiceName)
	eipStatus.SetStatus("available")
	if err = common.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	logger.Infof(ctx, "Case %s: End", SvcEniEIPCaseName)
	return resources, nil
}

func (c *svcEniEIP) Clean(ctx context.Context) error {
	// 删除创建的 EIP 资源
	err := c.base.EIPClient.DeleteEIP(ctx, svcEniEIPIP, nil)
	if err != nil {
		logger.Errorf(ctx, "Fail to clean eip: %v ", err)
		return err
	}

	return nil
}

func (c *svcEniEIP) Continue(ctx context.Context) bool {
	return true
}

func (c *svcEniEIP) ConfigFormat() string {
	return ""
}
