package cases

import (
	"context"
)

// CaseName - Case 名
type CaseName string

// Interface - 定义具体 Case 实现方法
type Interface interface {
	// Name 返回 Case 名
	Name() CaseName

	// Desc 返回 Case 描述
	Desc() string

	// Init 初始化对象
	Init(ctx context.Context, config []byte, base *BaseClient) error

	// Check 前置准备
	Check(ctx context.Context) ([]Resource, error)

	// Clean 环境清理
	Clean(ctx context.Context) error

	// Continue 是否后是否继续
	Continue(ctx context.Context) bool

	// ConfigFormat 返回 config string 示例
	ConfigFormat() string
}

type NewCaseFunc func(ctx context.Context) Interface

type CaseList map[CaseName]NewCaseFunc

// Resource - 定义 Case 创建 IaaS 资源, 用于最后回收确认
type Resource struct {
	CaseName CaseName     `json:"caseName"`
	Type     ResourceType `json:"type"`
	ID       string       `json:"id"`
}

// ResourceType - Case 资源类别
type ResourceType string

const (
	// ResourceTypeEIP - OpenAPI 创建 EIP
	ResourceTypeEIP ResourceType = "EIP"

	// ResourceTypeBLB - OpenAPI 创建 BLB
	ResourceTypeBLB ResourceType = "BLB"

	// ResourceTypeAppBLB - OpenAPI 创建 AppBLB
	ResourceTypeAppBLB ResourceType = "AppBLB"

	// ResourceTypeBosBucket - OpenAPI 创建 BosBucket
	ResourceTypeBOSBucket ResourceType = "BOSBucket"

	// ResourceTypeCfsFileSystem - OpenAPI 创建 CfsFileSystem
	ResourceTypeCFSFileSystem ResourceType = "CFSFileSystem"

	// ResourceTypeCCEInstance - OpenAPI 创建 CCEInstance
	ResourceTypeCCEInstance ResourceType = "CCEInstance"

	// ResourceTypeCCECluster - OpenAPI 创建 CCECluster
	ResourceTypeCCECluster ResourceType = "CCECluster"

	// ResourceTypeCCEInstanceGroup - OpenAPI 创建 CCEInstanceGroup
	ResourceTypeCCEInstanceGroup ResourceType = "CCEInstanceGroup"
)

// cceCaseList - 包含 CCE 所有 Test Case
var cceCaseList = make(CaseList)

// CCECaseList - 返回 CCE 所有 TestCase
func CCECaseList(ctx context.Context) CaseList {
	return cceCaseList
}

// AddCase - 增加 Case
func AddCase(ctx context.Context, name CaseName, f NewCaseFunc) {
	cceCaseList[name] = f
}
