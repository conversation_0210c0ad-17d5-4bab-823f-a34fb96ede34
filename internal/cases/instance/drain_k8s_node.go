/*
1、创建工作负载
2、排水成功，并成功驱逐节点上的pod
3、排水并删除节点
*/

package instance

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	v1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"sigs.k8s.io/yaml"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/errorcode"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	DrainK8SNode cases.CaseName = "DrainK8SNode"
)

const (
	testDeploymentName = "drain-node-test"
	testimage          = "registry.baidubce.com/cce/nginx-alpine-go:latest"
)

type drainK8SNode struct {
	base *cases.BaseClient
}

func init() {
	cases.AddCase(context.TODO(), DrainK8SNode, NewDrainK8SNode)
}

func NewDrainK8SNode(ctx context.Context) cases.Interface {
	return &drainK8SNode{}
}

func (c *drainK8SNode) Name() cases.CaseName {
	return DrainK8SNode
}

func (c *drainK8SNode) Desc() string {
	return "节点排水"
}

func (c *drainK8SNode) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base

	return nil
}

func (c *drainK8SNode) Check(ctx context.Context) (resources []cases.Resource, err error) {

	clusterID := c.base.ClusterID
	cluster, err := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "GetCluster %s failed: %s", clusterID, err)
		return resources, err
	}
	logger.Infof(ctx, "GetCluster %s success: %s", clusterID, utils.ToJSON(cluster))

	// 0. 等待集群所有节点是running状态并且namespace也为active 不影响排水
	err = c.ensureInstanceAndNameSpace(ctx)
	if err != nil {
		return resources, err
	}
	logger.Infof(ctx, "cluster instance running and namespace active")

	instances, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
		ClusterRole: ccetypes.ClusterRoleNode,
	}, nil)
	if err != nil {
		logger.Errorf(ctx, "list instance failed: %v", err.Error())
		return nil, err
	}
	if len(instances.InstancePage.InstanceList) == 0 {
		return nil, errors.New("cluster instance list is empty")
	}
	instanceID := instances.InstancePage.InstanceList[0].Spec.CCEInstanceID
	nodeIP := instances.InstancePage.InstanceList[0].Status.Machine.VPCIP

	// 1、创建工作负载
	err = c.ensureDeploymentRunning(ctx, 1, nodeIP)
	if err != nil {
		logger.Errorf(ctx, err.Error())
		return nil, err
	}

	// 2、节点排水并等待节点排水完成
	err = c.drainNodes(ctx, instanceID)
	if err != nil {
		logger.Errorf(ctx, "drain instance failed: %v", err.Error())
		return nil, err
	}

	// 3、节点排水并删除节点
	err = c.deleteAndDrainNodes(ctx, []string{instanceID}, &ccetypes.DeleteOption{
		DrainNode:         true,
		MoveOut:           false,
		DeleteResource:    true,
		DeleteCDSSnapshot: true,
	})
	if err != nil {
		logger.Errorf(ctx, "delete and drain instance failed: %v", err.Error())
		return nil, err
	}

	return nil, nil
}

func (c *drainK8SNode) Clean(ctx context.Context) error {
	return nil
}

func (c *drainK8SNode) Continue(ctx context.Context) bool {
	return true
}

func (c *drainK8SNode) ConfigFormat() string {
	return ""
}

func (c *drainK8SNode) ensureDeploymentRunning(ctx context.Context, replicas int32, nodeIP string) error {
	logger.Infof(ctx, "start to deploy test deployment, replicas: %d", replicas)
	deployment := v1.Deployment{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Deployment",
			APIVersion: "apps/v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      testDeploymentName,
			Namespace: corev1.NamespaceDefault,
			Labels: map[string]string{
				"app": testDeploymentName,
			},
		},
		Spec: v1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": testDeploymentName,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": testDeploymentName,
					},
				},
				Spec: corev1.PodSpec{
					NodeSelector: map[string]string{"kubernetes.io/hostname": nodeIP},
					Containers: []corev1.Container{{
						Name:            testDeploymentName,
						Image:           testimage,
						ImagePullPolicy: corev1.PullIfNotPresent,
					}},
				},
			},
		},
	}
	out, err := yaml.Marshal(&deployment)
	if err != nil {
		return fmt.Errorf("marshal deployment failed, err: %v", err)
	}
	deploymentYaml := string(out)
	_, err = c.base.AppClient.PostAppResource(ctx, c.base.ClusterID, testDeploymentName, deploymentYaml, corev1.NamespaceDefault, nil)
	if err != nil {
		return fmt.Errorf("create deployment failed, err: %v", err)
	}

	// 尝试部署deployment到指定节点，等待所有副本running
	err = common.WaitForFunc(ctx, func(ctx context.Context) (err error) {
		deploymentResource, getErr := c.base.AppClient.GetAppDeploymentByName(ctx, c.base.ClusterID, testDeploymentName, corev1.NamespaceDefault, nil)
		if getErr != nil {
			err = fmt.Errorf("get deployment Resource: %v failed, err: %v", testDeploymentName, err)
			return
		}
		if deploymentResource.Status != "Running" || deploymentResource.StatusInfo.Available != deploymentResource.StatusInfo.Replicas {
			err = fmt.Errorf("deployment status: %v, available: %d, expect replicas: %d", deploymentResource.Status,
				deploymentResource.StatusInfo.Available, deploymentResource.StatusInfo.Replicas)
			return
		}
		return
	}, 5*time.Second, 2*time.Minute)
	if err != nil {
		return fmt.Errorf("wait for deployment running failed, err: %v", err)
	}
	logger.Infof(ctx, "deployment %s is running", testDeploymentName)
	return nil
}

func (c *drainK8SNode) drainNodes(ctx context.Context, cceInstanceID string) error {

	// 节点排水并等待完成
	_, err := c.base.CCEHostClient.DrainNodes(ctx, &ccev2.DrainNodesRequest{
		ClusterID:      c.base.ClusterID,
		CCEInstanceIDs: []string{cceInstanceID},
		DrainNodeConfig: &utils.DrainNodeConfig{
			Force:               true,
			GracePeriodSeconds:  -1,
			Timeout:             4 * time.Minute,
			IgnoreAllDaemonSets: true,
		},
	}, nil)
	if err != nil {
		logger.Errorf(ctx, "instance %v drain failed: %v", cceInstanceID, err.Error())
		return err
	}
	// 等待节点成功排水
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	timer := time.NewTimer(4 * time.Minute)
	defer timer.Stop()

	for {
		select {
		case <-ticker.C:
			getInstanceResponse, err := c.base.CCEClient.GetInstance(ctx, c.base.ClusterID, cceInstanceID, nil)
			if err != nil {
				logger.Errorf(ctx, "get instance failed: %v", err.Error())
			}
			if getInstanceResponse.Instance.Status.InstanceDrain.InstanceDrainPhase == ccetypes.InstanceDrainPhaseDrainSuccessful {
				for _, pod := range getInstanceResponse.Instance.K8SNode.PodList.Pods {
					if pod.ObjectMeta.Labels["app"] == testDeploymentName {
						return fmt.Errorf("drain instances failed: %v", err)
					}
				}
				logger.Infof(ctx, "drain instances success")
				return nil
			}

		case <-timer.C:
			return fmt.Errorf("timeout waiting for drain instances succeeded")
		}
	}

}

// DeleteAndDrainNodes - 删除实例
func (c *drainK8SNode) deleteAndDrainNodes(ctx context.Context, ids []string, deleteOption *ccetypes.DeleteOption) error {
	if c.base.ClusterID == "" {
		return errors.New("clusterID is empty")
	}

	if len(ids) == 0 {
		return errors.New("ids is empty")
	}

	// 删除 Instance
	_, err := c.base.CCEClient.DeleteInstances(ctx, c.base.ClusterID, &ccev2.DeleteInstancesRequest{
		InstanceIDs:  ids,
		DeleteOption: deleteOption,
	}, nil)
	if err != nil {
		logger.Errorf(ctx, "DeleteInstances %v failed: %s", ids, err)
		return err
	}

	waitDeleteTimeout := time.Minute * 10
	timeoutCtx, cancel := context.WithTimeout(ctx, waitDeleteTimeout)
	// 等待 Instance 删除成功, 等待 10 min
	success := map[string]any{}
	drainStepExist := map[string]any{}

	wait.UntilWithContext(timeoutCtx, func(ctx context.Context) {
		if len(success) == len(ids) {
			logger.Infof(ctx, "Instances %v has been deleted", ids)
			cancel()
			return
		}

		for _, id := range ids {
			if _, ok := success[id]; ok {
				continue
			}
			resp, getErr := c.base.CCEClient.GetInstance(ctx, c.base.ClusterID, id, nil)
			if getErr != nil {
				// 已经删除成功
				if strings.Contains(getErr.Error(), errorcode.NewInstanceNotFound().Code) {
					success[id] = nil
					continue
				}
				logger.Errorf(ctx, "GetInstance %s failed: %s", id, err)
				continue
			}
			// 排水状态判断
			getEventStepsResponse, err := c.base.CCEClient.GetInstanceEventSteps(ctx, id, nil)
			if err != nil {
				logger.Errorf(ctx, "get instance event steps failed: %v", err.Error())
			}
			if getEventStepsResponse == nil || len(getEventStepsResponse.Steps) == 0 {
				logger.Errorf(ctx, "get instance event steps failed: no step")
			} else {
				for _, step := range getEventStepsResponse.Steps {
					if step.StepName == ccev2.NodeStepDrainK8SNode {
						drainStepExist[id] = nil
					}
				}
			}

			instance := resp.Instance
			if instance.Status.InstancePhase == ccetypes.InstancePhaseDeleteFailed {
				logger.Errorf(ctx, "Instance %s deleted failed", id)
				continue
			}

			logger.Infof(ctx, "Instance %s still exists", id)
		}

	}, time.Second*20)

	if errors.Is(timeoutCtx.Err(), context.DeadlineExceeded) {
		logger.Errorf(ctx, "wait instance delete timeout for %s", waitDeleteTimeout.String())
	}

	// 返回未释放的 cceInstanceIDs
	if len(success) != len(ids) {
		notReady := make([]string, 0, len(ids))
		for _, id := range ids {
			if _, ok := success[id]; !ok {
				notReady = append(notReady, id)
			}
		}
		return fmt.Errorf("Instance %v not deleted", notReady)
	}

	// 返回缺失节点排水状态的 cceInstanceID
	if len(drainStepExist) != len(ids) {
		notStep := make([]string, 0, len(ids))
		for _, id := range ids {
			if _, ok := drainStepExist[id]; !ok {
				notStep = append(notStep, id)
			}
		}
		return fmt.Errorf("Instance %v no drain step", notStep)
	}

	return nil
}

func (c *drainK8SNode) ensureInstanceAndNameSpace(ctx context.Context) error {

	instancesK8sNodes, err := common.NewInstancesK8sNodes(ctx, c.base, c.base.ClusterID)
	if err != nil {
		return err
	}
	err = common.WaitForResourceReady(ctx, instancesK8sNodes)
	if err != nil {
		return err
	}

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	timer := time.NewTimer(4 * time.Minute)
	defer timer.Stop()
	for {
		select {
		case <-ticker.C:
			namespaceList, nsErr := c.base.AppClient.GetNamespaceList(ctx, c.base.ClusterID, nil)
			if nsErr != nil {
				return fmt.Errorf("get namespace list failed: %v", nsErr)
			}
			if namespaceList.Namespaces == nil || len(namespaceList.Namespaces) == 0 {
				return fmt.Errorf("namespace list is empty")
			}

			allActive := true
			for _, namespace := range namespaceList.Namespaces {
				if namespace.Phase != corev1.NamespaceActive {
					allActive = false
					break
				}
			}
			if allActive {
				return nil
			}
		case <-timer.C:
			return fmt.Errorf("timeout waiting for all namespace to be ready")
		}
	}
}
