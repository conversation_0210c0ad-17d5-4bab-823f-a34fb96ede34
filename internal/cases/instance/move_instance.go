/* move_instance.go */
/*
modification history
--------------------
2022/10/18, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
将集群中已有节点进行移出，并将该节点重新移入
// @step: 0. 等待集群所有节点都running
// @step: 1. 将节点列表中的第一个节点移出集群（不清理bcc资源），并进行资源校验
// @step: 2. 对刚移出的bcc添加描述信息，将该实例移入集群
// @step: 3. 移入过程中对已有实例二次移入，校验幂等，预期移入失败
// @step: 4. 移入成功后对已有实例二次移入，校验幂等，预期移入失败
*/

package instance

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	bccsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	// MoveInstancesCaseName - case 名字
	MoveInstancesCaseName cases.CaseName = "MoveInstances"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), MoveInstancesCaseName, NewMoveInstances)
}

var _ cases.Interface = &moveInstances{}

type moveInstances struct {
	base   *cases.BaseClient
	config *moveInstancesConfig
}

// moveInstancesConfig - 定义接收 Init() config []byte 中反序列化结构体
type moveInstancesConfig struct {
	// TODO: 支持重建
	Password string `json:"password"`
}

// NewMoveInstances - 测试案例
func NewMoveInstances(ctx context.Context) cases.Interface {
	return &moveInstances{}
}

func (c *moveInstances) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base

	// 初始化参数
	var moveConfig moveInstancesConfig
	if err := json.Unmarshal(config, &moveConfig); err != nil {
		return fmt.Errorf("json.Unmarshal failed: %s", err)
	}

	// 用于扩容参数
	c.config = &moveConfig

	return nil
}

func (c *moveInstances) Name() cases.CaseName {
	return MoveInstancesCaseName
}

func (c *moveInstances) Desc() string {
	return "将集群中已有节点进行移出，并将该节点重新移入"
}

func (c *moveInstances) Check(ctx context.Context) ([]cases.Resource, error) {
	var err error
	resources := make([]cases.Resource, 0)
	clusterID := c.base.ClusterID

	// 0. 等待集群所有节点是running状态
	instancesK8sNodes, err := common.NewInstancesK8sNodes(ctx, c.base, clusterID)
	if err != nil {
		return resources, err
	}
	err = common.WaitForResourceReady(ctx, instancesK8sNodes)
	if err != nil {
		return resources, err
	}

	// 1. 将节点列表中的第一个节点移出集群（不清理bcc资源），并进行资源校验
	instanceID, err := MoveOutInstance(ctx, c.base, clusterID)
	if err != nil {
		return resources, err
	}

	// 2. 对刚移出的bcc添加描述信息，将该实例移入集群
	err = c.base.BCCClient.ModifyInstanceDesc(ctx, instanceID, &bccsdk.ModifyInstanceDescArgs{
		Description: "add by cce e2e: 测试'Test',\"123_\\n\"",
	}, nil)
	if err != nil {
		logger.Errorf(ctx, "modify instance description failed: %v", err.Error())
		return resources, err
	}

	err = MoveExistedInstanceToCluster(ctx, c.base, c.config.Password, []string{instanceID})
	if err != nil {
		logger.Errorf(ctx, "move existed instance to cluster failed: %v", err.Error())
		return resources, err
	}

	// 3. 移入过程中对已有实例二次移入，校验幂等，预期移入失败
	err = MoveExistedInstanceToCluster(ctx, c.base, c.config.Password, []string{instanceID})
	if err != nil {
		logger.Warnf(ctx, "moveExistedInstanceToCluster twice failed but as except: %v", err.Error())
	} else {
		return resources, errors.New("moveExistedInstanceToCluster twice success, except failed")
	}

	// 4. 移入成功后对已有实例二次移入，校验幂等，预期移入失败
	err = common.WaitForResourceReady(ctx, instancesK8sNodes)
	if err != nil {
		return resources, err
	}
	err = MoveExistedInstanceToCluster(ctx, c.base, c.config.Password, []string{instanceID})
	if err != nil {
		logger.Warnf(ctx, "moveExistedInstanceToCluster twice(instance already running) failed but as except: %v", err.Error())
	} else {
		return resources, errors.New("moveExistedInstanceToCluster twice success, except failed")
	}

	return resources, nil
}

func (c *moveInstances) Clean(ctx context.Context) error {
	return nil
}

func (c *moveInstances) Continue(ctx context.Context) bool {
	return false
}

func (c *moveInstances) ConfigFormat() string {
	return ""
}

// MoveOutInstance 将集群的node列表中第一个实例进行移出，并等待删除完毕
func MoveOutInstance(ctx context.Context, baseClient *cases.BaseClient, clusterID string) (string, error) {
	instances, err := baseClient.CCEClient.ListInstancesByPage(ctx, clusterID, &ccev2.ListInstancesByPageParams{
		ClusterRole: ccetypes.ClusterRoleNode,
	}, nil)
	if err != nil {
		logger.Errorf(ctx, "list instance failed: %v", err.Error())
		return "", err
	}

	if len(instances.InstancePage.InstanceList) == 0 {
		return "", errors.New("cluster instance list is empty")
	}
	instanceID := instances.InstancePage.InstanceList[0].Status.Machine.InstanceID

	err = baseClient.DeleteInstances(ctx, clusterID, []string{instanceID}, &ccetypes.DeleteOption{
		MoveOut:           true,
		DeleteResource:    false,
		DeleteCDSSnapshot: false,
	}, false)
	if err != nil {
		logger.Errorf(ctx, "move out instance failed: %v", err.Error())
		return "", err
	}

	return instanceID, nil
}

// MoveExistedInstanceToCluster 创建已有实例的节点（为了验证实例在移入过程中再次出发移入的场景，没有直接使用baseClient.CreateInstances）
func MoveExistedInstanceToCluster(ctx context.Context, baseClient *cases.BaseClient, password string, instanceIDs []string) error {
	var args []*ccev2.InstanceSet
	for _, instanceID := range instanceIDs {
		args = append(args, &ccev2.InstanceSet{
			InstanceSpec: ccetypes.InstanceSpec{
				Existed: true,
				ExistedOption: ccetypes.ExistedOption{
					ExistedInstanceID: instanceID,
					Rebuild:           func(rebuild bool) *bool { return &rebuild }(true),
				},
				InstanceOS: ccetypes.InstanceOS{
					ImageType: "System",
					ImageName: "7.6 x86_64 (64bit)",
					OSType:    "linux",
					OSName:    "CentOS",
					OSVersion: "7.6",
					OSArch:    "x86_64 (64bit)",
				},
				MachineType:   ccetypes.MachineTypeBCC,
				AdminPassword: password,
			},
		})
	}

	_, err := baseClient.CCEClient.CreateInstances(ctx, baseClient.ClusterID, args, nil)
	if err != nil {
		return fmt.Errorf("Error creating instances: %v ", err)
	}
	return nil
}
