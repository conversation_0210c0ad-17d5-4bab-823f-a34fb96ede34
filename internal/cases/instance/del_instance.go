// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/06/08 15:48:00, by <EMAIL>, create
*/
/*
Del Instance, 包含 Add Instance 的测试
*/

package instance

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bccimage"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	// DelInstancesCaseName - case 名字
	DelInstancesCaseName cases.CaseName = "DelInstances"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), DelInstancesCaseName, NewDelInstances)
}

var _ cases.Interface = &delInstnaces{}

type delInstnaces struct {
	base *cases.BaseClient

	config *delInstanesConfig

	addClient *addInstances // 复用 addInstances 的扩容方法
}

// delInstancesConfig - 定义接收 Init() config []byte 中反序列化结构体
type delInstanesConfig struct {
	InstanceSets []*ccev2.InstanceSet   `json:"instanceSets"`
	DeleteOption *ccetypes.DeleteOption `json:"deleteOption"`
}

// NewDelInstances - 测试案例
func NewDelInstances(ctx context.Context) cases.Interface {
	return &delInstnaces{}
}

func (c *delInstnaces) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base

	// 初始化参数
	var delConfig delInstanesConfig
	if err := json.Unmarshal(config, &delConfig); err != nil {
		return fmt.Errorf("json.Unmarshal failed: %s", err)
	}

	// 用于扩容参数
	c.config = &delConfig
	c.addClient = &addInstances{
		base:   base,
		config: c.config.InstanceSets,
	}

	return nil
}

func (c *delInstnaces) Name() cases.CaseName {
	return DelInstancesCaseName
}

func (c *delInstnaces) Desc() string {
	return "删除实例，包含扩容实例"
}

func (c *delInstnaces) Check(ctx context.Context) ([]cases.Resource, error) {
	var err error
	resources := make([]cases.Resource, 0)

	// 新建 Instances
	resources, err = c.addClient.Check(ctx)
	if err != nil {
		logger.Errorf(ctx, "AddInstance failed: %s", err)
		return resources, err
	}

	cceInstanceIDs := c.addClient.cceInstanceIDs
	logger.Infof(ctx, "AddInstanceIDs in %s success: %v", c.Name(), cceInstanceIDs)

	// 删除 Instances
	err = c.base.DeleteInstances(ctx, c.base.ClusterID, cceInstanceIDs, c.config.DeleteOption, true)
	if err != nil {
		logger.Errorf(ctx, "DelInstances failed: %s", err)
		return resources, err
	}

	return resources, nil
}

func (c *delInstnaces) Clean(ctx context.Context) error {
	return nil
}

func (c *delInstnaces) Continue(ctx context.Context) bool {
	return false
}

func (c *delInstnaces) ConfigFormat() string {
	str, _ := json.Marshal(&delInstanesConfig{
		InstanceSets: []*ccev2.InstanceSet{
			{
				InstanceSpec: ccetypes.InstanceSpec{
					AdminPassword: "jhkj4%w@oip",
					Existed:       true,
					ExistedOption: ccetypes.ExistedOption{
						ExistedInstanceID: "i-qsqpAg87",
					},
					MachineType: ccetypes.MachineTypeBCC,
					InstanceOS: ccetypes.InstanceOS{
						ImageType: bccimage.ImageTypeSystem,
						OSType:    bccimage.OSTypeLinux,
						OSName:    bccimage.OSNameCentOS,
						OSVersion: "7.5",
						OSArch:    "x86_64 (64bit)",
					},
				},
			},
			{
				InstanceSpec: ccetypes.InstanceSpec{
					AdminPassword: "jhkj4%w@oip",
					Existed:       false,
					MachineType:   ccetypes.MachineTypeBCC,
					InstanceType:  bcc.InstanceTypeN3,
					VPCConfig: ccetypes.VPCConfig{
						VPCSubnetID:     "sbn-wrusz28fzfc",
						SecurityGroupID: "g-sdfedd103nd",
					},
					InstanceResource: ccetypes.InstanceResource{
						CPU: 4,
						MEM: 8,
						CDSList: ccetypes.CDSConfigList{
							ccetypes.CDSConfig{
								Path:        "/data",
								StorageType: bcc.StorageTypeHP1,
								CDSSize:     100,
							},
						},
					},
					InstanceOS: ccetypes.InstanceOS{
						ImageType: bccimage.ImageTypeSystem,
						OSType:    bccimage.OSTypeLinux,
						OSName:    bccimage.OSNameCentOS,
						OSVersion: "7.5",
						OSArch:    "x86_64 (64bit)",
					},
					NeedEIP: true,
				},
				Count: 2,
			},
		},
		DeleteOption: &ccetypes.DeleteOption{
			MoveOut:           false,
			DeleteResource:    true,
			DeleteCDSSnapshot: true,
		},
	})

	return string(str)
}
