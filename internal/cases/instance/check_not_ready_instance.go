package instance

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	CheckNotReadyInstance cases.CaseName = "CheckNotReadyInstance"
)

type checkNotReadyInstance struct {
	base       *cases.BaseClient
	instanceID string
	nodeName   string
}

func init() {
	cases.AddCase(context.TODO(), CheckNotReadyInstance, NewCheckNotReadyInstance)
}

func NewCheckNotReadyInstance(ctx context.Context) cases.Interface {
	return &checkNotReadyInstance{}
}

func (c *checkNotReadyInstance) Name() cases.CaseName {
	return CheckNotReadyInstance
}

func (c *checkNotReadyInstance) Desc() string {
	return "移出异常节点kubelet检测"
}

func (c *checkNotReadyInstance) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return
}

func (c *checkNotReadyInstance) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID

	// 1、将节点列表第一个ready节点关机不计费, 并检查console和k8s节点状态是否都为notReady
	err = c.StopInstanceWithNoChargeAndCheckNodeStatus(ctx)
	if err != nil {
		return resources, err
	}
	logger.Infof(ctx, "stop instance %s success, nodeName: %s", c.instanceID, c.nodeName)

	// 2、移出该节点，并检查kubelet没有该k8s节点
	moveErr := c.base.DeleteInstances(ctx, clusterID, []string{c.instanceID}, &ccetypes.DeleteOption{
		MoveOut:           true,
		DeleteResource:    false,
		DeleteCDSSnapshot: false,
	}, false)
	if moveErr != nil {
		err = fmt.Errorf("move out instance %s failed: %v", c.instanceID, moveErr)
		return nil, err
	}

	err = common.WaitForFunc(ctx, func(ctx context.Context) (err error) {
		if err = c.kubeletNodeNotExist(ctx); err != nil {
			return err
		}
		return
	}, 5*time.Second, 2*time.Minute)
	if err != nil {
		return nil, err
	}

	// 3、开机节点，并检查kubelet中k8s节点是否不存在
	err = c.StartInstanceAndK8sNodeNotExist(ctx)
	if err != nil {
		return nil, err
	}

	return
}

func (c *checkNotReadyInstance) Clean(ctx context.Context) error {
	// 删除节点
	err := c.base.BCCClient.DeleteInstance(ctx, c.instanceID, nil)
	if err != nil {
		return fmt.Errorf("delete instance failed: %v", err)
	}

	return nil
}

func (c *checkNotReadyInstance) Continue(ctx context.Context) bool {
	return true
}

func (c *checkNotReadyInstance) ConfigFormat() string {
	return ""
}

func (c *checkNotReadyInstance) StopInstanceWithNoChargeAndCheckNodeStatus(ctx context.Context) error {

	instances, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
		ClusterRole: ccetypes.ClusterRoleNode,
	}, nil)
	if err != nil {
		return fmt.Errorf("list instance failed: %v", err)
	}

	if len(instances.InstancePage.InstanceList) == 0 {
		return fmt.Errorf("cluster instance list is empty")
	}

	for _, instance := range instances.InstancePage.InstanceList {
		if instance.Status.InstancePhase == ccetypes.InstancePhaseRunning {
			c.instanceID = instance.Status.Machine.InstanceID
			c.nodeName = instance.Status.Machine.K8SNodeName
			break
		}
	}

	err = c.base.BCCClient.StopInstanceWithNoCharge(ctx, c.instanceID, &bccapi.StopInstanceArgs{
		ForceStop:        true,
		StopWithNoCharge: true,
	}, nil)

	if err != nil {
		return fmt.Errorf("stop instance failed, instance does not support stop with no charge, err: %v", err)
	}

	err = common.WaitForFunc(ctx, func(ctx context.Context) (err error) {
		// 检查console的instance状态是否为notReady
		instance, getErr := c.base.CCEClient.GetInstance(ctx, c.base.ClusterID, c.instanceID, nil)
		if getErr != nil {
			return fmt.Errorf("get instance: %v failed, err: %v", c.instanceID, getErr)
		}
		if instance.Instance.Status.InstancePhase != ccetypes.InstancePhaseNotReady {
			return fmt.Errorf("instance status: %v, want: %v", instance.Instance.Status.InstancePhase, ccetypes.InstancePhaseNotReady)
		}

		// 检查kubelet的node状态是否为notReady
		resp, getErr := c.base.K8SClient.CoreV1().Nodes().Get(ctx, c.nodeName, metav1.GetOptions{})
		if getErr != nil {
			return fmt.Errorf("get node: %v failed, err: %v", c.nodeName, getErr)
		}
		if resp == nil {
			return fmt.Errorf("get node: %v failed, get nil", c.nodeName)
		}

		for _, condition := range resp.Status.Conditions {
			if condition.Type == v1.NodeReady && condition.Status != v1.ConditionUnknown {
				return fmt.Errorf("node %s is in %s, expect: Unknown", c.nodeName, condition.Status)
			}
		}
		return

	}, 10*time.Second, 3*time.Minute)

	if err != nil {
		return err
	}

	return nil
}

func (c *checkNotReadyInstance) StartInstanceAndK8sNodeNotExist(ctx context.Context) error {

	err := c.base.BCCClient.StartInstance(ctx, c.instanceID, nil)
	if err != nil {
		err = fmt.Errorf("start instance failed: %v", err)
		return err
	}

	// 等待instance启动，最多3分钟
	err = common.WaitForFunc(ctx, func(ctx context.Context) (err error) {
		instance, getErr := c.base.BCCClient.DescribeInstance(ctx, c.instanceID, nil)
		if getErr != nil {
			err = fmt.Errorf("get instance: %v failed, err: %v", c.instanceID, getErr)
			return
		}
		if instance.Status != bcc.InstanceStatusRunning {
			err = fmt.Errorf("instance status: %v, want: %v", instance.Status, bcc.InstanceStatusRunning)
			return
		}
		return
	}, 10*time.Second, 3*time.Minute)

	if err != nil {
		return fmt.Errorf("wait instance start failed error: %v", err)
	}
	logger.Infof(ctx, "start instance success:%s", c.instanceID)

	time.Sleep(1 * time.Minute)

	// 检查kubelet的k8sNode没有出现
	if err = c.kubeletNodeNotExist(ctx); err != nil {
		return err
	}

	return nil
}

func (c *checkNotReadyInstance) kubeletNodeNotExist(ctx context.Context) error {
	_, getErr := c.base.K8SClient.CoreV1().Nodes().Get(ctx, c.nodeName, metav1.GetOptions{})
	if getErr != nil {
		if strings.Contains(getErr.Error(), "not found") {
			return nil
		}
		return fmt.Errorf("get node: %v failed, but err message not expect, getErr: %v", c.nodeName, getErr)
	}
	return fmt.Errorf("node %s exist, expect not exist", c.nodeName)
}
