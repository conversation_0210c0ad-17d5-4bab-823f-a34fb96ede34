/*
检查节点移除后，对应的实例中的残留弹性网卡ENI
*/

package instance

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	kubeclient "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	image "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bccimage"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eni"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	CheckMoveoutNodeENI cases.CaseName = "CheckMoveoutNodeENI"
)

type checkMoveoutNodeENI struct {
	base   *cases.BaseClient
	config *checkInstancesConfig
}

type checkInstancesConfig struct {
	Password string `json:"password"`
}

func init() {
	cases.AddCase(context.TODO(), CheckMoveoutNodeENI, NewCheckDeletedNodeENI)
}

func NewCheckDeletedNodeENI(ctx context.Context) cases.Interface {
	return &checkMoveoutNodeENI{}
}

func (c *checkMoveoutNodeENI) Name() cases.CaseName { return CheckMoveoutNodeENI }

func (c *checkMoveoutNodeENI) Desc() string { return "检查移除节点的残留弹性网卡ENI" }

func (c *checkMoveoutNodeENI) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	// 初始化参数
	var checkConfig checkInstancesConfig
	if err := json.Unmarshal(config, &checkConfig); err != nil {
		return fmt.Errorf("json.Unmarshal failed: %s", err)
	}

	// 用于扩容参数
	c.config = &checkConfig
	return
}

func (c *checkMoveoutNodeENI) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID
	containerNetworkConfig := c.base.ClusterSpec.ContainerNetworkConfig.Mode

	if containerNetworkConfig != ccetypes.ContainerNetworkModeVPCENI {
		logger.Infof(ctx, "不是VPC-ENI")
		return
	}

	nodeList, getErr := c.base.KubeClient.ListNode(ctx, &kubeclient.ListOptions{
		LabelSelector: map[string]string{
			"cluster-role": "node"},
	})

	if getErr != nil {
		err = fmt.Errorf("List clusterID %s nodes failed: %v", clusterID, getErr)
		return
	}

	if len(nodeList.Items) == 0 {
		err = fmt.Errorf("no nodes found in cluster %v ", clusterID)
		return
	}

	nodeIndex := c.getRandIndex(len(nodeList.Items))
	sampleNode := nodeList.Items[nodeIndex]
	sampleNodeName := sampleNode.Name
	logger.Infof(ctx, "The node's ID is %s", sampleNodeName)

	instanceFromNode, getErr := c.base.CCEClient.GetInstanceByNodeName(ctx, clusterID, sampleNodeName, nil)
	if getErr != nil {
		err = fmt.Errorf("Get Instance from node %s failed: %v", sampleNodeName, getErr)
		return
	}

	sampleInstance := instanceFromNode.Instance
	sampleInstanceID := sampleInstance.Status.Machine.InstanceID
	logger.Infof(ctx, "sampleInstanceID is %s", sampleInstanceID)

	listEniRes, getErr := c.base.BCCClient.ListInstanceEnis(ctx, sampleInstanceID, nil)

	if getErr != nil {
		err = fmt.Errorf("ListInstance Enis failed: %v", getErr)
		return
	}

	// 主网卡在list[0]
	eniList := listEniRes.EniList
	if len(eniList) <= 1 {
		err = fmt.Errorf("no enis found in cluster %v", clusterID)
		return
	}

	c.PrintEnisID(ctx, eniList, sampleInstanceID)

	// 移除节点,moveout
	_, getErr = c.base.CCEClient.DeleteInstances(ctx, clusterID, &ccev2.DeleteInstancesRequest{
		InstanceIDs: []string{sampleInstanceID},
		DeleteOption: &ccetypes.DeleteOption{
			DrainNode:         false,
			MoveOut:           true,
			DeleteResource:    true,
			DeleteCDSSnapshot: true},
	}, nil)

	if getErr != nil {
		err = fmt.Errorf("Delete Instance %s failed: %v", sampleInstanceID, getErr)
		return
	}

	// 等待instance删除，最多5分钟
	waitErr := resource.WaitForFunc(ctx, func(ctx context.Context) error {
		// 看能不能获取到
		_, getErr := c.base.CCEClient.GetInstance(ctx, clusterID, sampleInstanceID, nil)
		var newErr error

		// 获取不到说明已完成删除
		if getErr != nil {
			if strings.Contains(getErr.Error(), "cce.warning.InstanceNotFound") {
				logger.Infof(ctx, "Instance %s has been deleted", sampleInstanceID)
				return nil
			}
			newErr = fmt.Errorf("GetInstanceResponse failed %v", getErr)
		} else {
			// 获取得到说明还在删除中，等待
			logger.Warnf(ctx, "The instance %s is still in cluster", sampleInstanceID)
			newErr = errors.New("Waitting for deleting")
		}

		return newErr
	}, 5*time.Second, 5*time.Minute)

	// 等待超时
	if waitErr != nil {
		err = fmt.Errorf("Delete Instance %s failed: %v", sampleInstanceID, waitErr)
		return
	}

	// 看eni
	listEniRes, getErr = c.base.BCCClient.ListInstanceEnis(ctx, sampleInstanceID, nil)

	if getErr != nil {
		err = fmt.Errorf("ListInstance Enis failed: %v", getErr)
		return
	}

	eniList = listEniRes.EniList

	// 剩一个主网卡,大于1个则没删完enis
	if len(eniList) > 1 {
		err = errors.New("Residual Enis are not deleted")
		return
	}

	logger.Infof(ctx, "Residual Enis have been deleted")

	// 移入实例，不残留资源
	getErr = c.MoveExistedInstanceToCluster(ctx, c.base, c.config.Password, sampleInstanceID)
	if getErr != nil {
		err = fmt.Errorf("Move Instance %s into Cluster failed: %v", sampleInstanceID, getErr)
	}

	return
}

func (c *checkMoveoutNodeENI) Clean(ctx context.Context) error { return nil }

func (c *checkMoveoutNodeENI) Continue(ctx context.Context) bool { return true }

func (c *checkMoveoutNodeENI) ConfigFormat() string { return "" }

func (c *checkMoveoutNodeENI) getRandIndex(length int) int {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	randomIndex := r.Intn(length)
	return randomIndex
}

func (c *checkMoveoutNodeENI) PrintEnisID(ctx context.Context, eni []eni.ENI, sampleInstanceID string) {
	logger.Infof(ctx, "The instance %v Enis are following: ", sampleInstanceID)
	logger.Infof(ctx, "主网卡为：%v", eni[0].ENIID)

	for index := range eni {
		if index == 0 {
			continue
		}
		logger.Infof(ctx, "ENI为：%v", eni[index].ENIID)
	}

	return
}

func (c *checkMoveoutNodeENI) MoveExistedInstanceToCluster(ctx context.Context, baseClient *cases.BaseClient, password string, instanceID string) error {
	var args []*ccev2.InstanceSet

	args = append(args, &ccev2.InstanceSet{
		InstanceSpec: ccetypes.InstanceSpec{
			Existed: true,
			ExistedOption: ccetypes.ExistedOption{
				ExistedInstanceID: instanceID,
			},
			InstanceOS: ccetypes.InstanceOS{
				ImageType: image.ImageTypeSystem,
				ImageName: "7.6 x86_64 (64bit)",
				OSType:    image.OSTypeLinux,
				OSName:    image.OSNameCentOS,
				OSVersion: "7.6",
				OSArch:    "x86_64 (64bit)",
			},
			MachineType:   ccetypes.MachineTypeBCC,
			AdminPassword: password,
		},
	})

	_, err := baseClient.CCEClient.CreateInstances(ctx, baseClient.ClusterID, args, nil)
	if err != nil {
		return fmt.Errorf("Error creating instances: %v ", err)
	}
	return nil
}
