package instance

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	SysTaintsAnnotationsLabels cases.CaseName = "SysTaintsAnnotationsLabels"
)

type sysTaintsAnnotationsLabels struct {
	base *cases.BaseClient
}

func init() {
	cases.AddCase(context.TODO(), SysTaintsAnnotationsLabels, NewSysTaintsAnnotationsLabels)
}

func NewSysTaintsAnnotationsLabels(ctx context.Context) cases.Interface {
	return &sysTaintsAnnotationsLabels{}
}

func (c *sysTaintsAnnotationsLabels) Name() cases.CaseName { return SysTaintsAnnotationsLabels }

func (c *sysTaintsAnnotationsLabels) Desc() string { return "污点注释标签同步" }

func (c *sysTaintsAnnotationsLabels) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	return
}

func (c *sysTaintsAnnotationsLabels) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterId := c.base.ClusterID
	nodeList, getErr := c.base.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{
		LabelSelector: "cluster-role=node"})

	if getErr != nil {
		err = fmt.Errorf("List nodes failed %v", getErr)
		return
	}

	if len(nodeList.Items) == 0 {
		err = errors.New("no nodes found")
		return
	}

	// 获取节点
	nodeIndex := c.getRandIndex(len(nodeList.Items))
	sampleNode := nodeList.Items[nodeIndex]
	sampleNodeName := sampleNode.Name
	logger.Infof(ctx, "-------------The node's ID is %s", sampleNodeName)

	// 获取节点对应的实例
	instanceFromNode, getErr := c.base.CCEClient.GetInstanceByNodeName(ctx, clusterId, sampleNodeName, nil)
	if getErr != nil {
		err = fmt.Errorf("Get Instance from node %s failed: %v", sampleNodeName, getErr)
		return
	}

	// 是否同步
	isSameTaintsAnnoAndLabel := c.cmpTaintsAnnoAndLabel(instanceFromNode.Instance, &sampleNode)

	// 未同步 则等待同步
	if !isSameTaintsAnnoAndLabel {
		logger.Infof(ctx, "Waitting started")
		waitErr := resource.WaitForFunc(ctx, func(ctx context.Context) error {
			getRealTimeNode, getErr := c.base.K8SClient.CoreV1().Nodes().Get(ctx, sampleNodeName, metav1.GetOptions{})
			if getErr != nil {
				return getErr
			}

			isSynced := c.cmpTaintsAnnoAndLabel(instanceFromNode.Instance, getRealTimeNode)
			if isSynced {
				logger.Infof(ctx, "Sync taints, annotations and labels succeed")
				return nil
			}
			return errors.New("Waitting")
		}, 10*time.Second, 10*time.Minute)

		if waitErr != nil {
			err = fmt.Errorf("Sync taints, annotations and labels Failed!", waitErr)
			return
		}

		return
	}

	// 随机进行删除taint,annotation,label
	taintsNum := len(instanceFromNode.Instance.Spec.Taints)
	annotationsNum := len(instanceFromNode.Instance.Spec.Annotations)
	labelsNum := len(instanceFromNode.Instance.Spec.Labels)

	if taintsNum == 0 && annotationsNum == 0 && labelsNum == 0 {
		logger.Infof(ctx, "Instance %s taints, annotations and labels are empty", instanceFromNode.Instance.Spec.InstanceName)
		return
	}

	if taintsNum != 0 {
		taintsIndex := c.getRandIndex(taintsNum)
		taintKey := instanceFromNode.Instance.Spec.Taints[taintsIndex].Key

		newTaints := make([]corev1.Taint, 0)

		for _, toSaveTaint := range sampleNode.Spec.Taints {
			if toSaveTaint.Key != taintKey {
				newTaints = append(newTaints, toSaveTaint)
			}
		}
		sampleNode.Spec.Taints = newTaints
	}

	if annotationsNum != 0 {
		toDelAnno := c.RandMapKey(instanceFromNode.Instance.Spec.Annotations)
		delete(sampleNode.Annotations, toDelAnno)
	}

	if labelsNum != 0 {
		toDelLab := c.RandMapKey(instanceFromNode.Instance.Spec.Labels)
		delete(sampleNode.Labels, toDelLab)
	}

	updateNode, getErr := c.base.K8SClient.CoreV1().Nodes().Update(ctx, &sampleNode, metav1.UpdateOptions{})
	if getErr != nil {
		err = fmt.Errorf("UpdateNode Failed: %v", getErr)
		return
	}

	// 等待同步
	logger.Infof(ctx, "Waitting started")
	waitErr := resource.WaitForFunc(ctx, func(ctx context.Context) error {
		getRealTimeNode, getErr := c.base.K8SClient.CoreV1().Nodes().Get(ctx, updateNode.Name, metav1.GetOptions{})
		if getErr != nil {
			return getErr
		}

		isSynced := c.cmpTaintsAnnoAndLabel(instanceFromNode.Instance, getRealTimeNode)
		if isSynced {
			logger.Infof(ctx, "Sync taints, annotations and labels succeed")
			return nil
		}
		return errors.New("Waitting")
	}, 10*time.Second, 10*time.Minute)

	if waitErr != nil {
		err = fmt.Errorf("Sync taints, annotations and labels Failed!", waitErr)
		return
	}

	return
}

func (c *sysTaintsAnnotationsLabels) Clean(ctx context.Context) error { return nil }

func (c *sysTaintsAnnotationsLabels) Continue(ctx context.Context) bool { return true }

func (c *sysTaintsAnnotationsLabels) ConfigFormat() string { return "" }

func (c *sysTaintsAnnotationsLabels) cmpTaintsAnnoAndLabel(ins *ccev2.Instance, node *corev1.Node) bool {
	nodeTaints := make(map[ccetypes.TaintUniqueKey]corev1.Taint)

	for _, taint := range node.Spec.Taints {
		key := ccetypes.TaintUniqueKey{
			Key:    taint.Key,
			Effect: taint.Effect,
		}
		nodeTaints[key] = taint
	}

	for _, insTaint := range ins.Spec.Taints {
		key := ccetypes.TaintUniqueKey{
			Key:    insTaint.Key,
			Effect: insTaint.Effect,
		}
		if nodeTaint, found := nodeTaints[key]; !found || nodeTaint.Value != insTaint.Value {
			fmt.Println("Taints are different")
			return false
		}
	}

	boolAnno := c.cmpMap(ins.Spec.Annotations, node.Annotations)
	if !boolAnno {
		fmt.Println("Annotations are different")
		return false
	}

	boolLab := c.cmpMap(ins.Spec.Labels, node.Labels)
	if !boolLab {
		fmt.Println("Labels are different")
		return false
	}

	return true
}

func (c *sysTaintsAnnotationsLabels) cmpMap(ins, node map[string]string) bool {
	for k, v := range ins {
		if _, ok := node[k]; !ok {
			return false
		}
		if v != node[k] {
			return false
		}
	}

	return true
}

// 取随机数
func (c *sysTaintsAnnotationsLabels) getRandIndex(length int) int {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	randomIndex := r.Intn(length)
	return randomIndex
}

func (c *sysTaintsAnnotationsLabels) RandMapKey(mapKey map[string]string) string {
	key := make([]string, 0)
	for k := range mapKey {
		key = append(key, k)
	}
	index := c.getRandIndex(len(key))
	delKey := key[index]

	return delKey
}
