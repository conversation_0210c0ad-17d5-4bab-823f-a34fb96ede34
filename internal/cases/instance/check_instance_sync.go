/* check_instance_sync.go */
/*
modification history
--------------------
2024/9/11, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
1、实例更新接口，更新污点
2、实例同步接口，修改实例名称
*/

package instance

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	CheckInstanceSync cases.CaseName = "CheckInstanceSync"
)

type checkInstanceSync struct {
	base *cases.BaseClient
}

func init() {
	cases.AddCase(context.TODO(), CheckInstanceSync, NewCheckInstanceSync)
}

func NewCheckInstanceSync(ctx context.Context) cases.Interface {
	return &checkInstanceSync{}
}

func (c *checkInstanceSync) Name() cases.CaseName {
	return CheckInstanceSync
}

func (c *checkInstanceSync) Desc() string {
	return "节点更新以及IaaS同步的用例"
}

func (c *checkInstanceSync) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	return
}

func (c *checkInstanceSync) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID
	// 选取集群里的第一个节点，更新污点
	instanceResp, listInsErr := c.base.CCEClient.ListInstancesByPage(ctx, clusterID, &ccev2.ListInstancesByPageParams{
		ClusterRole: types.ClusterRoleNode,
	}, nil)
	if listInsErr != nil {
		err = fmt.Errorf("list instance by page failed: %v", listInsErr)
		return
	}
	if len(instanceResp.InstancePage.InstanceList) == 0 {
		return
	}

	var instanceID string
	var bccID string
	for _, instance := range instanceResp.InstancePage.InstanceList {
		if instance.Status.InstancePhase == types.InstancePhaseRunning {
			instanceID = instance.Spec.CCEInstanceID
			bccID = instance.Status.Machine.InstanceID
			break
		}
	}

	// 更新instance的label和taints
	var labelKey = "new-label-key"
	var labelValue = "new-label-value"
	labels := map[string]string{
		labelKey: labelValue,
	}
	taints := types.InstanceTaints{
		{
			Key:    "testKey",
			Value:  "testValue",
			Effect: "NoSchedule",
		},
	}

	updateInstanceResp, updateInsErr := c.base.CCEClient.UpdateInstance(ctx, clusterID, instanceID, &types.InstanceSpec{
		Labels: labels,
		Taints: taints,
	}, nil)
	if updateInsErr != nil {
		err = fmt.Errorf("update instance failed: %v", updateInsErr)
		return
	}
	if _, ok := updateInstanceResp.Instance.Spec.Labels[labelKey]; !ok {
		err = errors.New("instance label not updated")
		return
	}
	if isEqual := reflect.DeepEqual(updateInstanceResp.Instance.Spec.Taints, taints); !isEqual {
		err = fmt.Errorf("got taints %v, want: %v", updateInstanceResp.Instance.Spec.Taints, taints)
		return
	}

	// 修改bcc节点的实例名称，校验实例同步接口
	newInstanceName := "newName"
	modifyInsErr := c.base.BCCClient.ModifyInstanceName(ctx, bccID, &bcc.ModifyInstanceNameArgs{InsName: newInstanceName}, nil)
	if modifyInsErr != nil {
		err = fmt.Errorf("modify instance name failed: %v", modifyInsErr)
		return
	}
	time.Sleep(2 * time.Second)

	// 实例同步接口并不是openapi接口
	_, syncInsErr := c.base.CCEHostClient.SyncInstance(ctx, clusterID, nil)
	if syncInsErr != nil {
		err = fmt.Errorf("sync instance failed: %v", syncInsErr)
		return
	}
	time.Sleep(5 * time.Second)

	instance, getInsErr := c.base.CCEClient.GetInstance(ctx, clusterID, instanceID, nil)
	if getInsErr != nil {
		err = fmt.Errorf("get instance %s failed: %v", instanceID, getInsErr)
		return
	}
	if instance.Instance.Spec.InstanceName != newInstanceName {
		err = errors.New("instance name sync is not as expected")
		return
	}

	return
}

func (c *checkInstanceSync) Clean(ctx context.Context) error {
	return nil
}

func (c *checkInstanceSync) Continue(ctx context.Context) bool {
	return true
}

func (c *checkInstanceSync) ConfigFormat() string {
	return ""
}
