// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/8/19 下午8:26, by <EMAIL>, create
*/
/*
DESCRIPTION
*/

package instance

import (
	"context"
	"errors"
	"fmt"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	ccesdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/cmp"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// CheckScript - case 名字
	CheckScript cases.CaseName = "CheckScript"

	// 自定义脚本打 label 值
	defaultLabelKey   = "post-key"
	defaultLabelValue = "post-value"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CheckScript, NewCheckScript)
}

type checkScript struct {
	base *cases.BaseClient
}

// NewCheckScript - 检查用户自定义脚本
func NewCheckScript(ctx context.Context) cases.Interface {
	return &checkScript{}
}

func (c *checkScript) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base

	return nil
}

func (c *checkScript) Name() cases.CaseName {
	return CheckScript
}

func (c *checkScript) Desc() string {
	return "[待补充]"
}

func (c *checkScript) Check(ctx context.Context) ([]cases.Resource, error) {
	clusterID := c.base.ClusterID

	// 查询 Instance
	// 由于集群状态和节点状态解耦:
	// ClusterPhase = Running 时, Node 的 InstancePhase 不一定为 Running
	var instanceList []*ccesdk.Instance
	var unReadyInstanceList []*ccesdk.Instance
	for i := 0; i < 30; i++ {
		// 查询所有节点
		cceResp, err := c.base.CCEClient.ListInstancesByPage(ctx, clusterID, nil, nil)
		if err != nil {
			logger.Errorf(ctx, "GetInstance %s failed: %s", clusterID, err)
			return nil, err
		}

		if cceResp == nil {
			return nil, errors.New("GetInstance return nil")
		}

		unReadyInstanceList = []*ccesdk.Instance{}
		instanceList = cceResp.InstancePage.InstanceList

		// 检查节点状态
		for _, instance := range cceResp.InstancePage.InstanceList {
			// CCE 接口返回 ready
			if instance.Status.InstancePhase != ccetypes.InstancePhaseRunning && instance.Status.InstancePhase != "ready" {
				unReadyInstanceList = append(unReadyInstanceList, instance)
				logger.Infof(ctx, "Instance %s status %s not running", instance.Spec.CCEInstanceID, instance.Status.InstancePhase)
				break
			}
		}

		// 判断是否都 Running
		if len(unReadyInstanceList) != 0 {
			logger.Infof(ctx, "Instances %s not ready, wait 30 seconds", utils.ToJSON(unReadyInstanceList))
			time.Sleep(30 * time.Second)
		}
	}

	if len(unReadyInstanceList) != 0 {
		logger.Errorf(ctx, "check script  failed: Instance %s not ready after 150s", utils.ToJSON(unReadyInstanceList))
		return nil, errors.New("instance not ready after 150s")
	}

	// 比较 Diff
	result := map[string][]*cmp.Diff{}
	for _, cceInstance := range instanceList {
		diffs := []*cmp.Diff{}
		cceInstanceID := cceInstance.Spec.CCEInstanceID

		// 检查 userScript是否生效
		// postScript 给 node 打label，通过label是否存在判断脚本执行情况
		if cceInstance.Spec.DeployCustomConfig.PostUserScript != "" && cceInstance.Spec.ClusterRole == ccetypes.ClusterRoleNode {
			node, err := c.base.K8SClient.CoreV1().Nodes().Get(ctx, cceInstance.Status.Machine.K8SNodeName, metav1.GetOptions{})
			if err != nil {
				logger.Errorf(ctx, "Get K8S Node %s failed: %s", cceInstance.Status.Machine.K8SNodeName, err)
				return nil, err
			}

			if diff, err := cmp.NodeLabel(ctx, node, defaultLabelKey, defaultLabelValue); err != nil {
				logger.Errorf(ctx, "check script failed: %s", err)
			} else if diff != nil {
				diffs = append(diffs, diff...)
			}
		}

		if len(diffs) != 0 {
			result[cceInstanceID] = diffs
			logger.Warnf(ctx, "check script %s failed, Diff=%s", cceInstanceID, utils.ToJSON(diffs))
		}
	}

	if len(result) != 0 {
		return nil, fmt.Errorf("check script %s failed: %s", clusterID, utils.ToJSON(result))
	}

	return nil, nil
}

func (c *checkScript) Clean(ctx context.Context) error {
	return nil
}

func (c *checkScript) Continue(ctx context.Context) bool {
	return false
}

func (c *checkScript) ConfigFormat() string {
	return ""
}
