package instance

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	AddMoveDelBidInstances cases.CaseName = "AddMoveDelBidInstances"
)

type addMoveDelBidInstances struct {
	base *cases.BaseClient

	config    addBidInstancesConfig
	addClient *addInstances // 复用 addInstances 的扩容方法
}

type addBidInstancesConfig struct {
	InstanceSets []*ccev2.InstanceSet `json:"instanceSets"`
	Password     string               `json:"password"`
}

func init() {
	cases.AddCase(context.TODO(), AddMoveDelBidInstances, NewAddMoveDelBidInstances)
}

func NewAddMoveDelBidInstances(ctx context.Context) cases.Interface {
	return &addMoveDelBidInstances{}
}

func (c *addMoveDelBidInstances) Name() cases.CaseName {
	return AddMoveDelBidInstances
}

func (c *addMoveDelBidInstances) Desc() string {
	return "竞价实例创建、移入移出与删除"
}

func (c *addMoveDelBidInstances) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	if base == nil {
		return errors.New("base is nil")
	}
	var addConfig addBidInstancesConfig
	if err := json.Unmarshal(config, &addConfig); err != nil {
		return fmt.Errorf("json.Unmarshal []*ccetypes.InstanceGroupSpec failed: %s", err)
	}

	c.base = base
	c.config = addConfig
	c.addClient = &addInstances{
		base:   base,
		config: c.config.InstanceSets,
	}
	return nil
}

func (c *addMoveDelBidInstances) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID
	resources = make([]cases.Resource, 0)

	// 1. 创建竞价节点
	resources, err = c.addClient.Check(ctx)
	if err != nil {
		logger.Errorf(ctx, "AddBidInstances failed: %s", err)
		return nil, err
	}
	cceInstanceIDs := c.addClient.cceInstanceIDs
	logger.Infof(ctx, "CreateInstances success: %s", cceInstanceIDs)

	instanceIDs, err := BycceInstanceIDsGetInstanceIDs(ctx, c.base, cceInstanceIDs, clusterID)
	if err != nil {
		logger.Errorf(ctx, "BycceInstanceIDsGetInstanceIDs failed: %s", err)
		return nil, fmt.Errorf("BycceInstanceIDsGetInstanceIDs failed: %s", err)
	}
	logger.Infof(ctx, "GetInstanceIDs success: %s", instanceIDs)

	// 2、移出竞价节点
	err = c.base.DeleteInstances(ctx, clusterID, cceInstanceIDs, &ccetypes.DeleteOption{
		MoveOut:           true,
		DeleteResource:    false,
		DeleteCDSSnapshot: false,
	}, false)
	if err != nil {
		logger.Errorf(ctx, "move out instance failed: %v", err.Error())
		return nil, err
	}
	logger.Infof(ctx, "move out instance success: %s", instanceIDs)

	// 3、移入并重装系统 竞价节点
	err = MoveExistedBidInstanceToCluster(ctx, c.base, c.config.Password, instanceIDs)
	if err != nil {
		logger.Errorf(ctx, "MoveExistedBidInstanceToCluster failed: %v", err.Error())
		return nil, err
	}
	logger.Infof(ctx, "MoveExistedBidInstanceToCluster success: %s", instanceIDs)

	// 4、删除竞价节点
	err = c.base.DeleteInstances(ctx, c.base.ClusterID, instanceIDs, &ccetypes.DeleteOption{
		DrainNode:         false,
		MoveOut:           false,
		DeleteResource:    true,
		DeleteCDSSnapshot: true,
	}, true)
	if err != nil {
		logger.Errorf(ctx, "DelInstances failed: %s", err)
		return nil, err
	}
	return resources, nil
}

func (c *addMoveDelBidInstances) Clean(ctx context.Context) error {
	return nil
}

func (c *addMoveDelBidInstances) Continue(ctx context.Context) bool {
	return true
}

func (c *addMoveDelBidInstances) ConfigFormat() string {
	return ""
}

func BycceInstanceIDsGetInstanceIDs(ctx context.Context, baseClient *cases.BaseClient, cceInstanceIDs []string, clusterID string) ([]string, error) {
	if clusterID == "" {
		err := errors.New("clusterID is empty")
		return nil, err
	}
	if cceInstanceIDs == nil || len(cceInstanceIDs) == 0 {
		return nil, errors.New("cceInstanceIDs is empty")
	}
	instanceIDs := make([]string, 0)
	for _, cceInstanceID := range cceInstanceIDs {
		if cceInstanceID == "" {
			return nil, errors.New("cceInstanceIDs is empty")
		}
		resp, getErr := baseClient.CCEClient.GetInstance(ctx, clusterID, cceInstanceID, nil)
		if getErr != nil {
			return nil, fmt.Errorf("get instance failed: %v", getErr)
		}
		if resp == nil || resp.Instance == nil || resp.Instance.Status.Machine.InstanceID == "" {
			return nil, fmt.Errorf("get instance failed, instanceID is empty")
		}
		instanceIDs = append(instanceIDs, resp.Instance.Status.Machine.InstanceID)
	}
	return instanceIDs, nil
}

func MoveExistedBidInstanceToCluster(ctx context.Context, baseClient *cases.BaseClient, password string, instanceIDs []string) error {
	var args []*ccev2.InstanceSet
	for _, instanceID := range instanceIDs {
		args = append(args, &ccev2.InstanceSet{
			InstanceSpec: ccetypes.InstanceSpec{
				Existed: true,
				ExistedOption: ccetypes.ExistedOption{
					ExistedInstanceID: instanceID,
					Rebuild:           func(rebuild bool) *bool { return &rebuild }(true),
				},
				InstanceOS: ccetypes.InstanceOS{
					ImageType: "System",
					ImageName: "7.6 x86_64 (64bit)",
					OSType:    "linux",
					OSName:    "CentOS",
					OSVersion: "7.6",
					OSArch:    "x86_64 (64bit)",
				},
				MachineType:   ccetypes.MachineTypeBCC,
				AdminPassword: password,
				Bid:           true,
			},
		})
	}

	_, err := baseClient.CCEClient.CreateInstances(ctx, baseClient.ClusterID, args, nil)
	if err != nil {
		return fmt.Errorf("Error creating instances: %w ", err)
	}
	return nil
}
