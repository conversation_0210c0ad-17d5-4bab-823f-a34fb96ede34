/* check_batch_search.go */
/*
modification history
--------------------
2022/11/1, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
1、检验instance模糊搜索(节点名称、实例名称、实例ID、内网IP)
2、检验instance批量搜索（节点名称、实例名称、实例ID、内网IP）
3、检验node模糊搜索（节点名称）
4、校验node批量搜索（节点名称）
PS: 尽量找3节点的集群验证
*/

package instance

import (
	"context"
	"errors"
	"strings"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appservice"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	// CheckBatchSearchCaseName - case 名字
	CheckBatchSearchCaseName cases.CaseName = "CheckBatchSearch"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CheckBatchSearchCaseName, NewCheckBatchSearch)
}

type checkBatchSearch struct {
	base *cases.BaseClient
}

// NewCheckBatchSearch - 测试案例
func NewCheckBatchSearch(ctx context.Context) cases.Interface {
	return &checkBatchSearch{}
}

func (c *checkBatchSearch) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base

	return nil
}

func (c *checkBatchSearch) Name() cases.CaseName {
	return CheckBatchSearchCaseName
}

func (c *checkBatchSearch) Desc() string {
	return "[待补充]"
}

func (c *checkBatchSearch) Check(ctx context.Context) ([]cases.Resource, error) {
	var err error
	var resources []cases.Resource
	var searchTypes = map[string][]string{
		"k8sNodeName":  {},
		"instanceName": {},
		"instanceID":   {},
		"vpcIP":        {},
	}

	instanceListWithNode, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
		ClusterRole: ccetypes.ClusterRoleNode,
	}, nil)
	if err != nil {
		logger.Errorf(ctx, "get total instance failed: %v", err)
		return resources, err
	}

	for _, instance := range instanceListWithNode.InstancePage.InstanceList {
		searchTypes["k8sNodeName"] = append(searchTypes["k8sNodeName"], instance.Status.Machine.K8SNodeName)
		searchTypes["instanceName"] = append(searchTypes["instanceName"], instance.Spec.InstanceName)
		searchTypes["instanceID"] = append(searchTypes["instanceID"], instance.Status.Machine.InstanceID)
		searchTypes["vpcIP"] = append(searchTypes["vpcIP"], instance.Status.Machine.VPCIP)
	}

	// 1、检验instance模糊搜索(节点名称、实例名称、实例ID、内网IP)
	for keywordType, keywords := range searchTypes {
		for _, searchKey := range keywords {
			instanceListByKeyword, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
				ClusterRole: ccetypes.ClusterRoleNode,
				KeywordType: ccev2.InstanceKeywordType(keywordType),
				Keyword:     searchKey[1 : len(searchKey)-1],
			}, nil)

			if err != nil {
				logger.Errorf(ctx, "get instance with fuzzy search failed: %v", err)
				return resources, err
			}

			if len(instanceListByKeyword.InstancePage.InstanceList) < 1 {
				logger.Errorf(ctx, "search instance with fuzzy keyword %s failed", searchKey[1:len(searchKey)-1])
				return resources, err
			}

			logger.Infof(ctx, "search instance with fuzzy keyword %s success, got result %d",
				searchKey[1:len(searchKey)-1], len(instanceListByKeyword.InstancePage.InstanceList))
		}
	}

	// 2、检验instance批量搜索（节点名称、实例名称、实例ID、内网IP）
	for keywordType, keywords := range searchTypes {
		var mutiKeyword string
		for count := 1; count <= len(keywords); count++ {
			mutiKeyword += keywords[count-1] + ","

			instanceListByKeyword, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
				ClusterRole: ccetypes.ClusterRoleNode,
				KeywordType: ccev2.InstanceKeywordType(keywordType),
				Keyword:     mutiKeyword,
			}, nil)

			if err != nil {
				logger.Errorf(ctx, "get instance with muti keyword search failed: %v", err)
				return resources, err
			}

			if len(instanceListByKeyword.InstancePage.InstanceList) != count {
				logger.Errorf(ctx, "search instance with muti keyword [%s] failed, got result %d",
					mutiKeyword, len(instanceListByKeyword.InstancePage.InstanceList))
				return resources, err
			}

			logger.Infof(ctx, "search instance muti keyword [%s] success, got result %d",
				mutiKeyword, len(instanceListByKeyword.InstancePage.InstanceList))
		}
	}

	// 将master节点记入k8sNodeName
	if !strings.Contains(string(c.base.ClusterSpec.MasterConfig.MasterType), "managed") {
		instanceListWithMaster, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
			ClusterRole: ccetypes.ClusterRoleMaster,
		}, nil)

		if err != nil {
			logger.Errorf(ctx, "get total master instance failed: %v", err)
			return resources, err
		}

		for _, instance := range instanceListWithMaster.InstancePage.InstanceList {
			searchTypes["k8sNodeName"] = append(searchTypes["k8sNodeName"], instance.Status.Machine.K8SNodeName)
		}
	}

	// 3、检验node模糊搜索（节点名称）
	for _, searchKey := range searchTypes["k8sNodeName"] {
		nodeListByKeyword, err := c.base.AppClient.GetNodeList(ctx, &appservice.GetNodeListArgs{
			ClusterUuid: c.base.ClusterID,
			FilterBy:    "name," + searchKey[1:len(searchKey)-1],
		}, nil)

		if err != nil {
			logger.Errorf(ctx, "get node with fuzzy search failed: %v", err)
			return resources, err
		}

		if len(nodeListByKeyword.Nodes) < 1 {
			logger.Errorf(ctx, "search node with fuzzy keyword %s failed", searchKey[1:len(searchKey)-1])
			return resources, err
		}

		logger.Infof(ctx, "search node with fuzzy keyword %s success, got result %d",
			searchKey[1:len(searchKey)-1], len(nodeListByKeyword.Nodes))
	}

	// 4、校验node批量搜索（节点名称）
	var mutiKeyword string
	for count := 1; count <= len(searchTypes["k8sNodeName"]); count++ {
		mutiKeyword += searchTypes["k8sNodeName"][count-1] + ","

		nodeListByKeyword, err := c.base.AppClient.GetNodeList(ctx, &appservice.GetNodeListArgs{
			ClusterUuid: c.base.ClusterID,
			FilterBy:    "name," + mutiKeyword,
			BatchQuery:  "true",
		}, nil)

		if err != nil {
			logger.Errorf(ctx, "get node with muti keyword search failed: %v", err)
			return resources, err
		}

		if len(nodeListByKeyword.Nodes) != count {
			logger.Errorf(ctx, "search node with muti keyword [%s] failed, got result %d",
				mutiKeyword, len(nodeListByKeyword.Nodes))
			return resources, err
		}

		logger.Infof(ctx, "search node muti keyword [%s] success, got result %d",
			mutiKeyword, len(nodeListByKeyword.Nodes))
	}

	return resources, nil
}

func (c *checkBatchSearch) Clean(ctx context.Context) error {
	return nil
}

func (c *checkBatchSearch) Continue(ctx context.Context) bool {
	return false
}

func (c *checkBatchSearch) ConfigFormat() string {
	return ""
}
