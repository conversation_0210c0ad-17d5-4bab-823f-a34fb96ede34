// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/06/08 15:48:00, by <EMAIL>, create
*/
/*
Test Case 模板, 复制粘贴
*/

package instance

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bccimage"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/cmp"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// AddInstances - 扩容节点 Case 名字
	AddInstances cases.CaseName = "AddInstances"
)

func init() {
	cases.AddCase(context.TODO(), AddInstances, NewAddInstances)
}

var _ cases.Interface = &addInstances{}

type addInstances struct {
	base *cases.BaseClient

	cceInstanceIDs []string

	config addInstancesConfig
}

type addInstancesConfig []*ccev2.InstanceSet

// NewAddInstances - 检查扩容已有节点
func NewAddInstances(ctx context.Context) cases.Interface {
	return &addInstances{}
}

func (c *addInstances) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	var addConfig addInstancesConfig
	if err := json.Unmarshal(config, &addConfig); err != nil {
		return fmt.Errorf("json.Unmarshal []*ccetypes.InstanceGroupSpec failed: %s", err)
	}

	c.base = base
	c.config = addConfig

	return nil
}

func (c *addInstances) Name() cases.CaseName {
	return AddInstances
}

func (c *addInstances) Desc() string {
	return "扩容实例"
}

func (c *addInstances) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)

	clusterID := c.base.ClusterID
	logger.Infof(ctx, "AddInstances in cluster %s", clusterID)

	// 创建 Instance
	cceInstanceIDs, err := c.base.CreateInstances(ctx, clusterID, c.config, true)
	if cceInstanceIDs != nil {
		// 放在 err 前为了避免资源泄漏
		for _, id := range cceInstanceIDs {
			resources = append(resources,
				cases.Resource{
					CaseName: c.Name(),
					Type:     cases.ResourceTypeCCEInstance,
					ID:       id,
				})
		}
	}
	if err != nil {
		logger.Errorf(ctx, "CreateInstances failed: %s", err)
		return resources, err
	}

	c.cceInstanceIDs = cceInstanceIDs
	logger.Infof(ctx, "CreateInstances success: %s", c.cceInstanceIDs)

	// 比较 Diff
	result := map[string][]*cmp.Diff{}
	for _, cceInstanceID := range c.cceInstanceIDs {
		diffs := []*cmp.Diff{}

		// 比较 CCE Instance 和 BCC Instance
		bccDiffs, err := c.base.CMPClient.InstanceCompareWithIaaS(ctx, clusterID, cceInstanceID, nil)
		if err != nil {
			logger.Errorf(ctx, "InstanceCompareWithIaaS %s failed: %s", cceInstanceID, err)
			return resources, err
		}

		diffs = append(diffs, bccDiffs...)

		// 比较 CCE Instance 和 K8S Master/Node
		k8sDiffs, err := c.base.CMPClient.InstanceCompareWithK8S(ctx, clusterID, cceInstanceID, nil)
		if err != nil {
			logger.Errorf(ctx, "InstanceCompareWithK8S %s failed: %s", cceInstanceID, err)
			return resources, err
		}

		diffs = append(diffs, k8sDiffs...)

		if len(diffs) != 0 {
			result[cceInstanceID] = diffs
			logger.Warnf(ctx, "Check Instance %s failed, Diff=%s", cceInstanceID, utils.ToJSON(diffs))
		}
	}

	if len(result) != 0 {
		return resources, fmt.Errorf("check instances failed: %s", utils.ToJSON(result))
	}

	return resources, nil
}

func (c *addInstances) Clean(ctx context.Context) error {
	// 不需要回收 Instance, 用于测试 del_instance
	return nil
}

func (c *addInstances) Continue(ctx context.Context) bool {
	return false
}

func (c *addInstances) ConfigFormat() string {
	str, _ := json.Marshal([]*ccev2.InstanceSet{
		{
			InstanceSpec: ccetypes.InstanceSpec{
				AdminPassword: "jhkj4%w@oip",
				Existed:       true,
				ExistedOption: ccetypes.ExistedOption{
					ExistedInstanceID: "i-qsqpAg87",
				},
				MachineType: ccetypes.MachineTypeBCC,
				InstanceOS: ccetypes.InstanceOS{
					ImageType: bccimage.ImageTypeSystem,
					OSType:    bccimage.OSTypeLinux,
					OSName:    bccimage.OSNameCentOS,
					OSVersion: "7.5",
					OSArch:    "x86_64 (64bit)",
				},
			},
		},
		{
			InstanceSpec: ccetypes.InstanceSpec{
				AdminPassword: "jhkj4%w@oip",
				Existed:       false,
				MachineType:   ccetypes.MachineTypeBCC,
				InstanceType:  bcc.InstanceTypeN3,
				VPCConfig: ccetypes.VPCConfig{
					VPCSubnetID:     "sbn-wrusz28fzfc",
					SecurityGroupID: "g-sdfedd103nd",
				},
				InstanceResource: ccetypes.InstanceResource{
					CPU: 4,
					MEM: 8,
					CDSList: ccetypes.CDSConfigList{
						ccetypes.CDSConfig{
							Path:        "/data",
							StorageType: bcc.StorageTypeHP1,
							CDSSize:     100,
						},
					},
				},
				InstanceOS: ccetypes.InstanceOS{
					ImageType: bccimage.ImageTypeSystem,
					OSType:    bccimage.OSTypeLinux,
					OSName:    bccimage.OSNameCentOS,
					OSVersion: "7.5",
					OSArch:    "x86_64 (64bit)",
				},
				NeedEIP: true,
			},
			Count: 2,
		},
	})

	return string(str)
}
