// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/11/20 20:28:00, by lili<PERSON><EMAIL>, create
2021/11/17 15:32:00, by <PERSON><PERSON><PERSON><PERSON>@baidu.com, update
*/
/*
CDSPVPVCAttach, csi dynamic cds pv/pvc attach
*/

package csi

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// CDSPVPVCAttach - Case 名字
	CDSPVPVCAttach         cases.CaseName = "CDSPVPVCAttach"
	cdsSize                               = "66"
	cdsCSIName                            = "cce-csi-cds-plugin"
	cdsCSIStorageClassName                = "cds-sc-ssd-test"
	cdsCSIStorageClassYAML                = `apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: cds-sc-ssd-test
provisioner: csi-cdsplugin
parameters:
  dynamicVolume: "true"
  cdsSizeInGB: "cds-size-xx"
  paymentTiming: "Postpaid"
  storageType: "ssd"
reclaimPolicy: Delete`
	cdsPVCName = "cds-pvc-test"
	cdsPVCYAML = `kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: cds-pvc-test
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: cds-sc-ssd-test
  resources:
    requests:
      storage: cds-size-xxGi`
	cdsPodName = "cds-pod-test"
	cdsPodYAML = `apiVersion: v1
kind: Pod
metadata:
  name: cds-pod-test
  namespace: default
  labels:
    name: cds-pod-test
spec:
  containers:
  - name: test-pvc-pod
    image: hub.baidubce.com/public-online/nginx:latest
    volumeMounts:
      - name: cds-pvc
        mountPath: "/cds-volume"
  volumes:
    - name: cds-pvc
      persistentVolumeClaim:
        claimName: cds-pvc-test`
	cdsPodLabel   = "test-pvc"
	cdsDeployYAML = `apiVersion: apps/v1
kind: Deployment
metadata:
  name: test-pvc-deploy
  labels:
    app: test-pvc
spec:
  replicas: 1
  minReadySeconds: 0
  strategy:
    type: RollingUpdate # 策略：滚动更新
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app: test-pvc
  template:
    metadata:
      labels:
        app: test-pvc
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: beta.kubernetes.io/instance-type
                operator: NotIn
                values:
                - BBC
      containers:
      - name: test-pvc-pod
        image: hub.baidubce.com/public-online/nginx:latest
        volumeMounts:
          - name: cds-pvc
            mountPath: "/cds-volume"
      volumes:
        - name: cds-pvc
          persistentVolumeClaim:
            claimName: cds-pvc-test`
)

func init() {
	cases.AddCase(context.TODO(), CDSPVPVCAttach, NewCDSPVPVCAttach)
}

type cdsPVPVCAttach struct {
	base *cases.BaseClient
}

// NewCDSPVPVCAttach - csi dynamic cds pv/pvc attach
func NewCDSPVPVCAttach(ctx context.Context) cases.Interface {
	return &cdsPVPVCAttach{}
}

func (c *cdsPVPVCAttach) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *cdsPVPVCAttach) Name() cases.CaseName {
	return CDSPVPVCAttach
}

func (c *cdsPVPVCAttach) Desc() string {
	return "容器挂载cds相关用例"
}

func (c *cdsPVPVCAttach) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	clusterID := c.base.ClusterID
	cluster, err := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "GetCluster %s failed: %s", clusterID, err)
		return resources, err
	}
	logger.Infof(ctx, "GetCluster %s success: %s", clusterID, utils.ToJSON(cluster))

	// 已通过插件管理部署csi-cds-plugin,检查是否已部署csi-cds-plugin
	status, err := c.base.GetAddonStatus(ctx, clusterID, cdsCSIName)
	if err != nil {
		logger.Errorf(ctx, "GetAddonStatus %s failed: %s", cdsCSIName, status)
		return resources, err
	}

	if status != ccev2.InstancePhaseDeployed {
		if status == ccev2.InstancePhaseUninstalled {
			// 安装cds插件
			logger.Infof(ctx, "Installing cds插件")
			_, err = c.base.CCEClient.InstallAddon(ctx, c.base.ClusterID, &ccev2.InstallParams{
				Name: cdsCSIName,
			}, nil)
			if err != nil {
				logger.Errorf(ctx, "install plugin %s failed: %v", cdsCSIName, err.Error())
				return resources, fmt.Errorf("install plugin %s failed: %v", cdsCSIName, err.Error())
			}
		} else {
			logger.Errorf(ctx, "PluginStatus %s status %s not %s", cdsCSIName, status, ccev2.InstancePhaseDeployed)
			return resources, fmt.Errorf("PluginStatus %s status %s not %s", cdsCSIName, status, ccev2.InstancePhaseDeployed)
		}
	}

	var k8sPod common.K8SPod
	if err := k8sPod.NewK8SPod(ctx, c.base, "kube-system", nil); err != nil {
		return resources, err
	}

	// 部署storageClass
	yaml := strings.ReplaceAll(cdsCSIStorageClassYAML, "cds-size-xx", cdsSize)
	if err := c.base.KubectlClient.Apply(ctx, cluster.Cluster.Spec.K8SVersion, c.base.KubeConfig, yaml); err != nil {
		logger.Errorf(ctx, "Deploy CDS StorageClass \n%s\n failed: %s", yaml, err)
		return resources, err
	}

	// 部署pvc
	yaml = strings.ReplaceAll(cdsPVCYAML, "cds-size-xx", cdsSize)
	if err := c.base.KubectlClient.Apply(ctx, cluster.Cluster.Spec.K8SVersion, c.base.KubeConfig, yaml); err != nil {
		logger.Errorf(ctx, "Deploy CDS PVC \n%s\n failed: %s", yaml, err)
		return resources, err
	}

	var k8sPvc common.K8SPVC
	if err := k8sPvc.NewK8SPVC(ctx, c.base, defaultNamespace, cdsPVCName); err != nil {
		return resources, err
	}

	pvc, err := k8sPvc.WaitPVCreated(ctx)
	if err != nil {
		logger.Errorf(ctx, "GetPersistentVolumeClaims %s/%s failed: %s", defaultNamespace, cdsPVCName, err)
		return resources, err
	}

	cdsPVName := pvc.Spec.VolumeName
	pv, err := c.base.K8SClient.CoreV1().PersistentVolumes().Get(ctx, cdsPVName, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "GetPersistentVolumes %s failed: %s", cdsPVName, err)
		return resources, err
	}
	logger.Infof(ctx, "GetPersistentVolumes %s success: %s", defaultNamespace, cdsPVName, utils.ToJSON(pv))
	cdsID := pv.Spec.CSI.VolumeHandle

	// 检查cds磁盘大小是否符合预期，磁盘状态是否是Available
	var cdsStatus common.CDSStatus
	if err := cdsStatus.NewCDSStatus(ctx, c.base, cdsID, bcc.VOLUMESTATUS_AVALIABLE); err != nil {
		return resources, err
	}
	cdsSizeCurrent, err := cdsStatus.GetCDSSize(ctx, cdsID)
	if err != nil {
		logger.Errorf(ctx, "GetCDSSize %s failed: %s", cdsID, err)
		return resources, err
	}
	if cdsSizeCurrent != cdsSize {
		logger.Errorf(ctx, "Created cds size %s, not %s", cdsSizeCurrent, cdsSize)
		return resources, fmt.Errorf("created cds size %s, not %s", cdsSizeCurrent, cdsSize)
	}
	if err := common.WaitForResourceReady(ctx, &cdsStatus); err != nil {
		return resources, err
	}

	// 部署deployment, 并等待Pod Running
	if err := c.base.KubectlClient.Apply(ctx, cluster.Cluster.Spec.K8SVersion, c.base.KubeConfig, cdsDeployYAML); err != nil {
		logger.Errorf(ctx, "Deploy CDSDeploy %s/%s failed: %s", defaultNamespace, cdsDeployYAML, err)
		return resources, err
	}
	k8sPod.SetMatchLabel("app", cdsPodLabel)
	k8sPod.SetNamespace(defaultNamespace)
	k8sPod.SetStatus(statusRunning)
	err = common.WaitForResourceReady(ctx, &k8sPod)
	if err != nil {
		return resources, err
	}
	podName := k8sPod.GetPodName()

	// 统计耗时
	if err := DeployCDSPodPerf(ctx, c.base, func(k8sPod common.K8SPod) v1.Pod {
		podList, _ := k8sPod.GetPodList(context.TODO())
		return podList.Items[0]
	}(k8sPod)); err != nil {
		return resources, err
	}

	// 向挂载点注入大文件，校验挂载点使用量
	var result string
	largeFileCmd := []string{"sh", "-c", "dd if=/dev/urandom of=/cds-volume/test bs=1M count=4196"}
	if _, err = k8sPod.ExecInPod(ctx, podName, defaultNamespace, largeFileCmd); err != nil {
		logger.Errorf(ctx, "exec in pod %s with cmd %v failed: %v", podName, largeFileCmd, err)
		return resources, err
	}
	checkLargeFileCmd := []string{"sh", "-c", "df -h | grep cds-volume | awk '{print $3}'"}
	if result, err = k8sPod.ExecInPod(ctx, podName, defaultNamespace, checkLargeFileCmd); err != nil || result < "4.0G" {
		logger.Infof(ctx, "cds-volume size want 4G, got %s", result)
		if result < "4.0G" {
			err = errors.New("mount path size is unexpected")
		}
		logger.Errorf(ctx, "exec in pod %s with cmd %v failed: %v", podName, checkLargeFileCmd, err)
		return resources, err
	}
	logger.Infof(ctx, "check add large file in mount path success")

	// 向挂点注入大量文件，校验挂载点文件数
	numbersFileCmd := []string{
		"sh",
		"-c",
		"for i in $(seq 10000);do mkdir -p /cds-volume/pkg$i/{bin,conf,log};done",
	}
	if _, err = k8sPod.ExecInPod(ctx, podName, defaultNamespace, numbersFileCmd); err != nil {
		logger.Errorf(ctx, "exec in pod %s with cmd %v failed: %v", podName, largeFileCmd, err)
		return resources, err
	}
	checkNumbersFileCmd := []string{"sh", "-c", "ls /cds-volume | wc -l"}
	if result, err = k8sPod.ExecInPod(ctx, podName, defaultNamespace, checkNumbersFileCmd); err != nil || result <= "10000" {
		logger.Infof(ctx, "cds-volume size want >10000, got %s", result)
		logger.Errorf(ctx, "exec in pod %s with cmd %v failed: %v", podName, checkNumbersFileCmd, err)
		return resources, err
	}
	logger.Infof(ctx, "check add numbers of folder in mount path success")

	// 检查cds磁盘状态是否是in-use
	cdsStatus.SetStatus(bcc.VOLUMESTATUS_INUSE)
	if err := common.WaitForResourceReady(ctx, &cdsStatus); err != nil {
		return resources, err
	}

	// 删除Pod(触发deployment重启pod)
	err = c.base.K8SClient.CoreV1().Pods(defaultNamespace).Delete(ctx, podName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Delete pod %s/%s failed: %s", defaultNamespace, podName, err)
		return resources, err
	}
	logger.Infof(ctx, "Delete pod %s/%s success", defaultNamespace, podName)

	// 检查pod重启校验状态
	if err = common.WaitForResourceReady(ctx, &k8sPod); err != nil {
		logger.Errorf(ctx, "wait cds pod ready failed: %v", err)
		return resources, err
	}

	// 删除deployment
	err = c.base.K8SClient.AppsV1().Deployments(defaultNamespace).Delete(ctx, "test-pvc-deploy", metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Delete deployment test-pvc-deploy failed: %v", err)
		return resources, err
	}

	// 检查cds磁盘状态是否是available
	cdsStatus.SetStatus(bcc.VOLUMESTATUS_AVALIABLE)
	if err := common.WaitForResourceReady(ctx, &cdsStatus); err != nil {
		return resources, err
	}

	// 删除PVC
	err = c.base.K8SClient.CoreV1().PersistentVolumeClaims(defaultNamespace).Delete(ctx, cdsPVCName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Delete pvc %s/%s failed: %s", defaultNamespace, cdsPVCName, err)
		return resources, err
	}
	logger.Infof(ctx, "Delete pvc %s/%s success", defaultNamespace, cdsPVCName)

	// 检查cds是否删除成功
	isExist, err := cdsStatus.WaitCDSDeleted(ctx, cdsID)
	if err != nil {
		logger.Errorf(ctx, "CheckCDSExist failed: %s", err)
		return resources, err
	}
	if isExist {
		logger.Errorf(ctx, "CDS %s not deleted", cdsID)
		return resources, fmt.Errorf("CDS %s not deleted", cdsID)
	}

	// 删除storageClass
	if err = c.base.K8SClient.StorageV1().StorageClasses().Delete(ctx, cdsCSIStorageClassName, metav1.DeleteOptions{}); err != nil {
		logger.Errorf(ctx, "Delete StorageClass %s failed: %v", cdsCSIStorageClassName, err)
		return resources, err
	}

	return resources, nil
}

func (c *cdsPVPVCAttach) Clean(ctx context.Context) error {
	// 不需要回收集群, 已在外部设置集群的清理 DeleteCluster: true
	return nil
}

func (c *cdsPVPVCAttach) Continue(ctx context.Context) bool {
	return false
}

func (c *cdsPVPVCAttach) ConfigFormat() string {
	return ""
}

func DeployCDSPodPerf(ctx context.Context, base *cases.BaseClient, pod v1.Pod) error {
	var pullingTime, attachedTime time.Time
	var success bool

	// 获取事件
	events, err := base.K8SClient.CoreV1().Events(defaultNamespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "list pod event failed")
		return nil
	}
	for _, event := range events.Items {
		if event.Reason == "SuccessfulAttachVolume" && strings.Contains(event.Name, pod.Name) {
			attachedTime = event.CreationTimestamp.Time
			logger.Infof(ctx, "pod attached to node at: %v", attachedTime)
			success = true
		}
		if event.Reason == "Pulling" && strings.Contains(event.Name, pod.Name) {
			pullingTime = event.CreationTimestamp.Time
			logger.Infof(ctx, "pod pulling image at: %v", pullingTime)
		}
	}

	storagePod := models.StoragePod{
		Region:    base.Region,
		ClusterID: base.ClusterID,
		Name:      pod.Name,
		Model:     "cds",
	}
	if success {
		storagePod.Success = true
		storagePod.AttachCost = pullingTime.Sub(pod.CreationTimestamp.Time)
	} else {
		storagePod.ErrMsg = pod.Status.Message
	}
	if err = base.QaDbClient.CreateStoragePod([]models.StoragePod{storagePod}); err != nil {
		return err
	}
	if success {
		return nil
	}
	return errors.New("cds pod is not ready")
}
