// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/11/20 20:28:00, by lili<PERSON><EMAIL>, create
2021/11/17 15:32:00, by <PERSON><PERSON><PERSON><PERSON>@baidu.com, update
*/
/*
BOSPVPVCAttach, csi static bos pv/pvc attach
*/

package csi

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// BOSPVPVCAttach - Case 名字
	BOSPVPVCAttach       cases.CaseName = "BOSPVPVCAttach"
	defaultBosBucketName                = "bos-csi-test-gztest"
	defaultNamespace                    = "default"
	statusRunning                       = "Running"
	statusPending                       = "Pending"
	bosCSIName                          = "cce-csi-bos-plugin"
	bosSecretYAML                       = `apiVersion: v1
kind: Secret
metadata:
  name: csi-bos-secret-test
  namespace: default
data:
  ak: ak-xx
  sk: sk-xx`
	bosPVName    = "bos-pv-test"
	bosPVCName   = "bos-pvc-test"
	bosPVPVCYAML = `apiVersion: v1
kind: PersistentVolume
metadata:
  name: bos-pv-test
  namespace: "default"
spec:
  accessModes:
  - ReadWriteOnce
  - ReadOnlyMany
  capacity:
    storage: 5Gi
  storageClassName: csi-bos
  csi:
    driver: "csi-bosplugin"
    volumeHandle: "bucket-xx"
    nodePublishSecretRef:
      name: "csi-bos-secret-test"
      namespace: "default"
  persistentVolumeReclaimPolicy: Retain
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: bos-pvc-test
spec:
  accessModes:
  - ReadWriteOnce
  - ReadOnlyMany
  resources:
    requests:
      storage: 5Gi
  storageClassName: csi-bos`
	bosPodRWName = "bos-pod-wr"
	bosPodRWYAML = `apiVersion: v1
kind: Pod
metadata:
  name: bos-pod-wr
  namespace: default
  labels:
    name: bos-pod-wr
spec:
  containers:
  - image: hub.baidubce.com/public-online/nginx:latest
    imagePullPolicy: Always
    name: bos-pod-wr
    volumeMounts:
    - mountPath: /var/lib/www/html
      name: bos-pvc
    - mountPath: /var/lib/www/html000
      name: bos-pvc
      readOnly: true
  volumes:
  - name: bos-pvc
    persistentVolumeClaim:
      claimName: bos-pvc-test
      readOnly: false`
	bosPodRName = "bos-pod-readonly"
	bosPodRYAML = `apiVersion: v1
kind: Pod
metadata:
  name: bos-pod-readonly
  namespace: default
  labels:
    name: bos-pod-readonly
spec:
  containers:
  - image: hub.baidubce.com/public-online/nginx:latest
    imagePullPolicy: Always
    name: bos-pod-readonly
    terminationMessagePath: /dev/termination-log
    terminationMessagePolicy: File
    volumeMounts:
    - mountPath: /var/lib/www/html000
      name: bos-pvc
      readOnly: true
  volumes:
  - name: bos-pvc
    persistentVolumeClaim:
      claimName: bos-pvc-test
      readOnly: true`
)

func init() {
	cases.AddCase(context.TODO(), BOSPVPVCAttach, NewBOSPVPVCAttach)
}

type bosPVPVCAttach struct {
	base   *cases.BaseClient
	config bosConfig
}

type bosConfig struct {
	BucketName string `json:"bucketName"`
	IsDelete   bool   `json:"isDelete"`
}

// NewBOSPVPVCAttach - csi static bos pv/pvc attach
func NewBOSPVPVCAttach(ctx context.Context) cases.Interface {
	return &bosPVPVCAttach{}
}

func (c *bosPVPVCAttach) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	var cfg bosConfig
	if err := json.Unmarshal(config, &cfg); err != nil {
		return fmt.Errorf("json.Unmarshal bos config failed: %s", err)
	}
	c.base = base
	c.config = cfg
	return nil
}

func (c *bosPVPVCAttach) Name() cases.CaseName {
	return BOSPVPVCAttach
}

func (c *bosPVPVCAttach) Desc() string {
	return "[待补充]"
}

func (c *bosPVPVCAttach) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	clusterID := c.base.ClusterID
	cluster, err := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "GetCluster %s failed: %s", clusterID, err)
		return nil, err
	}
	logger.Infof(ctx, "GetCluster %s success: %s", clusterID, utils.ToJSON(cluster))

	// bucket不存在则创建
	if c.config.BucketName == "" {
		c.config.BucketName = defaultBosBucketName
	}

	isExist, err := c.base.BOSClient.DoesBucketExist(c.config.BucketName)
	if err != nil {
		logger.Errorf(ctx, "CheckBucketExist %s failed: %s", c.config.BucketName, err)
		return nil, err
	}
	if !isExist {
		logger.Infof(ctx, "Create BOS bucket %s ", c.config.BucketName)
		_, err := c.base.BOSClient.PutBucket(c.config.BucketName)
		if err != nil {
			logger.Errorf(ctx, "PutBucket %s failed: %s", c.config.BucketName, err)
			return resources, err
		}
		resources = append(resources, cases.Resource{
			CaseName: BOSPVPVCAttach,
			Type:     cases.ResourceTypeBOSBucket,
			ID:       c.config.BucketName,
		})
	}

	status, err := c.base.GetAddonStatus(ctx, clusterID, bosCSIName)
	if err != nil {
		logger.Errorf(ctx, "GetAddonStatus %s failed: %s", bosCSIName, status)
		return resources, err
	}

	// 若没有部署，则安装BOS CSI插件
	if status == ccev2.InstancePhaseUninstalled {
		logger.Infof(ctx, "Addon %s not exist, install bos plugin", bosCSIName)
		_, err = c.base.CCEClient.InstallAddon(ctx, clusterID, &ccev2.InstallParams{
			Name:   bosCSIName,
			Params: "maxVolumesPerNode: 5\ncluster:\n  nodes:\n    - kubeletRootPath: /home/<USER>/kubelet\n      kubeletRootPathAffinity: true\n    - kubeletRootPath: /data/kubelet\n      kubeletRootPathAffinity: true\n    - kubeletRootPath: /var/lib/kubelet\n      kubeletRootPathAffinity: true\n",
		}, nil)
		if err != nil {
			return resources, fmt.Errorf("install addon %s failed: %s", bosCSIName, err)
		}

		// TODO: 优化实现，等待插件ready
		time.Sleep(5 * time.Second)

		status, err = c.base.GetAddonStatus(ctx, clusterID, bosCSIName)
		if err != nil {
			logger.Errorf(ctx, "GetAddonStatus %s failed: %s", bosCSIName, status)
			return resources, err
		}
	}

	if status != ccev2.InstancePhaseDeployed {
		logger.Errorf(ctx, "PluginStatus %s status %s not %s", bosCSIName, status, ccev2.InstancePhaseDeployed)
		return resources, fmt.Errorf("PluginStatus %s status %s not %s", bosCSIName, status, ccev2.InstancePhaseDeployed)
	}

	var k8sPod common.K8SPod
	if err := k8sPod.NewK8SPod(ctx, c.base, "default", nil); err != nil {
		return resources, err
	}

	// 部署csi-bos-secret-test
	ak := base64.StdEncoding.EncodeToString([]byte(c.base.Credentials.AccessKeyID))
	sk := base64.StdEncoding.EncodeToString([]byte(c.base.Credentials.SecretAccessKey))
	yaml := strings.ReplaceAll(strings.ReplaceAll(bosSecretYAML, "ak-xx", ak), "sk-xx", sk)
	if err := c.base.KubectlClient.Apply(ctx, cluster.Cluster.Spec.K8SVersion, c.base.KubeConfig, yaml); err != nil {
		logger.Errorf(ctx, "Deploy BosSecret \n%s\n failed: %s", yaml, err)
		return resources, err
	}

	// 部署pv/pvc
	yaml = strings.ReplaceAll(bosPVPVCYAML, "bucket-xx", c.config.BucketName)
	if err := c.base.KubectlClient.Apply(ctx, cluster.Cluster.Spec.K8SVersion, c.base.KubeConfig, yaml); err != nil {
		logger.Errorf(ctx, "Deploy BOS PV/PVC \n%s\n failed: %s", yaml, err)
		return resources, err
	}

	// 判断pvc状态是否已绑定
	var k8sPvc common.K8SPVC
	if err := k8sPvc.NewK8SPVC(ctx, c.base, defaultNamespace, bosPVCName); err != nil {
		return resources, err
	}
	k8sPvc.SetStatus(v1.ClaimBound)
	if err := common.WaitForResourceReady(ctx, &k8sPvc); err != nil {
		return resources, err
	}

	// 部署Pod（ReadOnly）,并等待Pod Running
	if err := c.base.KubectlClient.Apply(ctx, cluster.Cluster.Spec.K8SVersion, c.base.KubeConfig, bosPodRYAML); err != nil {
		logger.Errorf(ctx, "Deploy BosPodReadOnly %s/%s failed: %s", defaultNamespace, bosPodRName, err)
		return resources, err
	}
	k8sPod.SetNamespace(defaultNamespace)
	k8sPod.SetMatchLabel("name", bosPodRName)
	k8sPod.SetStatus(statusRunning)
	err = common.WaitForResourceReady(ctx, &k8sPod)
	if err != nil {
		return resources, err
	}

	// 统计成功率
	if err := DeployBOSPodPerf(ctx, c.base, func(k8sPod common.K8SPod) v1.Pod {
		podList, _ := k8sPod.GetPodList(context.TODO())
		return podList.Items[0]
	}(k8sPod)); err != nil {
		return resources, err
	}

	// TODO 可以提前放文件到bos上，读取一下挂载目录的内容

	// 部署Pod（ReadWrite）,并等待Pod Running
	if err := c.base.KubectlClient.Apply(ctx, cluster.Cluster.Spec.K8SVersion, c.base.KubeConfig, bosPodRWYAML); err != nil {
		logger.Errorf(ctx, "Deploy BosPodReadWrite %s/%s failed: %s", defaultNamespace, bosPodRWName, err)
		return resources, err
	}
	k8sPod.SetMatchLabel("name", bosPodRWName)
	if err := common.WaitForResourceReady(ctx, &k8sPod); err != nil {
		return resources, err
	}

	// 统计RW BOS POD 成功率
	if err := DeployBOSPodPerf(ctx, c.base, func(k8sPod common.K8SPod) v1.Pod {
		podList, _ := k8sPod.GetPodList(context.TODO())
		return podList.Items[0]
	}(k8sPod)); err != nil {
		return resources, err
	}

	// BOS PVC 读写测试
	var result string
	msg := "autocase-" + utils.RandString(6)
	writeCmd := []string{"sh", "-c", fmt.Sprintf("echo \"%s, \"$(date) > /var/lib/www/html/autotest.txt", msg)}
	if _, err = k8sPod.ExecInPod(ctx, bosPodRWName, defaultNamespace, writeCmd); err != nil {
		logger.Errorf(ctx, "exec in pod %s with cmd %v failed: %v", bosPodRWName, writeCmd, err)
		return resources, err
	}
	writeToReadOnlyCmd := []string{"sh", "-c", "echo \"autocase: write to readonly mount path, \"$(date) >> /var/lib/www/html000/autotest.txt"}
	if _, err = k8sPod.ExecInPod(ctx, bosPodRWName, defaultNamespace, writeToReadOnlyCmd); err != nil && !strings.Contains(err.Error(), "Read-only file system") {
		logger.Errorf(ctx, "exec in pod %s with cmd %v failed: %v", bosPodRWName, writeToReadOnlyCmd, err)
		return resources, err
	}

	readCmd := []string{"sh", "-c", "cat /var/lib/www/html000/autotest.txt"}
	if result, err = k8sPod.ExecInPod(ctx, bosPodRWName, defaultNamespace, readCmd); err != nil || !strings.Contains(result, msg) {
		if err == nil {
			logger.Errorf(ctx, "read from %s /var/lib/www/html000/, result is: %v, not as expect!", bosPodRWName, result)
			return resources, err
		}
		logger.Errorf(ctx, "exec in pod %s with cmd %v failed: %v", bosPodRWName, readCmd, err)
		return resources, err
	}

	// read from another pod （ReadOnly）
	result = ""
	if result, err = k8sPod.ExecInPod(ctx, bosPodRName, defaultNamespace, readCmd); err != nil || !strings.Contains(result, msg) {
		if err == nil {
			logger.Errorf(ctx, "read from %s /var/lib/www/html000/, result is: %v, not as expect!", bosPodRName, result)
			return resources, err
		}
		logger.Errorf(ctx, "exec in pod %s with cmd %v failed: %v", bosPodRName, readCmd, err)
		return resources, err
	}
	logger.Infof(ctx, "check bos write and read success")

	// 删除Pod（ReadWrite）
	err = c.base.K8SClient.CoreV1().Pods(defaultNamespace).Delete(ctx, bosPodRWName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Delete pod %s/%s failed: %s", defaultNamespace, bosPodRWName, err)
	}
	logger.Infof(ctx, "Delete pod %s/%s success", defaultNamespace, bosPodRWName)

	// 删除Pod（ReadOnly）
	err = c.base.K8SClient.CoreV1().Pods(defaultNamespace).Delete(ctx, bosPodRName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Delete pod %s/%s failed: %s", defaultNamespace, bosPodRName, err)
	}
	logger.Infof(ctx, "Delete pod %s/%s success", defaultNamespace, bosPodRName)

	// 删除PVC
	err = c.base.K8SClient.CoreV1().PersistentVolumeClaims(defaultNamespace).Delete(ctx, bosPVCName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Delete pvc %s/%s failed: %s", defaultNamespace, bosPVCName, err)
	}
	logger.Infof(ctx, "Delete pvc %s/%s success", defaultNamespace, bosPVCName)

	// 删除PV
	err = c.base.K8SClient.CoreV1().PersistentVolumes().Delete(ctx, bosPVName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Delete pv %s failed: %s", bosPVName, err)
	}
	logger.Infof(ctx, "Delete pv %s success", bosPVName)

	return resources, nil
}

func (c *bosPVPVCAttach) Clean(ctx context.Context) error {
	// 删除创建的bucket
	if c.config.IsDelete {
		err := c.base.BOSClient.DeleteBucket(c.config.BucketName)
		if err != nil {
			return fmt.Errorf("delete bucket %s err: %s", c.config.BucketName, err)
		}
	}
	return nil
}

func (c *bosPVPVCAttach) Continue(ctx context.Context) bool {
	return false
}

func (c *bosPVPVCAttach) ConfigFormat() string {
	return ""
}

func DeployBOSPodPerf(ctx context.Context, base *cases.BaseClient, pod v1.Pod) error {
	var pullingTime time.Time
	var success bool

	// 获取事件
	events, err := base.K8SClient.CoreV1().Events(defaultNamespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "list pod event failed")
		return nil
	}
	for _, event := range events.Items {
		if event.Reason == "Pulling" && strings.Contains(event.Name, pod.Name) {
			pullingTime = event.CreationTimestamp.Time
			logger.Infof(ctx, "pod pulling image at: %v", pullingTime)
			success = true
		}
	}

	// TODO:统计bos挂载时间
	storagePod := models.StoragePod{
		Region:    base.Region,
		ClusterID: base.ClusterID,
		Name:      pod.Name,
		Model:     "bos",
	}
	if success {
		storagePod.Success = true
		storagePod.AttachCost = pullingTime.Sub(pod.CreationTimestamp.Time)
	} else {
		storagePod.ErrMsg = pod.Status.Message
	}
	if err = base.QaDbClient.CreateStoragePod([]models.StoragePod{storagePod}); err != nil {
		return err
	}
	if success {
		return nil
	}
	return errors.New("bos pod is not ready")
}
