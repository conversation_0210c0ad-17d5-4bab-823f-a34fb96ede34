// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/11/20 20:28:00, by <EMAIL>, create
*/
/*
CFSPVPVCAttach, csi dynamic cfs pv/pvc attach
*/

package csi

import (
	"context"
	"errors"
	"strings"
	"time"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// CFSPVPVCAttach - Case 名字
	CFSPVPVCAttach      cases.CaseName = "CFSPVPVCAttach"
	cfsCSIPluginPodName                = "nfs-client-provisioner"
	cfsCSIPluginYAML                   = `kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: nfs-client-provisioner-runner
rules:
  - apiGroups: [""]
    resources: ["persistentvolumes"]
    verbs: ["get", "list", "watch", "create", "delete"]
  - apiGroups: [""]
    resources: ["persistentvolumeclaims"]
    verbs: ["get", "list", "watch", "update"]
  - apiGroups: ["storage.k8s.io"]
    resources: ["storageclasses"]
    verbs: ["get", "list", "watch"]
  - apiGroups: [""]
    resources: ["events"]
    verbs: ["create", "update", "patch"]
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: run-nfs-client-provisioner
subjects:
  - kind: ServiceAccount
    name: nfs-client-provisioner
    namespace: kube-system
roleRef:
  kind: ClusterRole
  name: nfs-client-provisioner-runner
  apiGroup: rbac.authorization.k8s.io
---
kind: Role
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: leader-locking-nfs-client-provisioner
  namespace: kube-system
rules:
  - apiGroups: [""]
    resources: ["endpoints"]
    verbs: ["get", "list", "watch", "create", "update", "patch"]
---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: leader-locking-nfs-client-provisioner
  namespace: kube-system
subjects:
  - kind: ServiceAccount
    name: nfs-client-provisioner
    namespace: kube-system
roleRef:
  kind: Role
  name: leader-locking-nfs-client-provisioner
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: nfs-client-provisioner
  namespace: kube-system
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: pv-cfs
spec:
  capacity:
    storage: 5Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: sharedcfs
  mountOptions:
    - hard
    - nfsvers=4.1
    - nordirplus
  nfs:
    path: /
    server: address-xx
---
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: pvc-cfs
  namespace: kube-system
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: sharedcfs
  resources:
    requests:
      storage: 5Gi
---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: nfs-client-provisioner
  namespace: kube-system
spec:
  selector:
    matchLabels:
      name: nfs-client-provisioner
  replicas: 1
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        name: nfs-client-provisioner
    spec:
      serviceAccountName: nfs-client-provisioner
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: feature.node.kubernetes.io/system-os_release.ID
                operator: NotIn
                values:
                - "rocky"
      containers:
        - name: nfs-client-provisioner
          image: registry.baidubce.com/cce-plugin-pro/nfs-client-provisioner:latest
          imagePullPolicy: Always
          volumeMounts:
            - name: nfs-client-root
              mountPath: /persistentvolumes
          env:
            - name: PROVISIONER_NAME
              value: baidubce/sharedcfs
            - name: NFS_SERVER
              value: address-xx
            - name: NFS_PATH
              value: /
            - name: SHARE_PATH
              value: "true"
      volumes:
        - name: nfs-client-root
          persistentVolumeClaim:
            claimName: pvc-cfs
---
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: sharedcfs
provisioner: baidubce/sharedcfs
parameters:
  archiveOnDelete: "false"
  sharePath: "false"
mountOptions:
  - hard
  - nfsvers=4.1
  - nordirplus`
	cfsPVCName = "cfs-pvc-test"
	cfsPVCYAML = `apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: cfs-pvc-test
  namespace: default
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: sharedcfs
  resources:
    requests:
      storage: 5Gi`
	cfsPodName = "cfs-pod-test"
	cfsPodYAML = `apiVersion: v1
kind: Pod
metadata:
  name: cfs-pod-test
  namespace: default
  labels:
    name: cfs-pod-test
spec:
  containers:
  - name: cfs-pod-test
    image: hub.baidubce.com/public-online/nginx:latest
    ports:
    - containerPort: 80
    volumeMounts:
    - mountPath: "/cfs-volume"
      name: mycfs
  volumes:
  - name: mycfs
    persistentVolumeClaim:
      claimName: cfs-pvc-test`
)

func init() {
	cases.AddCase(context.TODO(), CFSPVPVCAttach, NewCFSPVPVCAttach)
}

var _ cases.Interface = &cfsPVPVCAttach{}
var FsID string

type cfsPVPVCAttach struct {
	base *cases.BaseClient
}

// NewCFSPVPVCAttach - csi dynamic cfs pv/pvc attach
func NewCFSPVPVCAttach(ctx context.Context) cases.Interface {
	return &cfsPVPVCAttach{}
}

func (c *cfsPVPVCAttach) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	return nil
}

func (c *cfsPVPVCAttach) Name() cases.CaseName {
	return CFSPVPVCAttach
}

func (c *cfsPVPVCAttach) Desc() string {
	return "cfs pv/pvc 动态挂载"
}

func (c *cfsPVPVCAttach) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	clusterID := c.base.ClusterID

	skip, err := c.CheckNodesOSIsAllRocky(ctx)
	if err != nil {
		return resources, err
	}
	if skip {
		return resources, nil
	}

	cluster, err := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "GetCluster %s failed: %s", clusterID, err)
		return resources, err
	}
	logger.Infof(ctx, "GetCluster %s success: %s", clusterID, utils.ToJSON(cluster))
	cceResp, err := c.base.CCEClient.ListInstancesByPage(ctx, clusterID, nil, nil)
	if err != nil {
		logger.Errorf(ctx, "GetInstance %s failed: %s", clusterID, err)
		return resources, err
	}
	if cceResp == nil {
		return resources, errors.New("GetInstance return nil")
	}
	vpcID := cceResp.InstancePage.InstanceList[0].Spec.VPCID
	subnetID := cceResp.InstancePage.InstanceList[0].Spec.VPCSubnetID
	cfsFileSystemName := "cceautotest-cfs-test" + utils.RandString(4)
	// 判断文件系统是否存在, 不存在则创建
	isExist, fsID, err := c.base.CFSClient.CheckFileSystemExist(ctx, cfsFileSystemName)
	if err != nil {
		logger.Errorf(ctx, "CheckFileSystemExist %s failed: %s", cfsFileSystemName, err)
		return resources, err
	}
	if !isExist {
		fsID, err = c.base.CFSClient.CreateFileSystem(ctx, cfsFileSystemName)
		if err != nil {
			logger.Errorf(ctx, "CreateFileSystem %s failed: %s", cfsFileSystemName, err)
			return resources, err
		}
		resources = append(resources, cases.Resource{
			CaseName: CFSPVPVCAttach,
			Type:     cases.ResourceTypeCFSFileSystem,
			ID:       fsID,
		})
		time.Sleep(10 * time.Second)
	}
	FsID = fsID

	// 判断挂载点是否存在, 不存在则创建
	isExist, domain, err := c.base.CFSClient.CheckMountTargetExist(ctx, fsID, subnetID)
	if err != nil {
		logger.Errorf(ctx, "CheckMountTargetExist fileSystem %s in subnet %s failed: %s", fsID, subnetID, err)
		return resources, err
	}
	if !isExist {
		// cfs创建挂载点偶发出现iam认证失败问题，不稳定导致，添加一次重试
		result := false
		for i := 0; i < 2; i++ {
			domain, err = c.base.CFSClient.CreateMountTarget(ctx, fsID, vpcID, subnetID)
			if err != nil {
				logger.Warnf(ctx, "CreateMountTarget %s in vpc %s subnet %s failed: %s", fsID, vpcID, subnetID, err)
				time.Sleep(5 * time.Second)
				continue
			}
			result = true
			break
		}
		if !result {
			return resources, err
		}

		time.Sleep(10 * time.Second)
	}

	// 部署cfsPlugin, 并等待pod Running
	yaml := strings.ReplaceAll(cfsCSIPluginYAML, "address-xx", domain)
	_, err = c.base.K8SClient.StorageV1().StorageClasses().Get(ctx, "sharedcfs", metav1.GetOptions{})
	if err != nil {
		if strings.Contains(err.Error(), "\"sharedcfs\" not found") {
			logger.Infof(ctx, "StorageClass sharedcfs not exist, deply cfs provisioner")

			if err := c.base.KubectlClient.Apply(ctx, cluster.Cluster.Spec.K8SVersion, c.base.KubeConfig, yaml); err != nil {
				logger.Errorf(ctx, "Deploy CFS Plugin \n%s\n failed: %s", yaml, err)
				return resources, err
			}
		} else {
			logger.Errorf(ctx, "GetStorageClass sharedcfs failed: %s", err)
			return resources, err
		}
	}
	logger.Infof(ctx, "wait %s pods running...", cfsCSIPluginPodName)
	var k8sPod common.K8SPod
	if err := k8sPod.NewK8SPod(ctx, c.base, "kube-system", nil); err != nil {
		return resources, err
	}
	k8sPod.SetMatchLabel("name", cfsCSIPluginPodName)
	k8sPod.SetStatus(statusRunning)
	if err := common.WaitForResourceReady(ctx, &k8sPod); err != nil {
		return resources, err
	}

	// 部署pvc
	if err := c.base.KubectlClient.Apply(ctx, cluster.Cluster.Spec.K8SVersion, c.base.KubeConfig, cfsPVCYAML); err != nil {
		logger.Errorf(ctx, "Deploy CFS PVC \n%s\n failed: %s", cfsPVCYAML, err)
		return resources, err
	}

	// 部署Pod, 并等待Pod Running
	if err := c.base.KubectlClient.Apply(ctx, cluster.Cluster.Spec.K8SVersion, c.base.KubeConfig, cfsPodYAML); err != nil {
		logger.Errorf(ctx, "Deploy CFS Pod %s/%s failed: %s", defaultNamespace, cfsPodName, err)
		return resources, err
	}
	k8sPod.SetMatchLabel("name", cfsPodName)
	k8sPod.SetNamespace(defaultNamespace)
	common.WaitForResourceReady(ctx, &k8sPod)
	// 统计成功率
	if err := DeployCFSPodPerf(ctx, c.base, func(k8sPod common.K8SPod) v1.Pod {
		podList, _ := k8sPod.GetPodList(context.TODO())
		return podList.Items[0]
	}(k8sPod)); err != nil {
		return resources, err
	}

	// TODO 可以提前放文件到cfs上，读取一下挂载目录的内容

	// 删除Pod
	err = c.base.K8SClient.CoreV1().Pods(defaultNamespace).Delete(ctx, cfsPodName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Delete pod %s/%s failed: %s", defaultNamespace, cfsPodName, err)
	}
	logger.Infof(ctx, "Delete pod %s/%s success", defaultNamespace, cfsPodName)

	// 删除PVC
	err = c.base.K8SClient.CoreV1().PersistentVolumeClaims(defaultNamespace).Delete(ctx, cfsPVCName, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "Delete pvc %s/%s failed: %s", defaultNamespace, cfsPVCName, err)
	}
	logger.Infof(ctx, "Delete pvc %s/%s success", defaultNamespace, cfsPVCName)

	return resources, nil
}

func (c *cfsPVPVCAttach) Clean(ctx context.Context) error {
	// 删除创建的mountTarget
	if FsID == "" {
		return nil
	}
	mountTargetList, err := c.base.CFSClient.ListMountTargets(ctx, FsID)
	if err != nil {
		logger.Errorf(ctx, "ListMountTargets %s failed: %s", FsID, err)
		return err
	}
	if mountTargetList != nil && len(mountTargetList.MountTargetList) > 0 {
		for _, mountTarget := range mountTargetList.MountTargetList {
			if err = c.base.CFSClient.DeleteMountTarget(ctx, FsID, mountTarget.MountID); err != nil {
				logger.Errorf(ctx, "DeleteMountTarget %s/%s failed: %s", FsID, mountTarget.MountID, err)
				return err
			}
		}
	}
	time.Sleep(10 * time.Second)
	// 删除创建的fileSystem
	// 避免删除mountTarget超时，增加重试
	checkFileSystemDropped := func() error {
		ticker := time.NewTicker(10 * time.Second)
		defer ticker.Stop()
		timer := time.NewTimer(3 * time.Minute)
		defer timer.Stop()

		for {
			select {
			case <-ticker.C:
				if err = c.base.CFSClient.DropFileSystem(ctx, FsID); err != nil {
					logger.Errorf(ctx, "DropFileSystem %s failed: %s，and retry", FsID, err)
					continue
				}
				return nil
			case <-timer.C:
				logger.Errorf(ctx, "timeout waiting for FileSystem %s dropped ", FsID)
				return errors.New("timeout waiting for FileSystem dropped")
			}
		}
	}
	if err := checkFileSystemDropped(); err != nil {
		return err
	}
	return nil
}

func (c *cfsPVPVCAttach) Continue(ctx context.Context) bool {
	return false
}

func (c *cfsPVPVCAttach) ConfigFormat() string {
	return ""
}

// CheckNodesOSIsAllRocky 判断节点是否都是rocky节点
func (c *cfsPVPVCAttach) CheckNodesOSIsAllRocky(ctx context.Context) (bool, error) {
	instances, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
		ClusterRole: ccetypes.ClusterRoleNode,
		PageNo:      1,
		PageSize:    1000,
	}, nil)
	if err != nil {
		logger.Errorf(ctx, "listInstancesByPage failed: %s", err.Error())
		return false, err
	}

	for _, instance := range instances.InstancePage.InstanceList {
		if instance.Spec.InstanceOS.OSName != "Rocky Linux" {
			return false, nil
		}
	}

	return true, nil
}

func DeployCFSPodPerf(ctx context.Context, base *cases.BaseClient, pod v1.Pod) error {
	var pullingTime time.Time
	var success bool

	// 获取事件
	events, err := base.K8SClient.CoreV1().Events(defaultNamespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "list pod event failed")
		return nil
	}
	for _, event := range events.Items {
		if event.Reason == "Pulling" && strings.Contains(event.Name, pod.Name) {
			pullingTime = event.CreationTimestamp.Time
			logger.Infof(ctx, "pod pulling image at: %v", pullingTime)
			success = true
		}
	}

	// TODO:统计cfs挂载时间
	storagePod := models.StoragePod{
		Region:    base.Region,
		ClusterID: base.ClusterID,
		Name:      pod.Name,
		Model:     "cfs",
	}
	if success {
		storagePod.Success = true
		storagePod.AttachCost = pullingTime.Sub(pod.CreationTimestamp.Time)
	} else {
		storagePod.ErrMsg = pod.Status.Message
	}
	if err = base.QaDbClient.CreateStoragePod([]models.StoragePod{storagePod}); err != nil {
		return err
	}
	if success {
		return nil
	}
	return errors.New("cfs pod is not ready")
}
