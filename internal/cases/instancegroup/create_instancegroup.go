/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  create_instancegroup
 * @Version: 1.0.0
 * @Date: 2020/8/6 11:15 上午
 */
package instancegroup

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// CreateInstanceGroup - 创建节点组 Case 名字
	CreateInstanceGroup cases.CaseName = "CreateInstanceGroup"
)

type createInstanceGroupConfig struct {
	Request       ccev2.CreateInstanceGroupRequest `json:"request"`
	NeedClean     bool                             `json:"needClean"`
	CheckRecovery bool                             `json:"checkRecovery"`
}

type createInstanceGroup struct {
	base            *cases.BaseClient
	config          createInstanceGroupConfig
	instanceGroupID string
}

func init() {
	cases.AddCase(context.TODO(), CreateInstanceGroup, NewCreateInstanceGroup)
}

func NewCreateInstanceGroup(ctx context.Context) cases.Interface {
	return &createInstanceGroup{}
}

func (c *createInstanceGroup) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("nil baseClient")
	}

	logger.WithValues("case", "createInstanceGroup").Infof(ctx, "json raw config: %s", string(config))
	if err := json.Unmarshal(config, &c.config); err != nil {
		return err
	}
	logger.WithValues("case", "createInstanceGroup").Infof(ctx, "config: %s", utils.ToJSON(c.config))

	c.base = base
	c.instanceGroupID = ""
	return nil
}

func (c *createInstanceGroup) Name() cases.CaseName {
	return CreateInstanceGroup
}

func (c *createInstanceGroup) Desc() string {
	return "节点组创建"
}

func (c *createInstanceGroup) Check(ctx context.Context) ([]cases.Resource, error) {
	logger := logger.WithValues("case", "createInstanceGroup")

	logger.WithValues("case", "createInstanceGroup").Infof(ctx, "create instancegroup request: %s", utils.ToJSON(c.config.Request))
	resp, err := c.base.CCEClient.CreateInstanceGroup(ctx, c.base.ClusterID, &c.config.Request, nil)
	if err != nil {
		logger.Errorf(ctx, "failed to create instanceGroup, err: %v", err)
		return nil, err
	}

	c.instanceGroupID = resp.InstanceGroupID
	resources := []cases.Resource{
		{
			CaseName: CreateInstanceGroup,
			Type:     cases.ResourceTypeCCEInstanceGroup,
			ID:       resp.InstanceGroupID,
		},
	}

	checkInstanceGroupReplicas := func() error {
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()
		timer := time.NewTimer(20 * time.Minute)
		defer timer.Stop()

		for {
			select {
			case <-ticker.C:
				getIGResp, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, nil)
				if err != nil {
					logger.WithValues("instanceGroupID", c.instanceGroupID).Errorf(ctx, "failed to get instanceGroup, err: %v", err)
					continue
				}

				logger.WithValues("instanceGroupID", c.instanceGroupID).
					Infof(ctx, "instanceGroup expected replicas: %d, actual: %d",
						getIGResp.InstanceGroup.Spec.Replicas, getIGResp.InstanceGroup.Status.ReadyReplicas)
				if getIGResp.InstanceGroup.Spec.Replicas != getIGResp.InstanceGroup.Status.ReadyReplicas {
					continue
				}
				getIGInstances, err := c.base.CCEClient.ListInstancesByInstanceGroupID(ctx, c.base.ClusterID, c.instanceGroupID, 0, 0, nil)
				if err != nil {
					logger.Errorf(ctx, "list instance by instancegroupID failed: %v", err)
					continue
				}
				allInstanceReady := true
				for _, instance := range getIGInstances.Page.List {
					if instance.Status.InstancePhase != ccetypes.InstancePhaseRunning {
						logger.Warnf(ctx, "instance %s phase is %s, not running", instance.Spec.CCEInstanceID, instance.Status.InstancePhase)
						allInstanceReady = false
					}
				}
				if !allInstanceReady {
					logger.Warnf(ctx, "not all instances are running")
					continue
				}

				return nil
			case <-timer.C:
				logger.WithValues("instanceGroupID", c.instanceGroupID).Errorf(ctx, "timeout waiting for instanceGroup replicas ready")
				return errors.New("timeout waiting for instanceGroup replicas ready")
			}
		}
	}
	if err := checkInstanceGroupReplicas(); err != nil {
		return resources, err
	}

	//check deploySetID of instances in instanceGroup
	logger.WithValues("instanceGroupID", c.instanceGroupID).Infof(ctx, "begin to check deploySetID of instances in instanceGroup")
	getIGResp, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, nil)
	if err != nil {
		logger.Errorf(ctx, "get instance by instanceGroupID failed:", err)
	}

	getIGInstances, err := c.base.CCEClient.ListInstancesByInstanceGroupID(ctx, c.base.ClusterID, c.instanceGroupID, 0, 0, nil)
	if err != nil {
		logger.Errorf(ctx, "list instance by instanceGroupID failed:", err)
		return resources, err
	}

	err = c.CheckDeploySetID(ctx, getIGResp.InstanceGroup, getIGInstances.Page.List)
	if err != nil {
		return resources, err
	}

	//判断是否开启CA 获取全局的自动伸缩配置
	getRes, getErr := c.base.CCEClient.GetAutoScalerConfig(ctx, c.base.ClusterID, nil)
	if getErr != nil {
		logger.Errorf(ctx, "getAutoScalerConfig failed: %v", getErr)
		return resources, getErr
	}
	if getRes.Autoscaler == nil || getRes.Autoscaler.CAConfig == nil {
		logger.Infof(ctx, "autoscaler config is not nil, start to create autoscaler config")

		_, err = c.base.CCEClient.CreateAutoScalerConfig(ctx, c.base.ClusterID, nil)
		if err != nil {
			return resources, fmt.Errorf("create auto scaler failed: %v", err)
		}

		//再获取一次
		getRes, getErr = c.base.CCEClient.GetAutoScalerConfig(ctx, c.base.ClusterID, nil)
		if getErr != nil {
			logger.Errorf(ctx, "get auto scaler failed: %v", getErr)
		}

	}

	logger.Infof(ctx, "autoscaler config created")

	_, err = c.base.CCEClient.UpdateInstanceGroupClusterAutoscalerSpec(ctx, c.base.ClusterID, c.instanceGroupID, &ccev2.ClusterAutoscalerSpec{Enabled: false}, nil)
	if err != nil {
		logger.Errorf(ctx, "failed to update autoscaler sepc in instancegroup, err: %v", err)
		return resources, err
	}

	// if c.config.CheckRecovery {
	//	// 下面测试一下删除节点组内的节点后，节点组自恢复是否正常
	//	listInstanceResp, err := c.base.CCEClient.ListInstancesByInstanceGroupID(ctx, c.base.ClusterID, c.instanceGroupID, 0, 0, nil)
	//	if err != nil {
	//		logger.WithValues("instanceGroupID", c.instanceGroupID).
	//			Errorf(ctx, "failed to list instance in instancegroup, err: %v", err)
	//		return resources, err
	//	}
	//
	//	deleteInstanceRequest := ccev2.DeleteInstancesRequest{
	//		InstanceIDs: nil,
	//		DeleteOption: &ccetypes.DeleteOption{
	//			MoveOut:           false,
	//			DeleteResource:    true,
	//			DeleteCDSSnapshot: true,
	//		},
	//	}
	//	for _, instance := range listInstanceResp.Page.List {
	//		deleteInstanceRequest.InstanceIDs = append(deleteInstanceRequest.InstanceIDs, instance.Spec.CCEInstanceID)
	//	}
	//	deleteResp, err := c.base.CCEClient.DeleteInstances(ctx, c.base.ClusterID, &deleteInstanceRequest, nil)
	//	if err != nil {
	//		logger.WithValues("req", deleteInstanceRequest).
	//			WithValues("resp", deleteResp).
	//			Errorf(ctx, "failed to delete instances, err: %v", err)
	//		return resources, err
	//	}
	//
	//	deletedInstanceIDs := make(map[string]struct{})
	//	for _, id := range deleteInstanceRequest.InstanceIDs {
	//		deletedInstanceIDs[id] = struct{}{}
	//	}
	//
	//	if err := func() error {
	//		ticker := time.NewTicker(5 * time.Second)
	//		defer ticker.Stop()
	//		timer := time.NewTimer(20 * time.Minute)
	//		defer timer.Stop()
	//
	//	Retry:
	//		for {
	//			select {
	//			case <-ticker.C:
	//				resp, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, nil)
	//				if err != nil {
	//					logger.WithValues("instanceGroupID", c.instanceGroupID).Errorf(ctx, "failed to get instanceGroup, err: %v", err)
	//					return err
	//				}
	//				if resp.InstanceGroup.Spec.Replicas != resp.InstanceGroup.Status.ReadyReplicas {
	//					logger.WithValues("instanceGroupID", c.instanceGroupID).Infof(ctx, "instanceGroup is still recovering")
	//					continue
	//				}
	//				listInstanceResp, err := c.base.CCEClient.ListInstancesByInstanceGroupID(ctx, c.base.ClusterID, c.instanceGroupID, 0, 0, nil)
	//				if err != nil {
	//					logger.WithValues("instanceGroupID", c.instanceGroupID).
	//						Errorf(ctx, "failed to list instance in instancegroup, err: %v", err)
	//					return err
	//				}
	//				for _, instance := range listInstanceResp.Page.List {
	//					if _, found := deletedInstanceIDs[instance.Spec.CCEInstanceID]; found {
	//						logger.WithValues("instanceGroupID", c.instanceGroupID).
	//							WithValues("instanceID", instance.Spec.CCEInstanceID).
	//							Infof(ctx, "instance is still exist")
	//						continue Retry
	//					}
	//					if instance.Status.InstancePhase != ccetypes.InstancePhaseRunning {
	//						logger.WithValues("instanceGroupID", c.instanceGroupID).
	//							WithValues("instanceID", instance.Spec.CCEInstanceID).
	//							WithValues("phase", instance.Status.InstancePhase).
	//							Infof(ctx, "instance is not running")
	//						continue Retry
	//					}
	//				}
	//				return nil
	//			case <-timer.C:
	//				logger.WithValues("instanceGroupID", c.instanceGroupID).Errorf(ctx, "timeout(20m) waiting for instanceGroup recover")
	//				return fmt.Errorf("timeout(20m) waiting for instanceGroup %s recover", c.instanceGroupID)
	//			}
	//		}
	//	}(); err != nil {
	//		return resources, err
	//	}
	// }

	instanceGroup, initErr := common.NewInstanceGroup(ctx, c.base, c.instanceGroupID, time.Second*10, time.Minute*8)
	if initErr != nil {
		return nil, fmt.Errorf("create instance group %v failed: %v", c.instanceGroupID, initErr)
	}
	err = instanceGroup.AreTasksCompleted(ctx)
	if err != nil {
		return nil, fmt.Errorf("instance group %v tasks are not completed: %v", c.instanceGroupID, err)
	}

	return resources, nil
}

func (c *createInstanceGroup) Clean(ctx context.Context) error {
	// 清理测试节点组
	_, err := c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, true, nil)
	if err != nil {
		logger.Errorf(ctx, "delete instanceGroup %s failed: %v", c.instanceGroupID, err.Error())
		return fmt.Errorf(err.Error())
	}

	return nil
}

func (c *createInstanceGroup) Continue(ctx context.Context) bool {
	return false
}

func (c *createInstanceGroup) ConfigFormat() string {
	str, _ := json.Marshal(c.config)
	return string(str)
}

func (c *createInstanceGroup) CheckDeploySetID(ctx context.Context, instanceGroup *ccev2.InstanceGroup, instances []*ccev2.Instance) error {
	if instanceGroup == nil {
		return errors.New("nil instanceGroup")
	}

	iGDeploySetID := instanceGroup.Spec.InstanceTemplate.DeploySetID
	logger.Infof(ctx, "deploySetID %v of instances in instanceGroup: %v", iGDeploySetID, instanceGroup.Spec.CCEInstanceGroupID)

	for _, instance := range instances {
		//后端修复不全，实例接口返回信息中deploySetID为空，暂时用CRD接口
		instanceCRDRes, err := c.base.CCEHostClient.GetInstanceCRD(ctx, c.base.ClusterID, instance.Spec.CCEInstanceID, nil)
		if err != nil {
			return fmt.Errorf("failed to get instance %s crd: %v", instance.Spec.CCEInstanceID, err)
		}

		if instanceCRDRes.Instance.Spec.DeploySetID != iGDeploySetID {
			return fmt.Errorf("instance %s deploySetID %s not equal instanceGroup %s deploySetID %s", instance.Spec.CCEInstanceID,
				instanceCRDRes.Instance.Spec.DeploySetID, instanceGroup.Spec.CCEInstanceGroupID, iGDeploySetID)
		}
	}

	logger.Infof(ctx, "all instances in instanceGroup %s deploySetIDs are equal with instanceGroup deploySetID %s",
		instanceGroup.Spec.CCEInstanceGroupID, iGDeploySetID)

	return nil
}
