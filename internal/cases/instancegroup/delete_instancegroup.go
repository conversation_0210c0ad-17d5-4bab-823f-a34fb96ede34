/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  delete_instancegroup
 * @Version: 1.0.0
 * @Date: 2020/8/6 2:57 下午
 */
package instancegroup

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// DeleteInstanceGroup - 删除节点组 Case 名字
	DeleteInstanceGroup cases.CaseName = "DeleteInstanceGroup"
)

type deleteInstanceGroup struct {
	base              *cases.BaseClient
	remainInstanceIDs []string
	config            deleteInstanceGroupConfig
	resources         []cases.Resource
}

type deleteInstanceGroupConfig struct {
	CreateInstanceGroupConfig *ccetypes.InstanceGroupSpec `json:"createInstanceGroupConfig"`
	InstanceGroupID           string                      `json:"instanceGroupID"`
	DeleteInstances           bool                        `json:"deleteInstances"`
}

func init() {
	cases.AddCase(context.TODO(), DeleteInstanceGroup, NewDeleteInstanceGroup)
}

func NewDeleteInstanceGroup(ctx context.Context) cases.Interface {
	return &deleteInstanceGroup{}
}

func (c *deleteInstanceGroup) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("nil baseClient")
	}

	logger.WithValues("case", "deleteInstanceGroup").Infof(ctx, "json raw config: %s", string(config))
	if err := json.Unmarshal(config, &c.config); err != nil {
		return err
	}
	logger.WithValues("case", "deleteInstanceGroup").Infof(ctx, "config: %s", utils.ToJSON(c.config))

	c.base = base
	c.resources = nil
	return nil
}

func (c *deleteInstanceGroup) Name() cases.CaseName {
	return DeleteInstanceGroup
}

func (c *deleteInstanceGroup) Desc() string {
	return "节点组删除"
}

func (c *deleteInstanceGroup) Check(ctx context.Context) (resources []cases.Resource, err error) {
	logger := logger.WithValues("case", "deleteInstanceGroup")

	instanceGroupID := c.config.InstanceGroupID
	if c.config.CreateInstanceGroupConfig != nil {
		createInstanceGroupClient := &createInstanceGroup{
			base: c.base,
			config: createInstanceGroupConfig{
				Request: ccev2.CreateInstanceGroupRequest{
					InstanceGroupSpec: *c.config.CreateInstanceGroupConfig,
				},
				NeedClean: false,
			},
		}
		logger.WithValues("case", "deleteInstanceGroup").Infof(ctx, "create instancegroup request: %s", utils.ToJSON(createInstanceGroupClient.config.Request))

		resources, err = createInstanceGroupClient.Check(ctx)
		if err != nil {
			logger.Errorf(ctx, "failed to create instanceGroup, err: %v", err)
			return
		}

		if len(resources) == 0 {
			logger.Errorf(ctx, "no resource return by createInstanceGroupClient")
			return resources, errors.New("no resource return by createInstanceGroupClient")
		}

		for _, r := range resources {
			if r.Type == cases.ResourceTypeCCEInstanceGroup {
				instanceGroupID = r.ID
				break
			}
		}

		c.resources = resources
	}

	if instanceGroupID == "" {
		return resources, errors.New("empty instanceGroupID")
	}

	listInstanceByInstanceGroupIDResp, err := c.base.CCEClient.ListInstancesByInstanceGroupID(ctx, c.base.ClusterID, instanceGroupID, 0, 0, nil)
	if err != nil {
		logger.WithValues("instanceGroupID", instanceGroupID).WithValues("resp", listInstanceByInstanceGroupIDResp).
			Errorf(ctx, "failed to list instance in instanceGroup, err: %v", err)
		return resources, err
	}

	resp, err := c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, instanceGroupID, c.config.DeleteInstances, nil)
	if err != nil {
		logger.WithValues("instanceGroupID", instanceGroupID).WithValues("resp", resp).
			Errorf(ctx, "failed to delete instanceGroup, err: %v", err)
		return resources, err
	}

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()
	timer := time.NewTimer(20 * time.Minute)
	defer timer.Stop()

Retry:
	for {
		select {
		case <-ticker.C:
			resp, err := c.base.CCEClient.ListInstanceGroups(ctx, c.base.ClusterID, nil, nil)
			if err != nil {
				logger.WithValues("clusterID", c.base.ClusterID).Errorf(ctx, "failed to list instanceGroup, err: %v", err)
				continue
			}

			// 判断节点组是否已删除，若没有，则继续
			for _, ig := range resp.Page.List {
				if ig.Spec.CCEInstanceGroupID == instanceGroupID {
					logger.WithValues("instanceGroupID", instanceGroupID).Infof(ctx, "instanceGroup still exist")
					continue Retry
				}
			}

			// 判断集群的中节点是否还属于该节点组，若还有，则继续
			listInstanceResp, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
				PageNo:   1,
				PageSize: 1000,
			}, nil)
			if err != nil {
				logger.WithValues("clusterID", c.base.ClusterID).
					WithValues("resp", listInstanceResp).
					Errorf(ctx, "failed to list instance in cluster, err: %v", err)
				continue
			}

			for _, instance := range listInstanceResp.InstancePage.InstanceList {
				if instance.Spec.InstanceGroupID == instanceGroupID {
					logger.WithValues("instanceGroupID", instanceGroupID).
						WithValues("instanceID", instance.Spec.CCEInstanceID).
						Infof(ctx, "instance in instanceGroup still exist")
					continue Retry
				}
			}

			// 若只是移出节点组，节点保留在集群，则校验这些节点仍在集群中
			if !c.config.DeleteInstances {
				for _, instance := range listInstanceByInstanceGroupIDResp.Page.List {
					c.remainInstanceIDs = append(c.remainInstanceIDs, instance.Spec.CCEInstanceID)
					resp, err := c.base.CCEClient.GetInstance(ctx, c.base.ClusterID, instance.Spec.CCEInstanceID, nil)
					if err != nil {
						logger.WithValues("instanceID", instance.Spec.CCEInstanceID).
							Errorf(ctx, "check instance exist failed, err: %v", err)
						return resources, err
					}

					if resp.Instance.Status.InstancePhase == ccetypes.InstancePhaseDeleting {
						logger.WithValues("instanceID", resp.Instance.Spec.CCEInstanceID).
							Errorf(ctx, "move out instancegroup instance should not be deleted")
						return resources, errors.New("instance moved out of instancegroup is deleted")
					}
				}
			}

			// 若删除节点组时删除组内节点，则校验bcc侧是否泄漏
			if c.config.DeleteInstances {
				for _, instance := range listInstanceByInstanceGroupIDResp.Page.List {
					resp, err := c.base.BCCClient.DescribeInstance(ctx, instance.Status.Machine.InstanceID, nil)
					if err != nil {
						if strings.Contains(err.Error(), "The specified object is not found or resource do not exist") {
							logger.Infof(ctx, "the bcc %s is not existed", instance.Status.Machine.InstanceID)
						}
					}
					if resp != nil {
						logger.Warnf(ctx, "the bcc is still existed")
						continue Retry
					}
				}
			}

			return resources, nil
		case <-timer.C:
			logger.WithValues("instanceGroupID", instanceGroupID).Errorf(ctx, "timeout waiting for instanceGroup deleted")
			return resources, errors.New("timeout waiting for instanceGroup deleted")
		}
	}
}

func (c *deleteInstanceGroup) Clean(ctx context.Context) error {
	// 清理被移出节点组，但仍保留在集群的节点
	if c.remainInstanceIDs != nil {
		_, err := c.base.CCEClient.DeleteInstances(ctx, c.base.ClusterID, &ccev2.DeleteInstancesRequest{
			InstanceIDs: c.remainInstanceIDs,
			DeleteOption: &ccetypes.DeleteOption{
				MoveOut:           false,
				DeleteResource:    true,
				DeleteCDSSnapshot: true,
			},
		}, nil)
		if err != nil {
			logger.Errorf(ctx, "delete remain instances from instanceGroup failed: %v", err.Error())
			return fmt.Errorf(err.Error())
		}
	}

	return nil
}

func (c *deleteInstanceGroup) Continue(ctx context.Context) bool {
	return false
}

func (c *deleteInstanceGroup) ConfigFormat() string {
	str, _ := json.Marshal(c.config)
	return string(str)
}
