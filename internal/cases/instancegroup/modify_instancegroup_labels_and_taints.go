/*
*
  - @Author: pansiyuan02
  - @Description:
  - @File:  modify_instancegroup_labels_and_taints
  - @Version: 1.0.0
  - @Date: 2021/4/29 1:59 下午

新修改内容：
更改节点组配置的标签、污点、注解后：
1、同步：会更新节点组存量节点的标签、污点、注解
2、不同步：节点组存量节点不会更新标签、污点、注解

更改逻辑
*/
package instancegroup

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	taintutil "k8s.io/kubernetes/pkg/util/taints"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	ModifyInstanceGroupLabelsAndTaintsAndAnnotations cases.CaseName = "ModifyInstanceGroupLabelsAndTaintsAndAnnotations"
)

type modifyInstanceGroupLabelsAndTaintsAndAnnotations struct {
	base            *cases.BaseClient
	instanceGroupID string
	config          modifyInstanceGroupLabelsAndTaintsAndAnnotationsConfig
}

type modifyInstanceGroupLabelsAndTaintsAndAnnotationsConfig struct {
	CreateInstanceGroupConfig *ccetypes.InstanceGroupSpec `json:"createInstanceGroupConfig"`
	ModifyLabelsCases         []modifyLabelsCase          `json:"modifyLabelsCases"`
	ModifyAnnotationsCases    []modifyAnnotationsCase     `json:"modifyAnnotationsCases"`
	ModifyTaintsCases         []modifyTaintsCase          `json:"modifyTaintsCases"`
}

type modifyLabelsCase struct {
	Init      []expectedLabelsStatus `json:"init"`
	Actions   []modifyLabelsAction   `json:"actions"`
	Expected  []expectedLabelsStatus `json:"expected"`
	LabelSync bool                   `json:"labelsSync"`
}

type modifyLabelsAction struct {
	TargetType string            `json:"targetType"`
	ActionType string            `json:"actionType"`
	Labels     map[string]string `json:"labels"`
}

type expectedLabelsStatus struct {
	Labels map[string]string `json:"labels"`
	Exist  bool              `json:"exist"`
}

type modifyAnnotationsCase struct {
	Init     []expectedAnnotationsStatus `json:"init"`
	Actions  []modifyAnnotationsAction   `json:"actions"`
	Expected []expectedAnnotationsStatus `json:"expected"`
	AnnoSync bool                        `json:"annoSync"`
}

type modifyAnnotationsAction struct {
	TargetType  string            `json:"targetType"`
	ActionType  string            `json:"actionType"`
	Annotations map[string]string `json:"annotations"`
}

type expectedAnnotationsStatus struct {
	Annotations map[string]string `json:"annotations"`
	Exist       bool              `json:"exist"`
}

type modifyTaintsCase struct {
	Init      []expectedTaintsStatus `json:"init"`
	Actions   []modifyTaintsAction   `json:"actions"`
	Expected  []expectedTaintsStatus `json:"expected"`
	TaintSync bool                   `json:"taintSync"`
}

type modifyTaintsAction struct {
	TargetType string         `json:"targetType"`
	ActionType string         `json:"actionType"`
	Taints     []corev1.Taint `json:"taints"`
}

type expectedTaintsStatus struct {
	Taints []corev1.Taint `json:"taints"`
	Exist  bool           `json:"exist"`
}

func init() {
	cases.AddCase(context.TODO(), ModifyInstanceGroupLabelsAndTaintsAndAnnotations, NewModifyInstanceGroupLabelsAndTaintsAndAnnotations)
}

func NewModifyInstanceGroupLabelsAndTaintsAndAnnotations(ctx context.Context) cases.Interface {
	return &modifyInstanceGroupLabelsAndTaintsAndAnnotations{}
}

func (c *modifyInstanceGroupLabelsAndTaintsAndAnnotations) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("nil baseClient")
	}

	logger.WithValues("case", "modifyInstanceGroupLabels").Infof(ctx, "json raw config: %s", string(config))
	if err := json.Unmarshal(config, &c.config); err != nil {
		return err
	}
	logger.WithValues("case", "modifyInstanceGroupLabels").Infof(ctx, "config: %s", utils.ToJSON(c.config))

	c.base = base
	return nil
}

func (c *modifyInstanceGroupLabelsAndTaintsAndAnnotations) Name() cases.CaseName {
	return ModifyInstanceGroupLabelsAndTaintsAndAnnotations
}

func (c *modifyInstanceGroupLabelsAndTaintsAndAnnotations) Desc() string {
	return "修改节点组的标签和污点"
}

func (c *modifyInstanceGroupLabelsAndTaintsAndAnnotations) Check(ctx context.Context) ([]cases.Resource, error) {
	createInstanceGroupClient := &createInstanceGroup{
		base: c.base,
		config: createInstanceGroupConfig{
			Request: ccev2.CreateInstanceGroupRequest{
				InstanceGroupSpec: *c.config.CreateInstanceGroupConfig,
			},
			NeedClean: false,
		},
	}
	logger.Infof(ctx, "create instancegroup request: %s", utils.ToJSON(createInstanceGroupClient.config.Request))

	resources, err := createInstanceGroupClient.Check(ctx)
	if err != nil {
		logger.Errorf(ctx, "failed to create instanceGroup, err: %v", err)
		return resources, nil
	}

	if len(resources) == 0 {
		logger.Errorf(ctx, "no resource return by createInstanceGroupClient")
		return resources, errors.New("no resource return by createInstanceGroupClient")
	}

	var instanceGroupID string
	for _, r := range resources {
		if r.Type == cases.ResourceTypeCCEInstanceGroup {
			instanceGroupID = r.ID
			c.instanceGroupID = instanceGroupID
			break
		}
	}

	if instanceGroupID == "" {
		return resources, errors.New("empty instanceGroupID")
	}

	// labels cases
	logger.Infof(ctx, "start to check labels for instanceGroup %s", instanceGroupID)
	for i, tc := range c.config.ModifyLabelsCases {
		ig, err := c.getInstanceGroup(ctx, instanceGroupID)
		if err != nil {
			return resources, fmt.Errorf("label case %d failed, err: %v", i, err)
		}

		if err := c.initLabels(ctx, ig, tc.Init, tc.LabelSync); err != nil {
			return resources, fmt.Errorf("label case %d failed, err: %v", i, err)
		}

		oldNodes, err := c.getNodesOfInstanceGroup(ctx, instanceGroupID)
		if err != nil {
			return resources, fmt.Errorf("case %d, get instanceGroup %v nodes err: %v", i, instanceGroupID, err)
		}

		oldLabels := make([]map[string]string, len(oldNodes))
		for _, nodes := range oldNodes {
			oldLabels = append(oldLabels, nodes.Labels)
		}

		if err := c.checkLabelsResult(ctx, instanceGroupID, tc.Init, oldLabels, tc.LabelSync); err != nil {
			logger.Errorf(ctx, "failed to check init labels result, err: %v", err)
			return resources, fmt.Errorf("label case %d failed, err: %v", i, err)
		}

		for _, action := range tc.Actions {
			if err := c.doLabelsAction(ctx, ig, action, tc.LabelSync); err != nil {
				logger.Errorf(ctx, "failed to do labels action, err: %v", err)
				return resources, fmt.Errorf("label case %d failed, err: %v", i, err)
			}
		}

		oldNodes, err = c.getNodesOfInstanceGroup(ctx, instanceGroupID)
		if err != nil {
			return resources, fmt.Errorf("case %d, get instanceGroup %v nodes err: %v", i, instanceGroupID, err)
		}

		oldLabels = make([]map[string]string, len(oldNodes))
		for _, nodes := range oldNodes {
			oldLabels = append(oldLabels, nodes.Labels)
		}

		if err := c.checkLabelsResult(ctx, instanceGroupID, tc.Expected, oldLabels, tc.LabelSync); err != nil {
			logger.Errorf(ctx, "failed to check labels actions result, err: %v", err)
			return resources, fmt.Errorf("label case %d failed, err: %v", i, err)
		}
	}
	logger.Infof(ctx, "check labels for instanceGroup %s successfully", instanceGroupID)

	logger.Infof(ctx, "start to check annotations for instanceGroup %s", instanceGroupID)
	// annotations cases
	for i, tc := range c.config.ModifyAnnotationsCases {
		ig, err := c.getInstanceGroup(ctx, instanceGroupID)
		if err != nil {
			return resources, fmt.Errorf("label case %d failed, err: %v", i, err)
		}

		if err := c.initAnnotations(ctx, ig, tc.Init, tc.AnnoSync); err != nil {
			return resources, fmt.Errorf("label case %d failed, err: %v", i, err)
		}

		oldNodes, err := c.getNodesOfInstanceGroup(ctx, instanceGroupID)
		if err != nil {
			return resources, fmt.Errorf("case %d, get instanceGroup %v nodes err: %v", i, instanceGroupID, err)
		}

		oldAnnotations := make([]map[string]string, len(oldNodes))

		for _, nodes := range oldNodes {
			oldAnnotations = append(oldAnnotations, nodes.Annotations)
		}

		if err := c.checkAnnotationsResult(ctx, instanceGroupID, tc.Init, oldAnnotations, tc.AnnoSync); err != nil {
			logger.Errorf(ctx, "failed to check init labels result, err: %v", err)
			return resources, fmt.Errorf("label case %d failed, err: %v", i, err)
		}

		for _, action := range tc.Actions {
			if err := c.doAnnotationsAction(ctx, ig, action, tc.AnnoSync); err != nil {
				logger.Errorf(ctx, "failed to do labels action, err: %v", err)
				return resources, fmt.Errorf("label case %d failed, err: %v", i, err)
			}
		}

		oldAnnotations = make([]map[string]string, len(oldNodes))

		for _, nodes := range oldNodes {
			oldAnnotations = append(oldAnnotations, nodes.Annotations)
		}

		if err := c.checkAnnotationsResult(ctx, instanceGroupID, tc.Expected, oldAnnotations, tc.AnnoSync); err != nil {
			logger.Errorf(ctx, "failed to check labels actions result, err: %v", err)
			return resources, fmt.Errorf("label case %d failed, err: %v", i, err)
		}
	}
	logger.Infof(ctx, "check annotations for instanceGroup %s successfully", instanceGroupID)

	// taints cases
	logger.Infof(ctx, "start to check taints for instanceGroup %s", instanceGroupID)
	for i, tc := range c.config.ModifyTaintsCases {
		ig, err := c.getInstanceGroup(ctx, instanceGroupID)
		if err != nil {
			return resources, fmt.Errorf("taint case %d failed, err: %v", i, err)
		}

		if err := c.initTaints(ctx, ig, tc.Init, tc.TaintSync); err != nil {
			return resources, fmt.Errorf("taint case %d failed, err: %v", i, err)
		}

		oldNodes, err := c.getNodesOfInstanceGroup(ctx, instanceGroupID)
		if err != nil {
			return resources, fmt.Errorf("case %d, get instanceGroup %v nodes err: %v", i, instanceGroupID, err)
		}

		oldTaints := make([][]corev1.Taint, len(oldNodes))

		for _, nodes := range oldNodes {
			oldTaints = append(oldTaints, nodes.Spec.Taints)
		}

		if err := c.checkTaintsResult(ctx, instanceGroupID, tc.Init, oldTaints, tc.TaintSync); err != nil {
			logger.Errorf(ctx, "failed to check init taints result, err: %v", err)
			return resources, fmt.Errorf("taint case %d failed, err: %v", i, err)
		}

		for _, action := range tc.Actions {
			if err := c.doTaintsAction(ctx, ig, action, tc.TaintSync); err != nil {
				logger.Errorf(ctx, "failed to do taints action, err: %v", err)
				return resources, fmt.Errorf("taint case %d failed, err: %v", i, err)
			}
		}

		oldTaints = make([][]corev1.Taint, len(oldNodes))

		for _, nodes := range oldNodes {
			oldTaints = append(oldTaints, nodes.Spec.Taints)
		}

		if err := c.checkTaintsResult(ctx, instanceGroupID, tc.Expected, oldTaints, tc.TaintSync); err != nil {
			logger.Errorf(ctx, "failed to check taints actions result, err: %v", err)
			return resources, fmt.Errorf("taint case %d failed, err: %v", i, err)
		}
	}
	logger.Infof(ctx, "check taints for instanceGroup %s successfully", instanceGroupID)

	instanceGroup, initErr := common.NewInstanceGroup(ctx, c.base, c.instanceGroupID, time.Second*10, time.Minute*8)
	if initErr != nil {
		return nil, fmt.Errorf("create instance group %v failed: %v", c.instanceGroupID, initErr)
	}
	err = instanceGroup.AreTasksCompleted(ctx)
	if err != nil {
		return nil, fmt.Errorf("instance group %v tasks are not completed: %v", c.instanceGroupID, err)
	}

	return resources, nil
}

func (c *modifyInstanceGroupLabelsAndTaintsAndAnnotations) getInstanceGroup(ctx context.Context, id string) (*ccev2.InstanceGroup, error) {
	resp, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, id, nil)
	if err != nil {
		return nil, err
	}

	return resp.InstanceGroup, err
}

func (c *modifyInstanceGroupLabelsAndTaintsAndAnnotations) getNodesOfInstanceGroup(ctx context.Context, id string) ([]corev1.Node, error) {
	list, err := c.base.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s=%s", ccetypes.InstanceGroupIDLabelKey, id),
	})
	if err != nil {
		return nil, err
	}

	if len(list.Items) == 0 {
		return nil, errors.New("not node found in instancegroup")
	}
	return list.Items, nil
}

func (c *modifyInstanceGroupLabelsAndTaintsAndAnnotations) initLabels(ctx context.Context, ig *ccev2.InstanceGroup, initStatus []expectedLabelsStatus, isSyncMeta bool) error {
	for _, expected := range initStatus {
		var actionType string
		if expected.Exist {
			actionType = "add"
		} else {
			actionType = "delete"
		}

		if err := c.doLabelsAction(ctx, ig, modifyLabelsAction{
			TargetType: "instancegroup",
			ActionType: actionType,
			Labels:     expected.Labels,
		}, isSyncMeta); err != nil {
			logger.Infof(ctx, "failed to do init labels action, err: %v", err)
			return err
		}
	}

	return nil
}

func (c *modifyInstanceGroupLabelsAndTaintsAndAnnotations) doLabelsAction(ctx context.Context, ig *ccev2.InstanceGroup, action modifyLabelsAction, isSyncMeta bool) error {
	switch action.TargetType {
	case "node":
		nodes, err := c.getNodesOfInstanceGroup(ctx, ig.Spec.CCEInstanceGroupID)
		if err != nil {
			logger.Errorf(ctx, "failed to get node in instancegroup, err: %v", err)
			return err
		}

		for _, node := range nodes {
			for key, value := range action.Labels {
				switch action.ActionType {
				case "add":
					node.Labels[key] = value
				case "delete":
					delete(node.Labels, key)
				}
			}
			if _, err := c.base.K8SClient.CoreV1().Nodes().Update(ctx, &node, metav1.UpdateOptions{}); err != nil {
				logger.Errorf(ctx, "failed to update node, err: %v", err)
				return err
			}
		}
	case "instancegroup":
		for key, value := range action.Labels {
			switch action.ActionType {
			case "add":
				if ig.Spec.InstanceTemplate.Labels == nil {
					ig.Spec.InstanceTemplate.Labels = map[string]string{}
				}
				ig.Spec.InstanceTemplate.Labels[key] = value
			case "delete":
				delete(ig.Spec.InstanceTemplate.Labels, key)
			}
		}

		if _, err := c.base.CCEHostClient.UpdateInstanceGroupConfig(ctx, c.base.ClusterID, ig.Spec.CCEInstanceGroupID, &ccev2.UpdateInstanceGroupRequest{
			PasswordNeedUpdate: false,
			InstanceGroupSpec: ccetypes.InstanceGroupSpec{
				InstanceGroupName: ig.Spec.InstanceGroupName,
				InstanceTemplates: []ccetypes.InstanceTemplate{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							MachineType:  ig.Spec.InstanceTemplate.MachineType,
							InstanceType: ig.Spec.InstanceTemplate.InstanceType,
							VPCConfig: ccetypes.VPCConfig{
								VPCSubnetID: ig.Spec.InstanceTemplate.VPCSubnetID,
							},
							ImageID:     ig.Spec.InstanceTemplate.ImageID,
							Labels:      ig.Spec.InstanceTemplate.Labels,
							Taints:      ig.Spec.InstanceTemplate.Taints,
							Annotations: ig.Spec.InstanceTemplate.Annotations,
						},
					},
				},
				Replicas: ig.Spec.Replicas,
				ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
					Enabled: ig.Spec.ClusterAutoscalerSpec.Enabled,
				},
				SyncMeta: isSyncMeta,
			},
		}, nil); err != nil {
			logger.Errorf(ctx, "failed to update instancegroup labels, err: %v", err)
			return err
		}
	}

	return nil
}

func (c *modifyInstanceGroupLabelsAndTaintsAndAnnotations) checkLabelsResult(ctx context.Context, instanceGroupID string,
	labelsStatus []expectedLabelsStatus, oldLabelsStatus []map[string]string, isSyncMeta bool) error {
	timer := time.NewTimer(130 * time.Second)
	defer timer.Stop()
	ticker := time.NewTicker(3 * time.Second)
	defer ticker.Stop()

	if isSyncMeta {
		logger.Infof(ctx, "instancegroup syncMeta is true, all nodes in %v should be synced", instanceGroupID)
		for {
			select {
			case <-timer.C:
				return errors.New("timeout(2m10s) waitting for label action result")
			case <-ticker.C:
				nodes, err := c.getNodesOfInstanceGroup(ctx, instanceGroupID)
				if err != nil {
					logger.Errorf(ctx, "failed to get node in instancegroup, err: %v", err)
					continue
				}
				ready := true
			LOOP:
				for _, node := range nodes {
					logger.Infof(ctx, "check node labels, node[%s] contains labels: %v", node.Name, node.Labels)
					for _, expected := range labelsStatus {
						for key, value := range expected.Labels {
							v, found := node.Labels[key]
							exist := found && v == value
							if exist != expected.Exist {
								logger.Infof(ctx, "check node labels, node[%s] label[%s] check failed, expect: %v",
									node.Name, key, expected.Exist)
								ready = false
								break LOOP
							}
						}
					}
				}
				if !ready {
					logger.Infof(ctx, "node labels is syncing, retry later")
					continue
				}
				return nil
			}
		}
	} else {
		nodes, err := c.getNodesOfInstanceGroup(ctx, instanceGroupID)
		if err != nil {
			return fmt.Errorf("failed to get node in instancegroup, err: %v", err)
		}
		logger.Infof(ctx, "instancegroup syncMeta is false, all nodes in %v are unsynced", instanceGroupID)
		for i, node := range nodes {
			for key, value := range oldLabelsStatus[i] {
				v, found := node.Labels[key]
				if found && v != value {
					err = fmt.Errorf("node labels have been changed, expect: %v, actual: %v", value, v)
					return err
				}
			}
		}
		return nil
	}
}

func (c *modifyInstanceGroupLabelsAndTaintsAndAnnotations) initAnnotations(ctx context.Context, ig *ccev2.InstanceGroup, initStatus []expectedAnnotationsStatus, isSyncMeta bool) error {
	for _, expected := range initStatus {
		var actionType string
		if expected.Exist {
			actionType = "add"
		} else {
			actionType = "delete"
		}

		if err := c.doAnnotationsAction(ctx, ig, modifyAnnotationsAction{
			TargetType:  "instancegroup",
			ActionType:  actionType,
			Annotations: expected.Annotations,
		}, isSyncMeta); err != nil {
			logger.Infof(ctx, "failed to do init annotations action, err: %v", err)
			return err
		}
	}

	return nil
}

func (c *modifyInstanceGroupLabelsAndTaintsAndAnnotations) doAnnotationsAction(ctx context.Context, ig *ccev2.InstanceGroup, action modifyAnnotationsAction, isSyncMeta bool) error {
	switch action.TargetType {
	case "node":
		nodes, err := c.getNodesOfInstanceGroup(ctx, ig.Spec.CCEInstanceGroupID)
		if err != nil {
			logger.Errorf(ctx, "failed to get node in instancegroup, err: %v", err)
			return err
		}

		for _, node := range nodes {
			for key, value := range action.Annotations {
				switch action.ActionType {
				case "add":
					node.Annotations[key] = value
				case "delete":
					delete(node.Annotations, key)
				}
			}
			if _, err := c.base.K8SClient.CoreV1().Nodes().Update(ctx, &node, metav1.UpdateOptions{}); err != nil {
				logger.Errorf(ctx, "failed to update node, err: %v", err)
				return err
			}
		}
	case "instancegroup":
		for key, value := range action.Annotations {
			switch action.ActionType {
			case "add":
				if ig.Spec.InstanceTemplate.Annotations == nil {
					ig.Spec.InstanceTemplate.Annotations = map[string]string{}
				}
				ig.Spec.InstanceTemplate.Annotations[key] = value
			case "delete":
				delete(ig.Spec.InstanceTemplate.Annotations, key)
			}
		}

		if _, err := c.base.CCEHostClient.UpdateInstanceGroupConfig(ctx, c.base.ClusterID, ig.Spec.CCEInstanceGroupID, &ccev2.UpdateInstanceGroupRequest{
			PasswordNeedUpdate: false,
			InstanceGroupSpec: ccetypes.InstanceGroupSpec{
				InstanceGroupName: ig.Spec.InstanceGroupName,
				InstanceTemplates: []ccetypes.InstanceTemplate{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							MachineType:  ig.Spec.InstanceTemplate.MachineType,
							InstanceType: ig.Spec.InstanceTemplate.InstanceType,
							VPCConfig: ccetypes.VPCConfig{
								VPCSubnetID: ig.Spec.InstanceTemplate.VPCSubnetID,
							},
							ImageID:     ig.Spec.InstanceTemplate.ImageID,
							Labels:      ig.Spec.InstanceTemplate.Labels,
							Taints:      ig.Spec.InstanceTemplate.Taints,
							Annotations: ig.Spec.InstanceTemplate.Annotations,
						},
					},
				},
				Replicas: ig.Spec.Replicas,
				ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
					Enabled: ig.Spec.ClusterAutoscalerSpec.Enabled,
				},
				SyncMeta: isSyncMeta,
			},
		}, nil); err != nil {
			logger.Errorf(ctx, "failed to update instancegroup annotations, err: %v", err)
			return err
		}
	}

	return nil
}

func (c *modifyInstanceGroupLabelsAndTaintsAndAnnotations) checkAnnotationsResult(ctx context.Context, instanceGroupID string, annotationsStatus []expectedAnnotationsStatus,
	oldAnnotationsStatus []map[string]string, isSyncMeta bool) error {
	timer := time.NewTimer(130 * time.Second)
	defer timer.Stop()
	ticker := time.NewTicker(3 * time.Second)
	defer ticker.Stop()

	if isSyncMeta {
		logger.Infof(ctx, "instancegroup syncMeta is true, all nodes in %v should be synced", instanceGroupID)
		for {
			select {
			case <-timer.C:
				return errors.New("timeout(2m10s) waitting for label action result")
			case <-ticker.C:
				nodes, err := c.getNodesOfInstanceGroup(ctx, instanceGroupID)
				if err != nil {
					logger.Errorf(ctx, "failed to get node in instancegroup, err: %v", err)
					continue
				}
				ready := true
			LOOP:
				for _, node := range nodes {
					logger.Infof(ctx, "check node annotations, node[%s] contains annotations: %v", node.Name, node.Annotations)
					for _, expected := range annotationsStatus {
						for key, value := range expected.Annotations {
							v, found := node.Annotations[key]
							exist := found && v == value
							if exist != expected.Exist {
								logger.Infof(ctx, "check node annotations, node[%s] label[%s] check failed, expect: %v",
									node.Name, key, expected.Exist)
								ready = false
								break LOOP
							}
						}
					}
				}
				if !ready {
					logger.Infof(ctx, "node annotations is syncing, retry later")
					continue
				}
				return nil
			}
		}
	} else {
		logger.Infof(ctx, "instancegroup syncMeta is false, all nodes in %v are unsynced", instanceGroupID)
		nodes, err := c.getNodesOfInstanceGroup(ctx, instanceGroupID)
		if err != nil {
			logger.Errorf(ctx, "failed to get node in instancegroup, err: %v", err)
		}
		for i, node := range nodes {
			for key, value := range oldAnnotationsStatus[i] {
				v, found := node.Annotations[key]
				if found && v != value {
					err = fmt.Errorf("node labels have been changed, expect: %v, actual: %v", value, v)
					return err
				}
			}
		}
		return nil
	}
}

func (c *modifyInstanceGroupLabelsAndTaintsAndAnnotations) doTaintsAction(ctx context.Context, ig *ccev2.InstanceGroup, action modifyTaintsAction, isSyncMeta bool) error {
	switch action.TargetType {
	case "node":
		nodes, err := c.getNodesOfInstanceGroup(ctx, ig.Spec.CCEInstanceGroupID)
		if err != nil {
			logger.Errorf(ctx, "failed to get node in instancegroup, err: %v", err)
			return err
		}

		for _, node := range nodes {
			for _, taint := range action.Taints {
				exist := taintutil.TaintExists(node.Spec.Taints, &taint)
				switch {
				case action.ActionType == "add" && !exist:
					node.Spec.Taints = append(node.Spec.Taints, taint)
				case action.ActionType == "delete" && exist:
					newTaint, _ := taintutil.DeleteTaint(node.Spec.Taints, &taint)
					node.Spec.Taints = newTaint
				}
			}
			if _, err := c.base.K8SClient.CoreV1().Nodes().Update(ctx, &node, metav1.UpdateOptions{}); err != nil {
				logger.Errorf(ctx, "failed to update node, err: %v", err)
				return err
			}
		}
	case "instancegroup":
		for _, taint := range action.Taints {
			exist := taintutil.TaintExists(ig.Spec.InstanceTemplate.Taints, &taint)
			switch {
			case action.ActionType == "add" && !exist:
				ig.Spec.InstanceTemplate.Taints = append(ig.Spec.InstanceTemplate.Taints, taint)
			case action.ActionType == "delete" && exist:
				newTaint, _ := taintutil.DeleteTaint(ig.Spec.InstanceTemplate.Taints, &taint)
				ig.Spec.InstanceTemplate.Taints = newTaint
			}
		}
		if _, err := c.base.CCEHostClient.UpdateInstanceGroupConfig(ctx, c.base.ClusterID, ig.Spec.CCEInstanceGroupID, &ccev2.UpdateInstanceGroupRequest{
			PasswordNeedUpdate: false,
			InstanceGroupSpec: ccetypes.InstanceGroupSpec{
				InstanceGroupName: ig.Spec.InstanceGroupName,
				InstanceTemplates: []ccetypes.InstanceTemplate{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							MachineType:  ig.Spec.InstanceTemplate.MachineType,
							InstanceType: ig.Spec.InstanceTemplate.InstanceType,
							VPCConfig: ccetypes.VPCConfig{
								VPCSubnetID: ig.Spec.InstanceTemplate.VPCSubnetID,
							},
							ImageID:     ig.Spec.InstanceTemplate.ImageID,
							Labels:      ig.Spec.InstanceTemplate.Labels,
							Taints:      ig.Spec.InstanceTemplate.Taints,
							Annotations: ig.Spec.InstanceTemplate.Annotations,
						},
					},
				},
				Replicas: ig.Spec.Replicas,
				ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
					Enabled: ig.Spec.ClusterAutoscalerSpec.Enabled,
				},
				SyncMeta: isSyncMeta,
			},
		}, nil); err != nil {
			logger.Errorf(ctx, "failed to update instancegroup taints, err: %v", err)
			return err
		}
	}
	return nil
}

func (c *modifyInstanceGroupLabelsAndTaintsAndAnnotations) initTaints(ctx context.Context, ig *ccev2.InstanceGroup, initStatus []expectedTaintsStatus, isSyncMeta bool) error {
	for _, expected := range initStatus {
		var actionType string
		if expected.Exist {
			actionType = "add"
		} else {
			actionType = "delete"
		}

		if err := c.doTaintsAction(ctx, ig, modifyTaintsAction{
			TargetType: "instancegroup",
			ActionType: actionType,
			Taints:     expected.Taints,
		}, isSyncMeta); err != nil {
			logger.Infof(ctx, "failed to do init taints action, err: %v", err)
			return err
		}
	}

	return nil
}

func (c *modifyInstanceGroupLabelsAndTaintsAndAnnotations) checkTaintsResult(ctx context.Context, instanceGroupID string, taintsStatus []expectedTaintsStatus,
	oldTaintsStatus [][]corev1.Taint, isSyncMeta bool) error {
	timer := time.NewTimer(2 * time.Minute)
	defer timer.Stop()
	ticker := time.NewTicker(3 * time.Second)
	defer ticker.Stop()

	if isSyncMeta {
		logger.Infof(ctx, "instancegroup syncMeta is true, all nodes in %v should be synced", instanceGroupID)
		for {
			select {
			case <-timer.C:
				return errors.New("timeout waitting for taint action result")
			case <-ticker.C:
				nodes, err := c.getNodesOfInstanceGroup(ctx, instanceGroupID)
				if err != nil {
					logger.Errorf(ctx, "failed to get node in instancegroup, err: %v", err)
					continue
				}
				ready := true
			LOOP:
				for _, node := range nodes {
					for _, expected := range taintsStatus {
						for _, taint := range expected.Taints {
							exist := taintutil.TaintExists(node.Spec.Taints, &taint)
							if exist != expected.Exist {
								logger.Infof(ctx, "check node taints, node[%v] taint: %v, expected taint: %v, check failed, expect: %v",
									node.Name, node.Spec.Taints, taint, expected.Exist)
								ready = false
								break LOOP
							}
						}
					}
				}
				if !ready {
					logger.Infof(ctx, "node taints is syncing, retry later")
					continue
				}
				return nil
			}
		}
	} else {
		logger.Infof(ctx, "instancegroup syncMeta is false, all nodes in %v are unsynced", instanceGroupID)
		nodes, err := c.getNodesOfInstanceGroup(ctx, instanceGroupID)
		if err != nil {
			logger.Errorf(ctx, "failed to get node in instancegroup, err: %v", err)
		}
		for _, node := range nodes {
			for _, oldTaint := range oldTaintsStatus {
				for _, taint := range oldTaint {
					exist := taintutil.TaintExists(node.Spec.Taints, &taint)
					if !exist {
						return errors.New("node taints is different from old taints")
					}
				}
			}
		}
		return nil
	}
}

func (c *modifyInstanceGroupLabelsAndTaintsAndAnnotations) Clean(ctx context.Context) error {
	// 清理测试节点组
	_, err := c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, true, nil)
	if err != nil {
		logger.Errorf(ctx, "delete instanceGroup %s failed: %v", c.instanceGroupID, err.Error())
		return fmt.Errorf(err.Error())
	}
	return nil
}

func (c *modifyInstanceGroupLabelsAndTaintsAndAnnotations) Continue(ctx context.Context) bool {
	return false
}

func (c *modifyInstanceGroupLabelsAndTaintsAndAnnotations) ConfigFormat() string {
	str, _ := json.Marshal(c.config)
	return string(str)
}
