// 20241101, by s<PERSON><PERSON>@baidu,com, create

/*节点组自动扩缩容CA
IP不足不影响其他节点组自动扩容
todo 其他情况
*/

package instancegroup

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	CreateAutoScalerInstanceGroupWithLackingResources cases.CaseName = "CreateAutoScalerInstanceGroupWithLackingResources"
	testDeploymentName                                               = "test-deployment-lacking-resources"
)

type createAutoScalerIGWithLackingResources struct {
	base              *cases.BaseClient
	config            createAutoScalerIGWithLackingResourcesConfig
	instanceGroupsIDs []string
}

type createAutoScalerIGWithLackingResourcesConfig struct {
	CreateInstanceGroupConfig *ccetypes.InstanceGroupSpec `json:"createInstanceGroupConfig"`
	SubNetworksAndMaxReplicas []subNetworkAndMaxReplicas  `json:"subNetworkAndMaxReplicas"`
}

type subNetworkAndMaxReplicas struct {
	SubnetID    string `json:"subnetId"`
	SubnetName  string `json:"subnetName"`
	MaxReplicas int    `json:"maxReplicas"`
}

func init() {
	cases.AddCase(context.TODO(), CreateAutoScalerInstanceGroupWithLackingResources, NewCreateAutoScalerInstanceGroupWithLackingResources)
}

func NewCreateAutoScalerInstanceGroupWithLackingResources(ctx context.Context) cases.Interface {
	return &createAutoScalerIGWithLackingResources{}
}

func (c *createAutoScalerIGWithLackingResources) Name() cases.CaseName {
	return CreateAutoScalerInstanceGroupWithLackingResources
}

func (c *createAutoScalerIGWithLackingResources) Desc() string {
	return "在缺少资源下，创建开启自动扩缩容的节点组，不影响其他节点组自动扩容\n 1.IP不足; \n 2.待定todo"
}

func (c *createAutoScalerIGWithLackingResources) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	var createIGConfig createAutoScalerIGWithLackingResourcesConfig
	logger.Infof(ctx, "json raw config: %s", string(config))
	if err = json.Unmarshal(config, &createIGConfig); err != nil {
		return err
	}
	c.config = createIGConfig
	logger.Infof(ctx, "config: %s", utils.ToJSON(c.config))

	if c.config.SubNetworksAndMaxReplicas == nil || len(c.config.SubNetworksAndMaxReplicas) == 0 {
		err = errors.New("subNetworks is empty")
		return err
	}

	return nil
}

func (c *createAutoScalerIGWithLackingResources) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID

	workerNodes, listNodeErr := c.base.KubeClient.ListNode(ctx, &kube.ListOptions{
		LabelSelector: map[string]string{
			"cluster-role": "node",
		},
	})
	if listNodeErr != nil {
		err = fmt.Errorf("KubeClient.ListNode failed, %s", listNodeErr.Error())
		return
	}
	workerCount := len(workerNodes.Items)
	if workerCount < 3 {
		err = errors.New("this case needs at least 3 workers in cluster")
		return
	}

	// 检查集群是否开启自动扩缩容
	getASRes, err := c.base.CCEClient.GetAutoScalerConfig(ctx, clusterID, nil)
	if err != nil {
		return nil, fmt.Errorf("get autoscaler config failed: %v", err)
	}

	if getASRes == nil {
		err = errors.New("autoScalerConfigResp is nil ")
		return nil, err
	}

	if getASRes.Autoscaler == nil || getASRes.Autoscaler.CAConfig == nil {
		logger.Infof(ctx, "autoscaler config is nil, start to create autoscaler config")

		_, err = c.base.CCEClient.CreateAutoScalerConfig(ctx, clusterID, nil)
		if err != nil {
			err = fmt.Errorf("CCEClient.CreateAutoScalerConfig failed, %s", err.Error())
			return
		}
		// 重新获取一次
		getASRes, err = c.base.CCEClient.GetAutoScalerConfig(ctx, clusterID, nil)
		if err != nil {
			err = fmt.Errorf("CCEClient.GetAutoScalerConfig failed, %s", err.Error())
			return
		}
	}

	logger.Infof(ctx, "autoscaler config created")

	logger.Infof(ctx, "begin to create test deployment")
	err = c.applyDeployment(ctx, int32(workerCount))
	if err != nil {
		return
	}

	var igName string
	var instanceGroups []*common.InstanceGroup
	var totalMaxReplicas int

	for index, netWorkAndMaxReplicas := range c.config.SubNetworksAndMaxReplicas {
		totalMaxReplicas += netWorkAndMaxReplicas.MaxReplicas

		if netWorkAndMaxReplicas.SubnetName == "fewNetWorkIPs" {
			igName = fmt.Sprintf("test-ig-%s-%d", netWorkAndMaxReplicas.SubnetName, index)
		} else {
			igName = fmt.Sprintf("test-ig-%d", index)
		}

		igRequest := c.config.CreateInstanceGroupConfig
		igRequest.Replicas = 0
		igRequest.InstanceGroupName = igName
		igRequest.InstanceTemplate.VPCConfig.VPCSubnetID = netWorkAndMaxReplicas.SubnetID
		igRequest.ClusterAutoscalerSpec = &ccetypes.ClusterAutoscalerSpec{
			Enabled:              true,
			MinReplicas:          0,
			MaxReplicas:          netWorkAndMaxReplicas.MaxReplicas,
			ScalingGroupPriority: 100,
		}

		logger.Infof(ctx, "create instanceGroup %s, subnetName: %s", igName, netWorkAndMaxReplicas.SubnetName)
		createRes, createErr := c.base.CCEClient.CreateInstanceGroup(ctx, clusterID, &ccev2.CreateInstanceGroupRequest{
			InstanceGroupSpec: *c.config.CreateInstanceGroupConfig,
		}, nil)
		if createErr != nil {
			err = fmt.Errorf("CreateInstanceGroup %v with netWork %v failed, %s", igName, netWorkAndMaxReplicas.SubnetName, createErr.Error())
			return
		}

		c.instanceGroupsIDs = append(c.instanceGroupsIDs, createRes.InstanceGroupID)

		logger.Infof(ctx, "begin init instanceGroup %s, id is %v", igName, createRes.InstanceGroupID)
		instanceGroup, initErr := common.NewInstanceGroup(ctx, c.base, createRes.InstanceGroupID, time.Second*10, time.Minute*10)
		if initErr != nil {
			logger.Errorf(ctx, "failed to init instanceGroup client:%v", err)
			return nil, fmt.Errorf("failed to init instanceGroup client:%v", err)
		}
		instanceGroups = append(instanceGroups, instanceGroup)
	}

	// 扩到节点组们的上限，这样可以保证，节点组都会进行扩容
	err = c.scaleDeployment(ctx, int32(totalMaxReplicas))
	if err != nil {
		return
	}

	expectedErrWithLackingIPS := "InsufficientIPInSubnet"

	err = c.checkInstanceGroups(ctx, expectedErrWithLackingIPS, instanceGroups)
	if err != nil {
		return nil, fmt.Errorf("checkInstanceGroups failed, %s", err.Error())
	}

	return resources, nil
}

func (c *createAutoScalerIGWithLackingResources) Clean(ctx context.Context) error {
	for _, igID := range c.instanceGroupsIDs {
		_, err := c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, igID, true, nil)
		if err != nil {
			logger.Errorf(ctx, "delete instanceGroup %s failed: %v", igID, err.Error())
			return fmt.Errorf(err.Error())
		}
	}

	return nil
}

func (c *createAutoScalerIGWithLackingResources) Continue(ctx context.Context) bool {
	return true
}

func (c *createAutoScalerIGWithLackingResources) ConfigFormat() string { return "" }

func (c *createAutoScalerIGWithLackingResources) applyDeployment(ctx context.Context, replicas int32) (err error) {
	var terminationGracePeriodSeconds int64
	deployment := appsv1.Deployment{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Deployment",
			APIVersion: "apps/v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      testDeploymentName,
			Namespace: metav1.NamespaceDefault,
			Labels: map[string]string{
				"app": testDeploymentName,
			},
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": testDeploymentName,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": testDeploymentName,
					},
				},
				Spec: corev1.PodSpec{
					TerminationGracePeriodSeconds: &terminationGracePeriodSeconds,
					Affinity: &corev1.Affinity{
						PodAntiAffinity: &corev1.PodAntiAffinity{
							RequiredDuringSchedulingIgnoredDuringExecution: []corev1.PodAffinityTerm{{
								LabelSelector: &metav1.LabelSelector{
									MatchExpressions: []metav1.LabelSelectorRequirement{{
										Key:      "app",
										Operator: metav1.LabelSelectorOpIn,
										Values:   []string{testDeploymentName},
									}},
								},
								TopologyKey: "kubernetes.io/hostname", // 利用副本数和节点反亲和来强制触发扩容
							}},
						},
					},
					Containers: []corev1.Container{{
						Name:            testDeploymentName,
						Image:           "registry.baidubce.com/cce-public/busybox",
						ImagePullPolicy: corev1.PullIfNotPresent,
						Command:         []string{"sleep", "3600"},
					}},
				},
			},
		},
	}
	_, err = c.base.KubeClient.CreateDeploymentAppsV1(ctx, metav1.NamespaceDefault, &deployment)
	if err != nil {
		err = fmt.Errorf("CreateDeploymentAppsV1 failed, %s", err.Error())
		return
	}
	return
}

func (c *createAutoScalerIGWithLackingResources) scaleDeployment(ctx context.Context, increment int32) (err error) {
	kubeClient := c.base.KubeClient
	scale, getScaleErr := kubeClient.GetDeploymentScaleAppsV1(ctx, metav1.NamespaceDefault, testDeploymentName, &kube.GetOptions{})
	if getScaleErr != nil {
		err = fmt.Errorf("GetDeploymentScaleAppsV1 failed, %s", getScaleErr.Error())
		return
	}
	scale.Spec.Replicas = scale.Spec.Replicas + increment
	_, err = kubeClient.ScaleDeploymentAppsV1(ctx, corev1.NamespaceDefault, testDeploymentName, scale)
	if err != nil {
		err = fmt.Errorf("ScaleDeploymentAppsV1 to replicas %d failed, err: %v", scale.Spec.Replicas, err)
		return
	}
	return
}

func (c *createAutoScalerIGWithLackingResources) checkInstanceGroups(ctx context.Context, expectedErr string, instanceGroups []*common.InstanceGroup) (err error) {
	var wg sync.WaitGroup
	var errs []error
	errChan := make(chan error, len(instanceGroups))

	for _, instanceGroup := range instanceGroups {
		wg.Add(1)
		go func(ig *common.InstanceGroup) {
			defer wg.Done()
			err = c.isExpectedErr(ctx, expectedErr, ig)
			if err != nil {
				errChan <- err
			}
		}(instanceGroup)
	}

	go func() {
		wg.Wait()
		close(errChan)
	}()

	for err = range errChan {
		if err != nil {
			errs = append(errs, err)
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("errors occurred %v", c.combineErrs(errs))
	}

	return nil
}

func (c *createAutoScalerIGWithLackingResources) isExpectedErr(ctx context.Context, expectedErr string, instanceGroup *common.InstanceGroup) (err error) {
	instanceGroupID := instanceGroup.GetInstanceGroupID()
	logger.Infof(ctx, "instanceGroupID %v task info check begin", instanceGroupID)

	// 等待节点组开始扩容
	waitErr := common.WaitForFunc(ctx, func(ctx context.Context) error {
		listTaskRes, listTaskErr := c.base.CCEHostClient.ListTasks(ctx, ccetypes.TaskTypeInstanceGroupReplicas, ccev2.ListTaskOption{
			TargetID: instanceGroupID,
		}, nil)

		if listTaskErr != nil {
			err = fmt.Errorf("list task by instanceGroupID %s failed:%v", instanceGroupID, listTaskErr)
			return err
		}

		if listTaskRes.Page.TotalCount != 0 {
			logger.Infof(ctx, "instancegroup %s scaling replicas begin, tasks generated", instanceGroupID)
			return nil
		}

		return fmt.Errorf("instancegroup %s has none tasks", instanceGroupID)
	}, 5*time.Second, 6*time.Minute)

	if waitErr != nil {
		return fmt.Errorf("wait instancegroup %s scaling replicas failed, %v", instanceGroupID, waitErr)
	}

	// task是否处于完成态
	err = instanceGroup.AreTasksCompleted(ctx)
	if err != nil {
		return fmt.Errorf("check instancegroup %s tasks completed failed, %v", instanceGroupID, err)
	}

	// task完成还是终止
	listTaskRes, listTaskErr := c.base.CCEHostClient.ListTasks(ctx, ccetypes.TaskTypeInstanceGroupReplicas, ccev2.ListTaskOption{
		TargetID: instanceGroupID,
	}, nil)

	if listTaskErr != nil {
		err = fmt.Errorf("list task by instanceGroupID %s failed:%v", instanceGroupID, listTaskErr)
		return err
	}

	if listTaskRes.Page.Items[0].Phase == string(ccetypes.TaskPhaseAborted) {
		if strings.Contains(listTaskRes.Page.Items[0].ErrMessage, expectedErr) {
			logger.Infof(ctx, "instancegroup %v, task %v phase is aborted, error matches expected error: %v", instanceGroupID, listTaskRes.Page.Items[0].ID, expectedErr)
			return nil
		}

		return fmt.Errorf("instancegroup %v, task %v phase is aborted, unexpected error: %v", instanceGroupID, listTaskRes.Page.Items[0].ID, listTaskRes.Page.Items[0].ErrMessage)
	}

	return nil
}

func (c *createAutoScalerIGWithLackingResources) combineErrs(errors []error) string {
	var errMess string

	if len(errors) == 0 {
		return errMess
	}

	for _, err := range errors {
		errMess += err.Error() + "\n"
	}

	return errMess
}
