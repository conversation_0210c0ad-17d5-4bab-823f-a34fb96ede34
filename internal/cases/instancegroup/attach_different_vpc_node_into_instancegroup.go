// 20241029, by s<PERSON><PERSON>@baidu,com, create

/*创建节点组，不同的VPC节点
将此节点移入节点组
报错信息正确返回
*/

package instancegroup

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	bcctype "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	AttachDifferentVPCNodeIntoIGroup cases.CaseName = "AttachDifferentVPCNodeIntoIGroup"
)

type attachDifferentVPCNodeIntoIGroup struct {
	base            *cases.BaseClient
	instanceGroupID string
	instanceID      string
	config          attachDifferentVPCNodeIntoIGroupConfig
}

type attachDifferentVPCNodeIntoIGroupConfig struct {
	CreateInstanceGroupConfig *ccetypes.InstanceGroupSpec
	CreateInstanceConfig      *bccapi.CreateInstanceArgs `json:"createInstanceConfig"`
}

func init() {
	cases.AddCase(context.TODO(), AttachDifferentVPCNodeIntoIGroup, NewAttachDifferentVPCNodeIntoIGroup)
}

func NewAttachDifferentVPCNodeIntoIGroup(ctx context.Context) cases.Interface {
	return &attachDifferentVPCNodeIntoIGroup{}
}

func (c *attachDifferentVPCNodeIntoIGroup) Name() cases.CaseName {
	return AttachDifferentVPCNodeIntoIGroup
}

func (c *attachDifferentVPCNodeIntoIGroup) Desc() string {
	return "移入已有节点至节点组（不同vpc）"
}

func (c *attachDifferentVPCNodeIntoIGroup) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	var moveConfig attachDifferentVPCNodeIntoIGroupConfig
	logger.Infof(ctx, "json raw config: %s", string(config))
	if err := json.Unmarshal(config, &moveConfig); err != nil {
		return err
	}
	c.config = moveConfig
	logger.Infof(ctx, "config: %s", utils.ToJSON(c.config))
	return nil
}

func (c *attachDifferentVPCNodeIntoIGroup) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterName := c.base.ClusterID

	// 创建一个bcc节点 ////默认私有网络 vpc-rdzq6atrvvgg

	// creInsRes, err := c.base.BCCClient.CreateInstance(ctx, &bccapi.CreateInstanceArgs{
	//	ImageId:             "m-e9dDUxlp",
	//	Billing:             bccapi.Billing{PaymentTiming: bccapi.PaymentTimingPostPaid},
	//	InstanceType:        bccapi.InstanceTypeN5,
	//	ZoneName:            "cn-gz-c",
	//	CpuCount:            2,
	//	MemoryCapacityInGB:  8,
	//	RootDiskSizeInGb:    100,
	//	RootDiskStorageType: bccapi.StorageTypeSSD,
	//	PurchaseCount:       1,
	//	SubnetId:            "sbn-xeb3yrmh2ztj",
	//	SecurityGroupId:     "g-v1v8w1uqy56c",
	//	Name:                "sz-test-instance-diffferent-vpc",
	// }, nil)

	creInsRes, err := c.base.BCCClient.CreateInstance(ctx, c.config.CreateInstanceConfig, nil)

	if err != nil {
		return nil, fmt.Errorf("create bcc instance failed: %v", err)
	}

	if len(creInsRes.InstanceIds) == 0 {
		return nil, errors.New("nil instances create")
	}

	c.instanceID = creInsRes.InstanceIds[0]

	waitErr := resource.WaitForFunc(ctx, func(ctx context.Context) error {
		desInsRes, getErr := c.base.BCCClient.DescribeInstance(ctx, c.instanceID, nil)

		if getErr != nil {
			return fmt.Errorf(" get instance failed: %v", getErr)
		}

		if desInsRes.Status == bcctype.InstanceStatusRunning {
			logger.Infof(ctx, "instance %v is running", c.instanceID)
			return nil
		}

		return errors.New("instance is creating, waiting 5 seconds")
	}, 5*time.Second, 5*time.Minute)

	if waitErr != nil {
		return nil, fmt.Errorf(" instance running failed: %v", waitErr)
	}

	// 创建节点组
	// 节点组VPC子网是 sbn-5zemj8e472sj，属于 vpc-for-mock(vpc-dwk84jh6e3zi)
	logger.Infof(ctx, "create different-VPC-group ")
	c.instanceGroupID, err = c.CreateAndCheckIG(ctx, clusterName)
	if err != nil {
		return nil, fmt.Errorf("create and check instancegroup failed: %v", err)
	}

	existedInstanceConfig := []*ccev2.InstanceSet{
		{
			InstanceSpec: ccetypes.InstanceSpec{
				AdminPassword: c.config.CreateInstanceGroupConfig.InstanceTemplate.AdminPassword,
				Existed:       true,
				ExistedOption: ccetypes.ExistedOption{
					ExistedInstanceID: c.instanceID,
					Rebuild:           func(rebuild bool) *bool { return &rebuild }(false),
				},
				MachineType: ccetypes.MachineTypeBCC,
				ClusterRole: ccetypes.ClusterRoleNode,
			},
			Count: 1,
		},
	}

	_, err = c.base.CCEHostClient.AttachInstancesToInstanceGroup(ctx, clusterName, c.instanceGroupID, &ccev2.AttachInstancesToInstanceGroupRequest{
		Incluster:              false,
		UseInstanceGroupConfig: true,
		ExistedInstances:       existedInstanceConfig,
	}, nil)

	expectedErrMessage := "different VPC---to be fixed by RD "

	if err != nil {
		if strings.Contains(err.Error(), expectedErrMessage) {
			logger.Infof(ctx, "attach different VPC instance %v into instancegroup %v expected err message! ", c.instanceID, c.instanceGroupID)
			return nil, nil
		}
		return nil, fmt.Errorf("attach different VPC instance into instancegroup unexpected error:%v", err)
	}
	return nil, errors.New(" attach different VPC instance into instancegroup illegal ")
}

func (c *attachDifferentVPCNodeIntoIGroup) Clean(ctx context.Context) error {
	// 清理节点组
	_, err := c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, true, nil)
	if err != nil {
		return fmt.Errorf("delete instance group %v failed:%v", c.instanceGroupID, err)
	}

	// 清理BCC节点
	err = c.base.BCCClient.DeleteInstance(ctx, c.instanceID, nil)
	if err != nil {
		return fmt.Errorf("delete instance %v failed:%v", c.instanceID, err)
	}

	return nil
}

func (c *attachDifferentVPCNodeIntoIGroup) Continue(ctx context.Context) bool {
	return true
}

func (c *attachDifferentVPCNodeIntoIGroup) ConfigFormat() string { return "" }

func (c *attachDifferentVPCNodeIntoIGroup) CreateAndCheckIG(ctx context.Context, clusterName string) (instanceGroupID string, err error) {
	createNewInstanceGroup, createErr := c.base.CCEClient.CreateInstanceGroup(ctx, clusterName, &ccev2.CreateInstanceGroupRequest{
		InstanceGroupSpec: *c.config.CreateInstanceGroupConfig,
	}, nil)

	if createErr != nil {
		err = fmt.Errorf("instancegroup create falied : %V", createErr)
		return
	}

	instanceGroupID = createNewInstanceGroup.InstanceGroupID

	instanceGroup, err := common.NewInstanceGroup(ctx, c.base, instanceGroupID, 10*time.Second, 6*time.Minute)
	if err != nil {
		err = fmt.Errorf("failed to init instancegroup client:%v", err)
		return
	}

	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		err = fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
		return
	}

	return instanceGroupID, nil
}
