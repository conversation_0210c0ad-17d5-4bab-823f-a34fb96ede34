// 20241018, by s<PERSON><PERSON>@baidu,com, create

/*创建节点组
指定不存在的节点移除节点组
报错信息正确返回
*/

package instancegroup

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	ShrinkNonexistentNode cases.CaseName = "ShrinkNonexistentNode"
)

type shrinkNonexistent struct {
	base            *cases.BaseClient
	instanceGroupID string
	config          shrinkNonexistentConfig
}

type shrinkNonexistentConfig struct {
	CreateInstanceGroupConfig *ccetypes.InstanceGroupSpec
}

func init() {
	cases.AddCase(context.TODO(), ShrinkNonexistentNode, NewShrinkNonexistentNode)
}

func NewShrinkNonexistentNode(ctx context.Context) cases.Interface { return &shrinkNonexistent{} }

func (c *shrinkNonexistent) Name() cases.CaseName { return ShrinkNonexistentNode }

func (c *shrinkNonexistent) Desc() string { return "指定不存在的节点缩容" }

func (c *shrinkNonexistent) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	var shrinkConfig shrinkNonexistentConfig
	logger.Infof(ctx, "json raw config: %s", string(config))
	if err := json.Unmarshal(config, &shrinkConfig); err != nil {
		return err
	}
	c.config = shrinkConfig
	logger.Infof(ctx, "config: %s", utils.ToJSON(c.config))
	return nil
}

func (c *shrinkNonexistent) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterName := c.base.ClusterID

	// 创建节点组
	creatNewInstanceGroup, creatErr := c.base.CCEClient.CreateInstanceGroup(ctx, clusterName, &ccev2.CreateInstanceGroupRequest{
		InstanceGroupSpec: *c.config.CreateInstanceGroupConfig,
	}, nil)

	if creatErr != nil {
		err = fmt.Errorf("instance group create falied : %V", creatErr)
		return
	}

	instanceGroupID := creatNewInstanceGroup.InstanceGroupID
	c.instanceGroupID = instanceGroupID

	instanceGroup, err := common.NewInstanceGroup(ctx, c.base, instanceGroupID, time.Second*10, time.Minute*6)
	if err != nil {
		logger.Errorf(ctx, "failed to init instanceGroup client:%v", err)
		return nil, fmt.Errorf("failed to init instanceGroup client:%v", err)
	}

	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		logger.Errorf(ctx, "wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
		return nil, fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
	}

	instanceIDs, err := instanceGroup.GetInstanceListByInstanceGroupID(ctx)
	if err != nil {
		logger.Errorf(ctx, "get instanceID list by instanceGroup failed:%v", err)
		return nil, fmt.Errorf("get instanceID list by instanceGroup failed:%v", err)
	}

	var nonexistentInstanceIDs = make([]string, 0)
	for _, instanceID := range instanceIDs {
		nonexistentInstanceID := instanceID + "xsxs"
		nonexistentInstanceIDs = append(nonexistentInstanceIDs, nonexistentInstanceID)
	}

	logger.Infof(ctx, " nonexistent instance ids: %v", nonexistentInstanceIDs)

	_, creSIGErr := c.base.CCEClient.CreateScaleDownInstanceGroupByCleanPolicy(ctx, clusterName, instanceGroupID, nonexistentInstanceIDs,
		ccev2.DeleteCleanPolicy, &ccetypes.DeleteOption{
			DrainNode:         false,
			MoveOut:           true,
			DeleteResource:    true,
			DeleteCDSSnapshot: true,
		}, nil)

	if creSIGErr != nil {
		if strings.Contains(creSIGErr.Error(), "Instances not found") {
			logger.Infof(ctx, "scale down nonexistent instance")
			return nil, nil
		}
		return nil, fmt.Errorf(" ScaleDownInstanceGroup failed:%v", creSIGErr)
	}
	return nil, errors.New(" ScaleDown nonexistent Instance illegal ")
}

func (c *shrinkNonexistent) Clean(ctx context.Context) error {
	_, err := c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, true, nil)
	if err != nil {
		logger.Errorf(ctx, "delete instance group failed:%v", err)
		return fmt.Errorf("delete instance group failed:%v", err)
	}

	return nil
}

func (c *shrinkNonexistent) Continue(ctx context.Context) bool {
	return true
}

func (c *shrinkNonexistent) ConfigFormat() string { return "" }
