// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2022/02/10 , by <PERSON><PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
move instance in instanceGroup with del policy
移出集群，保留实例时，移除的节点在节点组和集群节点中均不存在，但虚机并未释放
移出集群，不保留实例时，移除的节点在节点组和集群节点中均不存在，虚机释放
*/

package instancegroup

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	MoveInstanceGroupWithInstanceDeleted cases.CaseName = "MoveInstanceGroupWithInstanceDeleted"
)

type moveInstanceGroupWithInstanceDeletedConfig struct {
	CreateInstanceGroupConfig *ccetypes.InstanceGroupSpec `json:"createInstanceGroupConfig"`
	InstanceGroupID           string                      `json:"instanceGroupID"`
	ReleaseInstances          bool                        `json:"releaseInstances"`
}

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), MoveInstanceGroupWithInstanceDeleted, NewMoveInstanceGroupWithInstanceDeleted)
}

var _ cases.Interface = &moveInstanceGroupWithInstanceDeleted{}

type moveInstanceGroupWithInstanceDeleted struct {
	base            *cases.BaseClient
	instanceGroupID string
	config          moveInstanceGroupWithInstanceDeletedConfig
	resources       []*cases.Resource
}

// NewMoveInstanceGroupWithInstanceDeleted - 删除节点组时，删除节点组和节点中实例，保留虚机实例
func NewMoveInstanceGroupWithInstanceDeleted(ctx context.Context) cases.Interface {
	return &moveInstanceGroupWithInstanceDeleted{}
}

func (c *moveInstanceGroupWithInstanceDeleted) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	logger.WithValues("case", "deleteInstanceGroup").Infof(ctx, "json raw config: %s", string(config))
	if err := json.Unmarshal(config, &c.config); err != nil {
		return err
	}
	logger.WithValues("case", "deleteInstanceGroup").Infof(ctx, "config: %s", utils.ToJSON(c.config))

	c.base = base
	c.resources = nil

	return nil
}

func (c *moveInstanceGroupWithInstanceDeleted) Name() cases.CaseName {
	return MoveInstanceGroupWithInstanceDeleted
}

func (c *moveInstanceGroupWithInstanceDeleted) Desc() string {
	return "[待补充]"
}

func (c *moveInstanceGroupWithInstanceDeleted) Check(ctx context.Context) ([]cases.Resource, error) {
	var resources []cases.Resource
	// 创建节点组
	resp, err := c.base.CCEClient.CreateInstanceGroup(ctx, c.base.ClusterID, &ccev2.CreateInstanceGroupRequest{
		InstanceGroupSpec: *c.config.CreateInstanceGroupConfig,
	}, nil)
	if err != nil {
		logger.Errorf(ctx, "failed to create instanceGroup, err: %v", err)
		return nil, err
	}
	instanceGroupID := resp.InstanceGroupID
	c.instanceGroupID = instanceGroupID

	instanceGroup, err := common.NewInstanceGroup(ctx, c.base, instanceGroupID, time.Second*10, time.Minute*6)
	if err != nil {
		logger.Errorf(ctx, "failed to init instanceGroup client:%v", err)
		return nil, fmt.Errorf("failed to init instanceGroup client:%v", err)
	}

	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		logger.Errorf(ctx, "wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
		return nil, fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
	}

	// 获取节点组节点ID列表
	instanceIDs, err := instanceGroup.GetInstanceListByInstanceGroupID(ctx)
	if err != nil {
		logger.Errorf(ctx, "get instanceID list by instanceGroup failed:%v", err)
		return nil, fmt.Errorf("get instanceID list by instanceGroup failed:%v", err)
	}

	// 获取节点组machine ID列表
	machineIDs, err := instanceGroup.GetMachineListByInstanceGroupID(ctx)
	if err != nil {
		logger.Errorf(ctx, "get machineID list by instanceGroup failed:%v", err)
		return nil, fmt.Errorf("get machineID list by instanceGroup failed:%v", err)
	}

	// 获取移出目标节点
	taskInstance := instanceIDs
	taskMachineID := machineIDs

	time.Sleep(10 * time.Second)

	// 1.节点组移除instance,节点不保留在集群中
	_, err = c.base.CCEHostClient.CreateScaleDownInstanceGroupByCleanPolicy(ctx, c.base.ClusterID,
		instanceGroupID, taskInstance, ccev2.DeleteCleanPolicy, &ccetypes.DeleteOption{
			MoveOut:           !c.config.ReleaseInstances,
			DeleteResource:    c.config.ReleaseInstances,
			DeleteCDSSnapshot: c.config.ReleaseInstances,
		}, nil)
	if err != nil {
		logger.Errorf(ctx, "failed to create scale down task, err: %v", err)
		return nil, fmt.Errorf("failed to create scale down task, err: %v", err)
	}

	// 检查节点组是否移除节点
	err = instanceGroup.CheckInstanceNotExistedInInstanceGroup(ctx, taskInstance)
	if err != nil {
		logger.Errorf(ctx, "check instance not existed failed:%v", err)
		return nil, err
	}

	// 检查节点列表实例不存在
	err = instanceGroup.CheckInstanceNotExistedInCluster(ctx, taskInstance)
	if err != nil {
		logger.Errorf(ctx, "check instances in cluster %s failed:%v", c.base.ClusterID, err)
		return nil, fmt.Errorf("check instance in cluster %s failed:%v", c.base.ClusterID, err)
	}

	// 如果不释放资源，则不删除虚机,检查对应虚机是否还存在
	if !c.config.ReleaseInstances {
		for _, id := range taskMachineID {
			res, err := c.base.BCCClient.DescribeInstance(ctx, id, nil)
			if err != nil {
				logger.Errorf(ctx, "get instance by id failed:%v ", err)
				return nil, fmt.Errorf("get instance by id failed:%v ", err)
			}
			if res == nil {
				logger.Errorf(ctx, "the instance is not existed")
				return nil, errors.New("the instance is not existed")
			}
		}
	} else {
		// 如果释放资源，删除虚机
		for _, id := range taskMachineID {
			res, err := c.base.BCCClient.DescribeInstance(ctx, id, nil)
			if err == nil {
				logger.Errorf(ctx, "the instance is still existed")
				return nil, errors.New("the instance is still existed")
			}
			if res != nil {
				logger.Errorf(ctx, "the instance is still existed")
				return nil, errors.New("the instance is still existed")
			}
		}
	}

	err = instanceGroup.AreTasksCompleted(ctx)
	if err != nil {
		logger.Errorf(ctx, "check instance group %v tasks completed failed:%v", instanceGroupID, err)
		return nil, fmt.Errorf("check instance group %v tasks completed failed:%v", instanceGroupID, err)
	}

	return resources, nil
}

func (c *moveInstanceGroupWithInstanceDeleted) Clean(ctx context.Context) error {
	// 清理测试节点组
	_, err := c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, true, nil)
	if err != nil {
		logger.Errorf(ctx, "delete instanceGroup %s failed: %v", c.instanceGroupID, err.Error())
		return fmt.Errorf(err.Error())
	}
	return nil
}

func (c *moveInstanceGroupWithInstanceDeleted) Continue(ctx context.Context) bool {
	return true
}

func (c *moveInstanceGroupWithInstanceDeleted) ConfigFormat() string {
	return ""
}
