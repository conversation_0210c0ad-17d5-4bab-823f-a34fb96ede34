/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  scale_instancegroup
 * @Version: 1.0.0
 * @Date: 2020/8/7 2:50 下午
 */
package instancegroup

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// ScaleInstanceGroup - 扩容节点组 Case 名字
	ScaleInstanceGroup cases.CaseName = "ScaleInstanceGroup"
)

func init() {
	cases.AddCase(context.TODO(), ScaleInstanceGroup, NewScaleInstanceGroup)
}

func NewScaleInstanceGroup(ctx context.Context) cases.Interface {
	return &scaleInstanceGroup{}
}

type scaleInstanceGroup struct {
	base            *cases.BaseClient
	instanceGroupID string
	config          scaleInstanceGroupConfig
}

type scaleInstanceGroupConfigReplicasItem struct {
	Replicas            int                    `json:"replicas"`
	ExistedInstanceSpec *ccetypes.InstanceSpec `json:"existedInstanceSpec"`
	UpdateInstanceGroup bool                   `json:"updateInstanceGroup"`
}

type scaleInstanceGroupConfig struct {
	CreateInstanceGroupConfig *ccetypes.InstanceGroupSpec            `json:"createInstanceGroupConfig"`
	ToReplicas                []scaleInstanceGroupConfigReplicasItem `json:"toReplicas"`
	SoldOutConfig             soldOutConfig                          `json:"soldOutConfig"`
}

type soldOutConfig struct {
	MachineType  ccetypes.MachineType      `json:"machineType"`
	InstanceType bcc.InstanceType          `json:"instanceType"`
	Resource     ccetypes.InstanceResource `json:"InstanceResource"`
}

func (c *scaleInstanceGroup) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("nil baseClient")
	}

	logger.WithValues("case", "scaleInstanceGroup").Infof(ctx, "json raw config: %s", string(config))
	if err := json.Unmarshal(config, &c.config); err != nil {
		return err
	}
	logger.WithValues("case", "scaleInstanceGroup").Infof(ctx, "config: %s", utils.ToJSON(c.config))

	c.base = base
	return nil
}

func (c *scaleInstanceGroup) Name() cases.CaseName {
	return ScaleInstanceGroup
}

func (c *scaleInstanceGroup) Desc() string {
	return "扩容节点组"
}

func (c *scaleInstanceGroup) Check(ctx context.Context) ([]cases.Resource, error) {
	logger := logger.WithValues("case", "scaleInstanceGroup")

	createInstanceGroupClient := &createInstanceGroup{
		base: c.base,
		config: createInstanceGroupConfig{
			Request: ccev2.CreateInstanceGroupRequest{
				InstanceGroupSpec: *c.config.CreateInstanceGroupConfig,
			},
			NeedClean: false,
		},
	}
	logger.WithValues("case", "scaleInstanceGroup").Infof(ctx, "create instancegroup request: %s", utils.ToJSON(createInstanceGroupClient.config.Request))

	resources, err := createInstanceGroupClient.Check(ctx)
	if err != nil {
		logger.Errorf(ctx, "failed to create instanceGroup, err: %v", err)
		return resources, nil
	}

	if len(resources) == 0 {
		logger.Errorf(ctx, "no resource return by createInstanceGroupClient")
		return resources, errors.New("no resource return by createInstanceGroupClient")
	}

	var instanceGroupID string
	for _, r := range resources {
		if r.Type == cases.ResourceTypeCCEInstanceGroup {
			instanceGroupID = r.ID
			c.instanceGroupID = instanceGroupID
			break
		}
	}

	if instanceGroupID == "" {
		return resources, errors.New("empty instanceGroupID")
	}

	for _, toReplicas := range c.config.ToReplicas {
		logger.WithValues("toReplicas", toReplicas).Infof(ctx, "start scaling case")
		igResp, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, instanceGroupID, nil)
		if err != nil {
			logger.WithValues("instanceGroupID", instanceGroupID).Errorf(ctx, "failed to get instanceGroup, err: %v", err)
			return resources, err
		}

		if igResp.InstanceGroup.Spec.Replicas >= toReplicas.Replicas {
			logger.WithValues("now replicas", igResp.InstanceGroup.Spec.Replicas).
				WithValues("to replicas", toReplicas.Replicas).
				Errorf(ctx, "scale instanceGroup config error")
			return resources, err
		}

		var instanceIDs []string
		if toReplicas.ExistedInstanceSpec != nil {
			resp, err := c.base.CCEClient.CreateInstances(ctx, c.base.ClusterID, []*ccev2.InstanceSet{
				{
					InstanceSpec: *toReplicas.ExistedInstanceSpec,
					Count:        toReplicas.Replicas - igResp.InstanceGroup.Spec.Replicas,
				},
			}, nil)
			if err != nil {
				logger.WithValues("clusterID", c.base.ClusterID).
					WithValues("resp", resp).
					Errorf(ctx, "failed to create instances, err: %v", err)
				return resources, err
			}
			instanceIDs = resp.CCEInstanceIDs

			err = func() error {
				ticker := time.NewTicker(5 * time.Second)
				defer ticker.Stop()
				timer := time.NewTimer(20 * time.Minute)
				defer timer.Stop()

			Retry:
				for {
					select {
					case <-ticker.C:
						for _, instanceID := range instanceIDs {
							instanceResp, err := c.base.CCEClient.GetInstance(ctx, c.base.ClusterID, instanceID, nil)
							if err != nil {
								logger.WithValues("instanceID", instanceID).
									WithValues("resp", instanceResp).
									Errorf(ctx, "get instance failed, err: %v", err)
								continue Retry
							}
							if instanceResp.Instance.Status.InstancePhase != ccetypes.InstancePhaseRunning {
								logger.WithValues("instanceID", instanceID).
									WithValues("phase", instanceResp.Instance.Status.InstancePhase).
									Infof(ctx, "instance is still not running")
								continue Retry
							}
						}
						return nil
					case <-timer.C:
						logger.Errorf(ctx, "timeout waiting instance creating")
						return errors.New("timeout waiting instance creating")
					}
				}
			}()
			if err != nil {
				return resources, err
			}
		}

		request := ccev2.UpdateInstanceGroupReplicasRequest{
			Replicas:    toReplicas.Replicas,
			InstanceIDs: instanceIDs,
		}
		updateResp, err := c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, c.base.ClusterID, instanceGroupID, &request, nil)
		if err != nil {
			logger.WithValues("instanceGroupID", instanceGroupID).
				WithValues("request", request).
				WithValues("resp", updateResp).
				Errorf(ctx, "failed to update instanceGroup replicas, err: %v", err)
			return resources, err
		}

		// 扩容过程中，更新节点组配置
		if toReplicas.UpdateInstanceGroup {
			time.Sleep(2 * time.Second)
			logger.Infof(ctx, "start to update instanceGroup config when scaling")

			// 更新节点组主备机型配置
			request := ccev2.UpdateInstanceGroupRequest{
				PasswordNeedUpdate: false,
				InstanceGroupSpec: ccetypes.InstanceGroupSpec{
					ClusterID:          c.base.ClusterID,
					CCEInstanceGroupID: instanceGroupID, // InstanceGroupID?
					InstanceGroupName:  c.config.CreateInstanceGroupConfig.InstanceGroupName,
					Replicas:           toReplicas.Replicas,
					CleanPolicy:        ccetypes.CleanPolicy(igResp.InstanceGroup.Spec.CleanPolicy),
					ShrinkPolicy:       ccetypes.ShrinkPolicy(igResp.InstanceGroup.Spec.ShrinkPolicy),
					ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
						Enabled:              igResp.InstanceGroup.Spec.ClusterAutoscalerSpec.Enabled,
						MinReplicas:          igResp.InstanceGroup.Spec.ClusterAutoscalerSpec.MinReplicas,
						MaxReplicas:          igResp.InstanceGroup.Spec.ClusterAutoscalerSpec.MaxReplicas,
						ScalingGroupPriority: igResp.InstanceGroup.Spec.ClusterAutoscalerSpec.ScalingGroupPriority,
					},
					InstanceTemplates: []ccetypes.InstanceTemplate{},
				},
			}
			mainTemplate := igResp.InstanceGroup.Spec.InstanceTemplate
			// 修改操作系统
			request.InstanceTemplates = append(request.InstanceTemplates, ccetypes.InstanceTemplate{
				InstanceSpec: ccetypes.InstanceSpec{
					InstanceTemplateID: mainTemplate.InstanceTemplateID,
					MachineType:        mainTemplate.MachineType,
					InstanceType:       mainTemplate.InstanceType,
					BBCOption:          *mainTemplate.BBCOption,
					// BECOption: *mainTemplate.BECOption,
					VPCConfig: ccetypes.VPCConfig{
						VPCSubnetID:       mainTemplate.VPCSubnetID,
						SecurityGroupType: ccetypes.SecurityGroupType(mainTemplate.SecurityGroupType),
						SecurityGroup: ccetypes.SecurityGroup{
							EnableCCERequiredSecurityGroup: mainTemplate.SecurityGroup.EnableCCERequiredSecurityGroup,
							EnableCCEOptionalSecurityGroup: mainTemplate.SecurityGroup.EnableCCEOptionalSecurityGroup,
							CustomSecurityGroupIDs:         mainTemplate.SecurityGroup.CustomSecurityGroupIDs,
						},
					},
					InstanceResource:   mainTemplate.InstanceResource,
					DeployCustomConfig: mainTemplate.DeployCustomConfig,
					InstanceOS: ccetypes.InstanceOS{
						ImageType: "System",
						ImageName: "8.0 x86_64 (64bit)",
						OSType:    "linux",
						OSName:    "CentOS",
						OSVersion: "8.0",
						OSArch:    "x86_64 (64bit)",
					},
					SSHKeyID:                  mainTemplate.SSHKeyID,
					InstanceChargingType:      mainTemplate.InstanceChargingType,
					InstancePreChargingOption: mainTemplate.InstancePreChargingOption,
					DeleteOption:              mainTemplate.DeleteOption,
					Tags:                      mainTemplate.Tags,
					Labels:                    mainTemplate.Labels,
					Annotations:               mainTemplate.Annotations,
					Taints:                    mainTemplate.Taints,
					Bid:                       mainTemplate.Bid,
					BidOption:                 mainTemplate.BidOption,
					RelationTag:               mainTemplate.RelationTag,
					DeploySetID:               mainTemplate.DeploySetID,
					AutoSnapshotID:            mainTemplate.AutoSnapshotID,
				},
			})
			updateConfigResp, err := c.base.CCEClient.UpdateInstanceGroupConfig(ctx, c.base.ClusterID, instanceGroupID, &request, nil)
			if err != nil {
				logger.WithValues("instanceGroupID", instanceGroupID).
					WithValues("request", request).
					WithValues("resp", updateConfigResp).
					Errorf(ctx, "failed to update instanceGroup config, err: %v", err)
				return resources, err
			}
		}

		err = func() error {
			ticker := time.NewTicker(5 * time.Second)
			defer ticker.Stop()
			timer := time.NewTimer(20 * time.Minute)
			defer timer.Stop()

		Retry:
			for {
				select {
				case <-ticker.C:
					igResp, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, instanceGroupID, nil)
					if err != nil {
						logger.WithValues("instanceGroupID", instanceGroupID).Errorf(ctx, "failed to get instanceGroup, err: %v", err)
						continue
					}

					if igResp.InstanceGroup.Spec.Replicas != toReplicas.Replicas {
						logger.WithValues("instanceGroupID", instanceGroupID).
							Errorf(ctx, "spec replicas should be %d not %d", toReplicas.Replicas, igResp.InstanceGroup.Spec.Replicas)
						return fmt.Errorf("spec replicas should be %d not %d", toReplicas.Replicas, igResp.InstanceGroup.Spec.Replicas)
					}

					if igResp.InstanceGroup.Spec.Replicas != igResp.InstanceGroup.Status.ReadyReplicas {
						logger.WithValues("instanceGroupID", instanceGroupID).Infof(ctx, "still scaling")
						continue
					}

					for _, instanceID := range instanceIDs {
						instanceResp, err := c.base.CCEClient.GetInstance(ctx, c.base.ClusterID, instanceID, nil)
						if err != nil {
							logger.WithValues("instanceID", instanceID).
								WithValues("resp", instanceResp).
								Errorf(ctx, "get instance failed, err: %v", err)
							continue Retry
						}

						if instanceResp.Instance.Spec.InstanceGroupID != instanceGroupID {
							logger.WithValues("instanceGroupID", instanceGroupID).
								WithValues("instanceID", instanceID).
								Errorf(ctx, "instance should be in instanceGroup")
							return errors.New("instance should be in instanceGroup")
						}

						// TODO: check instance status
					}
					return nil
				case <-timer.C:
					logger.Errorf(ctx, "timeout waiting instanceGroup scaling")
					return errors.New("timeout waiting instanceGroup scaling")
				}
			}
		}()
		if err != nil {
			return resources, err
		}
	}

	// 节点组主模版更换售罄机型，备选机型不变，并扩容
	logger.Infof(ctx, "change main template machine to sold-out type")
	ig, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, instanceGroupID, nil)
	if err != nil || ig == nil {
		return resources, fmt.Errorf("get instance group %v failed, err: %v", instanceGroupID, err)
	}
	igSpec := ig.InstanceGroup.Spec

	_, err = c.base.CCEHostClient.UpdateInstanceGroupConfig(ctx, c.base.ClusterID, instanceGroupID, &ccev2.UpdateInstanceGroupRequest{
		PasswordNeedUpdate: false,
		InstanceGroupSpec: ccetypes.InstanceGroupSpec{
			InstanceGroupName: c.config.CreateInstanceGroupConfig.InstanceGroupName,
			Replicas:          igSpec.Replicas + 1,
			CleanPolicy:       ccetypes.CleanPolicy(igSpec.CleanPolicy),
			ShrinkPolicy:      ccetypes.ShrinkPolicy(igSpec.ShrinkPolicy),
			InstanceTemplates: []ccetypes.InstanceTemplate{
				{
					InstanceSpec: ccetypes.InstanceSpec{
						// MachineType 、InstanceType 修改
						MachineType:  c.config.SoldOutConfig.MachineType,
						InstanceType: c.config.SoldOutConfig.InstanceType,
						VPCConfig: ccetypes.VPCConfig{
							VPCSubnetID:       igSpec.InstanceTemplate.VPCSubnetID,
							SecurityGroupType: ccetypes.SecurityGroupType(igSpec.InstanceTemplate.SecurityGroupType),
							SecurityGroup: ccetypes.SecurityGroup{
								EnableCCERequiredSecurityGroup: igSpec.InstanceTemplate.SecurityGroup.EnableCCERequiredSecurityGroup,
								EnableCCEOptionalSecurityGroup: igSpec.InstanceTemplate.SecurityGroup.EnableCCEOptionalSecurityGroup,
								CustomSecurityGroupIDs:         igSpec.InstanceTemplate.SecurityGroup.CustomSecurityGroupIDs,
							},
						},
						// 修改InstanceResource
						InstanceResource:   c.config.SoldOutConfig.Resource,
						DeployCustomConfig: igSpec.InstanceTemplate.DeployCustomConfig,
						InstanceOS: ccetypes.InstanceOS{
							ImageType: "System",
							ImageName: "8.0 x86_64 (64bit)",
							OSType:    "linux",
							OSName:    "CentOS",
							OSVersion: "8.0",
							OSArch:    "x86_64 (64bit)",
						},
					},
				},
				{
					InstanceSpec: ccetypes.InstanceSpec{
						// MachineType 、InstanceType 修改
						MachineType:  igSpec.InstanceTemplate.MachineType,
						InstanceType: igSpec.InstanceTemplate.InstanceType,
						VPCConfig: ccetypes.VPCConfig{
							VPCSubnetID:       igSpec.InstanceTemplate.VPCSubnetID,
							SecurityGroupType: ccetypes.SecurityGroupType(igSpec.InstanceTemplate.SecurityGroupType),
							SecurityGroup: ccetypes.SecurityGroup{
								EnableCCERequiredSecurityGroup: igSpec.InstanceTemplate.SecurityGroup.EnableCCERequiredSecurityGroup,
								EnableCCEOptionalSecurityGroup: igSpec.InstanceTemplate.SecurityGroup.EnableCCEOptionalSecurityGroup,
								CustomSecurityGroupIDs:         igSpec.InstanceTemplate.SecurityGroup.CustomSecurityGroupIDs,
							},
						},
						// 修改InstanceResource
						InstanceResource:   igSpec.InstanceTemplate.InstanceResource,
						DeployCustomConfig: igSpec.InstanceTemplate.DeployCustomConfig,
						InstanceOS: ccetypes.InstanceOS{
							ImageType: "System",
							ImageName: "8.0 x86_64 (64bit)",
							OSType:    "linux",
							OSName:    "CentOS",
							OSVersion: "8.0",
							OSArch:    "x86_64 (64bit)",
						},
					},
				},
			},
		},
	}, nil)
	if err != nil {
		return resources, fmt.Errorf("update instance group %v failed, err: %v", instanceGroupID, err)
	}

	instanceGroup, initErr := common.NewInstanceGroup(ctx, c.base, c.instanceGroupID, time.Second*10, time.Minute*8)
	if initErr != nil {
		return nil, fmt.Errorf("create instance group %v failed: %v", c.instanceGroupID, initErr)
	}

	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		err = fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, c.instanceGroupID, err)
		return resources, err
	}

	instance, err := c.base.CCEClient.ListInstanceGroupInstancesByParams(ctx, c.base.ClusterID, instanceGroupID, &ccev2.ListInstancesByPageParams{
		OrderBy:     ccev2.InstanceOrderByCreatedAt,
		Order:       ccev2.OrderDESC,
		PageNo:      1,
		PageSize:    1,
		ClusterRole: ccetypes.ClusterRoleNode,
	}, nil)
	if err != nil || instance == nil {
		return resources, fmt.Errorf("list instance group %v failed, err: %v", instanceGroupID, err)
	}

	if len(instance.Page.List) != 1 {
		return resources, fmt.Errorf("list instance group %v instances number is %v, not equal 1, : %v", instanceGroupID, len(instance.Page.List))
	}

	// 判断最新扩出的节点是否是备选机型
	ig, err = c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, instanceGroupID, nil)
	if err != nil || ig == nil {
		return resources, fmt.Errorf("get instance group %v failed, err: %v", instanceGroupID, err)
	}

	if instance.Page.List[0].Spec.InstanceResource.MachineSpec != ig.InstanceGroup.Spec.InstanceTemplates[1].InstanceResource.MachineSpec {
		return resources, fmt.Errorf("instance %v resource machine type is %v not equal instanceGroup %v templates[1] machine type %v , err: %v",
			instance.Page.List[0].Spec.CCEInstanceID, instance.Page.List[0].Spec.InstanceResource.MachineSpec, instanceGroupID, ig.InstanceGroup.Spec.InstanceTemplates[1].InstanceResource.MachineSpec, err)
	}

	logger.Infof(ctx, "instanceGroup scale up instance with alternative template successfully")

	err = instanceGroup.AreTasksCompleted(ctx)
	if err != nil {
		return nil, fmt.Errorf("instance group %v tasks are not completed: %v", c.instanceGroupID, err)
	}

	return resources, nil
}

func (c *scaleInstanceGroup) Clean(ctx context.Context) error {
	// 清理测试节点组
	_, err := c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, true, nil)
	if err != nil {
		logger.Errorf(ctx, "delete instanceGroup %s failed: %v", c.instanceGroupID, err.Error())
		return fmt.Errorf(err.Error())
	}
	return nil
}

func (c *scaleInstanceGroup) Continue(ctx context.Context) bool {
	return false
}

func (c *scaleInstanceGroup) ConfigFormat() string {
	str, _ := json.Marshal(c.config)
	return string(str)
}
