/*
modification history
--------------------
2024/11/18, by shimingming<PERSON>, create
*/
/*
DESCRIPTION
场景：校验节点组根据优先级触发自动扩容，并且当节点组扩容失败不会阻塞节点组的自动扩容。
*/

package instancegroup

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/utils/strings/slices"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/addon"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	CheckInstanceGroupPriority cases.CaseName = "CheckInstanceGroupPriority"
)

const (
	testIGPriorityDeploymentName = "test-ig-priority"
	igScaleTaskWaitTimeout       = time.Minute * 6  // 每一轮扩容的最长等待时间，受节点创建时间影响
	igScaleTaskWaitInterval      = time.Second * 10 // 每一轮扩容检查的间隔时间
)

type checkIGPriorityConfig struct {
	checkInstanceGroupConfig `json:",inline"`
	PriorityList             []int // 优先级队列，len = 节点组数量
}

type igPriority struct {
	Id       string `json:"id"`       // 节点组ID
	Name     string `json:"name"`     // 节点组名称
	Priority int    `json:"priority"` // 节点组扩容优先级
}

type igPriorityList []igPriority

func (i igPriorityList) Len() int {
	return len(i)
}

func (i igPriorityList) Less(j, k int) bool {
	return i[j].Priority > i[k].Priority
}

func (i igPriorityList) Swap(j, k int) {
	i[j], i[k] = i[k], i[j]
}

type checkInstanceGroupPriority struct {
	base           *cases.BaseClient
	checkConfig    checkIGPriorityConfig
	scaledIGIDList []string
	igList         igPriorityList
}

func init() {
	cases.AddCase(context.TODO(), CheckInstanceGroupPriority, NewCheckInstanceGroupPriority)
}

func NewCheckInstanceGroupPriority(ctx context.Context) cases.Interface {
	return &checkInstanceGroupPriority{}
}

func (c *checkInstanceGroupPriority) Name() cases.CaseName {
	return CheckInstanceGroupPriority
}

func (c *checkInstanceGroupPriority) Desc() string {
	return "校验节点组按照优先级触发自动扩容"
}

func (c *checkInstanceGroupPriority) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	var createIGPriorityConfig checkIGPriorityConfig
	err = json.Unmarshal(config, &createIGPriorityConfig)
	if err != nil {
		err = fmt.Errorf("unmarshal create instance group request error: %v", err)
		return
	}
	if len(createIGPriorityConfig.PriorityList) < 2 {
		err = errors.New("case config `priorityList` length must >= 2")
		return
	}

	c.base = base
	c.checkConfig = createIGPriorityConfig
	return
}

func (c *checkInstanceGroupPriority) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID
	loggerID := ctx.Value(logger.RequestID).(string)
	if loggerID == "" {
		loggerID = logger.GetUUID()
	}
	shortUUID := loggerID[:5]

	// 确认worker数量 >= 3 来保证集群内3副本的基础服务都可以完整调度
	workerNodes, listNodeErr := c.base.KubeClient.ListNode(ctx, &kube.ListOptions{
		LabelSelector: map[string]string{
			"cluster-role": "node",
		},
	})
	if listNodeErr != nil {
		err = fmt.Errorf("KubeClient.ListNode failed, %s", listNodeErr.Error())
		return
	}
	workerCount := len(workerNodes.Items)
	if workerCount < 3 {
		err = errors.New("this case needs at least 3 workers in cluster")
		return
	}

	// 创建全局自动伸缩配置
	err = c.ensureAutoScalerConfigCreated(ctx)
	if err != nil {
		return
	}

	// 部署 deployment 副本数 = workerCount，deployment 为节点反亲和
	err = c.applyDeployment(ctx, int32(workerCount))
	if err != nil {
		return
	}

	// 获取集群额外的信息并获取可用节点子网
	clusterExtraInfo, getExtraInfoErr := c.base.CCEHostClient.GetClusterExtraInfo(ctx, clusterID, nil)
	if getExtraInfoErr != nil {
		err = fmt.Errorf("CCEHostClient.GetClusterExtraInfo failed, err: %v", getExtraInfoErr)
		return
	}
	var nodeSubnetID string
	subnets := clusterExtraInfo.Subnets
	for _, subnet := range subnets {
		subnetResp, subnetRespErr := c.base.VPCClient.DescribeSubnet(ctx, subnet.SubnetID, nil)
		if subnetRespErr != nil {
			err = fmt.Errorf("DescribeSubnet failed, subnetId: %d, err: %s", subnet.SubnetID, subnetRespErr.Error())
			return nil, err
		}
		if subnet.SubnetCIDR != "" && subnetResp.AvailableIPNum > 50 {
			logger.Infof(ctx, "nodeSubnetID id: %s, subnet cidr: %s, available ip num: %d", subnet.SubnetID, subnet.SubnetCIDR, subnetResp.AvailableIPNum)
			nodeSubnetID = subnet.SubnetID
			break
		}
	}
	if nodeSubnetID == "" {
		err = errors.New("no subnet id found in cluster extra info")
		return
	}
	logger.Infof(ctx, "node subnet id: %s", nodeSubnetID)

	// 创建n个节点组，初始节点为0，假设节点组优先级分别为0、1、2，预期扩容优先级 2 > 1 > 0
	priorityList := c.checkConfig.PriorityList
	igCount := len(priorityList)
	igList := make(igPriorityList, 0, igCount)

	for _, priority := range priorityList {
		igName := fmt.Sprintf("test-ig-priority-%d-%s", priority, shortUUID)
		igRequest := c.checkConfig.CreateInstanceGroupRequest
		igRequest.InstanceTemplate.Taints = []corev1.Taint{
			{
				Key:    "test-ig-priority-key",
				Value:  "test-ig-priority-value",
				Effect: corev1.TaintEffectNoSchedule,
			},
		}
		igRequest.Replicas = 0                                          // 初始节点数量为0
		igRequest.InstanceGroupName = igName                            // 节点组名称，避免数据残留导致创建失败
		igRequest.InstanceTemplate.VPCConfig.VPCSubnetID = nodeSubnetID // 节点子网信息
		igRequest.ClusterAutoscalerSpec = &types.ClusterAutoscalerSpec{
			Enabled:              true,     // 开启自动伸缩
			MinReplicas:          0,        // 设置最小扩容节点数量为0
			MaxReplicas:          1,        // 设置最大扩容节点数量为1
			ScalingGroupPriority: priority, // 设置扩容优先级
		}

		createRes, createErr := c.base.CCEClient.CreateInstanceGroup(ctx, clusterID, &igRequest, nil)
		if createErr != nil {
			err = fmt.Errorf("CreateInstanceGroup [priority: %d] failed, %s", priority, createErr.Error())
			return
		}

		igID := createRes.InstanceGroupID
		igList = append(igList, igPriority{
			Id:       igID,
			Name:     igName,
			Priority: priority,
		})
		logger.Infof(ctx, "create instance group `%s` [id: %s] [priority: %d] success", igName, igID, priority)
	}
	// 节点组排序，按照priority降序
	sort.Sort(igList)
	logger.Infof(ctx, "sorted ig list: %s", utils.ToPrettyJSON(igList))

	c.igList = igList

	// 总共测试 ig total 轮，每一轮扩容 deployment 副本数 + 1，每次扩容都会触发节点组扩容，比较实际扩容的节点组优先级是否匹配预先设置好的优先级策略
	for idx := 0; idx < len(c.igList); idx++ {
		// 每次扩容1个
		err = c.scaleDeployment(ctx, 1)
		if err != nil {
			return
		}
		// 校验优先级
		err = c.checkPriority(ctx, idx)
		if err != nil {
			return
		}
	}

	return
}

func (c *checkInstanceGroupPriority) Clean(ctx context.Context) (err error) {
	kubeClient := c.base.KubeClient
	// 清理deployment
	deployment, _ := kubeClient.GetDeploymentAppsV1(ctx, metav1.NamespaceDefault, testIGPriorityDeploymentName, &kube.GetOptions{})
	if deployment != nil && deployment.Name != "" {
		err = c.base.KubeClient.DeleteDeploymentAppsV1(ctx, metav1.NamespaceDefault, testIGPriorityDeploymentName)
		if err != nil {
			err = fmt.Errorf("DeleteDeploymentAppsV1 failed, %s", err.Error())
			return
		}
	}
	// 清理ig
	if len(c.igList) > 0 {
		for _, ig := range c.igList {
			instanceGroup, initErr := common.NewInstanceGroup(ctx, c.base, ig.Id, time.Second*10, time.Minute*8)
			if initErr != nil {
				return fmt.Errorf("create instance group %v failed: %v", ig.Id, initErr)
			}
			err = instanceGroup.AreTasksCompleted(ctx)
			if err != nil {
				return fmt.Errorf("instance group %v tasks are not completed: %v", ig.Id, err)
			}

			_, err = c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, ig.Id, true, nil)
			if err != nil {
				err = fmt.Errorf("CCEClient.DeleteInstanceGroup failed, %s", err.Error())
				return
			}
		}
	}
	return
}

func (c *checkInstanceGroupPriority) Continue(ctx context.Context) bool {
	return true
}

func (c *checkInstanceGroupPriority) ConfigFormat() string {
	return ""
}

func (c *checkInstanceGroupPriority) ensureAutoScalerConfigCreated(ctx context.Context) (err error) {
	clusterID := c.base.ClusterID
	cceClient := c.base.CCEClient

	// 获取全局的自动伸缩配置
	getRes, getErr := cceClient.GetAutoScalerConfig(ctx, clusterID, nil)
	if getErr != nil {
		err = fmt.Errorf("CCEClient.GetAutoScalerConfig failed, %s", getErr.Error())
		return
	}
	// 没有自动伸缩配置，则创建配置
	if getRes.Autoscaler == nil || getRes.Autoscaler.CAConfig == nil {
		logger.Infof(ctx, "autoscaler config is nil, start to create autoscaler config")

		_, err = cceClient.CreateAutoScalerConfig(ctx, clusterID, nil)
		if err != nil {
			err = fmt.Errorf("CCEClient.CreateAutoScalerConfig failed, %s", err.Error())
			return
		}
		// 重新获取一次
		getRes, getErr = cceClient.GetAutoScalerConfig(ctx, clusterID, nil)
		if getErr != nil {
			err = fmt.Errorf("CCEClient.GetAutoScalerConfig failed, %s", getErr.Error())
			return
		}
	}
	logger.Infof(ctx, "autoscaler config created")

	caConfig := getRes.Autoscaler.CAConfig
	// 设置扩容算法 => 优先级
	caConfig.Expander = addon.CAExpanderPriority
	_, err = cceClient.UpdateAutoScalerConfig(ctx, clusterID, caConfig, nil)
	if err != nil {
		err = fmt.Errorf("CCEClient.UpdateAutoScalerConfig failed, %s", err.Error())
		return
	}
	return
}

func (c *checkInstanceGroupPriority) applyDeployment(ctx context.Context, replicas int32) (err error) {
	var terminationGracePeriodSeconds int64
	deployment := appsv1.Deployment{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Deployment",
			APIVersion: "apps/v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      testIGPriorityDeploymentName,
			Namespace: metav1.NamespaceDefault,
			Labels: map[string]string{
				"app": testIGPriorityDeploymentName,
			},
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": testIGPriorityDeploymentName,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": testIGPriorityDeploymentName,
					},
				},
				Spec: corev1.PodSpec{
					Tolerations: []corev1.Toleration{
						{
							Key:      "test-ig-priority-key",
							Operator: corev1.TolerationOpEqual,
							Value:    "test-ig-priority-value",
							Effect:   corev1.TaintEffectNoSchedule,
						},
					},
					TerminationGracePeriodSeconds: &terminationGracePeriodSeconds,
					Affinity: &corev1.Affinity{
						PodAntiAffinity: &corev1.PodAntiAffinity{
							RequiredDuringSchedulingIgnoredDuringExecution: []corev1.PodAffinityTerm{{
								LabelSelector: &metav1.LabelSelector{
									MatchExpressions: []metav1.LabelSelectorRequirement{{
										Key:      "app",
										Operator: metav1.LabelSelectorOpIn,
										Values:   []string{testIGPriorityDeploymentName},
									}},
								},
								TopologyKey: "kubernetes.io/hostname", // 利用副本数和节点反亲和来强制触发扩容
							}},
						},
					},
					Containers: []corev1.Container{{
						Name:            testIGPriorityDeploymentName,
						Image:           "registry.baidubce.com/cce-public/busybox",
						ImagePullPolicy: corev1.PullIfNotPresent,
						Command:         []string{"sleep", "3600"},
					}},
				},
			},
		},
	}
	_, err = c.base.KubeClient.CreateDeploymentAppsV1(ctx, metav1.NamespaceDefault, &deployment)
	if err != nil {
		err = fmt.Errorf("CreateDeploymentAppsV1 failed, %s", err.Error())
		return
	}
	return
}

func (c *checkInstanceGroupPriority) scaleDeployment(ctx context.Context, increment int32) (err error) {
	kubeClient := c.base.KubeClient
	scale, getScaleErr := kubeClient.GetDeploymentScaleAppsV1(ctx, metav1.NamespaceDefault, testIGPriorityDeploymentName, &kube.GetOptions{})
	if getScaleErr != nil {
		err = fmt.Errorf("GetDeploymentScaleAppsV1 failed, %s", getScaleErr.Error())
		return
	}
	scale.Spec.Replicas = scale.Spec.Replicas + increment
	_, err = kubeClient.ScaleDeploymentAppsV1(ctx, corev1.NamespaceDefault, testIGPriorityDeploymentName, scale)
	if err != nil {
		err = fmt.Errorf("ScaleDeploymentAppsV1 to replicas %d failed, err: %v", scale.Spec.Replicas, err)
		return
	}
	return
}

func (c *checkInstanceGroupPriority) checkPriority(ctx context.Context, idx int) (err error) {
	expectedPriority := c.igList[idx].Priority

	logger.Warnf(ctx, "start to check priority for %d", expectedPriority)
	var scalingIG *ccev2.InstanceGroup
	// 检查触发扩容事件的超时
	cancelCtx, cancel := context.WithTimeout(ctx, igScaleTaskWaitTimeout)
	wait.UntilWithContext(cancelCtx, func(ctx context.Context) {
		igInfoList, listIGErr := c.base.CCEClient.ListInstanceGroups(ctx, c.base.ClusterID, nil, nil)
		if listIGErr != nil {
			err = fmt.Errorf("CCEClient.ListInstanceGroups failed, %s", listIGErr.Error())
			return
		}
		logger.Warnf(ctx, "scaled instance group list: %v", c.scaledIGIDList)
		for _, igInfo := range igInfoList.Page.List {
			igID := igInfo.Spec.CCEInstanceGroupID
			// 检测是否为此次需要检测的ig，如果不是则跳过，避免其他case的干扰
			if isRequired := c.checkRequiredIG(ctx, igID); !isRequired {
				continue
			}
			// 节点组已经扩容过了
			if slices.Contains(c.scaledIGIDList, igID) {
				continue
			}
			// 节点组正在扩容
			if igInfo.Status.ScalingReplicas == 1 {
				scalingIG = igInfo
				c.scaledIGIDList = append(c.scaledIGIDList, igID)
				cancel()
				break
			}
		}
	}, igScaleTaskWaitInterval)
	if errors.Is(cancelCtx.Err(), context.DeadlineExceeded) {
		err = fmt.Errorf("wait instance group to scale timeout for %s", igScaleTaskWaitTimeout.String())
		return
	}
	if scalingIG == nil {
		err = fmt.Errorf("no instance group to scale in %s", igScaleTaskWaitTimeout.String())
		return
	}
	scalingIGName := scalingIG.Spec.InstanceGroupName
	scalingIGPriority := scalingIG.Spec.ClusterAutoscalerSpec.ScalingGroupPriority
	logger.Warnf(ctx, "scaling instance group `%s` priority %d", scalingIGName, scalingIGPriority)

	// 检查正在扩容的节点组优先级是否 >= 期望的优先级
	if scalingIGPriority < expectedPriority {
		err = fmt.Errorf("instance group `%s` priority %d less then expected priority %d", scalingIGName, scalingIGPriority, expectedPriority)
		return
	}
	logger.Warnf(ctx, "check priority for %d success", expectedPriority)
	return
}

func (c *checkInstanceGroupPriority) checkRequiredIG(ctx context.Context, igID string) (isRequired bool) {
	for _, requiredIG := range c.igList {
		if igID == requiredIG.Id {
			isRequired = true
			break
		}
	}
	if !isRequired {
		logger.Infof(ctx, "`%s` not in requierd ig check list, skip it", igID)
	}
	return
}
