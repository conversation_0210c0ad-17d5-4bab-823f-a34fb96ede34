package instancegroup

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	PrechargeInstancegroupAddInstance cases.CaseName = "PrechargeInstancegroupAddInstance"
)

type prechargeInstancegroupAddInstance struct {
	base              *cases.BaseClient
	instanceGroupID   string
	config            prechargeInstancegroupConfig
	afterMoveReplicas int
}

type prechargeInstancegroupConfig struct {
	CreateInstanceGroupConfig *ccetypes.InstanceGroupSpec `json:"createInstanceGroupConfig"`
	ExistedInstanceID         []string                    `json:"existedInstanceID"`
}

func init() {
	cases.AddCase(context.TODO(), PrechargeInstancegroupAddInstance, NewPrechargeInstancegroupAddInstance)
}

func NewPrechargeInstancegroupAddInstance(ctx context.Context) cases.Interface {
	return &prechargeInstancegroupAddInstance{}
}

func (c *prechargeInstancegroupAddInstance) Name() cases.CaseName {
	return PrechargeInstancegroupAddInstance
}

func (c *prechargeInstancegroupAddInstance) Desc() string {
	return "预付费节点组添加已有预付费节点操作"
}

func (c *prechargeInstancegroupAddInstance) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	if base == nil {
		return errors.New("nil baseClient")
	}

	logger.Infof(ctx, "json raw config: %s", string(config))
	if err := json.Unmarshal(config, &c.config); err != nil {
		return err
	}
	logger.Infof(ctx, "config: %s", utils.ToJSON(c.config))

	if c.config.ExistedInstanceID == nil || len(c.config.ExistedInstanceID) == 0 {
		return fmt.Errorf("existedInstanceID is nil or empty")
	}
	c.base = base
	return
}

func (c *prechargeInstancegroupAddInstance) Check(ctx context.Context) (resources []cases.Resource, err error) {

	// 0、确保已有节点存在
	err = c.EnsureExistInstance(ctx)
	if err != nil {
		return nil, fmt.Errorf("ensure existed instance failed: %v", err)
	}

	// 1、创建预付费节点组
	err = c.CreatePreChargeInstanceGroup(ctx)
	if err != nil {
		return nil, fmt.Errorf("create preChargeInstanceGroup failed: %v", err)
	}

	// 2、节点组添加已有节点
	logger.Infof(ctx, "attach existed instance to instancegroup %s", c.config.CreateInstanceGroupConfig.InstanceTemplate.AdminPassword)
	existedInstanceConfig := make([]*ccev2.InstanceSet, len(c.config.ExistedInstanceID))
	for i, instanceID := range c.config.ExistedInstanceID {
		existedInstanceConfig[i] = &ccev2.InstanceSet{
			InstanceSpec: ccetypes.InstanceSpec{
				AdminPassword: c.config.CreateInstanceGroupConfig.InstanceTemplate.AdminPassword,
				Existed:       true,
				ExistedOption: ccetypes.ExistedOption{
					ExistedInstanceID: instanceID,
					Rebuild:           func(rebuild bool) *bool { return &rebuild }(false),
				},
				MachineType: ccetypes.MachineTypeBCC,
				ClusterRole: ccetypes.ClusterRoleNode,
			},
		}
	}

	_, err = c.base.CCEHostClient.AttachInstancesToInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, &ccev2.AttachInstancesToInstanceGroupRequest{
		Incluster:              false,
		UseInstanceGroupConfig: true,
		ExistedInstances:       existedInstanceConfig,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("attach existed instance to instancegroup failed: %v", err)
	}

	// 3、所有相关节点组判断项，确保节点组移动任务完成，并确保replicas数量正确
	err = c.EnsureInstanceGroupTaskDone(ctx)
	if err != nil {
		return nil, err
	}

	// 4、清理节点组，并确保删除
	err = c.DeleteInstanceGroup(ctx)
	if err != nil {
		return nil, err
	}

	// 5、确保预付费已有节点 没有被删除
	err = c.EnsureExistInstance(ctx)
	if err != nil {
		return nil, fmt.Errorf("ensure existed instance failed: %v", err)
	}

	return
}

func (c *prechargeInstancegroupAddInstance) Clean(ctx context.Context) error {
	return nil
}

func (c *prechargeInstancegroupAddInstance) Continue(ctx context.Context) bool {
	return true
}

func (c *prechargeInstancegroupAddInstance) ConfigFormat() string {
	return ""
}

func (c *prechargeInstancegroupAddInstance) CreatePreChargeInstanceGroup(ctx context.Context) (err error) {
	createInstanceGroupRes, createErr := c.base.CCEClient.CreateInstanceGroup(ctx, c.base.ClusterID, &ccev2.CreateInstanceGroupRequest{
		InstanceGroupSpec: *c.config.CreateInstanceGroupConfig,
	}, nil)

	if createErr != nil {
		return fmt.Errorf("instancegroup create falied : %V", createErr)
	}

	c.instanceGroupID = createInstanceGroupRes.InstanceGroupID

	instanceGroup, err := common.NewInstanceGroup(ctx, c.base, c.instanceGroupID, 10*time.Second, 6*time.Minute)
	if err != nil {
		return fmt.Errorf("failed to init instancegroup client:%v", err)
	}

	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		return fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, c.instanceGroupID, err)
	}

	return nil
}

func (c *prechargeInstancegroupAddInstance) EnsureInstanceGroupTaskDone(ctx context.Context) (err error) {

	c.afterMoveReplicas = c.config.CreateInstanceGroupConfig.Replicas + len(c.config.ExistedInstanceID)

	err = func() error {
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()
		timer := time.NewTimer(10 * time.Minute)
		defer timer.Stop()
		for {
			select {
			case <-ticker.C:
				igResp, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, nil)
				if err != nil {
					return fmt.Errorf("failed to get instanceGroup %s : %v", c.instanceGroupID, err)
				}

				// 期望节点数 == c.afterMoveReplicas
				if igResp.InstanceGroup.Spec.Replicas != c.afterMoveReplicas {
					return fmt.Errorf("spec replicas should be %d not %d", c.afterMoveReplicas, igResp.InstanceGroup.Spec.Replicas)
				}

				// 可用节点数 == c.afterMoveReplicas
				if c.afterMoveReplicas != igResp.InstanceGroup.Status.ReadyReplicas {
					logger.Infof(ctx, "instanceGroup %s still scaling", c.instanceGroupID)
					continue
				}

				return nil
			case <-timer.C:
				return fmt.Errorf("timeout waiting instanceGroup moving")
			}
		}
	}()
	if err != nil {
		return err
	}

	// 2、检测节点组task是否完成
	err = func() error {
		ticker := time.NewTicker(3 * time.Second)
		defer ticker.Stop()
		timer := time.NewTimer(4 * time.Minute)
		defer timer.Stop()
		for {
			select {
			case <-ticker.C:
				listTaskRes, err := c.base.CCEHostClient.ListTasks(ctx, ccetypes.TaskTypeInstanceGroupReplicas, ccev2.ListTaskOption{
					TargetID: c.instanceGroupID,
				}, nil)
				if err != nil {
					return fmt.Errorf("failed to list task in instanceGroup, err: %v", err)
				}
				if listTaskRes == nil || listTaskRes.Page.TotalCount == 0 {
					return fmt.Errorf("task number is 0 or nil in instanceGroup: %s", c.instanceGroupID)
				}
				for _, task := range listTaskRes.Page.Items {
					if task.Phase != string(ccetypes.TaskProcessPhaseDone) {
						logger.Infof(ctx, "instance already move but instanceGroup task is not done, taskID: %s", task.ID)
						continue
					}
				}
				return nil
			case <-timer.C:
				return fmt.Errorf("timeout waiting instanceGroup task finish")
			}
		}
	}()
	if err != nil {
		return err
	}

	return nil
}

func (c *prechargeInstancegroupAddInstance) EnsureExistInstance(ctx context.Context) error {

	for _, instanceID := range c.config.ExistedInstanceID {
		instanceResp, err := c.base.BCCClient.DescribeInstance(ctx, instanceID, nil)
		if err != nil {
			return fmt.Errorf("failed to get instance: %s failed: %v", instanceID, err)
		}
		if instanceResp == nil {
			return fmt.Errorf("instance: %s is not exist", instanceID)
		}
		if instanceResp.Status != bcc.InstanceStatusRunning {
			return fmt.Errorf("instance: %s is not running", instanceID)
		}
		logger.Infof(ctx, "instance: %s is running", c.config.ExistedInstanceID[0])
	}
	return nil
}

func (c *prechargeInstancegroupAddInstance) DeleteInstanceGroup(ctx context.Context) error {

	_, err := c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, true, nil)
	if err != nil {
		return fmt.Errorf("delete instance group %v failed:%v", c.instanceGroupID, err)
	}

	err = func() error {
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()
		timer := time.NewTimer(10 * time.Minute)
		defer timer.Stop()
		for {
			select {
			case <-ticker.C:
				_, err = c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, nil)
				if err == nil {
					continue
				}
				return nil
			case <-timer.C:
				return fmt.Errorf("timeout waiting instanceGroup deleting")
			}
		}
	}()
	if err != nil {
		return err
	}
	return nil
}
