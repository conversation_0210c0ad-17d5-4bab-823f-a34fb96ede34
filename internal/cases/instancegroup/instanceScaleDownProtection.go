/*
节点缩容保护功能，只属于节点组的节点才能有缩容保护，游离节点无缩容保护
1、创建节点组，不开启缩容保护，扩容节点+1并检查节点的缩容保护 +1
2、开启缩容保护，扩容节点+2并检查节点的缩容保护. +2
3、缩容节点组中节点，调整节点数
3.1 缩容节点数至小于开启缩容保护的节点数，预期报错 本次是 3个 要缩到小于 2 个
3.2 缩容节点数大于等于开启缩容保护的节点数，预期成功 -1 本次是 3个 要缩到大于等于 2 个
4、主动删除节点，可以删除-1
5、更改节点缩容保护,缩容-1
*/

package instancegroup

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	InstanceScaleDownProtection cases.CaseName = "InstanceScaleDownProtection"
)

type instanceScaleDownProtection struct {
	base             *cases.BaseClient
	instanceGroupID  string
	config           InstanceScaleDownProtectionConfig
	enableScaleDown  []string
	disableScaleDown []string
}

type InstanceScaleDownProtectionConfig struct {
	CreateInstanceGroupConfig *ccetypes.InstanceGroupSpec `json:"createInstanceGroupConfig"`
}

func init() {
	cases.AddCase(context.TODO(), InstanceScaleDownProtection, NewInstanceScaleDownProtection)
}

func NewInstanceScaleDownProtection(ctx context.Context) cases.Interface {
	return &instanceScaleDownProtection{}
}

func (c *instanceScaleDownProtection) Name() cases.CaseName {
	return InstanceScaleDownProtection
}

func (c *instanceScaleDownProtection) Desc() string {
	return "节点缩容保护"
}

func (c *instanceScaleDownProtection) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base

	var createIGConfig InstanceScaleDownProtectionConfig
	logger.Infof(ctx, "json raw config: %s", string(config))
	if err = json.Unmarshal(config, &createIGConfig); err != nil {
		return err
	}
	c.config = createIGConfig
	logger.Infof(ctx, "config: %s", utils.ToJSON(c.config))

	return nil
}

func (c *instanceScaleDownProtection) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID

	// step1: 创建节点组，更改其副本数为1，未开启缩容保护
	c.config.CreateInstanceGroupConfig.Replicas = 1
	createRes, createErr := c.base.CCEClient.CreateInstanceGroup(ctx, clusterID, &ccev2.CreateInstanceGroupRequest{
		InstanceGroupSpec: *c.config.CreateInstanceGroupConfig}, nil)
	if createErr != nil || createRes == nil {
		return nil, fmt.Errorf("create instance group failed, err: %v", createErr)
	}

	c.instanceGroupID = createRes.InstanceGroupID

	instanceGroup, err := common.NewInstanceGroup(ctx, c.base, c.instanceGroupID, time.Second*10, time.Minute*8)
	if err != nil {
		err = fmt.Errorf("failed to init instancegroup client:%v", err)
		return nil, err
	}

	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		err = fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, c.instanceGroupID, err)
		return nil, err
	}

	getInstanceGroup, getErr := c.base.CCEClient.GetInstanceGroup(ctx, clusterID, c.instanceGroupID, nil)
	if getErr != nil || getInstanceGroup == nil {
		err = fmt.Errorf("instance group get falied: %v", getErr)
		return
	}
	ig := getInstanceGroup.InstanceGroup

	err = c.CheckNewestInstanceAndInstanceGroupScaleDown(ctx, ig)
	if err != nil {
		return nil, err
	}

	// step 2、开启节点组缩容保护，扩容节点+2
	logger.Infof(ctx, "enable instanceGroup %v scale down protection and scale up ", c.instanceGroupID)

	_, err = c.base.CCEHostClient.UpdateInstanceGroupConfig(ctx, clusterID, c.instanceGroupID, &ccev2.UpdateInstanceGroupRequest{
		PasswordNeedUpdate: false,
		InstanceGroupSpec: ccetypes.InstanceGroupSpec{
			InstanceGroupName: ig.Spec.InstanceGroupName,
			InstanceTemplate: ccetypes.InstanceTemplate{
				InstanceSpec: ccetypes.InstanceSpec{
					MachineType:  ig.Spec.InstanceTemplate.MachineType,
					InstanceType: ig.Spec.InstanceTemplate.InstanceType,
					VPCConfig: ccetypes.VPCConfig{
						VPCSubnetID:     ig.Spec.InstanceTemplate.VPCSubnetID,
						SecurityGroupID: ig.Spec.InstanceTemplate.VPCConfig.SecurityGroupID,
						SecurityGroup: ccetypes.SecurityGroup{
							EnableCCERequiredSecurityGroup: true,
							EnableCCEOptionalSecurityGroup: false,
						},
					},
					InstanceResource: ig.Spec.InstanceTemplate.InstanceResource,
					ImageID:          ig.Spec.InstanceTemplate.ImageID,
					// 缩容保护开启
					ScaleDownDisabled: true,
				},
			},
			Replicas: ig.Spec.Replicas + 2,
		},
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("update instance group %v config failed: %v", c.instanceGroupID, err)
	}

	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		err = fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, c.instanceGroupID, err)
		return nil, err
	}

	// 现在节点数为3
	getInstanceGroup, getErr = c.base.CCEClient.GetInstanceGroup(ctx, clusterID, c.instanceGroupID, nil)
	if getErr != nil || getInstanceGroup == nil {
		err = fmt.Errorf("instance group get falied: %v", getErr)
		return
	}
	ig = getInstanceGroup.InstanceGroup

	err = c.CheckNewestInstanceAndInstanceGroupScaleDown(ctx, ig)
	if err != nil {
		return nil, err
	}

	// 3、缩容节点
	err = c.updateDisableAndEnableCCEInstancesArray(ctx)
	if err != nil {
		return nil, err
	}
	logger.Infof(ctx, "disable instances %v", c.disableScaleDown)
	logger.Infof(ctx, "enable instances %v", c.enableScaleDown)

	// 缩容 len(c.disableScaleDown) + 1 个节点数,
	logger.Infof(ctx, "scale down  %v instances", len(c.disableScaleDown)+1)
	_, err = c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, clusterID, c.instanceGroupID, &ccev2.UpdateInstanceGroupReplicasRequest{
		Replicas: ig.Spec.Replicas - len(c.disableScaleDown) - 1,
	}, nil)

	if err != nil {
		if strings.Contains(err.Error(), "could not scale down to") {
			logger.Infof(ctx, "instance scale down protection and scale down disabled successfully")
		} else {
			return nil, fmt.Errorf("update instance group %v replicas failed: %v", c.instanceGroupID, err)
		}
	}

	// 缩容 len(c.disableScaleDown)  个节点数，即 1 个
	logger.Infof(ctx, "scale down  %v instances", len(c.disableScaleDown))

	_, err = c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, clusterID, c.instanceGroupID, &ccev2.UpdateInstanceGroupReplicasRequest{
		Replicas: ig.Spec.Replicas - len(c.disableScaleDown),
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("update instance group %v replicas failed: %v", c.instanceGroupID, err)
	}

	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		err = fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, c.instanceGroupID, err)
		return nil, err
	}
	// 检查节点组剩余节点是否是开启保护的
	instancesRes, err := c.base.CCEClient.ListInstancesByInstanceGroupID(ctx, c.base.ClusterID, c.instanceGroupID, 1, 100, nil)
	if err != nil || instancesRes == nil {
		return nil, fmt.Errorf("list instances by instance group id failed:%v", err)
	}

	// 此时节点组剩两个节点
	err = c.CheckInstanceExistInArr(ctx, instancesRes.Page.List, c.enableScaleDown)
	if err != nil {
		return nil, fmt.Errorf("instances %v are not exist in enableScaleDown array %v", instancesRes.Page.List, c.enableScaleDown)
	}

	// 更新两数组，disableScaleDown和enableScaleDown
	err = c.updateDisableAndEnableCCEInstancesArray(ctx)
	if err != nil {
		return nil, err
	}
	logger.Infof(ctx, "disable instances %v", c.disableScaleDown)
	logger.Infof(ctx, "enable instances %v", c.enableScaleDown)
	// 4、删除节点
	// 删除节点组中受保护的第一个节点
	instanceToBeRemoved := c.enableScaleDown[0]
	logger.Infof(ctx, "begin to delete instances %v in the instanceGroup %v", instanceToBeRemoved, c.instanceGroupID)

	_, err = c.base.CCEClient.CreateScaleDownInstanceGroupByCleanPolicy(ctx, c.base.ClusterID, c.instanceGroupID, []string{instanceToBeRemoved},
		ccev2.DeleteCleanPolicy, &ccetypes.DeleteOption{
			DrainNode:         false,
			MoveOut:           true,
			DeleteResource:    true,
			DeleteCDSSnapshot: true,
		}, nil)
	if err != nil {
		return nil, fmt.Errorf("create scale down instance group by clean policy failed: %v", err)
	}

	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		err = fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, c.instanceGroupID, err)
		return nil, err
	}

	// 节点组剩一个
	_, err = c.base.CCEClient.GetInstance(ctx, clusterID, instanceToBeRemoved, nil)
	if err != nil {
		if strings.Contains(err.Error(), "not exists") {
			logger.Infof(ctx, "instance %v is deleted successfully", instanceToBeRemoved)
		} else {
			return nil, fmt.Errorf("instance %v is still in cluster, expected  deleted", instanceToBeRemoved)
		}
	}

	// 5、更改节点缩容保护，并缩容
	err = c.updateDisableAndEnableCCEInstancesArray(ctx)
	if err != nil {
		return nil, err
	}
	logger.Infof(ctx, "disable instances %v", c.disableScaleDown)
	logger.Infof(ctx, "enable instances %v", c.enableScaleDown)

	res, err := c.base.CCEClient.EnableInstanceScaleDown(ctx, c.base.ClusterID, &ccev2.EnableInstanceScaleDownRequest{
		CCEInstanceIDs:    c.enableScaleDown,
		ScaleDownDisabled: false,
	}, nil)

	if err != nil || res == nil {
		return nil, fmt.Errorf("enable scale down instance %v failed: %v", c.enableScaleDown, err)
	}
	if len(res.FailedInstances) != 0 {
		return nil, fmt.Errorf("enable scale down some instances %v failed", res.FailedInstances)
	}

	getInstanceGroup, getErr = c.base.CCEClient.GetInstanceGroup(ctx, clusterID, c.instanceGroupID, nil)
	if getErr != nil || getInstanceGroup == nil {
		err = fmt.Errorf("instance group get falied: %v", getErr)
		return
	}
	ig = getInstanceGroup.InstanceGroup

	logger.Infof(ctx, "scale down  %v instances", len(c.enableScaleDown))

	// replicase: 1 - 1 =0
	finalReplicas := ig.Spec.Replicas - len(c.enableScaleDown)
	_, err = c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, clusterID, c.instanceGroupID, &ccev2.UpdateInstanceGroupReplicasRequest{
		Replicas: finalReplicas,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("update instance group %v replicas failed: %v", c.instanceGroupID, err)
	}

	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		err = fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, c.instanceGroupID, err)
		return nil, err
	}

	// 检查节点组的期望节点数为0,finalReplicas
	getInstanceGroup, getErr = c.base.CCEClient.GetInstanceGroup(ctx, clusterID, c.instanceGroupID, nil)
	if getErr != nil || getInstanceGroup == nil {
		err = fmt.Errorf("instance group get falied: %v", getErr)
		return
	}
	if getInstanceGroup.InstanceGroup.Spec.Replicas != finalReplicas {
		err = fmt.Errorf("instance group %v replicas is not %v:%v", c.instanceGroupID, finalReplicas, err)
		return nil, err
	}

	logger.Infof(ctx, "check successfully")

	return
}

func (c *instanceScaleDownProtection) Clean(ctx context.Context) error {
	_, err := c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, true, nil)
	if err != nil {
		logger.Errorf(ctx, "delete instanceGroup %s failed: %v", c.instanceGroupID, err.Error())
		return fmt.Errorf(err.Error())
	}
	return nil
}

func (c *instanceScaleDownProtection) Continue(ctx context.Context) bool {
	return true
}

func (c *instanceScaleDownProtection) ConfigFormat() string {
	return ""
}

func (c *instanceScaleDownProtection) CheckInstanceScaleDown(ctx context.Context, CCEInstancesID string) (bool, error) {
	instanceCrd, err := c.base.CCEHostClient.GetInstanceCRD(ctx, c.base.ClusterID, CCEInstancesID, nil)
	if err != nil || instanceCrd == nil {
		return false, fmt.Errorf("get instance %v crd failed: %v", CCEInstancesID, err)
	}
	isEnabled := instanceCrd.Instance.Spec.ScaleDownDisabled
	logger.Infof(ctx, "instance %v scaleDownProtection is %v", CCEInstancesID, isEnabled)

	return isEnabled, nil
}

func (c *instanceScaleDownProtection) CheckNewestInstanceAndInstanceGroupScaleDown(ctx context.Context, instanceGroup *ccev2.InstanceGroup) error {
	// 最新的节点组节点
	listInstance, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
		KeywordType: ccev2.InstanceKeywordTypeInstanceGroupID,
		Keyword:     c.instanceGroupID,
		OrderBy:     ccev2.InstanceOrderByCreatedAt,
		Order:       ccev2.OrderDESC,
		PageNo:      1,
		PageSize:    1,
		ClusterRole: ccetypes.ClusterRoleNode,
	}, nil)
	if err != nil || listInstance == nil {
		return fmt.Errorf("list instances by instance group %v failed:%v", c.instanceGroupID, err)
	}

	if len(listInstance.InstancePage.InstanceList) != 1 {
		return fmt.Errorf("list instances by instance group %v number %v is not expect 1", len(listInstance.InstancePage.InstanceList), c.instanceGroupID)
	}

	isDisabled, err := c.CheckInstanceScaleDown(ctx, listInstance.InstancePage.InstanceList[0].Spec.CCEInstanceID)
	if err != nil {
		return fmt.Errorf("check instance %v scale down failed: %v", listInstance.InstancePage.InstanceList[0].Spec.CCEInstanceID, err)
	}

	if isDisabled != instanceGroup.Spec.InstanceTemplate.ScaleDownDisabled {
		return fmt.Errorf("instance %v scale down protection is %v, not expect instanceGroup %v config %v",
			listInstance.InstancePage.InstanceList[0].Spec.CCEInstanceID, isDisabled, c.instanceGroupID, instanceGroup.Spec.InstanceTemplate.ScaleDownDisabled)
	}

	logger.Infof(ctx, "instance %v scale down protection is %v, expect instanceGroup %v config %v",
		listInstance.InstancePage.InstanceList[0].Spec.CCEInstanceID, isDisabled, c.instanceGroupID, instanceGroup.Spec.InstanceTemplate.ScaleDownDisabled)

	return nil
}

func (c *instanceScaleDownProtection) updateDisableAndEnableCCEInstancesArray(ctx context.Context) (err error) {
	logger.Infof(ctx, "begin to update disable and enable array")

	listInstancesRes, err := c.base.CCEClient.ListInstancesByInstanceGroupID(ctx, c.base.ClusterID, c.instanceGroupID, 1, 100, nil)
	if err != nil || listInstancesRes == nil {
		return fmt.Errorf("list instances by instance group %v failed:%v", c.instanceGroupID, err)
	}

	// 清空数组
	c.disableScaleDown = c.disableScaleDown[:0]
	c.enableScaleDown = c.enableScaleDown[:0]

	for _, instance := range listInstancesRes.Page.List {
		instanceCrd, getErr := c.base.CCEHostClient.GetInstanceCRD(ctx, c.base.ClusterID, instance.Spec.CCEInstanceID, nil)
		if getErr != nil || instanceCrd == nil {
			return fmt.Errorf("get instance %v crd failed: %v", instance.Spec.CCEInstanceID, err)
		}

		if instanceCrd.Instance.Spec.ScaleDownDisabled {
			c.enableScaleDown = append(c.enableScaleDown, instance.Spec.CCEInstanceID)
		} else {
			c.disableScaleDown = append(c.disableScaleDown, instance.Spec.CCEInstanceID)
		}
	}

	logger.Infof(ctx, "end to update disable and enable array")
	return nil
}

func (c *instanceScaleDownProtection) CheckInstanceExistInArr(ctx context.Context, instances []*ccev2.Instance, instancesArr []string) error {
	logger.Infof(ctx, "begin to check instances %v exist in array %v", instances, instancesArr)

	if len(instances) == 0 && len(instancesArr) == 0 {
		logger.Infof(ctx, "instances and instancesArr are both empty")
		return nil
	}

	var isExist bool

	for _, instance := range instances {
		isExist = false

		for _, instanceID := range instancesArr {
			if instance.Spec.CCEInstanceID == instanceID {
				isExist = true
				break
			}
			isExist = false
		}

		if !isExist {
			return fmt.Errorf("instances %v not exist in array %v", instance.Spec.CCEInstanceID, instancesArr)
		}
	}

	return nil
}
