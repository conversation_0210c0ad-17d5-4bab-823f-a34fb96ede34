/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  shrink_instancegroup
 * @Version: 1.0.0
 * @Date: 2020/8/10 10:54 上午
 */
package instancegroup

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/google/go-cmp/cmp"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// ShrinkInstanceGroup - 缩容节点组 Case 名字
	ShrinkInstanceGroup cases.CaseName = "ShrinkInstanceGroup"
)

type shrinkInstanceGroup struct {
	base              *cases.BaseClient
	remainInstanceIDs []string
	instanceGroupID   string
	config            shrinkInstanceGroupConfig
}

type shrinkInstanceGroupConfigReplicasItem struct {
	Replicas         int  `json:"replicas"`
	SpecifyInstances bool `json:"specifyInstances"`
	DeleteInstances  bool `json:"deleteInstances"`
}

type shrinkInstanceGroupConfig struct {
	CreateInstanceGroupConfig *ccetypes.InstanceGroupSpec             `json:"createInstanceGroupConfig"`
	ToReplicas                []shrinkInstanceGroupConfigReplicasItem `json:"toReplicas"`
}

func init() {
	cases.AddCase(context.TODO(), ShrinkInstanceGroup, NewShrinkInstanceGroup)
}

func NewShrinkInstanceGroup(ctx context.Context) cases.Interface {
	return &shrinkInstanceGroup{}
}

func (c *shrinkInstanceGroup) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("nil baseClient")
	}

	logger.WithValues("case", "shrinkInstanceGroup").Infof(ctx, "json raw config: %s", string(config))
	if err := json.Unmarshal(config, &c.config); err != nil {
		return err
	}
	logger.WithValues("case", "shrinkInstanceGroup").Infof(ctx, "config: %s", utils.ToJSON(c.config))

	c.base = base
	return nil
}

func (c *shrinkInstanceGroup) Name() cases.CaseName {
	return ShrinkInstanceGroup
}

func (c *shrinkInstanceGroup) Desc() string {
	return "缩容节点组"
}

func (c *shrinkInstanceGroup) Check(ctx context.Context) ([]cases.Resource, error) {
	logger := logger.WithValues("case", "shrinkInstanceGroup")

	createInstanceGroupClient := &createInstanceGroup{
		base: c.base,
		config: createInstanceGroupConfig{
			Request: ccev2.CreateInstanceGroupRequest{
				InstanceGroupSpec: *c.config.CreateInstanceGroupConfig,
			},
			NeedClean: false,
		},
	}
	logger.WithValues("case", "shrinkInstanceGroup").Infof(ctx, "create instanceGroup request: %s", utils.ToJSON(createInstanceGroupClient.config.Request))

	resources, err := createInstanceGroupClient.Check(ctx)
	if err != nil {
		logger.Errorf(ctx, "failed to create instanceGroup, err: %v", err)
		return resources, nil
	}

	if len(resources) == 0 {
		logger.Errorf(ctx, "no resource return by createInstanceGroupClient")
		return resources, errors.New("no resource return by createInstanceGroupClient")
	}

	var instanceGroupID string
	for _, r := range resources {
		if r.Type == cases.ResourceTypeCCEInstanceGroup {
			instanceGroupID = r.ID
			c.instanceGroupID = instanceGroupID
			break
		}
	}

	if instanceGroupID == "" {
		return resources, errors.New("empty instanceGroupID")
	}

	for _, toReplicas := range c.config.ToReplicas {
		logger.WithValues("toReplicas", toReplicas).Infof(ctx, "start shrinking case")
		igResp, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, instanceGroupID, nil)
		if err != nil {
			logger.WithValues("instanceGroupID", instanceGroupID).Errorf(ctx, "failed to get instanceGroup, err: %v", err)
			return resources, err
		}

		if igResp.InstanceGroup.Spec.Replicas <= toReplicas.Replicas {
			logger.WithValues("now replicas", igResp.InstanceGroup.Spec.Replicas).
				WithValues("to replicas", toReplicas.Replicas).
				Errorf(ctx, "shrink instanceGroup config error")
			return resources, err
		}

		listResp, err := c.base.CCEClient.ListInstancesByInstanceGroupID(ctx, c.base.ClusterID, instanceGroupID, 0, 0, nil)
		if err != nil {
			logger.WithValues("instanceGroupID", instanceGroupID).Errorf(ctx, "failed to list instance in instanceGroup, err: %v", err)
			return resources, err
		}
		if len(listResp.Page.List) < igResp.InstanceGroup.Spec.Replicas-toReplicas.Replicas {
			logger.WithValues("list instance count", len(listResp.Page.List)).
				WithValues("instanceGroup replicas", igResp.InstanceGroup.Spec.Replicas).
				Errorf(ctx, "not enough instance to run the shrink test case")
			return resources, errors.New("not enough instance to run the shrink test case")
		}

		// 读取节点组的task，确保处于done, 放在缩容前
		err = func() error {
			ticker := time.NewTicker(3 * time.Second)
			defer ticker.Stop()
			timer := time.NewTimer(4 * time.Minute)
			defer timer.Stop()
			for {
				select {
				case <-ticker.C:
					listTaskRes, err := c.base.CCEHostClient.ListTasks(ctx, ccetypes.TaskTypeInstanceGroupReplicas, ccev2.ListTaskOption{
						TargetID: instanceGroupID,
					}, nil)
					if err != nil {
						logger.WithValues("instanceGroupID", instanceGroupID).
							Errorf(ctx, "failed to list task in instanceGroup, err: %v", err)
						return errors.New("failed to list task in instanceGroup")
					}
					if listTaskRes == nil || listTaskRes.Page.TotalCount == 0 {
						logger.WithValues("instanceGroupID", instanceGroupID).
							Errorf(ctx, "task number is 0 in instanceGroup, err: %v", err)
						return errors.New("task number is 0  in instanceGroup")
					}
					for _, task := range listTaskRes.Page.Items {
						if task.Phase != string(ccetypes.TaskProcessPhaseDone) {
							logger.WithValues("taskID", task.ID).Infof(ctx, "instance already shrunk but task is not done")
							continue
						}
					}
					return nil
				case <-timer.C:
					logger.Errorf(ctx, "timeout waiting instanceGroup task finish")
					return errors.New("timeout waiting instanceGroup task finish")
				}
			}
		}()
		if err != nil {
			return resources, err
		}

		var instancesBeforeShrink []string
		for _, instance := range listResp.Page.List {
			instancesBeforeShrink = append(instancesBeforeShrink, instance.Spec.CCEInstanceID)
		}

		request := ccev2.UpdateInstanceGroupReplicasRequest{
			Replicas: toReplicas.Replicas,
		}
		if toReplicas.SpecifyInstances {
			for i := 0; i < igResp.InstanceGroup.Spec.Replicas-toReplicas.Replicas; i++ {
				request.InstanceIDs = append(request.InstanceIDs, listResp.Page.List[i].Spec.CCEInstanceID)
			}
		}

		if toReplicas.DeleteInstances {
			request.DeleteInstance = true
			request.DeleteOption = &ccetypes.DeleteOption{
				MoveOut:           false,
				DeleteResource:    true,
				DeleteCDSSnapshot: true,
			}
		}

		updateResp, err := c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, c.base.ClusterID, instanceGroupID, &request, nil)
		if err != nil {
			logger.WithValues("instanceGroupID", instanceGroupID).
				WithValues("request", request).
				WithValues("resp", updateResp).
				Errorf(ctx, "failed to update instanceGroup replicas, err: %v", err)
			return resources, err
		}

		specifiedInstances := make(map[string]struct{})
		for _, instanceID := range request.InstanceIDs {
			specifiedInstances[instanceID] = struct{}{}
		}

		err = func() error {
			ticker := time.NewTicker(5 * time.Second)
			defer ticker.Stop()
			timer := time.NewTimer(20 * time.Minute)
			defer timer.Stop()

			for {
				select {
				case <-ticker.C:
					igResp, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, instanceGroupID, nil)
					if err != nil {
						logger.WithValues("instanceGroupID", instanceGroupID).Errorf(ctx, "failed to get instanceGroup, err: %v", err)
						continue
					}

					if igResp.InstanceGroup.Spec.Replicas != toReplicas.Replicas {
						logger.WithValues("instanceGroupID", instanceGroupID).
							Errorf(ctx, "spec replicas should be %d not %d", toReplicas.Replicas, igResp.InstanceGroup.Spec.Replicas)
						return fmt.Errorf("spec replicas should be %d not %d", toReplicas.Replicas, igResp.InstanceGroup.Spec.Replicas)
					}

					if igResp.InstanceGroup.Spec.Replicas != igResp.InstanceGroup.Status.ActualReplicas {
						logger.WithValues("instanceGroupID", instanceGroupID).Infof(ctx, "still scaling")
						continue
					}

					actuallyShrinkedInstances := make(map[string]struct{})
					listResp, err := c.base.CCEClient.ListInstancesByInstanceGroupID(ctx, c.base.ClusterID, instanceGroupID, 0, 0, nil)
					if err != nil {
						logger.WithValues("instanceGroupID", instanceGroupID).Errorf(ctx, "failed to list instance in instanceGroup, err: %v", err)
						return err
					}

					// 为了防止节点组接口和节点组列表接口信息的不同步
					if listResp.Page.TotalCount != igResp.InstanceGroup.Status.ActualReplicas {
						logger.WithValues("instanceGroupID", instanceGroupID).Infof(ctx, "instance list still syncing")
						continue
					}

					instancesAfterShrink := make(map[string]struct{})
					for _, instance := range listResp.Page.List {
						instancesAfterShrink[instance.Spec.CCEInstanceID] = struct{}{}
					}

					for _, instance := range instancesBeforeShrink {
						if _, found := instancesAfterShrink[instance]; !found {
							actuallyShrinkedInstances[instance] = struct{}{}
						}
					}

					if toReplicas.SpecifyInstances {
						if !cmp.Equal(specifiedInstances, actuallyShrinkedInstances) {
							logger.WithValues("diff shrink instances", cmp.Diff(specifiedInstances, actuallyShrinkedInstances)).
								Errorf(ctx, "different between specified and actually shrink instances")
							return errors.New("different between specified and actually shrink instances")
						}
					}

					listAllInstancesResp, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
						PageNo:   1,
						PageSize: 1000,
					}, nil)
					if err != nil {
						logger.WithValues("clusterID", c.base.ClusterID).
							Errorf(ctx, "failed to list all instances, err: %v", err)
						return err
					}
					allInstances := make(map[string]struct{})
					for _, instance := range listAllInstancesResp.InstancePage.InstanceList {
						allInstances[instance.Spec.CCEInstanceID] = struct{}{}
					}
					if toReplicas.DeleteInstances {
						for instanceID := range actuallyShrinkedInstances {
							if _, found := allInstances[instanceID]; found {
								logger.WithValues("instanceID", instanceID).Errorf(ctx, "instance should be deleted after shrink")
								return errors.New("instance should be deleted after shrink")
							}
						}
					} else {
						for instanceID := range actuallyShrinkedInstances {
							c.remainInstanceIDs = append(c.remainInstanceIDs, instanceID)
							if _, found := allInstances[instanceID]; !found {
								logger.WithValues("instanceID", instanceID).Errorf(ctx, "instance should exist after shrink")
								return errors.New("instance should exist after shrink")
							}
						}
					}
					return nil
				case <-timer.C:
					logger.Errorf(ctx, "timeout waiting instanceGroup shrinking")
					return errors.New("timeout waiting instanceGroup shrinking")
				}
			}
		}()
		if err != nil {
			return resources, err
		}
	}

	instanceGroup, initErr := common.NewInstanceGroup(ctx, c.base, c.instanceGroupID, time.Second*10, time.Minute*8)
	if initErr != nil {
		return nil, fmt.Errorf("create instance group %v failed: %v", c.instanceGroupID, initErr)
	}
	err = instanceGroup.AreTasksCompleted(ctx)
	if err != nil {
		return nil, fmt.Errorf("instance group %v tasks are not completed: %v", c.instanceGroupID, err)
	}

	return resources, nil
}

func (c *shrinkInstanceGroup) Clean(ctx context.Context) error {
	// 清理测试节点组
	_, err := c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, true, nil)
	if err != nil {
		logger.Errorf(ctx, "delete instanceGroup %s failed: %v", c.instanceGroupID, err.Error())
		return fmt.Errorf(err.Error())
	}

	// 清理被移出节点组，但仍保留在集群的节点
	if c.remainInstanceIDs != nil {
		_, err = c.base.CCEClient.DeleteInstances(ctx, c.base.ClusterID, &ccev2.DeleteInstancesRequest{
			InstanceIDs: c.remainInstanceIDs,
			DeleteOption: &ccetypes.DeleteOption{
				MoveOut:           false,
				DeleteResource:    true,
				DeleteCDSSnapshot: true,
			},
		}, nil)
		if err != nil {
			logger.Errorf(ctx, "delete remain instances from instanceGroup failed: %v", err.Error())
			return fmt.Errorf(err.Error())
		}
	}

	return nil
}

func (c *shrinkInstanceGroup) Continue(ctx context.Context) bool {
	return false
}

func (c *shrinkInstanceGroup) ConfigFormat() string {
	str, _ := json.Marshal(c.config)
	return string(str)
}
