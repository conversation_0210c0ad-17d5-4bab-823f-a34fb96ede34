// 20241018, by s<PERSON><PERSON>@baidu,com, create

/*创建不同的节点组
将一个节点组节点移入另一个节点组
报错信息正确返回
*/

package instancegroup

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	AttachNodeIntoOtherIGroup cases.CaseName = "AttachNodeIntoOtherIGroup"
	BCCMachineType                           = "BCC"
)

type attachNodeIntoOtherIGroup struct {
	base                  *cases.BaseClient
	firstInstanceGroupID  string
	secondInstanceGroupID string
	config                attachNodeIntoOtherIGroupConfig
}

type attachNodeIntoOtherIGroupConfig struct {
	CreateInstanceGroupConfig *ccetypes.InstanceGroupSpec `json:"createInstanceGroupConfig"`
}

func init() {
	cases.AddCase(context.TODO(), AttachNodeIntoOtherIGroup, NewAttachNodeIntoOtherIGroup)
}

func NewAttachNodeIntoOtherIGroup(ctx context.Context) cases.Interface {
	return &attachNodeIntoOtherIGroup{}
}

func (c *attachNodeIntoOtherIGroup) Name() cases.CaseName { return AttachNodeIntoOtherIGroup }

func (c *attachNodeIntoOtherIGroup) Desc() string { return "移入其他节点组节点至节点组" }

func (c *attachNodeIntoOtherIGroup) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	var moveConfig attachNodeIntoOtherIGroupConfig
	logger.Infof(ctx, "json raw config: %s", string(config))
	if err := json.Unmarshal(config, &moveConfig); err != nil {
		return err
	}
	c.config = moveConfig
	logger.Infof(ctx, "config: %s", utils.ToJSON(c.config))
	return nil
}

func (c *attachNodeIntoOtherIGroup) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterName := c.base.ClusterID
	// 创建节点组 first-test-group
	logger.Infof(ctx, "create first-test-group ")
	c.firstInstanceGroupID, err = c.CreatAndCheckIG(ctx, clusterName)
	if err != nil {
		return nil, fmt.Errorf("create and check instancegroup failed: %v", err)
	}

	listRes, err := c.base.CCEClient.ListInstancesByInstanceGroupID(ctx, clusterName, c.firstInstanceGroupID, 0, 0, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to list instances by instance group %v : %v", c.firstInstanceGroupID, err)
	}

	// 选取这个节点组中的一个节点
	if len(listRes.Page.List) == 0 {
		return nil, fmt.Errorf("nil instances in instance group %v", c.firstInstanceGroupID)
	}
	moveNodeID := listRes.Page.List[0]

	// 创建节点组  second-test-group
	logger.Infof(ctx, "create second-test-group ")
	c.config.CreateInstanceGroupConfig.InstanceGroupName = c.config.CreateInstanceGroupConfig.InstanceGroupName + "second"
	c.config.CreateInstanceGroupConfig.Replicas = 0

	c.secondInstanceGroupID, err = c.CreatAndCheckIG(ctx, clusterName)
	if err != nil {
		return nil, fmt.Errorf("create and check instancegroup failed: %v", err)
	}

	logger.Infof(ctx, " attach node %v from instancegroup %v into instancegroup %v", moveNodeID.Spec.InstanceName, c.firstInstanceGroupID, c.secondInstanceGroupID)

	existedInstanceInClusterConfig := []*ccev2.ExistedInstanceInCluster{
		{
			ExistedInstanceID: moveNodeID.Status.Machine.InstanceID,
			MachineType:       BCCMachineType,
			Ip:                moveNodeID.Status.Machine.K8SNodeName,
			Name:              moveNodeID.Status.Machine.VPCIP,
			InstanceName:      moveNodeID.Spec.InstanceName,
		},
	}

	_, err = c.base.CCEHostClient.AttachInstancesToInstanceGroup(ctx, clusterName, c.secondInstanceGroupID, &ccev2.AttachInstancesToInstanceGroupRequest{
		Incluster:                 true,
		ExistedInstancesInCluster: existedInstanceInClusterConfig,
	}, nil)

	// /报错信息修改为最新的
	expectedErrMessage := "instance status unavailable"

	if err != nil {
		if strings.Contains(err.Error(), expectedErrMessage) {
			logger.Infof(ctx, "attach node %v into anotherIG %v expected err message! ", moveNodeID.Status.Machine.K8SNodeName, c.secondInstanceGroupID)
			return nil, nil
		}
		return nil, fmt.Errorf("attach node into anotherIG unexpected error:%v", err)
	}
	return nil, errors.New(" attach node into anotherIG illegal ")
}

func (c *attachNodeIntoOtherIGroup) Clean(ctx context.Context) error {
	// 清理的是group

	igs := []string{c.firstInstanceGroupID, c.secondInstanceGroupID}
	for _, ig := range igs {
		instanceGroup, initErr := common.NewInstanceGroup(ctx, c.base, ig, time.Second*10, time.Minute*8)
		if initErr != nil {
			return fmt.Errorf("create instance group %v failed: %v", ig, initErr)
		}
		err := instanceGroup.AreTasksCompleted(ctx)
		if err != nil {
			return fmt.Errorf("instance group %v tasks are not completed: %v", ig, err)
		}
	}

	_, err := c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.firstInstanceGroupID, true, nil)
	if err != nil {
		return fmt.Errorf("delete instance group %v failed:%v", c.firstInstanceGroupID, err)
	}
	_, err = c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.secondInstanceGroupID, true, nil)
	if err != nil {
		return fmt.Errorf("delete instance group %v failed:%v", c.secondInstanceGroupID, err)
	}

	return nil
}

func (c *attachNodeIntoOtherIGroup) Continue(ctx context.Context) bool {
	return true
}

func (c *attachNodeIntoOtherIGroup) ConfigFormat() string { return "" }

func (c *attachNodeIntoOtherIGroup) CreatAndCheckIG(ctx context.Context, clusterName string) (instanceGroupID string, err error) {
	creatNewInstanceGroup, creatErr := c.base.CCEClient.CreateInstanceGroup(ctx, clusterName, &ccev2.CreateInstanceGroupRequest{
		InstanceGroupSpec: *c.config.CreateInstanceGroupConfig,
	}, nil)

	if creatErr != nil {
		err = fmt.Errorf("instancegroup create falied : %v", creatErr)
		return
	}

	instanceGroupID = creatNewInstanceGroup.InstanceGroupID

	instanceGroup, err := common.NewInstanceGroup(ctx, c.base, instanceGroupID, time.Second*10, time.Minute*8)
	if err != nil {
		err = fmt.Errorf("failed to init instancegroup client:%v", err)
		return
	}

	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		err = fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
		return
	}

	return instanceGroupID, nil
}
