package instancegroup

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	CreateHPASInstanceGroup cases.CaseName = "CreateHPASInstanceGroup"
)

type createHPASInstanceGroup struct {
	base            *cases.BaseClient
	instanceGroupID string
	config          createHPASInstanceGroupConfig
}

type createHPASInstanceGroupConfig struct {
	CreateInstanceGroupConfig *ccetypes.InstanceGroupSpec `json:"createInstanceGroupConfig"`
}

func init() {
	cases.AddCase(context.TODO(), CreateHPASInstanceGroup, NewCreateHPASInstanceGroup)
}

func NewCreateHPASInstanceGroup(ctx context.Context) cases.Interface {
	return &createHPASInstanceGroup{}
}

func (c *createHPASInstanceGroup) Name() cases.CaseName {
	return CreateHPASInstanceGroup
}

func (c *createHPASInstanceGroup) Desc() string {
	return "HPAS节点组"
}

func (c *createHPASInstanceGroup) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base

	var hpasConfig createHPASInstanceGroupConfig
	logger.Infof(ctx, "json raw config: %s", string(config))
	if err = json.Unmarshal(config, &hpasConfig); err != nil {
		return err
	}
	c.config = hpasConfig
	logger.Infof(ctx, "config: %s", utils.ToJSON(c.config))
	return nil
}

func (c *createHPASInstanceGroup) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID

	// //创建节点组
	// // 使用CCEClient创建节点组示例
	// createReq := &ccev2.CreateInstanceGroupRequest{
	//	InstanceGroupSpec: ccetypes.InstanceGroupSpec{
	//		Replicas:          0,
	//		CleanPolicy:       ccetypes.DeleteCleanPolicy,
	//		ShrinkPolicy:      ccetypes.PriorityShrinkPolicy,
	//		InstanceGroupName: "test-hpas-ig",
	//		InstanceTemplates: []ccetypes.InstanceTemplate{
	//			{
	//				InstanceSpec: ccetypes.InstanceSpec{
	//					MachineType:  ccetypes.MachineTypeHPAS,
	//					InstanceType: bcc.InstanceTypeHPAS,
	//					VPCConfig: ccetypes.VPCConfig{
	//						VPCSubnetID:       "sbn-dxncvx41j3uw",
	//						SecurityGroupType: ccetypes.SecurityGroupTypeNormal,
	//						SecurityGroupID:   "g-vth3ep7nsipu",
	//					},
	//					EhcClusterID: "ehc-78dRy9pR",
	//					InstanceResource: ccetypes.InstanceResource{
	//						MachineSpec: "llama2_13B_train/8k",
	//					},
	//					HPASOption: ccetypes.HPASOption{
	//						AppType:             ccetypes.HPASAppTypeLlama13bTrain,
	//						AppPerformanceLevel: "8k",
	//					},
	//					AdminPassword: "xxxxxxxxxxx@1234",
	//					ImageID:       "m-6TQswGfH",
	//					InstanceOS: ccetypes.InstanceOS{
	//						ImageType: bccimage.ImageTypeGPUSystem,
	//					},
	//					DeployCustomConfig: ccetypes.DeployCustomConfig{
	//						KubeletRootDir: "/var/lib/kubelet1111",
	//						ContainerdConfig: ccetypes.ContainerdConfig{
	//							DataRoot: "/var/lib/kubelet11111",
	//						},
	//						ContainerLogMaxFiles:           func(maxFiles int32) *int32 { return &maxFiles }(5),
	//						PreUserScript:                  "aGVsbG9fcHJl",
	//						PostUserScript:                 "aGVsbG9fcG9zdA==",
	//						PostUserScriptFailedAutoCordon: true,
	//						EnableCordon:                   true,
	//					},
	//					RuntimeType:    ccetypes.RuntimeTypeContainerd,
	//					RuntimeVersion: "1.6.36",
	//					Labels: map[string]string{
	//						"a": "a",
	//						"cce.baidubce.com/gpu-share-device-plugin": "disable",
	//					},
	//					Annotations: map[string]string{
	//						"b": "b",
	//					},
	//					Tags: ccetypes.TagList{
	//						{
	//							TagKey:   "test",
	//							TagValue: "bci",
	//						},
	//					},
	//					Taints: ccetypes.InstanceTaints{
	//						{
	//							Key:    "a",
	//							Value:  "a",
	//							Effect: v1.TaintEffectNoSchedule,
	//						},
	//					},
	//				},
	//			},
	//		},
	//	},
	// }

	createReq := &ccev2.CreateInstanceGroupRequest{
		InstanceGroupSpec: *c.config.CreateInstanceGroupConfig,
	}

	// 调用CCEClient创建接口
	ig, err := c.base.CCEClient.CreateInstanceGroup(ctx, clusterID, createReq, nil)
	if err != nil {
		logger.Errorf(ctx, "Failed to create instance group via CCEClient, err %v ", err)
		return nil, fmt.Errorf("CCEClient create instance group failed: %v", err)
	}
	c.instanceGroupID = ig.InstanceGroupID

	logger.Infof(ctx, "Instance group created via CCEClient %v", c.instanceGroupID)

	// 校验下参数吧

	// 调用CCEClient获取节点组
	instanceGroup, err := c.base.CCEClient.GetInstanceGroup(ctx, clusterID, c.instanceGroupID, nil)
	if err != nil || instanceGroup == nil {
		logger.Errorf(ctx, "Failed to get instance group by ID %v, err: %v", c.instanceGroupID, err)
		return nil, fmt.Errorf("get instance group failed: %v", err)
	}

	// 返回获取到的节点组信息
	err = c.ValidityParameters(ctx, createReq, instanceGroup.InstanceGroup)
	if err != nil {
		return nil, fmt.Errorf("instance group validity parameters failed: %v", err)
	}

	logger.Infof(ctx, "Instance group validity parameters successfully")

	return nil, nil
}

func (c *createHPASInstanceGroup) Clean(ctx context.Context) error {
	// 清理节点组
	_, err := c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, true, nil)
	if err != nil {
		logger.Errorf(ctx, "Failed to delete instance group by ID %v, err: %v", c.instanceGroupID, err)
		return fmt.Errorf("delete instance group failed: %v", err)
	}

	return nil
}

func (c *createHPASInstanceGroup) Continue(ctx context.Context) bool {
	return true
}

func (c *createHPASInstanceGroup) ConfigFormat() string {
	return ""
}

func (c *createHPASInstanceGroup) ValidityParameters(ctx context.Context, req *ccev2.CreateInstanceGroupRequest, instanceGroup *ccev2.InstanceGroup) error {
	if req == nil || instanceGroup == nil {
		return errors.New("req or instanceGroup is nil")
	}

	if len(req.InstanceGroupSpec.InstanceTemplates) != len(req.InstanceGroupSpec.InstanceTemplates) {
		return errors.New("instance templates are not match")
	}

	logger.Infof(ctx, "begin to check instance group validity")
	for i := range req.InstanceGroupSpec.InstanceTemplates {
		// 校验MachineType
		if req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.MachineType != instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.MachineType {
			return fmt.Errorf("instance template %d machine type is not equal as HPAS, req is %v, ig is %v", i,
				req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.MachineType, instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.MachineType)
		}

		// 校验hpas option
		if req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.HPASOption != *instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.HPASOption {
			return fmt.Errorf("instance template %d hpas option is not equal, req is %v, ig is %v", i,
				req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.HPASOption, *instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.HPASOption)
		}

		// 检验实例规格
		if req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.InstanceResource.MachineSpec != instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.InstanceResource.MachineSpec {
			return fmt.Errorf("instance template %d machine spec is not equal, req is %v, ig is %v", i,
				req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.InstanceResource.MachineSpec, instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.InstanceResource.MachineSpec)
		}

		// 校验镜像
		if req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.ImageID != instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.ImageID {
			return fmt.Errorf("instance template %d image id is not equal, req is %v, ig is %v", i,
				req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.ImageID, instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.ImageID)
		}

		// 检验数据目录
		if req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.DeployCustomConfig.KubeletRootDir != instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.DeployCustomConfig.KubeletRootDir {
			return fmt.Errorf("instance template %d kubelet root dir is not equal, req is %v, ig is %v", i,
				req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.DeployCustomConfig.KubeletRootDir, instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.DeployCustomConfig.KubeletRootDir)
		}

		// 校验容器数据目录
		if req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.DeployCustomConfig.ContainerdConfig.DataRoot != instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.DeployCustomConfig.ContainerdConfig.DataRoot {
			return fmt.Errorf("instance template %d containerd data root is not equal, req is %v, ig is %v", i,
				req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.DeployCustomConfig.ContainerdConfig.DataRoot, instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.DeployCustomConfig.ContainerdConfig.DataRoot)
		}

		// 校验前置脚本
		if req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.DeployCustomConfig.PreUserScript != instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.DeployCustomConfig.PreUserScript {
			return fmt.Errorf("instance template %d pre install script is not equal, req is %v, ig is %v", i,
				req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.DeployCustomConfig.PreUserScript, instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.DeployCustomConfig.PreUserScript)
		}

		// 校验后置脚本
		if req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.DeployCustomConfig.PostUserScript != instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.DeployCustomConfig.PostUserScript {
			return fmt.Errorf("instance template %d post install script is not equal, req is %v, ig is %v", i,
				req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.DeployCustomConfig.PostUserScript, instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.DeployCustomConfig.PostUserScript)
		}

		// 检验kubelet参数---ContainerLogMaxFiles
		if *req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.DeployCustomConfig.ContainerLogMaxFiles != *instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.DeployCustomConfig.ContainerLogMaxFiles {
			return fmt.Errorf("instance template %d kubelet extra args is not equal, req is %v, ig is %v", i,
				*req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.DeployCustomConfig.ContainerLogMaxFiles, *instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.DeployCustomConfig.ContainerLogMaxFiles)
		}

		// 检验标签
		logger.Infof(ctx, "validating labels, mp1 is instanceGroup mp2 is req")
		err := c.ValidateMap(ctx,
			instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.Labels,
			req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.Labels)
		if err != nil {
			return err
		}

		// 检验注解
		logger.Infof(ctx, "validating annotations, mp1 is instanceGroup mp2 is req")
		err = c.ValidateMap(ctx,
			instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.Annotations,
			req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.Annotations)
		if err != nil {
			return err
		}

		// 检验资源标签
		logger.Infof(ctx, "validating annotations, covert tagList to map first")
		reqTagListMap := c.ConvertTagListToMap(ctx, req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.Tags)
		igTagListMap := c.ConvertTagListToMap(ctx, instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.Tags)
		logger.Infof(ctx, "reqTagListMap is %v and igTagListMap is %v", reqTagListMap, igTagListMap)
		err = c.ValidateMap(ctx, reqTagListMap, igTagListMap)
		if err != nil {
			return err
		}

		// 检验污点
		reqTaintMap := c.ConvertTaintToMap(ctx, req.InstanceGroupSpec.InstanceTemplates[i].InstanceSpec.Taints)
		igTaintMap := c.ConvertTaintToMap(ctx, instanceGroup.Spec.InstanceTemplates[i].InstanceSpec.Taints)
		logger.Infof(ctx, "reqTaintMap is %v and igTaintMap is %v", reqTaintMap, igTaintMap)
		err = c.ValidateMap(ctx, reqTaintMap, igTaintMap)
		if err != nil {
			return err
		}
	}

	return nil
}

func (c *createHPASInstanceGroup) ValidateMap(ctx context.Context, mp1, mp2 map[string]string) error {
	for key, value2 := range mp2 {
		if value1, ok := mp1[key]; ok {
			if value1 != value2 {
				return fmt.Errorf("map is not equal, mp1 key %v value %v, mp2 key %v value %v",
					key, value2, value1, mp1[key])
			}
		}
	}
	logger.Infof(ctx, "mp1 %v containers mp2 %v", mp1, mp2)
	return nil
}

// taglist--->map
func (c *createHPASInstanceGroup) ConvertTagListToMap(ctx context.Context, tags1 ccetypes.TagList) map[string]string {
	logger.Infof(ctx, "convert tag list to map")
	tagToMap := make(map[string]string)

	for _, tag := range tags1 {
		key := tag.TagKey + ":" + tag.TagValue
		tagToMap[key] = tag.TagKey
	}

	return tagToMap
}

// taint--->map
func (c *createHPASInstanceGroup) ConvertTaintToMap(ctx context.Context, taints1 ccetypes.InstanceTaints) map[string]string {
	logger.Infof(ctx, "convert taint list to map")
	taintToMap := make(map[string]string)

	for _, taint := range taints1 {
		key := taint.Key + ":" + taint.Value + ":" + string(taint.Effect)
		taintToMap[key] = taint.Key
	}

	return taintToMap
}
