/*
modification history
--------------------
2024/10/31, by shimingming<PERSON>, create
*/
/*
DESCRIPTION
场景：当部署后脚本执行失败并且开启失败后封锁节点，验证正常封锁节点并且优先级大于cordon选项，修改为成功脚本验证节点扩容并且为非cordon状态。
1. 当部署后脚本执行失败，验证节点能正常创建。
2. 当部署后脚本执行失败且开启失败后封锁节点，验证在节点创建后节点处于被cordon状态，SchedulingDisabled。
3. 如果没有开启cordon选项，失败后封锁节点的优先级大于cordon选项。
4. 当部署前脚本失败，节点会创建失败，验证此节点状态为创建失败。
*/

package instancegroup

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	CheckInstanceGroupUserScript cases.CaseName = "CheckInstanceGroupUserScript"
)

var (
	failedUserScript  = base64.StdEncoding.EncodeToString([]byte("exit 1"))
	successUserScript = base64.StdEncoding.EncodeToString([]byte("exit 0"))
)

const (
	nodeIGCaseCheckLabel = "ig-case-check-status"
	nodeIGCaseChecked    = "checked"
	nodeIGCaseUnChecked  = "unchecked"
)

type checkInstanceGroupConfig struct {
	CreateInstanceGroupRequest ccev2.CreateInstanceGroupRequest `json:"createInstanceGroupRequest"`
}

type checkInstanceGroupCustomConfig struct {
	base            *cases.BaseClient
	config          checkInstanceGroupConfig
	instanceGroupID string
}

func init() {
	cases.AddCase(context.TODO(), CheckInstanceGroupUserScript, NewCheckInstanceGroupUserScript)
}

func NewCheckInstanceGroupUserScript(ctx context.Context) cases.Interface {
	return &checkInstanceGroupCustomConfig{}
}

func (c *checkInstanceGroupCustomConfig) Name() cases.CaseName {
	return CheckInstanceGroupUserScript
}

func (c *checkInstanceGroupCustomConfig) Desc() string {
	return "检查执行脚本异常对于创建节点组的影响"
}

func (c *checkInstanceGroupCustomConfig) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	var createInstanceGroupRequest checkInstanceGroupConfig
	err = json.Unmarshal(config, &createInstanceGroupRequest)
	if err != nil {
		err = fmt.Errorf("unmarshal create instance group request error: %v", err)
		return
	}
	c.base = base
	c.config = createInstanceGroupRequest
	return
}

func (c *checkInstanceGroupCustomConfig) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID
	loggerID := ctx.Value(logger.RequestID).(string)
	if loggerID == "" {
		loggerID = logger.GetUUID()
	}
	shortUUID := loggerID[:5]

	createRequest := c.config.CreateInstanceGroupRequest
	// 随机名称避免冲突
	createRequest.InstanceGroupName = fmt.Sprintf("test-ig-%s", shortUUID)
	// 设置执行脚本失败后自动cordon
	createRequest.InstanceTemplate.DeployCustomConfig.PostUserScriptFailedAutoCordon = true
	// 关闭cordon选项
	createRequest.InstanceTemplate.DeployCustomConfig.EnableCordon = false
	// 补充节点子网
	if createRequest.InstanceTemplate.VPCConfig.VPCSubnetID == "" {
		// 获取集群额外的信息并获取可用节点子网
		clusterExtraInfo, getExtraInfoErr := c.base.CCEHostClient.GetClusterExtraInfo(ctx, clusterID, nil)
		if getExtraInfoErr != nil {
			err = fmt.Errorf("CCEHostClient.GetClusterExtraInfo failed, err: %v", getExtraInfoErr)
			return
		}
		var nodeSubnetID string
		subnets := clusterExtraInfo.Subnets
		for _, subnet := range subnets {
			if subnet.SubnetCIDR != "" {
				nodeSubnetID = subnet.SubnetID
				break
			}
		}
		if nodeSubnetID == "" {
			err = errors.New("no subnet id found in cluster extra info")
			return
		}
		logger.Infof(ctx, "node subnet id: %s", nodeSubnetID)
		createRequest.InstanceTemplate.VPCConfig.VPCSubnetID = nodeSubnetID
	}

	// 1. 创建一个包含失败脚本的节点组，验证创建操作 + 失败脚本的组合
	logger.Infof(ctx, "start to test create instance group with failed post user script")
	// 设置失败脚本
	createRequest.InstanceTemplate.DeployCustomConfig.PostUserScript = failedUserScript
	// 由于后续两次校验的节点来自同一个节点组，但判断标准不一样，所以设置检查标签，用于区分是否检查过
	createRequest.InstanceTemplate.Labels = map[string]string{nodeIGCaseCheckLabel: nodeIGCaseUnChecked}

	// 发起创建节点组的请求
	createRes, err := c.base.CCEClient.CreateInstanceGroup(ctx, c.base.ClusterID, &createRequest, nil)
	if err != nil {
		err = fmt.Errorf("CCEClient.CreateInstanceGroup error: %v", err)
		return
	}
	instanceGroupID := createRes.InstanceGroupID
	c.instanceGroupID = instanceGroupID
	logger.Infof(ctx, "create instance group id: %s", instanceGroupID)

	// 校验结果
	err = c.checkResultByCordonStatus(ctx, true)
	if err != nil {
		return
	}
	logger.Infof(ctx, "nodes are cordoned, check success")

	logger.Infof(ctx, "start to test edit instance group with success post user script")
	err = c.updateInstanceGroupConfigWithUserScript(ctx, successUserScript, successUserScript)
	if err != nil {
		return nil, fmt.Errorf("edit instance group with success post user script error: %v", err)
	}

	// 校验结果
	err = c.checkResultByCordonStatus(ctx, false)
	if err != nil {
		return
	}
	logger.Infof(ctx, "nodes are not cordoned, check success")

	//获取下当前时刻节点组的节点数
	ig, listErr := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, nil)
	if listErr != nil {
		return nil, fmt.Errorf("get instanceGroup  %v error: %v", c.instanceGroupID, err)
	}
	preIGReplicas := ig.InstanceGroup.Spec.Replicas

	//前置脚本失败，创建节点创建失败，至少需等待6分钟
	logger.Infof(ctx, "start to test edit instance group with failed pre user script")
	err = c.updateInstanceGroupConfigWithUserScript(ctx, failedUserScript, successUserScript)
	if err != nil {
		return nil, fmt.Errorf("edit instance group with failed pre user script error: %v", err)
	}

	//等待节点组扩容开始，只需判断节点组扩容的节点出现就行
	waitErr := common.WaitForFunc(ctx, func(ctx context.Context) error {
		iGInstances, listInstanceErr := c.base.CCEClient.ListInstancesByInstanceGroupID(ctx, c.base.ClusterID, c.instanceGroupID, 0, 0, nil)
		if listInstanceErr != nil {
			return fmt.Errorf("get instanceGroup  %v error: %v", c.instanceGroupID, err)
		}

		if iGInstances.Page.TotalCount <= preIGReplicas {
			return fmt.Errorf("instances number %v  is less than preInstanceNum %v", ig.InstanceGroup.Spec.Replicas, preIGReplicas)
		}
		return nil
	}, time.Second*5, time.Minute*5)
	if waitErr != nil {
		return nil, fmt.Errorf("wait instance group scale up time out: %v", waitErr)
	}

	//检查节点状态
	listRes, err := c.base.CCEClient.ListInstancesByPage(ctx, clusterID, &ccev2.ListInstancesByPageParams{
		KeywordType: ccev2.InstanceKeywordTypeInstanceGroupID,
		Keyword:     c.instanceGroupID,
		OrderBy:     ccev2.InstanceOrderByCreatedAt,
		Order:       ccev2.OrderDESC,
		ClusterRole: ccetypes.ClusterRoleNode,
		PageSize:    10,
		PageNo:      1,
	}, nil)

	if err != nil {
		return nil, fmt.Errorf("list instances by instanceGroup %v error: %v", c.instanceGroupID, err)
	}

	if len(listRes.InstancePage.InstanceList) == 0 {
		return nil, fmt.Errorf("list instances by instanceGroup %v error: %v", c.instanceGroupID, err)
	}
	instanceID := listRes.InstancePage.InstanceList[0].Spec.CCEInstanceID

	waitErr = common.WaitForFunc(ctx, func(ctx context.Context) error {
		instance, getErr := c.base.CCEClient.GetInstance(ctx, c.base.ClusterID, instanceID, nil)
		if getErr != nil {
			return fmt.Errorf("get instance %v error: %v", instanceID, getErr)
		}

		if instance.Instance.Status.InstancePhase != ccetypes.InstancePhaseCreateFailed {
			return fmt.Errorf("instance %v status is not failed: %v", instanceID, instance.Instance.Status.InstancePhase)
		}

		return nil
	}, time.Second*5, time.Minute*10)

	if waitErr != nil {
		return nil, fmt.Errorf("wait instance create failed: %v", waitErr)
	}
	logger.Infof(ctx, "instance create failed with failed pre user script")

	instanceGroup, initErr := common.NewInstanceGroup(ctx, c.base, c.instanceGroupID, time.Second*10, time.Minute*8)
	if initErr != nil {
		return nil, fmt.Errorf("create instance group %v failed: %v", c.instanceGroupID, initErr)
	}
	err = instanceGroup.AreTasksCompleted(ctx)
	if err != nil {
		return nil, fmt.Errorf("instance group %v tasks are not completed: %v", c.instanceGroupID, err)
	}

	return
}

func (c *checkInstanceGroupCustomConfig) Clean(ctx context.Context) (err error) {
	if c.instanceGroupID != "" {
		clusterID := c.base.ClusterID
		_, err = c.base.CCEClient.DeleteInstanceGroup(ctx, clusterID, c.instanceGroupID, true, nil)
		if err != nil {
			err = fmt.Errorf("CCEClient.DeleteInstanceGroup error: %v", err)
			return
		}
	}
	return
}

func (c *checkInstanceGroupCustomConfig) Continue(ctx context.Context) bool {
	return true
}

func (c *checkInstanceGroupCustomConfig) ConfigFormat() string {
	return ""
}

func (c *checkInstanceGroupCustomConfig) checkResultByCordonStatus(ctx context.Context, expectCordoned bool) (err error) {
	// 等待节点组和节点创建完成
	igResource, err := resource.NewInstanceGroup(ctx, c.base, c.instanceGroupID, time.Second*10, time.Minute*6)
	if err != nil {
		return
	}
	err = igResource.CheckResource(ctx)
	if err != nil {
		err = fmt.Errorf("instance group check resource error: %v", err)
		return
	}

	// 验证节点组创建后节点是否被cordon，期望是cordon
	nodes, err := c.base.KubeClient.ListNode(ctx, &kube.ListOptions{
		LabelSelector: map[string]string{
			nodeIGCaseCheckLabel: nodeIGCaseUnChecked,
		},
	})
	if err != nil {
		err = fmt.Errorf("list node error: %v", err)
		return
	}
	if len(nodes.Items) == 0 {
		err = errors.New("instance group node not found")
		return
	}
	logger.Infof(ctx, "instance group postUserScriptFailedAutoCordon is true, expected nodes are %v", expectCordoned)
	for _, node := range nodes.Items {
		if node.Spec.Unschedulable != expectCordoned {
			err = fmt.Errorf("node `%s` is `%v`, expect `%v`", node.Name, node.Spec.Unschedulable, expectCordoned)
			return
		}
		labels := node.GetLabels()
		labels[nodeIGCaseCheckLabel] = nodeIGCaseChecked
		node.SetLabels(labels)
		logger.Infof(ctx, "update node `%s` label to case checked", node.Name)
		_, err = c.base.KubeClient.UpdateNode(ctx, &node)
		if err != nil {
			err = fmt.Errorf("update node label error: %v", err)
			return
		}
	}
	return
}

func (c *checkInstanceGroupCustomConfig) updateInstanceGroupConfigWithUserScript(ctx context.Context, preUserScript, postUserScript string) (err error) {
	// 编辑节点组，设置脚本
	getIGRes, getErr := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, nil)
	if getErr != nil {
		err = fmt.Errorf("CCEClient.GetInstanceGroup failed, err: %v", getErr)
		return
	}
	instanceGroupSpec := getIGRes.InstanceGroup.Spec
	// 存在只有InstanceTemplate的情况，最终都以 InstanceTemplates 为准
	if len(instanceGroupSpec.InstanceTemplates) == 0 {
		instanceGroupSpec.InstanceTemplates = append(instanceGroupSpec.InstanceTemplates, instanceGroupSpec.InstanceTemplate)
	}

	toUpdateInstanceTemplates := make([]ccetypes.InstanceTemplate, 0, len(instanceGroupSpec.InstanceTemplates))
	for _, instanceTemplate := range instanceGroupSpec.InstanceTemplates {
		vpcConfig := instanceTemplate.VPCConfig
		// 修改为成功脚本
		instanceTemplate.DeployCustomConfig.PostUserScript = postUserScript
		instanceTemplate.DeployCustomConfig.PreUserScript = preUserScript

		toUpdateInstanceTemplates = append(toUpdateInstanceTemplates, ccetypes.InstanceTemplate{
			InstanceSpec: ccetypes.InstanceSpec{
				InstanceTemplateID:   instanceTemplate.InstanceTemplateID,
				MachineType:          instanceTemplate.MachineType,
				InstanceType:         instanceTemplate.InstanceType,
				ImageID:              instanceTemplate.ImageID,
				InstanceResource:     instanceTemplate.InstanceResource,
				InstanceChargingType: instanceTemplate.InstanceChargingType,
				VPCConfig: ccetypes.VPCConfig{
					VPCSubnetID:       vpcConfig.VPCSubnetID,
					SecurityGroupType: ccetypes.SecurityGroupType(vpcConfig.SecurityGroupType),
					SecurityGroup: ccetypes.SecurityGroup{
						EnableCCERequiredSecurityGroup: vpcConfig.SecurityGroup.EnableCCERequiredSecurityGroup,
						EnableCCEOptionalSecurityGroup: vpcConfig.SecurityGroup.EnableCCEOptionalSecurityGroup,
						CustomSecurityGroupIDs:         vpcConfig.SecurityGroup.CustomSecurityGroupIDs,
					},
				},
				DeployCustomConfig: instanceTemplate.DeployCustomConfig,
				DeleteOption:       instanceTemplate.DeleteOption,
				Tags:               instanceTemplate.Tags,
				Labels:             instanceTemplate.Labels,
				RelationTag:        instanceTemplate.RelationTag,
			},
		})
	}

	// configure接口需要包装几乎所有的参数，暂时没有简便的办法
	_, err = c.base.CCEHostClient.UpdateInstanceGroupConfig(ctx, c.base.ClusterID, c.instanceGroupID, &ccev2.UpdateInstanceGroupRequest{
		InstanceGroupSpec: ccetypes.InstanceGroupSpec{
			ClusterID:          c.base.ClusterID,
			CCEInstanceGroupID: instanceGroupSpec.CCEInstanceGroupID,
			InstanceGroupName:  instanceGroupSpec.InstanceGroupName,
			Replicas:           instanceGroupSpec.Replicas + 1,
			CleanPolicy:        ccetypes.CleanPolicy(instanceGroupSpec.CleanPolicy),
			ShrinkPolicy:       ccetypes.ShrinkPolicy(instanceGroupSpec.ShrinkPolicy),
			ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
				Enabled:              instanceGroupSpec.ClusterAutoscalerSpec.Enabled,
				MinReplicas:          instanceGroupSpec.ClusterAutoscalerSpec.MinReplicas,
				MaxReplicas:          instanceGroupSpec.ClusterAutoscalerSpec.MaxReplicas,
				ScalingGroupPriority: instanceGroupSpec.ClusterAutoscalerSpec.ScalingGroupPriority,
			},
			InstanceTemplates: toUpdateInstanceTemplates,
		},
	}, nil)
	if err != nil {
		err = fmt.Errorf("CCEClient.UpdateInstanceGroupConfig error: %v", err)
		return
	}

	logger.Infof(ctx, "update instanceGroup userScript successfully")

	return nil

}
