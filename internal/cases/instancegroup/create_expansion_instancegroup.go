package instancegroup

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/addon"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	CheckInstanceGroupExpension cases.CaseName = "CheckInstanceGroupExpension"
)

const (
	testInstanceDeoloyment = "test-instancegroup"
	igScaleWaitTimeout     = time.Minute * 6  // 扩容的最长等待时间
	igScaleWaitInterval    = time.Second * 20 // 扩容检查的间隔时间
)

type createExpensionInstanceGroupConfig struct {
	CreateInstanceGroupRequest ccev2.CreateInstanceGroupRequest `json:"createInstanceGroupRequest"`
	MaxReplicas                int                              `json:"maxReplicas"`
	MinReplicas                int                              `json:"minReplicas"`
}

type checkInstanceGroupExpension struct {
	base        *cases.BaseClient
	checkConfig createExpensionInstanceGroupConfig
	igID        string
}

func init() {
	cases.AddCase(context.TODO(), CheckInstanceGroupExpension, NewCheckInstanceGroupExpension)
}

func NewCheckInstanceGroupExpension(ctx context.Context) cases.Interface {
	return &checkInstanceGroupExpension{}
}

func (c *checkInstanceGroupExpension) Name() cases.CaseName {
	return CheckInstanceGroupExpension
}

func (c *checkInstanceGroupExpension) Desc() string {
	return ""
}

func (c *checkInstanceGroupExpension) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	var createIGConfig createExpensionInstanceGroupConfig
	err = json.Unmarshal(config, &createIGConfig)
	if err != nil {
		err = fmt.Errorf("unmarshal create instance group request error: %v", err)
		return
	}
	c.base = base
	c.checkConfig = createIGConfig
	return
}

func (c *checkInstanceGroupExpension) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID
	loggerID := ctx.Value(logger.RequestID).(string)
	if loggerID == "" {
		loggerID = logger.GetUUID()
	}

	// 创建全局自动伸缩配置
	err = c.ensureAutoScalerConfigCreated(ctx)
	if err != nil {
		return
	}

	//// 获取集群额外的信息并获取可用节点子网
	//clusterExtraInfo, getExtraInfoErr := c.base.CCEHostClient.GetClusterExtraInfo(ctx, clusterID, nil)
	//if getExtraInfoErr != nil {
	//	err = fmt.Errorf("CCEHostClient.GetClusterExtraInfo failed, err: %v", getExtraInfoErr)
	//	return
	//}
	//var nodeSubnetID string
	//subnets := clusterExtraInfo.Subnets
	//for _, subnet := range subnets {
	//	if subnet.SubnetCIDR != "" {
	//		nodeSubnetID = subnet.SubnetID
	//		break
	//	}
	//}
	//if nodeSubnetID == "" {
	//	err = errors.New("no subnet id found in cluster extra info")
	//	return
	//}
	//logger.Infof(ctx, "node subnet id: %s", nodeSubnetID)
	// 创建节点组
	igName := "testInstance"
	igRequest := c.checkConfig.CreateInstanceGroupRequest
	igRequest.Replicas = 1               // 初始节点数量为1
	igRequest.InstanceGroupName = igName // 节点组名称，避免数据残留导致创建失败
	//igRequest.InstanceTemplate.VPCConfig.VPCSubnetID = nodeSubnetID // 节点子网信息
	//配置在后面更新节点组加
	//// 给 igRequest 添加污点配置
	//igRequest.InstanceTemplate.Taints = append(igRequest.InstanceTemplate.Taints, corev1.Taint{
	//	Key:    testInstanceDeoloyment + "key",
	//	Value:  testInstanceDeoloyment + "value",
	//	Effect: corev1.TaintEffectNoSchedule,
	//})
	//igRequest.ClusterAutoscalerSpec = &types.ClusterAutoscalerSpec{
	//	Enabled:     true,                      // 开启自动伸缩
	//	MinReplicas: c.checkConfig.MinReplicas, // 设置最小扩容节点数量为MinReplicas
	//	MaxReplicas: c.checkConfig.MaxReplicas, // 设置最大扩容节点数量为MaxReplicas
	//}
	createRes, createErr := c.base.CCEClient.CreateInstanceGroup(ctx, clusterID, &igRequest, nil)
	if createErr != nil {
		err = fmt.Errorf("CreateInstanceGroup failed, %s", createErr.Error())
		return
	}
	c.igID = createRes.InstanceGroupID
	logger.Infof(ctx, "create instance group `%s` [id: %s] success", igName, c.igID)

	instanceGroup, err := common.NewInstanceGroup(ctx, c.base, c.igID, time.Second*10, time.Minute*10)
	if err != nil {
		logger.Errorf(ctx, "failed to init instanceGroup client:%v", err)
		return nil, fmt.Errorf("failed to init instanceGroup client:%v", err)
	}

	// 有扩容任务后，需要等待扩容完成
	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		// 处理错误逻辑
		logger.Errorf(ctx, "CheckResource failed: %v", err)
		return nil, fmt.Errorf("CheckResource failed: %v", err)
	}

	//更新节点组，加污点
	_, err = c.base.CCEHostClient.UpdateInstanceGroupConfig(ctx, clusterID, c.igID, &ccev2.UpdateInstanceGroupRequest{
		PasswordNeedUpdate: false,
		InstanceGroupSpec: ccetypes.InstanceGroupSpec{
			InstanceGroupName: igRequest.InstanceGroupName,
			InstanceTemplates: []ccetypes.InstanceTemplate{
				{
					InstanceSpec: ccetypes.InstanceSpec{
						MachineType:  igRequest.InstanceTemplate.MachineType,
						InstanceType: igRequest.InstanceTemplate.InstanceType,
						VPCConfig: ccetypes.VPCConfig{
							VPCSubnetID:     igRequest.InstanceTemplate.VPCConfig.VPCSubnetID,
							SecurityGroupID: igRequest.InstanceTemplate.SecurityGroupID,
							SecurityGroup:   igRequest.InstanceTemplate.SecurityGroup,
						},
						InstanceResource:   igRequest.InstanceTemplate.InstanceResource,
						InstanceOS:         igRequest.InstanceTemplate.InstanceOS,
						DeployCustomConfig: igRequest.InstanceTemplate.DeployCustomConfig,
						Taints: []corev1.Taint{
							{
								Key:    testInstanceDeoloyment + "key",
								Value:  testInstanceDeoloyment + "value",
								Effect: corev1.TaintEffectNoSchedule,
							},
						},
					},
				},
			},
			Replicas: igRequest.Replicas,
			ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
				Enabled:     true,
				MinReplicas: c.checkConfig.MinReplicas,
				MaxReplicas: c.checkConfig.MaxReplicas,
			},
			ShrinkPolicy: ccetypes.PriorityShrinkPolicy,
		},
	}, nil)

	if err != nil {
		return nil, fmt.Errorf("UpdateInstanceGroupConfig failed: %v", err)
	}

	// 调用ListTasks函数，传入targetID参数
	listTasks, err := c.base.CCEHostClient.ListTasks(ctx, ccetypes.TaskTypeInstanceGroupReplicas, ccev2.ListTaskOption{
		TargetID: c.igID, // 传入节点组ID作为targetID
	}, nil)

	if err != nil {
		return nil, fmt.Errorf("failed to list tasks: %v", err)
	}

	// 处理返回的任务列表
	if listTasks == nil || len(listTasks.Page.Items) == 0 {
		return nil, fmt.Errorf("该节点组没有任务")
	}

	//再判断下，防止这个节点被其他影响而删除
	workerNodes, listNodeErr := c.base.KubeClient.ListNode(ctx, &kube.ListOptions{
		LabelSelector: map[string]string{
			"cluster-role": "node", // 通过标签选择worker节点
		},
	})
	if listNodeErr != nil {
		return nil, fmt.Errorf("KubeClient.ListNode failed, %s", listNodeErr.Error())
	}

	workerCount := len(workerNodes.Items) // 获取实际worker节点数量
	if workerCount < 1 {
		return nil, errors.New("this case needs at least 1 workers in cluster")
	}

	maxReplicas := c.GetMax(c.checkConfig.MaxReplicas, workerCount)
	logger.Infof(ctx, "maxReplicas: %d", maxReplicas)
	// 确保扩容至最大数量
	err = common.CreateCADeployment(ctx, c.base, 2*maxReplicas, testInstanceDeoloyment)
	if err != nil {
		return
	}

	// 等待节点组task
	waitErr := common.WaitForFunc(ctx, func(ctx context.Context) error {
		latestListTask, listTaskErr := c.base.CCEHostClient.ListTasks(ctx, ccetypes.TaskTypeInstanceGroupReplicas, ccev2.ListTaskOption{
			TargetID: c.igID,
		}, nil)
		if listTaskErr != nil {
			return fmt.Errorf("list task by instanceGroupID %s failed:%v", c.igID, listTaskErr)
		}

		if latestListTask.Page.TotalCount > listTasks.Page.TotalCount {
			logger.Infof(ctx, "instancegroup %s scaling replicas begin, tasks generated", c.igID)
			return nil
		}
		return fmt.Errorf("instancegroup %s  no task generated", c.igID)
	}, 5*time.Second, 8*time.Minute)

	if waitErr != nil {
		err = fmt.Errorf("wait instancegroup %s scaling replicas failed, %v", c.igID, waitErr)
		return
	}

	// 有扩容任务后，需要等待扩容完成
	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		err = fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, c.igID, waitErr)
		return
	}

	// 检查节点组的实际节点是否是符合预期的
	err, num := c.CheckIGNodeNum(ctx, c.checkConfig.MaxReplicas)
	if err != nil {
		return nil, fmt.Errorf("check instance group node num failed: %v", err)
	}
	logger.Infof(ctx, "instance group %v scale up to %v, not excceed MaxReplicas %v", c.igID, num, c.checkConfig.MaxReplicas)

	// 再缩容工作负载至0，触发缩容
	err = c.scaleDeployment(ctx, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to scale up instance group %v to %v:%v", c.igID, num, err)
	}

	listTask, listTaskErr := c.base.CCEHostClient.ListTasks(ctx, ccetypes.TaskTypeInstanceGroupReplicas, ccev2.ListTaskOption{
		TargetID: c.igID,
	}, nil)
	if listTaskErr != nil {
		return nil, fmt.Errorf("list task by instanceGroupID %s failed:%v", c.igID, listTaskErr)
	}

	// 等待节点组新task
	waitErr = common.WaitForFunc(ctx, func(ctx context.Context) error {
		latestListTask, latestListTaskErr := c.base.CCEHostClient.ListTasks(ctx, ccetypes.TaskTypeInstanceGroupReplicas, ccev2.ListTaskOption{
			TargetID: c.igID,
		}, nil)
		if latestListTaskErr != nil {
			return fmt.Errorf("list task by instanceGroupID %s failed:%v", c.igID, listTaskErr)
		}

		if latestListTask.Page.TotalCount > listTask.Page.TotalCount {
			logger.Infof(ctx, "instancegroup %s scaling replicas begin, tasks generated", c.igID)
			return nil
		}
		return fmt.Errorf("instancegroup %s  no task generated", c.igID)
	}, 5*time.Second, 8*time.Minute)

	if waitErr != nil {
		err = fmt.Errorf("wait instancegroup %s scaling replicas failed, %v", c.igID, waitErr)
		return
	}

	// 有扩容任务后，需要等待扩容完成
	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		err = fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, c.igID, waitErr)
		return
	}

	// 检查节点组的实际节点是否是符合预期的
	err, num = c.CheckIGNodeNum(ctx, c.checkConfig.MinReplicas)
	if err != nil {
		return nil, fmt.Errorf("check instance group node num failed: %v", err)
	}
	logger.Infof(ctx, "instance group %v scale down to %v, excceed MinReplicas %v", c.igID, num, c.checkConfig.MinReplicas)

	return resources, nil
}

func (c *checkInstanceGroupExpension) ensureAutoScalerConfigCreated(ctx context.Context) (err error) {
	clusterID := c.base.ClusterID
	cceClient := c.base.CCEClient
	// 获取全局的自动伸缩配置
	getRes, getErr := cceClient.GetAutoScalerConfig(ctx, clusterID, nil)
	if getErr != nil {
		err = fmt.Errorf("CCEClient.GetAutoScalerConfig failed, %s", getErr.Error())
		return
	}
	// 没有自动伸缩配置，则创建配置
	if getRes.Autoscaler == nil || getRes.Autoscaler.CAConfig == nil {
		logger.Infof(ctx, "autoscaler config is nil, start to create autoscaler config")
		_, err = cceClient.CreateAutoScalerConfig(ctx, clusterID, nil)
		if err != nil {
			err = fmt.Errorf("CCEClient.CreateAutoScalerConfig failed, %s", err.Error())
			return
		}
	}
	logger.Infof(ctx, "autoscaler config created")

	caConfig := &addon.ClusterAutoscalerConfig{
		ScaleDownEnabled:       true,
		ScalingDownThreshold:   func(threshold int) *int { return &threshold }(80),
		ScaleDownUnneededTime:  func(unneededTime int) *int { return &unneededTime }(1),
		ScaleDownDelayAfterAdd: func(delay int) *int { return &delay }(1),
		MaxEmptyBulkDelete:     func(maxNum int) *int { return &maxNum }(10),
		Expander:               addon.CAExpanderPriority,
	}

	_, err = cceClient.UpdateAutoScalerConfig(ctx, clusterID, caConfig, nil)
	if err != nil {
		err = fmt.Errorf("CCEClient.UpdateAutoScalerConfig failed, %s", err.Error())
		return
	}
	return
}

func (c *checkInstanceGroupExpension) Clean(ctx context.Context) (err error) {
	kubeClient := c.base.KubeClient
	// 清理deployment
	deployment, _ := kubeClient.GetDeploymentAppsV1(ctx, metav1.NamespaceDefault, testInstanceDeoloyment, &kube.GetOptions{})
	if deployment != nil && deployment.Name != "" {
		err = c.base.KubeClient.DeleteDeploymentAppsV1(ctx, metav1.NamespaceDefault, testInstanceDeoloyment)
		if err != nil {
			err = fmt.Errorf("DeleteDeploymentAppsV1 failed, %s", err.Error())
			return
		}
	}
	// 清理ig
	igInfoList, listIGErr := c.base.CCEClient.ListInstanceGroups(ctx, c.base.ClusterID, nil, nil)
	if listIGErr != nil {
		err = fmt.Errorf("CCEClient.ListInstanceGroups failed, %s", listIGErr.Error())
		return
	}
	for _, igInfo := range igInfoList.Page.List {
		// 判断节点组名称是否为创建时指定的名称

		instanceGroup, initErr := common.NewInstanceGroup(ctx, c.base, igInfo.Spec.CCEInstanceGroupID, time.Second*10, time.Minute*8)
		if initErr != nil {
			return fmt.Errorf("create instance group %v failed: %v", igInfo.Spec.CCEInstanceGroupID, initErr)
		}
		err = instanceGroup.AreTasksCompleted(ctx)
		if err != nil {
			return fmt.Errorf("instance group %v tasks are not completed: %v", igInfo.Spec.CCEInstanceGroupID, err)
		}

		if igInfo.Spec.InstanceGroupName == "testInstance" {
			_, err = c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, igInfo.Spec.CCEInstanceGroupID, true, nil)
			if err != nil {
				err = fmt.Errorf("CCEClient.DeleteInstanceGroup failed, %s", err.Error())
				return
			}
		}
	}
	return
}

func (c *checkInstanceGroupExpension) scaleDeployment(ctx context.Context, increment int32) (err error) {
	kubeClient := c.base.KubeClient
	scale, getScaleErr := kubeClient.GetDeploymentScaleAppsV1(ctx, metav1.NamespaceDefault, testInstanceDeoloyment, &kube.GetOptions{})
	if getScaleErr != nil {
		err = fmt.Errorf("GetDeploymentScaleAppsV1 failed, %s", getScaleErr.Error())
		return
	}
	scale.Spec.Replicas = increment
	_, err = kubeClient.ScaleDeploymentAppsV1(ctx, corev1.NamespaceDefault, testInstanceDeoloyment, scale)
	if err != nil {
		err = fmt.Errorf("ScaleDeploymentAppsV1 to replicas %d failed, err: %v", scale.Spec.Replicas, err)
		return
	}
	return
}

func (c *checkInstanceGroupExpension) Continue(ctx context.Context) bool {
	return true
}

func (c *checkInstanceGroupExpension) ConfigFormat() string {
	return ""
}

func (c *checkInstanceGroupExpension) CheckIGNodeNum(ctx context.Context, expect int) (err error, num int) {
	ig, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.igID, nil)
	if err != nil {
		return fmt.Errorf("GetInstanceGroup failed, %s", err.Error()), 0
	}
	if ig.InstanceGroup.Spec.Replicas != expect {
		return fmt.Errorf("ig %v node num %v  is not equal to expect %v", c.igID, ig.InstanceGroup.Spec.Replicas, expect), ig.InstanceGroup.Spec.Replicas
	}
	logger.Infof(ctx, "instance group %v node num is equal to expect %v", c.igID, expect)
	return nil, ig.InstanceGroup.Spec.Replicas
}

func (c *checkInstanceGroupExpension) GetMax(num1, num2 int) int32 {

	if num1 > num2 {
		return int32(num1)
	}
	return int32(num2)

}
