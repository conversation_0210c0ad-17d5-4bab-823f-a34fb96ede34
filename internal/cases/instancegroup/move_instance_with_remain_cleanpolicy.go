// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2022/02/10 , by <PERSON><PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
move instance in instanceGroup with remain policy
移出集群，保留实例时，移除的节点在节点组不存在，集群节点中存在实例
*/

package instancegroup

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	MoveInstanceGroupWithInstanceRemain cases.CaseName = "MoveInstanceGroupWithInstanceRemain"
)

type moveInstanceGroupWithInstanceRemainConfig struct {
	CreateInstanceGroupConfig *ccetypes.InstanceGroupSpec `json:"createInstanceGroupConfig"`
	InstanceGroupID           string                      `json:"instanceGroupID"`
	DeleteInstances           bool                        `json:"deleteInstances"`
}

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), MoveInstanceGroupWithInstanceRemain, NewMoveInstanceGroupWithInstanceRemain)
}

var _ cases.Interface = &moveInstanceGroupWithInstanceRemain{}

type moveInstanceGroupWithInstanceRemain struct {
	base              *cases.BaseClient
	remainInstanceIDs []string
	instanceGroupID   string
	config            moveInstanceGroupWithInstanceRemainConfig
	resources         []*cases.Resource
}

// NewMoveInstanceGroupWithInstanceRemain - 删除节点组时，删除节点组中节点，节点组保留实例，保留虚机实例
func NewMoveInstanceGroupWithInstanceRemain(ctx context.Context) cases.Interface {
	return &moveInstanceGroupWithInstanceRemain{}
}

func (c *moveInstanceGroupWithInstanceRemain) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	logger.WithValues("case", "deleteInstanceGroup").Infof(ctx, "json raw config: %s", string(config))
	if err := json.Unmarshal(config, &c.config); err != nil {
		return err
	}
	logger.WithValues("case", "deleteInstanceGroup").Infof(ctx, "config: %s", utils.ToJSON(c.config))

	c.base = base
	c.resources = nil

	return nil
}

func (c *moveInstanceGroupWithInstanceRemain) Name() cases.CaseName {
	return MoveInstanceGroupWithInstanceRemain
}

func (c *moveInstanceGroupWithInstanceRemain) Desc() string {
	return "[待补充]"
}

func (c *moveInstanceGroupWithInstanceRemain) Check(ctx context.Context) ([]cases.Resource, error) {
	var resources []cases.Resource
	// 创建节点组
	resp, err := c.base.CCEClient.CreateInstanceGroup(ctx, c.base.ClusterID, &ccev2.CreateInstanceGroupRequest{
		InstanceGroupSpec: *c.config.CreateInstanceGroupConfig,
	}, nil)
	if err != nil {
		logger.Errorf(ctx, "failed to create instanceGroup, err: %v", err)
		return nil, err
	}
	instanceGroupID := resp.InstanceGroupID
	c.instanceGroupID = instanceGroupID

	instanceGroup, err := common.NewInstanceGroup(ctx, c.base, instanceGroupID, time.Second*10, time.Minute*6)
	if err != nil {
		logger.Errorf(ctx, "failed to init instanceGroup client:%v", err)
		return nil, fmt.Errorf("failed to init instanceGroup client:%v", err)
	}

	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		logger.Errorf(ctx, "wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
		return nil, fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
	}

	// 获取节点组节点ID列表
	instanceIDs, err := instanceGroup.GetInstanceListByInstanceGroupID(ctx)
	if err != nil {
		logger.Errorf(ctx, "get instanceID list by instanceGroup failed:%v", err)
		return nil, fmt.Errorf("get instanceID list by instanceGroup failed:%v", err)
	}

	// 获取移出目标节点
	taskInstance := instanceIDs
	c.remainInstanceIDs = taskInstance

	time.Sleep(10 * time.Second)

	// 1.节点组移除instance,但节点保留在集群中
	_, err = c.base.CCEHostClient.CreateScaleDownInstanceGroupByCleanPolicy(ctx, c.base.ClusterID,
		instanceGroupID, taskInstance, ccev2.RemainCleanPolicy, nil, nil)
	if err != nil {
		logger.Errorf(ctx, "failed to create scale down task, err: %v", err)
		return nil, fmt.Errorf("failed to create scale down task, err: %v", err)
	}

	// 检查节点组是否移除节点
	err = instanceGroup.CheckInstanceNotExistedInInstanceGroup(ctx, taskInstance)
	if err != nil {
		logger.Errorf(ctx, "check instance not existed failed:%v", err)
		return nil, err
	}

	// 检查节点列表实例存在
	instanceResp, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
		PageNo:   1,
		PageSize: 30,
	}, nil)
	if err != nil {
		logger.Errorf(ctx, "get instance by clusterID %s failed:%v", c.base.ClusterID, err)
		return nil, fmt.Errorf("get instance by clusterID %s failed:%v", c.base.ClusterID, err)
	}

	existInstances := make(map[string]struct{})
	for _, instance := range instanceResp.InstancePage.InstanceList {
		existInstances[instance.Spec.CCEInstanceID] = struct{}{}
	}

	for _, targetID := range taskInstance {
		if _, found := existInstances[targetID]; !found {
			logger.Errorf(ctx, "the instance %s is not in cluster", targetID)
			return nil, fmt.Errorf("the instance %s is not in cluster", targetID)
		}
	}

	err = instanceGroup.AreTasksCompleted(ctx)
	if err != nil {
		logger.Errorf(ctx, "check instance group %v tasks completed failed:%v", instanceGroupID, err)
		return nil, fmt.Errorf("check instance group %v tasks completed failed:%v", instanceGroupID, err)
	}

	return resources, nil
}

func (c *moveInstanceGroupWithInstanceRemain) Clean(ctx context.Context) error {
	// 清理测试节点组
	_, err := c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, true, nil)
	if err != nil {
		logger.Errorf(ctx, "delete instanceGroup %s failed: %v", c.instanceGroupID, err.Error())
		return fmt.Errorf(err.Error())
	}

	// 清理被移出节点组，但仍保留在集群的节点
	_, err = c.base.CCEClient.DeleteInstances(ctx, c.base.ClusterID, &ccev2.DeleteInstancesRequest{
		InstanceIDs: c.remainInstanceIDs,
		DeleteOption: &ccetypes.DeleteOption{
			MoveOut:           false,
			DeleteResource:    true,
			DeleteCDSSnapshot: true,
		},
	}, nil)
	if err != nil {
		logger.Errorf(ctx, "delete remain instances from instanceGroup failed: %v", err.Error())
		return fmt.Errorf(err.Error())
	}

	return nil
}

func (c *moveInstanceGroupWithInstanceRemain) Continue(ctx context.Context) bool {
	return true
}

func (c *moveInstanceGroupWithInstanceRemain) ConfigFormat() string {
	return ""
}
