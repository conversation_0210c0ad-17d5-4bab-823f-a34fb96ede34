package instancegroup

/*
创建抢占实例类型节点组
进行自动扩容
*/

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	corev1 "k8s.io/api/core/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logicbcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	CreateBidInstanceGroup cases.CaseName = "CreateBidInstanceGroup"
)

const (
	testBidIGDeploymentName = "test-bid-instance-group"
)

type createBidInstanceGroup struct {
	base            *cases.BaseClient
	config          createBidInstanceGroupConfig
	instanceGroupID string
}

type createBidInstanceGroupConfig struct {
	CreateInstanceGroupConfig *ccetypes.InstanceGroupSpec `json:"createInstanceGroupConfig"`
}

func init() {
	cases.AddCase(context.TODO(), CreateBidInstanceGroup, NewCreateBidInstanceGroup)
}

func NewCreateBidInstanceGroup(ctx context.Context) cases.Interface {
	return &createBidInstanceGroup{}
}

func (c *createBidInstanceGroup) Name() cases.CaseName {
	return CreateBidInstanceGroup
}

func (c *createBidInstanceGroup) Desc() string {
	return "竞价实例节点组"
}

func (c *createBidInstanceGroup) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	var creatConfig createBidInstanceGroupConfig
	logger.Infof(ctx, "createBidInstanceGroup json raw config: %s", string(config))
	if err = json.Unmarshal(config, &creatConfig); err != nil {
		return err
	}
	c.config = creatConfig
	logger.Infof(ctx, "createBidInstanceGroup config is: %s", utils.ToJSON(config))
	return
}

func (c *createBidInstanceGroup) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID

	// 增加竞价实例的配置
	logger.Infof(ctx, "add bid instance config to instance group config ")
	c.config.CreateInstanceGroupConfig.InstanceTemplate.Bid = true
	c.config.CreateInstanceGroupConfig.InstanceTemplate.BidOption = ccetypes.BidOption{
		BidMode:       logicbcc.BidModeMarketPrice,
		BidTimeout:    5,
		BidReleaseEIP: true,
		BidReleaseCDS: true,
	}

	c.config.CreateInstanceGroupConfig.InstanceTemplate.Taints = []corev1.Taint{
		{
			Key:    testBidIGDeploymentName + "key",
			Value:  testBidIGDeploymentName + "value",
			Effect: corev1.TaintEffectNoSchedule,
		},
	}

	logger.Infof(ctx, "create bid instance group, config: %v", *c.config.CreateInstanceGroupConfig)
	createRes, err := c.base.CCEClient.CreateInstanceGroup(ctx, clusterID, &ccev2.CreateInstanceGroupRequest{
		InstanceGroupSpec: *c.config.CreateInstanceGroupConfig,
	}, nil)

	if err != nil {
		return nil, fmt.Errorf("create bid instance group failed: %v", err)
	}

	c.instanceGroupID = createRes.InstanceGroupID

	instanceGroup, err := common.NewInstanceGroup(ctx, c.base, c.instanceGroupID, time.Second*10, time.Minute*8)
	if err != nil {
		err = fmt.Errorf("failed to init instancegroup client:%v", err)
		return nil, err
	}

	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		err = fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, c.instanceGroupID, err)
		return
	}

	listTaskRes, listTaskErr := c.base.CCEHostClient.ListTasks(ctx, ccetypes.TaskTypeInstanceGroupReplicas, ccev2.ListTaskOption{
		TargetID: c.instanceGroupID,
	}, nil)
	if listTaskErr != nil {
		return nil, fmt.Errorf("list task failed: %v", listTaskErr)
	}
	if listTaskRes == nil {
		return nil, errors.New("list task response is nil")
	}

	oldTaskNum := listTaskRes.Page.TotalCount
	logger.Infof(ctx, "instanceGroup %v current task num: %d", c.instanceGroupID, oldTaskNum)

	// 更改CA配置
	_, err = c.base.CCEClient.CreateAutoScalerConfig(ctx, c.base.ClusterID, nil)
	if err != nil {
		return resources, fmt.Errorf("create auto scaler failed: %v", err)
	}

	_, err = c.base.CCEClient.UpdateInstanceGroupClusterAutoscalerSpec(ctx, c.base.ClusterID, c.instanceGroupID,
		&ccev2.ClusterAutoscalerSpec{
			Enabled:              true,
			MaxReplicas:          3,
			MinReplicas:          1,
			ScalingGroupPriority: 100}, nil)
	if err != nil {
		logger.Errorf(ctx, "failed to update autoscaler sepc in instancegroup, err: %v", err)
		return resources, err
	}

	workerNodes, listNodeErr := c.base.KubeClient.ListNode(ctx, &kube.ListOptions{
		LabelSelector: map[string]string{
			"cluster-role": "node",
		},
	})
	if listNodeErr != nil {
		err = fmt.Errorf("KubeClient.ListNode failed, %s", listNodeErr.Error())
		return
	}

	if workerNodes.Items == nil || len(workerNodes.Items) == 0 {
		return nil, errors.New("no worker nodes found")
	}
	workerCount := len(workerNodes.Items)

	// 创建deployment
	err = common.CreateCADeployment(ctx, c.base, int32(workerCount+1), testBidIGDeploymentName)
	if err != nil {
		return nil, fmt.Errorf("create bid instance group deployment failed: %v", err)
	}

	// 触发节点组自动扩容，判断依据 task多了一个，
	waitErr := common.WaitForFunc(ctx, func(ctx context.Context) error {
		listTaskRes, listTaskErr = c.base.CCEHostClient.ListTasks(ctx, ccetypes.TaskTypeInstanceGroupReplicas, ccev2.ListTaskOption{
			TargetID: c.instanceGroupID,
		}, nil)
		if listTaskErr != nil {
			return fmt.Errorf("list task failed: %v", listTaskErr)
		}
		if listTaskRes == nil {
			return errors.New("list task response is nil")
		}

		currentTaskNum := listTaskRes.Page.TotalCount
		logger.Infof(ctx, "instanceGroup %v current task num: %d", c.instanceGroupID, currentTaskNum)

		if currentTaskNum > oldTaskNum {
			logger.Infof(ctx, "instanceGroup %v begin to scale up", c.instanceGroupID)
			logger.Infof(ctx, "instanceGroup %v newest task: %v", c.instanceGroupID, listTaskRes.Page.Items[0])
			return nil
		}

		return fmt.Errorf("wait instanceGroup %v to scale up, wait for 10s", c.instanceGroupID)
	}, time.Second*5, time.Minute*5)

	if waitErr != nil {
		return nil, fmt.Errorf("wait instanceGroup %v to scale up failed: %v", c.instanceGroupID, waitErr)
	}

	// 等待节点组扩容完成
	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		err = fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, c.instanceGroupID, err)
		return
	}

	// 校验下节点组的节点付费类型
	listInstance, err := c.base.CCEClient.ListInstancesByInstanceGroupID(ctx, clusterID, c.instanceGroupID, 1, 100, nil)
	if err != nil {
		logger.Errorf(ctx, "failed to list instances by instance group id %v: %v", c.instanceGroupID, err)
	}
	if listInstance == nil || len(listInstance.Page.List) == 0 {
		return nil, fmt.Errorf("list instances by instance group id %v not found", c.instanceGroupID)
	}

	for _, instance := range listInstance.Page.List {

		instanceCRD, getCRDErr := c.base.CCEHostClient.GetInstanceCRD(ctx, c.base.ClusterID, instance.Spec.CCEInstanceID, nil)
		if getCRDErr != nil {
			return nil, fmt.Errorf("get instance CRD failed: %v", getCRDErr)
		}

		if !instanceCRD.Instance.Spec.Bid {
			return nil, fmt.Errorf("instance %v not bid: %v", instance.Spec.CCEInstanceID, instance.Spec.Bid)
		}
		if instanceCRD.Instance.Spec.BidOption.BidMode != logicbcc.BidModeMarketPrice {
			return nil, fmt.Errorf("instance %v not market price: %v", instance.Spec.CCEInstanceID, instance.Spec.BidOption.BidMode)
		}
	}

	logger.Infof(ctx, "instanceGroup %v bid instances check  successfully: %d", c.instanceGroupID)

	return nil, nil
}

func (c *createBidInstanceGroup) Clean(ctx context.Context) error {
	_, err := c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, true, nil)
	if err != nil {
		logger.Errorf(ctx, "delete instanceGroup %s failed: %v", c.instanceGroupID, err.Error())
		return fmt.Errorf(err.Error())
	}
	return nil
}

func (c *createBidInstanceGroup) Continue(ctx context.Context) bool {
	return true
}

func (c *createBidInstanceGroup) ConfigFormat() string {
	return ""
}
