/*
1、创建节点组子网均匀扩容策略，主备子网充足
2、修改节点组，备选子网不足，备选子网扩不出来
3、修改节点组的扩容类型为机型顺序，主机型子网不足，主机型扩不出来
*/

package instancegroup

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	CreateUniformPolicyInstanceGroup cases.CaseName = "CreateUniformPolicyInstanceGroup"
)

type createUniformPolicyInstanceGroup struct {
	base            *cases.BaseClient
	config          createUniformPolicyInstanceGroupConfig
	instanceGroupID string
	subnetIDs       []string
}

type createUniformPolicyInstanceGroupConfig struct {
	CreateInstanceGroupConfig *ccetypes.InstanceGroupSpec `json:"createInstanceGroupConfig"`
	FewSubNetwork             string                      `json:"fewSubNetwork"`
}

func init() {
	cases.AddCase(context.TODO(), CreateUniformPolicyInstanceGroup, NewCreateUniformPolicyInstanceGroup)
}

func NewCreateUniformPolicyInstanceGroup(ctx context.Context) cases.Interface {
	return &createUniformPolicyInstanceGroup{}
}

func (c *createUniformPolicyInstanceGroup) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	c.base = base
	var createUniformConfig createUniformPolicyInstanceGroupConfig
	logger.Infof(ctx, "json raw config: %s", string(config))
	if err := json.Unmarshal(config, &createUniformConfig); err != nil {
		return err
	}
	c.config = createUniformConfig
	logger.Infof(ctx, "config: %s", utils.ToJSON(c.config))

	for _, template := range c.config.CreateInstanceGroupConfig.InstanceTemplates {
		c.subnetIDs = append(c.subnetIDs, template.VPCConfig.VPCSubnetID)
	}
	if len(c.subnetIDs) < 2 {
		return errors.New("subNetIDs are less than 2")
	}
	logger.Infof(ctx, "instanceGroup subNetIDs: %v", c.subnetIDs)

	return nil
}

func (c *createUniformPolicyInstanceGroup) Name() cases.CaseName {
	return CreateUniformPolicyInstanceGroup
}

func (c *createUniformPolicyInstanceGroup) Desc() string {
	return "创建多子网均匀分布的节点组"
}

func (c *createUniformPolicyInstanceGroup) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID

	//step1  创建节点组---期望节点数为2
	logger.Infof(ctx, "begin to create instanceGroup with uniform policy, all subnets have enough ips")
	creatUniformPolicyInstanceGroupRes, creatErr := c.base.CCEClient.CreateInstanceGroup(ctx, clusterID,
		&ccev2.CreateInstanceGroupRequest{
			InstanceGroupSpec: *c.config.CreateInstanceGroupConfig,
		}, nil)

	if creatErr != nil {
		err = fmt.Errorf("instance group create falied : %v", creatErr)
		return
	}

	instanceGroupID := creatUniformPolicyInstanceGroupRes.InstanceGroupID
	c.instanceGroupID = instanceGroupID

	instanceGroup, err := common.NewInstanceGroup(ctx, c.base, instanceGroupID, time.Second*10, time.Minute*10)
	if err != nil {
		logger.Errorf(ctx, "failed to init instanceGroup client:%v", err)
		return nil, fmt.Errorf("failed to init instanceGroup client:%v", err)
	}

	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		logger.Errorf(ctx, "wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
		return nil, fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
	}

	getInstanceGroup, getErr := c.base.CCEClient.GetInstanceGroup(ctx, clusterID, instanceGroupID, nil)
	if getErr != nil {
		err = fmt.Errorf("instance group get falied: %v", getErr)
		return
	}
	ig := getInstanceGroup.InstanceGroup

	// 校验下节点组均匀分布策略
	if getInstanceGroup.InstanceGroup.Spec.ShrinkPolicy != ccev2.UniformShrinkPolicy {
		return nil, errors.New("instance group shrink policy is not uniform")
	}

	// list 节点组节点
	listRes, listErr := c.base.CCEClient.ListInstancesByInstanceGroupID(ctx, clusterID, instanceGroupID, 0,
		0, nil)
	if listErr != nil {
		return nil, fmt.Errorf("list instances by instance group id failed:%v", listErr)
	}

	originInstanceIDs := make(map[string]any)

	// 收集节点子网
	instanceSubnetIDs := make([]string, 0, len(listRes.Page.List))
	for _, instanceSubnetID := range listRes.Page.List {
		originInstanceIDs[instanceSubnetID.Spec.CCEInstanceID] = struct{}{}
		instanceSubnetIDs = append(instanceSubnetIDs, instanceSubnetID.Spec.VPCConfig.VPCSubnetID)
	}

	// 比较两个子网
	instanceGroupSubnetIDMap := make(map[string]any)
	for _, subnetID := range instanceSubnetIDs {
		instanceGroupSubnetIDMap[subnetID] = struct{}{}
	}

	if len(instanceGroupSubnetIDMap) < 2 {
		return nil, errors.New("instance group is not uniform distribution, instances have the same or empty subnetID")
	}

	// 再看是否是节点组子网
	for _, key := range c.subnetIDs {
		if _, ok := instanceGroupSubnetIDMap[key]; !ok {
			return nil, fmt.Errorf("instance subnetID not in instanceGroupSubnetID: %s", key)
		}
	}

	logger.Infof(ctx, "instanceGroup with uniform policy which all subnets have enough ips has been checked successfully.")
	//step2 获取此时few-network的ip数
	subInfo, err := c.base.VPCClient.DescribeSubnet(ctx, c.config.FewSubNetwork, nil)
	if err != nil {
		return nil, fmt.Errorf("describe subnet %s failed: %v", c.config.FewSubNetwork, err)
	}
	logger.Infof(ctx, "describe subnet %s successfully, result: %v", c.config.FewSubNetwork, subInfo)

	availableIPCount := subInfo.AvailableIPNum
	scaleReplicas := ig.Spec.Replicas + 2*(availableIPCount-1)

	//更新节点组备选机型模版子网和节点数
	logger.Infof(ctx, "few-network %v ips will use %v", c.config.FewSubNetwork, availableIPCount-1)
	_, err = c.base.CCEHostClient.UpdateInstanceGroupConfig(ctx, clusterID, instanceGroupID, &ccev2.UpdateInstanceGroupRequest{
		PasswordNeedUpdate: false,
		InstanceGroupSpec: ccetypes.InstanceGroupSpec{
			InstanceGroupName: ig.Spec.InstanceGroupName,
			InstanceTemplates: []ccetypes.InstanceTemplate{
				{
					InstanceSpec: ccetypes.InstanceSpec{
						MachineType:  ig.Spec.InstanceTemplates[0].MachineType,
						InstanceType: ig.Spec.InstanceTemplates[0].InstanceType,
						VPCConfig: ccetypes.VPCConfig{
							VPCSubnetID:     ig.Spec.InstanceTemplates[0].VPCSubnetID,
							SecurityGroupID: ig.Spec.InstanceTemplates[0].VPCConfig.SecurityGroupID,
							SecurityGroup: ccetypes.SecurityGroup{
								EnableCCERequiredSecurityGroup: true,
								EnableCCEOptionalSecurityGroup: false,
							},
						},
						ImageID:            ig.Spec.InstanceTemplates[0].ImageID,
						InstanceResource:   ig.Spec.InstanceTemplates[0].InstanceResource,
						DeployCustomConfig: ig.Spec.InstanceTemplates[0].DeployCustomConfig,
					},
				},
				{
					InstanceSpec: ccetypes.InstanceSpec{
						MachineType:  ig.Spec.InstanceTemplates[1].MachineType,
						InstanceType: ig.Spec.InstanceTemplates[1].InstanceType,
						VPCConfig: ccetypes.VPCConfig{
							VPCSubnetID:     c.config.FewSubNetwork,
							SecurityGroupID: ig.Spec.InstanceTemplates[1].VPCConfig.SecurityGroupID,
							SecurityGroup: ccetypes.SecurityGroup{
								EnableCCERequiredSecurityGroup: true,
								EnableCCEOptionalSecurityGroup: false,
							},
						},
						ImageID:            ig.Spec.InstanceTemplates[1].ImageID,
						InstanceResource:   ig.Spec.InstanceTemplates[1].InstanceResource,
						DeployCustomConfig: ig.Spec.InstanceTemplates[1].DeployCustomConfig,
					},
				},
			},
			Replicas: scaleReplicas,
			ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
				Enabled: ig.Spec.ClusterAutoscalerSpec.Enabled,
			},
			ShrinkPolicy: ccetypes.UniformShrinkPolicy,
		},
	}, nil)

	if err != nil {
		return nil, fmt.Errorf("update instance group %v fail:%v", instanceGroupID, err)
	}

	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		logger.Errorf(ctx, "wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
		return nil, fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
	}

	time.Sleep(5 * time.Second)

	//再次扩容节点组
	logger.Infof(ctx, "few-network %v ips will use the left ip", c.config.FewSubNetwork)
	_, err = c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, clusterID, c.instanceGroupID, &ccev2.UpdateInstanceGroupReplicasRequest{
		Replicas: scaleReplicas + 1,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("update instance group %v replicas failed: %v", c.instanceGroupID, err)
	}

	time.Sleep(5 * time.Second)

	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		logger.Errorf(ctx, "wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
		return nil, fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
	}

	_, err = c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, clusterID, c.instanceGroupID, &ccev2.UpdateInstanceGroupReplicasRequest{
		Replicas: scaleReplicas + 2,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("update instance group %v replicas failed: %v", c.instanceGroupID, err)
	}

	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		logger.Errorf(ctx, "wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
		return nil, fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
	}

	//此时应该few-network的ip数已经为0了
	// 再次检查few-network的ip数
	logger.Infof(ctx, "In instancegroup %v, begin to check few-network available ip number ", instanceGroupID)
	subInfo, err = c.base.VPCClient.DescribeSubnet(ctx, c.config.FewSubNetwork, nil)
	if err != nil {
		return nil, fmt.Errorf("describe subnet %s failed: %v", c.config.FewSubNetwork, err)
	}

	if subInfo != nil && subInfo.AvailableIPNum != 0 {
		return nil, fmt.Errorf("In instanceGroup %v, few-network %v available ip num is %v ,not 0 ", instanceGroupID, c.config.FewSubNetwork, subInfo.AvailableIPNum)
	}

	logger.Infof(ctx, "few-network %v available ip num is 0 ", instanceGroupID)

	//再次扩容节点组
	logger.Infof(ctx, "continue to scale instance group with few-network %v ips is 0 ", c.config.FewSubNetwork)
	_, err = c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, clusterID, c.instanceGroupID, &ccev2.UpdateInstanceGroupReplicasRequest{
		Replicas: scaleReplicas + 4,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("update instance group %v replicas failed: %v", c.instanceGroupID, err)
	}

	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		logger.Errorf(ctx, "wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
		return nil, fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
	}

	latestCCEInstance, err := c.base.CCEClient.ListInstancesByPage(ctx, clusterID, &ccev2.ListInstancesByPageParams{
		KeywordType: ccev2.InstanceKeywordTypeInstanceGroupID,
		Keyword:     c.instanceGroupID,
		OrderBy:     ccev2.InstanceOrderByCreatedAt,
		Order:       ccev2.OrderDESC,
		PageSize:    2,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("list instances by instance group %v failed:%v", c.instanceGroupID, err)
	}

	if latestCCEInstance == nil || len(latestCCEInstance.InstancePage.InstanceList) != 2 {
		return nil, fmt.Errorf("lastest two instances in instance group %v are not created successfully", c.instanceGroupID)
	}

	for _, instance := range latestCCEInstance.InstancePage.InstanceList {
		if instance.Spec.VPCConfig.VPCSubnetID != ig.Spec.InstanceTemplates[0].VPCSubnetID {
			return nil, fmt.Errorf("instance %v is not created in main subnet", instance.Spec.CCEInstanceID)
		}
	}

	logger.Infof(ctx, "instanceGroup with uniform policy which alternative template's subnets have few ips has been checked successfully.")

	//step3 修改节点组扩容顺序为机型扩容顺序，主机型子网为不足的子网，备选子网为足的子网，并再次扩容节点组
	logger.Infof(ctx, "upgrade instance group %v to use PriorityShrinkPolicy", c.instanceGroupID)
	_, err = c.base.CCEHostClient.UpdateInstanceGroupConfig(ctx, clusterID, instanceGroupID, &ccev2.UpdateInstanceGroupRequest{
		PasswordNeedUpdate: false,
		InstanceGroupSpec: ccetypes.InstanceGroupSpec{
			InstanceGroupName: ig.Spec.InstanceGroupName,
			InstanceTemplates: []ccetypes.InstanceTemplate{
				{
					InstanceSpec: ccetypes.InstanceSpec{
						MachineType:  ig.Spec.InstanceTemplates[0].MachineType,
						InstanceType: ig.Spec.InstanceTemplates[0].InstanceType,
						VPCConfig: ccetypes.VPCConfig{
							VPCSubnetID:     c.config.FewSubNetwork, // 修改为主机型few-network
							SecurityGroupID: ig.Spec.InstanceTemplates[0].VPCConfig.SecurityGroupID,
							SecurityGroup: ccetypes.SecurityGroup{
								EnableCCERequiredSecurityGroup: true,
								EnableCCEOptionalSecurityGroup: false,
							},
						},
						ImageID:            ig.Spec.InstanceTemplates[0].ImageID,
						InstanceResource:   ig.Spec.InstanceTemplates[0].InstanceResource,
						DeployCustomConfig: ig.Spec.InstanceTemplates[0].DeployCustomConfig,
					},
				},
				{
					InstanceSpec: ccetypes.InstanceSpec{
						MachineType:  ig.Spec.InstanceTemplates[1].MachineType,
						InstanceType: ig.Spec.InstanceTemplates[1].InstanceType,
						VPCConfig: ccetypes.VPCConfig{
							VPCSubnetID:     ig.Spec.InstanceTemplates[0].VPCSubnetID,
							SecurityGroupID: ig.Spec.InstanceTemplates[1].VPCConfig.SecurityGroupID,
							SecurityGroup: ccetypes.SecurityGroup{
								EnableCCERequiredSecurityGroup: true,
								EnableCCEOptionalSecurityGroup: false,
							},
						},
						ImageID:            ig.Spec.InstanceTemplates[1].ImageID,
						InstanceResource:   ig.Spec.InstanceTemplates[1].InstanceResource,
						DeployCustomConfig: ig.Spec.InstanceTemplates[1].DeployCustomConfig,
					},
				},
			},
			Replicas: scaleReplicas + 6,
			ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
				Enabled: ig.Spec.ClusterAutoscalerSpec.Enabled,
			},
			ShrinkPolicy: ccetypes.PriorityShrinkPolicy, // 修改为机型扩容顺序
		},
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("update instance group %v replicas failed: %v", c.instanceGroupID, err)
	}
	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		logger.Errorf(ctx, "wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
		return nil, fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
	}

	latestCCEInstance, err = c.base.CCEClient.ListInstancesByPage(ctx, clusterID, &ccev2.ListInstancesByPageParams{
		KeywordType: ccev2.InstanceKeywordTypeInstanceGroupID,
		Keyword:     c.instanceGroupID,
		OrderBy:     ccev2.InstanceOrderByCreatedAt,
		Order:       ccev2.OrderDESC,
		PageSize:    2,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("list instances by instance group %v failed:%v", c.instanceGroupID, err)
	}

	if latestCCEInstance == nil || len(latestCCEInstance.InstancePage.InstanceList) != 2 {
		return nil, fmt.Errorf("lastest two instances in instance group %v are not created successfully", c.instanceGroupID)
	}

	getInstanceGroup, getErr = c.base.CCEClient.GetInstanceGroup(ctx, clusterID, instanceGroupID, nil)
	if getErr != nil {
		err = fmt.Errorf("instance group get falied: %v", getErr)
		return
	}

	for _, instance := range latestCCEInstance.InstancePage.InstanceList {
		if instance.Spec.VPCConfig.VPCSubnetID != getInstanceGroup.InstanceGroup.Spec.InstanceTemplates[1].VPCSubnetID {
			return nil, fmt.Errorf("instance %v is not created in alternative template's subnets %v",
				instance.Spec.CCEInstanceID, getInstanceGroup.InstanceGroup.Spec.InstanceTemplates[1].VPCSubnetID)
		}
	}

	logger.Infof(ctx, "instanceGroup with PriorityShrinkPolicy policy which main template's subnets have few ips has been checked successfully.")

	err = instanceGroup.AreTasksCompleted(ctx)
	if err != nil {
		logger.Errorf(ctx, "check instance group %v tasks completed failed:%v", instanceGroupID, err)
		return nil, fmt.Errorf("check instance group %v tasks completed failed:%v", instanceGroupID, err)
	}

	return resources, nil
}

func (c *createUniformPolicyInstanceGroup) Clean(ctx context.Context) error {
	// 清理测试节点组
	_, err := c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, true, nil)
	if err != nil {
		logger.Errorf(ctx, "delete instanceGroup %s failed: %v", c.instanceGroupID, err.Error())
		return fmt.Errorf(err.Error())
	}

	return nil
}

func (c *createUniformPolicyInstanceGroup) Continue(ctx context.Context) bool {
	return false
}

func (c *createUniformPolicyInstanceGroup) ConfigFormat() string { return "" }
