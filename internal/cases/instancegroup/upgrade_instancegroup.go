/*节点组升级
1、节点组支持0节点升级
2、节点组支持多节点升级
*/

package instancegroup

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	v1 "k8s.io/api/core/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	kubeclient "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	UpgradeInstanceGroup cases.CaseName = "UpgradeInstanceGroup"
)

type upgradeInstanceGroup struct {
	base            *cases.BaseClient
	config          instanceGroupUpgradeConfig
	instanceGroupID string
}

type instanceGroupUpgradeConfig struct {
	CreateInstanceGroupConfig *ccetypes.InstanceGroupSpec `json:"createInstanceGroupConfig"`
}

func init() {
	cases.AddCase(context.TODO(), UpgradeInstanceGroup, NewUpgradeInstanceGroup)
}

func NewUpgradeInstanceGroup(ctx context.Context) cases.Interface {
	return &upgradeInstanceGroup{}
}

func (c *upgradeInstanceGroup) Name() cases.CaseName {
	return UpgradeInstanceGroup
}

func (c *upgradeInstanceGroup) Desc() string {
	return "节点组升级"
}

func (c *upgradeInstanceGroup) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base

	var upConfig instanceGroupUpgradeConfig
	logger.Infof(ctx, "json raw config: %s", string(config))
	if err = json.Unmarshal(config, &upConfig); err != nil {
		return err
	}

	c.config = upConfig
	logger.Infof(ctx, "config: %s", utils.ToJSON(c.config))
	return
}

func (c *upgradeInstanceGroup) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID

	createIGResp, err := c.base.CCEClient.CreateInstanceGroup(ctx, clusterID, &ccev2.CreateInstanceGroupRequest{
		InstanceGroupSpec: *c.config.CreateInstanceGroupConfig}, nil)
	if err != nil {
		return nil, fmt.Errorf("CreateInstanceGroup err: %v", err)
	}

	instanceGroupID := createIGResp.InstanceGroupID
	c.instanceGroupID = instanceGroupID

	instanceGroup, err := common.NewInstanceGroup(ctx, c.base, instanceGroupID, time.Second*10, time.Minute*10)
	if err != nil {
		logger.Errorf(ctx, "failed to init instanceGroup client:%v", err)
		return nil, fmt.Errorf("failed to init instanceGroup client:%v", err)
	}

	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		logger.Errorf(ctx, "wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
		return nil, fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
	}

	igRes, err := c.base.CCEClient.GetInstanceGroup(ctx, clusterID, instanceGroupID, nil)
	if err != nil {
		return nil, fmt.Errorf("GetInstanceGroup %v err: %v", instanceGroupID, err)
	}

	if igRes == nil || igRes.InstanceGroup == nil {
		return nil, fmt.Errorf("GetInstanceGroup %v response is empty", instanceGroupID)
	}

	//节点组支持0节点的时候升级，这里做下判断吧
	if igRes.InstanceGroup.Spec.Replicas == 0 {
		logger.Infof(ctx, "InstanceGroup %v InstanceCount is 0", instanceGroupID)
		targetVersion, needUpgrade, upgradeErr := c.UpgradeInstanceGroup(ctx, clusterID, instanceGroupID)

		//有错误直接结束case
		if upgradeErr != nil {
			return nil, fmt.Errorf("UpgradeInstanceGroup err:%v", upgradeErr)
		}

		//无需升级，可升级内容已经是最新版本了,直接结束case
		if !needUpgrade {
			logger.Infof(ctx, "InstanceGroup %v no need to upgrade", instanceGroupID)
			return nil, nil
		}

		//需要升级,且已经升级完成了
		if targetVersion == "" {
			return nil, fmt.Errorf("targetVersion is empty")
		}

		// 扩容节点组
		_, err = c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, clusterID, instanceGroupID, &ccev2.UpdateInstanceGroupReplicasRequest{
			Replicas: igRes.InstanceGroup.Spec.Replicas + 1,
		}, nil)

		if err != nil {
			return nil, fmt.Errorf("update instance group %v replicas err: %v", instanceGroupID, err)
		}

		err = instanceGroup.CheckResource(ctx)
		if err != nil {
			logger.Errorf(ctx, "wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
			return nil, fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
		}

		// 检查节点组task是否完成，防止节点组扩容后，确保task已经完成
		err = instanceGroup.AreTasksCompleted(ctx)
		if err != nil {
			logger.Errorf(ctx, "check instance group %v tasks completed failed:%v", instanceGroupID, err)
			return nil, fmt.Errorf("check instance group %v tasks completed failed:%v", instanceGroupID, err)
		}

		//检查升级结果
		err = c.CheckUpgradeEffect(ctx, clusterID, instanceGroupID, targetVersion)
		if err != nil {
			return nil, fmt.Errorf("CheckUpgradeEffect err:%v", err)
		}
		logger.Infof(ctx, "InstanceGroup %v with zero node upgrade successfully", instanceGroupID)
		logger.Infof(ctx, "instancegroup %v nodes version are all target version: %v", instanceGroupID, targetVersion)
	}

	//此时，节点组节点数肯定不是0了，升级
	igRes, err = c.base.CCEClient.GetInstanceGroup(ctx, clusterID, instanceGroupID, nil)
	if err != nil {
		return nil, fmt.Errorf("GetInstanceGroup %v err: %v", instanceGroupID, err)
	}

	if igRes == nil || igRes.InstanceGroup == nil {
		return nil, fmt.Errorf("GetInstanceGroup %v response is empty", instanceGroupID)
	}

	logger.Infof(ctx, "instanceGroup %v current replicas is %v", instanceGroupID, igRes.InstanceGroup.Spec.Replicas)

	targetVersion, needUpgrade, upgradeErr := c.UpgradeInstanceGroup(ctx, clusterID, instanceGroupID)

	//有错误直接结束cae
	if upgradeErr != nil {
		return nil, fmt.Errorf("UpgradeInstanceGroup err:%v", upgradeErr)
	}

	//无需升级，可升级内容已经是最新版本了,直接结束case
	if !needUpgrade {
		logger.Infof(ctx, "InstanceGroup %v no need to upgrade", instanceGroupID)
		return nil, nil
	}

	//需要升级,且已经升级完成了
	if targetVersion == "" {
		return nil, fmt.Errorf("targetVersion is empty")
	}

	// 扩容节点组
	_, err = c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, clusterID, instanceGroupID, &ccev2.UpdateInstanceGroupReplicasRequest{
		Replicas: igRes.InstanceGroup.Spec.Replicas + 1,
	}, nil)

	if err != nil {
		return nil, fmt.Errorf("update instance group %v replicas err: %v", instanceGroupID, err)
	}

	err = instanceGroup.CheckResource(ctx)
	if err != nil {
		logger.Errorf(ctx, "wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
		return nil, fmt.Errorf("wait for %s/%s instance ready failed:%v", c.base.ClusterID, instanceGroupID, err)
	}

	// 检查节点组task是否完成，防止节点组扩容后，确保task已经完成
	err = instanceGroup.AreTasksCompleted(ctx)
	if err != nil {
		logger.Errorf(ctx, "check instance group %v tasks completed failed:%v", instanceGroupID, err)
		return nil, fmt.Errorf("check instance group %v tasks completed failed:%v", instanceGroupID, err)
	}

	//检查升级结果
	err = c.CheckUpgradeEffect(ctx, clusterID, instanceGroupID, targetVersion)
	if err != nil {
		return nil, fmt.Errorf("CheckUpgradeEffect err:%v", err)
	}
	logger.Infof(ctx, "InstanceGroup %v with zero node upgrade successfully", instanceGroupID)
	logger.Infof(ctx, "instancegroup %v nodes version are all target version: %v", instanceGroupID, targetVersion)

	return nil, nil
}

func (c *upgradeInstanceGroup) Clean(ctx context.Context) error {
	_, err := c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, true, nil)
	if err != nil {
		logger.Errorf(ctx, "delete instanceGroup %s failed: %v", c.instanceGroupID, err.Error())
		return fmt.Errorf(err.Error())
	}
	return nil
}

func (c *upgradeInstanceGroup) Continue(ctx context.Context) bool {
	return true
}

func (c *upgradeInstanceGroup) ConfigFormat() string {
	return ""
}

func (c *upgradeInstanceGroup) CheckNodesVersion(targetVersion string, nodes *v1.NodeList) error {
	if len(nodes.Items) == 0 {
		return errors.New("nodes is empty")
	}

	for _, node := range nodes.Items {
		if node.Status.NodeInfo.ContainerRuntimeVersion != targetVersion {
			return fmt.Errorf("node %v version is %v not target version: %v", node.Name, node.Status.NodeInfo.ContainerRuntimeVersion, targetVersion)
		}
	}

	return nil
}

func (c *upgradeInstanceGroup) ConvertInstanceVersionToString(instanceSpec *ccev2.InstanceSpec) string {
	if instanceSpec == nil {
		return ""
	}

	return string(instanceSpec.RuntimeType) + "://" + instanceSpec.RuntimeVersion
}

// UpgradeInstanceGroup 升级节点组
func (c *upgradeInstanceGroup) UpgradeInstanceGroup(ctx context.Context, clusterID, instanceGroupID string) (string, bool, error) {

	if clusterID == "" || instanceGroupID == "" {
		return "", false, fmt.Errorf("clusterID or instanceGroupID can't be empty")
	}

	// 获取节点组可升级版本
	getContainerUpgradeComponent, err := c.GetContainerRuntimeUpgradeComponent(ctx, clusterID, instanceGroupID)
	if err != nil {
		return "", false, fmt.Errorf("get container runtime upgrade component err:%v", err)
	}
	if getContainerUpgradeComponent == nil || &getContainerUpgradeComponent.Result == nil {
		return "", false, fmt.Errorf("get container runtime upgrade component response is nil")
	}

	// 升级的情况：没有gpu节点所以无toolkit; kubelet与集群面一致，要先升级集群面才可以升级kubelet
	// 这里只补充 升级容器运行时
	// todo 后续可以补充三个内容的升级
	if len(getContainerUpgradeComponent.Result.ContainerRuntimeVersion.ComponentVersion) == 0 {
		logger.Infof(ctx, "GetInstanceGroup %v ComponentsUpgradeVersions ContainerRuntimeVersion is empty", instanceGroupID)
		logger.Infof(ctx, "InstanceGroup %v ContainerRuntimeVersion is newest version", instanceGroupID)
		return "", false, nil
	}

	length := len(getContainerUpgradeComponent.Result.ContainerRuntimeVersion.ComponentVersion)

	//选取可升级内容的最旧版本
	targetVersion := getContainerUpgradeComponent.Result.ContainerRuntimeVersion.ComponentVersion[length-1].TargetVersion
	needDrainPods := getContainerUpgradeComponent.Result.ContainerRuntimeVersion.ComponentVersion[length-1].NeedDrainNode

	createWorkflowReq := ccev2.CreateWorkflowRequest{
		WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
		WorkflowConfig: ccetypes.WorkflowConfig{
			UpgradeNodesWorkflowConfig: &ccetypes.UpgradeNodesWorkflowConfig{
				Components: []ccetypes.UpgradeNodesWorkflowConfigComponent{
					{
						Name:           ccetypes.ContainerRuntime,
						CurrentVersion: getContainerUpgradeComponent.Result.ContainerRuntimeVersion.CurrentVersion,
						TargetVersion:  targetVersion,
					},
				},
				// 节点组全部节点，不指定则默认全部节点
				CCEInstanceIDList:      nil,
				InstanceGroupID:        instanceGroupID,
				NodeUpgradeBatchSize:   5,
				DrainNodeBeforeUpgrade: needDrainPods,
				PausePolicy:            ccetypes.PausePolicyNotPause,
				BatchIntervalMinutes:   0,
			},
		},
	}

	createWorkflowRes, err := c.base.CCEHostClient.CreateWorkflow(ctx, clusterID, &createWorkflowReq, nil)
	if err != nil {
		return targetVersion, false, fmt.Errorf("create upgrade instancegroup %v workflow err: %v", instanceGroupID, err)
	}

	if createWorkflowRes == nil {
		return targetVersion, false, fmt.Errorf("create upgrade instancegroup %v workflow response is empty", instanceGroupID)
	}

	workflowID := createWorkflowRes.WorkflowID

	waitErr := common.WaitForFunc(ctx, func(ctx context.Context) error {
		workRes, getErr := c.base.CCEHostClient.GetWorkflow(ctx, clusterID, workflowID, nil)
		if getErr != nil {
			return fmt.Errorf("get workflow %v err: %v", workflowID, getErr)
		}

		if workRes == nil {
			return fmt.Errorf("get workflow %v response is nil", workflowID)
		}

		if workRes.Workflow.Status.WorkflowPhase == ccetypes.WorkflowPhaseSucceeded {
			logger.Infof(ctx, "workflow %v status:%v, upgrade successed", workflowID, workRes.Workflow.Status.WorkflowPhase)
			return nil
		}

		return fmt.Errorf("workflow %v status is %v, not %v, waiting for 5s", workflowID, workRes.Workflow.Status.WorkflowPhase, ccetypes.WorkflowPhaseSucceeded)
	}, time.Second*5, time.Minute*5)

	if waitErr != nil {
		return targetVersion, false, fmt.Errorf("wait upgrade instance group %v workflow %v failed: %v", instanceGroupID, workflowID, waitErr)
	}

	logger.Infof(ctx, "upgrade instance group %v success", instanceGroupID)
	return targetVersion, true, nil
}

// GetContainerRuntimeUpgradeComponent 获取可升级版本信息
func (c *upgradeInstanceGroup) GetContainerRuntimeUpgradeComponent(ctx context.Context, clusterID, instanceGroupID string) (*ccev2.GetComponentsUpgradeVersionResponse, error) {

	if clusterID == "" || instanceGroupID == "" {
		return nil, fmt.Errorf("clusterID or instanceGroupID is empty")
	}

	getComponentRes, err := c.base.CCEHostClient.GetInstanceGroupComponentsUpgradeVersions(ctx, clusterID, instanceGroupID, nil)
	if err != nil {
		return nil, fmt.Errorf("GetInstanceGroup %v ComponentsUpgradeVersions err: %v", instanceGroupID, err)
	}

	if getComponentRes == nil {
		return nil, fmt.Errorf("GetInstanceGroup %v ComponentsUpgradeVersionsResponse is empty", instanceGroupID)
	}
	return getComponentRes, nil
}

func (c *upgradeInstanceGroup) CheckUpgradeEffect(ctx context.Context, clusterID, instanceGroupID, targetVersion string) error {

	if clusterID == "" || instanceGroupID == "" || targetVersion == "" {
		return fmt.Errorf("clusterID or instanceGroupID or targetVersion is empty")
	}

	//检查节点组模版
	logger.Infof(ctx, "begin to check instance group %v version", instanceGroupID)
	iGRes, err := c.base.CCEClient.GetInstanceGroup(ctx, clusterID, instanceGroupID, nil)
	if err != nil {
		return fmt.Errorf("get instance group %v err: %v", instanceGroupID, err)
	}
	if iGRes == nil {
		return fmt.Errorf("get instance group %v response is nil", instanceGroupID)
	}

	iGRuntimeVersion := c.ConvertInstanceVersionToString(&iGRes.InstanceGroup.Spec.InstanceTemplate.InstanceSpec)
	if iGRuntimeVersion != targetVersion {
		return fmt.Errorf("instance group %v runtime version is %v not target version: %v", instanceGroupID, iGRuntimeVersion, targetVersion)
	}
	logger.Infof(ctx, "instancegroup %v version is target version: %v", instanceGroupID, targetVersion)

	// 节点组中的所有节点是升级后的版本
	logger.Infof(ctx, "begin to check instance group %v nodes version", instanceGroupID)

	nodesRes, err := c.base.KubeClient.ListNode(ctx, &kubeclient.ListOptions{
		LabelSelector: map[string]string{
			"instance-group-id": instanceGroupID},
	})

	if err != nil {
		return fmt.Errorf("list instance group %v nodes err: %v", instanceGroupID, err)
	}

	if nodesRes == nil {
		return fmt.Errorf("list instance group %v nodes response is nil", instanceGroupID)
	}

	err = c.CheckNodesVersion(targetVersion, nodesRes)
	if err != nil {
		return fmt.Errorf("instance group %v instances version is not target version %v: %v", instanceGroupID, targetVersion, err)
	}

	logger.Infof(ctx, "check update instance group %v success", instanceGroupID)
	return nil
}
