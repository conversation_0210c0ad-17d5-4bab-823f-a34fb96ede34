package instancegroup

import (
	"context"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	CheckInstancegroupAPI cases.CaseName = "CheckInstancegroupAPI"
)

type instanceGroupCheck struct {
	base *cases.BaseClient
}

func init() {
	cases.AddCase(context.TODO(), CheckInstancegroupAPI, NewInstanceGroupCheck)
}

func NewInstanceGroupCheck(ctx context.Context) cases.Interface {
	return &instanceGroupCheck{}
}

func (c *instanceGroupCheck) Name() cases.CaseName {
	return CheckInstancegroupAPI
}

func (c *instanceGroupCheck) Desc() string {
	return "节点组API校验"
}

func (c *instanceGroupCheck) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	return
}

func (c *instanceGroupCheck) Check(ctx context.Context) (resources []cases.Resource, err error) {

	instances, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
		ClusterRole: ccetypes.ClusterRoleNode,
	}, nil)
	if err != nil {
		logger.Errorf(ctx, "list instance failed: %v", err.Error())
		return nil, err
	}
	if len(instances.InstancePage.InstanceList) == 0 {
		return nil, errors.New("cluster instance list is empty")
	}
	instanceResource := instances.InstancePage.InstanceList[0].Spec.InstanceResource
	imageID := instances.InstancePage.InstanceList[0].Spec.ImageID
	vpcSubnetID := instances.InstancePage.InstanceList[0].Spec.VPCSubnetID

	type fields struct {
		InstanceResource ccetypes.InstanceResource
		VPCConfig        ccetypes.VPCConfig
		ImagesID         string
	}
	checkInstanceGroupcases := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "check instanceGroup normal case",
			fields: fields{
				InstanceResource: instanceResource,
				VPCConfig: ccetypes.VPCConfig{
					VPCSubnetID:       vpcSubnetID,
					SecurityGroupType: "normal",
					SecurityGroup: ccetypes.SecurityGroup{
						CustomSecurityGroupIDs:         nil,
						EnableCCERequiredSecurityGroup: true,
						EnableCCEOptionalSecurityGroup: false,
					},
				},
				ImagesID: imageID,
			},
			wantErr: false,
		},
		{ //没有VPCConfig参数 预期节点组创建失败
			name: "check instanceGroup creation without VPCConfig case",
			fields: fields{
				InstanceResource: instanceResource,
				VPCConfig:        ccetypes.VPCConfig{},
				ImagesID:         imageID,
			},
			wantErr: true,
		},

		{ //没有imagesID参数 预期节点组创建失败
			name: "check instanceGroup creation without ImagesID case",
			fields: fields{
				InstanceResource: instanceResource,
				VPCConfig: ccetypes.VPCConfig{
					VPCSubnetID:       vpcSubnetID,
					SecurityGroupType: "normal",
					SecurityGroup: ccetypes.SecurityGroup{
						CustomSecurityGroupIDs:         nil,
						EnableCCERequiredSecurityGroup: true,
						EnableCCEOptionalSecurityGroup: false,
					},
				},
				ImagesID: "",
			},
			wantErr: true,
		},

		{ //没有cpu参数 预期节点组创建失败
			name: "check instanceGroup creation without CPU case",
			fields: fields{
				InstanceResource: ccetypes.InstanceResource{
					MEM:         instanceResource.MEM,
					MachineSpec: instanceResource.MachineSpec,
				},
				VPCConfig: ccetypes.VPCConfig{
					VPCSubnetID:       vpcSubnetID,
					SecurityGroupType: "normal",
					SecurityGroup: ccetypes.SecurityGroup{
						CustomSecurityGroupIDs:         nil,
						EnableCCERequiredSecurityGroup: true,
						EnableCCEOptionalSecurityGroup: false,
					},
				},
				ImagesID: imageID,
			},
			wantErr: true,
		},
		{ //没有mem参数 预期节点组创建失败
			name: "check instanceGroup creation without MEM case",
			fields: fields{
				InstanceResource: ccetypes.InstanceResource{
					CPU:         instanceResource.CPU,
					MachineSpec: instanceResource.MachineSpec,
				},
				VPCConfig: ccetypes.VPCConfig{
					VPCSubnetID:       vpcSubnetID,
					SecurityGroupType: "normal",
					SecurityGroup: ccetypes.SecurityGroup{
						CustomSecurityGroupIDs:         nil,
						EnableCCERequiredSecurityGroup: true,
						EnableCCEOptionalSecurityGroup: false,
					},
				},
				ImagesID: imageID,
			},
			wantErr: true,
		},
		{ //没有MachineSpec参数 预期节点组创建失败
			name: "check instanceGroup creation without MachineSpec case",
			fields: fields{
				InstanceResource: ccetypes.InstanceResource{
					CPU: instanceResource.CPU,
					MEM: instanceResource.MEM,
				},
				VPCConfig: ccetypes.VPCConfig{
					VPCSubnetID:       vpcSubnetID,
					SecurityGroupType: "normal",
					SecurityGroup: ccetypes.SecurityGroup{
						CustomSecurityGroupIDs:         nil,
						EnableCCERequiredSecurityGroup: true,
						EnableCCEOptionalSecurityGroup: false,
					},
				},
				ImagesID: imageID,
			},
			wantErr: true,
		},
	}

	fmt.Println("check instanceGroup cases")
	for _, tt := range checkInstanceGroupcases {
		_, checkErr := c.base.CCEClient.CreateInstanceGroup(ctx, c.base.ClusterID, &ccev2.CreateInstanceGroupRequest{
			InstanceGroupSpec: ccetypes.InstanceGroupSpec{
				InstanceGroupName: "test-instanceGroupOpenapi",
				ClusterID:         c.base.ClusterID,
				InstanceTemplate: ccetypes.InstanceTemplate{
					InstanceSpec: ccetypes.InstanceSpec{
						InstanceResource: tt.fields.InstanceResource,
						VPCConfig:        tt.fields.VPCConfig,
						ImageID:          tt.fields.ImagesID,
					},
				},
			},
		}, nil)
		if checkErr != nil && !tt.wantErr {
			logger.Errorf(ctx, "create instanceGroup failed: %v, but expected success", checkErr)
			err = fmt.Errorf("create instanceGroup failed: %v, but expected success", checkErr)
		}

		if checkErr == nil && tt.wantErr {
			logger.Errorf(ctx, "create instanceGroup expected an error : %v, but success", tt.name)
			err = fmt.Errorf("create instanceGroup expected an error : %v, but success", tt.name)
		}
	}
	return
}

func (c *instanceGroupCheck) Clean(ctx context.Context) error {
	return nil
}

func (c *instanceGroupCheck) Continue(ctx context.Context) bool {
	return true
}

func (c *instanceGroupCheck) ConfigFormat() string {
	return ""
}
