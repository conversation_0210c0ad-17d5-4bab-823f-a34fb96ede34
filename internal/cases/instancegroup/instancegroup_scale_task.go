/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  instancegroup_scale_task
 * @Version: 1.0.0
 * @Date: 2021/6/25 2:39 下午
 */
package instancegroup

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// InstanceGroupScalingTask - 节点组扩缩容Task Case 名字
	InstanceGroupScalingTask cases.CaseName = "InstanceGroupScalingTask"
)

func init() {
	cases.AddCase(context.TODO(), InstanceGroupScalingTask, NewInstanceGroupScalingTask)
}

func NewInstanceGroupScalingTask(ctx context.Context) cases.Interface {
	return &instanceGroupScalingTask{}
}

type instanceGroupScalingTask struct {
	base            *cases.BaseClient
	instanceGroupID string
	config          instanceGroupScalingTaskConfig
}

type instanceGroupScalingTaskConfig struct {
	CreateInstanceGroupConfig *ccetypes.InstanceGroupSpec `json:"createInstanceGroupConfig"`
	Tasks                     []*taskConfig               `json:"tasks"`
}

type taskConfig struct {
	TaskID                    string
	UpToReplicas              int      `json:"upToReplicas"`
	InstancesToBeRemoved      []string `json:"instancesToBeRemoved"`
	InstancesToBeRemovedCount int      `json:"instancesToBeRemovedCount"`
	ExpectedSucceed           bool     `json:"expectedSucceed"`
	ScaleFailed               bool     `json:"scaleFailed"`
	InsufficientMachineSpec   string   `json:"insufficientMachineSpec"`
}

func (c *instanceGroupScalingTask) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("nil baseClient")
	}

	logger.WithValues("case", "instanceGroupScalingTask").Infof(ctx, "json raw config: %s", string(config))
	if err := json.Unmarshal(config, &c.config); err != nil {
		return err
	}
	logger.WithValues("case", "instanceGroupScalingTask").Infof(ctx, "config: %s", utils.ToJSON(c.config))

	c.base = base
	return nil
}

func (c *instanceGroupScalingTask) Name() cases.CaseName {
	return InstanceGroupScalingTask
}

func (c *instanceGroupScalingTask) Desc() string {
	return "节点组扩缩容"
}

func (c *instanceGroupScalingTask) Check(ctx context.Context) ([]cases.Resource, error) {
	logger := logger.WithValues("case", "instanceGroupScalingTask")
	createInstanceGroupClient := &createInstanceGroup{
		base: c.base,
		config: createInstanceGroupConfig{
			Request: ccev2.CreateInstanceGroupRequest{
				InstanceGroupSpec: *c.config.CreateInstanceGroupConfig,
			},
			NeedClean: false,
		},
	}
	logger.WithValues("case", "instanceGroupScalingTask").Infof(ctx, "create instancegroup request: %s", utils.ToJSON(createInstanceGroupClient.config.Request))

	resources, err := createInstanceGroupClient.Check(ctx)
	if err != nil {
		logger.Errorf(ctx, "failed to create instanceGroup, err: %v", err)
		return resources, nil
	}

	if len(resources) == 0 {
		logger.Errorf(ctx, "no resource return by createInstanceGroupClient")
		return resources, errors.New("no resource return by createInstanceGroupClient")
	}

	var instanceGroupID string
	for _, r := range resources {
		if r.Type == cases.ResourceTypeCCEInstanceGroup {
			instanceGroupID = r.ID
			c.instanceGroupID = instanceGroupID
			break
		}
	}

	if instanceGroupID == "" {
		return resources, errors.New("empty instanceGroupID")
	}

	var canBeRemovedIndex int

	time.Sleep(10 * time.Second)

	instancesResp, err := c.base.CCEClient.ListInstancesByInstanceGroupID(ctx, c.base.ClusterID, instanceGroupID, 1, 1000, nil)
	if err != nil {
		logger.Errorf(ctx, "failed to list instance in instanceGroup, err: %v", err)
		return resources, fmt.Errorf("failed to list instance in instanceGroup, err: %v", err)
	}

	for _, config := range c.config.Tasks {
		// 扩容
		if config.UpToReplicas > 0 {
			createTask, err := c.base.CCEHostClient.CreateScaleUpInstanceGroupTask(ctx, c.base.ClusterID, instanceGroupID, config.UpToReplicas, nil)
			if err != nil {
				if !config.ExpectedSucceed {
					continue
				}
				logger.Errorf(ctx, "failed to create scale up task, err: %v", err)
				return resources, fmt.Errorf("failed to create scale up task, err: %v", err)
			}

			config.TaskID = createTask.TaskID
		}

		// 缩容
		if config.InstancesToBeRemovedCount > 0 || len(config.InstancesToBeRemoved) > 0 {
			if len(config.InstancesToBeRemoved) == 0 {
				if canBeRemovedIndex+config.InstancesToBeRemovedCount > len(instancesResp.Page.List) {
					logger.Errorf(ctx, "not enough instance in instanceGroup can be removed, have %d, need %d", len(instancesResp.Page.List), canBeRemovedIndex+config.InstancesToBeRemovedCount)
					return resources, fmt.Errorf("not enough instance in instanceGroup can be removed, have %d, need %d", len(instancesResp.Page.List), canBeRemovedIndex+config.InstancesToBeRemovedCount)
				}
				for i := 0; i < config.InstancesToBeRemovedCount; i++ {
					config.InstancesToBeRemoved = append(config.InstancesToBeRemoved, instancesResp.Page.List[i+canBeRemovedIndex].Spec.CCEInstanceID)
				}
				canBeRemovedIndex += config.InstancesToBeRemovedCount
			}

			createTask, err := c.base.CCEHostClient.CreateScaleDownInstanceGroupTask(ctx, c.base.ClusterID, instanceGroupID, config.InstancesToBeRemoved, nil)
			if err != nil {
				logger.Errorf(ctx, "failed to create scale down task, err: %v", err)
				return resources, fmt.Errorf("failed to create scale down task, err: %v", err)
			}

			config.TaskID = createTask.TaskID
		}

		// 失败扩容，报错信息
		if config.ScaleFailed {
			// 考虑到部分成功，扩容套餐为0的情况稳妥一点
			logger.Infof(ctx, "update instance group config to use InsufficientMachineSpec")
			if config.InsufficientMachineSpec == "" {
				return resources, errors.New("InsufficientMachineSpec is empty")
			}

			igRes, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, instanceGroupID, nil)
			if err != nil {
				logger.Errorf(ctx, "failed to get instance group %v, err: %v", instanceGroupID, err)
			}

			ig := igRes.InstanceGroup
			toUpReplicas := 1
			if _, err = c.base.CCEClient.UpdateInstanceGroupConfig(ctx, c.base.ClusterID, instanceGroupID, &ccev2.UpdateInstanceGroupRequest{
				PasswordNeedUpdate: false,
				InstanceGroupSpec: ccetypes.InstanceGroupSpec{
					InstanceGroupName: ig.Spec.InstanceGroupName,
					InstanceTemplates: []ccetypes.InstanceTemplate{
						{
							InstanceSpec: ccetypes.InstanceSpec{
								InstanceResource: ccetypes.InstanceResource{
									CPU:         ig.Spec.InstanceTemplate.InstanceResource.CPU,
									MEM:         ig.Spec.InstanceTemplate.InstanceResource.MEM,
									MachineSpec: config.InsufficientMachineSpec,
								},
								MachineType:  ig.Spec.InstanceTemplate.MachineType,
								InstanceType: ig.Spec.InstanceTemplate.InstanceType,
								VPCConfig: ccetypes.VPCConfig{
									VPCSubnetID: ig.Spec.InstanceTemplate.VPCSubnetID,
								},
								ImageID:     ig.Spec.InstanceTemplate.ImageID,
								Labels:      ig.Spec.InstanceTemplate.Labels,
								Taints:      ig.Spec.InstanceTemplate.Taints,
								Annotations: ig.Spec.InstanceTemplate.Annotations,
							},
						},
					},
					Replicas: ig.Spec.Replicas,
					ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
						Enabled: ig.Spec.ClusterAutoscalerSpec.Enabled,
					},
				},
			}, nil); err != nil {
				logger.Errorf(ctx, "failed to update instanceGroupConfig %v, err: %v", instanceGroupID, err)
				return resources, fmt.Errorf("failed to create scale down task, err: %v", err)
			}

			createTask, err := c.base.CCEHostClient.CreateScaleUpInstanceGroupTask(ctx, c.base.ClusterID, instanceGroupID, ig.Spec.Replicas+toUpReplicas, nil)
			if err != nil {
				logger.Errorf(ctx, "failed to create scale up task, err: %v", err)
				return resources, fmt.Errorf("failed to create scale up task, err: %v", err)
			}

			config.TaskID = createTask.TaskID
		}

		// 不触发扩容任务
		if config.TaskID == "" {
			continue
		}

		// 节点组存在task限制
		// 放在这里判断每个task是否完成，如果完成则继续下一个task，否则继续等待
		waitErr := common.WaitForFunc(ctx, func(ctx context.Context) error {
			getTaskResp, getTaskErr := c.base.CCEHostClient.GetTask(ctx, ccetypes.TaskTypeInstanceGroupReplicas, config.TaskID, nil)
			if getTaskErr != nil {
				logger.Errorf(ctx, "Failed to get task status, err: %v", err)
				return getTaskErr
			}

			if getTaskResp.Task.FinishTime == nil || getTaskResp.Task.FinishTime.IsZero() {
				return fmt.Errorf("taskID: %s, task is not finished", config.TaskID)
			}

			if getTaskResp.Task.Phase != string(ccetypes.TaskPhaseAborted) && getTaskResp.Task.Phase != string(ccetypes.TaskPhaseFinished) {
				return fmt.Errorf("taskID: %s, task phase: %s, not completed", config.TaskID, getTaskResp.Task.Phase)
			}

			if config.ExpectedSucceed && (getTaskResp.Task.Phase == string(ccetypes.TaskPhaseAborted) || getTaskResp.Task.ErrMessage != "") {
				logger.Errorf(ctx, "taskID: %s, expected succeed, actual phase: %s, err: %s", config.TaskID, getTaskResp.Task.Phase, getTaskResp.Task.ErrMessage)
				return fmt.Errorf("taskID: %s, expected succeed, actual phase: %s, err: %s", config.TaskID, getTaskResp.Task.Phase, getTaskResp.Task.ErrMessage)
			}

			if !config.ExpectedSucceed && (getTaskResp.Task.Phase == string(ccetypes.TaskPhaseFinished) || getTaskResp.Task.ErrMessage == "") {
				logger.Errorf(ctx, "taskID: %s, expected not succeed, actual phase: %s, err: %s", config.TaskID, getTaskResp.Task.Phase, getTaskResp.Task.ErrMessage)
				return fmt.Errorf("taskID: %s, expected not succeed, actual phase: %s, err: %s", config.TaskID, getTaskResp.Task.Phase, getTaskResp.Task.ErrMessage)
			}

			if !config.ExpectedSucceed {
				if getTaskResp.Task.Phase == string(ccetypes.TaskPhaseAborted) {
					var progressErrMessage string
					for _, progress := range getTaskResp.Task.TaskProcesses {
						if progress.Phase == string(ccetypes.TaskPhaseAborted) {
							progressErrMessage = progress.ErrMessage
							logger.Infof(ctx, "taskID: %s, progress create machines failed, progressErrMessage: %s", config.TaskID, progressErrMessage)
							break
						}
					}

					if progressErrMessage != getTaskResp.Task.ErrMessage {
						return fmt.Errorf("taskID: %s, Errmessage %v is different with progressErrMessage: %s", config.TaskID, getTaskResp.Task.ErrMessage, progressErrMessage)
					}
					logger.Infof(ctx, "taskID: %s, errMessage is complete", config.TaskID)
				}
			}

			instanceListResp, listInstanceErr := c.base.CCEClient.ListInstancesByInstanceGroupID(ctx, c.base.ClusterID, instanceGroupID, 1, 1000, nil)
			if listInstanceErr != nil {
				logger.Errorf(ctx, "failed to list instance in instanceGroup, err: %v", err)
				return fmt.Errorf("failed to list instance in instanceGroup, err: %v", err)
			}

			existInstances := make(map[string]struct{})
			existInstancesCCEInstanceID := make(map[string]struct{})
			for _, instance := range instanceListResp.Page.List {
				existInstances[instance.Status.Machine.K8SNodeName] = struct{}{}
				existInstancesCCEInstanceID[instance.Spec.CCEInstanceID] = struct{}{}
			}
			if len(config.InstancesToBeRemoved) > 0 {
				for _, instanceID := range config.InstancesToBeRemoved {
					if _, found := existInstancesCCEInstanceID[instanceID]; found {
						return fmt.Errorf("instance %s should be remove in task %s, but still exist", instanceID, config.TaskID)
					}
				}
			}

			if config.UpToReplicas > 0 {
				if len(getTaskResp.Task.TaskProcesses) != 2 {
					logger.Errorf(ctx, "task processes in scaling up task are not match")
					return errors.New("task processes in scaling up task are not match")
				}

				for instanceID := range getTaskResp.Task.TaskProcesses[1].Metrics {
					if _, found := existInstances[instanceID]; !found {
						return fmt.Errorf("instance %s in task %s should exists, but not found", instanceID, config.TaskID)
					}
				}
			}

			return nil
		}, 10*time.Second, 10*time.Minute)

		if waitErr != nil {
			logger.Errorf(ctx, "wait task finish failed, err: %v", waitErr)
			return resources, fmt.Errorf("wait task finish failed, err: %v", waitErr)
		}

		logger.Infof(ctx, "wait task finished successfully, task %v ", config)
	}

	return resources, nil

}

func (c *instanceGroupScalingTask) Clean(ctx context.Context) error {
	// 清理测试节点组
	_, err := c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, true, nil)
	if err != nil {
		logger.Errorf(ctx, "delete instanceGroup %s failed: %v", c.instanceGroupID, err.Error())
		return fmt.Errorf(err.Error())
	}
	return nil
}

func (c *instanceGroupScalingTask) Continue(ctx context.Context) bool {
	return false
}

func (c *instanceGroupScalingTask) ConfigFormat() string {
	str, _ := json.Marshal(c.config)
	return string(str)
}
