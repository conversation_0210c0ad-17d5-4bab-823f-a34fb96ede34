// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2022/03/01 13:58:00, by <EMAIL>, create
*/
/*
集群升级
*/

package workflow

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	// UpgradeClusterCaseName - case 名字
	UpgradeClusterCaseName cases.CaseName = "UpgradeCluster"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), UpgradeClusterCaseName, NewUpgradeCluster)
}

var _ cases.Interface = &upgradeCluster{}

type upgradeCluster struct {
	base                  *cases.BaseClient
	createWorkflowRequest *ccev2.CreateWorkflowRequest
}

// NewUpgradeCluster - 集群升级
func NewUpgradeCluster(ctx context.Context) cases.Interface {
	return &upgradeCluster{}
}

func (c *upgradeCluster) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base

	logger.Infof(ctx, "CreateWorkflowRequest: %s", string(config))

	// 初始化 CreateWorkflowRequest
	var request ccev2.CreateWorkflowRequest
	if err := json.Unmarshal(config, &request); err != nil {
		logger.Errorf(ctx, "Unmarshal CreateWorkflowRequest failed: %s", err)
		return err
	}
	c.createWorkflowRequest = &request

	return nil
}

func (c *upgradeCluster) Name() cases.CaseName {
	return UpgradeClusterCaseName
}

func (c *upgradeCluster) Desc() string {
	return "集群升级"
}

func (c *upgradeCluster) Check(ctx context.Context) ([]cases.Resource, error) {
	clusterID := c.base.ClusterID

	logger.Infof(ctx, "Test Workflow on %s begin", clusterID)

	// 创建升级 Workflow
	createWorkflowResp, err := c.base.CCEClient.CreateWorkflow(ctx, clusterID, c.createWorkflowRequest, nil)
	if err != nil {
		logger.Errorf(ctx, "CreateWorkflow %s failed: %s", clusterID, err)
		return nil, err
	}

	workflowID := createWorkflowResp.WorkflowID

	logger.Infof(ctx, "Create workflow %s succeed", workflowID)

	// 等待 Workflow 完成
	if err := c.waitWorkflowFinished(ctx, workflowID); err != nil {
		logger.Errorf(ctx, "waitWorkflowFinished %s failed: %s", clusterID, err)
		return nil, err
	}

	// 删除 Workflow
	if _, err := c.base.CCEClient.DeleteWorkflow(ctx, clusterID, workflowID, nil); err != nil {
		logger.Errorf(ctx, "DeleteWorkflow %s/%s failed: %s", clusterID, workflowID, err)
		return nil, err
	}

	return nil, nil
}

func (c *upgradeCluster) Clean(ctx context.Context) error {
	return nil
}

func (c *upgradeCluster) Continue(ctx context.Context) bool {
	return true
}

func (c *upgradeCluster) ConfigFormat() string {
	return ""
}

// waitWorkflowFinished - 等待 Workflow 完成
func (c *upgradeCluster) waitWorkflowFinished(ctx context.Context, workflowID string) error {
	if workflowID == "" {
		return errors.New("workflowID is empty")
	}

	clusterID := c.base.ClusterID

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	timer := time.NewTimer(20 * time.Minute)
	defer timer.Stop()

	for {
		select {
		case <-ticker.C:
			// 查询 Workflow
			resp, err := c.base.CCEClient.GetWorkflow(ctx, clusterID, workflowID, nil)
			if err != nil {
				logger.Errorf(ctx, "GetWorkflow failed: %s", err)
				return err
			}

			workflow := resp.Workflow

			// 检查 Workflow 状态
			phase := workflow.Status.WorkflowPhase

			switch phase {
			case ccetypes.WorkflowPhaseSucceeded:
				logger.Infof(ctx, "workflow %s %s", workflowID, phase)
				return nil
			case ccetypes.WorkflowPhaseDeleting, ccetypes.WorkflowPhaseDeleted:
				logger.Infof(ctx, "workflow %s %s", workflowID, phase)
				return nil
			case ccetypes.WorkflowPhaseFailed, ccetypes.WorkflowPhasePaused, ccetypes.WorkflowPhaseUnknown:
				logger.Infof(ctx, "workflow %s %s", workflowID, phase)
				return fmt.Errorf("workflow %s %s", workflowID, phase)
			case ccetypes.WorkflowPhasePending, ccetypes.WorkflowPhaseUpgrading:
				logger.Infof(ctx, "workflow %s %s, keep waiting", workflowID, phase)
			}
		case <-timer.C:
			logger.Errorf(ctx, "timeout waiting for Workflow %s succeeded", workflowID)
			return fmt.Errorf("timeout waiting for Workflow %s succeeded", workflowID)
		}
	}

	return nil
}
