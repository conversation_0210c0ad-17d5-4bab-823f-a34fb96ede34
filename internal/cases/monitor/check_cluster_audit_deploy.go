/* check_cluster_audit_deploy.go */
/*
modification history
--------------------
2024/9/25, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
1、校验集群审计（开启、查看）
*/

package monitor

import (
	"context"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CheckClusterAuditDeploy cases.CaseName = "CheckClusterAuditDeploy"
)

type checkClusterAuditDeploy struct {
	base *cases.BaseClient
}

func init() {
	cases.AddCase(context.TODO(), CheckClusterAuditDeploy, NewCheckClusterAuditDeploy)
}

func NewCheckClusterAuditDeploy(ctx context.Context) cases.Interface {
	return &checkClusterAuditDeploy{}
}

func (c *checkClusterAuditDeploy) Name() cases.CaseName {
	return CheckClusterAuditDeploy
}

func (c *checkClusterAuditDeploy) Desc() string {
	return "校验集群审计"
}

func (c *checkClusterAuditDeploy) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	return
}

func (c *checkClusterAuditDeploy) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID
	// 查看集群审计开启状态
	auditStatus, getErr := c.base.MonitorClient.GetClusterAuditDeploy(ctx, clusterID, nil)
	if getErr != nil {
		err = fmt.Errorf("get cluster audit deploy status failed: %v", getErr)
		return
	}

	// 若未开启，则开启审计 (开启审计会导致api-server 重启，所以该用例应独立执行)
	if !auditStatus.HasDeploy {
		_, openErr := c.base.MonitorClient.OpenClusterAuditDeploy(ctx, clusterID, nil)
		if openErr != nil {
			err = fmt.Errorf("open cluster audit deploy failed: %v", openErr)
			return
		}
		time.Sleep(30 * time.Second)
	}

	// 重复开启报错
	_, openErr := c.base.MonitorClient.OpenClusterAuditDeploy(ctx, clusterID, nil)
	if openErr != nil {
		if !strings.Contains(openErr.Error(), "Audit conf has exist") {
			err = fmt.Errorf("open cluster audit deploy failed: %v", openErr)
			return
		}
		logger.Infof(ctx, "audit deploy is not allow reopen, as expected")
	}

	return
}

func (c *checkClusterAuditDeploy) Clean(ctx context.Context) error {
	return nil
}

func (c *checkClusterAuditDeploy) Continue(ctx context.Context) bool {
	return true
}

func (c *checkClusterAuditDeploy) ConfigFormat() string {
	return ""
}
