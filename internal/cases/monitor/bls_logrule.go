/*bls_logrule.go */
/*
modification history
--------------------
2023/11/09, by <PERSON>, create
*/
/*
DESCRIPTION
*/

package monitor

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/baidubce/bce-sdk-go/services/bls/api"
	v1 "k8s.io/api/core/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	resource "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	// BLSLogRule
	BLSLogRule cases.CaseName = "BLSLogRule"

	AddonCCELogOperator = "cce-log-operator"

	BLSLogConfigYamlContainerPath = `apiVersion: cce.baidubce.com/v1
kind: LogConfig
metadata:
  name: cceautotest-container-path
  namespace: default
spec:
  srcConfig:
    srcType: container
    logType: internal
    srcDir: /share/log/
    matchPattern: ^test.*.log
    ttl: 3
    matchLabels:
      - key: io.kubernetes.container.name
        value: echo
      - key: io.kubernetes.pod.namespace
        value: default
  dstConfig:
    dstType: BLS
    logStore: cceautotest-logstore
    retention: 10
    rateLimit: 10
`
	DeploymentYamlContainerPath = `apiVersion: apps/v1
kind: Deployment
metadata:
  name: cceautotest-log-echo-to-path
  labels:
    app: cceautotest-log-echo-to-path
spec:
  replicas: 1
  minReadySeconds: 0
  strategy:
    type: RollingUpdate # 策略：滚动更新
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app: cceautotest-log-echo-to-path
  template:
    metadata:
      labels:
        app: cceautotest-log-echo-to-path
    spec:
      restartPolicy: Always
      containers:
        - name: echo
          image: hub.baidubce.com/cce/nginx-alpine-go:latest
          command: ["/bin/sh"]
          args: ["-c", "i=0; while true; do echo \"hi $i\" >> /share/log/test.log && echo \"hello $i\" >> /share/log/test01.log && sleep 100 && let i++; done"]
          imagePullPolicy: Always
          ports:
            - containerPort: 80
          volumeMounts:
            - name: log
              mountPath: "/share/log"
      volumes:
        - name: log
          emptyDir: {}
`

	DaemonSetYamlContainerPath = `apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: cceautotest-log-echo-to-path
  labels:
    app: cceautotest-log-echo-to-path
spec:
  selector:
    matchLabels:
      app: cceautotest-log-echo-to-path
  template:
    metadata:
      labels:
        app: cceautotest-log-echo-to-path
    spec:
      restartPolicy: Always
      containers:
        - name: echo
          image: hub.baidubce.com/cce/nginx-alpine-go:latest
          command: ["/bin/sh"]
          args: ["-c", "i=0; while true; do echo \"hi $i\" >> /share/log/test.log && echo \"hello $i\" >> /share/log/test01.log && sleep 100 && let i++; done"]
          imagePullPolicy: Always
          ports:
            - containerPort: 80
          volumeMounts:
            - name: log
              mountPath: "/share/log"
      volumes:
        - name: log
          emptyDir: {}
`

	BLSLogConfigYamlHostPath = `apiVersion: cce.baidubce.com/v1
kind: LogConfig
metadata:  
  name: host-messages
  namespace: kube-system
spec:
  srcConfig:
    srcType: host
    srcDir: /logbeat_host/var/log # 注意，host 日志需要添加 /logbeat_host 前缀
    matchPattern: messages # 采集 message 日志文件
    ttl: 3
  dstConfig:
    dstType: BLS
    logStore: host-messages
    retention: 10
    rateLimit: 10
`

	BLSLogConfigYamlKubeApiserver = `apiVersion: cce.baidubce.com/v1
kind: LogConfig
metadata:
  name: kube-apiserver-log                # 日志配置名字
  namespace: default                      # 日志配置所在 namespace
spec:
  srcConfig: # 定义日志来源 
    srcType: container                    # 日志类型为容器
    logType: stdout                       # 容器 stdout 输出日志
    ttl: 3                                # agent 采集日志时间范围
    matchLabels:                          # 这里的 label 对应 docker inspect 上的 label, 不是 pod 上的 label
      - key: io.kubernetes.container.name # 不用变
        value: kube-apiserver             # 改为具体 container name
      - key: io.kubernetes.pod.namespace  # 不用变
        value: kube-system                # 改为具体 namespace name
  dstConfig:
    dstType: BLS                          # 日志输出目的端类型，当前只支持 BLS
    logStore: kube-apiserver-log          # BLS logstore 名字，不存在会自动创建
    retention: 10                         # BLS logstore 中日志保存时间
    rateLimit: 10                         # 日志上传带宽限制 1-99 MB/s
`
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), BLSLogRule, NewBLSLogRule)
}

var _ cases.Interface = &blsLogRule{}

type blsLogRule struct {
	base *cases.BaseClient
}

// NewBLSLogRule - 测试用例
func NewBLSLogRule(ctx context.Context) cases.Interface {
	return &blsLogRule{}
}

func (c *blsLogRule) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	if base.BLSClient == nil {
		return errors.New("base.BLSClient is nil, please check BLSEndpoint")
	}

	// logger.WithValues("case", "BLSLogRule").Infof(ctx, "json raw config: %s", string(config))
	// if err := json.Unmarshal(config, &c.config); err != nil {
	// 	return err
	// }
	// logger.WithValues("case", "BLSLogRule").Infof(ctx, "config: %s", utils.ToJSON(c.config))

	c.base = base

	return nil
}

func (c *blsLogRule) Name() cases.CaseName {
	return BLSLogRule
}

func (c *blsLogRule) Desc() string {
	return "检查bls日志规则功能"
}

func (c *blsLogRule) Check(ctx context.Context) ([]cases.Resource, error) {
	// 检查cce-log-operator插件是否已安装
	resp, err := c.base.CCEClient.ListAddOns(ctx, c.base.ClusterID,
		&ccev2.ListParams{TargetAddons: AddonCCELogOperator},
		&bce.SignOption{},
	)
	if err != nil {
		logger.Errorf(ctx, "list addons %s failed: %v", AddonCCELogOperator, err.Error())
		return nil, err
	}
	if resp.Items[0].Instance != nil {
		logger.Infof(ctx, "addon %s is installed, status is %s", resp.Items[0].Meta.Name, resp.Items[0].Instance.Status.Phase)
		// 如果插件状态是Abnormal，直接退出用例
		if resp.Items[0].Instance.Status.Phase == ccev2.InstancePhaseAbnormal {
			return nil, fmt.Errorf("addon %s phase is Abnormal", resp.Items[0].Meta.Name)
		}
	} else {
		// 插件不存在则安装cce-log-operator插件
		_, err := c.base.CCEClient.InstallAddon(ctx, c.base.ClusterID,
			&ccev2.InstallParams{Name: AddonCCELogOperator},
			&bce.SignOption{},
		)
		if err != nil {
			logger.Errorf(ctx, "install addon %s failed: %v", AddonCCELogOperator, err.Error())
			return nil, err
		}
	}

	// 如果是托管集群，需要开启控制面日志采集
	if c.base.ClusterSpec.MasterConfig.MasterType == ccetypes.MasterTypeManagedPro {
		listLogstoreResp, err := c.base.CCEHostClient.ListLogStores(ctx, c.base.ClusterID, "", &bce.SignOption{})
		if err != nil {
			return nil, fmt.Errorf("list logstore failed: %v", err.Error())
		}
		// 如果没有开启集群控制面日志采集，则开启
		if !listLogstoreResp.EnableControlPlaneLog {
			createWorkflowResp, err := c.base.CCEHostClient.CreateWorkflow(ctx, c.base.ClusterID, &ccev2.CreateWorkflowRequest{
				WorkflowType: ccetypes.WorkflowTypeEnableControlPlaneLog,
				WorkflowConfig: ccetypes.WorkflowConfig{
					EnableControlPlaneLogWorkflowConfig: &ccetypes.EnableControlPlaneLogWorkflowConfig{
						EnableAPIServerLog:         true,
						EnableSchedulerLog:         true,
						EnableControllerManagerLog: true,
					},
				},
			}, &bce.SignOption{})
			if err != nil {
				return nil, fmt.Errorf("create workflow EnableControlPlaneLog failed: %v", err.Error())
			}
			workflowID := createWorkflowResp.WorkflowID
			// 等待workflow执行完成
			if err := resource.WaitForWorkflowPhase(ctx, c.base.CCEHostClient, c.base.ClusterID, workflowID, ccetypes.WorkflowPhaseSucceeded); err != nil {
				return nil, err
			}
		}
	}

	// 等待log-operator running
	var logOperator resource.K8SPod
	if err = logOperator.NewK8SPod(ctx, c.base, "kube-system", nil); err != nil {
		logger.Errorf(ctx, "new k8s pod failed: %v", err.Error())
		return nil, err
	}
	logOperator.SetMatchLabel("app", AddonCCELogOperator)
	logOperator.SetStatus(resource.StatusRunning)
	if err := resource.WaitForResourceReady(ctx, &logOperator); err != nil {
		return nil, err
	}

	LogConfigYamlList := []string{BLSLogConfigYamlContainerPath, BLSLogConfigYamlHostPath, BLSLogConfigYamlKubeApiserver}
	// managedPro集群，需要检查控制面日志
	if c.base.ClusterSpec.MasterConfig.MasterType == ccetypes.MasterTypeManagedPro {
		LogConfigYamlList = []string{BLSLogConfigYamlContainerPath, BLSLogConfigYamlHostPath}
	}

	// 创建日志规则
	for _, logConfigYaml := range LogConfigYamlList {
		if err := c.base.KubectlClient.Apply(ctx, ccetypes.K8S_1_13_10, c.base.KubeConfig, logConfigYaml); err != nil {
			logger.Errorf(ctx, "Deploy LogConfig failed: %s", err)
			return nil, err
		}

		// TODO: 判断LogConfig状态
	}

	// 创建工作负载
	if err := c.base.KubectlClient.Apply(ctx, ccetypes.K8S_1_13_10, c.base.KubeConfig, DaemonSetYamlContainerPath); err != nil {
		logger.Errorf(ctx, "Deploy DaemonSet failed: %s", err)
		return nil, err
	}
	var logPod resource.K8SPod
	if err = logPod.NewK8SPod(ctx, c.base, "default", nil); err != nil {
		logger.Errorf(ctx, "new k8s pod failed: %v", err.Error())
		return nil, err
	}
	logPod.SetMatchLabel("app", "cceautotest-log-echo-to-path")
	logPod.SetStatus(resource.StatusRunning)
	if err = resource.WaitForResourceReady(ctx, &logPod); err != nil {
		return nil, err
	}

	time.Sleep(3 * time.Minute)

	// 检查组件日志是否有异常（暂时不fail）
	podList, err := logOperator.GetPodList(ctx)
	for _, pod := range podList.Items {
		// 获取日志
		log, err := logOperator.GetPodLogs(ctx, pod.Name, &v1.PodLogOptions{})
		if err != nil {
			logger.Errorf(ctx, "get cce log operator pod[%s] logs failed: %v", pod.Name, err)
			// return nil, err
		} else {
			lines := strings.Split(log, "\n")
			for _, line := range lines {
				// logger.Infof(ctx, strings.Split(line, " ")[2])
				if strings.Contains(line, "[E]") {
					logger.Errorf(ctx, "Something went wrong in cce log operator: %s", line)
					// return nil, fmt.Errorf("Something went wrong in cce log operator: %s", line)
				}
			}
		}
	}

	// managedPro集群，需要检查控制面日志
	if c.base.ClusterSpec.MasterConfig.MasterType == ccetypes.MasterTypeManagedPro {
		if err = resource.WaitForFunc(ctx, c.CheckBLSLogForControlPlane, 30*time.Second, 3*time.Minute); err != nil {
			return nil, err
		}
	}

	// 检查业务日志, managedPro/custom 类型集群不检查apiserver pod日志采集
	if c.base.ClusterSpec.MasterConfig.MasterType != ccetypes.MasterTypeManagedPro && c.base.ClusterSpec.MasterConfig.MasterType != ccetypes.MasterTypeCustom {
		if err = resource.WaitForFunc(ctx, c.CheckBLSLogForKubeAPIServer, 1*time.Minute, 10*time.Minute); err != nil {
			return nil, err
		}
	}
	if err = resource.WaitForFunc(ctx, c.CheckBLSLogForContainerPath, 1*time.Minute, 10*time.Minute); err != nil {
		return nil, err
	}
	if err = c.CheckBLSLogForHostMessage(ctx); err != nil {
		return nil, err
	}

	// cce-log-operator插件卸载
	// _, err = c.base.CCEClient.UninstallAddon(ctx, c.base.ClusterID,
	// 	&ccev2.UninstallParams{Name: AddonName}, &bce.SignOption{})
	// if err != nil {
	// 	logger.Errorf(ctx, "uninstall addon %s failed: %v", AddonName, err.Error())
	// 	return nil, err
	// }

	return nil, err
}

func (c *blsLogRule) Clean(ctx context.Context) error {
	// TODO 卸载环境资源
	return nil
}

func (c *blsLogRule) Continue(ctx context.Context) bool {
	return true
}

func (c *blsLogRule) ConfigFormat() string {
	return ""
}

func (c *blsLogRule) CheckBLSLogForControlPlane(ctx context.Context) error {
	logStoreName := fmt.Sprintf("%s-k8s-log", c.base.ClusterID)
	currentTime := time.Now().UTC()
	startDateTime := currentTime.Add(-10 * time.Minute).Format("2006-01-02T15:04:05Z")
	endDateTime := currentTime.Format("2006-01-02T15:04:05Z")
	logger.Infof(ctx, "startDateTime: %s endDateTime: %s", startDateTime, endDateTime)

	for _, component := range []string{"kube-apiserver", "kube-controller-manager", "kube-scheduler"} {
		query := fmt.Sprintf("select * where @stream like '%%%s%%' limit 10", component)
		resp, err := c.base.BLSClient.QueryLogRecord(logStoreName, &api.QueryLogRecordArgs{
			Query:         query,
			StartDateTime: api.DateTime(startDateTime),
			EndDateTime:   api.DateTime(endDateTime),
		})
		logger.Infof(ctx, "query bls[%s]", query)
		if err != nil {
			return fmt.Errorf("QueryLogRecord[logStoreName=%s query=%s] failed: %v", logStoreName, query, err.Error())
		}

		// tagPodNameIdx := GetBLSLogRowColumnIdxFromFieldName(resp.ResultSet.Columns, "@tag_pod_name")
		if len(resp.ResultSet.Rows) == 0 {
			return fmt.Errorf("logStoreName=[%s] check control plane log query[%s] failed: no log record found", logStoreName, query)
		}

		// 2024.06.20 BLS新架构下日志集的日志没有@tag_pod_name列（只有@timestamp、@stream、@raw），即不再为新建的日志集增加默认的索引。所以这里暂时注释掉
		// https://console.cloud.baidu-int.com/devops/icafe/issue/CCE-15671/show?source=copy-shortcut
		// for _, row := range resp.ResultSet.Rows {
		// 	logger.Infof(ctx, "row: %s", utils.ToJSON(row))
		// 	if !strings.Contains(row[tagPodNameIdx].(string), component) {
		// 		return fmt.Errorf("logStoreName=[%s] check control plane log query[%s] @tag_pod_name[%s] failed: @tag_pod_name was not as expected", logStoreName, query, row[tagPodNameIdx].(string))
		// 	}
		// 	// TODO 检查是不是乱码
		// }
	}
	return nil
}

// TODO 检查bls task收集器状态

func (c *blsLogRule) CheckBLSLogForHostMessage(ctx context.Context) error {
	logStoreName := "host-messages"
	currentTime := time.Now().UTC()
	startDateTime := currentTime.Add(-10 * time.Minute).Format("2006-01-02T15:04:05Z")
	endDateTime := currentTime.Format("2006-01-02T15:04:05Z")

	resp, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
		PageNo:   1,
		PageSize: 100,
	}, nil)
	if err != nil {
		return fmt.Errorf("ListInstancesByPage[clusterID=%s] failed: %v", c.base.ClusterID, err.Error())
	}

	var errMsg string

	for _, ins := range resp.InstancePage.InstanceList {
		if ins.Spec.ClusterRole == ccetypes.ClusterRoleMaster {
			continue
		}
		name := ins.Spec.InstanceName
		if ins.Spec.InstanceGroupID != "" {
			name = ins.Spec.InstanceGroupID
		}
		query := fmt.Sprintf("select * where @raw like '%%%s%%' limit 10", name)
		resp, err := c.base.BLSClient.QueryLogRecord(logStoreName, &api.QueryLogRecordArgs{
			Query:         query,
			StartDateTime: api.DateTime(startDateTime),
			EndDateTime:   api.DateTime(endDateTime),
		})
		logger.Infof(ctx, "query bls[%s]", query)
		if err != nil {
			return fmt.Errorf("QueryLogRecord[logStoreName=%s query=%s] failed: %v", logStoreName, query, err.Error())
		}
		if len(resp.ResultSet.Rows) == 0 {
			errMsg += fmt.Sprintf("logStoreName=[%s] check host message log query[%s] failed: no log record found. MachineType[%s] OS[%s]",
				logStoreName, query, ins.Spec.MachineType, string(ins.Spec.InstanceOS.OSName)+" "+ins.Spec.InstanceOS.OSVersion) + "\n"
		}
	}
	if errMsg != "" {
		return fmt.Errorf(errMsg)
	}
	return nil
}

func (c *blsLogRule) CheckBLSLogForKubeAPIServer(ctx context.Context) error {
	logStoreName := "kube-apiserver-log"
	currentTime := time.Now().UTC()
	startDateTime := currentTime.Add(-10 * time.Minute).Format("2006-01-02T15:04:05Z")
	endDateTime := currentTime.Format("2006-01-02T15:04:05Z")

	query := fmt.Sprintf("select * where @stream like '%%%s%%' limit 10", "kube-apiserver")
	resp, err := c.base.BLSClient.QueryLogRecord(logStoreName, &api.QueryLogRecordArgs{
		Query:         query,
		StartDateTime: api.DateTime(startDateTime),
		EndDateTime:   api.DateTime(endDateTime),
	})
	logger.Infof(ctx, "query bls[%s]", query)
	if err != nil {
		return fmt.Errorf("QueryLogRecord[logStoreName=%s query=%s] failed: %v", logStoreName, query, err.Error())
	}
	if len(resp.ResultSet.Rows) == 0 {
		return fmt.Errorf("logStoreName=[%s] check kube api log query[%s] failed: no log record found", logStoreName, query)
	}

	return nil
}

func (c *blsLogRule) CheckBLSLogForContainerPath(ctx context.Context) error {
	logStoreName := "cceautotest-logstore"
	currentTime := time.Now().UTC()
	startDateTime := currentTime.Add(-10 * time.Minute).Format("2006-01-02T15:04:05Z")
	endDateTime := currentTime.Format("2006-01-02T15:04:05Z")

	var logPod resource.K8SPod
	if err := logPod.NewK8SPod(ctx, c.base, "default", nil); err != nil {
		logger.Errorf(ctx, "new k8s pod failed: %v", err.Error())
		return err
	}
	logPod.SetMatchLabel("app", "cceautotest-log-echo-to-path")
	logPod.SetStatus(resource.StatusRunning)
	podNameList := logPod.GetPodNameList()
	logger.Infof(ctx, "podNameList: %v", podNameList)

	for _, podName := range podNameList {
		query := fmt.Sprintf("select * where @stream = 'default_%s_echo' limit 10", podName) // bls命名规则default_podname_echo 如 default_cceautotest-log-echo-to-path-ftwx2_echo
		resp, err := c.base.BLSClient.QueryLogRecord(logStoreName, &api.QueryLogRecordArgs{
			Query:         query,
			StartDateTime: api.DateTime(startDateTime),
			EndDateTime:   api.DateTime(endDateTime),
		})
		logger.Infof(ctx, "query bls[%s]", query)
		if err != nil {
			return fmt.Errorf("QueryLogRecord[logStoreName=%s query=%s] failed: %v", logStoreName, query, err.Error())
		}
		rawIdx := GetBLSLogRowColumnIdxFromFieldName(resp.ResultSet.Columns, "@raw")
		// tagFilePathIdx := GetBLSLogRowColumnIdxFromFieldName(resp.ResultSet.Columns, "@tag_file_path")

		if len(resp.ResultSet.Rows) == 0 {
			return fmt.Errorf("logStoreName=[%s] check container path log query[%s] failed: no log record found", logStoreName, query)
		}

		for _, row := range resp.ResultSet.Rows {
			logger.Infof(ctx, "row: %v", row)
			// if strings.Contains(row[rawIdx].(string), "hi") {
			// 	if !strings.Contains(row[tagFilePathIdx].(string), "test.log") {
			// 		return fmt.Errorf("logStoreName[%s] check container path log query[%s] @raw[%s] @tag_file_path[%s] failed: @tag_file_path was not as expected",
			// 			logStoreName, query, row[rawIdx].(string), row[tagFilePathIdx].(string))
			// 	}

			// } else if strings.Contains(row[rawIdx].(string), "hello") {
			// 	if !strings.Contains(row[tagFilePathIdx].(string), "test01.log") {
			// 		return fmt.Errorf("logStoreName[%s] check container path log query[%s] @raw[%s] @tag_file_path[%s] failed: @tag_file_path was not as expected",
			// 			logStoreName, query, row[rawIdx].(string), row[tagFilePathIdx].(string))
			// 	}
			// } else {
			// 	return fmt.Errorf("logStoreName[%s] check container path log query[%s] @raw[%s] @tag_file_path[%s] failed: unexpected raw info",
			// 		logStoreName, query, row[rawIdx].(string), row[tagFilePathIdx].(string))
			// }
			if !strings.Contains(row[rawIdx].(string), "hi") && !strings.Contains(row[rawIdx].(string), "hello") {
				return fmt.Errorf("logStoreName[%s] check container path log query[%s] @raw[%s] failed: unexpected raw info",
					logStoreName, query, row[rawIdx].(string))
			}
			// TODO 检查有没有乱码
		}
	}

	return nil
}

func GetBLSLogRowColumnIdxFromFieldName(columns []string, fieldName string) int {
	for idx, column := range columns {
		if column == fieldName {
			return idx
		}
	}
	return -1
}
