/*
background:
开启异常事件推送BCM,再开启事件持久化，使得 获取事件列表会为空
只开启一个事件列表中的事件都存在

--------------------
2024/12/18, by <PERSON><PERSON><PERSON>, create
--------------------
DESCRIPTION
1、部署deployment
2、开bcm推送、事件持久化，等待两分钟，es轮询最大时间是两分钟，此时事件可以查到的
3、再关事件持久化，事件也可以查到的

*/

package monitor

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccemonitor"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	EventValidity      cases.CaseName = "EventValidity"
	TestDeployment                    = "test-event-deployment"
	TestImage                         = "registry.baidubce.com/cce/nginx-alpine-go:latest"
	AbnormalDeployment                = "abnormal-deployment"
	AbnormalImage                     = "registry.baidu.com/cce/nginx-abnormal-test:latest"
)

type eventValidity struct {
	base *cases.BaseClient
}

func init() {
	cases.AddCase(context.TODO(), EventValidity, NewEventValidity)
}

func NewEventValidity(ctx context.Context) cases.Interface {
	return &eventValidity{}
}

func (c *eventValidity) Name() cases.CaseName {
	return EventValidity
}

func (c *eventValidity) Desc() string {
	return "检查事件开启bcm，再开启持久化是否生效"
}

func (c *eventValidity) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	return
}

func (c *eventValidity) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID
	// 创建测试deployment和异常deployment
	logger.Infof(ctx, "create normal deployment")
	err = c.CreateDeployment(ctx, TestDeployment, TestImage, true)
	if err != nil {
		return nil, fmt.Errorf("check create %v deployment failed: %v", TestDeployment, err)
	}

	logger.Infof(ctx, "create abnormal deployment")
	err = c.CreateDeployment(ctx, AbnormalDeployment, AbnormalImage, false)
	if err != nil {
		return nil, fmt.Errorf("check create %v deployment failed: %v", AbnormalDeployment, err)
	}

	// 存在normal, warning event
	enableBCM := true
	// 开启异常事件推送BCM
	logger.Infof(ctx, "Enable bcm and query events")
	_, err = c.base.CCEClient.ToggleClusterBCM(ctx, clusterID, enableBCM, nil)
	if err != nil {
		return nil, err
	}

	err = c.QueryEvents(ctx)
	if err != nil {
		return nil, err
	}

	logger.Infof(ctx, "Enable event")
	// 开启事件持久化
	res, err := c.base.MonitorClient.ToggleEvents(ctx, clusterID, ccemonitor.EventStatusOpen, nil)
	if err != nil {
		return nil, err
	}

	if !res {
		return nil, fmt.Errorf("open events failed: %v", err)
	}

	err = c.QueryEvents(ctx)
	if err != nil {
		return nil, err
	}

	logger.Infof(ctx, "enable bcm and open event, events can be queried")
	logger.Infof(ctx, "close event")

	// 关闭事件持久化
	res, err = c.base.MonitorClient.ToggleEvents(ctx, clusterID, ccemonitor.EventStatusClose, nil)
	if err != nil {
		return nil, err
	}

	if !res {
		return nil, fmt.Errorf("enable events failed: %v", err)
	}

	err = c.QueryEvents(ctx)
	if err != nil {
		return nil, err
	}

	return
}

func (c *eventValidity) Clean(ctx context.Context) error {
	err := c.base.KubeClient.DeleteDeploymentAppsV1(ctx, metav1.NamespacePublic, TestDeployment)
	if err != nil {
		return fmt.Errorf("delete deployment %s failed: %v", TestDeployment, err)
	}

	err = c.base.KubeClient.DeleteDeploymentAppsV1(ctx, metav1.NamespacePublic, AbnormalDeployment)
	if err != nil {
		return fmt.Errorf("delete deployment %s failed: %v", AbnormalDeployment, err)
	}

	return nil
}

func (c *eventValidity) Continue(ctx context.Context) bool {
	return true
}

func (c *eventValidity) ConfigFormat() string {
	return ""
}

func (c *eventValidity) CreateDeployment(ctx context.Context, deployName, image string, checkRunning bool) error {
	replicas := int32(1)

	deployment := appsv1.Deployment{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Deployment",
			APIVersion: "apps/v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      deployName,
			Namespace: metav1.NamespacePublic,
			Labels: map[string]string{
				"app": deployName,
			},
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": deployName,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Name: deployName,
					Labels: map[string]string{
						"app": deployName,
					},
				},
				Spec: corev1.PodSpec{
					RestartPolicy: corev1.RestartPolicyAlways,
					Containers: []corev1.Container{
						{
							Name:            deployName,
							Image:           image,
							ImagePullPolicy: corev1.PullAlways,
						},
					},
				},
			},
		},
	}

	_, createErr := c.base.KubeClient.CreateDeploymentAppsV1(ctx, metav1.NamespacePublic, &deployment)

	if createErr != nil {
		return fmt.Errorf("create deployment %v err: %v", deployName, createErr)
	}

	if checkRunning {
		err := c.base.KubeClient.EnsureDeploymentRunningAppsV1(ctx, metav1.NamespacePublic, deployName,
			&kube.CheckOptions{
				Timeout:  2 * time.Minute,
				Interval: 2 * time.Second,
			})
		if err != nil {
			return fmt.Errorf("ensure deployment running %v err: %v", deployName, err)
		}
	}

	return nil
}

func (c *eventValidity) QueryEvents(ctx context.Context) error {
	// 当前时间前一小时
	startTime := time.Now().Add(-1 * time.Hour).UTC().Format(time.RFC3339)

	waitErr := resource.WaitForFunc(ctx, func(ctx context.Context) error {
		// 每次取最新时
		eventQueryArg := ccemonitor.EventQueryArg{
			StartTime:   startTime,
			EndTime:     time.Now().UTC().Format(time.RFC3339),
			ClusterUuid: c.base.ClusterID,
			Namespace:   metav1.NamespacePublic,
			PageNo:      1,
			PageSize:    100,
		}

		eventResults, err := c.base.MonitorClient.GetK8sEvent(ctx, &eventQueryArg, nil)
		if err != nil {
			return fmt.Errorf("get k8s events failed: %v", err)
		}

		if eventResults.TotalCount < 1 {
			return errors.New("no events found")
		}

		isWarningEventInNormal := false
		isWarningEventInWarning := false

		for _, eventResult := range eventResults.Result {
			logger.Infof(ctx, "event: %+v", eventResult)

			if strings.Contains(eventResult.InvolvedObject.Name, TestDeployment) && eventResult.Type == ccemonitor.WarningType {
				isWarningEventInNormal = true
				return fmt.Errorf("%v event exists in %v", ccemonitor.WarningType, TestDeployment)
			}

			if strings.Contains(eventResult.InvolvedObject.Name, AbnormalDeployment) && eventResult.Type == ccemonitor.WarningType {
				isWarningEventInWarning = true
				logger.Infof(ctx, "%v event exists in %v", ccemonitor.WarningType, AbnormalDeployment)
			}
		}

		if !isWarningEventInNormal && isWarningEventInWarning {
			return nil
		}

		return errors.New("unexpected events found in deployment")
	}, 10*time.Second, 8*time.Minute)

	if waitErr != nil {
		return fmt.Errorf("wait err: %v", waitErr)
	}

	return nil
}
