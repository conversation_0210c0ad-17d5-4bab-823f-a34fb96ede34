/* bos_logrule.go */
/*
modification history
--------------------
2022/10/8, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
*/

package monitor

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/baidubce/bce-sdk-go/services/bos/api"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	e2ecommon "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// BOSLogRule  log-agentv2
	BOSLogRule cases.CaseName = "BOSLogRule"

	AddonName = "cce-log-operator"

	BOSLogRuleYaml = `apiVersion: cce.io/v2
kind: LogRule
metadata:
  name: bos-demo
spec:
  logType: LOGTYPE
  storageType: bos
  bosConfig:
    secret: kube-system/bos-secret
    bucket: BUCKETNAME
    region: REGION
    endpoint: http://s3.REGION.bcebos.com
    timeKey: 1m
  sources:
  - namespace: '*'
    sourceType: '*'
    sourceName: '*'
`
	LogRuleDeploymentYaml = `apiVersion: apps/v1
kind: Deployment
metadata:
  name: logrule-deployment
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: logrule-test
  template:
    metadata:
      labels:
        app: logrule-test
    spec:
      containers:
      - name: nginx
        image: hub.baidubce.com/cce/nginx-alpine-go:latest
        command:
          - /bin/sh
          - '-c'
          - while true; do echo 'hello world'; sleep 5; done
        env:
          - name: cce_log_stdout
            value: 'true'
          - name: cce_log_internal
            value: "/usr/local/reg/logs/*.log"
        volumeMounts:
        - name: reg-log
          mountPath: /usr/local/reg/logs
      volumes:
      - name: reg-log
        emptyDir: {}
`

	bosSecretYAML = `apiVersion: v1
kind: Secret
metadata:
  name: bos-secret
  namespace: kube-system
data:
  bosak: ak-xx
  bossk: sk-xx`
)

var BucketName = map[string]string{
	"gz":  "bos-csi-test-gztest",
	"bj":  "bos-csi-test-bj",
	"bd":  "bos-csi-test-bd",
	"sz":  "bos-csi-test-sz",
	"fwh": "bos-csi-test-wh",
	"hkg": "bos-csi-test-hk",
}

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), BOSLogRule, NewBOSLogRule)
}

var _ cases.Interface = &bosLogRule{}

type bosLogRule struct {
	base   *cases.BaseClient
	config bosLogRuleConfig
}

type bosLogRuleConfig struct {
	LogRules []LogRule `json:"logRules"`
}

type LogRule struct {
	// LogType stdout/internal
	LogType string `json:"logType"`

	BucketName string `json:"bucketName"`
}

// NewBOSLogRule - 测试案例
func NewBOSLogRule(ctx context.Context) cases.Interface {
	return &bosLogRule{}
}

func (c *bosLogRule) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	logger.WithValues("case", "BOSLogRule").Infof(ctx, "json raw config: %s", string(config))
	if err := json.Unmarshal(config, &c.config); err != nil {
		return err
	}
	logger.WithValues("case", "BOSLogRule").Infof(ctx, "config: %s", utils.ToJSON(c.config))

	c.base = base

	return nil
}

func (c *bosLogRule) Name() cases.CaseName {
	return BOSLogRule
}

func (c *bosLogRule) Desc() string {
	return "[待补充]"
}

func (c *bosLogRule) Check(ctx context.Context) ([]cases.Resource, error) {
	// 安装log-agent-v2插件
	_, err := c.base.CCEClient.InstallAddon(ctx, c.base.ClusterID,
		&ccev2.InstallParams{
			Name:    AddonName,
			Params:  "CCELogOperatorImage: registry.baidubce.com/cce-plugin-pro/cce-log-operator:v2.2.3",
			Version: "2.2.3"},
		&bce.SignOption{},
	)
	if err != nil {
		logger.Errorf(ctx, "install addon %s failed: %v", AddonName, err.Error())
		return nil, err
	}

	// 等待log-operator running
	var logOperator e2ecommon.K8SPod
	if err = logOperator.NewK8SPod(ctx, c.base, "kube-system", nil); err != nil {
		logger.Errorf(ctx, "new k8s pod failed: %v", err.Error())
		return nil, err
	}

	logOperator.SetMatchLabel("app", AddonName)
	logOperator.SetStatus(e2ecommon.StatusRunning)
	if err := e2ecommon.WaitForResourceReady(ctx, &logOperator); err != nil {
		return nil, err
	}

	// 创建bos-secret
	ak := base64.StdEncoding.EncodeToString([]byte(c.base.Credentials.AccessKeyID))
	sk := base64.StdEncoding.EncodeToString([]byte(c.base.Credentials.SecretAccessKey))
	bosYaml := strings.ReplaceAll(strings.ReplaceAll(bosSecretYAML, "ak-xx", ak), "sk-xx", sk)
	if _, err = c.base.AppClient.PostAppResource(ctx, c.base.ClusterID, "bos-secret", bosYaml, "kube-system", nil); err != nil {
		logger.Errorf(ctx, "Deploy BosSecret \n%s\n failed: %s", bosYaml, err)
		return nil, err
	}

	// (为了先创建工作负载，在校验日志文件，对bucket和对应的pod进行映射)
	var podBucketMap = make(map[string][]string)
	// 创建日志规则和工作负载
	for i, logRule := range c.config.LogRules {
		bucketName := logRule.BucketName
		if logRule.BucketName == "" {
			bucketName = BucketName[c.base.Region]
		}

		// 创建logrule
		var logYaml string
		logYaml = strings.ReplaceAll(BOSLogRuleYaml, "LOGTYPE", logRule.LogType)
		logYaml = strings.ReplaceAll(logYaml, "bos-demo", "bos-demo-"+strconv.Itoa(i))
		logYaml = strings.ReplaceAll(logYaml, "BUCKETNAME", bucketName)
		logYaml = strings.ReplaceAll(logYaml, "REGION", c.base.Region)
		if _, err = c.base.AppClient.PostAppResource(ctx, c.base.ClusterID, "bos-demo-"+strconv.Itoa(i), logYaml, "default", nil); err != nil {
			logger.Errorf(ctx, "Deploy logrule default/bos-demo failed: %s", err)
			return nil, err
		}
		// TODO: 判断logrule状态

		// 创建工作负载（单pod）
		var deploymentYaml string
		deploymentYaml = strings.ReplaceAll(LogRuleDeploymentYaml, "logrule-test", "logrule-test-"+strconv.Itoa(i))
		deploymentYaml = strings.ReplaceAll(deploymentYaml, "logrule-deployment", "logrule-deployment-"+strconv.Itoa(i))
		if _, err = c.base.AppClient.PostAppResource(ctx, c.base.ClusterID, "logrule-deployment-"+strconv.Itoa(i), deploymentYaml, "default", nil); err != nil {
			logger.Errorf(ctx, "Deploy deployment default/logrule-deployment failed: %s", err)
			return nil, err
		}
		var logPod e2ecommon.K8SPod
		if err = logPod.NewK8SPod(ctx, c.base, "default", nil); err != nil {
			logger.Errorf(ctx, "new k8s pod failed: %v", err.Error())
			return nil, err
		}
		logPod.SetMatchLabel("app", "logrule-test-"+strconv.Itoa(i))
		logPod.SetStatus(e2ecommon.StatusRunning)
		if err = e2ecommon.WaitForResourceReady(ctx, &logPod); err != nil {
			return nil, err
		}
		podName := logPod.GetPodName()
		podBucketMap[bucketName] = append(podBucketMap[bucketName], podName)

		// 若logType为internal,则在容器中的日志路径下添加日志文件
		if logRule.LogType == "internal" {
			logger.Infof(ctx, "log type is internal, then add logfile in pod")
			interanlCmd := []string{"sh", "-c", "echo reg-test-message > /usr/local/reg/logs/reg.log"}

			if _, err = logPod.ExecInPod(ctx, podName, "default", interanlCmd); err != nil {
				return nil, err
			}
		}
	}

	for bucketName, podsName := range podBucketMap {
		// 校验bucket 下是否有对应日志文件
		if err = CheckBucketLogFileExisted(ctx, c.base, bucketName, podsName); err != nil {
			return nil, err
		}
	}

	// cce-log-operator插件卸载
	_, err = c.base.CCEClient.UninstallAddon(ctx, c.base.ClusterID,
		&ccev2.UninstallParams{Name: AddonName}, &bce.SignOption{})
	if err != nil {
		logger.Errorf(ctx, "uninstall addon %s failed: %v", AddonName, err.Error())
		return nil, err
	}

	return nil, err
}

func (c *bosLogRule) Clean(ctx context.Context) error {
	for _, logRule := range c.config.LogRules {
		bucketName := logRule.BucketName
		if logRule.BucketName == "" {
			bucketName = BucketName[c.base.Region]
		}

		// 删除bucket下对应日志文件
		listObjectResult, err := c.base.BOSClient.ListObjects(bucketName, &api.ListObjectsArgs{
			Prefix: "logs/",
		})
		if err != nil {
			logger.Errorf(ctx, "list objects failed: %v", err.Error())
			return err
		}

		var preObject []string
		var object []string
		for _, obj := range listObjectResult.Contents {
			preObject = append(preObject, obj.Key)
			object = append(object, obj.Key[strings.LastIndex(obj.Key, "/")+1:])
		}

		c.base.BOSClient.DeleteMultipleObjectsFromKeyList(bucketName, preObject)
		_, err = c.base.BOSClient.DeleteMultipleObjectsFromKeyList(bucketName, object)
		if err != nil && !strings.Contains(err.Error(), "the key list to be deleted is empty") {
			logger.Errorf(ctx, "delete file failed: %v", err.Error())
			return err
		}
	}

	return nil
}

func (c *bosLogRule) Continue(ctx context.Context) bool {
	return true
}

func (c *bosLogRule) ConfigFormat() string {
	return ""
}

// CheckBucketLogFileExisted 检查bucket内是否有日志记录的文件
func CheckBucketLogFileExisted(ctx context.Context, base *cases.BaseClient, bucketName string, podsName []string) error {
	timer := time.NewTimer(20 * time.Minute)
	defer timer.Stop()
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-timer.C:
			return errors.New("timeout(20m0s) waitting for bos bucket logfile existed check")
		case <-ticker.C:
			// 列举有日志插件上传的文件（取文件夹级）
			listObjectResult, err := base.BOSClient.ListObjects(bucketName, &api.ListObjectsArgs{
				Prefix:    "logs/",
				Delimiter: "/",
			})
			if err != nil {
				return fmt.Errorf("list objects from bucket %s failed: %v", bucketName, err.Error())
			}
			// 统计含有容器名的日志目录个数（不重复）
			var doesPodLogsExisted = make(map[string]bool)
			for _, obj := range listObjectResult.CommonPrefixes {
				for _, podName := range podsName {
					if strings.Contains(obj.Prefix, podName) {
						doesPodLogsExisted[podName] = true
					}
				}
			}

			if len(doesPodLogsExisted) < len(podsName) {
				logger.Infof(ctx, "logfile number is lower than expect, retry")
				continue
			}

			logger.Infof(ctx, "logfile number is expected")
			return nil
		}
	}
}
