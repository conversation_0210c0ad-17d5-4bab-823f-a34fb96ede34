// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/01 , by z<PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
GetClusterMaster,获取集群master
*/

package monitor

import (
	"context"
	"errors"
	"fmt"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	// GetClusterMaster
	GetClusterMaster cases.CaseName = "GetClusterMaster"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), GetClusterMaster, NewGetClusterMaster)
}

type getClusterMaster struct {
	base      *cases.BaseClient
	clusterID string
}

// NewTemplate - 测试案例
func NewGetClusterMaster(ctx context.Context) cases.Interface {
	return &getClusterMaster{}
}

func (c *getClusterMaster) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base

	return nil
}

func (c *getClusterMaster) Name() cases.CaseName {
	return GetClusterMaster
}

func (c *getClusterMaster) Desc() string {
	return "检查集群Master"
}

func (c *getClusterMaster) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	clusterID := c.base.ClusterID
	cluster, err := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "get cluster failed: %v", err)
		return nil, err
	}
	if cluster.Cluster.Spec.MasterConfig.MasterType == types.MasterTypeManagedPro {
		logger.Infof(ctx, "cluster type is managed, skip check master")
		return nil, nil
	}

	result, err := c.base.MonitorClient.GetClusterMaster(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "Get cluster master failed: %s", err)
		return nil, err
	}
	if len(result) != 0 {
		// 检查获取的节点是否为Master
		if result[0].ClusterRole != "master" {
			return nil, fmt.Errorf("%s get cluster master failed", clusterID)
		}
	}

	return resources, nil
}

func (c *getClusterMaster) Clean(ctx context.Context) error {
	return nil
}

func (c *getClusterMaster) Continue(ctx context.Context) bool {
	return true
}

func (c *getClusterMaster) ConfigFormat() string {
	return ""
}
