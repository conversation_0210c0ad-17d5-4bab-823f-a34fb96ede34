/* check_cluster_report.go */
/*
modification history
--------------------
2024/9/25, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
1、校验集群服务画像
*/

package monitor

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccemonitor"
)

const (
	CheckClusterReport cases.CaseName = "CheckClusterReport"
)

type checkClusterReport struct {
	base *cases.BaseClient
}

func init() {
	cases.AddCase(context.TODO(), CheckClusterReport, NewCheckClusterReport)
}

func NewCheckClusterReport(ctx context.Context) cases.Interface {
	return &checkClusterReport{}
}

func (c *checkClusterReport) Name() cases.CaseName {
	return CheckClusterReport
}

func (c *checkClusterReport) Desc() string {
	return "校验集群服务画像"
}

func (c *checkClusterReport) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	return
}

func (c *checkClusterReport) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID
	category := map[string]ccemonitor.Category{
		"镜像": ccemonitor.StandardImageCheck,
		"应用": ccemonitor.StandardAppCheck,
		"网络": ccemonitor.StandardNetworkCheck,
		"安全": ccemonitor.StandardSecurityCheck,
	}

	for k, v := range category {
		_, imageErr := c.base.MonitorClient.GetClusterReport(ctx, clusterID, &ccemonitor.ClusterReportRequest{
			ClusterID: clusterID,
			Category:  v,
		}, nil)
		if imageErr != nil {
			err = fmt.Errorf("get %s report failed: %v", k, imageErr)
			return
		}
	}

	return
}

func (c *checkClusterReport) Clean(ctx context.Context) error {
	return nil
}

func (c *checkClusterReport) Continue(ctx context.Context) bool {
	return true
}

func (c *checkClusterReport) ConfigFormat() string {
	return ""
}
