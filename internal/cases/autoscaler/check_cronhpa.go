package autoscaler

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	v1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	res "k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/yaml"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appservice"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CheckCronhpa cases.CaseName = "CheckCronhpa"

	TargetSize      = 3
	DefaultReplicas = 1

	AddonCCECronhpaController = "cce-cronhpa-controller"
	DefaultNameSpace          = "default"
	DeploymentCronHPAName     = "deployment-cronhpa"

	CronHPAName = "cronhpa-sample"
	CronHPAYaml = `apiVersion: cce.baidubce.com/v1
kind: CronHPA
metadata:
  name: cronhpa-sample
spec:
   scaleTargetRef:
      apiVersion: apps/v1
      kind: Deployment
      name: deployment-cronhpa
   crons:
   - name: "scale-up"
     schedule: "5 * * * * *"
     targetSize: 3`
)

type checkCronhpa struct {
	base *cases.BaseClient
}

func init() {
	cases.AddCase(context.TODO(), CheckCronhpa, NewCheckCronhpa)
}

func NewCheckCronhpa(ctx context.Context) cases.Interface {
	return &checkCronhpa{}
}

func (c *checkCronhpa) Name() cases.CaseName {
	return CheckCronhpa
}

func (c *checkCronhpa) Desc() string {
	return "检查cronhpa删除是否正常"
}

func (c *checkCronhpa) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	return
}

func (c *checkCronhpa) Check(ctx context.Context) (resources []cases.Resource, err error) {
	// 0、检查cce-cronhpa-controller插件是否已安装
	err = c.installCronHPA(ctx)
	if err != nil {
		return nil, err
	}

	// 1、创建工作负载，创建cronhpa
	err = c.applyDeployment(ctx, DefaultReplicas)
	if err != nil {
		return nil, err
	}
	_, err = c.base.AppClient.PostAppResource(ctx, c.base.ClusterID, CronHPAName, CronHPAYaml, corev1.NamespaceDefault, nil)
	if err != nil {
		return nil, fmt.Errorf("create deployment failed, err: %v", err)
	}

	// 2、等待工作负载的副本数达到3
	err = common.WaitForFunc(ctx, func(ctx context.Context) (err error) {
		err = c.getDeploymentReplicas(ctx, TargetSize)
		return err
	}, 10*time.Second, 3*time.Minute)
	if err != nil {
		return nil, fmt.Errorf("cronHPA has not scaled deployment '%s' to the desired 3 replicas, err: %v", DeploymentCronHPAName, err)
	}
	logger.Infof(ctx, "CronHPA has scaled deployment '%s' to the desired 3 replicas.", DeploymentCronHPAName)

	// 3、通过接口修改cronhpa，添加排除日期
	getRes, err := c.base.AppClient.GetCronHPAByName(ctx, c.base.ClusterID, DefaultNameSpace, "cronHPAName", CronHPAName, nil)
	if err != nil {
		return nil, fmt.Errorf("get cronHPA failed, err: %v", err)
	}

	if len(getRes.CronHPAs) == 0 {
		return nil, fmt.Errorf("cronHPA %v has no data", CronHPAName)
	}

	data := strconv.Itoa(time.Now().Day())
	excludeDate := "* * * [] * *"
	excludeDate = strings.ReplaceAll(excludeDate, "[]", data)
	excludeDates := []string{excludeDate}

	modifyReq := &appservice.CronHPARequest{
		ClusterID: c.base.ClusterID,
		Namespace: DefaultNameSpace,
		Name:      DeploymentCronHPAName,
		CronHPA: appservice.CronHPAArgs{
			Name:           CronHPAName,
			Namespace:      DefaultNameSpace,
			Kind:           getRes.CronHPAs[0].Spec.ScaleTargetRef.Kind,
			KindName:       DeploymentCronHPAName,
			Cron:           getRes.CronHPAs[0].Spec.Crons,
			ExcludeDates:   excludeDates,
			ScaleTargetRef: getRes.CronHPAs[0].Spec.ScaleTargetRef,
		},
	}

	_, err = c.base.AppClient.ModifyCronHPA(ctx, c.base.ClusterID, DefaultNameSpace, CronHPAName, modifyReq, nil)

	if err != nil {
		return nil, fmt.Errorf("modify cronHPA failed, err: %v", err)
	}

	logger.Infof(ctx, " CronHPA %v has been modified with excludeDates %v ", DeploymentCronHPAName, excludeDates)

	err = common.WaitForFunc(ctx, func(ctx context.Context) (err error) {
		getRes, err = c.base.AppClient.GetCronHPAByName(ctx, c.base.ClusterID, DefaultNameSpace, "cronHPAName", CronHPAName, nil)
		if err != nil {
			return fmt.Errorf("get cronHPA failed, err: %v", err)
		}

		if len(getRes.CronHPAs) == 0 {
			return fmt.Errorf("cronHPA %v has no data", CronHPAName)
		}

		if getRes.CronHPAs[0].Status.Conditions[0].State == appservice.Succeed && strings.Contains(getRes.CronHPAs[0].Status.Conditions[0].Message, "cron hpa job scale-up executed successfully") {
			logger.Infof(ctx, "CronHPA %v has been executed successfully", CronHPAName)
			return nil
		}

		return fmt.Errorf("CronHPA %v has not been executed, waiting for execution ", CronHPAName)
	}, 10*time.Second, 2*time.Minute)

	if err != nil {
		return nil, fmt.Errorf("CronHPA %v has not been executed successfully, err: %v", CronHPAName, err)
	}

	err = c.getDeploymentReplicas(ctx, TargetSize)
	if err != nil {
		return nil, fmt.Errorf("get deployment replicas failed after modified cronhpa, err: %v", err)
	}

	// 4、删除CronHPA
	err = c.base.AppClient.DeleteCronHPA(ctx, c.base.ClusterID, &appservice.DeleteCronHPAArgs{
		Kind:        "CronHPA",
		ClusterUuid: c.base.ClusterID,
		ResourceList: []appservice.CronHPAResource{
			{
				Kind:      "CronHPA",
				Group:     "cce.baidubce.com",
				Version:   "v1",
				NameSpace: DefaultNameSpace,
				Name:      CronHPAName,
			},
		},
		Method: "delete",
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("delete cronhpa failed: %v", err)
	}

	// 5、将工作负载pod减少为DefaultReplicas ，检查副本数是否达到预期
	_, err = c.base.AppClient.ScaleAppResource(ctx, c.base.ClusterID, "deployment", DefaultNameSpace, DeploymentCronHPAName, strconv.Itoa(DefaultReplicas), nil)
	if err != nil {
		return nil, fmt.Errorf("scale deployment failed: %s", err)
	}
	err = common.WaitForFunc(ctx, func(ctx context.Context) (err error) {
		err = c.getDeploymentReplicas(ctx, DefaultReplicas)
		return err
	}, 10*time.Second, time.Minute*3)
	if err != nil {
		return nil, fmt.Errorf("scale deployment replicas failed: %v", err)
	}

	// 6、等待1min，确保工作负载副本数为DefaultReplicas
	time.Sleep(1 * time.Minute)
	err = c.getDeploymentReplicas(ctx, DefaultReplicas)
	if err != nil {
		return nil, err
	}
	return
}

func (c *checkCronhpa) Clean(ctx context.Context) error {
	// 删除 Deployment
	if err := c.base.AppClient.DeleteAppResource(ctx, c.base.ClusterID, "deployment", DefaultNameSpace, DeploymentCronHPAName, nil); err != nil {
		return fmt.Errorf("DeleteAppResource %s failed: %s", DeploymentCronHPAName, err)
	}
	return nil
}

func (c *checkCronhpa) Continue(ctx context.Context) bool {
	return true
}

func (c *checkCronhpa) ConfigFormat() string {
	return ""
}

func (c *checkCronhpa) applyDeployment(ctx context.Context, replicas int32) error {
	deployment := v1.Deployment{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Deployment",
			APIVersion: "apps/v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      DeploymentCronHPAName,
			Namespace: corev1.NamespaceDefault,
			Labels: map[string]string{
				"app": DeploymentCronHPAName,
			},
		},
		Spec: v1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": DeploymentCronHPAName,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": DeploymentCronHPAName,
					},
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{{
						Name:            DeploymentCronHPAName,
						Image:           "registry.baidubce.com/public-tools/stress-ng:latest",
						ImagePullPolicy: corev1.PullIfNotPresent,
						Command:         []string{"stress-ng"},
						Args:            []string{"-c", "2"},
						Resources: corev1.ResourceRequirements{
							Requests: corev1.ResourceList{
								corev1.ResourceCPU:    res.MustParse("1"),
								corev1.ResourceMemory: res.MustParse("1000Mi"),
							},
						},
					}},
				},
			},
		},
	}
	out, err := yaml.Marshal(&deployment)
	if err != nil {
		return fmt.Errorf("marshal deployment failed, err: %v", err)
	}
	deploymentYaml := string(out)
	_, err = c.base.AppClient.PostAppResource(ctx, c.base.ClusterID, DeploymentCronHPAName, deploymentYaml, corev1.NamespaceDefault, nil)
	if err != nil {
		return fmt.Errorf("create deployment failed, err: %v", err)
	}

	logger.Infof(ctx, "deploy test deployment success, replicas: %d", replicas)

	return nil
}

func (c *checkCronhpa) installCronHPA(ctx context.Context) error {
	resp, err := c.base.CCEClient.ListAddOns(ctx, c.base.ClusterID,
		&ccev2.ListParams{TargetAddons: AddonCCECronhpaController},
		&bce.SignOption{},
	)
	if err != nil {
		return fmt.Errorf("list addons %s failed: %v", AddonCCECronhpaController, err.Error())
	}

	if resp != nil && len(resp.Items) != 0 && resp.Items[0].Instance != nil {
		logger.Infof(ctx, "addon %s is installed, status is %s", resp.Items[0].Meta.Name, resp.Items[0].Instance.Status.Phase)
		// 如果插件状态是Abnormal，直接退出用例
		if resp.Items[0].Instance.Status.Phase == ccev2.InstancePhaseAbnormal {
			return fmt.Errorf("addon %s phase is Abnormal", resp.Items[0].Meta.Name)
		}
		return nil
	}

	// 插件不存在则安装插件
	_, err = c.base.CCEClient.InstallAddon(ctx, c.base.ClusterID,
		&ccev2.InstallParams{Name: AddonCCECronhpaController},
		&bce.SignOption{},
	)
	if err != nil {
		return fmt.Errorf("install addon %s failed: %v", AddonCCECronhpaController, err)
	}

	return nil
}

func (c *checkCronhpa) getDeploymentReplicas(ctx context.Context, expectReplicas int32) error {
	deploymentResource, err := c.base.AppClient.GetAppDeploymentByName(ctx, c.base.ClusterID, DeploymentCronHPAName, DefaultNameSpace, nil)
	if err != nil {
		return fmt.Errorf("get deployment resource %s failed: %v", DeploymentCronHPAName, err)
	}
	if deploymentResource == nil {
		return fmt.Errorf("get deployment resource %s failed", DeploymentCronHPAName)
	}
	if deploymentResource.StatusInfo.Replicas != expectReplicas {
		return fmt.Errorf("deployment %s replicas expect to be %d, now: %d ", DeploymentCronHPAName, expectReplicas, deploymentResource.StatusInfo.Replicas)
	}
	return nil
}
