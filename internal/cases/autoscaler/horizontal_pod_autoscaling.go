// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
horizontal pod autoscaling
*/

package autoscaler

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	// hoizontal pod autoscaling
	HorizontalPodAutoscaling cases.CaseName = "HorizontalPodAutoscaling"

	DeploymentYaml = `apiVersion: apps/v1
kind: Deployment
metadata:
  name: test-e2e-hpa-deploy
  labels:
    app: test-e2e-hpa-deploy
spec:
  replicas: 1
  minReadySeconds: 0
  selector:
    matchLabels:
      app: test-e2e-hpa-deploy
  template:
    metadata:
      labels:
        app: test-e2e-hpa-deploy
    spec:
      containers:
        - name: hpa
          image: registry.baidubce.com/cce-public/hpa-example
          ports:
          - containerPort: 80
            protocol: TCP
          resources: # 资源限制
            limits:
              cpu: 250m
              memory: 512Mi
            requests:
              cpu: 10m
              memory: 5Mi`

	ServiceYaml = `apiVersion: v1
kind: Service
metadata:
  name: test-e2e-hpa-service
spec:
    selector:
        app: test-e2e-hpa-deploy
    ports:
    - port: 80
      protocol: TCP
      targetPort: 80`

	HpaYaml = `apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: test-e2e-hpa
  namespace: default
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: test-e2e-hpa-deploy
  minReplicas: 1
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: AverageValue
        averageValue: 1m`
)

func init() {
	cases.AddCase(context.TODO(), HorizontalPodAutoscaling, NewHorizontalPodAutoscaling)
}

type horizontalPodAutoscaling struct {
	base *cases.BaseClient
}

// horizontalPodAutoscaling
func NewHorizontalPodAutoscaling(ctx context.Context) cases.Interface {
	return &horizontalPodAutoscaling{}
}

func (c *horizontalPodAutoscaling) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base

	return nil
}

func (c *horizontalPodAutoscaling) Name() cases.CaseName {
	return HorizontalPodAutoscaling
}

func (c *horizontalPodAutoscaling) Desc() string {
	return "测试水平扩展"
}

func (c *horizontalPodAutoscaling) Check(ctx context.Context) ([]cases.Resource, error) {
	// create hpa deploy and check pod running
	hpa_deploy_name := "test-e2e-hpa-deploy"
	_, err := c.base.AppClient.PostAppResource(ctx, c.base.ClusterID,
		hpa_deploy_name, DeploymentYaml, "default", nil)
	if err != nil {
		logger.Errorf(ctx, "hpa deploy %s created failed: %v", hpa_deploy_name, err)
		return nil, err
	}
	timeoutCtx, cancelFn := context.WithTimeout(ctx, time.Minute*10)
	timeout := true
	pollDeployment := func(_ context.Context) {
		deploymentResource, err := c.base.AppClient.GetAppDeploymentByName(ctx, c.base.ClusterID,
			hpa_deploy_name, "default", nil)
		if err != nil {
			logger.Errorf(ctx, "get hpa deploy %s err: %v", hpa_deploy_name, err)
			return
		}

		if deploymentResource.Status == "Running" &&
			deploymentResource.StatusInfo.Available == deploymentResource.StatusInfo.Replicas {
			cancelFn()
			timeout = false
			logger.Infof(ctx, "hpa deploy %s ready, status: %s", hpa_deploy_name, deploymentResource.Status)
		} else {
			timeout = true
		}
	}
	wait.UntilWithContext(timeoutCtx, pollDeployment, time.Second*5)
	if timeout {
		err := fmt.Errorf("wait hpa deploy %s ready timeout", hpa_deploy_name)
		logger.Errorf(ctx, "%v", err)
		return nil, err
	}

	// create hpa service
	hpa_service_name := "test-e2e-hpa-service"
	_, err = c.base.AppClient.PostAppResource(ctx, c.base.ClusterID,
		hpa_service_name, ServiceYaml, "default", nil)
	if err != nil {
		logger.Errorf(ctx, "hpa service %s created failed: %s", hpa_service_name, err)
		return nil, err
	}
	logger.Infof(ctx, "hpa service %s created", hpa_service_name)

	// get cluster k8s version
	clusterInfo, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "Get cluster failed: %s", err)
		return nil, err
	}

	// create hpa
	hpaYaml := HpaYaml
	if clusterInfo.Cluster.Spec.K8SVersion <= ccetypes.K8S_1_22_5 {
		hpaYaml = strings.ReplaceAll(hpaYaml, "autoscaling/v2", "autoscaling/v2beta2")
	}
	hpaName := "test-e2e-hpa"
	_, err = c.base.AppClient.PostAppResource(ctx, c.base.ClusterID,
		hpaName, hpaYaml, "default", nil)
	if err != nil {
		logger.Errorf(ctx, "hpa %s created failed: %s", hpaName, err)
		return nil, err
	}
	logger.Infof(ctx, "hpa %s created", hpaName)

	// edit hpa change maxReplicas 10 to 5
	hpaEdit, err := c.base.K8SClient.AutoscalingV1().HorizontalPodAutoscalers("default").Get(ctx, hpaName, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "get hpa %s err: %v", hpaName, err)
	}
	hpaEdit.Spec.MaxReplicas = 5
	hpaUpdate, err := c.base.K8SClient.AutoscalingV1().HorizontalPodAutoscalers("default").Update(ctx, hpaEdit, metav1.UpdateOptions{})
	if err != nil {
		logger.Errorf(ctx, "update hpa %s err: %v", hpaName, err)
	}
	logger.Infof(ctx, "update hpa %s maxReplicas to 5", hpaName)
	// check hpa
	if hpaUpdate.Spec.MaxReplicas != hpaEdit.Spec.MaxReplicas {
		err = fmt.Errorf("update hpa %s maxReplicas not equal", hpaName)
		logger.Errorf(ctx, "%v", err)
	}

	// create service proxy and send requests
	execPodSpec := c.CreateExecPodObject()
	_, err = c.base.K8SClient.CoreV1().Pods(execPodSpec.Namespace).Create(ctx, execPodSpec, metav1.CreateOptions{})
	if err != nil {
		logger.Errorf(ctx, "create hpa exec pod %s error: %v", execPodSpec.Name, err)
	}

	// check hpa deploy pod replicas > 1
	timeoutCtx, cancelFn = context.WithTimeout(ctx, time.Minute*10)
	timeout = true
	pollDeployment = func(_ context.Context) {
		deploymentResource, err := c.base.AppClient.GetAppDeploymentByName(ctx, c.base.ClusterID,
			hpa_deploy_name, "default", nil)
		if err != nil {
			logger.Errorf(ctx, "get hpa deploy %s err: %v", hpa_deploy_name, err)
			return
		}

		if deploymentResource.StatusInfo.Available > 1 {
			cancelFn()
			logger.Infof(ctx, "hpa deploy %s available replicas: %d", hpa_deploy_name, deploymentResource.StatusInfo.Available)
			timeout = false
		} else {
			logger.Errorf(ctx, "hpa deploy %s available replicas: %d", hpa_deploy_name, deploymentResource.StatusInfo.Available)
			timeout = true
		}
	}
	wait.UntilWithContext(timeoutCtx, pollDeployment, time.Second*5)
	if timeout {
		err := fmt.Errorf("wait hpa deploy %s available replicas > 1 timeout", hpa_deploy_name)
		logger.Errorf(ctx, "%v", err)
		return nil, err
	}

	// delete exec pod
	if err := c.base.K8SClient.CoreV1().Pods(execPodSpec.Namespace).Delete(ctx, execPodSpec.Name, metav1.DeleteOptions{}); err != nil {
		logger.Errorf(ctx, "delete pod %s error: %v", execPodSpec.Name, err)
	}

	// delete hpa
	if err := c.base.AppClient.DeleteAppResource(ctx, c.base.ClusterID, "horizontalpodautoscaler",
		"default", hpaName, nil); err != nil {
		logger.Errorf(ctx, "delete hpa %s error: %s", hpaName, err)
		return nil, err
	}
	logger.Infof(ctx, "delete hpa %s success", hpaName)

	// delete hpa service
	if err := c.base.AppClient.DeleteAppResource(ctx, c.base.ClusterID, "service",
		"default", hpa_service_name, nil); err != nil {
		logger.Errorf(ctx, "delete hpa service %s error: %v", hpa_service_name, err)
		return nil, err
	}
	logger.Infof(ctx, "delete hpa service %s success", hpa_service_name)

	// delete hpa deploy
	if err := c.base.AppClient.DeleteAppResource(ctx, c.base.ClusterID, "deployment",
		"default", hpa_deploy_name, nil); err != nil {
		logger.Errorf(ctx, "delete hpa deploy %s error: %s", hpa_deploy_name, err)
		return nil, err
	}
	logger.Infof(ctx, "delete hpa deploy %s success", hpa_deploy_name)

	return nil, nil
}

func (c *horizontalPodAutoscaling) Clean(ctx context.Context) error {
	return nil
}

func (c *horizontalPodAutoscaling) Continue(ctx context.Context) bool {
	// 默认不继续
	return true
}

func (c *horizontalPodAutoscaling) ConfigFormat() string {
	return ""
}

func (c *horizontalPodAutoscaling) CreateExecPodObject() *corev1.Pod {
	return &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "busybox",
			Namespace: "default",
			Labels: map[string]string{
				"app": "busybox",
			},
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name:  "busybox",
					Image: "registry.baidubce.com/cce-public/busybox",
					Command: []string{
						"/bin/sh",
						"-c",
						"while true; do echo `wget -q -O- http://test-e2e-hpa-service.default.svc.cluster.local`; done",
					},
				},
			},
		},
	}
}
