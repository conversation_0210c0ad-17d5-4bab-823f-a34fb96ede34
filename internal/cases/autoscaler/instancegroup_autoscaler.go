/* instancegroup_autoscaler.go */
/*
modification history
--------------------
2024/9/11, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
节点组自动伸缩配置校验
1、接口校验 （巡检需要）
*/

package autoscaler

import (
	"context"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/addon"
)

const (
	InstanceGroupAutoscaler cases.CaseName = "InstanceGroupAutoscaler"
)

type instanceGroupAutoscaler struct {
	base *cases.BaseClient
}

func init() {
	cases.AddCase(context.TODO(), InstanceGroupAutoscaler, NewInstanceGroupAutoscaler)
}

func NewInstanceGroupAutoscaler(ctx context.Context) cases.Interface {
	return &instanceGroupAutoscaler{}
}

func (c *instanceGroupAutoscaler) Name() cases.CaseName {
	return InstanceGroupAutoscaler
}

func (c *instanceGroupAutoscaler) Desc() string {
	return "节点组自动伸缩配置"
}

func (c *instanceGroupAutoscaler) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	return
}

func (c *instanceGroupAutoscaler) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID
	// 初始化集群autoscaler
	_, createASErr := c.base.CCEClient.CreateAutoScalerConfig(ctx, clusterID, nil)
	if createASErr != nil {
		err = fmt.Errorf("create auto scaler failed: %v", createASErr)
		return
	}

	configList := []*addon.ClusterAutoscalerConfig{
		{
			ScaleDownEnabled: true,
		},
		{
			ScaleDownEnabled: true,
			Expander:         addon.CAExpanderLeastWaste,
		},
		{
			ScaleDownEnabled: false,
		},
		// TODO: 补充其他接口参数的校验
	}

	// 更新autoscaler配置
	for _, config := range configList {
		_, updateASErr := c.base.CCEClient.UpdateAutoScalerConfig(ctx, clusterID, config, nil)
		if updateASErr != nil {
			err = fmt.Errorf("update auto scaler failed: %v", updateASErr)
			return
		}
		// 校验autoscaler配置
		resp, getASErr := c.base.CCEClient.GetAutoScalerConfig(ctx, clusterID, nil)
		if getASErr != nil {
			err = fmt.Errorf("get auto scaler failed: %v", getASErr)
			return
		}
		if config.ScaleDownEnabled != resp.Autoscaler.CAConfig.ScaleDownEnabled {
			err = errors.New("scaleDownEnabled in response is not equal in config")
			return
		}
	}

	return
}

func (c *instanceGroupAutoscaler) Clean(ctx context.Context) error {
	return nil
}

func (c *instanceGroupAutoscaler) Continue(ctx context.Context) bool {
	return true
}

func (c *instanceGroupAutoscaler) ConfigFormat() string {
	return ""
}
