package aiInfra

/*
  创建ai-infra集群，并推送生效数据
  - name: CreateAIInfraCluster
    config:
      accountID: 2e1be1eb99e946c3a543ec5a4eaa7d39
      vpcID: vpc-ppwu5pe9vvvy
      subnetID: sbn-zbrqp14k8jat
      zoneID: cn-bj-d
      cpromID: cprom-c4xgaida71z8
      securityGroupID: g-4nze10t26hr1
*/
import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/aiinfra"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/inspect"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CreateAIInfraCluster              cases.CaseName = "CreateAIInfraCluster"
	CreateAIInfraClusterLatencyExpect                = 900000

	InstanceFlavor = "bcc.g4.c2m8"
	InstanceCPU    = 2
	InstanceMEM    = 8
	InstanceSpecID = "g4"
	RootDiskTupe   = "cloud_hp1"
	ImageID        = "b7fb3b9d-c3f6-4828-a749-827cc4057241"
)

type createAIInfraCluster struct {
	base             *cases.BaseClient
	inspectClient    *inspect.Client
	config           createAIInfraClusterConfig
	AIInfraClusterID string
}

type createAIInfraClusterConfig struct {
	AccountID       string `json:"accountID"`
	VpcID           string `json:"vpcID"`
	SubnetID        string `json:"subnetID"`
	ZoneID          string `json:"zoneID"`
	CpromID         string `json:"cpromID"`
	SecurityGroupID string `json:"securityGroupID"`
}

func init() {
	cases.AddCase(context.TODO(), CreateAIInfraCluster, NewCreateAIInfraCluster)
}

func NewCreateAIInfraCluster(ctx context.Context) cases.Interface {
	return &createAIInfraCluster{}
}

func (c *createAIInfraCluster) Name() cases.CaseName {
	return CreateAIInfraCluster
}

func (c *createAIInfraCluster) Desc() string {
	return "创建ai-infra集群"
}

func (c *createAIInfraCluster) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base

	var cfg createAIInfraClusterConfig
	if err := json.Unmarshal(config, &cfg); err != nil {
		return fmt.Errorf("json.Unmarshal ai-infra config failed: %s", err)
	}
	c.config = cfg

	c.inspectClient = inspect.NewClient(c.base.Region)

	return
}

func (c *createAIInfraCluster) Check(ctx context.Context) (resources []cases.Resource, err error) {
	// 创建ai-infra集群
	request := c.GenerateCreateClusterRequest()
	response, createErr := c.base.AIInfraClient.CreateClusterV2(ctx, request, nil)
	if createErr != nil || response == nil {
		err = fmt.Errorf("CreateAIInfraCluster failed: %v, %v", response, createErr)
		return nil, err
	}
	clusterID := response.ClusterID
	c.AIInfraClusterID = clusterID

	start := time.Now().UTC()
	// 判断集群时是否创建成功
	if c.CheckClusterReady(ctx) {
		// 成功则推送端到端耗时数据
		time.Sleep(time.Second * 10)
		steps, detailErr := c.base.AIInfraClient.GetClusterCreateSteps(ctx, clusterID, nil)
		if detailErr != nil || steps == nil {
			err = fmt.Errorf("CetClusterCreateSteps failed: %v, %v", steps, err)
			return nil, err
		}

		for _, step := range steps.Steps {
			if step.StepName == "创建基础网络配置" && step.Ready {
				caseName := string(inspect.CreateAIInfraBasicNetworkCase)
				latency := step.CostSeconds * 1000
				c.PollingInspectData(ctx, clusterID, caseName, float64(latency))
			}
			if step.StepName == "检查基础网络配置" && step.Ready {
				caseName := string(inspect.CheckAIInfraBasicNetworkCase)
				latency := step.CostSeconds * 1000
				c.PollingInspectData(ctx, clusterID, caseName, float64(latency))
			}
			if step.StepName == "创建CCE集群" && step.Ready {
				caseName := string(inspect.CreateAIInfraCCEClusterCase)
				latency := step.CostSeconds * 1000
				c.PollingInspectData(ctx, clusterID, caseName, float64(latency))
			}
			if step.StepName == "创建资源组" && step.Ready {
				caseName := string(inspect.CreateAIInfraResourceGroupCase)
				latency := step.CostSeconds * 1000
				c.PollingInspectData(ctx, clusterID, caseName, float64(latency))
			}
			if step.StepName == "安装插件" && step.Ready {
				caseName := string(inspect.InstallAIInfraPluginCase)
				latency := step.CostSeconds * 1000
				c.PollingInspectData(ctx, clusterID, caseName, float64(latency))
			}
			if step.StepName == "关联Cprom监控" && step.Ready {
				caseName := string(inspect.RelateAIInfraMonitorCase)
				latency := step.CostSeconds * 1000
				c.PollingInspectData(ctx, clusterID, caseName, float64(latency))
			}
			if step.StepName == "同步资源组" && step.Ready {
				caseName := string(inspect.SyncAIInfraResourceGroupCase)
				latency := step.CostSeconds * 1000
				c.PollingInspectData(ctx, clusterID, caseName, float64(latency))

				caseName = string(inspect.CreateAIInfraClusterCase)
				finish := step.FinishedTime
				latency = finish.Sub(start).Milliseconds()
				c.PollingInspectData(ctx, clusterID, caseName, float64(latency))
			}
		}
	} else {
		// 失败则推送失败
		caseName := string(inspect.CreateAIInfraClusterCase)
		c.PollingInspectData(ctx, clusterID, caseName, float64(-1))
	}

	return
}

func (c *createAIInfraCluster) Clean(ctx context.Context) (err error) {
	// 获取资源池
	resGroups, listErr := c.base.AIInfraClient.ListResourceGroups(ctx, c.AIInfraClusterID, nil)
	if listErr != nil {
		err = fmt.Errorf("list resource groups failed: %v", listErr)
		return
	}
	if resGroups == nil || len(resGroups.Page.Items) == 0 {
		err = fmt.Errorf("no resource groups found")
		return
	}

	for _, resGroup := range resGroups.Page.Items {
		resGroupID := resGroup.Spec.ResourceGroupID
		// 获取资源节点
		nodes, getErr := c.base.AIInfraClient.GetResourcePoolInstances(ctx, c.AIInfraClusterID, resGroupID, nil)
		if getErr != nil {
			err = fmt.Errorf("get resource pool instances failed: %v", getErr)
			return
		}

		if nodes == nil || len(nodes.Page.Items) == 0 {
			continue
		}

		deleteNodes := make([]string, 0)
		for _, node := range nodes.Page.Items {
			deleteNodes = append(deleteNodes, node.Spec.CCEInstanceID)
		}
		// 删除资源节点
		delErr := c.base.AIInfraClient.DeleteResourcePoolInstances(ctx, c.AIInfraClusterID, resGroupID, &aiinfra.DeleteInstancesParams{
			InstanceIDs: deleteNodes,
		}, nil)
		if delErr != nil {
			err = fmt.Errorf("delete resource pool instances failed: %v", delErr)
			return
		}
		if !c.WaitResourceNodeDeleted(ctx, resGroupID) {
			err = errors.New("wait resource node failed")
			return
		}
		// 删除资源池
		delErr = c.base.AIInfraClient.DeleteResourcePool(ctx, c.AIInfraClusterID, resGroupID, nil)
		if delErr != nil {
			err = fmt.Errorf("delete resource group failed: %v", delErr)
			return
		}
	}
	if !c.WaitResourceGroupDeleted(ctx) {
		err = errors.New("wait resource group failed")
		return
	}
	// 删除集群
	delErr := c.base.AIInfraClient.DeleteCluster(ctx, c.AIInfraClusterID, nil)
	if delErr != nil {
		err = fmt.Errorf("delete cluster failed: %v", delErr)
		return
	}
	if !c.WaitClusterDeleted(ctx) {
		logger.Warnf(ctx, "wait cluster deleted failed")
	}
	return
}

func (c *createAIInfraCluster) Continue(ctx context.Context) bool {
	return true
}

func (c *createAIInfraCluster) ConfigFormat() string {
	return ""
}

func (c *createAIInfraCluster) CheckClusterReady(ctx context.Context) bool {
	waitErr := resource.WaitForFunc(ctx, func(ctx context.Context) (err error) {
		info, getErr := c.base.AIInfraClient.GetClusterInfo(ctx, c.AIInfraClusterID, nil)
		if getErr != nil {
			err = fmt.Errorf("GetClusterInfo failed: %v", getErr)
			return err
		}

		if info.Cluster.Status.Phase != aiinfra.ClusterStatusPhaseRunning {
			err = fmt.Errorf("cluster status is not running: %v", info.Cluster.Status.Phase)
			return err
		}

		if info.Cluster.Status.Network.NetworkPhase != "Ready" {
			err = fmt.Errorf("cluster network status is not ready: %v", info.Cluster.Status.Network.NetworkPhase)
			return err
		}

		resourceGroupsFlag := true
		for _, resourceGroup := range info.Cluster.Status.ResourceGroups {
			if resourceGroup.Phase != aiinfra.ResourceGroupPhaseRunning {
				resourceGroupsFlag = false
				break
			}
		}
		if !resourceGroupsFlag {
			err = fmt.Errorf("resource groups is not running")
			return err
		}

		pluginsFlag := true
		for _, plugin := range info.Cluster.Status.PluginStatuses {
			if plugin.Phase != aiinfra.PluginStatusPhaseDeployed {
				pluginsFlag = false
				break
			}
		}
		if !pluginsFlag {
			err = fmt.Errorf("plugins is not deployed")
			return err
		}

		if info.Cluster.Status.MonitorCollector.Phase != "Running" {
			err = fmt.Errorf("monitor collector is not running")
			return err
		}

		return nil
	}, 10*time.Second, 1200*time.Second)
	if waitErr != nil {
		return false
	}
	return true
}

func (c *createAIInfraCluster) GenerateCreateClusterRequest() *aiinfra.CreateClusterRequestV2 {
	plugins := []*aiinfra.Plugin{
		{
			PluginName: "cce-volcano",
			Namespace:  "kube-system",
			Values:     "Binpack: true\nPreemptgang: false\nReclaimgang: false\nVolcanoWebhook:\n  hostNetwork: true\nNodeSelector:\n  ai-infra.baidubce.com/node-pool-type: system",
		},
		{
			PluginName: "cce-gpu-manager",
			Namespace:  "kube-system",
			Values:     "EnableHook: false\nEnableSGPU: true\nGPUShareMemoryUnit: GiB\nIgnoreDeviceType: false\nWebhookHostNetwork: true\nNodeSelector:\n  ai-infra.baidubce.com/node-pool-type: system",
		},
		{
			PluginName: "cce-aibox",
			Namespace:  "kube-system",
			Values:     "NodeSelector:\n  ai-infra.baidubce.com/node-pool-type: system",
		},
		{
			PluginName: "cce-rdma-plugin",
			Namespace:  "kube-system",
		},
		{
			PluginName: "cce-inference-controller",
			Namespace:  "kube-system",
		},
		{
			PluginName: "cce-ingress-controller",
			Namespace:  "kube-system",
		},
		{
			PluginName: "cce-node-problem-detector",
			Namespace:  "kube-system",
		},
		{
			PluginName: "cce-node-remedier",
			Namespace:  "kube-system",
			Values:     "policy:\n  nodeSelector:\n    beta.kubernetes.io/os: linux\n  maxNodesPerDay: 10\n  maxNodesPerHour: 3\n  maxProcessingNodes: 5\n  minIntervalPerNode: 1s\nrules:\n- condition: GPUUnhealthy\n  timeout: 1s\n  steps:\n  - CordonNode\n- condition: NICUnhealthy\n  timeout: 1s\n  steps:\n  - CordonNode\n- condition: MemoryUnhealthy\n  timeout: 1s\n  steps:\n  - CordonNode\n- condition: ReadonlyFilesystem\n  timeout: 1s\n  steps:\n  - CordonNode\n- condition: KernelDeadlock\n  timeout: 1s\n  steps:\n  - CordonNode",
		},
		{
			PluginName: "cce-fluid",
			Namespace:  "kube-system",
		},
		{
			PluginName: "cce-log-operator",
			Namespace:  "kube-system",
		},
		{
			PluginName: "cce-aihcp-init",
			Namespace:  "kube-system",
			Values:     "pfsL1Config:\n  enable: \"false\"\npfsL2Config:\n  enable: \"false\"",
		},
		{
			PluginName: "cce-npu-manager",
			Namespace:  "kube-system",
		},
		{
			PluginName: "cce-csi-pfsl2-plugin",
			Namespace:  "kube-system",
			Values:     "storage:\n  pfsId: pfs-gkhLeF\n  mountTargetId: mt-aqCiKU\n",
			Version:    "1.0.6",
		},
		{
			PluginName: "cce-ingress-nginx-controller",
			Namespace:  "kube-system",
			Options: map[string]string{
				"alias_name": "tensorboard-ngx-control",
			},
			Values: "controller:\n  ingressClass: tensorboard\n  kind: Deployment\n  resources:\n    limits:\n      cpu: \"5\"\n      memory: 10Gi\n    requests:\n      cpu: \"0.25\"\n      memory: 256Mi\n  scope:\n    enabled: false\n    namespace: \"\"\n  service:\n    externalTrafficPolicy: \"\"\n    annotations:\n      service.beta.kubernetes.io/cce-load-balancer-backend-label: node.kubernetes.io/instance-type=BCC\n      service.beta.kubernetes.io/cce-load-balancer-backend-type: eni\n      service.beta.kubernetes.io/cce-load-balancer-internal-vpc: \"true\"\nfullnameOverride: tensorboard-ngx-control",
		},
		{
			PluginName: "keda",
			Namespace:  "kube-system",
		},
		{
			PluginName: "cce-cronhpa-controller",
			Namespace:  "kube-system",
		},
	}

	return &aiinfra.CreateClusterRequestV2{
		ClusterName: "ai-infra-inspect",
		TemplateRef: &aiinfra.TemplateRef{
			Name: "aihc-default-l50-g4",
		},
		Network: &aiinfra.ClusterNetworkV2{
			Type:                  "managedv1",
			Mode:                  "vpc-eni",
			ClusterIPServiceCIDR:  "***********/16",
			LbServiceVPCSubnetID:  c.config.SubnetID,
			ClusterMasterSubnetID: c.config.SubnetID,
			EniVPCSubnetIDs:       []string{c.config.SubnetID},
			NodeSubnetIDs:         []string{c.config.SubnetID},
		},
		Tenant: &aiinfra.ClusterTenantV2{
			AccountID: c.config.AccountID,
			VpcID:     c.config.VpcID,
		},
		Plugins: plugins,
		NodeDefaultSecurityGroups: []*aiinfra.SecurityGroup{
			{
				ID:   c.config.SecurityGroupID,
				Name: "CCE-Worker默认安全组",
				Type: "normal",
			},
		},
		EniDefaultSecurityGroups: []*aiinfra.SecurityGroup{
			{
				ID:   c.config.SecurityGroupID,
				Name: "CCE-Worker默认安全组",
				Type: "normal",
			},
		},
		EnableDeleteProtection: false,
		UserResourceGroups: []aiinfra.UserResourceGroup{
			{
				ResourceGroupName: "inspection",
				Replicas:          2,
				NodeSets: []aiinfra.NodeSetsV2{
					{
						IsDefault: true,
						Template: aiinfra.NodeTemplate{
							AvailableZone: c.config.ZoneID,
							Flavor:        InstanceFlavor,
							NodeResource: aiinfra.NodeResource{
								CPU:           InstanceCPU,
								MEM:           InstanceMEM,
								RootDiskType:  RootDiskTupe,
								RootDiskSize:  100,
								LocalDiskSize: 100,
								SpecID:        InstanceSpecID,
								MachineSpec:   InstanceFlavor,
							},
							Provider:   "bce",
							ImageID:    ImageID,
							ChargeType: "Postpaid",
							SubnetID:   c.config.SubnetID,
						},
					},
				},
			},
		},
		MonitorCollector: &aiinfra.MonitorCollector{
			AutoCreateMonitorInstance: false,
			Enable:                    true,
			MonitorInstanceID:         c.config.CpromID,
		},
	}
}

// PollingInspectData 推送ai-infra集群创建的生效数据
func (c *createAIInfraCluster) PollingInspectData(ctx context.Context, clusterID, caseName string, latency float64) {
	result := true
	// 时延<0 则认为异步创建的集群失败
	if latency < 0 {
		result = false
	}

	params := inspect.PollingDataParams{
		Region:      c.base.Region,
		PollingType: inspect.DataflowType,
		Case:        caseName,
		Cluster:     clusterID,
		CaseDesc:    fmt.Sprintf("%s taking time", caseName),
		Result:      result,
		ErrorInfo:   "",
		CaseResults: []inspect.CaseResult{
			{
				Name:    inspect.TakingTimeName,
				Desc:    fmt.Sprintf("%s taking time", caseName),
				Value:   latency,
				Checker: "<",
				Limit:   CreateAIInfraClusterLatencyExpect,
			},
		},
	}
	_, err := c.inspectClient.PollingData(&params)
	if err != nil {
		logger.Errorf(ctx, "polling data failed: %v", err)
	}
}

func (c *createAIInfraCluster) WaitResourceNodeDeleted(ctx context.Context, resourceGroupID string) bool {
	waitErr := resource.WaitForFunc(ctx, func(ctx context.Context) (err error) {
		nodes, getErr := c.base.AIInfraClient.GetResourcePoolInstances(ctx, c.AIInfraClusterID, resourceGroupID, nil)
		if getErr != nil {
			err = fmt.Errorf("get resource pool instances failed: %v", getErr)
			return
		}
		if nodes == nil || nodes.Page.TotalCount > 0 {
			return fmt.Errorf("resource pool instances still exist")
		}

		return nil

	}, 10*time.Second, 90*time.Second)
	if waitErr != nil {
		logger.Errorf(ctx, "wait resource pool instances failed: %v", waitErr)
		return false
	}
	return true
}

func (c *createAIInfraCluster) WaitResourceGroupDeleted(ctx context.Context) bool {
	waitErr := resource.WaitForFunc(ctx, func(ctx context.Context) (err error) {
		resGroups, listErr := c.base.AIInfraClient.ListResourceGroups(ctx, c.AIInfraClusterID, nil)
		if listErr != nil {
			err = fmt.Errorf("get resource group failed: %v", listErr)
			return
		}
		if resGroups == nil || resGroups.Page.TotalCount > 0 {
			return fmt.Errorf("resource group still exist")
		}
		return nil
	}, 10*time.Second, 120*time.Second)
	if waitErr != nil {
		logger.Errorf(ctx, "wait resource group failed: %v", waitErr)
		return false
	}
	return true
}

func (c *createAIInfraCluster) WaitClusterDeleted(ctx context.Context) bool {
	waitErr := resource.WaitForFunc(ctx, func(ctx context.Context) (err error) {
		_, getErr := c.base.AIInfraClient.GetClusterInfo(ctx, c.AIInfraClusterID, nil)
		if getErr != nil {
			if strings.Contains(getErr.Error(), "not found") {
				return nil
			}
			err = fmt.Errorf("get cluster failed: %v", getErr)
			return
		}
		return fmt.Errorf("cluster still exist")
	}, 10*time.Second, 600*time.Second)
	if waitErr != nil {
		logger.Errorf(ctx, "wait cluster delete failed: %v", waitErr)
		return false
	}

	return true
}
