// Copyright 2025 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2025/05/12, by <EMAIL>, create
*/
/*
Ingress扩展域名测试：
1.若集群无blb ingress组件，则接口创建
2.创建ingress，并添加https证书和扩展域名
3.校验创建后blb&eip资源状态
4.校验https访问联通性
5.更新ingress, 添加扩展域名(泛域名), 删除一个扩展域名（精确域名）, 并修改转发规则和EIP带宽, 但未开启BLB配置全量更新
6.检查blb侧配置信息, BLB的扩展域名信息不变，转发规则更新
7.校验对应https访问失败
8.更新ingress，开启BLB配置全量更新
9.检查blb侧配置信息更新: 扩展域名、带宽
10.更新ingress，关闭扩展域名
11.检查blb侧配置信息更新
12.检查、BLB、EIP是否被清理
13.检查：EIP是否进入回收站并删除回收站的EIP
*/

package ingress

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	extensions "k8s.io/api/extensions/v1beta1"
	networkingv1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appservice"
	eipsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"

	ccesdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/executor"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/network/ingress-controller/common"
)

const (
	// IngressAdditionCertDomains - Case 名字
	IngressAdditionCertDomains cases.CaseName = "IngressAdditionCertDomains"

	// 测试用工作负载
	DeploymentYAML = `apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment-for-ingress-addition-cert-domians
  labels:
    app: nginx
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nginx-for-ingress-addition-cert-domians
  template:
    metadata:
      labels:
        app: nginx-for-ingress-addition-cert-domians
    spec:
      restartPolicy: Always
      containers:
        - name: nginx
          image: registry.baidubce.com/qatest-public/mynginx:v1
          imagePullPolicy: Always
          ports:
            - containerPort: 80
`

	// 测试用nodeport service
	serviceName = "service-for-ingress-addition-cert-domians"
	serviceYAML = `apiVersion: v1
kind: Service
metadata:
  name: service-for-ingress-addition-cert-domians
spec:
  selector:
    app: nginx-for-ingress-addition-cert-domians
  type: NodePort
  ports:
  - name: nginx
    protocol: TCP
    port: 80
    targetPort: 80
`

	// 测试用 Ingress
	ingressName = "autotest-cce-ingress-for-addition-cert-domains"
	ingressYAML = `apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    cce.ingress.blb-backup-content: ""
    kubernetes.io/cce.ingress.blb-cert-id: "[BLBCertID]"
    kubernetes.io/cce.ingress.blb.listener.http.enable-x-forwarded-for-header: "false"
    kubernetes.io/cce.ingress.blb.listener.http.keep-session.enable: "true"
    kubernetes.io/cce.ingress.blb.listener.http.keep-session.timeout: "3601"
    kubernetes.io/cce.ingress.blb.listener.http.keep-session.type: "insert"
    kubernetes.io/cce.ingress.blb.listener.http.scheduler: "LeastConnection"
    kubernetes.io/cce.ingress.blb.listener.https.enable-x-forwarded-for-header: "false"
    kubernetes.io/cce.ingress.blb.listener.https.keep-session.enable: "true"
    kubernetes.io/cce.ingress.blb.listener.https.keep-session.cookie-name: "cce-regression-test"
    kubernetes.io/cce.ingress.blb.listener.https.keep-session.type: "rewrite"
    kubernetes.io/cce.ingress.blb.listener.https.scheduler: "RoundRobin"
    kubernetes.io/cce.ingress.http-redirect: "false"
    kubernetes.io/cce.ingress.https: "true"
    kubernetes.io/cce.ingress.https-rules: "[{\"host\":\"a.zhtest.com\",\"http\":{\"paths\":[{\"path\":\"/age\",\"backend\":{\"serviceName\":\"[SERVICENAME]\",\"servicePort\":80},\"pathType\":\"ImplementationSpecific\"}]}},{\"host\":\"b.zhtest.com\",\"http\":{\"paths\":[{\"path\":\"/\",\"backend\":{\"serviceName\":\"[SERVICENAME]\",\"servicePort\":80},\"pathType\":\"ImplementationSpecific\"}]}}]"
    kubernetes.io/cce.ingress.internal: "false"
    kubernetes.io/cce.ingress.listener.customized-listener: "[CustomizedListener]"
    kubernetes.io/cce.ingress.timeout-in-seconds: "30"
    kubernetes.io/cce.ingress.vpc-subnet-id: "[SUBNETID]"
    kubernetes.io/ingress.class: cce
  name: autotest-cce-ingress-for-addition-cert-domains
  namespace: default
spec:
  rules:
  - host: a.zhtest.com
    http:
      paths:
      - backend:
          serviceName: [SERVICENAME]
          servicePort: 80
        path: /age
  - host: b.zhtest.com
    http:
      paths:
      - backend:
          serviceName: [SERVICENAME]
          servicePort: 80
        path: /`

	ingressYAML_k8S122 = `apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    cce.ingress.blb-backup-content: ""
    kubernetes.io/cce.ingress.blb-cert-id: "[BLBCertID]"
    kubernetes.io/cce.ingress.blb.listener.http.enable-x-forwarded-for-header: "false"
    kubernetes.io/cce.ingress.blb.listener.http.keep-session.enable: "true"
    kubernetes.io/cce.ingress.blb.listener.http.keep-session.timeout: "3601"
    kubernetes.io/cce.ingress.blb.listener.http.keep-session.type: "insert"
    kubernetes.io/cce.ingress.blb.listener.http.scheduler: "LeastConnection"
    kubernetes.io/cce.ingress.blb.listener.https.enable-x-forwarded-for-header: "false"
    kubernetes.io/cce.ingress.blb.listener.https.keep-session.enable: "true"
    kubernetes.io/cce.ingress.blb.listener.https.keep-session.cookie-name: "cce-regression-test"
    kubernetes.io/cce.ingress.blb.listener.https.keep-session.type: "rewrite"
    kubernetes.io/cce.ingress.blb.listener.https.scheduler: "RoundRobin"
    kubernetes.io/cce.ingress.http-redirect: "false"
    kubernetes.io/cce.ingress.https: "true"
    kubernetes.io/cce.ingress.https-rules: "[{\"host\":\"a.zhtest.com\",\"http\":{\"paths\":[{\"path\":\"/age\",\"backend\":{\"serviceName\":\"[SERVICENAME]\",\"servicePort\":80},\"pathType\":\"ImplementationSpecific\"}]}},{\"host\":\"b.zhtest.com\",\"http\":{\"paths\":[{\"path\":\"/\",\"backend\":{\"serviceName\":\"[SERVICENAME]\",\"servicePort\":80},\"pathType\":\"ImplementationSpecific\"}]}}]"
    kubernetes.io/cce.ingress.internal: "false"
    kubernetes.io/cce.ingress.listener.customized-listener: "[CustomizedListener]"
    kubernetes.io/cce.ingress.timeout-in-seconds: "30"
    kubernetes.io/cce.ingress.vpc-subnet-id: "[SUBNETID]"
    kubernetes.io/ingress.class: cce
  name: autotest-cce-ingress-for-addition-cert-domains
  namespace: default
spec:
  rules:
  - host: a.zhtest.com
    http:
      paths:
      - backend:
          service:
            name: [SERVICENAME]
            port: 
              number: 80
        path: /age
        pathType: ImplementationSpecific
  - host: b.zhtest.com
    http:
      paths:
      - backend:
          service:
            name: [SERVICENAME]
            port: 
              number: 80
        path: /
        pathType: ImplementationSpecific`
)

func init() {
	cases.AddCase(context.TODO(), IngressAdditionCertDomains, NewIngressAdditionCertDomains)
}

type ingressAdditionCertDomains struct {
	base *cases.BaseClient

	shell       executor.Executor
	ingressName string
	ingress     *extensions.Ingress
	config      ingressConfig
}

// NewIngressAdditionCertDomains - 测试案例
func NewIngressAdditionCertDomains(ctx context.Context) cases.Interface {
	return &ingressAdditionCertDomains{
		shell: executor.NewExecutor("/bin/bash"),
	}
}

func (c *ingressAdditionCertDomains) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base

	var cfg ingressConfig
	if err := json.Unmarshal(config, &cfg); err != nil {
		return fmt.Errorf("json.Unmarshal bos config failed: %s", err)
	}
	c.config = cfg

	return nil
}

func (c *ingressAdditionCertDomains) Name() cases.CaseName {
	return IngressAdditionCertDomains
}

func (c *ingressAdditionCertDomains) Desc() string {
	return "测试Ingress扩展域名"
}

func (c *ingressAdditionCertDomains) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)

	// 1、若集群无blb ingress组件，则接口创建
	ingressControllerDp, err := c.base.K8SClient.AppsV1().Deployments(IngressControllerNamespace).Get(ctx, IngressControllerDeploymentName, metav1.GetOptions{})
	if err != nil || ingressControllerDp == nil {
		// 使用addon接口安装
		_, err = c.base.CCEClient.InstallAddon(ctx, c.base.ClusterID, &ccesdk.InstallParams{
			Name: "cce-ingress-controller",
		}, nil)
		if err != nil {
			logger.Errorf(ctx, "Install cce-ingress-controller failed: %v", err.Error())
		}
		logger.Infof(ctx, "Install cce-ingress-controller succeeded")
	} else {
		logger.Infof(ctx, "Found cce-ingress-controller, skip deploy")
	}

	// 等待 ingress pod running
	// 托管集群ingress组件被托管了无需校验
	cluster, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "get cluster failed: %v", err)
		return nil, err
	}
	if cluster.Cluster.Spec.MasterConfig.MasterType != types.MasterTypeManagedPro {
		var k8sPod resource.K8SPod
		if err = k8sPod.NewK8SPod(ctx, c.base, IngressControllerNamespace, nil); err != nil {
			return nil, err
		}
		k8sPod.SetMatchLabel("app", IngressControllerDeploymentName)
		k8sPod.SetStatus(resource.StatusRunning)
		if err = resource.WaitForResourceReady(ctx, &k8sPod); err != nil {
			logger.Errorf(ctx, "WaitPodsRunning failed: %s", err)
			return nil, err
		}
	}

	// 2、创建ingress，并添加https证书（适配1.22集群）
	// 部署 Deployment
	yaml := DeploymentYAML
	if c.config.IsArm {
		// yaml = strings.ReplaceAll(DeploymentYAML, "nginx:", "nginx-arm64:")
		logger.Warnf(ctx, "not support arm cluster now, skip")
		return nil, nil
	}
	if err := c.base.KubectlClient.Apply(ctx, types.K8S_1_13_10, c.base.KubeConfig, yaml); err != nil {
		logger.Errorf(ctx, "Deploy deployment failed: %s", err)
		return nil, err
	}

	// 部署 nodeport service
	if err := c.base.KubectlClient.Apply(ctx, types.K8S_1_13_10, c.base.KubeConfig, serviceYAML); err != nil {
		logger.Errorf(ctx, "Deploy service failed: %s", err)
		return nil, err
	}

	// 获取集群的一个vpc子网
	clusterInfo, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "Get cluster failed: %s", err)
		return nil, err
	}
	subnetID := clusterInfo.Cluster.Spec.ContainerNetworkConfig.LBServiceVPCSubnetID
	k8sVersion := clusterInfo.Cluster.Spec.K8SVersion

	// 部署 Ingress
	var strYml string
	// 兼容1.22集群
	if k8sVersion >= types.K8S_1_22_5 {
		strYml = strings.ReplaceAll(ingressYAML_k8S122, "[SUBNETID]", subnetID)
	} else {
		strYml = strings.ReplaceAll(ingressYAML, "[SUBNETID]", subnetID)
	}

	// ingress添加证书
	if c.config.BLBCertID == "" {
		logger.Infof(ctx, "config.BLBCertID is empty, use default cert")
		c.config.BLBCertID = "cert-7k3sfv6wkzry" // jpaas账户下BLB 证书 cert-5bqpykggjzmi

	}
	strYml = strings.ReplaceAll(strYml, "[BLBCertID]", c.config.BLBCertID)
	// 添加自定义域名证书
	customizedListener := "{\\\"443\\\":{\\\"protocol\\\":\\\"HTTPS\\\",\\\"additionalCertDomains\\\":[{\\\"host\\\":\\\"a.zhtest.com\\\",\\\"certId\\\":\\\"cert-7ub5ubne3bn3\\\"},{\\\"host\\\":\\\"b.zhtest.com\\\",\\\"certId\\\":\\\"cert-7ub5ubne3bn3\\\"}]}}"
	strYml = strings.ReplaceAll(strYml, "[CustomizedListener]", customizedListener)
	strYml = strings.ReplaceAll(strYml, "[SERVICENAME]", serviceName)
	// 创建
	logger.Infof(ctx, "Deploy Ingress with yaml: \n%s", strYml)
	if _, err = c.base.AppClient.PostAppResource(ctx, c.base.ClusterID, ingressName, strYml, "default", nil); err != nil {
		logger.Errorf(ctx, "Deploy Ingress failed: %s", err)
		return nil, err
	}

	// 3、校验创建后blb&eip资源状态
	var ok bool
	var eip string
	var blbID string
	var annotations map[string]string
	for i := 0; i < 20; i++ {
		time.Sleep(30 * time.Second)

		annotations, err = GetIngressAnnotation(ctx, c.base, k8sVersion, ingressName)
		if err != nil {
			return resources, fmt.Errorf("Get Ingress Annoatation failed: %v ", err.Error())
		}

		// 检查是否有 LB
		blbID, ok = annotations[common.AnnotationBLBShortID]
		if !ok {
			logger.Infof(ctx, "%s is empty, retry", common.AnnotationBLBShortID)
			continue
		}

		logger.Infof(ctx, "BLB ID: %s", blbID)

		// 检查是否有 EIP
		eip, ok = annotations[common.AnnotationEIP]
		if !ok {
			logger.Infof(ctx, "%s is empty, retry", common.AnnotationEIP)
			continue
		}

		logger.Infof(ctx, "EIP: %s", eip)

		break
	}

	// 统计 BLB/EIP 资源
	if blbID != "" {
		resources = append(resources, cases.Resource{
			CaseName: IngressAdditionCertDomains,
			Type:     cases.ResourceTypeBLB,
			ID:       blbID,
		})
	}

	if eip != "" {
		resources = append(resources, cases.Resource{
			CaseName: IngressAdditionCertDomains,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
	}

	// 结果统计
	if blbID == "" || eip == "" {
		return resources, fmt.Errorf("create ingress failed: blbID=[%s] eip=[%s]", blbID, eip)
	}

	var blbStatus resource.AppBLBStatus
	if err = blbStatus.NewAppBLBStatus(ctx, c.base, blbID, "available"); err != nil {
		return resources, err
	}
	if err = resource.WaitForResourceReady(ctx, &blbStatus); err != nil {
		return resources, err
	}

	var eipStatus resource.EIPStatus
	if err = eipStatus.NewEIPStatus(ctx, c.base, eip, "binded"); err != nil {
		return resources, err
	}
	if err = resource.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	// 4、校验https连通性
	cmd := fmt.Sprintf("curl --cacert internal/cases/ingress/ca.crt --resolve \"b.zhtest.com:443:%s\" https://b.zhtest.com/", eip)
	if err := CheckAccessiable(ctx, c.shell, cmd, "Welcome to nginx"); err != nil {
		return resources, err
	}
	cmd = fmt.Sprintf("curl --cacert internal/cases/ingress/ca.crt --resolve \"a.zhtest.com:443:%s\" https://a.zhtest.com/age", eip)
	if err := CheckAccessiable(ctx, c.shell, cmd, "18"); err != nil {
		return resources, err
	}
	cmd = fmt.Sprintf("curl --cacert internal/cases/ingress/ca.crt --resolve \"b.zhtest.com:443:%s\" https://b.zhtest.com/age", eip)
	if err := CheckAccessiable(ctx, c.shell, cmd, "503 Service Unavailable"); err != nil {
		return resources, err
	}

	// 5、校验blb侧配置信息
	expectDdditionalCertDomains := map[string]string{"b.zhtest.com": "cert-7ub5ubne3bn3", "a.zhtest.com": "cert-7ub5ubne3bn3"}
	if err := CheckBLBListener(ctx, c.base, blbID, expectDdditionalCertDomains, "RoundRobin"); err != nil {
		return resources, err
	}
	expectRules := map[string]string{"a.zhtest.com/age": "", "b.zhtest.com/": ""}
	if err := CheckBLBPolicy(ctx, c.base, blbID, expectRules); err != nil {
		return resources, err
	}

	// 6、更新ingress, 添加扩展域名(泛域名), 删除一个扩展域名（精确域名）, 并修改转发规则和EIP带宽, 但未开启BLB配置全量更新
	// var newAnnotations map[string]string
	newIngress, err := c.base.AppClient.GetIngressDetailByName(ctx, c.base.ClusterID, "default", ingressName, string(k8sVersion), nil)
	if err != nil {
		logger.Errorf(ctx, "Get ingress detail by name failed: %v", err.Error())
		return resources, err
	}

	httpsRules := fmt.Sprintf("[{\"host\":\"a.zhtest.com\",\"http\":{\"paths\":[{\"path\":\"/age\",\"backend\":{\"serviceName\":\"%s\",\"servicePort\":80},\"pathType\":\"ImplementationSpecific\"}]}},{\"host\":\"b.zhtest.com\",\"http\":{\"paths\":[{\"path\":\"/\",\"backend\":{\"serviceName\":\"%s\",\"servicePort\":80},\"pathType\":\"ImplementationSpecific\"}]}},{\"host\":\"*.zhtest0.com\",\"http\":{\"paths\":[{\"path\":\"/*\",\"backend\":{\"serviceName\":\"%s\",\"servicePort\":80},\"pathType\":\"ImplementationSpecific\"}]}}]", serviceName, serviceName, serviceName)
	customizedListener = fmt.Sprintf("{\"443\":{\"protocol\":\"HTTPS\",\"scheduler\":\"LeastConnection\",\"additionalCertDomains\":[{\"host\":\"b.zhtest.com\",\"certId\":\"cert-7ub5ubne3bn3\"},{\"host\":\"*.zhtest0.com\",\"certId\":\"cert-7df0277qnqi3\"}]}}")
	if k8sVersion >= types.K8S_1_22_5 {
		ingressDetail, _ := newIngress.(*appservice.IngressDetailNetwokingv1)
		// ingressDetail.Annotations["kubernetes.io/cce.ingress.update-in-time"] = "true"
		ingressDetail.Annotations["kubernetes.io/cce.ingress.eip-bandwidth-in-mbps"] = "2"
		ingressDetail.Annotations["kubernetes.io/cce.ingress.https-rules"] = httpsRules
		ingressDetail.Annotations["kubernetes.io/cce.ingress.listener.customized-listener"] = customizedListener
		pathType := networkingv1.PathTypeImplementationSpecific
		ingressDetail.Spec.Rules = append(ingressDetail.Spec.Rules, networkingv1.IngressRule{
			Host: "*.zhtest0.com",
			IngressRuleValue: networkingv1.IngressRuleValue{
				HTTP: &networkingv1.HTTPIngressRuleValue{
					Paths: []networkingv1.HTTPIngressPath{
						{
							Path:     "/*",
							PathType: &pathType,
							Backend: networkingv1.IngressBackend{
								Service: &networkingv1.IngressServiceBackend{
									Name: serviceName,
									Port: networkingv1.ServiceBackendPort{
										Number: 80,
									},
								},
							},
						},
					},
				},
			},
		})
		err = c.base.AppClient.UpdateIngressYaml(ctx, c.base.ClusterID, "default", ingressName, ingressDetail, nil)
		if err != nil {
			logger.Errorf(ctx, "update ingress failed: %v", err.Error())
			return resources, fmt.Errorf("update ingress failed: %v", err.Error())
		}

		// newAnnotations, err = GetIngressAnnotation(ctx, c.base, k8sVersion, ingressName)
		// if err != nil {
		// 	return resources, fmt.Errorf("Get Ingress Annoatation failed: %v ", err.Error())
		// }
	} else {
		ingressDetail, _ := newIngress.(*appservice.IngressDetail)
		// ingressDetail.Annotations["kubernetes.io/cce.ingress.update-in-time"] = "true"
		ingressDetail.Annotations["kubernetes.io/cce.ingress.eip-bandwidth-in-mbps"] = "2"
		ingressDetail.Annotations["kubernetes.io/cce.ingress.https-rules"] = httpsRules
		ingressDetail.Annotations["kubernetes.io/cce.ingress.listener.customized-listener"] = customizedListener
		pathType := extensions.PathTypeImplementationSpecific
		ingressDetail.Spec.Rules = append(ingressDetail.Spec.Rules, extensions.IngressRule{
			Host: "*.zhtest0.com",
			IngressRuleValue: extensions.IngressRuleValue{
				HTTP: &extensions.HTTPIngressRuleValue{
					Paths: []extensions.HTTPIngressPath{
						{
							Path:     "/*",
							PathType: &pathType,
							Backend: extensions.IngressBackend{
								ServiceName: serviceName,
								ServicePort: intstr.FromInt(80),
							},
						},
					},
				},
			},
		})
		err = c.base.AppClient.UpdateIngressYaml(ctx, c.base.ClusterID, "default", ingressName, ingressDetail, nil)
		if err != nil {
			logger.Errorf(ctx, "update ingress failed: %v", err.Error())
			return resources, fmt.Errorf("update ingress failed: %v", err.Error())
		}

		// newAnnotations, err = GetIngressAnnotation(ctx, c.base, k8sVersion, ingressName)
		// if err != nil {
		// 	return resources, fmt.Errorf("Get Ingress Annoatation failed: %v ", err.Error())
		// }
	}

	time.Sleep(20 * time.Second)

	// 7、校验blb侧配置信息, BLB的扩展域名信息不变，转发规则更新
	err = CheckBLBListener(ctx, c.base, blbID, expectDdditionalCertDomains, "RoundRobin")
	if err != nil {
		logger.Errorf(ctx, "CheckBLBListener after update ingress (update-in-time=false) failed: %v", err.Error())
		return resources, err
	}
	expectRules = map[string]string{"a.zhtest.com/age": "", "b.zhtest.com/": "", "*.zhtest0.com/*": ""}
	if err := CheckBLBPolicy(ctx, c.base, blbID, expectRules); err != nil {
		logger.Errorf(ctx, "CheckBLBPolicy after supdate ingress (update-in-time=false) failed: %v", err.Error())
		return resources, err
	}
	if err := CheckEIPInfo(ctx, c.base, eip, 100); err != nil {
		logger.Errorf(ctx, "CheckEIPInfo after update ingress (update-in-time=false) failed: %v", err.Error())
		return resources, err
	}

	// 8、校验https访问报错
	cmd = fmt.Sprintf("curl --cacert internal/cases/ingress/ca.crt --resolve \"ddd.zhtest0.com:443:%s\" https://ddd.zhtest0.com/", eip)
	if err := CheckNotAccessiable(ctx, c.shell, cmd, "curl: (60)"); err != nil {
		logger.Errorf(ctx, "CheckNotAccessiable after failed: %v", err.Error())
		return resources, err
	}

	// 9、更新ingress, 开启BLB配置全量更新（Annotation kubernetes.io/cce.ingress.update-in-time=true)
	anno := map[string]string{
		"kubernetes.io/cce.ingress.update-in-time": "true",
	}
	if err := UpdateIngressAnnotations(ctx, c.base, k8sVersion, ingressName, anno); err != nil {
		logger.Errorf(ctx, "UpdateIngressAnnotations kubernetes.io/cce.ingress.update-in-time to true failed: %v", err.Error())
		return resources, err
	}
	time.Sleep(10 * time.Second)

	// 10、校验blb侧配置信息
	expectDdditionalCertDomains = map[string]string{"b.zhtest.com": "cert-7ub5ubne3bn3", "*.zhtest0.com": "cert-7df0277qnqi3"}
	if err := CheckBLBListener(ctx, c.base, blbID, expectDdditionalCertDomains, "LeastConnection"); err != nil {
		logger.Errorf(ctx, "CheckBLBListener after set kubernetes.io/cce.ingress.update-in-time annotation to true failed: %v", err.Error())
		return resources, err
	}
	// check EIP bandwidth
	if err := CheckEIPInfo(ctx, c.base, eip, 2); err != nil {
		logger.Errorf(ctx, "CheckEIPInfo after set kubernetes.io/cce.ingress.update-in-time annotation to true failed: %v", err.Error())
		return resources, err
	}

	// 11、校验https访问
	cmd = fmt.Sprintf("curl --cacert internal/cases/ingress/ca.crt --resolve \"ddd.zhtest0.com:443:%s\" https://ddd.zhtest0.com/", eip)
	if err := CheckAccessiable(ctx, c.shell, cmd, "Welcome to nginx"); err != nil {
		logger.Errorf(ctx, "CheckAccessiable after set kubernetes.io/cce.ingress.update-in-time annotation to true failed: %v", err.Error())
		return resources, err
	}
	cmd = fmt.Sprintf("curl --cacert internal/cases/ingress/ca.crt --resolve \"a.zhtest0.com:443:%s\" https://a.zhtest0.com/hello", eip)
	if err := CheckAccessiable(ctx, c.shell, cmd, "hi"); err != nil {
		return resources, err
	}

	// 12、关闭扩展域名
	anno = map[string]string{
		"kubernetes.io/cce.ingress.listener.customized-listener": "{\"443\":{\"protocol\":\"HTTPS\"}}",
	}
	if err := UpdateIngressAnnotations(ctx, c.base, k8sVersion, ingressName, anno); err != nil {
		logger.Errorf(ctx, "UpdateIngressAnnotations kubernetes.io/cce.ingress.listener.customized-listener(remove  additionalCertDomains) failed: %v", err.Error())
		return resources, err
	}
	time.Sleep(10 * time.Second)
	// 13、校验blb侧配置信息, 并验证自定义监听器Annotation与其他监听器配置Annotation同时出现时，自定义监听器会进行合并而不是覆盖
	// 配置合并设计间：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/Jk_2Ea1IJe/jFLv3m8XZn/eW_0Egf3_f2umx#anchor-7da30da0-1520-11f0-9c08-31bb75cc7e56
	expectDdditionalCertDomains = map[string]string{}
	if err := CheckBLBListener(ctx, c.base, blbID, expectDdditionalCertDomains, "RoundRobin"); err != nil {
		logger.Errorf(ctx, "CheckBLBListener after delete additional cert domains failed: %v", err.Error())
		return resources, err
	}

	// 14、删除ingress
	err = c.base.AppClient.DeleteAppResource(ctx, c.base.ClusterID, "ingress", "default", ingressName, nil)
	if err != nil {
		logger.Errorf(ctx, "delete ingress failed: %v", err.Error())
		return resources, err
	}

	// 15、检查：BLB、EIP是否被清理
	eipStatus.SetStatus("deleted")
	if err = resource.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	// 16、检查：EIP是否进入回收站并删除回收站的EIP
	if isInRecycleBin, err := eipStatus.CheckIsInRecycleBin(ctx); err != nil || !isInRecycleBin {
		return resources, err
	}
	logger.Infof(ctx, "delete eip %s in recycle bin", eip)
	if isSuccessDelete, err := eipStatus.DeleteEIPInRecycleBin(ctx, eip); err != nil || !isSuccessDelete {
		return resources, err
	}

	return resources, nil
}

func (c *ingressAdditionCertDomains) Clean(ctx context.Context) error {
	return nil
}

func (c *ingressAdditionCertDomains) Continue(ctx context.Context) bool {
	return true
}

func (c *ingressAdditionCertDomains) ConfigFormat() string {
	return ""
}

// CheckBLBListener 校验配置信息同步到blb
func CheckBLBListener(ctx context.Context, baseClient *cases.BaseClient, blbID string, expectCertDomains map[string]string, expectScheduler appblb.SchedulerType) error {
	// 获取当前blb的监听信息
	httpsListeners, err := baseClient.AppBLBClient.DescribeAppHTTPSListener(ctx, blbID, 443, nil)
	if err != nil {
		logger.Errorf(ctx, "DescribeAppHTTPSListener %s failed : %v", blbID, err.Error())
		return err
	}
	if len(httpsListeners.ListenerList) == 0 {
		return fmt.Errorf("blb https listener is empty")
	}

	// 检查BLB的扩展域名
	additionalCertDomains := httpsListeners.ListenerList[0].AdditionalCertDomains
	if len(additionalCertDomains) != len(expectCertDomains) {
		return fmt.Errorf("blb https listener additional cert domains %v count is not as expect: %v", additionalCertDomains, expectCertDomains)
	}
	for _, l := range httpsListeners.ListenerList[0].AdditionalCertDomains {
		if certID, ok := expectCertDomains[l.Host]; ok {
			if l.CertID != certID {
				return fmt.Errorf("blb https listener certID %s is not as expect: %s", l.CertID, certID)
			}
		} else {
			return fmt.Errorf("blb https listener has unexpected additional cert domain (%s, %s)", l.Host, l.CertID)
		}
	}

	logger.Infof(ctx, "check ingressblb pass through appblb listener additional cert domains success")

	// 检查调度方法
	if httpsListeners.ListenerList[0].Scheduler != expectScheduler {
		return fmt.Errorf("app httpsListener resp args do not match: scheduler is %s, expect: %s", httpsListeners.ListenerList[0].Scheduler, expectScheduler)
	}

	// 检查会话保持配置
	if !httpsListeners.ListenerList[0].KeepSession ||
		httpsListeners.ListenerList[0].KeepSessionType != "rewrite" ||
		httpsListeners.ListenerList[0].KeepSessionCookieName != "cce-regression-test" ||
		httpsListeners.ListenerList[0].XForwardedFor {
		return errors.New("app httpsListener resp args do not match: keep session config is not as expect")
	}
	logger.Infof(ctx, "check ingressblb pass through appblb listener args success")

	return nil
}

func CheckBLBPolicy(ctx context.Context, baseClient *cases.BaseClient, blbID string, expectRules map[string]string) error {
	args := &appblb.DescribeAppPolicysArgs{
		Port: 443,
		Type: "HTTPS",
	}
	httpsPolicys, err := baseClient.AppBLBClient.DescribeAppPolicys(ctx, blbID, args, nil)
	if err != nil {
		logger.Errorf(ctx, "DescribeAppPolicys %s failed : %v", blbID, err.Error())
		return err
	}
	if len(httpsPolicys.PolicyList) == 0 {
		return fmt.Errorf("blb https policy is empty")
	}
	expectAppServerGroupName := fmt.Sprintf("default/%s", serviceName)
	ruleList := []string{}
	for _, policy := range httpsPolicys.PolicyList {
		// AppServerGroupName
		if policy.AppServerGroupName != expectAppServerGroupName {
			return fmt.Errorf("blb https policy appServerGroupName is %s, not as expect: %s", policy.AppServerGroupName, expectAppServerGroupName)
		}
		// TODO: backendPort
		// rule
		var host, uri string
		for _, rule := range policy.RuleList {
			if rule.Key == appblb.AppRuleKeyTypeHost {
				host = rule.Value
			}
			if rule.Key == appblb.AppRuleKeyTypeURI {
				uri = rule.Value
			}

		}
		ruleList = append(ruleList, host+uri)
	}
	if len(ruleList) != len(expectRules) {
		return fmt.Errorf("blb https policy ruleList is %v, not as expect: %v", ruleList, expectRules)
	}
	for _, rule := range ruleList {
		if expect, ok := expectRules[rule]; !ok {
			return fmt.Errorf("blb https policy rule is %v, not as expect: %v", rule, expect)
		}
	}
	return nil
}

// CheckEIPInfo
func CheckEIPInfo(ctx context.Context, baseClient *cases.BaseClient, ip string, bandwidth int) error {
	// 获取当前eip的详情
	args := eipsdk.GetEIPsArgs{
		EIP: ip,
	}
	eipList, err := baseClient.EIPClient.GetEIPs(ctx, &args, nil)
	if err != nil {
		logger.Errorf(ctx, "GetEIPs %s failed : %v", ip, err.Error())
		return err
	}

	// check: bandwidth
	if eipList[0].BandwidthInMbps != bandwidth {
		return fmt.Errorf("blb bandwidth is %d, not as expect: %d", eipList[0].BandwidthInMbps, bandwidth)
	}
	return nil
}

func CheckAccessiable(ctx context.Context, shellExecutor executor.Executor, cmd string, containString string) error {
	ok := false
	var err error
	var stdout, stderr string
	for i := 0; i < 20; i++ {
		time.Sleep(10 * time.Second)
		stdout, stderr, err = shellExecutor.ShellExec(ctx, cmd)
		if err != nil {
			logger.Errorf(ctx, "ShellExec[%s] failed, stderr: %v, err: %v ", cmd, stderr, err)
			continue
		}
		if !strings.Contains(stdout, containString) {
			logger.Errorf(ctx, "invite ingress failed, stdout: %s, expect: %s", stdout, containString)
			err = fmt.Errorf(fmt.Sprintf("invite ingress failed, stdout: %s, expect: %s", stdout, containString))
			continue
		}
		ok = true
		break
	}

	if !ok {
		return fmt.Errorf("ShellExec[%s] failed: %v, stderr: %v", cmd, err, stderr)
	}
	return nil
}

func CheckNotAccessiable(ctx context.Context, shellExecutor executor.Executor, cmd string, containString string) error {
	ok := false
	var err error
	var stdout, stderr string
	for i := 0; i < 2; i++ {
		stdout, stderr, err = shellExecutor.ShellExec(ctx, cmd)
		if err != nil {
			logger.Infof(ctx, "ShellExec[%s], stderr: %v, err: %v", cmd, stderr, err)
			if strings.Contains(stderr, containString) {
				return nil
			}
		}
		logger.Infof(ctx, "ShellExec[%s], stdout: %v, stderr: %v, but expect err: %s", cmd, stdout, stderr, containString)
		time.Sleep(10 * time.Second)
	}

	if !ok {
		return fmt.Errorf("CheckNotAccessiable[%s] failed: stdout: %v, stderr: %v, not contain expect err: %s", cmd, stdout, stderr, containString)
	}
	return nil
}

func UpdateIngressAnnotations(ctx context.Context, baseClient *cases.BaseClient, k8sVersion types.K8SVersion, ingressName string, annotations map[string]string) error {
	ingress, err := baseClient.AppClient.GetIngressDetailByName(ctx, baseClient.ClusterID, "default", ingressName, string(k8sVersion), nil)
	if err != nil {
		logger.Errorf(ctx, "GetIngressDetailByName failed: %v", err.Error())
		return err
	}
	if k8sVersion >= types.K8S_1_22_5 {
		ingressDetail, _ := ingress.(*appservice.IngressDetailNetwokingv1)
		for k, v := range annotations {
			ingressDetail.Annotations[k] = v
		}
		err := baseClient.AppClient.UpdateIngressYaml(ctx, baseClient.ClusterID, "default", ingressName, ingressDetail, nil)
		if err != nil {
			logger.Errorf(ctx, "update ingress failed: %v", err.Error())
			return fmt.Errorf("update ingress failed: %v", err.Error())
		}
	} else {
		ingressDetail, _ := ingress.(*appservice.IngressDetail)
		for k, v := range annotations {
			ingressDetail.Annotations[k] = v
		}
		err := baseClient.AppClient.UpdateIngressYaml(ctx, baseClient.ClusterID, "default", ingressName, ingressDetail, nil)
		if err != nil {
			logger.Errorf(ctx, "update ingress failed: %v", err.Error())
			return fmt.Errorf("update ingress failed: %v", err.Error())
		}
	}
	return nil
}
