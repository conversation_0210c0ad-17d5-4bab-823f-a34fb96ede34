// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/06/11 13:25:00, by <EMAIL>, create
*/
/*
创建 Ingress 测试：
1、若集群无blb ingress组件，则接口创建
2、创建ingress，并添加https证书（适配1.22集群）
3、校验创建后blb&eip资源状态
4、校验hostname连通性(空host)
5、更新ingress, 校验annotation字段没有变更
6、扩容组件副本, 校验是否选主
7、服务所在节点cordon，blb后端保留该节点
8、校验blb端口创建透传参数是否生效
9、blb监听修改定制配置，触发ingress同步成功
10.检查、BLB、EIP是否被清理
11、检查：EIP是否进入回收站并删除回收站的EIP
*/

package ingress

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	v1 "k8s.io/api/core/v1"
	"k8s.io/api/extensions/v1beta1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8stypes "k8s.io/apimachinery/pkg/types"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appservice"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccemonitor"
	// "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/vpc"
	ccesdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/executor"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/network/ingress-controller/common"
)

const (
	// CreateIngress - 创建 Ingress Case 名字
	CreateIngress cases.CaseName = "CreateIngress"

	shellPath = "/bin/bash"

	// DeployMentYAML - 测试用工作负载
	DeployMentYAML = `apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment-for-ingress
  labels:
    app: nginx
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      restartPolicy: Always
      containers:
        - name: nginx
          image: registry.baidubce.com/qa-test/nginx:1.17
          imagePullPolicy: Always
          ports:
            - containerPort: 80
`

	// NodePortSVCYAML - 测试用nodeport service
	NodePortSVCYAML = `apiVersion: v1
kind: Service
metadata:
  name: service-for-ingress
spec:
  selector:
    app: nginx
  type: NodePort
  ports:
  - name: nginx
    protocol: TCP
    port: 80
    targetPort: 80
`

	// cceIngressYAML - 测试用 Ingress
	cceIngressYAML = `apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    cce.ingress.blb-backup-content: ""
    kubernetes.io/cce.ingress.blb-cert-id: "[BLBCertID]"
    kubernetes.io/cce.ingress.blb.listener.http.enable-x-forwarded-for-header: "false"
    kubernetes.io/cce.ingress.blb.listener.http.keep-session.enable: "true"
    kubernetes.io/cce.ingress.blb.listener.http.keep-session.timeout: "3601"
    kubernetes.io/cce.ingress.blb.listener.http.keep-session.type: "insert"
    kubernetes.io/cce.ingress.blb.listener.http.scheduler: "LeastConnection"
    kubernetes.io/cce.ingress.blb.listener.https.enable-x-forwarded-for-header: "false"
    kubernetes.io/cce.ingress.blb.listener.https.keep-session.enable: "true"
    kubernetes.io/cce.ingress.blb.listener.https.keep-session.cookie-name: "cce-regression-test"
    kubernetes.io/cce.ingress.blb.listener.https.keep-session.type: "rewrite"
    kubernetes.io/cce.ingress.blb.listener.https.scheduler: "RoundRobin"
    kubernetes.io/cce.ingress.http-redirect: "false"
    kubernetes.io/cce.ingress.https: "true"
    kubernetes.io/cce.ingress.internal: "false"
    kubernetes.io/cce.ingress.timeout-in-seconds: "30"
    kubernetes.io/cce.ingress.vpc-subnet-id: "[SUBNETID]"
    kubernetes.io/ingress.class: cce
  name: cce-test-ingress
  namespace: default
spec:
  rules:
  - http:
      paths:
      - backend:
          serviceName: service-for-ingress
          servicePort: 80
        path: /`

	cce122IngressYAML = `apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    cce.ingress.blb-backup-content: ""
    kubernetes.io/cce.ingress.blb-cert-id: "[BLBCertID]"
    kubernetes.io/cce.ingress.blb.listener.http.enable-x-forwarded-for-header: "false"
    kubernetes.io/cce.ingress.blb.listener.http.keep-session.enable: "true"
    kubernetes.io/cce.ingress.blb.listener.http.keep-session.timeout: "3601"
    kubernetes.io/cce.ingress.blb.listener.http.keep-session.type: "insert"
    kubernetes.io/cce.ingress.blb.listener.http.scheduler: "LeastConnection"
    kubernetes.io/cce.ingress.blb.listener.https.enable-x-forwarded-for-header: "false"
    kubernetes.io/cce.ingress.blb.listener.https.keep-session.enable: "true"
    kubernetes.io/cce.ingress.blb.listener.https.keep-session.cookie-name: "cce-regression-test"
    kubernetes.io/cce.ingress.blb.listener.https.keep-session.type: "rewrite"
    kubernetes.io/cce.ingress.blb.listener.https.scheduler: "RoundRobin"
    kubernetes.io/cce.ingress.http-redirect: "false"
    kubernetes.io/cce.ingress.https: "true"
    kubernetes.io/cce.ingress.internal: "false"
    kubernetes.io/cce.ingress.timeout-in-seconds: "30"
    kubernetes.io/cce.ingress.vpc-subnet-id: "[SUBNETID]"
    kubernetes.io/ingress.class: cce
  name: cce-test-ingress
  namespace: default
spec:
  rules:
  - http:
      paths:
      - backend:
          service:
            name: service-for-ingress
            port: 
              number: 80
        path: /
        pathType: ImplementationSpecific`
)

func init() {
	cases.AddCase(context.TODO(), CreateIngress, NewCreateIngress)
}

type createIngress struct {
	base *cases.BaseClient

	shell       executor.Executor
	ingressName string
	ingress     *v1beta1.Ingress
	config      ingressConfig
}

type ingressConfig struct {
	IsArm     bool   `json:"isArm"`
	BLBCertID string `json:"BLBCertID"`

	// 预期BLB group id
	CheckBLBLayer4ClusterID string `json:"expectBLBLayer4ClusterID"`

	CheckBLBLayer7ClusterID string `json:"expectBLBLayer7ClusterID"`
}

// NewCreateIngress - 测试案例
func NewCreateIngress(ctx context.Context) cases.Interface {
	return &createIngress{
		shell: executor.NewExecutor(shellPath),
	}
}

func (c *createIngress) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base

	var cfg ingressConfig
	if err := json.Unmarshal(config, &cfg); err != nil {
		return fmt.Errorf("json.Unmarshal bos config failed: %s", err)
	}
	c.config = cfg

	return nil
}

func (c *createIngress) Name() cases.CaseName {
	return CreateIngress
}

func (c *createIngress) Desc() string {
	return "测试创建Ingress"
}

func (c *createIngress) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)

	// 1、若集群无blb ingress组件，则接口创建
	ingressControllerDp, err := c.base.K8SClient.AppsV1().Deployments(IngressControllerNamespace).Get(ctx, IngressControllerDeploymentName, metav1.GetOptions{})
	if err != nil || ingressControllerDp == nil {
		// 使用addon接口安装
		_, err = c.base.CCEClient.InstallAddon(ctx, c.base.ClusterID, &ccesdk.InstallParams{
			Name: "cce-ingress-controller",
		}, nil)
		if err != nil {
			logger.Errorf(ctx, "Install cce-ingress-controller failed: %v", err.Error())
		}
		logger.Infof(ctx, "Install cce-ingress-controller succeeded")
	} else {
		logger.Infof(ctx, "Found cce-ingress-controller, skip deploy")
	}

	// 等待 ingress pod running
	// 托管集群ingress组件被托管了无需校验
	cluster, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "get cluster failed: %v", err)
		return nil, err
	}
	if cluster.Cluster.Spec.MasterConfig.MasterType != types.MasterTypeManagedPro {
		var k8sPod resource.K8SPod
		if err = k8sPod.NewK8SPod(ctx, c.base, IngressControllerNamespace, nil); err != nil {
			return nil, err
		}
		k8sPod.SetMatchLabel("app", IngressControllerDeploymentName)
		k8sPod.SetStatus(resource.StatusRunning)
		if err = resource.WaitForResourceReady(ctx, &k8sPod); err != nil {
			logger.Errorf(ctx, "WaitPodsRunning failed: %s", err)
			return nil, err
		}
	}

	// 2、创建ingress，并添加https证书（适配1.22集群）
	// 部署 Deployment
	yaml := DeployMentYAML
	if c.config.IsArm {
		yaml = strings.ReplaceAll(DeployMentYAML, "nginx:", "nginx-arm64:")
	}
	if err := c.base.KubectlClient.Apply(ctx, types.K8S_1_13_10, c.base.KubeConfig, yaml); err != nil {
		logger.Errorf(ctx, "Deploy deployment failed: %s", err)
		return nil, err
	}

	// 部署 nodeport service
	if err := c.base.KubectlClient.Apply(ctx, types.K8S_1_13_10, c.base.KubeConfig, NodePortSVCYAML); err != nil {
		logger.Errorf(ctx, "Deploy service failed: %s", err)
		return nil, err
	}

	// 获取集群的一个vpc子网
	clusterInfo, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "Get cluster failed: %s", err)
		return nil, err
	}
	subnetID := clusterInfo.Cluster.Spec.ContainerNetworkConfig.LBServiceVPCSubnetID
	k8sVersion := clusterInfo.Cluster.Spec.K8SVersion

	// subnets, err := c.base.VPCClient.ListSubnet(ctx, &vpc.ListSubnetArgs{VPCID: clusterInfo.Cluster.Spec.VPCID,
	// 	SubnetType: vpc.SubnetTypeBCC}, nil)
	// if err != nil {
	// 	logger.Errorf(ctx, "List subnets failed: %s", err)
	// 	return nil, err
	// }
	// subnetID := subnets[0].SubnetID

	// 部署 Ingress
	var strYml string
	// 兼容1.22集群
	if k8sVersion >= types.K8S_1_22_5 {
		strYml = strings.ReplaceAll(cce122IngressYAML, "[SUBNETID]", subnetID)
	} else {
		strYml = strings.ReplaceAll(cceIngressYAML, "[SUBNETID]", subnetID)
	}

	// ingress添加证书
	if c.config.BLBCertID == "" {
		logger.Infof(ctx, "config.BLBCertID is empty, use default cert")
		c.config.BLBCertID = "cert-7k3sfv6wkzry" // jpaas账户下BLB 证书 cert-5bqpykggjzmi
	}
	strYml = strings.ReplaceAll(strYml, "[BLBCertID]", c.config.BLBCertID)
	if _, err = c.base.AppClient.PostAppResource(ctx, c.base.ClusterID, "cce-test-ingress", strYml, "default", nil); err != nil {
		logger.Errorf(ctx, "Deploy Ingress failed: %s", err)
		return nil, err
	}

	// 3、校验创建后blb&eip资源状态
	var ok bool
	var eip string
	var blbID string
	var annotations map[string]string
	for i := 0; i < 20; i++ {
		time.Sleep(30 * time.Second)

		annotations, err = GetIngressAnnotation(ctx, c.base, k8sVersion, "cce-test-ingress")
		if err != nil {
			return resources, fmt.Errorf("Get Ingress Annoatation failed: %v ", err.Error())
		}

		// 检查是否有 LB
		blbID, ok = annotations[common.AnnotationBLBShortID]
		if !ok {
			logger.Infof(ctx, "%s is empty, retry", common.AnnotationBLBShortID)
			continue
		}

		logger.Infof(ctx, "BLB ID: %s", blbID)

		// 检查是否有 EIP
		eip, ok = annotations[common.AnnotationEIP]
		if !ok {
			logger.Infof(ctx, "%s is empty, retry", common.AnnotationEIP)
			continue
		}

		logger.Infof(ctx, "EIP: %s", eip)

		break
	}

	// 统计 BLB/EIP 资源
	if blbID != "" {
		resources = append(resources, cases.Resource{
			CaseName: CreateIngress,
			Type:     cases.ResourceTypeBLB,
			ID:       blbID,
		})
	}

	if eip != "" {
		resources = append(resources, cases.Resource{
			CaseName: CreateIngress,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
	}

	// 结果统计
	if blbID == "" || eip == "" {
		return resources, fmt.Errorf("create ingress failed: blbID=[%s] eip=[%s]", blbID, eip)
	}

	// 检查BLB所处7层集群信息（lcc测试）
	if c.config.CheckBLBLayer4ClusterID != "" || c.config.CheckBLBLayer7ClusterID != "" {
		blbInfo, err := c.base.AppBLBClient.DescribeAppLoadBalancerByID(ctx, blbID, nil)
		if err != nil {
			logger.Errorf(ctx, "%s AppBLB DescribeAppLoadBalancerByID failed: %s", blbID, err)
			return resources, fmt.Errorf("%s AppBLB DescribeAppLoadBalancerByID failed: %s", blbID, err)
		}
		if blbInfo.Layer4ClusterID != nil {
			logger.Infof(ctx, "AppBLB %s Layer4ClusterId: %s", blbID, *blbInfo.Layer4ClusterID)
		}
		if blbInfo.Layer7ClusterID != nil {
			logger.Infof(ctx, "AppBLB %s Layer7ClusterId: %s", blbID, *blbInfo.Layer7ClusterID)
		}
		if *blbInfo.Layer7ClusterID != c.config.CheckBLBLayer7ClusterID {
			return resources, fmt.Errorf("AppBLB %s Layer7ClusterID is %s , not as expect: %s", blbID, *blbInfo.Layer7ClusterID, c.config.CheckBLBLayer7ClusterID)
		}
	}

	var blbStatus resource.AppBLBStatus
	if err = blbStatus.NewAppBLBStatus(ctx, c.base, blbID, "available"); err != nil {
		return resources, err
	}
	if err = resource.WaitForResourceReady(ctx, &blbStatus); err != nil {
		return resources, err
	}

	var eipStatus resource.EIPStatus
	if err = eipStatus.NewEIPStatus(ctx, c.base, eip, "binded"); err != nil {
		return resources, err
	}
	if err = resource.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	// 4、校验hostname连通性(空host)
	ok = false
	var cmd string
	for i := 0; i < 20; i++ {
		time.Sleep(10 * time.Second)
		cmd = fmt.Sprintf("curl http://%s/", eip)
		stdout, _, err := c.shell.ShellExec(ctx, cmd)
		if err != nil {
			logger.Errorf(ctx, "ShellExec[%s] failed: %v ", cmd, err)
			continue
		}
		if !strings.Contains(stdout, "Welcome to nginx") {
			logger.Errorf(ctx, "invite nginx service failed")
			err = fmt.Errorf(fmt.Sprintf("invite nginx service failed, stdout: %s", stdout))
			continue
		}
		ok = true
		break
	}

	if !ok {
		return resources, fmt.Errorf("ShellExec[%s] failed: %v ", cmd, err)
	}

	// 5、更新ingress, 校验annotation字段没有变更
	var newAnnotations map[string]string

	// 使用cce-app-service api获取ingress详情
	newIngress, err := c.base.AppClient.GetIngressDetailByName(ctx, c.base.ClusterID, "default", "cce-test-ingress", string(k8sVersion), nil)
	if err != nil {
		logger.Errorf(ctx, "Get ingress detail by name failed: %v", err.Error())
		return resources, err
	}

	if k8sVersion >= types.K8S_1_22_5 {
		ingressDetail, _ := newIngress.(*appservice.IngressDetailNetwokingv1)
		ingressDetail.Annotations["test"] = "reg"
		err = c.base.AppClient.UpdateIngressYaml(ctx, c.base.ClusterID, "default", "cce-test-ingress", ingressDetail, nil)
		if err != nil {
			logger.Errorf(ctx, "update ingress annotation failed: %v", err.Error())
		}

		newAnnotations, err = GetIngressAnnotation(ctx, c.base, k8sVersion, "cce-test-ingress")
		if err != nil {
			return resources, fmt.Errorf("Get Ingress Annoatation failed: %v ", err.Error())
		}
	} else {
		ingressDetail, _ := newIngress.(*appservice.IngressDetail)
		ingressDetail.Annotations["test"] = "reg"
		err = c.base.AppClient.UpdateIngressYaml(ctx, c.base.ClusterID, "default", "cce-test-ingress", ingressDetail, nil)
		if err != nil {
			logger.Errorf(ctx, "update ingress annotation failed: %v", err.Error())
		}

		newAnnotations, err = GetIngressAnnotation(ctx, c.base, k8sVersion, "cce-test-ingress")
		if err != nil {
			return resources, fmt.Errorf("Get Ingress Annoatation failed: %v ", err.Error())
		}
	}

	for key, value := range newAnnotations {
		if v, ok := annotations[key]; ok {
			if v != value {
				logger.Errorf(ctx, "ingress annotation changed, origin is [%s: %s], now is [%s: %s]", key, v, key, value)
				return resources, err
			}
		}
	}

	logger.Infof(ctx, "check annotations success after ingress update")

	// 6、扩容组件副本, 校验是否选主(1.22和vpc-cni集群除外)
	// 托管集群ingress组件托管不进行校验
	if cluster.Cluster.Spec.MasterConfig.MasterType != types.MasterTypeManagedPro {
		if clusterInfo.Cluster.Spec.K8SVersion < types.K8S_1_22_5 && !strings.Contains(string(clusterInfo.Cluster.Spec.ContainerNetworkConfig.Mode), "vpc-secondary-ip") {
			if err = SelectMainCheck(ctx, c.base); err != nil {
				logger.Errorf(ctx, "check muti %s pod failed: %v", IngressControllerDeploymentName, err.Error())
				return resources, err
			}
		}
	}

	// 7、服务所在节点cordon，blb后端保留该节点
	if err = CordonNodeCheck(ctx, c.base, blbID); err != nil {
		logger.Errorf(ctx, "check blbrs with cordon node failed: %s", err.Error())
		return resources, err
	}

	// 8、校验blb端口创建透传参数是否生效
	if err = PassThroughCheck(ctx, c.base, blbID); err != nil {
		logger.Errorf(ctx, "check ingress pass through appblb listener args failed: %v")
		return resources, err
	}

	// 9、blb监听修改定制配置，触发ingress同步成功
	if err = ListenerUpdateSyncCheck(ctx, c.base, blbID); err != nil {
		logger.Errorf(ctx, "blb listener update cause ingress sync failed")
		return resources, err
	}

	err = c.base.AppClient.DeleteAppResource(ctx, c.base.ClusterID, "ingress", "default", "cce-test-ingress", nil)
	if err != nil {
		logger.Errorf(ctx, "delete ingress failed: %v", err.Error())
		return resources, err
	}

	// 10、检查：BLB、EIP是否被清理
	eipStatus.SetStatus("deleted")
	if err = resource.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	// 11、检查：EIP是否进入回收站并删除回收站的EIP
	if isInRecycleBin, err := eipStatus.CheckIsInRecycleBin(ctx); err != nil || !isInRecycleBin {
		return resources, err
	}
	logger.Infof(ctx, "delete eip %s in recycle bin", eip)
	if isSuccessDelete, err := eipStatus.DeleteEIPInRecycleBin(ctx, eip); err != nil || !isSuccessDelete {
		return resources, err
	}

	return resources, nil
}

func (c *createIngress) Clean(ctx context.Context) error {
	return nil
}

func (c *createIngress) Continue(ctx context.Context) bool {
	return true
}

func (c *createIngress) ConfigFormat() string {
	return ""
}

// SelectMainCheck 校验组件扩容副本后，选主功能是否生效，有且仅有一个副本工作
func SelectMainCheck(ctx context.Context, baseClient *cases.BaseClient) error {
	logger.Infof(ctx, "start to cce-ingress-controller select main check")
	// 扩容 cce-ingress-controller 副本数
	scale, err := baseClient.K8SClient.AppsV1().Deployments(IngressControllerNamespace).GetScale(ctx, IngressControllerDeploymentName, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "get deployment %s scale failed: %v", IngressControllerDeploymentName, err.Error())
		return err
	}

	scale.Spec.Replicas = int32(3)
	_, err = baseClient.K8SClient.AppsV1().Deployments(IngressControllerNamespace).UpdateScale(context.TODO(), IngressControllerDeploymentName, scale, metav1.UpdateOptions{})
	if err != nil {
		logger.Errorf(ctx, "scale up  deployment %s replicas to 3 failed: %v", IngressControllerDeploymentName, err.Error())
		return err
	}

	time.Sleep(5 * time.Second)

	var k8sPod resource.K8SPod
	if err := k8sPod.NewK8SPod(ctx, baseClient, IngressControllerNamespace, nil); err != nil {
		return err
	}
	k8sPod.SetMatchLabel("app", IngressControllerDeploymentName)
	k8sPod.SetStatus(resource.StatusRunning)
	resource.WaitForResourceReady(ctx, &k8sPod)

	// 更新ingress annotation
	_, err = baseClient.K8SClient.ExtensionsV1beta1().Ingresses("default").
		Patch(ctx, "cce-test-ingress", k8stypes.MergePatchType, []byte(`{"metadata":{"annotations":{"select-main-check":"true"}}}`), metav1.PatchOptions{})
	if err != nil {
		logger.Errorf(ctx, "update ingress annotation failed: %v", err.Error())
	}

	// 统计3副本日志，有且仅有1副本pod在执行
	executeReplica := 0
	pods, err := k8sPod.GetPodList(ctx)
	if err != nil {
		logger.Errorf(ctx, "get pod list failed: %v", err)
		return err
	}

	for _, pod := range pods.Items {
		log, err := k8sPod.GetPodLogs(ctx, pod.Name, &v1.PodLogOptions{})
		if err != nil {
			logger.Errorf(ctx, "get pod logs failed: %v", err)
			return err
		}
		if strings.Contains(log, "successfully acquired lease kube-system/cce-ingress-controller") {
			executeReplica++
		}
	}

	if executeReplica == 1 {
		return nil
	}

	return fmt.Errorf("executeReplica = %d", executeReplica)
}

// CordonNodeCheck 校验服务所在节点被封锁后，BLB后端服务器不变更
func CordonNodeCheck(ctx context.Context, baseClient *cases.BaseClient, blbID string) error {
	var k8sPod resource.K8SPod
	if err := k8sPod.NewK8SPod(ctx, baseClient, "default", nil); err != nil {
		return err
	}
	k8sPod.SetMatchLabel("app", "nginx")

	var appBLBBackends resource.AppBLBBackends
	if err := appBLBBackends.NewAppBLBBackends(ctx, baseClient, blbID, baseClient.ClusterID, false, k8sPod); err != nil {
		return err
	}

	nodeNameList, err := k8sPod.GetPodNodeNameList(ctx)
	if err != nil {
		return fmt.Errorf("Get pod node name list failed: %v ", err)
	}

	// 封锁服务所在的节点
	for nodeName := range nodeNameList {
		err = CordonNode(ctx, baseClient, nodeName, true)
		if err != nil {
			return fmt.Errorf("cordon node %s failed: %v", nodeName, err.Error())
		}
	}

	time.Sleep(30 * time.Second)

	if err = resource.WaitForResourceReady(ctx, &appBLBBackends); err != nil {
		return fmt.Errorf("wait blb rs equal worker failed: %v", err.Error())
	}

	logger.Infof(ctx, "check blb rs with cordon node success")

	for nodeName := range nodeNameList {
		err = CordonNode(ctx, baseClient, nodeName, false)
		if err != nil {
			return fmt.Errorf("cordon node %s failed: %v", nodeName, err.Error())
		}
	}

	return nil
}

// PassThroughCheck ingress透传blb监听端口参数校验
func PassThroughCheck(ctx context.Context, baseClient *cases.BaseClient, blbID string) error {
	blbHTTPListener, err := baseClient.AppBLBClient.DescribeAppHTTPListener(ctx, blbID, 80, nil)
	if err != nil {
		return fmt.Errorf("DescribeAppHTTPListener failed: %v", err.Error())
	}

	if len(blbHTTPListener.ListenerList) == 0 {
		return errors.New("blb http listener list is empty")
	}

	if blbHTTPListener.ListenerList[0].Scheduler != "LeastConnection" ||
		!blbHTTPListener.ListenerList[0].KeepSession ||
		blbHTTPListener.ListenerList[0].KeepSessionType != "insert" ||
		blbHTTPListener.ListenerList[0].KeepSessionTimeout != 3601 ||
		blbHTTPListener.ListenerList[0].XForwardedFor {
		return errors.New("app httpListener resp args do not match")
	}

	blbHTTPSListener, err := baseClient.AppBLBClient.DescribeAppHTTPSListener(ctx, blbID, 443, nil)
	if err != nil {
		return fmt.Errorf("DescribeAppHTTPListener failed: %v", err.Error())
	}
	if len(blbHTTPSListener.ListenerList) == 0 {
		return errors.New("blb https listener list is empty")
	}

	if blbHTTPSListener.ListenerList[0].Scheduler != "RoundRobin" ||
		!blbHTTPSListener.ListenerList[0].KeepSession ||
		blbHTTPSListener.ListenerList[0].KeepSessionType != "rewrite" ||
		blbHTTPSListener.ListenerList[0].KeepSessionCookieName != "cce-regression-test" ||
		blbHTTPSListener.ListenerList[0].XForwardedFor {
		return errors.New("app httpsListener resp args do not match")
	}

	logger.Infof(ctx, "check ingress pass through appblb listener args success")

	return nil
}

func CordonNode(ctx context.Context, baseClient *cases.BaseClient, nodeName string, desired bool) error {
	payload := []struct {
		Op    string `json:"op"`
		Path  string `json:"path"`
		Value any    `json:"value"`
	}{
		{
			Op:    "replace",
			Path:  "/spec/unschedulable",
			Value: desired,
		},
	}
	payloadBytes, _ := json.Marshal(payload)
	_, err := baseClient.K8SClient.CoreV1().Nodes().Patch(ctx, nodeName, k8stypes.JSONPatchType, payloadBytes, metav1.PatchOptions{})

	if err != nil {
		logger.Errorf(ctx, "%v", err)
		return err
	}
	return nil
}

func GetIngressAnnotation(ctx context.Context, baseClient *cases.BaseClient, k8sVersion types.K8SVersion, ingressName string) (map[string]string, error) {
	var annotations map[string]string
	// 使用cce-app-service的api进行请求ingress列表
	ingressList, err := baseClient.AppClient.GetIngressList(ctx, baseClient.ClusterID, "default", ingressName, string(k8sVersion), nil)
	if err != nil {
		return nil, fmt.Errorf("Get Ingress failed: %s ", err)
	}
	// logger.Infof(ctx, "Get ingress success: %s", utils.ToJSON(ingressList))

	if k8sVersion >= types.K8S_1_22_5 {
		ingress, _ := ingressList.(*appservice.IngressListNetwokingV1)
		if len(ingress.Items) != 0 {
			annotations = ingress.Items[0].Annotations
		} else {
			return nil, errors.New("ingress list is empty")
		}
	} else {
		ingress, _ := ingressList.(*appservice.IngressList)
		if len(ingress.Items) != 0 {
			annotations = ingress.Items[0].Annotations
		} else {
			return nil, errors.New("ingress list is empty")
		}
	}
	return annotations, nil
}

// ListenerUpdateSyncCheck blb监听变更自定义配置后的同步校验
func ListenerUpdateSyncCheck(ctx context.Context, baseClient *cases.BaseClient, blbID string) error {
	// 获取当前blb的监听信息
	listeners, err := baseClient.AppBLBClient.DescribeAppHTTPSListener(ctx, blbID, 443, nil)
	if err != nil {
		logger.Errorf(ctx, "describe blb listener failed : %v", err.Error())
		return err
	}
	if len(listeners.ListenerList) == 0 {
		return fmt.Errorf("blb https listener is empty")
	}

	// 更新监听的自定义配置
	err = baseClient.AppBLBClient.UpdateAppHTTPSListener(ctx, blbID, 443, &appblb.UpdateAppHTTPSListenerArgs{
		Scheduler:             listeners.ListenerList[0].Scheduler,
		KeepSession:           listeners.ListenerList[0].KeepSession,
		KeepSessionType:       listeners.ListenerList[0].KeepSessionType,
		KeepSessionTimeout:    listeners.ListenerList[0].KeepSessionTimeout,
		KeepSessionCookieName: listeners.ListenerList[0].KeepSessionCookieName,
		XForwardedFor:         listeners.ListenerList[0].XForwardedFor,
		ServerTimeout:         listeners.ListenerList[0].ServerTimeout,
		CertIDs:               listeners.ListenerList[0].CertIDs,
		EncryptionType:        listeners.ListenerList[0].EncryptionType,
		EncryptionProtocols:   listeners.ListenerList[0].EncryptionProtocols,
		DualAuth:              listeners.ListenerList[0].DualAuth,
		ClientCertIDs:         listeners.ListenerList[0].ClientCertIDs,
		ExtendedAttributes: map[string]interface{}{
			"proxy_buffer_size": 4,
			"proxy_buffers":     "8 4",
			"proxy_set_header": []string{
				"X-BLB-Cport $cport",
			},
		},
	}, nil)
	if err != nil {
		logger.Errorf(ctx, "update blb https listener failed: %v", err.Error())
		return err
	}
	newListeners, err := baseClient.AppBLBClient.DescribeAppHTTPSListener(ctx, blbID, 443, nil)
	if err != nil {
		logger.Errorf(ctx, "describe blb listener failed : %v", err.Error())
		return err
	}
	logger.Infof(ctx, "new listener : %v", utils.ToJSON(newListeners.ListenerList[0]))

	// 更新ingress的annotation以触发同步
	_, err = baseClient.K8SClient.NetworkingV1().Ingresses("default").
		Patch(ctx, "cce-test-ingress", k8stypes.MergePatchType, []byte(`{"metadata":{"annotations":{"updateListener":"true"}}}`), metav1.PatchOptions{})
	if err != nil {
		logger.Errorf(ctx, "update ingress annotation failed: %v", err.Error())
		return err
	}

	// 获取ingress的事件，校验是否同步成功
	time.Sleep(60 * time.Second)
	events, err := baseClient.MonitorClient.GetK8sEvent(ctx, &ccemonitor.EventQueryArg{
		StartTime:    time.Now().Add(-1 * time.Hour).UTC().Format("2006-01-02T15:04:05.000Z"),
		EndTime:      time.Now().UTC().Format("2006-01-02T15:04:05.000Z"),
		ClusterUuid:  baseClient.ClusterID,
		Namespace:    "default",
		ResourceName: "cce-test-ingress",
		ResourceKind: "Ingress",
		PageNo:       1,
		PageSize:     20,
	}, nil)
	if err != nil {
		logger.Errorf(ctx, "get ingress event failed: %v", err.Error())
		return err
	}

	for _, event := range events.Result {
		if event.Reason == "SyncIngressSuccess" {
			diff := time.Now().Sub(event.LastOccurrenceTimestamp)
			if diff < time.Minute {
				logger.Infof(ctx, "sync ingress success")
				return nil
			}
		}
	}

	return fmt.Errorf("sync ingress failed")
}
