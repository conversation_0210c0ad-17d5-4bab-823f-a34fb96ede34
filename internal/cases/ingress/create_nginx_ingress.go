/* create_nginx_ingress.go */
/*
modification history
--------------------
2022/7/18, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
*/

package ingress

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	e2ecommon "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/executor"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	// CreateNginxIngress - 创建 Nginx Ingress Case 名字
	CreateNginxIngress cases.CaseName = "CreateNginxIngress"

	// 插件名称
	PluginCCEIngressNginxController = "cce-ingress-nginx-controller"

	DefaultNamespace = "default"
	// IngressClass - ingress class 名
	IngressClass string = "cce-nginx"

	NginxDeployName = "deployment-for-nginx-ingress"
	// NginxDeployMentYAML - 测试用工作负载
	NginxDeployMentYAML = `apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment-for-nginx-ingress
  labels:
    app: nginx
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      restartPolicy: Always
      containers:
        - name: nginx
          image: registry.baidubce.com/qa-test/nginx:1.17
          imagePullPolicy: Always
          ports:
            - containerPort: 80
`

	NginxNodePortSVCName = "service-for-nginx-ingress"
	// NginxNodePortSVCYAML - 测试用nodeport service
	NginxNodePortSVCYAML = `apiVersion: v1
kind: Service
metadata:
  name: service-for-nginx-ingress
spec:
  selector:
    app: nginx
  type: NodePort
  ports:
  - name: nginx
    protocol: TCP
    port: 80
    targetPort: 80
`
	IngressName = "cce-test-nginx-ingress"
	// cceIngressYAML - 测试用 Ingress
	cceNginxIngressYAML_ExtensionsV1beta1 = `apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    cce.ingress.blb-backup-content: ""
    kubernetes.io/ingress.class: cce-nginx
    nginx.ingress.kubernetes.io/use-regex: 'true'
  name: cce-test-nginx-ingress
  namespace: default
spec:
  rules:
  - http:
      paths:
      - backend:
          serviceName: service-for-nginx-ingress
          servicePort: 80
        path: /`

	// cce1-22IngressYAML - 测试用 Ingress
	cceNginxIngressYAML_NetworkingV1 = `apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    cce.ingress.blb-backup-content: ""
    kubernetes.io/ingress.class: cce-nginx
    nginx.ingress.kubernetes.io/use-regex: 'true'
  name: cce-test-nginx-ingress
  namespace: default
spec:
  rules:
  - http:
      paths:
      - backend:
          service:
            name: service-for-nginx-ingress
            port: 
              number: 80
        path: /
        pathType: ImplementationSpecific
`
)

func init() {
	cases.AddCase(context.TODO(), CreateNginxIngress, NewCreateNginxIngress)
}

var _ cases.Interface = &createNginxIngress{}

type createNginxIngress struct {
	base *cases.BaseClient

	shell  executor.Executor
	config ingressConfig
}

// NewCreateNginxIngress - 测试案例
func NewCreateNginxIngress(ctx context.Context) cases.Interface {
	return &createNginxIngress{
		shell: executor.NewExecutor(shellPath),
	}
}

func (c *createNginxIngress) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base

	var cfg ingressConfig
	if err := json.Unmarshal(config, &cfg); err != nil {
		return fmt.Errorf("json.Unmarshal nginx ingress config failed: %s", err)
	}
	c.config = cfg

	return nil
}

func (c *createNginxIngress) Name() cases.CaseName {
	return CreateNginxIngress
}

func (c *createNginxIngress) Desc() string {
	return "创建Ingress Nginx"
}

// Check - nginx-ingress测试项
//
//	1、部署 cce-nginx-ingress 插件
//	2、svc关联的blb状态是否为availabel和eip状态是否为binding
//	3、创建nginx-ingress是否能被分配eip
//	4、后端连通性验证
//	5、插件卸载，blb和eip资源是否被清理
func (c *createNginxIngress) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	clusterID := c.base.ClusterID

	// 根据列表节点进行节点组创建
	instanceGroupName := "test-instancegroup-for-nginx-ingress"
	ig, err := e2ecommon.NewInstanceGroupWithNullConfig(ctx, c.base, instanceGroupName, true)
	if err != nil {
		return nil, fmt.Errorf("new instance group client failed: %v", err.Error())
	}

	// 等待节点组节点ready
	if err := ig.CheckResource(ctx); err != nil {
		return nil, fmt.Errorf("check instancegroup resource failed: %v", err.Error())
	}

	instanceGroupID := ig.GetInstanceGroupID()

	// 1、部署 cce-nginx-ingress 插件, 如果cce-nginx-ngx-control helm release存在，则跳过部署
	releaseName := "cce-nginx-ngx-control"
	plugin, err := e2ecommon.NewPlugin(ctx, c.base, "cce-ingress-nginx-controller")
	if err != nil {
		return nil, fmt.Errorf("new plugin client failed: %v", err.Error())
	}
	releaseExist, err := plugin.IsReleaseExist(ctx, releaseName)
	if err != nil {
		return nil, fmt.Errorf("%v", err.Error())
	}
	if !releaseExist {
		// K8s1.20.8版本之前的集群使用3.29.0版本
		// if err := plugin.InstallNginxIngressPlugin(ctx, IngressClass, instanceGroupID, c.base.ClusterSpec.K8SVersion); err != nil {
		// 	return nil, fmt.Errorf("install nginx ingress plugin failed: %v", err.Error())
		// }
		// 使用addon接口安装插件, 指定按流量计费和带宽峰值为10mbps
		_, err = c.base.CCEClient.InstallAddon(ctx, clusterID, &ccev2.InstallParams{
			Name: PluginCCEIngressNginxController,
			Params: fmt.Sprintf("fullnameOverride: %s\ncontroller:\n  ingressClass: cce-nginx\n  scope:\n    enabled: false\n    namespace: ''\n  kind: DaemonSet\n  nodeSelector:\n    instance-group-id: %s\n  resources:\n    requests:\n      cpu: 0.25\n      memory: 256Mi\n    limits:\n      cpu: 0.5\n      memory: 1024Mi\n  tolerations: []\n  service:\n    annotations:\n      service.beta.kubernetes.io/cce-elastic-ip-billing-method: ByTraffic\n      service.beta.kubernetes.io/cce-elastic-ip-bandwidth-in-mbps: 10\n",
				releaseName, instanceGroupID),
		}, nil)
		if err != nil {
			return nil, fmt.Errorf("install nginx ingress plugin failed: %v", err.Error())
		}
		time.Sleep(30 * time.Second)
	}

	// 2、svc关联的blb状态是否为availabel和eip状态是否为binding
	ingressSvc := IngressClass + "-ingress-svc"
	var k8sService e2ecommon.K8SService
	if err := k8sService.NewK8SService(ctx, c.base, "kube-system", ingressSvc); err != nil {
		return nil, err
	}
	err = e2ecommon.WaitForResourceReady(ctx, &k8sService)
	if err != nil {
		return nil, fmt.Errorf("check nginx-ingress plugin's lb-service status failed,: %v", err.Error())
	}
	blb, _ := k8sService.GetServiceBlB(ctx, "kube-system", ingressSvc)
	eip, _ := k8sService.GetServiceEIP(ctx, "kube-system", ingressSvc)

	// 统计 BLB/EIP 资源
	if blb != "" {
		resources = append(resources, cases.Resource{
			CaseName: CreateNginxIngress,
			Type:     cases.ResourceTypeAppBLB,
			ID:       blb,
		})
	}
	if eip != "" {
		resources = append(resources, cases.Resource{
			CaseName: CreateNginxIngress,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
	}

	// 检查blb所处4层集群信息（lcc测试）
	if c.config.CheckBLBLayer4ClusterID != "" || c.config.CheckBLBLayer7ClusterID != "" {
		blbInfo, err := c.base.AppBLBClient.DescribeAppLoadBalancerByID(ctx, blb, nil)
		if err != nil {
			logger.Errorf(ctx, "%s AppBLB DescribeAppLoadBalancerByID failed: %s", blb, err)
			return resources, fmt.Errorf("%s AppBLB DescribeAppLoadBalancerByID failed: %s", blb, err)
		}
		if blbInfo.Layer4ClusterID != nil {
			logger.Infof(ctx, "AppBLB %s Layer4ClusterId: %s", blb, *blbInfo.Layer4ClusterID)
		}
		if blbInfo.Layer7ClusterID != nil {
			logger.Infof(ctx, "AppBLB %s Layer7ClusterId: %s", blb, *blbInfo.Layer7ClusterID)
		}
		if c.config.CheckBLBLayer4ClusterID != "" && *blbInfo.Layer4ClusterID != c.config.CheckBLBLayer4ClusterID {
			return resources, fmt.Errorf("AppBLB %s Layer4ClusterID is %s , not as expect: %s", blb, *blbInfo.Layer4ClusterID, c.config.CheckBLBLayer4ClusterID)
		}
		// Nginx Ingress是4层BLB
		// if c.config.CheckBLBLayer7ClusterID != "" && *blbInfo.Layer7ClusterID != c.config.CheckBLBLayer7ClusterID {
		// 	return resources, fmt.Errorf("AppBLB %s Layer7ClusterID is %s , not as expect: %s", blb, *blbInfo.Layer7ClusterID, c.config.CheckBLBLayer7ClusterID)
		// }
	}

	var blbStatus e2ecommon.AppBLBStatus
	if err = blbStatus.NewAppBLBStatus(ctx, c.base, blb, "available"); err != nil {
		return resources, err
	}
	if err = e2ecommon.WaitForResourceReady(ctx, &blbStatus); err != nil {
		return resources, err
	}

	var eipStatus e2ecommon.EIPStatus
	if err = eipStatus.NewEIPStatus(ctx, c.base, eip, "binded"); err != nil {
		return resources, err
	}
	if err = e2ecommon.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	// 部署 Deployment
	yaml := NginxDeployMentYAML
	if c.config.IsArm {
		yaml = strings.ReplaceAll(DeployMentYAML, "qa-test/nginx:", "qa-test/nginx-arm64:")
	}
	//if err := c.base.KubectlClient.Apply(ctx, ccetypes.K8S_1_13_10, c.base.KubeConfig, yaml); err != nil {
	//	logger.Errorf(ctx, "Deploy deployment failed: %s", err)
	//	return nil, err
	//}
	//  改用app-service部署
	_, err = c.base.AppClient.PostAppResource(ctx, clusterID, NginxDeployName, yaml, DefaultNamespace, nil)
	if err != nil {
		logger.Errorf(ctx, "deployment created failed: %s", err)
		return nil, err
	}

	// 部署 nodeport service
	_, err = c.base.AppClient.PostAppResource(ctx, clusterID, NginxNodePortSVCName, NginxNodePortSVCYAML, DefaultNamespace, nil)
	if err != nil {
		logger.Errorf(ctx, "node port svc created failed: %s", err)
		return nil, err
	}

	// 3、创建nginx-ingress是否能被分配eip
	ingressYaml := cceNginxIngressYAML_ExtensionsV1beta1
	isk8sVersionAfterOrEqual1225, _ := c.base.ClusterSpec.K8SVersion.IsAfterOrEqual(ccetypes.K8S_1_22_5)
	if isk8sVersionAfterOrEqual1225 {
		ingressYaml = cceNginxIngressYAML_NetworkingV1
	}

	_, err = c.base.AppClient.PostAppResource(ctx, clusterID, IngressName, ingressYaml, DefaultNamespace, nil)
	if err != nil {
		logger.Errorf(ctx, "Deploy nginx ingress failed: %s", err)
		return nil, err
	}

	var k8sIngress e2ecommon.K8SIngress
	if err := k8sIngress.NewK8SIngress(ctx, c.base, DefaultNamespace, IngressName, ingressYaml); err != nil {
		return nil, err
	}
	if err := e2ecommon.WaitForResourceReady(ctx, &k8sIngress); err != nil {
		return nil, err
	}

	// 4、后端连通性验证
	ok := false
	var cmd string
	for i := 0; i < 20; i++ {
		time.Sleep(10 * time.Second)
		cmd = fmt.Sprintf("curl http://%s/", eip)
		stdout, _, err := c.shell.ShellExec(ctx, cmd)
		if err != nil {
			logger.Errorf(ctx, "ShellExec[%s] failed: %v ", cmd, err)
			continue
		}
		if !strings.Contains(stdout, "Welcome to nginx") {
			logger.Errorf(ctx, "invite nginx service failed")
			continue
		}
		ok = true
		break
	}

	if !ok {
		return resources, fmt.Errorf("ShellExec[%s] failed: %v ", cmd, err)
	}

	// 5、插件卸载，blb和eip资源是否被清理
	if err := plugin.UninstallPlugin(ctx, "kube-system", IngressClass+"-ngx-control"); err != nil {
		logger.Infof(ctx, "uninstall nginx ingress plugin falied: %v", err.Error())
		return nil, err
	}
	blbStatus.SetStatus("deleted")
	if err = e2ecommon.WaitForResourceReady(ctx, &blbStatus); err != nil {
		return resources, err
	}
	eipStatus.SetStatus("deleted")
	if err = e2ecommon.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	return resources, nil
}

func (c *createNginxIngress) Clean(ctx context.Context) error {
	// 删除 Ingress
	if c.base.ClusterSpec.K8SVersion >= ccetypes.K8S_1_22_5 {
		if err := c.base.K8SClient.
			NetworkingV1().
			Ingresses(DefaultNamespace).
			Delete(ctx, IngressName, metav1.DeleteOptions{}); err != nil {
			logger.Infof(ctx, "delete ingress failed: %v", err.Error())
			return err
		}
	} else {
		if err := c.base.K8SClient.ExtensionsV1beta1().Ingresses(DefaultNamespace).Delete(ctx, IngressName, metav1.DeleteOptions{}); err != nil {
			logger.Infof(ctx, "delete ingress failed: %v", err.Error())
			return err
		}
	}

	// 删除 service
	if err := c.base.K8SClient.CoreV1().Services(DefaultNamespace).Delete(ctx, NginxNodePortSVCName, metav1.DeleteOptions{}); err != nil {
		logger.Infof(ctx, "delete service failed: %v", err.Error())
		return err
	}

	// 删除 deployment
	if err := c.base.K8SClient.AppsV1().Deployments(DefaultNamespace).Delete(ctx, NginxDeployName, metav1.DeleteOptions{}); err != nil {
		logger.Infof(ctx, "delete deployment failed: %v", err.Error())
		return err
	}

	return nil
}

func (c *createNginxIngress) Continue(ctx context.Context) bool {
	return true
}

func (c *createNginxIngress) ConfigFormat() string {
	return ""
}
