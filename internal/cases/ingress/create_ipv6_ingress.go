// Copyright 2025 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2025/01/XX, by <EMAIL>, create
*/
/*
IPv6 Ingress E2E测试：
1. 创建标准Ingress（仅默认BLB）
2. 更新Ingress添加IPv6开关注解
3. 验证IPv6 BLB自动创建
4. 验证双BLB配置同步
5. 测试IPv6网络连通性
6. 动态切换IPv6开关
7. 验证IPv6 BLB删除
8. 完整清理流程验证
*/

package ingress

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	eipsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"
	"strings"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8stypes "k8s.io/apimachinery/pkg/types"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/executor"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	CreateIPv6Ingress cases.CaseName = "CreateIPv6Ingress"

	ipv6IngressYAML = `apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/cce.ingress.blb-backup-content: ""
    kubernetes.io/cce.ingress.eip-bandwidth-in-mbps: "1"
    kubernetes.io/cce.ingress.https: "false"
    kubernetes.io/cce.ingress.internal: "false"
    kubernetes.io/cce.ingress.timeout-in-seconds: "30"
    kubernetes.io/cce.ingress.vpc-subnet-id: "[SUBNETID]"
    kubernetes.io/ingress.class: cce
  name: cce-test-ipv6-ingress
  namespace: default
spec:
  rules:
  - http:
      paths:
      - backend:
          service:
            name: service-for-ipv6-ingress
            port: 
              number: 80
        path: /
        pathType: ImplementationSpecific`

	ipv6ServiceYAML = `apiVersion: v1
kind: Service
metadata:
  name: service-for-ipv6-ingress
  namespace: default
spec:
  type: NodePort
  ipFamilyPolicy: PreferDualStack
  ipFamilies:
  - IPv4
  - IPv6
  ports:
  - port: 80
    protocol: TCP
    targetPort: 80
  selector:
    app: nginx`

	ipv6EnableAnnotation = "kubernetes.io/cce.ingress.enable-ipv6"
)

func init() {
	cases.AddCase(context.TODO(), CreateIPv6Ingress, NewCreateIPv6Ingress)
}

type createIPv6Ingress struct {
	base        *cases.BaseClient
	shell       executor.Executor
	ingressName string
	config      ingressConfig
}

func NewCreateIPv6Ingress(ctx context.Context) cases.Interface {
	return &createIPv6Ingress{
		shell:       executor.NewExecutor(shellPath),
		ingressName: "cce-test-ipv6-ingress",
	}
}

func (c *createIPv6Ingress) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base

	var cfg ingressConfig
	if err := json.Unmarshal(config, &cfg); err != nil {
		return fmt.Errorf("json.Unmarshal ipv6 ingress config failed: %s", err)
	}
	c.config = cfg

	return nil
}

func (c *createIPv6Ingress) Name() cases.CaseName {
	return CreateIPv6Ingress
}

func (c *createIPv6Ingress) Desc() string {
	return "测试IPv6 Ingress双BLB功能"
}

func (c *createIPv6Ingress) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)

	// 0. 执行前强制清理同名资源
	logger.Infof(ctx, "执行前清理同名资源...")
	if err := c.forceCleanupResources(ctx); err != nil {
		logger.Warnf(ctx, "执行前清理资源失败: %v", err)
	}

	// 等待资源完全删除
	if err := c.waitForResourcesDeleted(ctx); err != nil {
		return nil, fmt.Errorf("等待资源清理失败: %v", err)
	}

	// 1. 部署后端服务
	if err := c.deployBackendService(ctx); err != nil {
		return nil, fmt.Errorf("部署后端服务失败: %v", err)
	}

	// 2. 创建标准Ingress
	logger.Infof(ctx, "创建标准Ingress...")
	if err := c.createStandardIngress(ctx); err != nil {
		return nil, fmt.Errorf("创建标准Ingress失败: %v", err)
	}

	// 3. 验证默认BLB创建
	defaultBLBID, defaultEIP, err := c.waitForDefaultBLB(ctx)
	if err != nil {
		return nil, fmt.Errorf("等待默认BLB创建失败: %v", err)
	}
	logger.Infof(ctx, "默认BLB创建成功: %s, EIP: %s", defaultBLBID, defaultEIP)

	// 4. 启用IPv6功能
	logger.Infof(ctx, "启用IPv6功能...")
	if err := c.enableIPv6(ctx); err != nil {
		return nil, fmt.Errorf("启用IPv6功能失败: %v", err)
	}

	// 5. 验证IPv6 BLB创建
	ipv6BLBID, ipv6EIP, err := c.waitForIPv6BLB(ctx)
	if err != nil {
		return nil, fmt.Errorf("等待IPv6 BLB创建失败: %v", err)
	}
	logger.Infof(ctx, "IPv6 BLB创建成功: %s, EIP: %s", ipv6BLBID, ipv6EIP)

	// 6. 测试网络连通性
	if err := c.testNetworkConnectivity(ctx, defaultEIP, ipv6EIP); err != nil {
		return nil, fmt.Errorf("网络连通性测试失败: %v", err)
	}

	// 7. 删除Ingress并验证BLB和EIP清理
	logger.Infof(ctx, "删除Ingress...")
	if err := c.deleteIngressAndVerifyCleanup(ctx, defaultBLBID, defaultEIP, ipv6BLBID, ipv6EIP); err != nil {
		return nil, fmt.Errorf("删除Ingress和验证清理失败: %v", err)
	}

	return resources, nil
}

func (c *createIPv6Ingress) deployBackendService(ctx context.Context) error {
	yaml := DeployMentYAML
	// 替换为支持IPv6的nginx镜像
	yaml = strings.ReplaceAll(yaml, "registry.baidubce.com/qa-test/nginx:1.17", "registry.baidubce.com/cce/nginx-alpine-go:ipv6")

	if c.config.IsArm {
		yaml = strings.ReplaceAll(yaml, "nginx:", "nginx-arm64:")
	}
	if err := c.base.KubectlClient.Apply(ctx, types.K8S_1_13_10, c.base.KubeConfig, yaml); err != nil {
		return fmt.Errorf("部署deployment失败: %v", err)
	}

	if err := c.base.KubectlClient.Apply(ctx, types.K8S_1_13_10, c.base.KubeConfig, ipv6ServiceYAML); err != nil {
		return fmt.Errorf("部署service失败: %v", err)
	}

	return nil
}

func (c *createIPv6Ingress) createStandardIngress(ctx context.Context) error {
	clusterInfo, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		return fmt.Errorf("获取集群信息失败: %v", err)
	}
	subnetID := clusterInfo.Cluster.Spec.ContainerNetworkConfig.LBServiceVPCSubnetID

	yaml := ipv6IngressYAML
	yaml = strings.ReplaceAll(yaml, "[SUBNETID]", subnetID)

	if _, err := c.base.AppClient.PostAppResource(ctx, c.base.ClusterID, c.ingressName, yaml, "default", nil); err != nil {
		return fmt.Errorf("创建Ingress失败: %v", err)
	}

	return nil
}

func (c *createIPv6Ingress) waitForDefaultBLB(ctx context.Context) (string, string, error) {
	clusterInfo, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		return "", "", fmt.Errorf("获取集群信息失败: %v", err)
	}
	k8sVersion := clusterInfo.Cluster.Spec.K8SVersion

	for i := 0; i < 20; i++ {
		time.Sleep(30 * time.Second)

		annotations, err := GetIngressAnnotation(ctx, c.base, k8sVersion, c.ingressName)
		if err != nil {
			logger.Errorf(ctx, "获取Ingress注解失败: %v", err)
			continue
		}

		blbID, hasBlb := annotations[AnnotationBLBShortID]
		eip, hasEip := annotations[AnnotationEIP]

		if hasBlb && hasEip && blbID != "" && eip != "" {
			return blbID, eip, nil
		}

		logger.Infof(ctx, "等待默认BLB创建中... (%d/20)", i+1)
	}

	return "", "", fmt.Errorf("等待默认BLB创建超时")
}

func (c *createIPv6Ingress) enableIPv6(ctx context.Context) error {
	clusterInfo, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		return fmt.Errorf("获取集群信息失败: %v", err)
	}
	k8sVersion := clusterInfo.Cluster.Spec.K8SVersion

	// 使用patch方式更新annotation
	patchData := fmt.Sprintf(`{"metadata":{"annotations":{"%s":"true"}}}`, ipv6EnableAnnotation)

	if k8sVersion >= types.K8S_1_22_5 {
		_, err = c.base.K8SClient.NetworkingV1().Ingresses("default").
			Patch(ctx, c.ingressName, k8stypes.MergePatchType, []byte(patchData), metav1.PatchOptions{})
	} else {
		_, err = c.base.K8SClient.ExtensionsV1beta1().Ingresses("default").
			Patch(ctx, c.ingressName, k8stypes.MergePatchType, []byte(patchData), metav1.PatchOptions{})
	}

	if err != nil {
		return fmt.Errorf("更新Ingress IPv6注解失败: %v", err)
	}

	return nil
}

func (c *createIPv6Ingress) waitForIPv6BLB(ctx context.Context) (string, string, error) {
	clusterInfo, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		return "", "", fmt.Errorf("获取集群信息失败: %v", err)
	}
	k8sVersion := clusterInfo.Cluster.Spec.K8SVersion

	for i := 0; i < 30; i++ {
		time.Sleep(30 * time.Second)

		annotations, err := GetIngressAnnotation(ctx, c.base, k8sVersion, c.ingressName)
		if err != nil {
			logger.Errorf(ctx, "获取Ingress注解失败: %v", err)
			continue
		}

		ipv6BlbID, hasIPv6Blb := annotations["kubernetes.io/cce.ingress.blb-ipv6-id"]
		ipv6EIP, hasIPv6Eip := annotations["kubernetes.io/cce.ingress.ipv6-eip"]

		if hasIPv6Blb && hasIPv6Eip && ipv6BlbID != "" && ipv6EIP != "" {
			return ipv6BlbID, ipv6EIP, nil
		}

		logger.Infof(ctx, "等待IPv6 BLB创建中... (%d/30)", i+1)
	}

	return "", "", fmt.Errorf("等待IPv6 BLB创建超时")
}

func (c *createIPv6Ingress) testNetworkConnectivity(ctx context.Context, defaultEIP, ipv6EIP string) error {
	// 测试IPv4连通性
	ok := false
	var cmd string
	for i := 0; i < 20; i++ {
		time.Sleep(10 * time.Second)
		cmd = fmt.Sprintf("curl http://%s/", defaultEIP)
		stdout, _, err := c.shell.ShellExec(ctx, cmd)
		if err != nil {
			logger.Errorf(ctx, "ShellExec[%s] failed: %v ", cmd, err)
			continue
		}
		if !strings.Contains(stdout, "Welcome to nginx") {
			logger.Errorf(ctx, "IPv4连通性测试失败")
			continue
		}
		ok = true
		break
	}

	if !ok {
		return fmt.Errorf("IPv4连通性测试失败")
	}
	logger.Infof(ctx, "IPv4连通性测试通过")

	// 测试IPv6连通性
	ok = false
	for i := 0; i < 20; i++ {
		time.Sleep(10 * time.Second)
		cmd = fmt.Sprintf("curl -6 http://[%s]/", ipv6EIP)
		stdout, _, err := c.shell.ShellExec(ctx, cmd)
		if err != nil {
			logger.Errorf(ctx, "ShellExec[%s] failed: %v ", cmd, err)
			continue
		}
		if !strings.Contains(stdout, "Welcome to nginx") {
			logger.Errorf(ctx, "IPv6连通性测试失败")
			continue
		}
		ok = true
		break
	}

	if !ok {
		return fmt.Errorf("IPv6连通性测试失败")
	}
	logger.Infof(ctx, "IPv6连通性测试通过")

	return nil
}

func (c *createIPv6Ingress) disableIPv6(ctx context.Context) error {
	clusterInfo, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		return fmt.Errorf("获取集群信息失败: %v", err)
	}
	k8sVersion := clusterInfo.Cluster.Spec.K8SVersion

	// 使用patch方式更新annotation
	patchData := fmt.Sprintf(`{"metadata":{"annotations":{"%s":"false"}}}`, ipv6EnableAnnotation)

	if k8sVersion >= types.K8S_1_22_5 {
		_, err = c.base.K8SClient.NetworkingV1().Ingresses("default").
			Patch(ctx, c.ingressName, k8stypes.MergePatchType, []byte(patchData), metav1.PatchOptions{})
	} else {
		_, err = c.base.K8SClient.ExtensionsV1beta1().Ingresses("default").
			Patch(ctx, c.ingressName, k8stypes.MergePatchType, []byte(patchData), metav1.PatchOptions{})
	}

	if err != nil {
		return fmt.Errorf("更新Ingress IPv6注解失败: %v", err)
	}

	return nil
}

func (c *createIPv6Ingress) cleanupResources(ctx context.Context) error {
	return c.forceCleanupResources(ctx)
}

func (c *createIPv6Ingress) forceCleanupResources(ctx context.Context) error {
	clusterInfo, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		logger.Warnf(ctx, "获取集群信息失败，跳过清理: %v", err)
		return nil
	}
	k8sVersion := clusterInfo.Cluster.Spec.K8SVersion

	// 强制删除Ingress
	if k8sVersion >= types.K8S_1_22_5 {
		err = c.base.K8SClient.NetworkingV1().Ingresses("default").Delete(ctx, c.ingressName, metav1.DeleteOptions{})
	} else {
		err = c.base.K8SClient.ExtensionsV1beta1().Ingresses("default").Delete(ctx, c.ingressName, metav1.DeleteOptions{})
	}
	if err != nil && !strings.Contains(err.Error(), "not found") {
		logger.Warnf(ctx, "强制删除Ingress失败: %v", err)
	} else {
		logger.Infof(ctx, "成功删除Ingress: %s", c.ingressName)
	}

	// 强制删除Service
	err = c.base.K8SClient.CoreV1().Services("default").Delete(ctx, "service-for-ipv6-ingress", metav1.DeleteOptions{})
	if err != nil && !strings.Contains(err.Error(), "not found") {
		logger.Warnf(ctx, "强制删除Service失败: %v", err)
	} else {
		logger.Infof(ctx, "成功删除Service: service-for-ipv6-ingress")
	}

	// 强制删除Deployment
	err = c.base.K8SClient.AppsV1().Deployments("default").Delete(ctx, "nginx-deployment", metav1.DeleteOptions{})
	if err != nil && !strings.Contains(err.Error(), "not found") {
		logger.Warnf(ctx, "强制删除Deployment失败: %v", err)
	} else {
		logger.Infof(ctx, "成功删除Deployment: nginx-deployment")
	}

	return nil
}

func (c *createIPv6Ingress) Clean(ctx context.Context) error {
	logger.Infof(ctx, "执行Clean清理...")
	if err := c.forceCleanupResources(ctx); err != nil {
		logger.Errorf(ctx, "Clean清理失败: %v", err)
		return err
	}
	// 等待资源完全删除
	time.Sleep(10 * time.Second)
	return nil
}

func (c *createIPv6Ingress) Continue(ctx context.Context) bool {
	return true
}

func (c *createIPv6Ingress) ConfigFormat() string {
	return `{
	"isArm": false,
	"BLBCertID": ""
}`
}

func (c *createIPv6Ingress) waitForResourcesDeleted(ctx context.Context) error {
	clusterInfo, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		return fmt.Errorf("获取集群信息失败: %v", err)
	}
	k8sVersion := clusterInfo.Cluster.Spec.K8SVersion

	for i := 0; i < 24; i++ { // 最多等待2分钟 (24 * 5秒)
		time.Sleep(5 * time.Second)

		allDeleted := true

		// 检查Ingress是否已删除
		if k8sVersion >= types.K8S_1_22_5 {
			_, err = c.base.K8SClient.NetworkingV1().Ingresses("default").Get(ctx, c.ingressName, metav1.GetOptions{})
		} else {
			_, err = c.base.K8SClient.ExtensionsV1beta1().Ingresses("default").Get(ctx, c.ingressName, metav1.GetOptions{})
		}
		if err == nil || !strings.Contains(err.Error(), "not found") {
			logger.Infof(ctx, "等待Ingress删除中... (%d/24)", i+1)
			allDeleted = false
		}

		// 检查Service是否已删除
		_, err = c.base.K8SClient.CoreV1().Services("default").Get(ctx, "service-for-ipv6-ingress", metav1.GetOptions{})
		if err == nil || !strings.Contains(err.Error(), "not found") {
			logger.Infof(ctx, "等待Service删除中... (%d/24)", i+1)
			allDeleted = false
		}

		// 检查Deployment是否已删除
		_, err = c.base.K8SClient.AppsV1().Deployments("default").Get(ctx, "nginx-deployment", metav1.GetOptions{})
		if err == nil || !strings.Contains(err.Error(), "not found") {
			logger.Infof(ctx, "等待Deployment删除中... (%d/24)", i+1)
			allDeleted = false
		}

		if allDeleted {
			logger.Infof(ctx, "所有资源已成功删除")
			return nil
		}
	}

	return fmt.Errorf("等待资源删除超时")
}

func (c *createIPv6Ingress) deleteIngressAndVerifyCleanup(ctx context.Context, defaultBLBID, defaultEIP, ipv6BLBID, ipv6EIP string) error {
	clusterInfo, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		return fmt.Errorf("获取集群信息失败: %v", err)
	}
	k8sVersion := clusterInfo.Cluster.Spec.K8SVersion

	// 删除Ingress
	if k8sVersion >= types.K8S_1_22_5 {
		err = c.base.K8SClient.NetworkingV1().Ingresses("default").Delete(ctx, c.ingressName, metav1.DeleteOptions{})
	} else {
		err = c.base.K8SClient.ExtensionsV1beta1().Ingresses("default").Delete(ctx, c.ingressName, metav1.DeleteOptions{})
	}
	if err != nil {
		return fmt.Errorf("删除Ingress失败: %v", err)
	}
	logger.Infof(ctx, "Ingress删除成功")

	// 验证IPv6 BLB删除
	if err := c.waitForBLBDeletion(ctx, ipv6BLBID, "IPv6 BLB"); err != nil {
		return fmt.Errorf("等待IPv6 BLB删除失败: %v", err)
	}

	// 验证默认BLB删除
	if err := c.waitForBLBDeletion(ctx, defaultBLBID, "默认BLB"); err != nil {
		return fmt.Errorf("等待默认BLB删除失败: %v", err)
	}

	// 验证IPv6 EIP删除
	if err := c.waitForEIPDeletion(ctx, ipv6EIP, "IPv6 EIP"); err != nil {
		return fmt.Errorf("等待IPv6 EIP删除失败: %v", err)
	}

	// 验证默认EIP删除
	if err := c.waitForEIPDeletion(ctx, defaultEIP, "默认EIP"); err != nil {
		return fmt.Errorf("等待默认EIP删除失败: %v", err)
	}

	logger.Infof(ctx, "所有BLB和EIP资源已成功删除")
	return nil
}

func (c *createIPv6Ingress) waitForBLBDeletion(ctx context.Context, blbID, blbType string) error {
	for i := 0; i < 20; i++ {
		time.Sleep(30 * time.Second)

		resp, err := c.base.AppBLBClient.DescribeAppLoadBalancerByID(ctx, blbID, nil)
		if err != nil && strings.Contains(err.Error(), "InstanceNotFound") {
			logger.Infof(ctx, "%s已成功删除: %s", blbType, blbID)
			return nil
		}
		logger.Infof(ctx, "resp:%v, err: %s", resp, err)
		logger.Infof(ctx, "等待%s删除中... (%d/20)", blbType, i+1)
	}

	return fmt.Errorf("等待%s删除超时: %s", blbType, blbID)
}

func (c *createIPv6Ingress) waitForEIPDeletion(ctx context.Context, eip, eipType string) error {
	for i := 0; i < 20; i++ {
		time.Sleep(30 * time.Second)

		args := &eipsdk.GetEIPsArgs{EIP: eip}
		// 如果是IPv6 EIP，需要指定ipVersion
		if strings.Contains(eipType, "IPv6") {
			args.IPVersion = "ipv6"
		}

		eips, err := c.base.EIPClient.GetEIPs(ctx, args, nil)
		if err != nil {
			return fmt.Errorf("查询%s状态失败: %v", eipType, err)
		}

		if len(eips) == 0 {
			logger.Infof(ctx, "%s已成功删除: %s", eipType, eip)
			return nil
		}

		logger.Infof(ctx, "等待%s删除中... (%d/20)", eipType, i+1)
	}

	return fmt.Errorf("等待%s删除超时: %s", eipType, eip)
}
