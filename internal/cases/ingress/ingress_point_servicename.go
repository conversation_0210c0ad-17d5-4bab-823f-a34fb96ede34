// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2021/08/13 11:57:00, by <PERSON><PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
创建 ingress 指定ServicePort为字符串
*/

package ingress

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"k8s.io/api/extensions/v1beta1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	e2ecommon "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/executor"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/network/ingress-controller/common"
)

const (
	// PointServiceName - 创建 Ingress Case 名字
	PointServiceName cases.CaseName = "IngressPointServiceName"

	PortName                = "cce-test"
	PointServiceIngressName = "ingress-point-serviceportname"

	// cceIngressPortNameYAML - 测试用 Ingress
	cceIngressPortNameYAML = `apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: "cce"
  name: ingress-point-serviceportname
  namespace: default
spec:
  rules:
  - host: www.cce-ingress.com
    http:
      paths:
      - backend:
          serviceName: autotest-for-ingress-point-servicename
          servicePort: [PORT_NAME]
        path: /
`

	cceIngressPortNameYAMLNetworkingV1 = `apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: "cce"
  name: ingress-point-serviceportname
  namespace: default
spec:
  rules:
  - host: www.cce-ingress.com
    http:
      paths:
      - backend:
          service:
            name: autotest-for-ingress-point-servicename
            port: 
              name: [PORT_NAME]
        path: /
        pathType: ImplementationSpecific
`

	// deployMentYAML - 测试用工作负载
	deploymentYAML = `apiVersion: apps/v1
kind: Deployment
metadata:
  name: autotest-for-ingress-point-servicename
  labels:
    app: autotest-for-ingress-point-servicename
spec:
  replicas: 2
  selector:
    matchLabels:
      app: autotest-for-ingress-point-servicename
  template:
    metadata:
      labels:
        app: autotest-for-ingress-point-servicename
    spec:
      restartPolicy: Always
      containers:
        - name: nginx
          image: registry.baidubce.com/qa-test/nginx:1.17
          imagePullPolicy: Always
          ports:
            - containerPort: 80
`

	SvcPortNameYaml = `
apiVersion: v1
kind: Service
metadata:
  name: autotest-for-ingress-point-servicename
  namespace: default
spec:
  selector:
    app: autotest-for-ingress-point-servicename
  type: NodePort
  sessionAffinity: None
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
    name: [PORT_NAME]
`
)

func init() {
	cases.AddCase(context.TODO(), PointServiceName, NewIngressPointServiceName)
}

type ingressPointServiceName struct {
	base       *cases.BaseClient
	K8SVersion ccetypes.K8SVersion

	shell       executor.Executor
	ingressName string
	ingress     *v1beta1.Ingress
	config      ingressConfig
}

// NewIngressPointServiceName - 测试案例
func NewIngressPointServiceName(ctx context.Context) cases.Interface {
	return &ingressPointServiceName{
		shell: executor.NewExecutor(shellPath),
	}
}

func (c *ingressPointServiceName) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base

	// get cluster info
	response, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "ccev2.GetCluster failed: %s", err)
		return fmt.Errorf("ccev2.GetCluster failed:  %s", err)
	}

	if response == nil {
		return fmt.Errorf("GetCluster %s return nil", c.base.ClusterID)
	}

	c.K8SVersion = response.Cluster.Spec.K8SVersion

	return nil
}

func (c *ingressPointServiceName) Name() cases.CaseName {
	return PointServiceName
}

func (c *ingressPointServiceName) Desc() string {
	return "[待补充]"
}

func (c *ingressPointServiceName) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)

	// 部署 cce-ingress-controller
	ingressControllerDp, err := c.base.K8SClient.AppsV1().Deployments(IngressControllerNamespace).Get(ctx, IngressControllerDeploymentName, metav1.GetOptions{})
	if err != nil || ingressControllerDp == nil {
		if err := c.base.AppClient.DeployIngressController(ctx, c.base.ClusterID, nil); err != nil {
			logger.Warnf(ctx, "Deploy cce-ingress-controller failed: %s.  But still continue ", err)
		} else {
			logger.Infof(ctx, "Deployed cce-ingress-controlled")
		}
	} else {
		logger.Infof(ctx, "Found cce-ingress-controlled, skip deploy")
	}

	// 等待 ingress pod running
	// TODO: network-inspector 在 CoreDNS 前启动, 有概率无法解析 service 域名, 待排查
	if err := e2ecommon.WaitPodsRunning(ctx, c.base.K8SClient, "kube-system", map[string]string{
		"app": "cce-ingress-controller",
	}); err != nil {
		logger.Errorf(ctx, "WaitPodsRunning failed: %s", err)
		return nil, err
	}

	// 部署 Deployment
	yaml := deploymentYAML
	if c.config.IsArm {
		yaml = strings.ReplaceAll(DeployMentYAML, "nginx:", "nginx-arm64:")
	}
	if err := c.base.KubectlClient.Apply(ctx, ccetypes.K8S_1_13_10, c.base.KubeConfig, yaml); err != nil {
		logger.Errorf(ctx, "Deploy deployment failed: %s", err)
		return nil, err
	}

	// 部署 Service
	strYml := strings.ReplaceAll(SvcPortNameYaml, "[PORT_NAME]", PortName)
	if err := c.base.KubectlClient.Apply(ctx, ccetypes.K8S_1_13_10, c.base.KubeConfig, strYml); err != nil {
		logger.Errorf(ctx, "Deploy service failed: %s", err)
		return nil, err
	}

	// 部署 Ingress
	// TODO: 暂时使用 1.13 版本 kubectl

	// "extensions/v1beta1"在1.16版本中被弃用。从Kubernetes 1.22版本开始，"extensions/v1beta1" API版本不再可用。
	// "networking.k8s.io/v1" API版本在Kubernetes 1.19版本中引入。
	// 如果集群保本 >= 1.20.8, apiVersion使用networking.k8s.io/v1
	isK8SVersionAfterOrEqual1_20_8, _ := c.K8SVersion.IsAfterOrEqual(ccetypes.K8S_1_20_8)
	if isK8SVersionAfterOrEqual1_20_8 {
		strYml = strings.ReplaceAll(cceIngressPortNameYAMLNetworkingV1, "[PORT_NAME]", PortName)
		logger.Infof(ctx, "Update yaml to\n %s", strYml)
	} else {
		strYml = strings.ReplaceAll(cceIngressPortNameYAML, "[PORT_NAME]", PortName)
	}
	if err := c.base.KubectlClient.Apply(ctx, ccetypes.K8S_1_13_10, c.base.KubeConfig, strYml); err != nil {
		logger.Errorf(ctx, "Deploy ingress failed: %s", err)
		return nil, err
	}

	// 统计 BLB/EIP 资源
	var ok bool
	var eip string
	var blbID string
	var annotations map[string]string
	for i := 0; i < 20; i++ {
		time.Sleep(30 * time.Second)

		// 检查集群中 Ingress 是否存在
		var ingress any
		if isK8SVersionAfterOrEqual1_20_8 {
			ingress, err := c.base.K8SClient.NetworkingV1().Ingresses("default").Get(ctx, PointServiceIngressName, metav1.GetOptions{})
			if err != nil {
				logger.Errorf(ctx, "Get Ingress default/cce-test-ingress failed: %s", err)
				return nil, err
			}
			annotations = ingress.GetAnnotations()
		} else {
			ingress, err := c.base.K8SClient.ExtensionsV1beta1().Ingresses("default").Get(ctx, PointServiceIngressName, metav1.GetOptions{})
			if err != nil {
				logger.Errorf(ctx, "Get Ingress default/cce-test-ingress failed: %s", err)
				return nil, err
			}
			annotations = ingress.GetAnnotations()
		}
		logger.Infof(ctx, "Get ingress success: %s", utils.ToJSON(ingress))

		// 检查是否有 LB
		blbID, ok = annotations[common.AnnotationBLBShortID]
		if !ok {
			logger.Infof(ctx, "%s is empty, retry", common.AnnotationBLBShortID)
			continue
		}

		logger.Infof(ctx, "BLB ID: %s", blbID)

		// 检查是否有 EIP
		eip, ok = annotations[common.AnnotationEIP]
		if !ok {
			logger.Infof(ctx, "%s is empty, retry", common.AnnotationEIP)
			continue
		}

		logger.Infof(ctx, "EIP: %s", eip)

		break
	}

	// 统计 BLB/EIP 资源
	if blbID != "" {
		resources = append(resources, cases.Resource{
			CaseName: PointServiceName,
			Type:     cases.ResourceTypeBLB,
			ID:       blbID,
		})
	}

	if eip != "" {
		resources = append(resources, cases.Resource{
			CaseName: PointServiceName,
			Type:     cases.ResourceTypeEIP,
			ID:       eip,
		})
	}

	// 结果统计
	if blbID == "" || eip == "" {
		return resources, fmt.Errorf("create ingress failed: blbID=[%s] eip=[%s]", blbID, eip)
	}

	var blbStatus e2ecommon.AppBLBStatus
	if err = blbStatus.NewAppBLBStatus(ctx, c.base, blbID, "available"); err != nil {
		return resources, err
	}
	if err = e2ecommon.WaitForResourceReady(ctx, &blbStatus); err != nil {
		return resources, err
	}

	var eipStatus e2ecommon.EIPStatus
	if err = eipStatus.NewEIPStatus(ctx, c.base, eip, "binded"); err != nil {
		return resources, err
	}
	if err = e2ecommon.WaitForResourceReady(ctx, &eipStatus); err != nil {
		return resources, err
	}

	// 4、校验hostname连通性
	ok = false
	var cmd string
	for i := 0; i < 20; i++ {
		time.Sleep(10 * time.Second)
		cmd = fmt.Sprintf("curl http://%s/ -H 'Host: www.cce-ingress.com'", eip)
		logger.Infof(ctx, "ShellExec[%s]", cmd)
		stdout, _, err := c.shell.ShellExec(ctx, cmd)
		if err != nil {
			logger.Errorf(ctx, "ShellExec[%s] failed: %v ", cmd, err)
			continue
		}
		if !strings.Contains(stdout, "Welcome to nginx") {
			logger.Errorf(ctx, "invite nginx service failed")
			err = fmt.Errorf(fmt.Sprintf("invite nginx service failed, stdout: %s", stdout))
			continue
		}
		ok = true
		break
	}

	if !ok {
		return resources, fmt.Errorf("ShellExec[%s] failed: %v ", cmd, err)
	}

	return resources, nil
}

func (c *ingressPointServiceName) Clean(ctx context.Context) error {
	var err error
	// 删除 Ingress
	if c.K8SVersion >= ccetypes.K8S_1_20_8 {
		err = c.base.K8SClient.NetworkingV1().Ingresses("default").Delete(ctx, PointServiceIngressName, metav1.DeleteOptions{})
	} else {
		err = c.base.K8SClient.ExtensionsV1beta1().Ingresses("default").Delete(ctx, PointServiceIngressName, metav1.DeleteOptions{})
	}
	if err != nil {
		logger.Errorf(ctx, "delete ingress/%s failed: %v", PointServiceIngressName, err.Error())
		return err
	}

	// 删除Service
	err = c.base.K8SClient.CoreV1().Services("default").Delete(ctx, "autotest-for-ingress-point-servicename", metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "delete service/autotest-for-ingress-point-servicename failed: %v", err.Error())
		return err
	}

	// 删除Deployment
	err = c.base.K8SClient.AppsV1().Deployments("default").Delete(ctx, "autotest-for-ingress-point-servicename", metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "delete deployment/autotest-for-ingress-point-servicename failed: %v", err.Error())
		return err
	}

	return nil
}

func (c *ingressPointServiceName) Continue(ctx context.Context) bool {
	return true
}

func (c *ingressPointServiceName) ConfigFormat() string {
	return ""
}
