package plugin

import (
	"context"
	"fmt"
	"strings"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
)

func CheckDaemonSetExist(ctx context.Context, k8sClient kubernetes.Interface, label string, namespace string) error {
	daemonsetList, err := k8sClient.AppsV1().DaemonSets(namespace).List(ctx, metav1.ListOptions{
		LabelSelector: label,
	})
	if err != nil {
		msg := fmt.Sprintf("fail to get daemonset list with label %s: %v", label, err)
		return fmt.Errorf(msg)
	}
	if daemonsetList == nil {
		msg := fmt.Sprintf("fail to get daemonset list with label %s: result is nil", label)
		return fmt.Errorf(msg)
	}
	if len(daemonsetList.Items) == 0 {
		msg := fmt.Sprintf("no daemonset with label %s found", label)
		return fmt.Errorf(msg)
	}

	return nil
}

func CheckDeploymentExist(ctx context.Context, k8sClient kubernetes.Interface, label string, namespace string) error {
	deploymentList, err := k8sClient.AppsV1().Deployments(namespace).List(ctx, metav1.ListOptions{
		LabelSelector: label,
	})
	if err != nil {
		msg := fmt.Sprintf("fail to get deployment list with label %s: %v", label, err)
		return fmt.Errorf(msg)
	}
	if deploymentList == nil {
		msg := fmt.Sprintf("fail to get deployment list with label %s: result is nil", label)
		return fmt.Errorf(msg)
	}
	if len(deploymentList.Items) == 0 {
		msg := fmt.Sprintf("no deployment with label %s found", label)
		return fmt.Errorf(msg)
	}

	return nil
}

func CheckPodRunning(ctx context.Context, k8sClient kubernetes.Interface, label string, namespace string) error {
	podList, err := k8sClient.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{
		LabelSelector: label,
	})
	if err != nil {
		msg := fmt.Sprintf("fail to get pod list with label %s: %v", label, err)
		return fmt.Errorf(msg)
	}
	if podList == nil {
		msg := fmt.Sprintf("fail to get pod list with label %s: result is nil", label)
		return fmt.Errorf(msg)
	}

	for _, pod := range podList.Items {
		if pod.Status.Phase != corev1.PodRunning {
			msg := fmt.Sprintf("pod %s not running", pod.Name)
			//  排除组件是由于亲和性导致的未running
			skip := false
			for _, condition := range pod.Status.Conditions {
				if condition.Reason == "Unschedulable" && strings.Contains(condition.Message, "anti-affinity") {
					skip = true
					break
				}
			}
			if skip {
				continue
			}
			return fmt.Errorf(msg)
		}
		for _, cs := range pod.Status.ContainerStatuses {
			if !cs.Ready {
				return fmt.Errorf("pod %s container %s not ready", pod.Name, cs.Name)
			}
		}
	}

	return nil
}

func CheckNetworkDevice(ctx context.Context, client *kube.Client) error {
	podList, listPodErr := client.ListPod(ctx, NameSpace, &kube.ListOptions{
		LabelSelector: map[string]string{
			"app.cce.baidubce.com": "cce-network-agent",
		},
	})

	if listPodErr != nil {
		return fmt.Errorf("list pod failed: %v", listPodErr)
	}

	execPodCmd := []string{
		"sh",
		"-c",
		"ifconfig lo",
	}

	unExpectedNode := make([]string, 0)
	for _, pod := range podList.Items {
		result, execErr := client.RemoteExec(NameSpace, pod.Name, "", execPodCmd)
		if execErr != nil {
			return fmt.Errorf("remote exec pod %s failed: %v", pod.Name, execErr)
		}
		if strings.Contains(result, "Device not found") {
			unExpectedNode = append(unExpectedNode, pod.Spec.NodeName)
		}
		fmt.Printf("%v", result)
	}

	if len(unExpectedNode) != 0 {
		return fmt.Errorf("node %v lo device is not found", unExpectedNode)
	}

	return nil
}
