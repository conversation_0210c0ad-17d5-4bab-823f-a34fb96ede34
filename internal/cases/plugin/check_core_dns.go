package plugin

import (
	"context"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/addon"
)

const (
	PluginCheckCoreDNS                cases.CaseName = "Plugin_Check_Core_DNS"
	PluginCheckCoreDNSPodLabel                       = "k8s-app=kube-dns"
	PluginCheckCoreDNSDeploymentLabel                = "k8s-app=kube-dns"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), PluginCheckCoreDNS, NewPluginCheckCoreDNS)
}

var _ cases.Interface = &pluginCheckCoreDNS{}

type pluginCheckCoreDNS struct {
	base         *cases.BaseClient
	addOnFactory addon.FactoryInterface
}

func NewPluginCheckCoreDNS(ctx context.Context) cases.Interface {
	return &pluginCheckCoreDNS{}
}

func (c *pluginCheckCoreDNS) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base
	c.addOnFactory = &addon.Factory{}

	return nil
}

func (c *pluginCheckCoreDNS) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	var errCheckProcess error

	for i := 0; i < 60; i++ {
		resources, errCheckProcess = c.checkProcess(ctx, resources)
		if errCheckProcess == nil {
			break
		}
		logger.Warnf(ctx, fmt.Sprintf("retry count %d error %s", i, errCheckProcess))
		time.Sleep(5 * time.Second)
	}

	return resources, errCheckProcess
}

func (c *pluginCheckCoreDNS) checkProcess(ctx context.Context, resources []cases.Resource) ([]cases.Resource, error) {
	pluginReleaseName := string(addon.AddonCoreDNS)

	// 检查 Helm Release 是否 Ready
	if c.base.ClusterSpec.K8SCustomConfig.EnableDefaultPluginDeployByHelm {
		status, err := c.base.GetAddonStatus(ctx, c.base.ClusterID, pluginReleaseName)
		if err != nil {
			msg := fmt.Sprintf("failed to get plugin status %s: %s", pluginReleaseName, err)
			logger.Errorf(ctx, msg)
			return resources, fmt.Errorf(msg)
		}
		if status != ccev2.InstancePhaseDeployed {
			msg := fmt.Sprintf("PluginStatus %s status %s not %s", pluginReleaseName, status, ccev2.InstancePhaseDeployed)
			logger.Errorf(ctx, msg)
			return resources, fmt.Errorf(msg)
		}
	}

	// 检查相关 DaemonSet 或 Deployment 是否存在
	errDeploymentErr := CheckDeploymentExist(ctx, c.base.K8SClient, PluginCheckCoreDNSDeploymentLabel, "kube-system")
	if errDeploymentErr != nil {
		msg := fmt.Sprintf("plugin %s deployment not exist: %v", pluginReleaseName, errDeploymentErr)
		return resources, fmt.Errorf(msg)
	}

	// 检查相关 Pod 是否正常运行
	errPodRunning := CheckPodRunning(ctx, c.base.K8SClient, PluginCheckCoreDNSPodLabel, "kube-system")
	if errPodRunning != nil {
		msg := fmt.Sprintf("plugin %s pod not running: %v", pluginReleaseName, errPodRunning)
		return resources, fmt.Errorf(msg)
	}

	return resources, nil
}

func (c *pluginCheckCoreDNS) Name() cases.CaseName {
	return PluginCheckCoreDNS
}

func (c *pluginCheckCoreDNS) Desc() string {
	return "[待补充]"
}

func (c *pluginCheckCoreDNS) Clean(ctx context.Context) error {
	return nil
}

func (c *pluginCheckCoreDNS) Continue(ctx context.Context) bool {
	return true
}

func (c *pluginCheckCoreDNS) ConfigFormat() string {
	return ""
}
