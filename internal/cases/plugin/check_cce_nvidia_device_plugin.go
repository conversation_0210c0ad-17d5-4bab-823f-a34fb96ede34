package plugin

import (
	"context"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/addon"
)

const (
	PluginCheckNvidiaDevicePlugin         cases.CaseName = "Plugin_Check_Nvidia_Device_Plugin"
	PluginCheckNvidiaDevicePluginPodLabel                = "name=nvidia-device-plugin-ds"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), PluginCheckNvidiaDevicePlugin, NewPluginCheckNvidiaDevicePlugin)
}

var _ cases.Interface = &pluginCheckNvidiaDevicePlugin{}

type pluginCheckNvidiaDevicePlugin struct {
	base *cases.BaseClient
}

func NewPluginCheckNvidiaDevicePlugin(ctx context.Context) cases.Interface {
	return &pluginCheckNvidiaDevicePlugin{}
}

func (c *pluginCheckNvidiaDevicePlugin) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base

	return nil
}

func (c *pluginCheckNvidiaDevicePlugin) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	var errCheckProcess error

	for i := 0; i < 60; i++ {
		resources, errCheckProcess = c.checkProcess(ctx, resources)
		if errCheckProcess == nil {
			break
		}
		logger.Warnf(ctx, fmt.Sprintf("retry count %d error %s", i, errCheckProcess))
		time.Sleep(5 * time.Second)
	}

	return resources, errCheckProcess
}

func (c *pluginCheckNvidiaDevicePlugin) checkProcess(ctx context.Context, resources []cases.Resource) ([]cases.Resource, error) {
	pluginReleaseName := string(addon.AddonNvidiaDevicePlugin)

	// 检查 Helm Release 是否 Ready
	if c.base.ClusterSpec.K8SCustomConfig.EnableDefaultPluginDeployByHelm {
		status, err := c.base.GetAddonStatus(ctx, c.base.ClusterID, pluginReleaseName)
		if err != nil {
			logger.Errorf(ctx, "GetAddonStatus %s failed: %s", pluginReleaseName, status)
			return resources, err
		}
		if status != ccev2.InstancePhaseDeployed {
			logger.Errorf(ctx, "PluginStatus %s status %s not %s", pluginReleaseName, status, ccev2.InstancePhaseDeployed)
			return resources, fmt.Errorf("PluginStatus %s status %s not %s", pluginReleaseName, status, ccev2.InstancePhaseDeployed)
		}

	}

	// 检查相关 Pod 是否正常运行
	errPodRunning := CheckPodRunning(ctx, c.base.K8SClient, PluginCheckNvidiaDevicePluginPodLabel, "kube-system")
	if errPodRunning != nil {
		msg := fmt.Sprintf("plugin %s pod not running: %v", pluginReleaseName, errPodRunning)
		return resources, fmt.Errorf(msg)
	}

	return resources, nil
}

func (c *pluginCheckNvidiaDevicePlugin) Name() cases.CaseName {
	return PluginCheckNvidiaDevicePlugin
}

func (c *pluginCheckNvidiaDevicePlugin) Desc() string {
	return "[待补充]"
}

func (c *pluginCheckNvidiaDevicePlugin) Clean(ctx context.Context) error {
	return nil
}

func (c *pluginCheckNvidiaDevicePlugin) Continue(ctx context.Context) bool {
	return true
}

func (c *pluginCheckNvidiaDevicePlugin) ConfigFormat() string {
	return ""
}
