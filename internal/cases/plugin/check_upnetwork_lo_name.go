package plugin

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/helm"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CheckUpnetworkLOName cases.CaseName = "CheckUpnetworkLOName"
	NameSpace            string         = "kube-system"
	NetworkPluginName    string         = "cce-network-v2"
	DaemonSetName        string         = "cce-network-agent"
	LabelGenerationKey   string         = "pod-template-generation"
)

var matchLabel = map[string]string{
	"app.cce.baidubce.com": "cce-network-agent",
}

type checkUpnetworkLOName struct {
	base *cases.BaseClient
}

func init() {
	cases.AddCase(context.TODO(), CheckUpnetworkLOName, NewCheckUpnetworkLOName)
}

func NewCheckUpnetworkLOName(ctx context.Context) cases.Interface {
	return &checkUpnetworkLOName{}
}

func (c *checkUpnetworkLOName) Name() cases.CaseName {
	return CheckUpnetworkLOName
}

func (c *checkUpnetworkLOName) Desc() string {
	return "校验网络组件升级LO网卡名"
}

func (c *checkUpnetworkLOName) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	return
}

func (c *checkUpnetworkLOName) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID

	// 获取组件历史版本
	chartDetail, chartDetailErr := c.base.HelmClient.GetPublicRepoChartDetail(ctx, NetworkPluginName)
	if chartDetailErr != nil {
		err = fmt.Errorf("get %s chart detail failed: %v", NetworkPluginName, chartDetailErr)
		return
	}
	if len(chartDetail.History) < 2 {
		err = errors.New("history is lower than 2, can not support plugin update test")
		return
	}

	// 最新版本与上一版本
	latestVersion := chartDetail.History[0].Version
	oldestVersion := chartDetail.History[1].Version

	// 集群建好之后，检查node运行，
	waitErr := resource.WaitForFunc(ctx, func(ctx context.Context) (err error) {
		listNode, listErr := c.base.KubeClient.ListNode(ctx, &kube.ListOptions{})

		if listErr != nil {
			return fmt.Errorf("list node failed: %v", listErr)
		}

		for _, node := range listNode.Items {
			for _, condition := range node.Status.Conditions {
				if condition.Type == corev1.NodeReady {
					if condition.Status != corev1.ConditionTrue {
						return fmt.Errorf("node %s is not ready", node.Name)
					}
				}
			}
		}

		logger.Infof(ctx, "nodes are all running")
		return nil
	}, 5*time.Second, 10*time.Minute)

	if waitErr != nil {
		return nil, fmt.Errorf(" wait nodes running failed: %v", waitErr)
	}

	// 获取当前版本  ifconfig lo 信息
	// 检查pod运行
	waitErr = resource.WaitPodsRunning(ctx, c.base.K8SClient, NameSpace, matchLabel)
	if waitErr != nil {
		return nil, fmt.Errorf("%v", waitErr)
	}

	loErr := CheckNetworkDevice(ctx, c.base.KubeClient)
	if loErr != nil {
		err = fmt.Errorf("check network device failed: %v", loErr)
		return
	}

	//比较当前版本号与最新的版本号
	isSame, getErr := c.comparePluginVersion(ctx, clusterID, latestVersion)
	if getErr != nil {
		err = fmt.Errorf("compare pluginVersion failed: %v", getErr)
	}

	// 如果不是最新版本则升级
	if !isSame {
		logger.Infof(ctx, "current network plugin is not the latest version, start upgrade to latest version %v! ",
			latestVersion)

		checkErr := c.checkNetworkPluginUpgrade(ctx, clusterID, latestVersion)
		if checkErr != nil {
			err = fmt.Errorf("check network plugin upgrade failed: %v", checkErr)
			return
		}

		waitErr = c.checkPodRunningAndGeneration(ctx)
		if waitErr != nil {
			return nil, fmt.Errorf("%v", waitErr)
		}

		loErr = CheckNetworkDevice(ctx, c.base.KubeClient)
		if loErr != nil {
			err = fmt.Errorf("check network device failed: %v", loErr)
			return
		}

		logger.Infof(ctx, "lo name unchanged!")
		return
	}

	// // 最新版本，则返回上一个版本
	logger.Infof(ctx, " current network plugin is the latest version %v, upgrade to oldestVersion :%v ! ",
		latestVersion, oldestVersion)

	checkErr := c.checkNetworkPluginUpgrade(ctx, clusterID, oldestVersion)

	if checkErr != nil {
		err = fmt.Errorf("check network plugin upgrade failed: %v", checkErr)
		return
	}

	waitErr = c.checkPodRunningAndGeneration(ctx)
	if waitErr != nil {
		return nil, fmt.Errorf("%v", waitErr)
	}

	loErr = CheckNetworkDevice(ctx, c.base.KubeClient)
	if loErr != nil {
		err = fmt.Errorf("check network device failed: %v", loErr)
		return
	}

	logger.Infof(ctx, "lo name unchanged!")

	// 再返回最新的版本
	logger.Infof(ctx, "upgrade to latestVersion! ")
	checkErr = c.checkNetworkPluginUpgrade(ctx, clusterID, latestVersion)

	if checkErr != nil {
		err = fmt.Errorf("check network plugin upgrade failed: %v", checkErr)
		return
	}

	waitErr = c.checkPodRunningAndGeneration(ctx)
	if waitErr != nil {
		return nil, fmt.Errorf("%v", waitErr)
	}

	loErr = CheckNetworkDevice(ctx, c.base.KubeClient)
	if loErr != nil {
		err = fmt.Errorf("check network device failed: %v", loErr)
		return
	}

	logger.Infof(ctx, "lo name unchanged!")
	return
}

func (c *checkUpnetworkLOName) Clean(ctx context.Context) error {
	return nil
}

func (c *checkUpnetworkLOName) Continue(ctx context.Context) bool {
	return true
}

func (c *checkUpnetworkLOName) ConfigFormat() string {
	return ""
}

func (c *checkUpnetworkLOName) checkNetworkPluginUpgrade(ctx context.Context, clusterID string, version string) error {
	res, err := c.base.HelmClient.GetReleaseDetail(ctx, clusterID, NameSpace, NetworkPluginName)
	if err != nil {
		return err
	}

	upRes := helm.ReleaseUpgradeRequest{
		ChartName:    NetworkPluginName,
		ChartVersion: version,
		Description:  res.Description,
		Values:       res.Values,
		IsPublic:     true,
	}

	err = c.base.HelmClient.UpgradeRelease(ctx, clusterID, NameSpace, NetworkPluginName, &upRes)
	if err != nil {
		return err
	}

	isSame, err := c.comparePluginVersion(ctx, clusterID, version)
	if err != nil {
		return err
	}

	if !isSame {
		err = fmt.Errorf(" upgrade version is not version %s", version)
		return err
	}

	return nil
}

func (c *checkUpnetworkLOName) checkPodRunningAndGeneration(ctx context.Context) (err error) {
	dsGeneration, err := c.getDsGeneration(ctx)
	if err != nil {
		return err
	}

	var labelErr error

	waitErr := resource.WaitForFunc(ctx, func(ctx context.Context) error {
		podList, listPodErr := c.base.KubeClient.ListPod(ctx, NameSpace, &kube.ListOptions{
			LabelSelector: matchLabel,
		})

		if listPodErr != nil {
			return listPodErr
		}

		for _, pod := range podList.Items {
			if _, ok := pod.Labels[LabelGenerationKey]; !ok {
				labelErr = fmt.Errorf("pod %s has no label %s", pod.Name, LabelGenerationKey)
				return nil
			}
			if pod.Labels[LabelGenerationKey] != dsGeneration {
				return fmt.Errorf("%s pod label%s generation %s is different with dsGeneration %s", pod.Name,
					LabelGenerationKey, pod.Labels[LabelGenerationKey], dsGeneration)
			}
			if pod.Status.Phase != corev1.PodRunning {
				return fmt.Errorf("pod %s is not running", pod.Name)
			}
			for _, cs := range pod.Status.ContainerStatuses {
				if !cs.Ready {
					return fmt.Errorf("pod %s container %s not ready", pod.Name, cs.Name)
				}
			}
		}

		logger.Infof(ctx, "pod are all running and generations are same with dsGeneration")
		return nil
	}, 5*time.Second, 5*time.Minute)

	if waitErr != nil {
		return fmt.Errorf("check pod running and generation failed: %v", waitErr)
	}
	if labelErr != nil {
		return fmt.Errorf("check pod label failed: %v", labelErr)
	}

	return nil
}

func (c *checkUpnetworkLOName) getDsGeneration(ctx context.Context) (string, error) {
	ds, getDsErr := c.base.K8SClient.AppsV1().DaemonSets(NameSpace).Get(ctx, DaemonSetName, metav1.GetOptions{})
	if getDsErr != nil {
		return "", fmt.Errorf("get DaemonSet cce-network-agent failed: %v", getDsErr)
	}
	return strconv.FormatInt(ds.Generation, 10), nil
}

func (c *checkUpnetworkLOName) comparePluginVersion(ctx context.Context, clusterID string, compareVersion string) (
	isSame bool, err error) {
	pluginInfo, listErr := c.base.CCEClient.ListAddOns(ctx, clusterID, &ccev2.ListParams{
		TargetAddons: NetworkPluginName,
	}, nil)

	if listErr != nil {
		err = fmt.Errorf("list addon %s failed: %v", NetworkPluginName, listErr)
		return false, err
	}
	if len(pluginInfo.Items) == 0 {
		err = errors.New("get addon list is empty")
		return false, err
	}

	if pluginInfo.Items[0].Instance.InstalledVersion != compareVersion {
		return false, nil
	}

	return true, nil
}
