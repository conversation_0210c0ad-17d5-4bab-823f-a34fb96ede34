// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/11/23 , by <EMAIL>, create
*/
/*
部署 K8S 插件测试
*/

package plugin

import (
	"context"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	// CheckPluginsExist - 部署插件
	CheckPluginsExist cases.CaseName = "CheckPluginsExist"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CheckPluginsExist, NewCheckPluginsExist)
}

var _ cases.Interface = &checkPluginsExist{}

type checkPluginsExist struct {
	base      *cases.BaseClient
	clusterID string
}

// NewCheckPluginsExist - 部署插件
func NewCheckPluginsExist(ctx context.Context) cases.Interface {
	return &checkPluginsExist{}
}

func (c *checkPluginsExist) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base

	return nil
}

func (c *checkPluginsExist) Name() cases.CaseName {
	return CheckPluginsExist
}

func (c *checkPluginsExist) Desc() string {
	return "部署 K8S 插件测试"
}

func (c *checkPluginsExist) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)

	// 获取集群部署 PluginList
	pluginList := c.base.ClusterSpec.Plugins

	logger.Infof(ctx, "Target Plugins: %v", pluginList)

	// 检查 PluginList 是否 Ready
	errMsg := ""
	for _, pluginName := range pluginList {
		// TODO: plugin通过addon接口进行校验
		if pluginName == "cce-network-v2" || pluginName == "cce-coredns" {
			continue
		}
		// 托管集群不校验lb-controller
		masterType := c.base.ClusterSpec.MasterConfig.MasterType
		if (masterType == ccetypes.MasterTypeManaged || masterType == ccetypes.MasterTypeManagedPro) &&
			pluginName == "cce-lb-controller" {
			continue
		}

		status, err := c.base.PluginClient.PluginStatus(ctx, pluginName)
		if err != nil {
			logger.Errorf(ctx, "PluginStatus %s failed, status: %s, err: %v", pluginName, status, err.Error())
			errMsg += fmt.Sprintf("PluginStatus %s failed, status: %s, err: %v\n", pluginName, status, err)
			continue
		}

		if status != plugin.PluginExist {
			logger.Errorf(ctx, "PluginStatus %s status %s not %s", pluginName, status, plugin.PluginExist)
			errMsg += fmt.Sprintf("PluginStatus %s status %s not %s\n", pluginName, status, plugin.PluginExist)
			continue
		}
	}

	if errMsg != "" {
		return resources, fmt.Errorf(errMsg)
	}

	return resources, nil
}

func (c *checkPluginsExist) Clean(ctx context.Context) error {
	return nil
}

func (c *checkPluginsExist) Continue(ctx context.Context) bool {
	return true
}

func (c *checkPluginsExist) ConfigFormat() string {
	return ""
}
