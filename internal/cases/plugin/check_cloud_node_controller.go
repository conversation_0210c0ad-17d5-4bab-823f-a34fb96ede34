// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2021/06/17 , by <PERSON><PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
check cloud node controller param,检查参数
*/

package plugin

import (
	"context"
	"errors"
	"fmt"
	"strings"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CheckCNCParam    cases.CaseName = "CheckCNCParam"
	DefaultNameSpace string         = "kube-system"
	CNCPodName       string         = "cce-cloud-node-controller"

	// labels
	InstanceType = "beta.kubernetes.io/instance-type"
	InstanceGpu  = "beta.kubernetes.io/instance-gpu"
	Region       = "failure-domain.beta.kubernetes.io/region"
	Zone         = "failure-domain.beta.kubernetes.io/zone"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CheckCNCParam, NewCheckCNCParam)
}

var _ cases.Interface = &checkCNCParam{}

type checkCNCParam struct {
	base      *cases.BaseClient
	clusterID string
}

// NewTemplate - 测试案例
func NewCheckCNCParam(ctx context.Context) cases.Interface {
	return &checkCNCParam{}
}

func (c *checkCNCParam) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base

	return nil
}

func (c *checkCNCParam) Name() cases.CaseName {
	return CheckCNCParam
}

func (c *checkCNCParam) Desc() string {
	return "cloud node controller 参数检查"
}

func (c *checkCNCParam) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	// 检查参数： 查看pod，是否部署cloud-node-controller：1.13.10未部署，1.16.8/1.16.9部署
	// 获取集群版本
	version, err := c.base.K8SClient.Discovery().ServerVersion()
	if err != nil {
		logger.Errorf(ctx, "k8sClient.ServerVersion failed: %s", err)
		return nil, err
	}
	clusterVersion := strings.Trim(version.GitVersion, "v")
	logger.Infof(ctx, "cluster version: %s", clusterVersion)

	if clusterVersion != "1.13.10" {
		if err := resource.WaitPodsRunning(ctx, c.base.K8SClient, DefaultNameSpace, map[string]string{
			"k8s-app": CNCPodName,
		}); err != nil {
			logger.Errorf(ctx, "WaitPodsRunning failed: %s", err)
			return nil, err
		}
	}
	// 检查labels,providerID,去掉Cloud taint
	nodes, err := c.base.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "get node list failed:%v", err)
		return nil, err
	}
	if nodes == nil {
		logger.Errorf(ctx, "cluster have 0 node")
		return nil, errors.New("cluster have 0 node")
	}

	for _, node := range nodes.Items {
		exited, err := c.checkLabel(ctx, node, clusterVersion)
		if err != nil {
			logger.Errorf(ctx, "node %v check label failed", node)
			return nil, err
		}
		if !exited {
			logger.Errorf(ctx, "node %v does not have key", node)
			return nil, err
		}
	}

	return resources, nil
}

func (c *checkCNCParam) checkLabel(ctx context.Context, node v1.Node, version string) (bool, error) {
	labels := []string{InstanceType, InstanceGpu, Region, Zone}

	// 1.13版本不一定会有 gpu label，去掉该label校验
	if version == "1.13.10" {
		labels = []string{InstanceType, Region, Zone}
	}

	for _, label := range labels {
		_, ok := node.Labels[label]
		if !ok {
			logger.Errorf(ctx, "node %v does not have key: %v", node, label)
			return false, fmt.Errorf("node %v does not have key: %v", node, label)
		}
	}
	return true, nil
}

func (c *checkCNCParam) Clean(ctx context.Context) error {
	return nil
}

func (c *checkCNCParam) Continue(ctx context.Context) bool {
	return true
}

func (c *checkCNCParam) ConfigFormat() string {
	return ""
}
