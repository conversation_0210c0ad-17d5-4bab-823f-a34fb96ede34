package plugin

import (
	"context"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/addon"
)

const (
	PluginCheckMetricsServer                cases.CaseName = "Plugin_Check_Metrics_Server"
	PluginCheckMetricsServerPodLabel                       = "k8s-app=metrics-server"
	PluginCheckMetricsServerDeploymentLabel                = "k8s-app=metrics-server"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), PluginCheckMetricsServer, NewPluginCheckMetricsServer)
}

var _ cases.Interface = &pluginCheckMetricsServer{}

type pluginCheckMetricsServer struct {
	base *cases.BaseClient
}

func NewPluginCheckMetricsServer(ctx context.Context) cases.Interface {
	return &pluginCheckMetricsServer{}
}

func (c *pluginCheckMetricsServer) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base

	return nil
}

func (c *pluginCheckMetricsServer) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	var errCheckProcess error

	for i := 0; i < 60; i++ {
		resources, errCheckProcess = c.checkProcess(ctx, resources)
		if errCheckProcess == nil {
			break
		}
		logger.Warnf(ctx, fmt.Sprintf("retry count %d error %s", i, errCheckProcess))
		time.Sleep(5 * time.Second)
	}

	return resources, errCheckProcess
}

func (c *pluginCheckMetricsServer) checkProcess(ctx context.Context, resources []cases.Resource) ([]cases.Resource, error) {
	pluginReleaseName := string(addon.AddonMetricsServer)

	// 检查 Helm Release 是否 Ready
	if c.base.ClusterSpec.K8SCustomConfig.EnableDefaultPluginDeployByHelm {
		status, err := c.base.GetAddonStatus(ctx, c.base.ClusterID, pluginReleaseName)
		if err != nil {
			logger.Errorf(ctx, "PluginStatus %s failed: %s", pluginReleaseName, status)
			return resources, err
		}
		if status != ccev2.InstancePhaseDeployed {
			logger.Errorf(ctx, "PluginStatus %s status %s not %s", pluginReleaseName, status, ccev2.InstancePhaseDeployed)
			return resources, fmt.Errorf("PluginStatus %s status %s not %s", pluginReleaseName, status, ccev2.InstancePhaseDeployed)
		}
	}

	// 检查相关 DaemonSet 或 Deployment 是否存在
	errDeploymentErr := CheckDeploymentExist(ctx, c.base.K8SClient, PluginCheckMetricsServerDeploymentLabel, "kube-system")
	if errDeploymentErr != nil {
		msg := fmt.Sprintf("plugin %s deployment not exist: %v", pluginReleaseName, errDeploymentErr)
		return resources, fmt.Errorf(msg)
	}

	// 检查相关 Pod 是否正常运行
	errPodRunning := CheckPodRunning(ctx, c.base.K8SClient, PluginCheckMetricsServerPodLabel, "kube-system")
	if errPodRunning != nil {
		msg := fmt.Sprintf("plugin %s pod not running: %v", pluginReleaseName, errPodRunning)
		return resources, fmt.Errorf(msg)
	}

	return resources, nil
}

func (c *pluginCheckMetricsServer) Name() cases.CaseName {
	return PluginCheckMetricsServer
}

func (c *pluginCheckMetricsServer) Desc() string {
	return "[待补充]"
}

func (c *pluginCheckMetricsServer) Clean(ctx context.Context) error {
	return nil
}

func (c *pluginCheckMetricsServer) Continue(ctx context.Context) bool {
	return true
}

func (c *pluginCheckMetricsServer) ConfigFormat() string {
	return ""
}
