/* check_addon_update.go */
/*
modification history
--------------------
2024/9/11, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
以csi-bos-plugin为例，校验组件升级、改配等接口（巡检覆盖）
*/

package plugin

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CheckAddonUpdate cases.CaseName = "CheckAddonUpdate"

	CCEPluginName string = "cce-csi-bos-plugin"
	DefaultParams string = "maxVolumesPerNode: 5"
	UpdateParams  string = "maxVolumesPerNode: 4"
)

type checkAddonUpdate struct {
	base *cases.BaseClient
}

func init() {
	cases.AddCase(context.TODO(), CheckAddonUpdate, NewCheckAddonUpdate)
}

func NewCheckAddonUpdate(ctx context.Context) cases.Interface {
	return &checkAddonUpdate{}
}

func (c *checkAddonUpdate) Name() cases.CaseName {
	return CheckAddonUpdate
}

func (c *checkAddonUpdate) Desc() string {
	return "校验组件升级用例"
}

func (c *checkAddonUpdate) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	return
}

func (c *checkAddonUpdate) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID
	// 获取组件历史版本
	chartDetail, chartDetailErr := c.base.HelmClient.GetPublicRepoChartDetail(ctx, CCEPluginName)
	if chartDetailErr != nil {
		err = fmt.Errorf("get %s chart detail failed: %v", CCEPluginName, chartDetailErr)
		return
	}
	if len(chartDetail.History) < 2 {
		err = errors.New("history is lower than 2, can not support plugin update test")
		return
	}

	// 取上一个版本进行部署后，执行升级
	oldVersion := chartDetail.History[1].Version
	latestVersion := chartDetail.History[0].Version

	// 若已部署，则先卸载
	pluginInfo, listErr := c.base.CCEClient.ListAddOns(ctx, clusterID, &ccev2.ListParams{
		TargetAddons: CCEPluginName,
	}, nil)
	if listErr != nil {
		err = fmt.Errorf("list addon %s failed: %v", CCEPluginName, listErr)
		return
	}
	if len(pluginInfo.Items) == 0 {
		err = errors.New("get addon list is empty")
		return
	}
	if pluginInfo.Items[0].Instance != nil {
		err = c.Clean(ctx)
		if err != nil {
			return
		}
	}

	// 安装历史版本组件
	_, installErr := c.base.CCEClient.InstallAddon(ctx, clusterID, &ccev2.InstallParams{
		Name:    CCEPluginName,
		Version: oldVersion,
	}, nil)
	if installErr != nil {
		err = fmt.Errorf("install %s %s failed: %v", oldVersion, CCEPluginName, installErr)
		return
	}

	// 升级版本
	_, upgradeErr := c.base.CCEClient.UpgradeAddon(ctx, clusterID, &ccev2.UpgradeParams{
		Name: CCEPluginName,
	}, nil)
	if upgradeErr != nil {
		err = fmt.Errorf("upgrade addon %s to %s failed: %v", CCEPluginName, latestVersion, upgradeErr.Error())
		return
	}
	pluginInfo, listErr = c.base.CCEClient.ListAddOns(ctx, clusterID, &ccev2.ListParams{
		TargetAddons: CCEPluginName,
	}, nil)
	if listErr != nil {
		err = fmt.Errorf("list addon %s failed: %v", CCEPluginName, listErr)
		return
	}
	if len(pluginInfo.Items) == 0 {
		err = errors.New("get addon list is empty")
		return
	}
	if pluginInfo.Items[0].Instance.InstalledVersion != latestVersion {
		err = fmt.Errorf("installed version is %s, want %s", pluginInfo.Items[0].Instance.InstalledVersion, latestVersion)
		return
	}

	logger.Infof(ctx, "check plugin update succeed")

	// 更新已安装组件的参数
	params := pluginInfo.Items[0].Instance.Params
	newParams := strings.ReplaceAll(params, DefaultParams, UpdateParams)
	logger.Infof(ctx, "newParams: %v", newParams)
	_, updateErr := c.base.CCEClient.UpdateAddon(ctx, clusterID, &ccev2.UpdateParams{
		Name:   CCEPluginName,
		Params: newParams,
	}, nil)
	if updateErr != nil {
		err = fmt.Errorf("update addon params failed: %v", updateErr)
		return
	}

	return
}

func (c *checkAddonUpdate) Clean(ctx context.Context) error {
	logger.Infof(ctx, "start to uninstall plugin %s", CCEPluginName)
	_, err := c.base.CCEClient.UninstallAddon(ctx, c.base.ClusterID, &ccev2.UninstallParams{
		Name: CCEPluginName,
	}, nil)
	return err
}

func (c *checkAddonUpdate) Continue(ctx context.Context) bool {
	return true
}

func (c *checkAddonUpdate) ConfigFormat() string {
	return ""
}
