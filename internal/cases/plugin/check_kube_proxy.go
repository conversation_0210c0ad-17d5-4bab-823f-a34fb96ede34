package plugin

import (
	"context"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/addon"
)

const (
	PluginCheckKubeProxy               cases.CaseName = "Plugin_Check_Kube_Proxy"
	PluginCheckKubeProxyPodLabel                      = "k8s-app=kube-proxy"
	PluginCheckKubeProxyDaemonsetLabel                = "k8s-app=kube-proxy"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), PluginCheckKubeProxy, NewPluginCheckKubeProxy)
}

type pluginCheckKubeProxy struct {
	base *cases.BaseClient
}

func NewPluginCheckKubeProxy(ctx context.Context) cases.Interface {
	return &pluginCheckKubeProxy{}
}

func (c *pluginCheckKubeProxy) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base

	return nil
}

func (c *pluginCheckKubeProxy) Check(ctx context.Context) ([]cases.Resource, error) {
	var resources []cases.Resource
	var errCheckProcess error

	for i := 0; i < 60; i++ {
		resources, errCheckProcess = c.checkProcess(ctx, resources)
		if errCheckProcess == nil {
			break
		}
		logger.Warnf(ctx, fmt.Sprintf("retry count %d error %s", i, errCheckProcess))
		time.Sleep(5 * time.Second)
	}

	return resources, errCheckProcess
}

func (c *pluginCheckKubeProxy) checkProcess(ctx context.Context, resources []cases.Resource) ([]cases.Resource, error) {
	pluginReleaseName := string(addon.AddonKubeProxy)

	// 检查 Helm Release 是否 Ready
	if c.base.ClusterSpec.K8SCustomConfig.EnableDefaultPluginDeployByHelm {
		status, err := c.base.GetAddonStatus(ctx, c.base.ClusterID, pluginReleaseName)
		if err != nil {
			logger.Errorf(ctx, "PluginStatus %s failed: %s", pluginReleaseName, status)
			return resources, err
		}
		if status != ccev2.InstancePhaseDeployed {
			logger.Errorf(ctx, "PluginStatus %s status %s not %s", pluginReleaseName, status, ccev2.InstancePhaseDeployed)
			return resources, fmt.Errorf("PluginStatus %s status %s not %s", pluginReleaseName, status, ccev2.InstancePhaseDeployed)
		}
	}

	// 检查相关 DaemonSet 或 Deployment 是否存在
	errDaemonsetErr := CheckDaemonSetExist(ctx, c.base.K8SClient, PluginCheckKubeProxyDaemonsetLabel, "kube-system")
	if errDaemonsetErr != nil {
		msg := fmt.Sprintf("plugin %s daemonset not exist: %v", pluginReleaseName, errDaemonsetErr)
		return resources, fmt.Errorf(msg)
	}

	// 检查相关 Pod 是否正常运行
	errPodRunning := CheckPodRunning(ctx, c.base.K8SClient, PluginCheckKubeProxyPodLabel, "kube-system")
	if errPodRunning != nil {
		msg := fmt.Sprintf("plugin %s pod not running: %v", pluginReleaseName, errPodRunning)
		return resources, fmt.Errorf(msg)
	}

	return resources, nil
}

func (c *pluginCheckKubeProxy) Name() cases.CaseName {
	return PluginCheckKubeProxy
}

func (c *pluginCheckKubeProxy) Desc() string {
	return "检查插件kube-proxy"
}

func (c *pluginCheckKubeProxy) Clean(ctx context.Context) error {
	return nil
}

func (c *pluginCheckKubeProxy) Continue(ctx context.Context) bool {
	return true
}

func (c *pluginCheckKubeProxy) ConfigFormat() string {
	return ""
}
