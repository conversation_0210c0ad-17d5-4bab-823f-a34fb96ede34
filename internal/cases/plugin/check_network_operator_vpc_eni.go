/*
modification history
--------------------
2024/8/14, by shimingming01, create
*/
/*
DESCRIPTION
场景：network-operator-vpc-cni 在给节点新增eni时会直接占满eni的可分配ip上限（实际辅助ip未达上限），导致operator误以为已经无法再分配ip，所以在需要分配新ip的行为都会失败。
1. 校验当产生分配新IP的操作能够顺利通过。
2. 由于现在的机制是eni创建出来就会自动分配辅助ip到上限，所以只要触发申请eni即可，对需要验证的worker部署刚好超量的pod就会强行触发申请新的eni。

测试流程：
1. 获取当前容器子网的剩余可用ip数量。
2. 通过nrs获取集群中可以用来申请新的最小eni的节点以及这个节点的现存可用ip数量。
3. 在这个节点上部署deployment消耗现存ip + 1来触发申请新的eni，确认deployment是running即可。
*/

package plugin

import (
	"context"
	"errors"
	"fmt"
	"time"

	v1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/yaml"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CheckNetworkOperatorVPCENICaseName cases.CaseName = "Plugin_Check_Network_Operator_VPC_ENI"
)

const (
	testVpcENIDeploymentName = "test-vpc-eni-busybox"
)

// 满足申请一张最小的eni的节点
type minEniNode struct {
	nodeName       string
	maxIPsPerENI   int // 申请一张eni需要的ip数量
	unUsedEniIPNum int // 当前eni未使用的ip数量
}

type checkNetworkOperatorVPCENI struct {
	base *cases.BaseClient
}

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CheckNetworkOperatorVPCENICaseName, NewCheckNetworkOperatorVPCENI)
}

// NewCheckNetworkOperatorVPCENI - 创建CheckNetworkOperatorVPCENI实例
func NewCheckNetworkOperatorVPCENI(ctx context.Context) cases.Interface {
	return &checkNetworkOperatorVPCENI{}
}

func (c *checkNetworkOperatorVPCENI) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base

	return nil
}

func (c *checkNetworkOperatorVPCENI) Name() cases.CaseName {
	return CheckNetworkOperatorVPCENICaseName
}

func (c *checkNetworkOperatorVPCENI) Desc() string {
	return "VPC-ENI网络模式下Operator给worker分配新eni的校验"
}

func (c *checkNetworkOperatorVPCENI) Check(ctx context.Context) (resources []cases.Resource, err error) {
	logger.Infof(ctx, "case called")

	clusterID := c.base.ClusterID

	// 获取集群额外的信息
	clusterExtraInfo, getExtraInfoErr := c.base.CCEHostClient.GetClusterExtraInfo(ctx, clusterID, nil)
	if getExtraInfoErr != nil {
		err = fmt.Errorf("CCEHostClient.GetClusterExtraInfo failed, err: %v", getExtraInfoErr)
		return
	}
	if len(clusterExtraInfo.ENISubnets) == 0 {
		err = errors.New("cluster has no eni subnets")
		return
	}

	// 默认选取第1个容器子网做判断，通常case设置里面只会使用一个子网
	subnetID := clusterExtraInfo.ENISubnets[0].SubnetID
	logger.Infof(ctx, "eni subnet id: %v", subnetID)

	// 获取当前容器子网的剩余可分IP数量
	var subnetAvailableIPNum int
	subnetAvailableIPNum, err = c.getSubnetInfo(ctx, subnetID)
	if err != nil {
		return
	}

	logger.Infof(ctx, "start to get nrs info by all worker nodes")
	// 获取节点和对应的nrs信息来确认实际占用的辅助ip数量和整个集群会预期会占用的最大ip数量
	nodes, listNodeErr := c.base.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if listNodeErr != nil {
		err = fmt.Errorf("list node failed, err: %v", listNodeErr)
		return
	}

	var nodeToApplyEni minEniNode

	for _, node := range nodes.Items {
		nrs, getNrsErr := c.base.KubeClient.GetNrs(ctx, node.Name, &kube.GetOptions{})
		if getNrsErr != nil {
			err = fmt.Errorf("getNrs failed for `%s`, err: %v", node.Name, getNrsErr)
			return
		}
		if nrs.Spec.Eni.MaxAllocateENI == len(nrs.Status.Enis) {
			logger.Warnf(ctx, "node `%s` eni num is max, can't apply more eni", node.Name)
			continue
		}
		// 找到申请eni成本最低的值
		if nodeToApplyEni.maxIPsPerENI == 0 || nrs.Spec.Eni.MaxIPsPerENI < nodeToApplyEni.maxIPsPerENI {
			nodeToApplyEni.maxIPsPerENI = nrs.Spec.Eni.MaxIPsPerENI
		}

		nodeToApplyEni.unUsedEniIPNum = len(nrs.Spec.Ipam.Pool) - len(nrs.Status.Ipam.Used)
		nodeToApplyEni.nodeName = node.Labels["kubernetes.io/hostname"]
	}

	// 不存在可以再申请eni的节点了，无法进行测试
	if nodeToApplyEni.maxIPsPerENI == 0 {
		logger.Warnf(ctx, "can't find a node to apply eni, skip this test.")
		return
	}

	// 当前子网剩余ip数量已经不满足申请eni的最低要求，无法进行测试
	if subnetAvailableIPNum < nodeToApplyEni.maxIPsPerENI {
		logger.Warnf(ctx, "subnet available ip num is not enough to apply a new min eni, skip this test.")
		return
	}

	// 部署deployment消耗支持申请eni的节点的未使用ip + 1，强行申请一张最小的eni
	logger.Infof(ctx, "apply deployment to node to apply new eni, %+v", nodeToApplyEni)
	err = c.ensureDeploymentRunning(ctx, int32(nodeToApplyEni.unUsedEniIPNum+1), nodeToApplyEni.nodeName)
	if err != nil {
		return
	}
	logger.Infof(ctx, "vpc eni operator work success")
	return
}

func (c *checkNetworkOperatorVPCENI) Clean(ctx context.Context) error {
	logger.Infof(ctx, "start to delete deployment %s", testVpcENIDeploymentName)
	return c.base.KubeClient.EnsureDeploymentDeletedAppsV1(ctx,
		metav1.NamespaceDefault,
		testVpcENIDeploymentName,
		&kube.CheckOptions{
			Interval: time.Second * 5,
			Timeout:  time.Minute * 3,
		},
	)
}

func (c *checkNetworkOperatorVPCENI) Continue(ctx context.Context) bool {
	return true
}

func (c *checkNetworkOperatorVPCENI) ConfigFormat() string {
	return ""
}

func (c *checkNetworkOperatorVPCENI) getSubnetInfo(ctx context.Context, subnetID string) (availableIPNum int, err error) {
	logger.Infof(ctx, "start to get subnet available IP num for `%s`", subnetID)
	subnet, descSubnetErr := c.base.VPCClient.DescribeSubnet(ctx, subnetID, nil)
	if descSubnetErr != nil {
		err = fmt.Errorf("describe subnet `%s` failed, err: %v", subnetID, descSubnetErr)
		return
	}
	availableIPNum = subnet.AvailableIPNum
	logger.Infof(ctx, "get subnet available IP num for `%s`, result: %d", subnetID, availableIPNum)
	return
}

func (c *checkNetworkOperatorVPCENI) ensureDeploymentRunning(ctx context.Context, replicas int32, hostName string) error {
	logger.Infof(ctx, "start to deploy test deployment, replicas: %d", replicas)
	var terminationGracePeriodSeconds int64
	deployment := v1.Deployment{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Deployment",
			APIVersion: "apps/v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      testVpcENIDeploymentName,
			Namespace: corev1.NamespaceDefault,
			Labels: map[string]string{
				"app": testVpcENIDeploymentName,
			},
		},
		Spec: v1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": testVpcENIDeploymentName,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": testVpcENIDeploymentName,
					},
				},
				Spec: corev1.PodSpec{
					TerminationGracePeriodSeconds: &terminationGracePeriodSeconds,
					NodeSelector: map[string]string{
						"kubernetes.io/hostname": hostName,
					},
					Containers: []corev1.Container{{
						Name:            testVpcENIDeploymentName,
						Image:           "registry.baidubce.com/cce-public/busybox",
						ImagePullPolicy: corev1.PullIfNotPresent,
						Command:         []string{"sleep", "3600"},
					}},
				},
			},
		},
	}
	out, err := yaml.Marshal(&deployment)
	if err != nil {
		return fmt.Errorf("marshal deployment failed, err: %v", err)
	}
	deploymentYaml := string(out)
	_, err = c.base.AppClient.PostAppResource(ctx, c.base.ClusterID, testVpcENIDeploymentName, deploymentYaml, corev1.NamespaceDefault, nil)
	if err != nil {
		return fmt.Errorf("create deployment failed, err: %v", err)
	}

	// 尝试部署deployment到指定节点，等待所有副本running
	err = resource.WaitForFunc(ctx, func(ctx context.Context) (err error) {
		deploymentResource, getErr := c.base.AppClient.GetAppDeploymentByName(ctx, c.base.ClusterID, testVpcENIDeploymentName, corev1.NamespaceDefault, nil)
		if getErr != nil {
			err = fmt.Errorf("get deployment Resource: %v failed, err: %v", testVpcENIDeploymentName, err)
			return
		}
		if deploymentResource.Status != "Running" || deploymentResource.StatusInfo.Available != deploymentResource.StatusInfo.Replicas {
			err = fmt.Errorf("deployment status: %v, available: %d, expect replicas: %d", deploymentResource.Status,
				deploymentResource.StatusInfo.Available, deploymentResource.StatusInfo.Replicas)
			return
		}
		// 其实更加准确的是判断事件里面是否存在 ENICapacityExceed，更加能确认是ip分配问题造成的状态没达到预期
		return
	}, time.Second*5, time.Minute*3)
	if err != nil {
		return fmt.Errorf("wait for deployment running failed, err: %v", err)
	}
	logger.Infof(ctx, "deployment %s is running", testVpcENIDeploymentName)
	return nil
}
