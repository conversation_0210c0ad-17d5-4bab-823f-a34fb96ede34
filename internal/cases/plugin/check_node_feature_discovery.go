package plugin

import (
	"context"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/addon"
)

const (
	PluginCheckNodeFeatureDiscovery                      cases.CaseName = "Plugin_Check_Node_Feature_Discovery"
	PluginCheckNodeFeatureDiscoveryMasterPodLabel                       = "app=nfd-master"
	PluginCheckNodeFeatureDiscoveryWorkerPodLabel                       = "ap=nfd-worker"
	PluginCheckNodeFeatureDiscoveryMasterDeploymentLabel                = "app=nfd-master"
	PluginCheckNodeFeatureDiscoveryWorkerDaemonsetLabel                 = "app=nfd-worker"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), PluginCheckNodeFeatureDiscovery, NewPluginCheckNodeFeatureDiscovery)
}

var _ cases.Interface = &pluginCheckNodeFeatureDiscovery{}

type pluginCheckNodeFeatureDiscovery struct {
	base         *cases.BaseClient
	addOnFactory addon.FactoryInterface
}

func NewPluginCheckNodeFeatureDiscovery(ctx context.Context) cases.Interface {
	return &pluginCheckNodeFeatureDiscovery{}
}

func (c *pluginCheckNodeFeatureDiscovery) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base
	c.addOnFactory = &addon.Factory{}

	return nil
}

func (c *pluginCheckNodeFeatureDiscovery) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	var errCheckProcess error

	for i := 0; i < 60; i++ {
		resources, errCheckProcess = c.checkProcess(ctx, resources)
		if errCheckProcess == nil {
			break
		}
		logger.Warnf(ctx, fmt.Sprintf("retry count %d error %s", i, errCheckProcess))
		time.Sleep(5 * time.Second)
	}

	return resources, errCheckProcess
}

func (c *pluginCheckNodeFeatureDiscovery) checkProcess(ctx context.Context, resources []cases.Resource) ([]cases.Resource, error) {
	pluginReleaseName := string(addon.AddOnCCENodeFeatureDiscovery)

	// 检查 Helm Release 是否 Ready
	if c.base.ClusterSpec.K8SCustomConfig.EnableDefaultPluginDeployByHelm {
		status, err := c.base.GetAddonStatus(ctx, c.base.ClusterID, pluginReleaseName)
		if err != nil {
			logger.Errorf(ctx, "PluginStatus %s failed: %s", pluginReleaseName, status)
			return resources, err
		}
		if status != ccev2.InstancePhaseDeployed {
			logger.Errorf(ctx, "PluginStatus %s status %s not %s", pluginReleaseName, status, ccev2.InstancePhaseDeployed)
			return resources, fmt.Errorf("PluginStatus %s status %s not %s", pluginReleaseName, status, ccev2.InstancePhaseDeployed)
		}
	}

	// 检查相关 DaemonSet 或 Deployment 是否存在
	errDeploymentErr := CheckDeploymentExist(ctx, c.base.K8SClient, PluginCheckNodeFeatureDiscoveryMasterDeploymentLabel, "kube-system")
	if errDeploymentErr != nil {
		msg := fmt.Sprintf("plugin %s master deployment not exist: %v", pluginReleaseName, errDeploymentErr)
		return resources, fmt.Errorf(msg)
	}
	errDaemonsetErr := CheckDaemonSetExist(ctx, c.base.K8SClient, PluginCheckNodeFeatureDiscoveryWorkerDaemonsetLabel, "kube-system")
	if errDaemonsetErr != nil {
		msg := fmt.Sprintf("plugin %s worker daemonset not exist: %v", pluginReleaseName, errDaemonsetErr)
		return resources, fmt.Errorf(msg)
	}

	// 检查相关 Pod 是否正常运行
	errWorkerRunning := CheckPodRunning(ctx, c.base.K8SClient, PluginCheckNodeFeatureDiscoveryWorkerPodLabel, "kube-system")
	if errWorkerRunning != nil {
		msg := fmt.Sprintf("plugin %s worker pod not running: %v", pluginReleaseName, errWorkerRunning)
		return resources, fmt.Errorf(msg)
	}
	errMasterRunning := CheckPodRunning(ctx, c.base.K8SClient, PluginCheckNodeFeatureDiscoveryMasterPodLabel, "kube-system")
	if errMasterRunning != nil {
		msg := fmt.Sprintf("plugin %s master pod not running: %v", pluginReleaseName, errMasterRunning)
		return resources, fmt.Errorf(msg)
	}

	return resources, nil
}

func (c *pluginCheckNodeFeatureDiscovery) Name() cases.CaseName {
	return PluginCheckNodeFeatureDiscovery
}

func (c *pluginCheckNodeFeatureDiscovery) Desc() string {
	return "[待补充]"
}

func (c *pluginCheckNodeFeatureDiscovery) Clean(ctx context.Context) error {
	return nil
}

func (c *pluginCheckNodeFeatureDiscovery) Continue(ctx context.Context) bool {
	return true
}

func (c *pluginCheckNodeFeatureDiscovery) ConfigFormat() string {
	return ""
}
