package cluster

import (
	"context"
	"fmt"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"strings"

	"k8s.io/client-go/tools/clientcmd"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	CheckOIDCCluster   cases.CaseName = "CheckOIDCCluster"
	ExpectedServerPort string         = "6443"
	RowConfClustersKey string         = "kubernetes"
)

type checkOIDCCluster struct {
	base *cases.BaseClient
}

func init() {
	cases.AddCase(context.TODO(), CheckOIDCCluster, NewCheckOIDCCluster)
}

func NewCheckOIDCCluster(ctx context.Context) cases.Interface {
	return &checkOIDCCluster{}
}

func (c *checkOIDCCluster) Name() cases.CaseName {
	return CheckOIDCCluster
}

func (c *checkOIDCCluster) Desc() string {
	return "检查OIDC集群连通"
}

func (c *checkOIDCCluster) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	return nil
}

func (c *checkOIDCCluster) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID

	getCRD, err := c.base.CCEHostClient.GetClusterCRD(ctx, clusterID, nil)
	if err != nil {
		return nil, err
	}

	if getCRD.Cluster.Spec.AuthenticateMode != types.AuthenticateModeOIDC {
		return nil, fmt.Errorf("cluster %v authenticate mode is not oidc", clusterID)
	}

	kubeConfigRes, err := c.base.CCEClient.GetAdminKubeConfig(ctx, clusterID, models.KubeConfigTypeVPC, nil)
	if err != nil {
		return nil, err
	}

	kubeConfig := kubeConfigRes.KubeConfig
	logger.Infof(ctx, "VPC type kubeconfig: %v", kubeConfig)

	clientConfig, err := clientcmd.NewClientConfigFromBytes([]byte(kubeConfig))
	if err != nil {
		return nil, err
	}

	rawConf, err := clientConfig.RawConfig()
	if err != nil {
		return nil, err
	}

	if _, ok := rawConf.Clusters[RowConfClustersKey]; !ok {
		return nil, fmt.Errorf(" get cluster %v server failed ", clusterID)
	}
	configServer := rawConf.Clusters[RowConfClustersKey].Server
	if configServer == "" {
		return nil, fmt.Errorf(" %v kubeconfig server is nil ", clusterID)
	}
	logger.Infof(ctx, "kubeconfig Server: %v", configServer)

	serverSplitArr := strings.Split(configServer, ":")
	serverPort := serverSplitArr[len(serverSplitArr)-1]
	if serverPort != ExpectedServerPort {
		return nil, fmt.Errorf(" %v kubeconfig server port is not expected server port %v ", clusterID, ExpectedServerPort)
	}

	//检查联通性
	_, err = c.base.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf(" check oidc cluster connection failed: %v", err)
	}

	//更新凭证
	_, err = c.base.CCEClient.RenewRBACKubeConfig(ctx, &ccev2.KubeConfigRequest{
		ClusterID: clusterID,
		NameSpace: ccev2.ClusterNamespace,
		Renew:     true,
		Role:      ccev2.RoleAdmin, // 所使用aksk是管理员权限
	}, nil)

	if err != nil {
		if strings.Contains(err.Error(), "This is not supported for oidc cluster.") {
			logger.Infof(ctx, "renew oidc cluster kubeconfig is not support, get expected error")
			return nil, nil
		}
		return nil, fmt.Errorf(" renew oidc cluster kubeconfig failed: %v", err)
	}

	return nil, fmt.Errorf(" renew oidc cluster kubeconfig is not support, but success! ")
}

func (c *checkOIDCCluster) Clean(ctx context.Context) error {
	return nil
}

func (c *checkOIDCCluster) Continue(ctx context.Context) bool {
	return true
}

func (c *checkOIDCCluster) ConfigFormat() string {
	return ""
}
