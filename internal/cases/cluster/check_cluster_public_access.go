/*
modification history
--------------------
2024/9/4, by shimingming<PERSON>, create
*/
/*
DESCRIPTION
场景：托管集群，开启/关闭apiserver公网访问。
根据集群当前的 ExposedPublic 状态分2种情况
1. ExposedPublic 为 false，测试绑定EIP开启公网访问是否成功，之后 ExposedPublic = true 进入第二种情况
2. ExposedPublic 为 true，测试获取集群版本是否成功，再解绑EIP关闭公网访问，重新验证公网的集群客户端是否获取集群版本成功
*/

package cluster

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"strings"
	"time"

	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	CheckClusterPublicAccess cases.CaseName = "CheckClusterPublicAccess"
)

const (
	newEIPNamePrefix     = "e2e-test"
	bindEIPTimeout       = time.Minute * 6
	bindEIPCheckInterval = time.Second * 10
)

type checkClusterPublicAccess struct {
	base          *cases.BaseClient
	exposedPublic bool
	createdNewEIP string
}

func init() {
	cases.AddCase(context.TODO(), CheckClusterPublicAccess, NewCheckClusterPublicAccess)
}

func NewCheckClusterPublicAccess(ctx context.Context) cases.Interface {
	return &checkClusterPublicAccess{}
}

func (c *checkClusterPublicAccess) Name() cases.CaseName {
	return CheckClusterPublicAccess
}

func (c *checkClusterPublicAccess) Desc() string {
	return "检查集群公网访问"
}

func (c *checkClusterPublicAccess) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	return
}

func (c *checkClusterPublicAccess) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID

	masterConfig := c.base.ClusterSpec.MasterConfig
	// 只有托管集群需要跑这个case
	if masterConfig.MasterType != ccetypes.MasterTypeManagedPro {
		logger.Infof(ctx, "only managed pro cluster need to run this case")
		return
	}
	c.exposedPublic = masterConfig.ExposedPublic
	logger.Infof(ctx, "cluster initial ExposedPublic: %v", c.exposedPublic)

	var kubeClient *kube.Client
	// ExposedPublic 为 false
	if !c.exposedPublic {
		var toBindEIP string
		eipList, getEipErr := c.base.EIPClient.GetEIPs(ctx, &eip.GetEIPsArgs{
			Status: eip.EIPAvailable,
		}, nil)
		if getEipErr != nil {
			err = fmt.Errorf("EIPClient.GetEIPs failed for %s: %v", eip.EIPAvailable, getEipErr)
			return
		}
		if len(eipList) > 0 {
			toBindEIP = c.sampleEIP(eipList)
			logger.Infof(ctx, "get the existed available eip: %s", toBindEIP)
		} else {
			logger.Infof(ctx, "no available eip, try to create a new one")
			// 如果不存在可用的eip，创建一个
			uuid := logger.GetUUID()
			newEIPName := fmt.Sprintf("%s-%s", newEIPNamePrefix, uuid[:5])
			billing := eip.Billing{
				PaymentTiming: eip.PaymentTimingPostpaid,
				BillingMethod: eip.BillingMethodByTraffic,
			}
			toBindEIP, err = c.base.EIPClient.CreateEIP(ctx, &eip.CreateEIPArgs{
				BandwidthInMbps: 1,
				Billing:         &billing,
				Name:            newEIPName,
			}, nil)
			if err != nil {
				err = fmt.Errorf("EIPClient.CreateEIP failed: %v", err)
				return
			}
			c.createdNewEIP = toBindEIP
			logger.Infof(ctx, "created a new eip: %s", toBindEIP)
		}
		logger.Infof(ctx, "configure the cluster eip with: %s", toBindEIP)
		bindEIPRes, bindEIPErr := c.base.CCEClient.ConfigureClusterEIP(ctx, clusterID, &ccev2.ConfigureClusterEIPRequest{
			ConfigureClusterEIP: ccev2.ConfigureClusterEIP{
				ClusterID: clusterID,
				EIPIP:     toBindEIP,
				EIPSource: ccetypes.BLBSourceUser,
				Action:    ccetypes.EIPActionBind,
			},
		}, nil)
		if bindEIPErr != nil {
			err = fmt.Errorf("CCEClient.ConfigureClusterEIP bind failed: %v", bindEIPErr)
			return
		}
		if !bindEIPRes.Success {
			err = errors.New("CCEClient.ConfigureClusterEIP failed")
			return
		}
		logger.Infof(ctx, "configure cluster eip request success, wait for cluster status")
		// 轮询检查EIP是否绑定成功，绑定成功后 ExposedPublic = true
		err = c.ensureBindEIPSuccess(ctx, ccetypes.ClusterPhaseEIPOpenFailed, true)
		if err != nil {
			err = fmt.Errorf("ensureBindEIPSuccess failed: %v", err)
			return
		}
		c.exposedPublic = true
		logger.Infof(ctx, "configure cluster eip success, ExposedPublic is true")
	}
	if c.exposedPublic {
		kubeClient, err = kube.NewClientByClusterID(ctx, clusterID, models.KubeConfigTypePublic, c.base.CCEClient, 30*time.Second, true)
		if err != nil {
			err = fmt.Errorf("create kube client failed: %v", err)
			return
		}
		// 预期可以通过client获取集群版本信息
		_, err = kubeClient.ClientSet.Discovery().ServerVersion()
		if err != nil {
			err = fmt.Errorf("get cluster server version failed: %v", err)
			return
		}
		logger.Infof(ctx, "get cluster server version success")
	}
	if kubeClient == nil {
		err = errors.New("kube client is nil")
		return
	}
	// 解绑 EIP，关闭公共访问
	logger.Infof(ctx, "try to unbind eip")
	unBindEIPRes, unBindEIPErr := c.base.CCEClient.ConfigureClusterEIP(ctx, clusterID, &ccev2.ConfigureClusterEIPRequest{
		ConfigureClusterEIP: ccev2.ConfigureClusterEIP{
			ClusterID: clusterID,
			EIPSource: ccetypes.BLBSourceUser,
			Action:    ccetypes.EIPActionUNBind,
		},
	}, nil)
	if unBindEIPErr != nil {
		err = fmt.Errorf("CCEClient.ConfigureClusterEIP unbind failed: %v", unBindEIPErr)
		return
	}
	if !unBindEIPRes.Success {
		err = errors.New("CCEClient.ConfigureClusterEIP failed")
		return
	}
	logger.Infof(ctx, "configure cluster eip request success, wait for cluster status")

	err = c.ensureBindEIPSuccess(ctx, ccetypes.ClusterPhaseEIPOpenFailed, false)
	if err != nil {
		err = fmt.Errorf("ensureBindEIPSuccess failed: %v", err)
		return
	}
	c.exposedPublic = false
	logger.Infof(ctx, "configure cluster eip success, ExposedPublic is false")

	logger.Infof(ctx, "try to test cluster public access, expect timeout")
	// 预期不可以通过公网client获取集群版本信息，请求会发生超时
	_, err = kubeClient.ClientSet.Discovery().ServerVersion()
	if err == nil {
		err = errors.New("ExposedPublic is false, but public kubeconfig still can access the cluster")
		return
	}
	if strings.Contains(err.Error(), "Client.Timeout exceeded") {
		err = nil
		logger.Infof(ctx, "cluster public access is timeout, test pass")
		return
	}
	return
}

func (c *checkClusterPublicAccess) Clean(ctx context.Context) error {
	if c.createdNewEIP != "" {
		err := c.base.EIPClient.DeleteEIP(ctx, c.createdNewEIP, nil)
		if err != nil {
			err = fmt.Errorf("EIPClient.DeleteEIP failed: %v", err)
			return err
		}
	}
	return nil
}

func (c *checkClusterPublicAccess) Continue(ctx context.Context) bool {
	return true
}

func (c *checkClusterPublicAccess) ConfigFormat() string {
	return ""
}

func (c *checkClusterPublicAccess) ensureBindEIPSuccess(ctx context.Context, failedEIPPhase ccetypes.ClusterPhase, expectedExposedPublic bool) (err error) {
	timeoutCtx, cancel := context.WithTimeout(ctx, bindEIPTimeout)
	wait.UntilWithContext(timeoutCtx, func(ctx context.Context) {
		clusterRes, getErr := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
		if getErr != nil {
			logger.Errorf(ctx, "CCEClient.GetCluster failed: %v", getErr)
			return
		}
		phase := clusterRes.Cluster.Status.ClusterPhase
		// 绑定EIP失败了，直接取消轮询
		if phase == failedEIPPhase {
			err = fmt.Errorf("CCEClient.GetCluster success, ClusterPhase is %s", failedEIPPhase)
			logger.Errorf(ctx, err.Error())
			cancel()
			return
		}
		// 成功绑定EIP，取消轮询
		if phase == ccetypes.ClusterPhaseRunning && clusterRes.Cluster.Spec.MasterConfig.ExposedPublic == expectedExposedPublic {
			logger.Infof(ctx, "CCEClient.GetCluster success, ClusterPhase is %s, ExposedPublic is %v", ccetypes.ClusterPhaseRunning, expectedExposedPublic)
			cancel()
			return
		}
		// 其他状态继续轮询
		logger.Warnf(ctx, "CCEClient.GetCluster success, ClusterPhase is %s, next loop", phase)
		return
	}, bindEIPCheckInterval)
	if errors.Is(timeoutCtx.Err(), context.DeadlineExceeded) {
		err = fmt.Errorf("WaitForFunc timeout for %s", bindEIPTimeout.String())
		return
	}
	return
}

func (c *checkClusterPublicAccess) sampleEIP(eipList []*eip.EIP) string {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	randomIndex := r.Intn(len(eipList))
	return eipList[randomIndex].EIP
}
