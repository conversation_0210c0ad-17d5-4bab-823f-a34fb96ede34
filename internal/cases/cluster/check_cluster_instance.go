// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/07/10 20:28:00, by <EMAIL>, create
*/
/*
CheckClusterInstance, 对集群所有slave节点进行预期值检查
*/

package cluster

import (
	"context"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	ccesdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/cmp"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// CheckClusterInstance - Case 名字
	CheckClusterInstance cases.CaseName = "CheckClusterInstance"
)

func init() {
	cases.AddCase(context.TODO(), CheckClusterInstance, NewCheckClusterInstance)
}

var _ cases.Interface = &checkClusterInstance{}

type checkClusterInstance struct {
	base *cases.BaseClient
}

// NewCheckClusterInstance - 对集群所有slave节点进行预期值检查
func NewCheckClusterInstance(ctx context.Context) cases.Interface {
	return &checkClusterInstance{}
}

func (c *checkClusterInstance) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base

	return nil
}

func (c *checkClusterInstance) Name() cases.CaseName {
	return CheckClusterInstance
}

func (c *checkClusterInstance) Desc() string {
	return "对集群所有slave节点进行预期值检查"
}

func (c *checkClusterInstance) Check(ctx context.Context) ([]cases.Resource, error) {
	clusterID := c.base.ClusterID
	resources := make([]cases.Resource, 0)
	var errMsg string

	// 获取集群信息，用于事件的记录
	clusterInfo, err := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "get cluster %s failed: %s", clusterID, err)
	}

	// 查询 Instance
	// 由于集群状态和节点状态解耦:
	// ClusterPhase = Running 时, Node 的 InstancePhase 不一定为 Running
	var instanceList []*ccesdk.Instance
	var unReadyInstanceList []*ccesdk.Instance

	for i := 0; i < 60; i++ {
		// 查询所有节点
		cceResp, err := c.base.CCEClient.ListInstancesByPage(ctx, clusterID, nil, nil)
		if err != nil {
			logger.Errorf(ctx, "GetInstance %s failed: %s", clusterID, err)
			return nil, err
		}

		if cceResp == nil {
			return nil, errors.New("GetInstance return nil")
		}

		unReadyInstanceList = []*ccesdk.Instance{}
		instanceList = cceResp.InstancePage.InstanceList

		// 检查节点状态
		for _, instance := range cceResp.InstancePage.InstanceList {
			if instance.Status.InstancePhase == ccetypes.InstancePhaseCreateFailed {
				logger.Errorf(ctx, "Instance %s status %s, exit check", instance.Spec.CCEInstanceID, instance.Status.InstancePhase)
				return resources, fmt.Errorf("Instance %s status %s, exit check", instance.Spec.CCEInstanceID, instance.Status.InstancePhase)
			}

			// CCE 接口返回 ready
			if instance.Status.InstancePhase != ccetypes.InstancePhaseRunning && instance.Status.InstancePhase != "ready" {
				unReadyInstanceList = append(unReadyInstanceList, instance)
				logger.Infof(ctx, "Instance %s status %s not running", instance.Spec.CCEInstanceID, instance.Status.InstancePhase)
				break
			}
		}

		// 判断是否都 Running
		if len(unReadyInstanceList) != 0 {
			logger.Infof(ctx, "Instances %s not ready, wait 30 seconds", utils.ToJSON(unReadyInstanceList))
			time.Sleep(30 * time.Second)
			continue
		}

		// 都为 Running
		if len(unReadyInstanceList) == 0 {
			logger.Infof(ctx, "Instances all ready")
			// 记录节点成功的事件
			events := make([]models.Event, 0, len(instanceList))
			for _, instance := range cceResp.InstancePage.InstanceList {
				if instance.Spec.ClusterRole == ccetypes.ClusterRoleMaster {
					continue
				}
				events = append(events, models.Event{
					Region:           c.base.Region,
					ClusterID:        clusterID,
					ClusterName:      clusterInfo.Cluster.Spec.ClusterName,
					OperationType:    models.ScaleUpNodeOperation,
					OperationSuccess: true,
					AnalysisData: c.base.Region + "-" + string(instance.Spec.MachineType) + "-" + instance.Status.Machine.InstanceID +
						"-" + string(models.ScaleUpNodeOperation) + "-" + string(models.GetEventOperationResult(true)),
					AnalysisDig: errMsg,
				})
			}
			if len(events) > 0 {
				createEventErr := c.base.QaDbClient.CreateEvent(events)
				if createEventErr != nil {
					logger.Errorf(ctx, "CreateEvent failed: %s", utils.ToJSON(createEventErr))
				}
			}
			break
		}
	}

	if len(unReadyInstanceList) != 0 {
		logger.Errorf(ctx, "CheckClusterInstance failed: Instance %s not ready after 1800s", utils.ToJSON(unReadyInstanceList))

		events := make([]models.Event, 0, len(unReadyInstanceList))
		for _, instance := range unReadyInstanceList {
			if instance.Spec.ClusterRole == ccetypes.ClusterRoleMaster {
				continue
			}
			instanceEventSteps, err := c.base.CCEClient.GetInstanceEventSteps(ctx, instance.Spec.CCEInstanceID, nil)
			if err != nil {
				logger.Errorf(ctx, "get instance %s event steps failed: %s", instance.Spec.CCEInstanceID, err)
			}
			for _, step := range instanceEventSteps.Steps {
				// 判断实例状态的30分钟内，失败步骤状态可能还未转为failed，正处于的该步的重试
				if step.StepStatus == ccesdk.StepStatusFailed || step.StepStatus == ccesdk.StepStatusDoing {
					errMsg = string(step.StepName) + " : " + step.ErrMsg
					break
				}
			}
			events = append(events, models.Event{
				Region:        c.base.Region,
				ClusterID:     clusterID,
				ClusterName:   clusterInfo.Cluster.Spec.ClusterName,
				OperationType: models.ScaleUpNodeOperation,
				AnalysisData: c.base.Region + "-" + string(instance.Spec.MachineType) + "-" + instance.Status.Machine.InstanceID +
					"-" + string(models.ScaleUpNodeOperation) + "-" + string(models.GetEventOperationResult(false)),
				AnalysisDig: errMsg,
			})
		}
		if len(events) > 0 {
			createEventErr := c.base.QaDbClient.CreateEvent(events)
			if createEventErr != nil {
				logger.Errorf(ctx, "CreateEvent failed: %s", utils.ToJSON(createEventErr))
			}
		}
		return resources, errors.New("Instance not ready after 1800s")
	}

	// 节点都 Running 后需要给cloud-node-controller增删taint的时间
	time.Sleep(30 * time.Second)

	// 比较 Diff
	result := map[string][]*cmp.Diff{}
	for _, cceInstance := range instanceList {
		diffs := []*cmp.Diff{}
		cceInstanceID := cceInstance.Spec.CCEInstanceID

		resources = append(resources,
			cases.Resource{
				CaseName: c.Name(),
				Type:     cases.ResourceTypeCCEInstance,
				ID:       cceInstanceID,
			})

		// 比较 CCE Instance 和 BCC Instance
		bccDiffs, err := c.base.CMPClient.InstanceCompareWithIaaS(ctx, clusterID, cceInstanceID, nil)
		if err != nil {
			logger.Errorf(ctx, "InstanceCompareWithIaaS %s failed: %s", cceInstanceID, err)
			return resources, err
		}

		diffs = append(diffs, bccDiffs...)

		// 比较 CCE Instance 和 K8S Node
		if cceInstance.Spec.ClusterRole == ccetypes.ClusterRoleNode {
			k8sDiffs, err := c.base.CMPClient.InstanceCompareWithK8S(ctx, clusterID, cceInstanceID, nil)
			if err != nil {
				logger.Errorf(ctx, "InstanceCompareWithK8S %s failed: %s", cceInstanceID, err)
				return resources, err
			}

			diffs = append(diffs, k8sDiffs...)
		}

		if len(diffs) != 0 {
			result[cceInstanceID] = diffs
			logger.Warnf(ctx, "Check Instance %s failed, Diff=%s", cceInstanceID, utils.ToJSON(diffs))
		}
	}

	if len(result) != 0 {
		return resources, fmt.Errorf("CheckClusterInstance %s failed: %s", clusterID, utils.ToJSON(result))
	}

	return resources, nil
}

func (c *checkClusterInstance) Clean(ctx context.Context) error {
	// 不需要回收集群, 已在外部设置集群的清理 DeleteCluster: true
	return nil
}

func (c *checkClusterInstance) Continue(ctx context.Context) bool {
	return false
}

func (c *checkClusterInstance) ConfigFormat() string {
	return ""
}
