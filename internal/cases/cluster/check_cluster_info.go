/* check_cluster_info.go */
/*
modification history
--------------------
2024/5/9, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
1、校验集群子网信息
2、修改集群备注
3、集群和节点的步骤接口校验
4、开启删除保护校验
5、校验服务画像

*/

package cluster

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/cce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	// CheckClusterInfo - Case 名字
	CheckClusterInfo cases.CaseName = "CheckClusterInfo"
)

func init() {
	cases.AddCase(context.TODO(), CheckClusterInfo, NewCheckClusterInfo)
}

var _ cases.Interface = &checkClusterInfo{}

type checkClusterInfo struct {
	base *cases.BaseClient
}

// NewCheckClusterInfo - 对集群所有Pods进行健康检查
func NewCheckClusterInfo(ctx context.Context) cases.Interface {
	return &checkClusterInfo{}
}

func (c *checkClusterInfo) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base

	return nil
}

func (c *checkClusterInfo) Name() cases.CaseName {
	return CheckClusterInfo
}

func (c *checkClusterInfo) Desc() string {
	return "校验集群子网信息、修改集群备注"
}

func (c *checkClusterInfo) Check(ctx context.Context) ([]cases.Resource, error) {
	clusterID := c.base.ClusterID
	resources := make([]cases.Resource, 0)

	info, err := c.base.CCEHostClient.GetClusterExtraInfo(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "get cluster extra info failed: %v", err.Error())
		return resources, err
	}

	if info == nil {
		return resources, errors.New("cluster extra info is empty")
	}
	// TODO: check info correct

	// check update cluster comment success
	comment := cce.ClusterComment{Comment: "cce-reg-test"}
	if err = c.base.CCEV1Client.UpdateClusterComment(ctx, clusterID, &comment, nil); err != nil {
		return resources, fmt.Errorf("update cluster comment failed: %v ", err)
	}

	cluster, err := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	if err != nil {
		return resources, fmt.Errorf("get cluster detail failed: %v", err)
	}

	description := cluster.Cluster.Spec.Description
	if description != comment.Comment {
		return resources, fmt.Errorf("cluster description %s is not update to %s", description, comment.Comment)
	}

	// check cluster and instance steps
	_, err = c.base.CCEClient.GetClusterEventSteps(ctx, clusterID, nil)
	if err != nil {
		return resources, fmt.Errorf("get cluster event steps failed: %v", err)
	}
	instances, err := c.base.CCEClient.ListInstancesByPage(ctx, clusterID, &ccev2.ListInstancesByPageParams{ClusterRole: types.ClusterRoleNode}, nil)
	if err != nil {
		return resources, fmt.Errorf("get instance list failed: %v", err)
	}

	var instanceID string
	for _, instance := range instances.InstancePage.InstanceList {
		// 避免选到异常状态的节点
		if instance.Status.InstancePhase == types.InstancePhaseRunning {
			instanceID = instance.Spec.CCEInstanceID
			break
		}
	}
	if instanceID == "" {
		return resources, fmt.Errorf("cluster node list is empty")
	}

	_, err = c.base.CCEClient.GetInstanceEventSteps(ctx, instanceID, nil)
	if err != nil {
		return resources, fmt.Errorf("get instance event steps failed: %v", err)
	}

	// check cluster forbid delete
	err = c.base.CCEHostClient.UpdateClusterForbidDeleteConfig(ctx, clusterID, &ccev2.UpdateClusterForbidDeleteRequest{
		ForbidDelete: true,
	}, nil)
	if err != nil {
		return resources, fmt.Errorf("update cluster forbid delete config failed: %v", err)
	}

	_, delErr := c.base.CCEClient.DeleteCluster(ctx, clusterID, nil, nil)
	if delErr != nil {
		if !strings.Contains(delErr.Error(), "not allow delete") {
			return resources, fmt.Errorf("delete cluster failed: %v", delErr)
		}
	}

	err = c.base.CCEHostClient.UpdateClusterForbidDeleteConfig(ctx, clusterID, &ccev2.UpdateClusterForbidDeleteRequest{
		ForbidDelete: false,
	}, nil)
	if err != nil {
		return resources, fmt.Errorf("update cluster forbid delete config failed: %v", err)
	}

	return resources, nil
}

func (c *checkClusterInfo) Clean(ctx context.Context) error {
	return nil
}

func (c *checkClusterInfo) Continue(ctx context.Context) bool {
	return false
}

func (c *checkClusterInfo) ConfigFormat() string {
	return ""
}
