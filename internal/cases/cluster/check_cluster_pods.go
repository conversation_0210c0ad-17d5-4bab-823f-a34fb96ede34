// Copyright 2022 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2022/03/10, by zhang<PERSON><PERSON>@baidu.com, create
*/
/*
CheckClusterPods, 对集群所有Pods节点进行健康检查
*/

package cluster

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/workflow/workflow-controller/tasks"
)

const (
	// CheckClusterPods - Case 名字
	CheckClusterPods cases.CaseName = "CheckClusterPods"
)

func init() {
	cases.AddCase(context.TODO(), CheckClusterPods, NewCheckClusterPods)
}

var _ cases.Interface = &checkClusterPods{}

type checkClusterPods struct {
	base *cases.BaseClient
}

// NewCheckClusterPods - 对集群所有Pods进行健康检查
func NewCheckClusterPods(ctx context.Context) cases.Interface {
	return &checkClusterPods{}
}

func (c *checkClusterPods) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base

	return nil
}

func (c *checkClusterPods) Name() cases.CaseName {
	return CheckClusterPods
}

func (c *checkClusterPods) Desc() string {
	return "对集群所有Pods节点进行健康检查"
}

func (c *checkClusterPods) Check(ctx context.Context) ([]cases.Resource, error) {
	clusterID := c.base.ClusterID

	// 获取masterType
	cluster, _ := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	masterType := cluster.Cluster.Spec.MasterConfig.MasterType

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()
	timer := time.NewTimer(3 * time.Minute)
	defer timer.Stop()
	for {
		select {
		case <-ticker.C:
			// 获取所有 Pods
			podList, err := c.base.K8SClient.CoreV1().Pods(metav1.NamespaceAll).List(ctx, metav1.ListOptions{})
			if err != nil {
				logger.Errorf(ctx, "get all pods failed: %s", err)
				return nil, err
			}
			if len(podList.Items) == 0 {
				continue
			}

			flag := true
			for _, pod := range podList.Items {
				ready, err := tasks.CheckPodReady(ctx, &pod)
				if err != nil {
					logger.Errorf(ctx, "CheckPodReady failed: %s", err)
					return nil, err
				}
				// 如果是eni-ipam，忽略
				if !ready && !strings.Contains(pod.Name, "eni-ipam") {
					// 若集群类型为custom版本，且失败容器是lb-controller, 同时失败是调度原因，则忽略
					if strings.Contains(pod.Name, "lb-controller") && masterType == ccetypes.MasterTypeCustom {
						if pod.Status.Conditions[0].Status != v1.ConditionTrue &&
							pod.Status.Conditions[0].Reason == v1.PodReasonUnschedulable {
							logger.Infof(ctx, "ignore custom cluster's lb-controller scheduling problem")
							continue
						}
					}
					if strings.Contains(pod.Name, "coredns") {
						if pod.Status.Conditions[0].Status != v1.ConditionTrue &&
							pod.Status.Conditions[0].Reason == v1.PodReasonUnschedulable {
							logger.Infof(ctx, "ignore custom cluster's lb-controller scheduling problem")
							continue
						}
					}

					flag = false
					break
				}
			}

			if flag {
				logger.Infof(ctx, "all pods running in cluster %v", clusterID)
				return nil, nil
			}

		case <-timer.C:
			logger.Errorf(ctx, "timeout(3m) waiting for all pods ready in cluster %v", clusterID)
			return nil, fmt.Errorf("timeout(3m) waiting for all pods ready in cluster %v", clusterID)
		}
	}
}

func (c *checkClusterPods) Clean(ctx context.Context) error {
	return nil
}

func (c *checkClusterPods) Continue(ctx context.Context) bool {
	return false
}

func (c *checkClusterPods) ConfigFormat() string {
	return ""
}
