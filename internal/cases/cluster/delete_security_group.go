package cluster

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

// DeleteSecurityGroup 删除安全组
const (
	DeleteSecurityGroup cases.CaseName = "DeleteSecurityGroup"
)

type deleteSecurityGroup struct {
	base *cases.BaseClient
}

func init() {
	cases.AddCase(context.TODO(), DeleteSecurityGroup, NewDeleteSercurityGroup)
}

func NewDeleteSercurityGroup(ctx context.Context) cases.Interface {
	return &deleteSecurityGroup{}
}

func (c *deleteSecurityGroup) Name() cases.CaseName {
	return DeleteSecurityGroup
}

func (c *deleteSecurityGroup) Desc() string {
	return "删除安全组"
}

func (c *deleteSecurityGroup) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	return
}

func (c *deleteSecurityGroup) Check(ctx context.Context) (resources []cases.Resource, err error) {

	err = c.DeleteNormalSecurityGroup(ctx, "eniSecurityGroupId")
	if err != nil {
		return nil, fmt.Errorf("delete eni security group failed: %v", err)
	}

	err = c.DeleteNormalSecurityGroup(ctx, "masterSecurityGroupId")
	if err != nil {
		return nil, fmt.Errorf("delete master security group failed: %v", err)
	}

	err = c.DeleteNormalSecurityGroup(ctx, "nodeSecurityGroupId")
	if err != nil {
		return nil, fmt.Errorf("delete node security group failed: %v", err)
	}

	return
}

func (c *deleteSecurityGroup) Clean(ctx context.Context) error {
	return nil
}

func (c *deleteSecurityGroup) Continue(ctx context.Context) bool {
	return true
}

func (c *deleteSecurityGroup) ConfigFormat() string {
	return ""
}

func (c *deleteSecurityGroup) DeleteNormalSecurityGroup(ctx context.Context, securityGroupName string) error {
	var securityGroupId string
	if v, ok := c.base.GetSharedMemCache(securityGroupName); ok {
		securityGroupId = v.(string)
	} else {
		logger.Errorf(ctx, "%s security group id not found", securityGroupName)
		return nil
	}

	err := c.base.VPCClient.DeleteNormalSecurityGroup(ctx, securityGroupId, nil)
	if err != nil {
		return fmt.Errorf("delete %s security group failed: %v", securityGroupId, err)
	}
	return nil
}
