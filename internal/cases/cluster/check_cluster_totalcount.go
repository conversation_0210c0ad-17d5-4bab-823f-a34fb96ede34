package cluster

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/retrypolicy"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CheckClusterTotalCount cases.CaseName = "CheckClusterTotalCount"
)

type checkClusterTotalCount struct {
	base   *cases.BaseClient
	config *accountConfig
	client ccev2.Interface
}

type accountConfig struct {
	AccessKeyID     string `json:"accessKeyId"`
	AccessKeySecret string `json:"accessKeySecret"`
	Endpoint        string `json:"endpoint"`
}

func init() {
	cases.AddCase(context.TODO(), CheckClusterTotalCount, NewCheckClusterTotalCount)
}

func NewCheckClusterTotalCount(ctx context.Context) cases.Interface {
	return &checkClusterTotalCount{}
}

func (c *checkClusterTotalCount) Name() cases.CaseName {
	return CheckClusterTotalCount
}

func (c *checkClusterTotalCount) Desc() string {
	return "集群详情接口的总数值校验用例"
}

func (c *checkClusterTotalCount) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	// 初始化参数

	var accConfig accountConfig
	if err = json.Unmarshal(config, &accConfig); err != nil {
		return fmt.Errorf("unmarshal Config error: %v", err)
	}

	c.config = &accConfig
	if c.config.Endpoint == "" || c.config.AccessKeySecret == "" || c.config.AccessKeyID == "" {
		logger.Infof(ctx, "c.config element exits nill, taking c.base.CCEClient")
		c.client = base.CCEClient
		return nil
	}

	c.client = ccev2.NewClient(&bce.Config{
		Credentials: &bce.Credentials{
			AccessKeyID:     c.config.AccessKeyID,
			SecretAccessKey: c.config.AccessKeySecret,
		},
		Endpoint:    c.config.Endpoint,
		Checksum:    true,
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(ctx, 0, 0),
	})

	return
}

func (c *checkClusterTotalCount) Check(ctx context.Context) (resources []cases.Resource, err error) {
	listResponse, err := c.client.ListClusters(ctx, "cluster_name", "", "cluster_name", "ASC", 1, 10, nil)
	if err != nil {
		logger.Errorf(ctx, "listCluster error with page size %d: %v", 10, err)
		return resources, err
	}
	if listResponse == nil {
		err = errors.New("listCluster error: listResponse is nil")
		return resources, err
	}
	totalCount := listResponse.ClusterPage.TotalCount

	// 第二次请求，分页大小为totalCountPageSize10 + 1
	listResponse, err = c.client.ListClusters(ctx, "cluster_name", "", "cluster_name", "ASC", 1, totalCount+1, nil)
	if err != nil {
		logger.Errorf(ctx, "listCluster error with page size %d: %v", totalCount+1, err)
		return resources, err
	}
	if listResponse == nil {
		err = errors.New("listCluster error: listResponse is nil")
		return resources, err
	}

	if totalCount != listResponse.ClusterPage.TotalCount {
		logger.Errorf(ctx, "PageSize: 10, totalCount: %d; Pagessize: %d, totalcount:%d, listCluster error: total count not equal", totalCount, totalCount+1, listResponse.ClusterPage.TotalCount)
		err = errors.New("listCluster error: total count not equal")
		return resources, err
	}
	return resources, nil
}

func (c *checkClusterTotalCount) Clean(ctx context.Context) error {
	return nil
}

func (c *checkClusterTotalCount) Continue(ctx context.Context) bool {
	return true
}

func (c *checkClusterTotalCount) ConfigFormat() string {
	return ""
}
