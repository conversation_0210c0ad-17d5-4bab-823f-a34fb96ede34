/*
modification history
--------------------
2024/12/05, by shimingming01, create
*/

package inspection

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"strconv"
	"time"

	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CreateClusterInspection cases.CaseName = "CreateClusterInspection"
)

const (
	WaitInspectionRunningTimeout  = time.Minute * 5
	WaitInspectionRunningInterval = time.Second * 10
)

type createClusterInspection struct {
	base *cases.BaseClient
}

func init() {
	cases.AddCase(context.TODO(), CreateClusterInspection, NewCreateClusterInspection)
}

func NewCreateClusterInspection(ctx context.Context) cases.Interface {
	return &createClusterInspection{}
}

func (c *createClusterInspection) Name() cases.CaseName {
	return CreateClusterInspection
}

func (c *createClusterInspection) Desc() string {
	return "验证集群巡检执行"
}

func (c *createClusterInspection) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	return
}

func (c *createClusterInspection) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID

	probeClient := c.base.CCEProbeHostClient

	// 开始之前，确保当前集群没有别的巡检任务在跑
	err = c.CheckInspectionRunning(ctx, false)
	if err != nil {
		return
	}

	// 验证巡检项的变更
	err = c.CheckInspectionItems(ctx)
	if err != nil {
		return
	}

	// 获取当前报告列表数量
	listRes, err := probeClient.ListInspectionReport(ctx, clusterID, nil, nil)
	if err != nil {
		err = fmt.Errorf("CCEProbeHostClient.ListInspectionReport failed, err: %v", err)
		return
	}
	totalCount := listRes.TotalCount
	logger.Infof(ctx, "cluster inspection report count: %d", totalCount)

	// 创建巡检任务
	createRes, err := probeClient.CreateInspection(ctx, clusterID, nil)
	if err != nil {
		err = fmt.Errorf("CCEProbeHostClient.CreateInspection failed, err: %v", err)
		return
	}
	logger.Infof(ctx, "create inspection task success, taskID: %s", createRes.TaskId)
	taskID := createRes.TaskId

	time.Sleep(5 * time.Second)

	// 再次获取当前报告列表数量
	listRes, err = probeClient.ListInspectionReport(ctx, clusterID, nil, nil)
	if err != nil {
		err = fmt.Errorf("CCEProbeHostClient.ListInspectionReport failed, err: %v", err)
		return
	}
	if listRes.TotalCount != totalCount+1 {
		err = fmt.Errorf("total count check failed, totalCount: %d, expect: %d", listRes.TotalCount, totalCount+1)
		return
	}

	// 创建任务后，确保当前集群巡检任务已经开始跑
	logger.Infof(ctx, "wait cluster inspection running")
	err = c.CheckInspectionRunning(ctx, true)
	if err != nil {
		return
	}
	// 等待巡检任务结束
	logger.Infof(ctx, "wait cluster inspection finished")
	err = c.CheckInspectionRunning(ctx, false)
	if err != nil {
		return
	}

	// 检查巡检报告的检查项数量是否一致
	logger.Infof(ctx, "check inspection report")
	report, err := probeClient.GetInspectionReport(ctx, clusterID, taskID, nil)
	if err != nil {
		err = fmt.Errorf("CCEProbeHostClient.GetInspectionReport failed, err: %v", err)
		return
	}
	if report == nil {
		err = fmt.Errorf("report is nil for task `%s`", taskID)
		return
	}
	return
}

func (c *createClusterInspection) Clean(ctx context.Context) error {
	return nil
}

func (c *createClusterInspection) Continue(ctx context.Context) bool {
	return true
}

func (c *createClusterInspection) ConfigFormat() string {
	return ""
}

// CheckInspectionRunning 检查巡检任务是否在运行，集群只能有一个正在巡检的任务
func (c *createClusterInspection) CheckInspectionRunning(ctx context.Context, expect bool) (err error) {
	timeoutCtx, cancel := context.WithTimeout(ctx, WaitInspectionRunningTimeout)
	wait.UntilWithContext(timeoutCtx, func(ctx context.Context) {
		isRunning, getErr := c.base.CCEProbeHostClient.IsInspectionRunning(ctx, c.base.ClusterID, nil)
		if getErr != nil {
			logger.Errorf(ctx, "get inspection status error: %v", getErr)
			return
		}
		if isRunning != expect {
			logger.Warnf(ctx, "cluster inspection running status is %v, expect: %v", isRunning, expect)
			return
		}
		cancel()
		return
	}, WaitInspectionRunningInterval)
	if errors.Is(timeoutCtx.Err(), context.DeadlineExceeded) {
		err = fmt.Errorf("wait cluster inspection running status timeout for %s", WaitInspectionRunningTimeout.String())
		return
	}
	return
}

func (c *createClusterInspection) CheckInspectionItems(ctx context.Context) (err error) {
	clusterID := c.base.ClusterID
	probeClient := c.base.CCEProbeHostClient
	// 获取巡检项列表
	items, err := probeClient.GetInspectionItems(ctx, clusterID, nil)
	if err != nil {
		err = fmt.Errorf("CCEProbeHostClient.GetInspectionItems failed, err: %v", err)
		return
	}
	itemMap := make(map[int]bool)
	itemKeys := make([]int, 0, 50)
	for itemKey, itemInfo := range items {
		// 当前巡检项的key存在兼容数据，数字key和字符串key，我们只要字符串key
		_, toIntErr := strconv.ParseInt(itemKey, 10, 64)
		if toIntErr == nil {
			continue
		}
		for _, item := range itemInfo {
			itemMap[item.ItemId] = item.Status
			itemKeys = append(itemKeys, item.ItemId)
		}
	}
	itemCount := len(itemMap)
	if itemCount == 0 {
		err = errors.New("no inspection items founded")
		return
	}
	logger.Infof(ctx, "total inspection items count: %d", itemCount)

	// 随机修改一个巡检项的状态
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	randItemKey := itemKeys[r.Intn(itemCount)]

	logger.Infof(ctx, "update inspection item `%d` status from: %v, to: %v", randItemKey, itemMap[randItemKey], !itemMap[randItemKey])
	itemMap[randItemKey] = !itemMap[randItemKey]
	err = probeClient.UpdateInspectionItems(ctx, clusterID, itemMap, nil)
	if err != nil {
		err = fmt.Errorf("CCEProbeHostClient.UpdateInspectionItems failed, err: %v", err)
		return
	}

	// 重新获取巡检项列表
	items, err = probeClient.GetInspectionItems(ctx, clusterID, nil)
	if err != nil {
		err = fmt.Errorf("CCEProbeHostClient.GetInspectionItems failed, err: %v", err)
		return
	}
	expectedUpdatedValue := itemMap[randItemKey]
	// 重新检查巡检项是否存在，且已经被修改
	err = func(k int, v bool) (err error) {
		var foundUpdatedItemKey bool
		for _, itemInfo := range items {
			for _, item := range itemInfo {
				if item.ItemId == k {
					foundUpdatedItemKey = true
					if item.Status != v {
						err = fmt.Errorf("update inspection item `%d` status failed, expect: %v", k, v)
						return
					}
					return
				}
			}
		}
		if !foundUpdatedItemKey {
			err = fmt.Errorf("check inspection item `%d` status failed, item not found", k)
			return
		}
		return
	}(randItemKey, expectedUpdatedValue)
	return
}
