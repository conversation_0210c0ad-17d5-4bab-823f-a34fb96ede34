/* kubeconfig_update.go */
/*
modification history
--------------------
2024/9/5, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
更新集群访问凭证，校验原先的访问凭证失效，新的访问凭证成功
*/

package k8s

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	RenewKubeconfig cases.CaseName = "RenewKubeconfig"
)

type renewKubeconfig struct {
	base *cases.BaseClient
}

func init() {
	cases.AddCase(context.TODO(), RenewKubeconfig, NewRenewKubeconfig)
}

func NewRenewKubeconfig(ctx context.Context) cases.Interface {
	return &renewKubeconfig{}
}

func (c *renewKubeconfig) Name() cases.CaseName {
	return RenewKubeconfig
}

func (c *renewKubeconfig) Desc() string {
	return "renew kubeconfig"
}

func (c *renewKubeconfig) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	return
}

func (c *renewKubeconfig) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID
	// 校验原先kubeconfig的连通性
	_, err = c.base.K8SClient.CoreV1().Namespaces().List(ctx, v1.ListOptions{})
	if err != nil {
		err = fmt.Errorf("get cluster server version failed: %v", err)
		return
	}
	logger.Infof(ctx, "get cluster server version success")

	// 更新kubeconfig
	_, err = c.base.CCEHostClient.RenewRBACKubeConfig(ctx, &ccev2.KubeConfigRequest{
		ClusterID: clusterID,
		NameSpace: ccev2.ClusterNamespace,
		Renew:     true,
		Role:      ccev2.RoleAdmin, // 所使用aksk是管理员权限
	}, nil)
	if err != nil {
		err = fmt.Errorf("renew kubeconfig failed: %v", err.Error())
		return
	}

	// 老凭证失效
	_, err = c.base.K8SClient.CoreV1().Namespaces().List(ctx, v1.ListOptions{})
	if err == nil {
		err = errors.New("renew kubeconfig finished, but kubeconfig still can access the cluster resource")
		return
	}
	if strings.Contains(err.Error(), "cannot list resource") {
		err = nil
		logger.Infof(ctx, "cluster config cannot list resource, test pass")
	}

	// 新凭证校验连通性
	newClient, err := kube.NewClientByClusterID(ctx, clusterID, c.base.UserKubeConfigType, c.base.CCEClient, 30*time.Second, true)
	if err != nil {
		err = fmt.Errorf("NewClientByClusterID failed: %s", err)
		return nil, err
	}

	_, err = newClient.ClientSet.CoreV1().Namespaces().List(ctx, v1.ListOptions{})
	if err != nil {
		err = fmt.Errorf("get cluster namespace list with new kubeconfig failed: %v", err)
		return
	}
	logger.Infof(ctx, "get cluster namespace list with new kubeconfig success")

	c.base.K8SClient = newClient.ClientSet
	c.base.KubeConfig = newClient.KubeConfig
	logger.Infof(ctx, "kubeconfig:", string(c.base.KubeConfig))

	// 重新初始化PluginClient
	resp, getErr := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	if getErr != nil {
		err = fmt.Errorf("cceClient.GetCluster failed, ClusterID: %s, %w", clusterID, getErr)
		return
	}
	if c.base.Config.PluginConfig != nil {
		c.base.PluginClient, err = plugin.NewClient(ctx, &ccev1.Cluster{
			Spec: ccetypes.ClusterSpec{
				ClusterID:  clusterID,
				UserID:     c.base.Config.UserID,
				K8SVersion: resp.Cluster.Spec.K8SVersion, // 部署回归插件, 无需全量数据, 按实际情况初始化
			},
		}, c.base.KubeConfig, c.base.Config.PluginConfig)
		if err != nil {
			err = fmt.Errorf("plugin.NewClient failed, ClusterID: %s, %w", clusterID, err)
			return
		}
		if err != nil {
			err = fmt.Errorf("plugin.NewHelmClient failed, ClusterID: %s, %w", clusterID, err)
			return
		}
	}
	return
}

func (c *renewKubeconfig) Clean(ctx context.Context) error {
	return nil
}

func (c *renewKubeconfig) Continue(ctx context.Context) bool {
	return true
}

func (c *renewKubeconfig) ConfigFormat() string {
	return ""
}
