// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/06/08 15:48:00, by <EMAIL>, create
*/
/*
检查 kubectl get cs 状态
*/

package k8s

import (
	"context"
	"errors"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/cluster/cluster-controller/controllers"
)

const (
	// K8SComponentStatus - kubectl get cs
	K8SComponentStatus cases.CaseName = "K8SComponentStatus"
)

func init() {
	cases.AddCase(context.TODO(), K8SComponentStatus, NewK8SComponentStatus)
}

type k8sComponentStatus struct {
	base *cases.BaseClient
}

// NewK8SComponentStatus - 实现 Kubectl get cs 检查
func NewK8SComponentStatus(ctx context.Context) cases.Interface {
	return &k8sComponentStatus{}
}

func (c *k8sComponentStatus) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	c.base = base

	return nil
}

func (c *k8sComponentStatus) Name() cases.CaseName {
	return K8SComponentStatus
}

func (c *k8sComponentStatus) Desc() string {
	return "检查集群组件状态"
}

func (c *k8sComponentStatus) Check(ctx context.Context) ([]cases.Resource, error) {
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()
	timer := time.NewTimer(3 * time.Minute)
	defer timer.Stop()
	for {
		select {
		case <-ticker.C:
			ready, err := controllers.CheckK8SClusterStatus(ctx, c.base.ClusterID, c.base.K8SClient)
			if err != nil {
				logger.Errorf(ctx, "CheckK8SClusterStatus failed: %s", err)
			}
			if ready {
				logger.Infof(ctx, "the k8s cluster status is ready")
				return nil, nil
			}
			logger.Warnf(ctx, "the k8s cluster status is not ready, wait")
		case <-timer.C:
			logger.Errorf(ctx, "timeout waiting for k8s cluster status ready")
			return nil, errors.New("timeout waiting for k8s cluster status ready")
		}
	}
}

func (c *k8sComponentStatus) Clean(ctx context.Context) error {
	return nil
}

func (c *k8sComponentStatus) Continue(ctx context.Context) bool {
	return false
}

func (c *k8sComponentStatus) ConfigFormat() string {
	return ""
}
