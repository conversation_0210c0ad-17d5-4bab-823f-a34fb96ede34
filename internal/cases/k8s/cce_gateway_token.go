package k8s

import (
	"context"
	"errors"
	"fmt"
	"time"

	corev1 "k8s.io/api/core/v1"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/yaml"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	// CCEGatewayToken - 检查 cce gateway 在集群创建后正常部署了cce-plugin-token
	CCEGatewayToken cases.CaseName = "CCEGatewayToken"
)

func init() {
	cases.AddCase(context.TODO(), CCEGatewayToken, NewCCEGatewayToken)
}

type cceGatewayToken struct {
	base *cases.BaseClient
}

func NewCCEGatewayToken(ctx context.Context) cases.Interface {
	return &cceGatewayToken{}
}

func (c *cceGatewayToken) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base cannot be nil")
	}
	c.base = base
	return nil
}

func (c *cceGatewayToken) Name() cases.CaseName {
	return CCEGatewayToken
}

func (c *cceGatewayToken) Desc() string {
	return "检查 cce gateway 在集群创建后正常部署了cce-plugin-token"
}

func (c *cceGatewayToken) Check(ctx context.Context) ([]cases.Resource, error) {
	clusterID := c.base.ClusterID

	// 首先获取实际token，因为获取期望token的接口会触发cce-gateway向集群对齐token
	// cce-gateway首次写入token可能由于连接apiserver超时导致写入失败，会自动重试保证最终一致(CCE-2517)
	// 因此最长等待一个cce-gateway的超时+重试间隔(~40s)
	var err error
	var got *corev1.Secret
	for i := 0; i < 4; i++ {
		got, err = c.base.K8SClient.CoreV1().Secrets("kube-system").Get(ctx, "cce-plugin-token", metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "k8sClient get kube-system/cce-plugin-token error: %v", err)
			if !kerrors.IsNotFound(err) {
				return nil, err
			}
			logger.Warnf(ctx, "kube-system/cce-plugin-token not exist, retry after 10s")
			<-time.After(10 * time.Second)
		}
	}
	if err != nil {
		logger.Errorf(ctx, "k8sClient get kube-system/cce-plugin-token error: %v", err)
		return nil, err
	}

	wantSecretYAML, err := c.base.CCEGatewayClient.EnsureTokenSecret(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "fail to ensure latest token from cce-gateway: %v", err)
		return nil, err
	}

	want := new(corev1.Secret)
	if err = yaml.Unmarshal([]byte(wantSecretYAML), want); err != nil {
		logger.Errorf(ctx, "fail to unmarshal token yaml: %v", err)
		return nil, err
	}

	keysToValidate := []string{"token", "expiredAt"}
	for _, key := range keysToValidate {
		gotStr, wantStr := string(want.Data[key]), string((got.Data[key]))
		if gotStr != wantStr {
			return nil, fmt.Errorf("cce-plugin-token.%s mismatch: got = %v, want = %v", key, gotStr, wantStr)
		}
	}

	return nil, nil
}

func (c *cceGatewayToken) Clean(ctx context.Context) error {
	return nil
}

func (c *cceGatewayToken) Continue(ctx context.Context) bool {
	return true
}

func (c *cceGatewayToken) ConfigFormat() string {
	return ""
}
