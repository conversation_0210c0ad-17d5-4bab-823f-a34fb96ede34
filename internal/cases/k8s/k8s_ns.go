// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

package k8s

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/uuid"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	K8SNs cases.CaseName = "K8SNs"
)

func init() {
	cases.AddCase(context.TODO(), K8SNs, NewK8SNs)
}

type k8sNs struct {
	base *cases.BaseClient
}

// NewK8SNs - 实现 Kubectl create edit delete ns
func NewK8SNs(ctx context.Context) cases.Interface {
	return &k8sNs{}
}

func (c *k8sNs) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base

	return nil
}

func (c *k8sNs) Name() cases.CaseName {
	return K8SNs
}

func (c *k8sNs) Desc() string {
	return "创建和删除命名空间"
}

func (c *k8sNs) Check(ctx context.Context) ([]cases.Resource, error) {
	ns := "test-ns-" + string(uuid.NewUUID())
	nsSpec := &corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: ns,
		},
	}
	// create ns
	response, err := c.base.K8SClient.CoreV1().Namespaces().Create(ctx, nsSpec, metav1.CreateOptions{})
	if err != nil {
		logger.Errorf(ctx, "create ns %s, error: %v", ns, err)
		return nil, err
	}
	if response == nil {
		return nil, fmt.Errorf("Create ns %s response is nil", ns)
	}
	logger.Infof(ctx, "create ns %s success", ns)

	// edit ns, patch labels
	nspatch, err := json.Marshal(map[string]any{
		"metadata": map[string]any{
			"labels": map[string]string{"testLabel": "testValue"},
		},
	})
	_, err = c.base.K8SClient.CoreV1().Namespaces().Patch(ctx, ns,
		types.MergePatchType, nspatch, metav1.PatchOptions{})
	if err != nil {
		logger.Errorf(ctx, "edit ns %s error: %v", ns, err)
		return nil, err
	}
	// check labels
	time.Sleep(1 * time.Second)
	gotns, err := c.base.K8SClient.CoreV1().Namespaces().Get(ctx, ns, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "get ns %s, error: %v", ns, err)
		return nil, err
	}
	logger.Infof(ctx, "get ns %s success, label: %v", ns, gotns.ObjectMeta.Labels)
	if val, ok := gotns.ObjectMeta.Labels["testLabel"]; ok && val == "testValue" {
		logger.Infof(ctx, "edit ns %s success", ns)
	} else {
		logger.Errorf(ctx, "edit ns %s failed", ns)
		return nil, fmt.Errorf("edit ns %s failed", ns)
	}

	// delete ns
	err = c.base.K8SClient.CoreV1().Namespaces().Delete(ctx, ns, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf(ctx, "delete ns %s error: %v", ns, err)
		return nil, err
	}
	logger.Infof(ctx, "delete ns %s success", ns)

	return nil, nil
}

func (c *k8sNs) Clean(ctx context.Context) error {
	return nil
}

func (c *k8sNs) Continue(ctx context.Context) bool {
	// 默认不继续
	return true
}

func (c *k8sNs) ConfigFormat() string {
	return ""
}
