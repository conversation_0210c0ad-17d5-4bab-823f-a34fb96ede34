// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
check pull image from private registry
*/

package k8s

import (
	"context"
	b64 "encoding/base64"
	"errors"
	"fmt"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	// K8SDockerRegistrySecret -  pull image from private registry
	K8SDockerRegistrySecret cases.CaseName = "K8SDockerRegistrySecret"

	DeploymentYaml = `apiVersion: apps/v1
kind: Deployment
metadata:
  name: test-e2e-deploy-private-registry
  labels:
    app: test-e2e-deploy-private-registry
spec:
  replicas: 1
  minReadySeconds: 0
  selector:
    matchLabels:
      app: test-e2e-deploy-private-registry
  template:
    metadata:
      labels:
        app: test-e2e-deploy-private-registry
    spec:
      imagePullSecrets:
      - name: test-private-registry
      containers:
        - name: hostnames
          image: registry.baidubce.com/cce-test/serve_hostname`
)

func init() {
	cases.AddCase(context.TODO(), K8SDockerRegistrySecret, NewK8SDockerRegistrySecret)
}

type k8sDockerRegistrySecret struct {
	base *cases.BaseClient
}

// K8SDockerRegistrySecret - check pull image from private registry
func NewK8SDockerRegistrySecret(ctx context.Context) cases.Interface {
	return &k8sDockerRegistrySecret{}
}

func (c *k8sDockerRegistrySecret) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base

	return nil
}

func (c *k8sDockerRegistrySecret) Name() cases.CaseName {
	return K8SDockerRegistrySecret
}

func (c *k8sDockerRegistrySecret) Desc() string {
	return "检测从私有仓库拉取镜像"
}

func (c *k8sDockerRegistrySecret) Check(ctx context.Context) ([]cases.Resource, error) {
	// 创建 docker-registry-secret
	secretData := `{"auths":{"https://registry.baidubce.com":{"username":"%s",
"password":"%s",
"email":"<EMAIL>",
"auth":"%s"}}}`
	userName := "2e1be1eb99e946c3a543ec5a4eaa7d39"
	passWord := "aaLL3210#"
	secretData = fmt.Sprintf(secretData, userName, passWord, b64.StdEncoding.EncodeToString([]byte(userName+":"+passWord)))
	secretName := "test-private-registry"
	secret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      secretName,
			Namespace: "default",
		},
		Type:       "kubernetes.io/dockerconfigjson",
		StringData: map[string]string{".dockerconfigjson": secretData},
	}

	response, err := c.base.K8SClient.CoreV1().Secrets("default").Create(ctx, secret, metav1.CreateOptions{})
	if err != nil {
		logger.Errorf(ctx, "create private registry secret failed, err: %v", err)
		return nil, err
	}
	if response == nil {
		return nil, errors.New("Create private registry response is nil")
	}
	logger.Infof(ctx, "create private registry secret success")

	// 创建 deploy pull image from private registry
	deploymentName := "test-e2e-deploy-private-registry"
	_, err = c.base.AppClient.PostAppResource(ctx, c.base.ClusterID,
		deploymentName, DeploymentYaml, "default", nil)
	if err != nil {
		logger.Errorf(ctx, "private registry deploy created failed: %s", err)
		return nil, err
	}

	// check pull image success, pod running
	timeoutCtx, cancelFn := context.WithTimeout(ctx, time.Minute*10)
	timeout := true
	pollDeployment := func(_ context.Context) {
		deploymentResource, err := c.base.AppClient.GetAppDeploymentByName(ctx, c.base.ClusterID,
			deploymentName, "default", nil)
		if err != nil {
			logger.Errorf(ctx, "get private registry deploy %v err: %v", deploymentName, err)
			return
		}

		if deploymentResource.Status == "Running" &&
			deploymentResource.StatusInfo.Available == deploymentResource.StatusInfo.Replicas {
			cancelFn()
			timeout = false
			logger.Infof(ctx, "private registry deploy %v ready, status: %s", deploymentName, deploymentResource.Status)
		} else {
			timeout = true
		}
	}
	wait.UntilWithContext(timeoutCtx, pollDeployment, time.Second*5)
	if timeout {
		err := fmt.Errorf("wait deploy %s ready timeout", deploymentName)
		logger.Errorf(ctx, "%v", err)
		return nil, err
	}

	// 删除 deploy
	if err := c.base.AppClient.DeleteAppResource(ctx, c.base.ClusterID, "deployment",
		"default", deploymentName, nil); err != nil {
		logger.Errorf(ctx, "delete private registry deploy error: %s", err)
		return nil, err
	}
	logger.Infof(ctx, "delete private registry deploy %s success", deploymentName)

	// 删除 secret
	if err := c.base.AppClient.DeleteAppResource(ctx, c.base.ClusterID, "secret",
		"default", secretName, nil); err != nil {
		logger.Errorf(ctx, "delete private registry secret %s error: %s", secretName, err)
		return nil, err
	}
	logger.Infof(ctx, "delete private registry secret %s success", secretName)

	return nil, nil
}

func (c *k8sDockerRegistrySecret) Clean(ctx context.Context) error {
	return nil
}

func (c *k8sDockerRegistrySecret) Continue(ctx context.Context) bool {
	// 默认不继续
	return true
}

func (c *k8sDockerRegistrySecret) ConfigFormat() string {
	return ""
}
