// Copyright 2022 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2022/11/10 14:52:00, by <PERSON><PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
检查 master pro 组件状态
*/

package k8s

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

var _ cases.Interface = &k8sComponentStatus{}

const (
	// MasterComponentStatus - 元集群中获取用户master组件状态
	MasterComponentStatus cases.CaseName = "MasterComponentStatus"
)

func init() {
	cases.AddCase(context.TODO(), MasterComponentStatus, NewMasterComponentStatus)
}

type masterComponentStatus struct {
	base             *cases.BaseClient
	masterComponents *common.MasterComponent
}

type metaConfig struct {
	ClusterID      string                `json:"ResClusterID"`
	KubeConfigType models.KubeConfigType `json:"KubeConfigType"`
}

// NewMasterComponentStatus - 元集群中获取用户master组件状态
func NewMasterComponentStatus(ctx context.Context) cases.Interface {
	return &masterComponentStatus{}
}

func (c *masterComponentStatus) Init(ctx context.Context, conf []byte, base *cases.BaseClient) error {
	c.base = base
	var cfg metaConfig
	if err := json.Unmarshal(conf, &cfg); err != nil {
		return fmt.Errorf("json.Unmarshal bos config failed: %s", err)
	}
	response, err := c.base.MetaClient.GetAdminKubeConfig(ctx, cfg.ClusterID, cfg.KubeConfigType, nil)
	if err != nil {
		logger.Errorf(ctx, "GetKubeConfig failed: %s", err)
		return err
	}

	// 初始化 K8S Client
	config, err := clientcmd.RESTConfigFromKubeConfig([]byte(response.KubeConfig))
	if err != nil {
		logger.Errorf(ctx, "RESTConfigFromKubeConfig failed: %s", err)
		return err
	}

	config.Timeout = 30 * time.Second
	config.ContentType = "application/vnd.kubernetes.protobuf"

	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		logger.Errorf(ctx, "kubernetes.NewForConfig failed: %s", err)
		return err
	}

	namespace := c.base.ClusterID

	c.masterComponents, err = common.NewMasterComponent(ctx, namespace, clientset)
	if err != nil {
		logger.Errorf(ctx, "new master components check failed:%v", err)
		return nil
	}

	return nil
}

func (c *masterComponentStatus) Name() cases.CaseName {
	return MasterComponentStatus
}

func (c *masterComponentStatus) Desc() string {
	return "检查 master pro 组件状态"
}

func (c *masterComponentStatus) Check(ctx context.Context) ([]cases.Resource, error) {
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()
	timer := time.NewTimer(10 * time.Minute)
	defer timer.Stop()
	for {
		select {
		case <-ticker.C:
			ready, err := c.masterComponents.CheckMasterComponentReady(ctx)
			if err != nil {
				logger.Errorf(ctx, "CheckMasterComponentStatus failed: %s", err)
			}
			if ready {
				logger.Infof(ctx, "the master component status is ready")
				// 检查连通性
				response, err := c.base.CCEClient.GetAdminKubeConfig(ctx, c.base.ClusterID,
					"internal", nil)
				if err != nil {
					logger.Errorf(ctx, "get user cluster kubeconfig failed:%v", err)
					return nil, fmt.Errorf("get user cluster kubeconfig failed:%v", err)
				}
				err = c.masterComponents.CheckMasterComponentHealthy(ctx, []byte(response.KubeConfig))
				if err != nil {
					return nil, err
				}
				return nil, nil
			}
			logger.Warnf(ctx, "the master component is not ready, wait")
		case <-timer.C:
			logger.Errorf(ctx, "timeout waiting for master component status ready")
			return nil, errors.New("timeout waiting for master component status ready")
		}
	}
}

func (c *masterComponentStatus) Clean(ctx context.Context) error {
	return nil
}

func (c *masterComponentStatus) Continue(ctx context.Context) bool {
	return false
}

func (c *masterComponentStatus) ConfigFormat() string {
	return ""
}
