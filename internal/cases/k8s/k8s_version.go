package k8s

import (
	"context"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	K8SVersion cases.CaseName = "K8SVersion"
)

type k8sVersion struct {
	base *cases.BaseClient
}

func init() {
	cases.AddCase(context.TODO(), K8SVersion, NewK8SVersion)
}

func NewK8SVersion(ctx context.Context) cases.Interface {
	return &k8sVersion{}
}

func (c *k8sVersion) Name() cases.CaseName {
	return K8SVersion
}

func (c *k8sVersion) Desc() string {
	return "检查集群版本"
}

func (c *k8sVersion) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	return
}

func (c *k8sVersion) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID
	// 获取集群期望 Version
	response, getErr := c.base.CCEClient.GetCluster(ctx, clusterID, nil)
	if getErr != nil {
		err = fmt.Errorf("ccev2.GetCluster failed: %w", getErr)
		return
	}
	if response == nil {
		err = fmt.Errorf("ccev2.GetCluster %s return nil", clusterID)
		return
	}
	targetK8SVersion := response.Cluster.Spec.K8SVersion
	logger.Infof(ctx, "targetK8SVersion: %s", targetK8SVersion)

	// 获取集群实际 Version
	version, err := c.base.K8SClient.Discovery().ServerVersion()
	if err != nil {
		err = fmt.Errorf("k8sClient.ServerVersion failed: %w", err)
		return
	}
	gotK8SVersion := strings.Trim(version.GitVersion, "v")
	logger.Infof(ctx, "gotK8SVersion: %s", gotK8SVersion)

	if string(targetK8SVersion) != gotK8SVersion {
		err = fmt.Errorf("expect K8SVersion %s, but got %s", targetK8SVersion, gotK8SVersion)
		return
	}

	return
}

func (c *k8sVersion) Clean(ctx context.Context) error {
	return nil
}

func (c *k8sVersion) Continue(ctx context.Context) bool {
	return true
}

func (c *k8sVersion) ConfigFormat() string {
	return ""
}
