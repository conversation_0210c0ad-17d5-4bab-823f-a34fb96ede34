package cases

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	blssdk "github.com/baidubce/bce-sdk-go/services/bls"
	bossdk "github.com/baidubce/bce-sdk-go/services/bos"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/cce-test/e2e-test/conf"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/aiinfra"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	appblbsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appservice"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	bccsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/blb"
	blbsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/blb"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/cce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccegateway"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccemonitor"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/cceprobe"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	ccesdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/cfs"
	cfssdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/cfs"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"
	eipsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"
	enisdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eni"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/helm"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logicbcc"
	logicbccsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logicbcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/retrypolicy"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/vpc"
	vpcsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/vpc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/cmp"
	ccev1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/crd/api/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/errorcode"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/filesystem"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/kubectl"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

type BaseClient struct {
	Credentials        *bce.Credentials
	ClusterID          string
	KubeConfig         string
	KubeClient         *kube.Client // 配置中集群client的统一封装，后续扩展这个client来简化集群资源获取或者操作
	UserKubeConfigType models.KubeConfigType
	Region             string
	ClusterSpec        *ccesdk.ClusterSpec // Status 会变化, 如果需要可使用 cceClient 实时查询

	AIInfraClient      aiinfra.Interface
	CMPClient          cmp.Interface
	CCEClient          ccev2.Interface
	CCEHostClient      ccev2.Interface
	CCEProbeHostClient cceprobe.Interface
	CCEV1Client        cce.Interface
	BLBClient          blbsdk.Interface
	EIPClient          eipsdk.Interface
	VPCClient          vpcsdk.Interface
	BCCClient          bccsdk.Interface
	LogicBCCClient     logicbccsdk.Interface
	ENIClient          enisdk.Interface
	MetaClient         ccev2.Interface
	BOSClient          *bossdk.Client
	CFSClient          cfssdk.Interface
	BLSClient          *blssdk.Client
	PluginClient       plugin.Interface
	KubectlClient      kubectl.Interface
	AppBLBClient       appblbsdk.Interface
	CCEGatewayClient   ccegateway.Client
	K8SClient          kubernetes.Interface
	MonitorClient      ccemonitor.Interface
	AppClient          appservice.Interface
	HelmAccountClient  helm.Interface
	HelmClient         helm.Interface
	FileClient         filesystem.File
	QaDbClient         *models.Client

	// 并发安全的共享内存存储，可用于在case间共享数据, value需要自己断言
	SharedMemCache sync.Map

	// RenewKubeconfig
	Config *conf.Config
}

func NewBaseClient(ctx context.Context, clusterID string, config *conf.Config) (client *BaseClient, err error) {
	if clusterID == "" {
		err = errors.New("clusterID is nil")
	}

	// 初始化 cceClient
	cceClient := ccev2.NewClient(bceConfig(config.Endpoint.CCEV2Endpoint, config))
	cceClient.SetDebug(true)
	cceClient.SetInspect(config.EnableInspect)

	// 初始化 cceHostClient (endpoint 采用vip或hostIP, 应对没有接入OpenAPI的接口)
	cceHostClient := ccev2.NewClient(bceConfig(config.Endpoint.CCEV2HostEndpoint, config))
	cceHostClient.SetDebug(true)
	cceHostClient.SetInspect(config.EnableInspect)

	cceProbeHostClient := cceprobe.NewClient(bceConfig(config.Endpoint.CCEProbeHostEndpoint, config))
	cceProbeHostClient.SetInspect(true)

	ccev1Client := cce.NewClient(&cce.Config{Config: bceConfig(config.Endpoint.CCEServiceInternalEndpoint, config)})

	// 初始化 appblbClient
	appblbClient := appblb.NewClient(bceConfig(config.Endpoint.AppBLBEndpoint, config))
	// appblbClient.SetDebug(true)

	// 初始化 blbClient
	blbClient := blb.NewClient(bceConfig(config.Endpoint.BLBEndpoint, config))
	blbClient.SetDebug(true)

	// 初始化 eipClient
	eipClient := eip.NewClient(bceConfig(config.Endpoint.EIPEndpoint, config))
	eipClient.SetDebug(true)

	// 初始化 vpcClient
	vpcClient := vpc.NewClient(bceConfig(config.Endpoint.VPCEndpoint, config))
	vpcClient.SetDebug(true)

	// 初始化 bccClient
	bccClient := bcc.NewClient(bceConfig(config.Endpoint.BCCEndpoint, config))
	bccClient.SetDebug(true)

	// 初始化 logicbccClient
	logicbccClient := logicbcc.NewClient(bceConfig(config.Endpoint.BCCEndpoint, config))
	logicbccClient.SetDebug(true)

	// 初始化 bosClient（使用sdk）
	bosClient, _ := bossdk.NewClient(config.AccessKey, config.AccessKeySecret, config.Endpoint.BOSEndpoint)

	// 初始化 blsClient（使用sdk）
	blsClient, _ := blssdk.NewClient(config.AccessKey, config.AccessKeySecret, config.Endpoint.BLSEndpoint)

	// 初始化 cfsClient
	cfsClient := cfs.NewClient(bceConfig(config.Endpoint.CFSEndpoint, config))
	cfsClient.SetDebug(true)

	// 初始化 ccegateway client
	ccegwClient := ccegateway.NewClient(ccegateway.NewConfig(bceConfig(config.Endpoint.CCEGatewayEndpoint, config)))
	// ccegwClient.SetDebug(true)

	// 初始化 eni client
	eniClient := enisdk.NewClient(enisdk.NewConfig(bceConfig(config.Endpoint.BCCEndpoint, config)))

	// 初始化 monitor-service client
	ccemonitorClient := ccemonitor.NewClient(bceConfig(config.Endpoint.MonitorEndpoint, config))
	ccemonitorClient.SetDebug(true)
	ccemonitorClient.SetInspect(config.EnableInspect)

	// 初始化 app-service client
	appServiceClient := appservice.NewClient(bceConfig(config.Endpoint.AppServiceEndpoint, config))
	appServiceClient.SetDebug(true)
	appServiceClient.SetInspect(config.EnableInspect)

	// 初始化 helm-account client
	helmAccountClient := helm.NewClient(bceConfig(config.Endpoint.HelmAccountEndpoint, config))
	helmAccountClient.SetDebug(true)
	helmAccountClient.SetInspect(config.EnableInspect)

	// 初始化 app-service client
	helmClient := helm.NewClient(bceConfig(config.Endpoint.HelmEndpoint, config))
	helmClient.SetDebug(true)

	fileClient := filesystem.NewFile()

	// 初始化当前集群的客户端
	kubeClient, err := kube.NewClientByClusterID(ctx, clusterID, config.UserKubeConfigType, cceClient, config.KubeClientTimeout, !config.DisableKubeProtobuf)
	if err != nil {
		err = fmt.Errorf("NewClientByClusterID failed: %v", err)
		return
	}

	// 查询集群信息
	resp, getErr := cceClient.GetCluster(ctx, clusterID, nil)
	if getErr != nil {
		err = fmt.Errorf("cceClient.GetCluster failed, ClusterID: %s, %w", clusterID, getErr)
		return
	}

	// 初始化 plugin client
	var pluginClient plugin.Interface
	var kubectlClient kubectl.Interface

	if config.PluginConfig != nil {
		pluginClient, err = plugin.NewClient(ctx, &ccev1.Cluster{
			Spec: ccetypes.ClusterSpec{
				ClusterID:  clusterID,
				UserID:     config.UserID,
				K8SVersion: resp.Cluster.Spec.K8SVersion, // 部署回归插件, 无需全量数据, 按实际情况初始化
			},
		}, kubeClient.KubeConfig, config.PluginConfig)
		if err != nil {
			err = fmt.Errorf("plugin.NewClient failed, ClusterID: %s, %w", clusterID, err)
			return
		}

		if err != nil {
			err = fmt.Errorf("plugin.NewHelmClient failed, ClusterID: %s, %w", clusterID, err)
			return
		}
		// 初始化 kubectl client
		kubectlClient, err = kubectl.NewClient(ctx, clusterID, config.PluginConfig.KubectlBinPath,
			config.PluginConfig.KubeConfigTempPath)
		if err != nil {
			err = fmt.Errorf("kubectl.NewClient failed, ClusterID: %s, %w", clusterID, err)
			return
		}
	}

	qaDbClient, newDBErr := models.NewClient(ctx, config.QAMySQLEndpoint)
	if newDBErr != nil {
		err = fmt.Errorf("internalmodels.NewClient failed: %w", newDBErr)
		return
	}

	// 初始化 cmp.Interface
	cmpClient, err := cmp.NewClient(ctx, &cmp.Config{
		Region:          config.Region,
		AccessKey:       config.AccessKey,
		AccessKeySecret: config.AccessKeySecret,
		BCCEndpoint:     config.Endpoint.BCCEndpoint,
		BECEndpoint:     config.Endpoint.BECEndpoint,
		CCEV2Endpoint:   config.Endpoint.CCEV2Endpoint,
		KubeConfigType:  config.UserKubeConfigType,
	})
	if err != nil {
		err = fmt.Errorf("cmp.NewClient failed, ClusterID: %s, %w", clusterID, err)
		return
	}

	// 初始化 ai-infra client
	aiInfraClient := aiinfra.NewClient(bceConfig(config.Endpoint.AIInfraEndpoint, config))
	aiInfraClient.SetDebug(true)

	// 初始化 meta client
	ak := config.AccessKey
	sk := config.AccessKeySecret

	metaConfig := config
	metaConfig.AccessKey = config.ResAccessKey
	metaConfig.AccessKeySecret = config.ResAccessKeySecret
	metaClient := ccev2.NewClient(bceConfig(config.Endpoint.CCEV2Endpoint, metaConfig))

	client = &BaseClient{
		Credentials: &bce.Credentials{
			AccessKeyID:     ak,
			SecretAccessKey: sk,
		},
		ClusterID:          clusterID,
		Config:             config,
		KubeConfig:         kubeClient.KubeConfig,
		KubeClient:         kubeClient,
		UserKubeConfigType: config.UserKubeConfigType,
		Region:             config.Region,
		ClusterSpec:        resp.Cluster.Spec,

		K8SClient:          kubeClient.ClientSet,
		AIInfraClient:      aiInfraClient,
		CMPClient:          cmpClient,
		CCEClient:          cceClient,
		CCEHostClient:      cceHostClient,
		CCEProbeHostClient: cceProbeHostClient,
		CCEV1Client:        ccev1Client,
		BLBClient:          blbClient,
		EIPClient:          eipClient,
		VPCClient:          vpcClient,
		BCCClient:          bccClient,
		LogicBCCClient:     logicbccClient,
		BOSClient:          bosClient,
		BLSClient:          blsClient,
		ENIClient:          eniClient,
		MetaClient:         metaClient,
		CFSClient:          cfsClient,
		PluginClient:       pluginClient,
		AppBLBClient:       appblbClient,
		CCEGatewayClient:   ccegwClient,
		KubectlClient:      kubectlClient,
		MonitorClient:      ccemonitorClient,
		AppClient:          appServiceClient,
		HelmAccountClient:  helmAccountClient,
		HelmClient:         helmClient,
		FileClient:         fileClient,
		QaDbClient:         qaDbClient,
	}
	return
}

func bceConfig(endpoint string, config *conf.Config) *bce.Config {
	return &bce.Config{
		Credentials: &bce.Credentials{
			AccessKeyID:     config.AccessKey,
			SecretAccessKey: config.AccessKeySecret,
		},
		Endpoint:    endpoint,
		Checksum:    true,
		Region:      config.Region,
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
	}
}

// CreateInstances - 新增实例
// PS: 即使返回失败, 也必须要处理已经创建出来的资源, 防止泄露.
func (c *BaseClient) CreateInstances(ctx context.Context, clusterID string,
	instanceSets []*ccev2.InstanceSet, record bool) ([]string, error) {
	var errMsg string
	// 获取集群信息，用于事件的记录
	clusterInfo, err := c.CCEClient.GetCluster(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "get cluster %s failed: %s", clusterID, err)
	}

	// 创建 Instance
	response, err := c.CCEClient.CreateInstances(ctx, clusterID, instanceSets, nil)
	if err != nil {
		logger.Errorf(ctx, "CreateInstances failed: %s", err)
		return nil, err
	}

	if response == nil {
		return nil, errors.New("CreateInstances return nil")
	}

	cceInstanceIDs := response.CCEInstanceIDs
	logger.Infof(ctx, "CreateInstances success: %s", cceInstanceIDs)

	events := make([]models.Event, 0, len(cceInstanceIDs))
	waitInstanceTimeout := time.Minute * 15
	// 等待 Instance Ready, 等待 15 min
	timeoutCtx, cancel := context.WithTimeout(ctx, waitInstanceTimeout)
	success := map[string]any{}

	wait.UntilWithContext(timeoutCtx, func(ctx context.Context) {
		if len(success) == len(cceInstanceIDs) {
			logger.Infof(ctx, "Instance %v status running", cceInstanceIDs)
			cancel()
			return
		}
		for _, cceInstanceID := range cceInstanceIDs {
			if _, ok := success[cceInstanceID]; ok {
				continue
			}

			instResp, getErr := c.CCEClient.GetInstance(ctx, clusterID, cceInstanceID, nil)
			if getErr != nil {
				logger.Errorf(ctx, "GetInstance %s failed: %s", cceInstanceID, getErr)
				continue
			}

			instance := instResp.Instance
			if instance.Status.InstancePhase == ccetypes.InstancePhaseCreateFailed {
				logger.Errorf(ctx, "Instance %s created failed", cceInstanceID)
				continue
			} else if instance.Status.InstancePhase != ccetypes.InstancePhaseRunning {
				logger.Infof(ctx, "Instance %s not ready", cceInstanceID)
			} else {
				logger.Infof(ctx, "Instance %s status: %s", cceInstanceID, instance.Status.InstancePhase)
				success[cceInstanceID] = nil

				// 成功扩容的节点进行数据持久化
				if record {
					event := models.Event{
						Region:           c.Region,
						ClusterID:        clusterID,
						ClusterName:      clusterInfo.Cluster.Spec.ClusterName,
						OperationType:    models.ScaleUpNodeOperation,
						OperationSuccess: true,
						AnalysisData: c.Region + "-" + string(instance.Spec.MachineType) + "-" + instance.Status.Machine.InstanceID +
							"-" + string(models.ScaleUpNodeOperation) + "-" + string(models.GetEventOperationResult(true)),
						AnalysisDig: errMsg,
					}
					events = append(events, event)
				}
			}
		}
	}, time.Second*30)

	if len(events) > 0 {
		createEventErr := c.QaDbClient.CreateEvent(events)
		if createEventErr != nil {
			logger.Errorf(ctx, "QaDbClient.CreateEvent failed: %v", createEventErr)
		}
	}

	if errors.Is(timeoutCtx.Err(), context.DeadlineExceeded) {
		logger.Errorf(ctx, "wait instance create timeout for %s", waitInstanceTimeout.String())
	}

	// 返回未 Ready 的 cceInstanceIDs
	if len(success) != len(cceInstanceIDs) {
		failedEvents := make([]models.Event, 0, len(cceInstanceIDs))
		notReady := make([]string, 0, len(cceInstanceIDs))

		for _, id := range cceInstanceIDs {
			if _, ok := success[id]; !ok {
				notReady = append(notReady, id)

				instance, _ := c.CCEClient.GetInstance(ctx, clusterID, id, nil)

				instanceEventSteps, err := c.CCEClient.GetInstanceEventSteps(ctx, id, nil)
				if err != nil {
					logger.Errorf(ctx, "get instance %s event steps failed: %s", id, err)
				}
				for _, step := range instanceEventSteps.Steps {
					// 判断实例状态的30分钟内，失败步骤状态可能还未转为failed，正处于的该步的重试
					if step.StepStatus == ccesdk.StepStatusFailed || step.StepStatus == ccesdk.StepStatusDoing {
						errMsg = string(step.StepName) + " : " + step.ErrMsg
						break
					}
				}

				if record {
					event := models.Event{
						Region:           c.Region,
						ClusterID:        clusterID,
						ClusterName:      clusterInfo.Cluster.Spec.ClusterName,
						OperationType:    models.ScaleUpNodeOperation,
						OperationSuccess: false,
						AnalysisData: c.Region + "-" + string(instance.Instance.Spec.MachineType) + "-" + instance.Instance.Status.Machine.InstanceID +
							"-" + string(models.ScaleUpNodeOperation) + "-" + string(models.GetEventOperationResult(false)),
						AnalysisDig: errMsg,
					}
					failedEvents = append(failedEvents, event)
				}
			}
		}
		if len(failedEvents) > 0 {
			createEventErr := c.QaDbClient.CreateEvent(failedEvents)
			if createEventErr != nil {
				logger.Errorf(ctx, "QaDbClient.CreateEvent failed: %v", createEventErr)
			}
		}

		return cceInstanceIDs, fmt.Errorf("Instance %v phase not running", notReady)
	}

	return cceInstanceIDs, nil
}

// DeleteInstances - 删除实例
func (c *BaseClient) DeleteInstances(ctx context.Context, clusterID string, ids []string,
	deleteOption *ccetypes.DeleteOption, record bool) error {
	if clusterID == "" {
		return errors.New("clusterID is empty")
	}

	if len(ids) == 0 {
		return errors.New("ids is empty")
	}

	var errMsg string
	// 获取集群信息，用于事件的记录
	clusterInfo, err := c.CCEClient.GetCluster(ctx, clusterID, nil)
	if err != nil {
		logger.Errorf(ctx, "get cluster %s failed: %s", clusterID, err)
	}

	// 删除 Instance
	_, err = c.CCEClient.DeleteInstances(ctx, clusterID, &ccev2.DeleteInstancesRequest{
		InstanceIDs:  ids,
		DeleteOption: deleteOption,
	}, nil)
	if err != nil {
		logger.Errorf(ctx, "DeleteInstances %v failed: %s", ids, err)
		return err
	}

	events := make([]models.Event, 0, len(ids))
	waitDeleteTimeout := time.Minute * 10
	timeoutCtx, cancel := context.WithTimeout(ctx, waitDeleteTimeout)
	// 等待 Instance 删除成功, 等待 10 min
	success := map[string]any{}

	wait.UntilWithContext(timeoutCtx, func(ctx context.Context) {
		if len(success) == len(ids) {
			logger.Infof(ctx, "Instances %v has been deleted", ids)
			cancel()
			return
		}

		for _, id := range ids {
			if _, ok := success[id]; ok {
				continue
			}

			resp, getErr := c.CCEClient.GetInstance(ctx, clusterID, id, nil)
			if getErr != nil {
				// 已经删除成功
				if strings.Contains(getErr.Error(), errorcode.NewInstanceNotFound().Code) {
					success[id] = nil

					// 成功缩容的节点进行数据持久化
					if record {
						event := models.Event{
							Region:           c.Region,
							ClusterID:        clusterID,
							ClusterName:      clusterInfo.Cluster.Spec.ClusterName,
							OperationType:    models.ShrinkNodeOperation,
							OperationSuccess: true,
							AnalysisData:     c.Region + "-" + id + "-" + string(models.ShrinkNodeOperation) + "-" + string(models.GetEventOperationResult(true)),
							AnalysisDig:      errMsg,
						}
						events = append(events, event)
					}
					continue
				}

				logger.Errorf(ctx, "GetInstance %s failed: %s", id, err)
				continue
			}

			instance := resp.Instance
			if instance.Status.InstancePhase == ccetypes.InstancePhaseDeleteFailed {
				logger.Errorf(ctx, "Instance %s deleted failed", id)
				continue
			}

			logger.Infof(ctx, "Instance %s still exists", id)
		}
	}, time.Second*30)

	if len(events) > 0 {
		createEventErr := c.QaDbClient.CreateEvent(events)
		if createEventErr != nil {
			logger.Errorf(ctx, "QaDbClient.CreateEvent failed: %v", createEventErr)
		}
	}

	if errors.Is(timeoutCtx.Err(), context.DeadlineExceeded) {
		logger.Errorf(ctx, "wait instance delete timeout for %s", waitDeleteTimeout.String())
	}

	// 返回未释放的 cceInstanceIDs
	if len(success) != len(ids) {
		failedEvents := make([]models.Event, 0, len(ids))
		notReady := make([]string, 0, len(ids))

		for _, id := range ids {
			if _, ok := success[id]; !ok {
				notReady = append(notReady, id)

				instanceEventSteps, getErr := c.CCEClient.GetInstanceEventSteps(ctx, id, nil)
				if getErr != nil {
					logger.Errorf(ctx, "get instance %s event steps failed: %s", id, getErr)
				}
				for _, step := range instanceEventSteps.Steps {
					// 判断实例状态的30分钟内，失败步骤状态可能还未转为failed，正处于的该步的重试
					if step.StepStatus == ccesdk.StepStatusFailed || step.StepStatus == ccesdk.StepStatusDoing {
						errMsg = string(step.StepName) + " : " + step.ErrMsg
						break
					}
				}

				if record {
					event := models.Event{
						Region:           c.Region,
						ClusterID:        clusterID,
						ClusterName:      clusterInfo.Cluster.Spec.ClusterName,
						OperationType:    models.ShrinkNodeOperation,
						OperationSuccess: false,
						AnalysisData:     c.Region + "-" + id + "-" + string(models.ShrinkNodeOperation) + "-" + string(models.GetEventOperationResult(false)),
						AnalysisDig:      errMsg,
					}
					failedEvents = append(failedEvents, event)
				}
			}
		}
		if len(failedEvents) > 0 {
			createEventErr := c.QaDbClient.CreateEvent(failedEvents)
			if createEventErr != nil {
				logger.Errorf(ctx, "QaDbClient.CreateEvent failed: %v", createEventErr)
			}
		}

		return fmt.Errorf("Instance %v not deleted", notReady)
	}

	return nil
}

func (c *BaseClient) GetSharedMemCache(key any) (any, bool) {
	return c.SharedMemCache.Load(key)
}

func (c *BaseClient) SetSharedMemCache(k, v any) {
	c.SharedMemCache.Store(k, v)
}

// 查询组件状态
func (c *BaseClient) GetAddonStatus(ctx context.Context, clusterID string, addonName string) (ccev2.AddOnInstancePhase, error) {
	listAddonResponse, err := c.CCEClient.ListAddOns(ctx, clusterID, &ccev2.ListParams{TargetAddons: addonName}, nil)
	if err != nil {
		logger.Errorf(ctx, "ListAddOns %s failed: %s", addonName, err)
		return "", err
	}
	if listAddonResponse == nil || len(listAddonResponse.Items) == 0 {
		logger.Errorf(ctx, "ListAddOns %s failed, %s not exists", addonName, addonName)
		return "", fmt.Errorf("addonItems is nil")
	}
	if listAddonResponse.Items[0].Instance == nil {
		return ccev2.InstancePhaseUninstalled, nil
	}
	return listAddonResponse.Items[0].Instance.Status.Phase, nil
}
