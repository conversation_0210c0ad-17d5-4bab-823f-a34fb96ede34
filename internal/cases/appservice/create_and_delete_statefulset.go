// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/11/11 , by z<PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
create and delete statefulset
*/

package appservice

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appservice"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CreateAndDeleteStatefulSet cases.CaseName = "CreateAndDeleteStatefulSet"
	StatefulSetYAML                           = `apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: statefulset-test
spec:
  serviceName: "nginx"
  replicas: 1
  selector:
    matchLabels:
      app: nginx # has to match .spec.template.metadata.labels
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: registry.baidubce.com/qa-test/nginx:1.17
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 20
          timeoutSeconds: 5
          periodSeconds: 5
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          timeoutSeconds: 1
          periodSeconds: 5
        ports:
        - containerPort: 80
          name: web`
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CreateAndDeleteStatefulSet, NewCreateAndDeleteStatefulSet)
}

type createAndDeleteStatefulSet struct {
	base   *cases.BaseClient
	config ArmConfig
}

// NewCreateAndDeleteStatefulSet - 测试 StatefulSet
func NewCreateAndDeleteStatefulSet(ctx context.Context) cases.Interface {
	return &createAndDeleteStatefulSet{}
}

func (c *createAndDeleteStatefulSet) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base

	var cfg ArmConfig
	if err := json.Unmarshal(config, &cfg); err != nil {
		return fmt.Errorf("json.Unmarshal bos config failed: %s", err)
	}
	c.config = cfg

	return nil
}

func (c *createAndDeleteStatefulSet) Name() cases.CaseName {
	return CreateAndDeleteStatefulSet
}

func (c *createAndDeleteStatefulSet) Desc() string {
	return "创建和删除 StatefulSet"
}

func (c *createAndDeleteStatefulSet) Check(ctx context.Context) ([]cases.Resource, error) {
	var resources []cases.Resource
	name := "statefulset-test"
	yaml := StatefulSetYAML

	clusterID := c.base.ClusterID
	if c.config.IsArm {
		yaml = strings.ReplaceAll(StatefulSetYAML, "nginx:", "nginx-arm64:")
	}
	// 创建 StatefulSet
	_, err := c.base.AppClient.PostAppResource(ctx, clusterID, name, yaml, DefaultNameSpace, nil)
	if err != nil {
		logger.Errorf(ctx, "PostAppResource failed: %s", err)
		return nil, err
	}

	// 等待负载创建完成
	var statefulSet *appservice.StatefulSetDetail
	timeoutCtx, cancelFn := context.WithTimeout(ctx, time.Minute*10)
	timeout := true

	pollStatefulSet := func(_ context.Context) {
		statefulSet, err = c.base.AppClient.GetStatefulSetByName(ctx, clusterID, name, DefaultNameSpace, nil)
		if err != nil {
			logger.Errorf(ctx, "get statefulSet Resource: %v err: %v", name, err)
			return
		}

		if statefulSet.Status == "running" || statefulSet.Status == "succeeded" {
			cancelFn()
			timeout = false
		} else {
			timeout = true
		}
	}

	wait.UntilWithContext(timeoutCtx, pollStatefulSet, time.Second*5)

	if timeout {
		err := fmt.Errorf("wait statefulSet Resource: %v ready timeout", name)
		logger.Errorf(ctx, "%v", err)
		return nil, err
	}

	if statefulSet.TypeMeta.Kind != "statefulset" {
		logger.Errorf(ctx, "this kind is not statefulset")
		return nil, errors.New("this kind is not statefulset")
	}

	logger.Infof(ctx, "%d pod is desired,  %d pod is pending, %d pod is running", statefulSet.PodInfo.Desired, statefulSet.PodInfo.Pending, statefulSet.PodInfo.Succeeded)

	// totalCount, err := c.base.AppClient.GetAppResource(ctx, clusterID, "statefulset", DefaultNameSpace,nil)
	// if err != nil {
	//	logger.Errorf(ctx, "Get StatefulSet list failed: %s", err)
	//	return nil, err
	// }

	// 删除 StatefulSet
	err = c.base.AppClient.DeleteAppResource(ctx, clusterID, "statefulset", DefaultNameSpace, name, nil)
	if err != nil {
		logger.Errorf(ctx, "Delete StatefulSet failed: %s", err)
		return nil, err
	}

	// time.Sleep(5 * time.Second)
	//
	// // 检查StatefulSet 是否删除成功
	// resourceList, err := c.base.AppClient.GetAppResource(ctx, clusterID, "statefulset",  DefaultNameSpace,nil)
	// if err != nil {
	//	logger.Errorf(ctx, "Get StatefulSet list failed: %s", err)
	//	return nil, err
	// }
	//
	// if resourceList.ListMeta.TotalItems != totalCount.ListMeta.TotalItems-1 {
	//	logger.Errorf(ctx, "Delete StatefulSet failed: %d != %d", resourceList.ListMeta.TotalItems, totalCount.ListMeta.TotalItems-1)
	//	return nil, fmt.Errorf("delete StatefulSet failed:%d != %d", resourceList.ListMeta.TotalItems, totalCount.ListMeta.TotalItems-1)
	// }

	return resources, nil
}

func (c *createAndDeleteStatefulSet) Clean(ctx context.Context) error {
	return nil
}

func (c *createAndDeleteStatefulSet) Continue(ctx context.Context) bool {
	return true
}

func (c *createAndDeleteStatefulSet) ConfigFormat() string {
	return ""
}
