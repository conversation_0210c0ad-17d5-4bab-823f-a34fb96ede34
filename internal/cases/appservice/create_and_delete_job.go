// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/17 , by z<PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
create and delete Job
*/

package appservice

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appservice"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CreateAndDeleteJob cases.CaseName = "CreateAndDeleteJob"
	JobYAML                           = `apiVersion: batch/v1
kind: Job
metadata:
  name: job-test
spec:
  completions: 1 # One-off job
  parallelism: 1 # Non-parallel
  backoffLimit: 6 # default number of retries when job is failed
  template:
    metadata:
      name: pi
    spec:
      containers:
      - name: pi
        image: registry.baidubce.com/qa-test/perl:5.18.4-threaded-buster
        command: ["perl",  "-Mbignum=bpi", "-wle", "print bpi(2000)"]
      restartPolicy: Never`
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CreateAndDeleteJob, NewCreateAndDeleteJob)
}

var _ cases.Interface = &createAndDeleteJob{}

type createAndDeleteJob struct {
	base   *cases.BaseClient
	config ArmConfig
}

// NewTemplate - 测试案例
func NewCreateAndDeleteJob(ctx context.Context) cases.Interface {
	return &createAndDeleteJob{}
}

func (c *createAndDeleteJob) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base

	var cfg ArmConfig
	if err := json.Unmarshal(config, &cfg); err != nil {
		return fmt.Errorf("json.Unmarshal bos config failed: %s", err)
	}
	c.config = cfg

	return nil
}

func (c *createAndDeleteJob) Name() cases.CaseName {
	return CreateAndDeleteJob
}

func (c *createAndDeleteJob) Desc() string {
	return "创建和删除Job"
}

func (c *createAndDeleteJob) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	jobName := "job-test"
	yaml := JobYAML

	clusterID := c.base.ClusterID
	if c.config.IsArm {
		yaml = strings.ReplaceAll(JobYAML, "perl:", "perl-arm64:")
	}
	// 建立Job
	_, err := c.base.AppClient.PostAppResource(ctx, clusterID, jobName, yaml, DefaultNameSpace, nil)
	if err != nil {
		logger.Errorf(ctx, "Create Job failed: %s", err)
		return nil, err
	}

	// 等待负载创建完成
	var jobResource *appservice.JobDetail
	timeoutCtx, cancelFn := context.WithTimeout(ctx, time.Minute*10)
	timeout := true

	pollJob := func(_ context.Context) {
		jobResource, err = c.base.AppClient.GetJobByName(ctx, clusterID, jobName, DefaultNameSpace, nil)
		if err != nil {
			logger.Errorf(ctx, "get job Resource: %v err: %v", jobName, err)
			return
		}

		if jobResource.Status == "running" || jobResource.Status == "succeeded" {
			cancelFn()
			timeout = false
		} else {
			timeout = true
		}
	}

	wait.UntilWithContext(timeoutCtx, pollJob, time.Second*5)

	if timeout {
		err := fmt.Errorf("wait job Resource: %v ready timeout", jobName)
		logger.Errorf(ctx, "%v", err)
		return nil, err
	}

	if jobResource.TypeMeta.Kind != "job" {
		logger.Errorf(ctx, "this kind is not job")
		return nil, errors.New("this kind is not job")
	}
	logger.Infof(ctx, "%d pod is desired,  %d pod is pending, %d pod is running", jobResource.PodInfo.Desired, jobResource.PodInfo.Pending, jobResource.PodInfo.Succeeded)

	// 删除Job
	err = c.base.AppClient.DeleteAppResource(ctx, clusterID, "job", DefaultNameSpace, jobName, nil)
	if err != nil {
		logger.Errorf(ctx, "Delete Job failed: %s", err)
		return nil, err
	}

	// time.Sleep(5 * time.Second)

	// 检查Job是否删除成功
	// resourceList, err := c.base.AppClient.GetAppResource(ctx, clusterID, "job", DefaultNameSpace,nil)
	// if err != nil {
	//	logger.Errorf(ctx, "Get Job list failed: %s", err)
	//	return nil, err
	// }
	// if resourceList.ListMeta.TotalItems != totalCount.ListMeta.TotalItems-1 {
	//	logger.Errorf(ctx, "Delete Job failed: %d != %d", resourceList.ListMeta.TotalItems, totalCount.ListMeta.TotalItems-1)
	//	return nil, fmt.Errorf("delete Job failed: %d != %d", resourceList.ListMeta.TotalItems, totalCount.ListMeta.TotalItems-1)
	// }
	return resources, nil
}

func (c *createAndDeleteJob) Clean(ctx context.Context) error {
	return nil
}

func (c *createAndDeleteJob) Continue(ctx context.Context) bool {
	return true
}

func (c *createAndDeleteJob) ConfigFormat() string {
	return ""
}
