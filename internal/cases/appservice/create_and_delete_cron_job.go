// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2023/12/12 , by zhang<PERSON><PERSON>@baidu.com, create
*/
/*
create and delete CronJob, check list/detail/raw/delete api
*/

package appservice

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appservice"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	CreateAndDeleteCronJob cases.CaseName = "CreateAndDeleteCronJob"
	CronJobYAML                           = `apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: autotest-cronjob
spec:
  schedule: "*/1 * * * *"
  suspend: false
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  concurrencyPolicy: "Forbid"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: hello
              image: hub.baidubce.com/cce/busybox:latest
              args:
              - /bin/sh
              - -c
              - date; echo Hello from the Kubernetes cluster
          restartPolicy: OnFailure`
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CreateAndDeleteCronJob, NewCreateAndDeleteCronJob)
}

var _ cases.Interface = &createAndDeleteCronJob{}

type createAndDeleteCronJob struct {
	base   *cases.BaseClient
	config ArmConfig
}

// NewTemplate - 测试案例
func NewCreateAndDeleteCronJob(ctx context.Context) cases.Interface {
	return &createAndDeleteCronJob{}
}

func (c *createAndDeleteCronJob) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base

	var cfg ArmConfig
	if err := json.Unmarshal(config, &cfg); err != nil {
		return fmt.Errorf("json.Unmarshal bos config failed: %s", err)
	}
	c.config = cfg

	return nil
}

func (c *createAndDeleteCronJob) Name() cases.CaseName {
	return CreateAndDeleteCronJob
}

func (c *createAndDeleteCronJob) Desc() string {
	return "创建和删除CronJob"
}

func (c *createAndDeleteCronJob) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	cronJobName := "autotest-cronjob"
	yaml := CronJobYAML

	clusterID := c.base.ClusterID
	// if c.config.IsArm {
	// 	yaml = strings.Replace(JobYAML, "perl:", "perl-arm64:", -1)
	// }
	isK8SVersionAfter_1_24, _ := c.base.ClusterSpec.K8SVersion.IsAfter(ccetypes.K8S_1_24_4)
	if isK8SVersionAfter_1_24 {
		yaml = strings.ReplaceAll(yaml, "apiVersion: batch/v1beta1", "apiVersion: batch/v1")
	}
	logger.Infof(ctx, "yaml: %s", yaml)
	// 创建CronJob
	_, err := c.base.AppClient.PostAppResource(ctx, clusterID, cronJobName, yaml, DefaultNameSpace, nil)
	if err != nil {
		logger.Errorf(ctx, "Create CronJob failed: %s", err)
		return nil, err
	}
	// 获取当前时间4分钟后的时间并舍入至最近的分钟，并等待（为了避免请求job和pod列表时恰好是保留任务回收的时刻）
	now := time.Now()
	destTime := now.Add(4 * time.Minute).Truncate(time.Minute)
	time.Sleep(destTime.Sub(now))
	time.Sleep(30 * time.Second)

	// List CronJob
	cronJobList, err := c.base.AppClient.GetCronJobNamespacedList(ctx, clusterID, DefaultNameSpace, nil)
	if err != nil {
		logger.Errorf(ctx, "GetCronJobNamespacedList failed: %s", err)
		return nil, fmt.Errorf("GetCronJobNamespacedList failed: %s", err)
	}
	found := false
	for _, cronJobItem := range cronJobList.CronJobs {
		if cronJobName == cronJobItem.ObjectMeta.Name {
			found = true
			break
		}
	}
	if !found {
		return nil, fmt.Errorf("cronJob %s not found in list result", cronJobName)
	}

	// Get CronJob Detail
	cronJobDetail, err := c.base.AppClient.GetCronJobByName(ctx, clusterID, cronJobName, DefaultNameSpace, nil)
	if err != nil {
		logger.Errorf(ctx, "GetCronJobByName %s failed: %s", cronJobName, err)
		return nil, fmt.Errorf("GetCronJobByName %s failed: %s", cronJobName, err)
	}
	if cronJobDetail.ObjectMeta.Name != cronJobName {
		return nil, fmt.Errorf("cronJob %s not found in list result", cronJobName)
	}
	// lastSuccessfulTime := cronJobDetail.LastSchedule
	// now := time.Now()
	// t := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), 0, 0, now.Location()).UTC().Format("2006-01-02T15:04:05Z")
	// if lastSuccessfulTime != t {
	// 	return nil, fmt.Errorf("cronJob %s lastSuccessfulTime[%s] is not correct, expect now time: %s", cronJobName, lastSuccessfulTime, t)
	// }

	// Get CronJob raw yaml
	_, err = c.base.AppClient.GetResourceYAML(ctx, clusterID, "cronjob", DefaultNameSpace, cronJobName, nil)
	if err != nil {
		logger.Errorf(ctx, "CronJob GetResourceYAML %s failed: %s", cronJobName, err)
		return nil, fmt.Errorf("CronJob GetResourceYAML %s failed: %s", cronJobName, err)
	}

	// List Job (获取定时任务的job列表)
	jobList := []appservice.JobDetail{}
	jobListResp, err := c.base.AppClient.GetJobNamespacedList(ctx, clusterID, DefaultNameSpace, nil)
	if err != nil {
		logger.Errorf(ctx, "GetCronJobNamespacedList failed: %s", err)
		return nil, fmt.Errorf("GetCronJobNamespacedList failed: %s", err)
	}
	successCount := 0
	for _, jobItem := range jobListResp.Jobs {
		logger.Infof(ctx, "job %s status: %s", jobItem.ObjectMeta.Name, jobItem.Status)
		if len(jobItem.ObjectMeta.OwnerReferences) > 0 && jobItem.ObjectMeta.OwnerReferences[0].Name == cronJobName && jobItem.Status == "succeeded" {
			successCount++
			jobList = append(jobList, jobItem)
		}
	}

	// Get Job Detail
	// 在1.28版本中，job拥有的pod使用的标签改为 batch.kubernetes.io/controller-uid和batch.kubernets.io/job-name，而为了向前兼容性job拥有的pod使用的标签仍包含 controller-uid 和 job-name
	// 但 job yaml里 spec.selector.matchLabels: batch.kubernetes.io/controller-uid，因此cce-app-service里对比标签的时候沿用原先的controller-uid，导致获取到的pod数量为0
	// 见：https://console.cloud.baidu-int.com/devops/icafe/issue/CCE-15354/show?source=copy-shortcut
	// 因此，加上pod数校验
	if len(jobList) == 0 {
		return nil, fmt.Errorf("cronJob %s not found in list result", cronJobName)
	}
	jobName := jobList[0].ObjectMeta.Name
	jobDetailResp, err := c.base.AppClient.GetJobByName(ctx, clusterID, jobName, DefaultNameSpace, nil)
	if err != nil {
		logger.Errorf(ctx, "get job [%v] detail failed:  err: %v", jobName, err)
		return nil, fmt.Errorf("get job detail of cronJob %s failed: %v", cronJobName, err)
	}
	if jobDetailResp.ObjectMeta.Name != jobName {
		return nil, fmt.Errorf("check job detail of cronJob %s failed: name not match", cronJobName)
	}
	if jobDetailResp.PodInfo.Desired != 1 {
		return nil, fmt.Errorf("check job detail of cronJob %s failed: PodInfo.Desired not match, expect 1, got %d", cronJobName, jobDetailResp.PodInfo.Desired)
	}
	if jobDetailResp.PodInfo.Running != 1 && jobDetailResp.PodInfo.Succeeded != 1 && jobDetailResp.PodInfo.Pending != 1 {
		return nil, fmt.Errorf("check job detail of cronJob %s failed: PodInfo.Running/Succeeded/Pending not match, expect 1, got running: %d succeeded: %d pending: %d",
			cronJobName, jobDetailResp.PodInfo.Running, jobDetailResp.PodInfo.Succeeded, jobDetailResp.PodInfo.Pending)
	}

	// 保留成功任务数：3，保留失败任务数：3
	if successCount != 3 {
		return nil, fmt.Errorf("cronJob %s successful jobs history count %d not as expect successfulJobsHistoryLimit 3", cronJobName, successCount)
	}
	sort.Slice(jobList, func(i, j int) bool {
		return jobList[j].ObjectMeta.CreationTimestamp.Sub(jobList[i].ObjectMeta.CreationTimestamp) > 0
	})
	for i := 1; i < len(jobList); i++ {
		// check whether there is a difference of 1 minute between jobs (including 59 minutes and 0 minutes)
		if (jobList[i-1].ObjectMeta.CreationTimestamp.Minute()+1)%60 != jobList[i].ObjectMeta.CreationTimestamp.Minute() {
			logger.Infof(ctx, "job [%s].ObjectMeta.CreationTimestamp: %s", jobList[i-1].ObjectMeta.Name, jobList[i-1].ObjectMeta.CreationTimestamp)
			logger.Infof(ctx, "job [%s].ObjectMeta.CreationTimestamp: %s", jobList[i].ObjectMeta.Name, jobList[i].ObjectMeta.CreationTimestamp)
			return nil, fmt.Errorf("cronJob %s schedule jobs not as expect: */1 * * * * ", cronJobName)
		}
	}

	// Check whether the pod creation meets the expected scheduling
	podList := []appservice.Pod{}
	podListResp, err := c.base.AppClient.GetPodResourceByNamespace(ctx, clusterID, DefaultNameSpace, nil)
	if err != nil {
		logger.Errorf(ctx, "GetCronJobNamespacedList failed: %s", err)
		return nil, fmt.Errorf("GetCronJobNamespacedList failed: %s", err)
	}
	successPodCount := 0
	for _, podItem := range podListResp.Pods {
		// logger.Infof(ctx, "podItem: %s %s %s", podItem.ObjectMeta.Name, podItem.ObjectMeta.OwnerReferences[0].Name, podItem.PodStatus.PodPhase)
		logger.Infof(ctx, "pod %s phase: %s", podItem.ObjectMeta.Name, podItem.PodStatus.PodPhase)
		if strings.Contains(podItem.ObjectMeta.Name, cronJobName) && podItem.PodStatus.PodPhase == "Succeeded" {
			successPodCount++
			podList = append(podList, podItem)
		}
	}
	if successPodCount != 3 {
		return nil, fmt.Errorf("cronJob %s successful pods history count %d not as expect successfulJobsHistoryLimit 3", cronJobName, successPodCount)
	}
	sort.Slice(podList, func(i, j int) bool {
		return podList[j].ObjectMeta.CreationTimestamp.Sub(podList[i].ObjectMeta.CreationTimestamp) > 0
	})
	for i := 1; i < len(podList); i++ {
		logger.Infof(ctx, "pod [%s].ObjectMeta.CreationTimestamp: %s", podList[i-1].ObjectMeta.Name, podList[i-1].ObjectMeta.CreationTimestamp)
		logger.Infof(ctx, "pod [%s].ObjectMeta.CreationTimestamp: %s", podList[i].ObjectMeta.Name, podList[i].ObjectMeta.CreationTimestamp)
		if (podList[i-1].ObjectMeta.CreationTimestamp.Minute()+1)%60 != podList[i].ObjectMeta.CreationTimestamp.Minute() {
			return nil, fmt.Errorf("cronJob %s schedule pods not as expect: */1 * * * * ", cronJobName)
		}
	}

	// TODO：更新 CronJob 标签、注释
	// TODO：更新 CronJob schedule
	// TODO：停止 CronJob，检查suspend，以及后续无job生成
	// TODO：运行 CronJob，检查suspend，以及后续开始有job生成

	// 删除 CronJob
	err = c.base.AppClient.DeleteAppResource(ctx, clusterID, "cronjob", DefaultNameSpace, cronJobName, nil)
	if err != nil {
		logger.Errorf(ctx, "Delete CronJob %s failed: %s", err)
		return nil, fmt.Errorf("Delete CronJob %s failed: %s", cronJobName, err)
	}

	// time.Sleep(5 * time.Second)

	// 检查CronJob是否删除成功, 以及对应的jobs、pods是否都删除
	// resourceList, err := c.base.AppClient.GetAppResource(ctx, clusterID, "job", DefaultNameSpace,nil)
	// if err != nil {
	//	logger.Errorf(ctx, "Get Job list failed: %s", err)
	//	return nil, err
	// }
	// if resourceList.ListMeta.TotalItems != totalCount.ListMeta.TotalItems-1 {
	//	logger.Errorf(ctx, "Delete Job failed: %d != %d", resourceList.ListMeta.TotalItems, totalCount.ListMeta.TotalItems-1)
	//	return nil, fmt.Errorf("delete Job failed: %d != %d", resourceList.ListMeta.TotalItems, totalCount.ListMeta.TotalItems-1)
	// }
	return resources, nil
}

func (c *createAndDeleteCronJob) Clean(ctx context.Context) error {
	return nil
}

func (c *createAndDeleteCronJob) Continue(ctx context.Context) bool {
	return true
}

func (c *createAndDeleteCronJob) ConfigFormat() string {
	return ""
}
