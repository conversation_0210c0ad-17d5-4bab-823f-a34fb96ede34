package appservice

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appservice"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CreateAndDeleteNS cases.CaseName = "CreateAndDeleteNS"
	NameSpaceYAML                    = `apiVersion: v1
kind: Namespace
metadata:
  name: ns-test
  labels: 
    key01: ""
    key02: "values"`

	DeployYaml = `apiVersion: apps/v1
kind: Deployment
metadata:
  name: deploy-test
  labels:
    app: nginx
spec:
  replicas: 1
  minReadySeconds: 0
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      restartPolicy: Always
      containers:
        - name: nginx
          image: registry.baidubce.com/qa-test/nginx:1.17
          imagePullPolicy: Always
          ports:
            - containerPort: 80
          resources: # 资源限制
            limits:
              cpu: 250m
              memory: 512Mi
            requests:
              cpu: 200m
              memory: 500Mi`
)

type createAndDeleteNS struct {
	base   *cases.BaseClient
	config ArmConfig
}

func init() {
	cases.AddCase(context.TODO(), CreateAndDeleteNS, NewCreateAndDeleteNS)
}

func NewCreateAndDeleteNS(ctx context.Context) cases.Interface {
	return &createAndDeleteNS{}
}

func (c *createAndDeleteNS) Name() cases.CaseName {
	return CreateAndDeleteNS
}

func (c *createAndDeleteNS) Desc() string {
	return "创删命名空间以及资源限制用例"
}

func (c *createAndDeleteNS) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base

	var cfg ArmConfig
	if err = json.Unmarshal(config, &cfg); err != nil {
		return fmt.Errorf("json.Unmarshal arm config failed: %s", err)
	}
	c.config = cfg

	return
}

func (c *createAndDeleteNS) Check(ctx context.Context) (resources []cases.Resource, err error) {
	namespaceName := "ns-test"
	yaml := NameSpaceYAML
	deploymentName := "deploy-test"
	deployYaml := DeployYaml
	clusterID := c.base.ClusterID

	if c.config.IsArm {
		deployYaml = strings.ReplaceAll(DeployYaml, "qa-test/nginx:", "qa-test/nginx-arm64:")
	}
	// 创建 namespace
	_, postErr := c.base.AppClient.PostAppResource(ctx, clusterID, namespaceName, yaml, namespaceName, nil)
	if postErr != nil {
		err = fmt.Errorf("failed to create namespace %s: %v", namespaceName, postErr)
		return
	}
	logger.Infof(ctx, "created namespace %s successfully", namespaceName)

	_, nsErr := c.base.AppClient.GetNamespaceList(ctx, clusterID, nil)
	if nsErr != nil {
		err = fmt.Errorf("get namespace list failed, %v", nsErr)
		return
	}

	// 创建 ResourceQuota
	resourceQuotaName := namespaceName
	quotaSpec := &appservice.DeployResourceQuotaSpec{
		Name:      resourceQuotaName,
		Namespace: namespaceName,
		Spec: &corev1.ResourceQuotaSpec{
			Hard: corev1.ResourceList{
				corev1.ResourceRequestsCPU:    *resource.NewMilliQuantity(50, resource.DecimalSI),
				corev1.ResourceRequestsMemory: resource.MustParse("50Mi"),
				corev1.ResourceLimitsCPU:      *resource.NewMilliQuantity(100, resource.DecimalSI),
				corev1.ResourceLimitsMemory:   resource.MustParse("100Mi"),
			},
		},
	}

	_, createRQErr := c.base.AppClient.CreateResourceQuota(ctx, clusterID, quotaSpec, nil)
	if createRQErr != nil {
		err = fmt.Errorf("failed to create resource quota: %v", createRQErr)
		return
	}
	logger.Infof(ctx, "created resource quota %s successfully", resourceQuotaName)

	// 创建 Deployment
	_, err = c.base.AppClient.PostAppResource(ctx, clusterID, deploymentName, deployYaml, namespaceName, nil)
	if err != nil {
		logger.Errorf(ctx, "Deployment creation failed due to: %v", err)
		return nil, err
	}

	timeoutCtx, cancelFn := context.WithTimeout(ctx, 10*time.Minute)
	wait.UntilWithContext(timeoutCtx, func(ctx context.Context) {
		deploymentList, err := c.base.AppClient.GetAppDeploymentList(ctx, clusterID, namespaceName, nil)
		if err != nil {
			logger.Errorf(ctx, "GetAppDeploymentList failed: %v", err)
			return
		}
		for _, deployment := range deploymentList.Deployments {
			for _, cond := range deployment.DeploymentStatus.Conditions {
				logger.Infof(ctx, "Checking condition with message: %s", cond.Message)
				if strings.Contains(cond.Message, "exceeded quota") {
					logger.Infof(ctx, "Deployment is exceeded quota")
					cancelFn()
					return
				}
			}
		}
	}, time.Second*5)

	// 检查是否因为超时退出
	if errors.Is(timeoutCtx.Err(), context.DeadlineExceeded) {
		err = fmt.Errorf("wait for Deployment instance to exceed quota timed out")
		return
	}

	// 删除 Deployment
	if err = c.base.AppClient.DeleteAppResource(ctx, clusterID, "deployment", namespaceName, deploymentName, nil); err != nil {
		err = fmt.Errorf("DeleteAppResource failed: %v", err)
		return
	}

	// 删除 namespace
	if err = c.base.AppClient.PostDeleteNameSpace(ctx, clusterID, namespaceName, nil); err != nil {
		err = fmt.Errorf("Delete namespace failed: %v ", err)
		return
	}
	return
}

func (c *createAndDeleteNS) Clean(ctx context.Context) error {
	return nil
}

func (c *createAndDeleteNS) Continue(ctx context.Context) bool {
	return true
}

func (c *createAndDeleteNS) ConfigFormat() string {
	return ""
}
