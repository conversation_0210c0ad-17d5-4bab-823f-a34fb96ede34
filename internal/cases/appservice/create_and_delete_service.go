// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/17 , by <PERSON><PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
create and delete Job
*/

package appservice

import (
	"context"
	"errors"
	"fmt"
	"time"

	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appservice"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CreateAndDeleteService cases.CaseName = "CreateAndDeleteService"

	// ServiceYAML default template of Service(ClusterIP)
	ServiceYAML = `apiVersion: v1
kind: Service
metadata:
  name: service-autotest-type-cluster-ip
  annotations:
    prometheus.io/scrape: "true"
    # service.beta.kubernetes.io/cce-load-balancer-internal-vpc: "true" # LB Service 不分配 EIP
spec:
  selector:
    app: nginx
  type: ClusterIP
  sessionAffinity: None # 默认值，不使用客户端会话亲和性
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80`
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CreateAndDeleteService, NewCreateAndDeleteService)
}

type createAndDeleteService struct {
	base      *cases.BaseClient
	clusterID string
}

// NewTemplate - 测试案例
func NewCreateAndDeleteService(ctx context.Context) cases.Interface {
	return &createAndDeleteService{}
}

func (c *createAndDeleteService) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base

	return nil
}

func (c *createAndDeleteService) Name() cases.CaseName {
	return CreateAndDeleteService
}

func (c *createAndDeleteService) Desc() string {
	return "创建和删除Service"
}

func (c *createAndDeleteService) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	name := "service-autotest-type-cluster-ip"

	clusterID := c.base.ClusterID
	// 建立service
	_, err := c.base.AppClient.PostAppResource(ctx, clusterID, name, ServiceYAML, DefaultNameSpace, nil)
	if err != nil {
		logger.Errorf(ctx, "Create Service failed: %s", err)
		return nil, err
	}

	// 等待负载创建完成
	var service *appservice.ServiceDetail
	timeoutCtx, cancelFn := context.WithTimeout(ctx, time.Minute*10)
	timeout := true

	pollService := func(_ context.Context) {
		service, err = c.base.AppClient.GetServiceByName(ctx, clusterID, name, DefaultNameSpace, nil)
		if err != nil {
			logger.Errorf(ctx, "get service Resource: %v err: %v", name, err)
			return
		}

		if service.ClusterIP != "" {
			cancelFn()
			timeout = false
		} else {
			timeout = true
		}
	}

	wait.UntilWithContext(timeoutCtx, pollService, time.Second*5)

	if timeout {
		err := fmt.Errorf("wait service Resource: %v ready timeout", name)
		logger.Errorf(ctx, "%v", err)
		return nil, err
	}

	if service.Type != "ClusterIP" {
		logger.Errorf(ctx, "this kind is not service")
		return nil, errors.New("this kind is not service")
	}
	logger.Infof(ctx, "clusterIP is :%s", service.ClusterIP)

	// if service.PodList.Pods[0].PodStatus.Status != "running" {
	//	logger.Errorf(ctx, "the pod is not running")
	//	return nil, err
	// }

	// totalCount, err := c.base.AppClient.GetAppResource(ctx, clusterID, "service", DefaultNameSpace,nil)
	// if err != nil {
	//	logger.Errorf(ctx, "Get Service list failed: %s", err)
	//	return nil, err
	// }

	// 删除Service
	err = c.base.AppClient.DeleteAppResource(ctx, clusterID, "service", DefaultNameSpace, name, nil)
	if err != nil {
		logger.Errorf(ctx, "Delete Service failed: %s", err)
		return nil, err
	}

	// time.Sleep(5 * time.Second)

	// 检查Service是否删除成功
	// resourceList, err := c.base.AppClient.GetAppResource(ctx, clusterID, "service",  DefaultNameSpace,nil)
	// if err != nil {
	//	logger.Errorf(ctx, "Get Service list failed: %s", err)
	//	return nil, err
	// }
	// if resourceList.ListMeta.TotalItems != totalCount.ListMeta.TotalItems-1 {
	//	logger.Errorf(ctx, "Delete Service failed : %d != %d", resourceList.ListMeta.TotalItems, totalCount.ListMeta.TotalItems-1)
	//	return nil, fmt.Errorf("delete Service failed : %d != %d", resourceList.ListMeta.TotalItems, totalCount.ListMeta.TotalItems-1)
	// }
	return resources, nil
}

func (c *createAndDeleteService) Clean(ctx context.Context) error {
	return nil
}

func (c *createAndDeleteService) Continue(ctx context.Context) bool {
	return true
}

func (c *createAndDeleteService) ConfigFormat() string {
	return ""
}
