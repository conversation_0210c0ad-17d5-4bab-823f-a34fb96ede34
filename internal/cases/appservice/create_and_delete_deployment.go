// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/17 , by z<PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
create and delete deployment
*/

package appservice

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appservice"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	// CreateAndDeleteDeployment
	DefaultNameSpace = "default"

	ExpectNumber = 2

	CreateAndDeleteDeployment cases.CaseName = "CreateAndDeleteDeployment"

	DeploymentYAML = `apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment-test
  labels:
    app: nginx
spec:
  replicas: 2
  minReadySeconds: 0
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      restartPolicy: Always
      containers:
        - name: nginx
          image: registry.baidubce.com/qa-test/nginx:1.17
          imagePullPolicy: Always
          ports:
            - containerPort: 80
          resources:
            limits:
              cpu: 250m
              memory: 512Mi
            requests:
              cpu: 20m
              memory: 512Mi
          livenessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 20
            timeoutSeconds: 5
            periodSeconds: 5
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 5`
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CreateAndDeleteDeployment, NewCreateAndDeleteDeployment)
}

var _ cases.Interface = &createAndDeleteDeployment{}

type createAndDeleteDeployment struct {
	base   *cases.BaseClient
	config ArmConfig
}

// NewTemplate - 测试案例
func NewCreateAndDeleteDeployment(ctx context.Context) cases.Interface {
	return &createAndDeleteDeployment{}
}

func (c *createAndDeleteDeployment) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base

	var cfg ArmConfig
	if err := json.Unmarshal(config, &cfg); err != nil {
		return fmt.Errorf("json.Unmarshal bos config failed: %s", err)
	}
	c.config = cfg

	return nil
}

func (c *createAndDeleteDeployment) Name() cases.CaseName {
	return CreateAndDeleteDeployment
}

func (c *createAndDeleteDeployment) Desc() string {
	return "创建和删除Deployment"
}

func (c *createAndDeleteDeployment) Check(ctx context.Context) (resources []cases.Resource, err error) {
	deploymentName := "deployment-test"
	yaml := DeploymentYAML

	clusterID := c.base.ClusterID

	if c.config.IsArm {
		yaml = strings.ReplaceAll(DeploymentYAML, "nginx:", "nginx-arm64:")
	}
	// 创建 Deployment
	_, err = c.base.AppClient.PostAppResource(ctx, clusterID, deploymentName, yaml, DefaultNameSpace, nil)
	if err != nil {
		logger.Errorf(ctx, "deployment created failed: %s", err)
		return nil, err
	}
	time.Sleep(2 * time.Second)

	deployments, err := c.base.AppClient.GetAppDeploymentList(ctx, clusterID, DefaultNameSpace, nil)
	if err != nil {
		logger.Errorf(ctx, "list deployment by namespace failed: %v", err)
		return nil, err
	}
	if len(deployments.Deployments) == 0 {
		return nil, fmt.Errorf("deployment list is empty")
	}

	// 确认负载的状态，如若处于creating状态，则replicas为0
	var deploymentResource *appservice.GetAppDeploymentResponse
	timeoutCtx, cancelFn := context.WithTimeout(ctx, time.Minute*10)
	timeout := true

	pollDeployment := func(_ context.Context) {
		deploymentResource, err = c.base.AppClient.GetAppDeploymentByName(ctx, clusterID, deploymentName, DefaultNameSpace, nil)
		if err != nil {
			logger.Errorf(ctx, "get deployment Resource: %v err: %v", deploymentName, err)
			return
		}

		if deploymentResource.Status == "Running" &&
			deploymentResource.StatusInfo.Available == deploymentResource.StatusInfo.Replicas {
			cancelFn()
			timeout = false
		} else {
			timeout = true
		}
	}

	wait.UntilWithContext(timeoutCtx, pollDeployment, time.Second*5)

	if timeout {
		err := fmt.Errorf("wait deployment Resource: %v ready timeout", deploymentName)
		logger.Errorf(ctx, "%v", err)
		return nil, err
	}

	logger.Infof(ctx, "the statue of deployment is : %s", deploymentResource.Status)

	// 扩容副本数，检查副本数是否达到预期
	_, err = c.base.AppClient.ScaleAppResource(ctx, clusterID, "deployment", DefaultNameSpace, deploymentName, "3", nil)
	if err != nil {
		logger.Errorf(ctx, "Scale deployment failed: %s", err)
		return nil, err
	}

	wait.UntilWithContext(timeoutCtx, pollDeployment, time.Second*5)

	// 为Reload添加重试机制
	logger.Infof(ctx, "Begin reload deployment: %s", deploymentName)
	timeoutCtx, reloadCancel := context.WithTimeout(ctx, time.Minute*5)
	timeout = true

	reloadDeployment := func(_ context.Context) {
		err = c.base.AppClient.ReloadAppResource(timeoutCtx, clusterID, "deployment", DefaultNameSpace, deploymentName, nil)
		if err != nil {
			logger.Errorf(timeoutCtx, "reload deployment failed, retrying: %v", err)
			return
		}
		reloadCancel()
		timeout = false
	}

	wait.UntilWithContext(timeoutCtx, reloadDeployment, time.Second*10)

	if timeout {
		err := fmt.Errorf("reload deployment Resource: %v ready timeout", deploymentName)
		logger.Errorf(ctx, "%v", err)
		return nil, err
	}

	// deploymentTotalCount, err := c.base.AppClient.GetAppResource(ctx, clusterID, "deployment", DefaultNameSpace,nil)
	// if err != nil {
	//	logger.Errorf(ctx, "get deployment list failed: %s", err)
	//	return nil, err
	// }

	// 删除 Deployment
	if err := c.base.AppClient.DeleteAppResource(ctx, clusterID, "deployment", DefaultNameSpace, deploymentName, nil); err != nil {
		logger.Errorf(ctx, "DeleteAppResource failed: %s", err)
		return nil, err
	}

	// time.Sleep(5 * time.Second)

	// 检查 Deployment 是否删除成功
	// deploymentResourceList, err := c.base.AppClient.GetAppResource(ctx, clusterID, "deployment", DefaultNameSpace,nil)
	// if err != nil {
	//	logger.Errorf(ctx, "get deployment list failed: %s", err)
	//	return nil, err
	// }
	//
	// if deploymentResourceList.ListMeta.TotalItems != deploymentTotalCount.ListMeta.TotalItems-1 {
	//	logger.Errorf(ctx, "Delete Deployment failed: %d != %d", deploymentResourceList.ListMeta.TotalItems, deploymentTotalCount.ListMeta.TotalItems-1)
	//	return nil, fmt.Errorf("delete Deployment failed: %d != %d", deploymentResourceList.ListMeta.TotalItems, deploymentTotalCount.ListMeta.TotalItems-1)
	// }

	return resources, nil
}

func (c *createAndDeleteDeployment) Clean(ctx context.Context) error {
	return nil
}

func (c *createAndDeleteDeployment) Continue(ctx context.Context) bool {
	return true
}

func (c *createAndDeleteDeployment) ConfigFormat() string {
	return ""
}
