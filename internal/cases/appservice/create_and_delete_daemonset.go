// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/17 , by z<PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
create and delete Job
*/

package appservice

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appservice"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CreateAndDeleteDaemonSet cases.CaseName = "CreateAndDeleteDaemonSet"
	DaemonSetYAML                           = `apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: daemonset-test
spec:
  minReadySeconds: 0
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: daemonset-example
  template:
    metadata:
      labels:
        app: daemonset-example
    spec:
      containers:
        - name: daemonset-example
          image: registry.baidubce.com/qa-test/nginx:1.17
          command:
          - sleep
          - "3600"
  updateStrategy:
    type: RollingUpdate`
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CreateAndDeleteDaemonSet, NewCreateAndDeleteDaemonSet)
}

type createAndDeleteDaemonSet struct {
	base   *cases.BaseClient
	config ArmConfig
}

type ArmConfig struct {
	IsArm bool `json:"isArm"`
}

// NewTemplate - 测试案例
func NewCreateAndDeleteDaemonSet(ctx context.Context) cases.Interface {
	return &createAndDeleteDaemonSet{}
}

func (c *createAndDeleteDaemonSet) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base

	var cfg ArmConfig
	if err := json.Unmarshal(config, &cfg); err != nil {
		return fmt.Errorf("json.Unmarshal bos config failed: %s", err)
	}
	c.config = cfg

	return nil
}

func (c *createAndDeleteDaemonSet) Name() cases.CaseName {
	return CreateAndDeleteDaemonSet
}

func (c *createAndDeleteDaemonSet) Desc() string {
	return "创建和删除DaemonSet"
}

func (c *createAndDeleteDaemonSet) Check(ctx context.Context) ([]cases.Resource, error) {
	var resources []cases.Resource
	name := "daemonset-test"
	yaml := DaemonSetYAML

	clusterID := c.base.ClusterID
	if c.config.IsArm {
		yaml = strings.ReplaceAll(DaemonSetYAML, "nginx:", "nginx:-arm64:")
	}
	// 建立DaemonSet
	_, err := c.base.AppClient.PostAppResource(ctx, clusterID, name, yaml, DefaultNameSpace, nil)
	if err != nil {
		logger.Errorf(ctx, "Create DaemonSet failed: %s", err)
		return nil, err
	}

	// 等待负载创建完成
	var daemonset *appservice.DaemonSetDetail
	timeoutCtx, cancelFn := context.WithTimeout(ctx, time.Minute*10)
	timeout := true

	pollDaemonset := func(_ context.Context) {
		daemonset, err = c.base.AppClient.GetDaemonSetByName(ctx, clusterID, name, DefaultNameSpace, nil)
		if err != nil {
			logger.Errorf(ctx, "get daemonset Resource: %v err: %v", name, err)
			return
		}

		if daemonset.Status == "running" || daemonset.Status == "succeeded" {
			cancelFn()
			timeout = false
		} else {
			timeout = true
		}
	}

	wait.UntilWithContext(timeoutCtx, pollDaemonset, time.Second*5)

	if timeout {
		err := fmt.Errorf("wait daemonset Resource: %v ready timeout", name)
		logger.Errorf(ctx, "%v", err)
		return nil, err
	}

	// 检查DaemonSet的详细信息
	if daemonset.TypeMeta.Kind != "daemonset" {
		logger.Errorf(ctx, "this kind is not daemonset")
		return nil, errors.New("this kind is not daemonset")
	}
	logger.Infof(ctx, "%d pod is desired,  %d pod is pending, %d pod is running", daemonset.PodInfo.Desired, daemonset.PodInfo.Pending, daemonset.PodInfo.Succeeded)

	// if daemonset.PodInfo.Succeeded != daemonset.PodInfo.Desired{
	//	logger.Errorf(ctx, "the pod is not running")
	//	return nil, err
	// }

	// totalCount, err := c.base.AppClient.GetAppResource(ctx, clusterID, "daemonset", DefaultNameSpace,nil)
	// if err != nil {
	//	logger.Errorf(ctx, "Get DaemonSet list failed: %s", err)
	//	return nil, err
	// }

	// 删除Daemonset
	err = c.base.AppClient.DeleteAppResource(ctx, clusterID, "daemonset", DefaultNameSpace, name, nil)
	if err != nil {
		logger.Errorf(ctx, "Delete DaemonSet failed: %s", err)
		return nil, err
	}

	// time.Sleep(10 * time.Second)

	// 检查Daemonset是否删除成功
	// resourceList, err := c.base.AppClient.GetAppResource(ctx, clusterID, "daemonset", DefaultNameSpace,nil)
	// if err != nil {
	//	logger.Errorf(ctx, "get DaemonSet list failed: %s", err)
	//	return nil, err
	// }
	// if resourceList.ListMeta.TotalItems != totalCount.ListMeta.TotalItems-1 {
	//	logger.Errorf(ctx, "Delete DaemonSet failed: %d != %d", resourceList.ListMeta.TotalItems, totalCount.ListMeta.TotalItems-1)
	//	return nil, fmt.Errorf("delete DaemonSet failed : %d != %d", resourceList.ListMeta.TotalItems, totalCount.ListMeta.TotalItems-1)
	// }
	return resources, nil
}

func (c *createAndDeleteDaemonSet) Clean(ctx context.Context) error {
	return nil
}

func (c *createAndDeleteDaemonSet) Continue(ctx context.Context) bool {
	return true
}

func (c *createAndDeleteDaemonSet) ConfigFormat() string {
	return ""
}
