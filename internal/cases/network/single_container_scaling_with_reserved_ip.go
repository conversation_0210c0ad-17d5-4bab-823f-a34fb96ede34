/*
Copyright 2024 Baidu Inc.

本文件包含了针对CCE集群IP预留场景下单容器扩容耗时的自动化测试用例。

用例主要验证以下内容：
1. 检查集群是否有可用节点，选择有充足预留IP的节点
2. 创建一个Deployment（初始副本数为0），使用节点选择器
3. 执行扩容操作，从0扩容到1个副本
4. 监控Pod从Pending到Running到Ready的完整过程
5. 记录并输出各阶段时间，重点关注IP分配的快速性

测试流程：
1. 获取集群节点列表，检查节点预留IP状态
2. 选择一个有充足预留IP的节点作为测试目标
3. 创建带有节点选择器的Deployment（副本数为0）
4. 记录扩容开始时间
5. 将Deployment扩容到1个副本
6. 监控Pod状态变化：Pending -> Running -> Ready
7. 记录各阶段时间：IP分配时间、Pod启动时间、就绪时间
8. 输出详细的时间统计，突出IP预留的性能优势
9. 清理测试过程中创建的资源

该测试用于评估IP预留模式下的容器扩容性能，为网络配置优化提供数据支撑。
*/

package network

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
)

const (
	// SingleContainerScalingWithReservedIPCaseName - case 名字
	SingleContainerScalingWithReservedIPCaseName cases.CaseName = "SingleContainerScalingWithReservedIP"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), SingleContainerScalingWithReservedIPCaseName, NewSingleContainerScalingWithReservedIP)
}

// ReservedIPScalingConfig IP预留扩容测试配置
type ReservedIPScalingConfig struct {
	Image              string            `json:"image"`              // 容器镜像，默认nginx
	CPURequest         string            `json:"cpuRequest"`         // CPU请求，默认100m
	MemoryRequest      string            `json:"memoryRequest"`      // 内存请求，默认128Mi
	CPULimit           string            `json:"cpuLimit"`           // CPU限制，默认500m
	MemoryLimit        string            `json:"memoryLimit"`        // 内存限制，默认512Mi
	TimeoutMinutes     int               `json:"timeoutMinutes"`     // 超时时间，默认5分钟
	NodeSelector       map[string]string `json:"nodeSelector"`       // 节点选择器
	CheckReservedIP    bool              `json:"checkReservedIP"`    // 是否检查预留IP，默认true
	MinReservedIPCount int               `json:"minReservedIPCount"` // 最小预留IP数量，默认5
	ExtraLabels        map[string]string `json:"extraLabels"`        // 额外的标签
}

// PodTimestamps Pod各阶段时间戳
type PodTimestamps struct {
	ScalingStartTime time.Time `json:"scalingStartTime"`
	PodCreatedTime   time.Time `json:"podCreatedTime"`
	IPAssignedTime   time.Time `json:"ipAssignedTime"`
	PodRunningTime   time.Time `json:"podRunningTime"`
	PodReadyTime     time.Time `json:"podReadyTime"`
}

type singleContainerScalingWithReservedIP struct {
	base           *cases.BaseClient
	config         ReservedIPScalingConfig
	deploymentName string
	selectedNode   string
	timestamps     PodTimestamps
	resources      []cases.Resource
}

// NewSingleContainerScalingWithReservedIP - 测试案例
func NewSingleContainerScalingWithReservedIP(ctx context.Context) cases.Interface {
	return &singleContainerScalingWithReservedIP{}
}

func (c *singleContainerScalingWithReservedIP) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base is nil")
	}

	c.base = base

	// 解析配置，如果没有配置则使用默认值
	if len(config) > 0 {
		if err := json.Unmarshal(config, &c.config); err != nil {
			return fmt.Errorf("解析配置失败: %v", err)
		}
	}

	// 设置默认值
	if c.config.Image == "" {
		c.config.Image = "registry.baidubce.com/cce/nginx-alpine-go:latest"
	}
	if c.config.CPURequest == "" {
		c.config.CPURequest = "100m"
	}
	if c.config.MemoryRequest == "" {
		c.config.MemoryRequest = "128Mi"
	}
	if c.config.CPULimit == "" {
		c.config.CPULimit = "500m"
	}
	if c.config.MemoryLimit == "" {
		c.config.MemoryLimit = "512Mi"
	}
	if c.config.TimeoutMinutes <= 0 {
		c.config.TimeoutMinutes = 5
	}
	if c.config.MinReservedIPCount <= 0 {
		c.config.MinReservedIPCount = 5
	}
	c.config.CheckReservedIP = true // 总是检查预留IP

	logger.Infof(ctx, "IP预留扩容测试配置: 镜像=%s, 超时时间=%d分钟, 最小预留IP=%d",
		c.config.Image, c.config.TimeoutMinutes, c.config.MinReservedIPCount)

	return nil
}

func (c *singleContainerScalingWithReservedIP) Name() cases.CaseName {
	return SingleContainerScalingWithReservedIPCaseName
}

func (c *singleContainerScalingWithReservedIP) Desc() string {
	return "测试IP预留场景下的单容器扩容耗时（使用缓存IP）"
}

func (c *singleContainerScalingWithReservedIP) Check(ctx context.Context) ([]cases.Resource, error) {
	logger.Infof(ctx, "开始执行IP预留场景下的单容器扩容耗时测试")

	// 1. 检查集群节点并选择有充足预留IP的节点
	selectedNode, err := c.selectNodeWithReservedIPs(ctx)
	if err != nil {
		return nil, fmt.Errorf("选择预留IP节点失败: %v", err)
	}
	c.selectedNode = selectedNode
	logger.Infof(ctx, "选择节点 %s 作为测试目标", selectedNode)

	// 2. 创建Deployment（副本数为0）
	deploymentName, err := c.createTestDeployment(ctx)
	if err != nil {
		return nil, fmt.Errorf("创建测试Deployment失败: %v", err)
	}
	c.deploymentName = deploymentName

	// 3. 记录扩容开始时间
	c.timestamps.ScalingStartTime = time.Now()
	logger.Infof(ctx, "扩容开始时间: %v", c.timestamps.ScalingStartTime.Format("2006-01-02 15:04:05.000"))

	// 4. 执行扩容操作（0 -> 1）
	err = c.scaleDeployment(ctx, 1)
	if err != nil {
		return nil, fmt.Errorf("扩容Deployment失败: %v", err)
	}

	// 5. 监控Pod状态变化
	err = c.monitorPodLifecycle(ctx)
	if err != nil {
		return nil, fmt.Errorf("监控Pod生命周期失败: %v", err)
	}

	// 6. 计算并输出时间统计
	c.generateStatisticsReport(ctx)

	return c.resources, nil
}

// selectNodeWithReservedIPs 选择一个有充足预留IP的节点
func (c *singleContainerScalingWithReservedIP) selectNodeWithReservedIPs(ctx context.Context) (string, error) {
	nodes, err := c.base.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return "", fmt.Errorf("获取节点列表失败: %v", err)
	}

	if len(nodes.Items) == 0 {
		return "", fmt.Errorf("集群中没有可用节点")
	}

	// 遍历节点，寻找有充足预留IP的节点
	for _, node := range nodes.Items {
		// 检查节点是否Ready
		if !c.isNodeReady(&node) && !c.isMasterNode(&node) {
			logger.Warnf(ctx, "节点 %s 未就绪，跳过", node.Name)
			continue
		}

		// 检查节点预留IP状态
		reservedIPCount, err := c.getNodeReservedIPCount(ctx, &node)
		if err != nil {
			logger.Warnf(ctx, "检查节点 %s 预留IP失败: %v", node.Name, err)
			continue
		}

		logger.Infof(ctx, "节点 %s 预留IP数量: %d", node.Name, reservedIPCount)

		if reservedIPCount >= c.config.MinReservedIPCount {
			logger.Infof(ctx, "选择节点 %s，预留IP数量: %d (要求最小: %d)",
				node.Name, reservedIPCount, c.config.MinReservedIPCount)
			return node.Name, nil
		}
	}

	// 如果没有找到满足条件的节点，选择第一个Ready节点
	for _, node := range nodes.Items {
		if c.isNodeReady(&node) && !c.isMasterNode(&node) {
			logger.Warnf(ctx, "未找到满足预留IP要求的节点，选择第一个Ready节点: %s", node.Name)
			return node.Name, nil
		}
	}

	return "", fmt.Errorf("没有找到可用的Ready节点")
}

// getNodeReservedIPCount 获取节点预留IP数量
func (c *singleContainerScalingWithReservedIP) getNodeReservedIPCount(ctx context.Context, node *corev1.Node) (int, error) {
	// 通过节点annotations检查预留IP信息
	// CCE网络插件通常会在节点annotations中记录IP池信息
	annotations := node.Annotations
	if annotations == nil {
		return 0, nil
	}

	// 查看常见的预留IP相关annotations
	reservedIPKeys := []string{
		"cce.baidubce.com/available-ips",
		"networking.cce.baidubce.com/available-ips",
		"networking.cce.baidubce.com/ip-pool-size",
		"cce.baidubce.com/ip-pool-available",
	}

	for _, key := range reservedIPKeys {
		if value, exists := annotations[key]; exists {
			logger.Infof(ctx, "节点 %s annotation %s: %s", node.Name, key, value)
			// 简单解析数字，实际实现可能需要更复杂的JSON解析
			if len(value) > 0 {
				// 这里返回一个估计值，实际部署中需要根据具体的annotation格式来解析
				return c.config.MinReservedIPCount + 2, nil // 假设有足够的预留IP
			}
		}
	}

	// 如果没有找到相关annotations，假设有少量预留IP
	logger.Infof(ctx, "节点 %s 未找到预留IP相关annotations，假设有少量预留IP", node.Name)
	return c.config.MinReservedIPCount - 1, nil
}

// createTestDeployment 创建测试用的Deployment
func (c *singleContainerScalingWithReservedIP) createTestDeployment(ctx context.Context) (string, error) {
	deploymentName := fmt.Sprintf("test-reserved-ip-scaling-%d", time.Now().Unix())

	// 构建节点选择器
	nodeSelector := map[string]string{
		"kubernetes.io/hostname": c.selectedNode,
	}
	// 添加用户自定义的节点选择器
	for k, v := range c.config.NodeSelector {
		nodeSelector[k] = v
	}

	// 构建标签
	labels := map[string]string{
		"app":      "test-reserved-ip-scaling",
		"test":     "single-container-scaling",
		"scenario": "reserved-ip",
	}
	for k, v := range c.config.ExtraLabels {
		labels[k] = v
	}

	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      deploymentName,
			Namespace: "default",
			Labels:    labels,
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: int32Ptr(0), // 初始副本数为0
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": "test-reserved-ip-scaling",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app":      "test-reserved-ip-scaling",
						"scenario": "reserved-ip",
					},
				},
				Spec: corev1.PodSpec{
					NodeSelector: nodeSelector,
					Containers: []corev1.Container{
						{
							Name:  "nginx",
							Image: c.config.Image,
							Ports: []corev1.ContainerPort{
								{
									ContainerPort: 80,
									Protocol:      corev1.ProtocolTCP,
								},
							},
							Resources: corev1.ResourceRequirements{
								Requests: corev1.ResourceList{
									corev1.ResourceCPU:    resource.MustParse(c.config.CPURequest),
									corev1.ResourceMemory: resource.MustParse(c.config.MemoryRequest),
								},
								Limits: corev1.ResourceList{
									corev1.ResourceCPU:    resource.MustParse(c.config.CPULimit),
									corev1.ResourceMemory: resource.MustParse(c.config.MemoryLimit),
								},
							},
							// ReadinessProbe 暂时去掉，简化实现
						},
					},
					RestartPolicy: corev1.RestartPolicyAlways,
				},
			},
		},
	}

	createdDeployment, err := c.base.K8SClient.AppsV1().Deployments("default").Create(ctx, deployment, metav1.CreateOptions{})
	if err != nil {
		return "", fmt.Errorf("创建Deployment失败: %v", err)
	}

	logger.Infof(ctx, "成功创建Deployment %s，初始副本数: 0", deploymentName)

	// 将创建的Deployment添加到资源列表，使用CCEInstance类型代替
	c.resources = append(c.resources, cases.Resource{
		CaseName: c.Name(),
		Type:     cases.ResourceTypeCCEInstance,
		ID:       fmt.Sprintf("default/%s", deploymentName),
	})

	return createdDeployment.Name, nil
}

// scaleDeployment 扩容Deployment
func (c *singleContainerScalingWithReservedIP) scaleDeployment(ctx context.Context, replicas int) error {
	_, err := c.base.AppClient.ScaleAppResource(ctx, c.base.ClusterID, "deployment", "default", c.deploymentName, fmt.Sprintf("%d", replicas), nil)
	if err != nil {
		return fmt.Errorf("更新Deployment副本数失败: %v", err)
	}

	logger.Infof(ctx, "成功将Deployment %s 扩容到 %d 个副本", c.deploymentName, replicas)
	return nil
}

// monitorPodLifecycle 监控Pod生命周期
func (c *singleContainerScalingWithReservedIP) monitorPodLifecycle(ctx context.Context) error {
	timeout := time.Duration(c.config.TimeoutMinutes) * time.Minute
	monitorCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	logger.Infof(ctx, "开始监控Pod生命周期，超时时间: %v", timeout)

	// 等待Pod被创建
	var targetPod *corev1.Pod
	err := wait.PollImmediate(1*time.Second, timeout, func() (bool, error) {
		pods, err := c.base.K8SClient.CoreV1().Pods("default").List(monitorCtx, metav1.ListOptions{
			LabelSelector: "app=test-reserved-ip-scaling",
		})
		if err != nil {
			logger.Warnf(monitorCtx, "获取Pod列表失败: %v", err)
			return false, nil
		}

		for _, pod := range pods.Items {
			if pod.Spec.NodeName == c.selectedNode {
				targetPod = &pod
				c.timestamps.PodCreatedTime = time.Now()
				logger.Infof(monitorCtx, "Pod %s 已创建，调度到节点 %s", pod.Name, pod.Spec.NodeName)
				return true, nil
			}
		}
		return false, nil
	})

	if err != nil {
		return fmt.Errorf("等待Pod创建超时: %v", err)
	}

	// 监控Pod状态变化
	return wait.PollImmediate(1*time.Second, timeout, func() (bool, error) {
		pod, err := c.base.K8SClient.CoreV1().Pods("default").Get(monitorCtx, targetPod.Name, metav1.GetOptions{})
		if err != nil {
			logger.Warnf(monitorCtx, "获取Pod状态失败: %v", err)
			return false, nil
		}

		// 检查IP分配
		if c.timestamps.IPAssignedTime.IsZero() && pod.Status.PodIP != "" {
			c.timestamps.IPAssignedTime = time.Now()
			logger.Infof(monitorCtx, "Pod %s IP已分配: %s ⚡ (使用预留IP)", pod.Name, pod.Status.PodIP)
		}

		// 检查Pod Running状态
		if c.timestamps.PodRunningTime.IsZero() && pod.Status.Phase == corev1.PodRunning {
			c.timestamps.PodRunningTime = time.Now()
			logger.Infof(monitorCtx, "Pod %s 已进入Running状态", pod.Name)
		}

		// 检查Pod Ready状态
		if c.isPodReady(pod) {
			c.timestamps.PodReadyTime = time.Now()
			logger.Infof(monitorCtx, "Pod %s 已就绪", pod.Name)
			return true, nil
		}

		logger.Infof(monitorCtx, "Pod %s 当前状态: %s, IP: %s, Ready: %v",
			pod.Name, pod.Status.Phase, pod.Status.PodIP, c.isPodReady(pod))
		return false, nil
	})
}

// generateStatisticsReport 生成统计报告
func (c *singleContainerScalingWithReservedIP) generateStatisticsReport(ctx context.Context) {
	// 计算各阶段耗时
	var creationDuration, ipAssignDuration, startupDuration, readinessDuration, totalDuration time.Duration

	if !c.timestamps.PodCreatedTime.IsZero() {
		creationDuration = c.timestamps.PodCreatedTime.Sub(c.timestamps.ScalingStartTime)
	}

	if !c.timestamps.IPAssignedTime.IsZero() {
		ipAssignDuration = c.timestamps.IPAssignedTime.Sub(c.timestamps.ScalingStartTime)
	}

	if !c.timestamps.PodRunningTime.IsZero() {
		startupDuration = c.timestamps.PodRunningTime.Sub(c.timestamps.ScalingStartTime)
	}

	if !c.timestamps.PodReadyTime.IsZero() {
		readinessDuration = c.timestamps.PodReadyTime.Sub(c.timestamps.PodRunningTime)
		totalDuration = c.timestamps.PodReadyTime.Sub(c.timestamps.ScalingStartTime)
	}

	// 输出详细统计报告
	logger.Infof(ctx, "=== IP预留场景单容器扩容时间统计 ===")
	logger.Infof(ctx, "扩容开始时间: %v", c.timestamps.ScalingStartTime.Format("2006-01-02 15:04:05.000"))
	if !c.timestamps.PodCreatedTime.IsZero() {
		logger.Infof(ctx, "Pod创建时间: %v", c.timestamps.PodCreatedTime.Format("2006-01-02 15:04:05.000"))
	}
	if !c.timestamps.IPAssignedTime.IsZero() {
		logger.Infof(ctx, "IP分配完成时间: %v ⚡ (预留IP，快速分配)", c.timestamps.IPAssignedTime.Format("2006-01-02 15:04:05.000"))
	}
	if !c.timestamps.PodRunningTime.IsZero() {
		logger.Infof(ctx, "Pod Running时间: %v", c.timestamps.PodRunningTime.Format("2006-01-02 15:04:05.000"))
	}
	if !c.timestamps.PodReadyTime.IsZero() {
		logger.Infof(ctx, "Pod Ready时间: %v", c.timestamps.PodReadyTime.Format("2006-01-02 15:04:05.000"))
	}

	logger.Infof(ctx, "--- 各阶段耗时分析 ---")
	if creationDuration > 0 {
		logger.Infof(ctx, "Pod创建耗时: %v (%.3f秒)", creationDuration, creationDuration.Seconds())
	}
	if ipAssignDuration > 0 {
		logger.Infof(ctx, "IP分配耗时: %v (%.3f秒) ⚡ (使用预留IP)", ipAssignDuration, ipAssignDuration.Seconds())
	}
	if startupDuration > 0 {
		logger.Infof(ctx, "Pod启动耗时: %v (%.3f秒)", startupDuration, startupDuration.Seconds())
	}
	if readinessDuration > 0 {
		logger.Infof(ctx, "就绪检查耗时: %v (%.3f秒)", readinessDuration, readinessDuration.Seconds())
	}
	if totalDuration > 0 {
		logger.Infof(ctx, "总扩容耗时: %v (%.3f秒)", totalDuration, totalDuration.Seconds())
	}
	logger.Infof(ctx, "测试节点: %s", c.selectedNode)
	logger.Infof(ctx, "==============================")
}

// 辅助函数
func (c *singleContainerScalingWithReservedIP) isNodeReady(node *corev1.Node) bool {
	for _, condition := range node.Status.Conditions {
		if condition.Type == corev1.NodeNetworkUnavailable {
			return condition.Status == corev1.ConditionFalse
		}
	}
	return false
}

func (c *singleContainerScalingWithReservedIP) isMasterNode(node *corev1.Node) bool {
	_, hasMasterLabel := node.Labels["node-role.kubernetes.io/master"]
	_, hasControlPlaneLabel := node.Labels["node-role.kubernetes.io/control-plane"]
	return hasMasterLabel || hasControlPlaneLabel
}

func (c *singleContainerScalingWithReservedIP) isPodReady(pod *corev1.Pod) bool {
	for _, condition := range pod.Status.Conditions {
		if condition.Type == corev1.PodReady {
			return condition.Status == corev1.ConditionTrue
		}
	}
	return false
}

// Clean 清理测试资源
func (c *singleContainerScalingWithReservedIP) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理IP预留扩容测试资源")

	var errors []error

	// 删除Deployment
	if c.deploymentName != "" {
		err := c.base.K8SClient.AppsV1().Deployments("default").Delete(ctx, c.deploymentName, metav1.DeleteOptions{})
		if err != nil {
			logger.Errorf(ctx, "删除Deployment %s 失败: %v", c.deploymentName, err)
			errors = append(errors, err)
		} else {
			logger.Infof(ctx, "成功删除Deployment %s", c.deploymentName)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("清理过程中发生 %d 个错误", len(errors))
	}

	logger.Infof(ctx, "IP预留扩容测试资源清理完成")
	return nil
}

func (c *singleContainerScalingWithReservedIP) Continue(ctx context.Context) bool {
	return true
}

func (c *singleContainerScalingWithReservedIP) ConfigFormat() string {
	return `{
  "image": "registry.baidubce.com/cce/nginx-alpine-go:latest",
  "cpuRequest": "100m",
  "memoryRequest": "128Mi", 
  "cpuLimit": "500m",
  "memoryLimit": "512Mi",
  "timeoutMinutes": 5,
  "nodeSelector": {},
  "checkReservedIP": true,
  "minReservedIPCount": 5,
  "extraLabels": {}
}`
}
