// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2024/01/01 00:00:00, by <EMAIL>, create
*/
/*
网络QoS带宽限制测试:
1. 创建带有带宽限制annotation的Pod;
2. 校验容器出入向带宽限制是否生效;
3. 验证不同单位的带宽配置;
4. 检查tc配置是否正确。
*/

package network

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/remotecommand"
)

const (
	// BandwidthQoSCaseName - case 名字
	BandwidthQoSCaseName cases.CaseName = "BandwidthQoS"

	// 测试用的命名空间
	testNamespace = "bandwidth-qos-test"

	// 测试用的资源名称
	testClientName     = "bandwidth-test-client"
	testEgressPodName  = "bandwidth-test-egress"
	testIngressPodName = "bandwidth-test-ingress"

	// 带宽限制注解
	ingressBandwidthAnnotation = "kubernetes.io/ingress-bandwidth"
	egressBandwidthAnnotation  = "kubernetes.io/egress-bandwidth"
	ingressBandwidthValue      = "10M" // 入向带宽限制10MB/s
	egressBandwidthValue       = "5M"  // 出向带宽限制5MB/s

	// 带宽转换系数
	bytesToBitsMultiplier = 8 // 1 byte = 8 bits
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), BandwidthQoSCaseName, NewBandwidthQoS)
}

type bandwidthQoS struct {
	base *cases.BaseClient
}

// NewBandwidthQoS - 测试案例
func NewBandwidthQoS(ctx context.Context) cases.Interface {
	return &bandwidthQoS{}
}

func (c *bandwidthQoS) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base
	return nil
}

func (c *bandwidthQoS) Name() cases.CaseName {
	return BandwidthQoSCaseName
}

func (c *bandwidthQoS) Desc() string {
	return "校验带宽限制参数生效，容器出入向带宽限制"
}

func (c *bandwidthQoS) Check(ctx context.Context) ([]cases.Resource, error) {
	logger.Infof(ctx, "开始执行带宽QoS测试")

	// 1. 创建测试命名空间
	if err := c.createNamespace(ctx); err != nil {
		logger.Errorf(ctx, "创建命名空间失败: %v", err)
		return nil, err
	}

	// 2. 创建客户端Pod（无带宽限制）
	if err := c.createClientPod(ctx); err != nil {
		logger.Errorf(ctx, "创建客户端Pod失败: %v", err)
		return nil, err
	}

	// 3. 创建出向限制Pod
	if err := c.createEgressPod(ctx); err != nil {
		logger.Errorf(ctx, "创建出向限制Pod失败: %v", err)
		return nil, err
	}

	// 4. 创建入向限制Pod
	if err := c.createIngressPod(ctx); err != nil {
		logger.Errorf(ctx, "创建入向限制Pod失败: %v", err)
		return nil, err
	}

	// 5. 等待Pod运行
	if err := c.waitPodsRunning(ctx); err != nil {
		logger.Errorf(ctx, "等待Pod运行失败: %v", err)
		return nil, err
	}

	// // 6. 验证tc配置是否正确
	// if err := c.validateTCConfig(ctx); err != nil {
	// 	logger.Errorf(ctx, "验证tc配置失败: %v", err)
	// 	return nil, err
	// }

	// 7. 测试带宽限制
	if err := c.testBandwidthLimit(ctx); err != nil {
		logger.Errorf(ctx, "测试带宽限制失败: %v", err)
		return nil, err
	}

	logger.Infof(ctx, "带宽QoS测试完成")
	return nil, nil
}

func (c *bandwidthQoS) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理带宽QoS测试资源")

	// 删除测试命名空间
	err := c.base.K8SClient.CoreV1().Namespaces().Delete(ctx, testNamespace, metav1.DeleteOptions{})
	if err != nil {
		logger.Warnf(ctx, "删除命名空间 %s 失败: %v", testNamespace, err)
		return err
	}

	// 等待命名空间删除
	err = wait.Poll(5*time.Second, 60*time.Second, func() (bool, error) {
		_, err := c.base.K8SClient.CoreV1().Namespaces().Get(ctx, testNamespace, metav1.GetOptions{})
		if err != nil {
			return true, nil // 命名空间已删除
		}
		return false, nil
	})
	if err != nil {
		logger.Warnf(ctx, "等待命名空间删除超时: %v", err)
	}

	logger.Infof(ctx, "带宽QoS测试资源清理完成")
	return nil
}

func (c *bandwidthQoS) Continue(ctx context.Context) bool {
	return true
}

func (c *bandwidthQoS) ConfigFormat() string {
	return ""
}

// createNamespace 创建测试命名空间
func (c *bandwidthQoS) createNamespace(ctx context.Context) error {
	// 先检查命名空间是否存在
	_, err := c.base.K8SClient.CoreV1().Namespaces().Get(ctx, testNamespace, metav1.GetOptions{})
	if err == nil {
		// 命名空间已存在，先删除它
		logger.Infof(ctx, "命名空间 %s 已存在，先删除它", testNamespace)
		err = c.base.K8SClient.CoreV1().Namespaces().Delete(ctx, testNamespace, metav1.DeleteOptions{})
		if err != nil {
			return fmt.Errorf("删除已存在的命名空间失败: %v", err)
		}

		// 等待命名空间删除完成
		err = wait.Poll(2*time.Second, 60*time.Second, func() (bool, error) {
			_, err := c.base.K8SClient.CoreV1().Namespaces().Get(ctx, testNamespace, metav1.GetOptions{})
			if err != nil {
				return true, nil // 命名空间已删除
			}
			return false, nil
		})
		if err != nil {
			return fmt.Errorf("等待命名空间删除超时: %v", err)
		}
	}

	// 创建新的命名空间
	ns := &corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: testNamespace,
		},
	}

	_, err = c.base.K8SClient.CoreV1().Namespaces().Create(ctx, ns, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建命名空间失败: %v", err)
	}

	logger.Infof(ctx, "成功创建命名空间: %s", testNamespace)
	return nil
}

// createClientPod 创建测试客户端Pod（无带宽限制）
func (c *bandwidthQoS) createClientPod(ctx context.Context) error {
	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      testClientName,
			Namespace: testNamespace,
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: int32Ptr(1),
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": testClientName,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": testClientName,
					},
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						{
							Name:  "iperf3",
							Image: "registry.baidubce.com/cce-plugin-dev/networkstatic/iperf3",
							Command: []string{
								"sleep",
								"3600",
							},
						},
					},
				},
			},
		},
	}

	_, err := c.base.K8SClient.AppsV1().Deployments(testNamespace).Create(ctx, deployment, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建客户端Deployment失败: %v", err)
	}

	logger.Infof(ctx, "成功创建客户端Pod")
	return nil
}

// createEgressPod 创建出向带宽限制Pod
func (c *bandwidthQoS) createEgressPod(ctx context.Context) error {
	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      testEgressPodName,
			Namespace: testNamespace,
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: int32Ptr(1),
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": testEgressPodName,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": testEgressPodName,
					},
					Annotations: map[string]string{
						egressBandwidthAnnotation: egressBandwidthValue, // 只设置出向带宽限制
					},
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						{
							Name:  "iperf3",
							Image: "registry.baidubce.com/cce-plugin-dev/networkstatic/iperf3",
							Command: []string{
								"iperf3",
								"-s",
								"-p",
								"5201",
							},
							Ports: []corev1.ContainerPort{
								{
									Name:          "iperf3",
									Protocol:      corev1.ProtocolTCP,
									ContainerPort: 5201,
								},
							},
						},
					},
				},
			},
		},
	}

	_, err := c.base.K8SClient.AppsV1().Deployments(testNamespace).Create(ctx, deployment, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建出向限制Pod失败: %v", err)
	}

	// 创建Service
	service := &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      testEgressPodName,
			Namespace: testNamespace,
		},
		Spec: corev1.ServiceSpec{
			Selector: map[string]string{
				"app": testEgressPodName,
			},
			Ports: []corev1.ServicePort{
				{
					Name:       "iperf3",
					Port:       5201,
					TargetPort: intstr.FromInt(5201),
					Protocol:   corev1.ProtocolTCP,
				},
			},
		},
	}

	_, err = c.base.K8SClient.CoreV1().Services(testNamespace).Create(ctx, service, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建出向限制Pod的Service失败: %v", err)
	}

	logger.Infof(ctx, "成功创建出向限制Pod，带宽限制: egress=%s", egressBandwidthValue)
	return nil
}

// createIngressPod 创建入向带宽限制Pod
func (c *bandwidthQoS) createIngressPod(ctx context.Context) error {
	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      testIngressPodName,
			Namespace: testNamespace,
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: int32Ptr(1),
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": testIngressPodName,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": testIngressPodName,
					},
					Annotations: map[string]string{
						ingressBandwidthAnnotation: ingressBandwidthValue, // 只设置入向带宽限制
					},
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						{
							Name:  "iperf3",
							Image: "registry.baidubce.com/cce-plugin-dev/networkstatic/iperf3",
							Command: []string{
								"iperf3",
								"-s",
								"-p",
								"5201",
							},
							Ports: []corev1.ContainerPort{
								{
									Name:          "iperf3",
									Protocol:      corev1.ProtocolTCP,
									ContainerPort: 5201,
								},
							},
						},
					},
				},
			},
		},
	}

	_, err := c.base.K8SClient.AppsV1().Deployments(testNamespace).Create(ctx, deployment, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建入向限制Pod失败: %v", err)
	}

	// 创建Service
	service := &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      testIngressPodName,
			Namespace: testNamespace,
		},
		Spec: corev1.ServiceSpec{
			Selector: map[string]string{
				"app": testIngressPodName,
			},
			Ports: []corev1.ServicePort{
				{
					Name:       "iperf3",
					Port:       5201,
					TargetPort: intstr.FromInt(5201),
					Protocol:   corev1.ProtocolTCP,
				},
			},
		},
	}

	_, err = c.base.K8SClient.CoreV1().Services(testNamespace).Create(ctx, service, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建入向限制Pod的Service失败: %v", err)
	}

	logger.Infof(ctx, "成功创建入向限制Pod，带宽限制: ingress=%s", ingressBandwidthValue)
	return nil
}

// waitPodsRunning 等待Pod运行
func (c *bandwidthQoS) waitPodsRunning(ctx context.Context) error {
	logger.Infof(ctx, "等待Pod运行中...")

	// 等待服务端Pod运行
	err := resource.WaitPodsRunning(ctx, c.base.K8SClient, testNamespace, map[string]string{
		"app": testClientName,
	})
	if err != nil {
		return fmt.Errorf("等待服务端Pod运行失败: %v", err)
	}

	// 等待客户端Pod运行
	err = resource.WaitPodsRunning(ctx, c.base.K8SClient, testNamespace, map[string]string{
		"app": testEgressPodName,
	})
	if err != nil {
		return fmt.Errorf("等待出向限制Pod运行失败: %v", err)
	}

	err = resource.WaitPodsRunning(ctx, c.base.K8SClient, testNamespace, map[string]string{
		"app": testIngressPodName,
	})
	if err != nil {
		return fmt.Errorf("等待入向限制Pod运行失败: %v", err)
	}

	logger.Infof(ctx, "所有Pod已正常运行")
	return nil
}

// validateTCConfig 验证tc配置是否正确
func (c *bandwidthQoS) validateTCConfig(ctx context.Context) error {
	// 获取出向限制Pod
	egressPods, err := c.base.K8SClient.CoreV1().Pods(testNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "app=" + testEgressPodName,
	})
	if err != nil {
		return fmt.Errorf("获取出向限制Pod失败: %v", err)
	}
	if len(egressPods.Items) == 0 {
		return errors.New("没有找到出向限制Pod")
	}
	egressPod := egressPods.Items[0]

	// 获取入向限制Pod
	ingressPods, err := c.base.K8SClient.CoreV1().Pods(testNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "app=" + testIngressPodName,
	})
	if err != nil {
		return fmt.Errorf("获取入向限制Pod失败: %v", err)
	}
	if len(ingressPods.Items) == 0 {
		return errors.New("没有找到入向限制Pod")
	}
	ingressPod := ingressPods.Items[0]

	// 测试出向带宽限制
	logger.Infof(ctx, "测试出向带宽限制...")
	// 获取客户端Pod
	clientPods, err := c.base.K8SClient.CoreV1().Pods(testNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "app=" + testClientName,
	})
	if err != nil {
		return fmt.Errorf("获取客户端Pod失败: %v", err)
	}
	if len(clientPods.Items) == 0 {
		return errors.New("没有找到客户端Pod")
	}
	clientPod := clientPods.Items[0]

	output, err := c.execCommandInPod(ctx, clientPod.Name, "iperf3", []string{
		"-c",
		egressPod.Status.PodIP,
		"-t",
		"10",
		"-f",
		"m", // 以Mbits/s为单位显示结果
	})
	if err != nil {
		return fmt.Errorf("测试出向带宽失败: %v", err)
	}

	// 解析iperf3输出，获取带宽值
	egressBandwidth, err := c.parseIperf3Bandwidth(output)
	if err != nil {
		return fmt.Errorf("解析出向带宽测试结果失败: %v", err)
	}

	// 检查出向带宽是否符合限制（5M = 40Mbits/s）
	if egressBandwidth > 45 { // 允许10%的误差
		return fmt.Errorf("出向带宽超出限制，期望 ≤ 40 Mbits/s，实际: %.2f Mbits/s", egressBandwidth)
	}
	logger.Infof(ctx, "出向带宽测试通过，带宽: %.2f Mbits/s", egressBandwidth)

	// 测试入向带宽限制
	logger.Infof(ctx, "测试入向带宽限制...")
	output, err = c.execCommandInPod(ctx, clientPod.Name, "iperf3", []string{
		"-c",
		ingressPod.Status.PodIP,
		"-t",
		"10",
		"-f",
		"m", // 以Mbits/s为单位显示结果
		"-R",
	})
	if err != nil {
		return fmt.Errorf("测试入向带宽失败: %v", err)
	}

	// 解析iperf3输出，获取带宽值
	ingressBandwidth, err := c.parseIperf3Bandwidth(output)
	if err != nil {
		return fmt.Errorf("解析入向带宽测试结果失败: %v", err)
	}

	// 检查入向带宽是否符合限制（10M = 80Mbits/s）
	if ingressBandwidth > 88 { // 允许10%的误差
		return fmt.Errorf("入向带宽超出限制，期望 ≤ 80 Mbits/s，实际: %.2f Mbits/s", ingressBandwidth)
	}
	logger.Infof(ctx, "入向带宽测试通过，带宽: %.2f Mbits/s", ingressBandwidth)

	return nil
}

// parseIperf3Bandwidth 从iperf3输出中解析带宽值（Mbits/s）
func (c *bandwidthQoS) parseIperf3Bandwidth(output string) (float64, error) {
	// 查找包含带宽结果的行
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		if strings.Contains(line, "sender") || strings.Contains(line, "receiver") {
			// 提取带宽值
			fields := strings.Fields(line)
			for i, field := range fields {
				if field == "Mbits/sec" && i > 0 {
					bandwidth, err := strconv.ParseFloat(fields[i-1], 64)
					if err != nil {
						return 0, fmt.Errorf("解析带宽值失败: %v", err)
					}
					return bandwidth, nil
				}
			}
		}
	}
	return 0, errors.New("未找到带宽测试结果")
}

// testBandwidthLimit 测试带宽限制
func (c *bandwidthQoS) testBandwidthLimit(ctx context.Context) error {
	logger.Infof(ctx, "开始测试带宽限制...")

	// 获取客户端Pod
	clientPods, err := c.base.K8SClient.CoreV1().Pods(testNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "app=" + testClientName,
	})
	if err != nil {
		return fmt.Errorf("获取客户端Pod失败: %v", err)
	}
	if len(clientPods.Items) == 0 {
		return errors.New("没有找到客户端Pod")
	}
	clientPod := clientPods.Items[0]

	// 获取出向限制Pod
	egressPods, err := c.base.K8SClient.CoreV1().Pods(testNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "app=" + testEgressPodName,
	})
	if err != nil {
		return fmt.Errorf("获取出向限制Pod失败: %v", err)
	}
	if len(egressPods.Items) == 0 {
		return errors.New("没有找到出向限制Pod")
	}
	egressPod := egressPods.Items[0]

	// 获取入向限制Pod
	ingressPods, err := c.base.K8SClient.CoreV1().Pods(testNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "app=" + testIngressPodName,
	})
	if err != nil {
		return fmt.Errorf("获取入向限制Pod失败: %v", err)
	}
	if len(ingressPods.Items) == 0 {
		return errors.New("没有找到入向限制Pod")
	}
	ingressPod := ingressPods.Items[0]

	// 等待iperf3服务器启动
	time.Sleep(5 * time.Second)

	// 测试出向限制Pod的带宽限制
	logger.Infof(ctx, "测试出向限制Pod的带宽限制...")
	output, err := c.execCommandInPod(ctx, clientPod.Name, "iperf3", []string{
		"iperf3",
		"-c",
		egressPod.Status.PodIP,
		"-p",
		"5201",
		"-t",
		"10",
		"-f",
		"m",
		"-R",
	})
	if err != nil {
		return fmt.Errorf("测试出向带宽失败: %v", err)
	}

	// 检查带宽是否在限制范围内（5MB/s = 40Mbits/s）
	if bandwidth, err := c.parseIperf3Output(output); err != nil {
		logger.Errorf(ctx, "解析iperf3输出失败: %v", err)
	} else {
		logger.Infof(ctx, "出向带宽测试结果: %.2f Mbits/sec（限制: %.2f Mbits/sec）",
			bandwidth, 5.0*bytesToBitsMultiplier)
		if bandwidth > 5.0*bytesToBitsMultiplier*1.1 { // 允许10%的误差
			return fmt.Errorf("出向带宽超出限制，期望 <= %.2f Mbits/sec，实际: %.2f Mbits/sec",
				5.0*bytesToBitsMultiplier, bandwidth)
		}
	}

	// 测试入向限制Pod的带宽限制
	logger.Infof(ctx, "测试入向限制Pod的带宽限制...")
	output, err = c.execCommandInPod(ctx, clientPod.Name, "iperf3", []string{
		"iperf3",
		"-c",
		ingressPod.Status.PodIP,
		"-p",
		"5201",
		"-t",
		"10",
		"-f",
		"m",
	})
	if err != nil {
		return fmt.Errorf("测试入向带宽失败: %v", err)
	}

	// 检查带宽是否在限制范围内（10MB/s = 80Mbits/s）
	if bandwidth, err := c.parseIperf3Output(output); err != nil {
		logger.Errorf(ctx, "解析iperf3输出失败: %v", err)
	} else {
		logger.Infof(ctx, "入向带宽测试结果: %.2f Mbits/sec（限制: %.2f Mbits/sec）",
			bandwidth, 10.0*bytesToBitsMultiplier)
		if bandwidth > 10.0*bytesToBitsMultiplier*1.1 { // 允许10%的误差
			return fmt.Errorf("入向带宽超出限制，期望 <= %.2f Mbits/sec，实际: %.2f Mbits/sec",
				10.0*bytesToBitsMultiplier, bandwidth)
		}
	}

	logger.Infof(ctx, "带宽限制测试完成")
	return nil
}

// execCommandInPod 在Pod中执行命令
func (c *bandwidthQoS) execCommandInPod(ctx context.Context, podName, containerName string, command []string) (string, error) {
	req := c.base.K8SClient.CoreV1().RESTClient().Post().
		Resource("pods").
		Name(podName).
		Namespace(testNamespace).
		SubResource("exec")

	req.VersionedParams(&corev1.PodExecOptions{
		Container: containerName,
		Command:   command,
		Stdin:     false,
		Stdout:    true,
		Stderr:    true,
		TTY:       false,
	}, scheme.ParameterCodec)

	exec, err := remotecommand.NewSPDYExecutor(c.base.KubeClient.RestConfig, "POST", req.URL())
	if err != nil {
		return "", fmt.Errorf("创建SPDY执行器失败: %v", err)
	}

	var stdout, stderr bytes.Buffer
	err = exec.Stream(remotecommand.StreamOptions{
		Stdout: &stdout,
		Stderr: &stderr,
		Tty:    false,
	})
	if err != nil {
		return "", fmt.Errorf("执行命令失败: %v, stderr: %s", err, stderr.String())
	}

	return stdout.String(), nil
}

// parseIperf3Output 解析iperf3输出，返回带宽值（Mbits/sec）
func (c *bandwidthQoS) parseIperf3Output(output string) (float64, error) {
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		// 查找包含 "receiver" 的行，这行包含最终的带宽结果
		if strings.Contains(line, "receiver") {
			// 使用正则表达式提取带宽值
			re := regexp.MustCompile(`(\d+\.?\d*)\s+Mbits/sec`)
			matches := re.FindStringSubmatch(line)
			if len(matches) > 1 {
				return strconv.ParseFloat(matches[1], 64)
			}
		}
	}
	return 0.0, fmt.Errorf("未找到带宽测试结果")
}

// testDifferentBandwidthUnits 测试不同单位的带宽配置
func (c *bandwidthQoS) testDifferentBandwidthUnits(ctx context.Context) error {
	logger.Infof(ctx, "测试不同单位的带宽配置...")

	// 测试不同单位的带宽配置
	testCases := []struct {
		name    string
		ingress string
		egress  string
	}{
		{
			name:    "mb-unit-test-1",
			ingress: "1M",
			egress:  "0.5M",
		},
		{
			name:    "mb-unit-test-2",
			ingress: "2M",
			egress:  "1M",
		},
	}

	for _, tc := range testCases {
		logger.Infof(ctx, "测试用例: %s", tc.name)

		// 创建带有不同单位的测试Pod
		podName := fmt.Sprintf("test-unit-%s", tc.name)

		if err := c.createTestPodWithBandwidth(ctx, podName, tc.ingress, tc.egress); err != nil {
			logger.Errorf(ctx, "创建测试Pod失败: %v", err)
			continue
		}

		// 等待Pod运行
		if err := c.waitPodRunning(ctx, podName); err != nil {
			logger.Errorf(ctx, "等待Pod运行失败: %v", err)
			continue
		}

		// 验证Pod的annotation配置
		pod, err := c.base.K8SClient.CoreV1().Pods(testNamespace).Get(ctx, podName, metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "获取Pod失败: %v", err)
			continue
		}

		// 检查annotation是否正确
		annotations := pod.Annotations
		if annotations != nil {
			if ingress, exists := annotations[ingressBandwidthAnnotation]; exists && ingress == tc.ingress {
				logger.Infof(ctx, "测试用例 %s ingress配置正确: %s", tc.name, ingress)
			} else {
				logger.Errorf(ctx, "测试用例 %s ingress配置错误", tc.name)
			}

			if egress, exists := annotations[egressBandwidthAnnotation]; exists && egress == tc.egress {
				logger.Infof(ctx, "测试用例 %s egress配置正确: %s", tc.name, egress)
			} else {
				logger.Errorf(ctx, "测试用例 %s egress配置错误", tc.name)
			}
		}

		// 删除测试Pod
		if err := c.deleteTestPod(ctx, podName); err != nil {
			logger.Warnf(ctx, "删除测试Pod失败: %v", err)
		}
	}

	logger.Infof(ctx, "不同单位带宽配置测试完成")
	return nil
}

// createTestPodWithBandwidth 创建带有指定带宽限制的测试Pod
func (c *bandwidthQoS) createTestPodWithBandwidth(ctx context.Context, podName, ingress, egress string) error {
	pod := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      podName,
			Namespace: testNamespace,
			Annotations: map[string]string{
				ingressBandwidthAnnotation: ingress,
				egressBandwidthAnnotation:  egress,
			},
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name:  "nginx",
					Image: "registry.baidubce.com/cce-plugin-dev/networkstatic/iperf3",
					Command: []string{
						"sleep",
						"300",
					},
				},
			},
			RestartPolicy: corev1.RestartPolicyNever,
		},
	}

	_, err := c.base.K8SClient.CoreV1().Pods(testNamespace).Create(ctx, pod, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建测试Pod失败: %v", err)
	}

	return nil
}

// waitPodRunning 等待指定Pod运行
func (c *bandwidthQoS) waitPodRunning(ctx context.Context, podName string) error {
	return wait.Poll(2*time.Second, 60*time.Second, func() (bool, error) {
		pod, err := c.base.K8SClient.CoreV1().Pods(testNamespace).Get(ctx, podName, metav1.GetOptions{})
		if err != nil {
			return false, err
		}

		return pod.Status.Phase == corev1.PodRunning, nil
	})
}

// deleteTestPod 删除测试Pod
func (c *bandwidthQoS) deleteTestPod(ctx context.Context, podName string) error {
	return c.base.K8SClient.CoreV1().Pods(testNamespace).Delete(ctx, podName, metav1.DeleteOptions{})
}

// int32Ptr 返回int32指针
func int32Ptr(i int32) *int32 {
	return &i
}
