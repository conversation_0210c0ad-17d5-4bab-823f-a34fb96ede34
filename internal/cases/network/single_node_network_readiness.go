/*
Copyright 2024 Baidu Inc.

本文件包含了针对CCE集群单节点网络就绪时间的自动化测试用例。

用例主要验证以下内容：
1. 检查集群是否有可用节点组，没有就新建一个节点组
2. 执行节点组扩容操作，增加一个节点
3. 监控并等待新节点就绪
4. 记录并输出节点网络就绪时间

测试流程：
1. 检查集群当前节点组状态，获取可用的节点组
2. 如果没有可用节点组，则创建一个新的节点组
3. 记录扩容开始时间
4. 对选定的节点组进行扩容操作（+1节点）
5. 等待新节点就绪，监控节点状态变化
6. 记录节点就绪时间并计算总耗时
7. 输出节点网络就绪时间统计信息
8. 清理测试过程中创建的资源（如果创建了新节点组）

该测试用于评估CCE集群节点网络就绪性能，为性能优化提供数据支撑。
*/

package network

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
)

const (
	// SingleNodeNetworkReadinessCaseName - case 名字
	SingleNodeNetworkReadinessCaseName cases.CaseName = "SingleNodeNetworkReadiness"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), SingleNodeNetworkReadinessCaseName, NewSingleNodeNetworkReadiness)
}

type singleNodeNetworkReadiness struct {
	base             *cases.BaseClient
	instanceGroup    string
	originalReplicas int
	createdNodeGroup bool // 标记是否是我们创建的节点组
	startTime        time.Time
	nodeReadyTime    time.Time
	resources        []cases.Resource
}

// NewSingleNodeNetworkReadiness - 测试案例
func NewSingleNodeNetworkReadiness(ctx context.Context) cases.Interface {
	return &singleNodeNetworkReadiness{}
}

func (c *singleNodeNetworkReadiness) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base is nil")
	}

	c.base = base
	return nil
}

func (c *singleNodeNetworkReadiness) Name() cases.CaseName {
	return SingleNodeNetworkReadinessCaseName
}

func (c *singleNodeNetworkReadiness) Desc() string {
	return "测试单节点网络就绪时间（创建小规格节点组）"
}

func (c *singleNodeNetworkReadiness) Check(ctx context.Context) ([]cases.Resource, error) {
	logger.Infof(ctx, "开始执行单节点网络就绪时间测试")

	// 1. 获取或创建节点组
	instanceGroupID, err := c.getOrCreateInstanceGroup(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取或创建节点组失败: %v", err)
	}
	c.instanceGroup = instanceGroupID

	// 2. 获取原始副本数
	originalReplicas, err := c.getInstanceGroupReplicas(ctx, instanceGroupID)
	if err != nil {
		return nil, fmt.Errorf("获取节点组副本数失败: %v", err)
	}
	c.originalReplicas = originalReplicas
	logger.Infof(ctx, "节点组 %s 当前副本数: %d", instanceGroupID, originalReplicas)

	// 3. 记录扩容开始时间
	c.startTime = time.Now()
	logger.Infof(ctx, "扩容开始时间: %v", c.startTime.Format("2006-01-02 15:04:05"))

	// 4. 执行扩容操作
	targetReplicas := originalReplicas + 1
	err = c.scaleInstanceGroup(ctx, instanceGroupID, targetReplicas)
	if err != nil {
		return nil, fmt.Errorf("扩容节点组失败: %v", err)
	}

	// 5. 等待新节点就绪
	err = c.waitForNewNodeReady(ctx, targetReplicas)
	if err != nil {
		return nil, fmt.Errorf("等待节点就绪失败: %v", err)
	}

	// 6. 计算并输出时间统计
	totalDuration := c.nodeReadyTime.Sub(c.startTime)
	logger.Infof(ctx, "=== 单节点网络就绪时间统计 ===")
	logger.Infof(ctx, "扩容开始时间: %v", c.startTime.Format("2006-01-02 15:04:05.000"))
	logger.Infof(ctx, "节点就绪时间: %v", c.nodeReadyTime.Format("2006-01-02 15:04:05.000"))
	logger.Infof(ctx, "总就绪时间: %v", totalDuration)
	logger.Infof(ctx, "总就绪时间(秒): %.2f", totalDuration.Seconds())
	logger.Infof(ctx, "===========================")

	return c.resources, nil
}

// getOrCreateInstanceGroup 获取或创建节点组
func (c *singleNodeNetworkReadiness) getOrCreateInstanceGroup(ctx context.Context) (string, error) {
	// 为了批量节点测试，直接创建新的小规格节点组，避免大规格节点供应量不足的问题
	logger.Infof(ctx, "为批量节点测试创建新的小规格节点组")
	return c.createInstanceGroup(ctx)
}

// createInstanceGroup 创建新的节点组
func (c *singleNodeNetworkReadiness) createInstanceGroup(ctx context.Context) (string, error) {
	// 获取集群信息用于创建节点组
	cluster, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		return "", fmt.Errorf("获取集群信息失败: %v", err)
	}

	// 获取集群中已有节点的信息，仅用于获取镜像ID
	instances, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
		ClusterRole: types.ClusterRoleNode,
	}, nil)
	if err != nil {
		return "", fmt.Errorf("获取集群节点信息失败: %v", err)
	}
	if len(instances.InstancePage.InstanceList) == 0 {
		return "", fmt.Errorf("集群中没有可用的节点，无法获取配置信息")
	}

	// 仅获取镜像ID，其他配置使用固定小规格
	instance := instances.InstancePage.InstanceList[0]
	imageID := instance.Spec.ImageID
	if imageID == "" {
		return "", fmt.Errorf("获取到的镜像ID为空，无法创建节点组")
	}
	logger.Infof(ctx, "将使用已有节点的镜像ID: %s", imageID)

	// 获取集群额外信息，找到可用的子网
	clusterExtraInfo, err := c.base.CCEHostClient.GetClusterExtraInfo(ctx, c.base.ClusterID, nil)
	if err != nil {
		return "", fmt.Errorf("获取集群额外信息失败: %v", err)
	}

	var nodeSubnetID string
	for _, subnet := range clusterExtraInfo.Subnets {
		if subnet.SubnetCIDR != "" {
			nodeSubnetID = subnet.SubnetID
			break
		}
	}

	if nodeSubnetID == "" {
		return "", fmt.Errorf("未找到可用的子网")
	}

	logger.Infof(ctx, "将使用子网 %s 创建节点组", nodeSubnetID)

	// 从集群信息获取VPCID
	vpcID := cluster.Cluster.Spec.VPCID
	logger.Infof(ctx, "将使用VPC %s 创建节点组", vpcID)

	// 创建节点组，使用固定的小规格配置，按照标准格式
	igName := fmt.Sprintf("test-network-readiness-%d", time.Now().Unix())
	createReq := &ccev2.CreateInstanceGroupRequest{
		InstanceGroupSpec: types.InstanceGroupSpec{
			InstanceGroupName: igName,
			Replicas:          0, // 创建0个节点的节点组，避免不必要的节点创建
			InstanceTemplate: types.InstanceTemplate{
				InstanceSpec: types.InstanceSpec{
					ClusterRole:  types.ClusterRoleNode,
					MachineType:  types.MachineTypeBCC, // 使用BCC机型
					InstanceType: "N5",                 // 小规格：2核8G
					VPCConfig: types.VPCConfig{
						VPCID:       vpcID,
						VPCSubnetID: nodeSubnetID,
						SecurityGroup: types.SecurityGroup{
							EnableCCERequiredSecurityGroup: true,
						},
					},
					InstanceResource: types.InstanceResource{
						CPU:          2,             // 固定2核
						MEM:          8,             // 固定8G内存
						MachineSpec:  "bcc.g4.c2m8", // 机器规格
						SpecID:       "g4",          // 规格ID
						RootDiskType: "cloud_hp1",   // 系统盘类型
						RootDiskSize: 100,           // 系统盘大小(GB)
					},
					ImageID:       imageID,
					AdminPassword: "Test123456!",
				},
			},
		},
	}

	// 发送创建请求
	createResp, err := c.base.CCEClient.CreateInstanceGroup(ctx, c.base.ClusterID, createReq, nil)
	if err != nil {
		return "", fmt.Errorf("创建节点组失败: %v", err)
	}

	// 保存节点组ID
	instanceGroupID := createResp.InstanceGroupID
	c.createdNodeGroup = true
	logger.Infof(ctx, "成功创建节点组 %s (ID: %s)", igName, instanceGroupID)

	// 将创建的节点组添加到资源列表
	c.resources = append(c.resources, cases.Resource{
		CaseName: c.Name(),
		Type:     cases.ResourceTypeCCEInstanceGroup,
		ID:       instanceGroupID,
	})

	// 等待节点组就绪
	logger.Infof(ctx, "等待节点组中的节点就绪...")

	// 创建instanceGroup资源对象并等待就绪
	ig, err := resource.NewInstanceGroup(ctx, c.base, instanceGroupID, 10*time.Second, 15*time.Minute)
	if err != nil {
		return "", fmt.Errorf("创建instanceGroup资源对象失败: %v", err)
	}

	if err := ig.CheckResource(ctx); err != nil {
		return "", fmt.Errorf("等待节点组就绪失败: %v", err)
	}

	logger.Infof(ctx, "节点组 %s 已就绪", instanceGroupID)

	return instanceGroupID, nil
}

// scaleInstanceGroup 扩容节点组
func (c *singleNodeNetworkReadiness) scaleInstanceGroup(ctx context.Context, instanceGroupID string, replicas int) error {
	request := &ccev2.UpdateInstanceGroupReplicasRequest{
		Replicas: replicas,
	}

	logger.Infof(ctx, "开始扩容节点组 %s 到 %d 个副本", instanceGroupID, replicas)

	resp, err := c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, c.base.ClusterID, instanceGroupID, request, nil)
	if err != nil {
		return fmt.Errorf("更新节点组副本数失败: %v", err)
	}

	logger.Infof(ctx, "节点组扩容请求发送成功，RequestID: %s", resp.RequestID)
	return nil
}

// getInstanceGroupReplicas 获取节点组副本数
func (c *singleNodeNetworkReadiness) getInstanceGroupReplicas(ctx context.Context, instanceGroupID string) (int, error) {
	ig, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, instanceGroupID, nil)
	if err != nil {
		return 0, fmt.Errorf("获取节点组信息失败: %v", err)
	}

	return ig.InstanceGroup.Spec.Replicas, nil
}

// waitForNewNodeReady 等待新节点就绪
func (c *singleNodeNetworkReadiness) waitForNewNodeReady(ctx context.Context, targetReplicas int) error {
	logger.Infof(ctx, "开始等待新节点就绪，目标副本数: %d", targetReplicas)

	// 获取扩容前的节点列表
	initialNodes, err := c.getClusterNodes(ctx)
	if err != nil {
		return fmt.Errorf("获取初始节点列表失败: %v", err)
	}
	logger.Infof(ctx, "扩容前节点数量: %d", len(initialNodes))

	// 等待新节点出现并就绪
	return wait.PollImmediate(10*time.Second, 10*time.Minute, func() (bool, error) {
		// 检查节点组副本状态
		ig, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroup, nil)
		if err != nil {
			logger.Warnf(ctx, "获取节点组状态失败: %v", err)
			return false, nil
		}

		readyReplicas := ig.InstanceGroup.Status.ReadyReplicas
		scalingReplicas := ig.InstanceGroup.Status.ScalingReplicas
		deletingReplicas := ig.InstanceGroup.Status.DeletingReplicas

		logger.Infof(ctx, "节点组状态 - 就绪: %d, 扩容中: %d, 删除中: %d, 目标: %d",
			readyReplicas, scalingReplicas, deletingReplicas, targetReplicas)

		// 检查Kubernetes节点状态
		currentNodes, err := c.getClusterNodes(ctx)
		if err != nil {
			logger.Warnf(ctx, "获取当前节点列表失败: %v", err)
			return false, nil
		}

		logger.Infof(ctx, "当前节点数量: %d", len(currentNodes))

		// 如果节点数量增加，检查新节点是否就绪
		if len(currentNodes) > len(initialNodes) {
			// 找到新节点
			newNodes := c.findNewNodes(initialNodes, currentNodes)
			if len(newNodes) > 0 {
				logger.Infof(ctx, "发现 %d 个新节点", len(newNodes))

				// 检查所有新节点是否就绪
				allReady := true
				for _, node := range newNodes {
					if !c.isNodeReady(&node) {
						logger.Infof(ctx, "节点 %s 尚未就绪", node.Name)
						allReady = false
						break
					}
				}

				if allReady && readyReplicas >= targetReplicas {
					c.nodeReadyTime = time.Now()
					logger.Infof(ctx, "所有新节点已就绪！节点就绪时间: %v", c.nodeReadyTime.Format("2006-01-02 15:04:05.000"))
					return true, nil
				}
			}
		}

		return false, nil
	})
}

// getClusterNodes 获取集群节点列表
func (c *singleNodeNetworkReadiness) getClusterNodes(ctx context.Context) ([]corev1.Node, error) {
	nodeList, err := c.base.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, err
	}

	return nodeList.Items, nil
}

// findNewNodes 找到新增的节点
func (c *singleNodeNetworkReadiness) findNewNodes(initialNodes, currentNodes []corev1.Node) []corev1.Node {
	initialNodeMap := make(map[string]struct{})
	for _, node := range initialNodes {
		initialNodeMap[node.Name] = struct{}{}
	}

	var newNodes []corev1.Node
	for _, node := range currentNodes {
		if _, exists := initialNodeMap[node.Name]; !exists {
			newNodes = append(newNodes, node)
		}
	}

	return newNodes
}

// isNodeReady 检查节点是否就绪
func (c *singleNodeNetworkReadiness) isNodeReady(node *corev1.Node) bool {
	for _, condition := range node.Status.Conditions {
		if condition.Type == corev1.NodeNetworkUnavailable {
			return condition.Status == corev1.ConditionFalse
		}
	}
	return false
}

func (c *singleNodeNetworkReadiness) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理测试环境")

	// 如果创建了新的节点组，需要删除它
	if c.createdNodeGroup && c.instanceGroup != "" {
		logger.Infof(ctx, "删除测试创建的节点组: %s", c.instanceGroup)

		// 首先将副本数设置为0
		err := c.scaleInstanceGroup(ctx, c.instanceGroup, 0)
		if err != nil {
			logger.Errorf(ctx, "缩容节点组到0失败: %v", err)
		} else {
			// 等待节点删除完成
			logger.Infof(ctx, "等待节点组节点删除完成")
			time.Sleep(2 * time.Minute)
		}

		// 删除节点组
		_, err = c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.instanceGroup, true, nil)
		if err != nil {
			logger.Errorf(ctx, "删除节点组失败: %v", err)
			return err
		}

		logger.Infof(ctx, "节点组删除请求已发送")
	} else if c.instanceGroup != "" && c.originalReplicas >= 0 {
		// 如果使用的是现有节点组，恢复原始副本数
		logger.Infof(ctx, "恢复节点组 %s 到原始副本数: %d", c.instanceGroup, c.originalReplicas)
		err := c.scaleInstanceGroup(ctx, c.instanceGroup, c.originalReplicas)
		if err != nil {
			logger.Errorf(ctx, "恢复节点组副本数失败: %v", err)
			return err
		}
	}

	return nil
}

func (c *singleNodeNetworkReadiness) Continue(ctx context.Context) bool {
	return true
}

func (c *singleNodeNetworkReadiness) ConfigFormat() string {
	return `{}`
}
