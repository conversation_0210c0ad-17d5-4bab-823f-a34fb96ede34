/*
PSTS IP v6 测试用例

功能描述：
测试主网卡辅助IP的功能，使得校验集群在开启PSTS时子网信息中存在指定子网，
特别验证IPv6 IP在StatefulSet扩缩容过程中的一致性。

测试流程：
1. 使用 registry.baidubce.com/cce/nginx-alpine-go:latest 镜像创建用于测试的pod
2. 需要在配置case中设置 config: subnetId，如果没有需要报错
3. 在确认当前集群没有指定子网后，创建对应的PSTS以及STS，来使用PSTS的功能
4. 当STS确认创建pod完成后，确认是否通过在集群中 get subnet，获取到PSTS中配置的子网信息
5. 扩缩容STS，确认在这个pod删除又重新创建之后，IPv6的IP是否保持一致

预期结果：
每一步请确认对应的执行结果，如果能够顺利执行并且按照每一步提供的结果，则是符合预期。

注意事项：
此测试会创建测试资源，测试完成后会自动清理所有创建的资源。
*/

package network

import (
	"context"
	"fmt"
	"time"
	"encoding/json"
	"strings"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/apimachinery/pkg/util/wait"
)

const (
	// PSTSIPV6CaseName - case 名字
	PSTSIPV6CaseName cases.CaseName = "PSTSIPV6"
)

// pstsIPV6Config 测试配置
type pstsIPV6Config struct {
	SubnetID     string `json:"subnetId"`
	IPv6CIDRBase string `json:"ipv6CidrBase"`
}

// pstsIPV6 是用于测试PSTS IPv6功能的测试用例
type pstsIPV6 struct {
	base                *cases.BaseClient
	config              pstsIPV6Config
	testNamespace       string
	testPSTSName        string
	testStatefulSetName string
	testServiceName     string
	createdResources    []string
	originalPodIPv4     string
	originalPodIPv6     string
}

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), PSTSIPV6CaseName, NewPSTSIPV6)
}

// NewPSTSIPV6 创建一个新的PSTS IPv6测试用例
func NewPSTSIPV6(ctx context.Context) cases.Interface {
	return &pstsIPV6{
		testNamespace:       "default",
		testPSTSName:        "test-psts-ipv6",
		testStatefulSetName: "test-sts-ipv6",
		testServiceName:     "test-service-ipv6",
		createdResources:    make([]string, 0),
	}
}

// Name 返回测试用例名称
func (c *pstsIPV6) Name() cases.CaseName {
	return PSTSIPV6CaseName
}

// Desc 返回测试用例描述
func (c *pstsIPV6) Desc() string {
	return "测试主网卡辅助IP的功能，使用Fixed模式的PSTS验证集群在开启PSTS时子网信息中存在指定子网，特别验证IPv4和IPv6 IP在StatefulSet扩缩容过程中的一致性（Fixed模式下IP地址必须保持不变）"
}

// Init 初始化测试用例
func (c *pstsIPV6) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	logger.Infof(ctx, "初始化PSTS IPv6测试用例")
	if base == nil {
		return fmt.Errorf("base client is nil")
	}
	c.base = base

	// 解析配置
	if len(config) > 0 {
		if err := json.Unmarshal(config, &c.config); err != nil {
			return fmt.Errorf("解析配置失败: %v", err)
		}
	}

	// 检查必需的配置
	if c.config.SubnetID == "" {
		return fmt.Errorf("配置中缺少必需的subnetId参数")
	}

	logger.Infof(ctx, "使用子网ID: %s", c.config.SubnetID)
	logger.Infof(ctx, "使用IPv6网段: %s", c.config.IPv6CIDRBase)
	return nil
}

// Check 执行测试
func (c *pstsIPV6) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)

	logger.Infof(ctx, "开始执行PSTS IPv6功能测试")

	// 1. 确认当前集群没有指定的子网
	if err := c.checkSubnetNotExists(ctx); err != nil {
		return resources, fmt.Errorf("检查子网状态失败: %v", err)
	}

	// 2. 创建PSTS资源
	if err := c.createPSTS(ctx); err != nil {
		return resources, fmt.Errorf("创建PSTS失败: %v", err)
	}

	// 3. 创建Service
	if err := c.createService(ctx); err != nil {
		return resources, fmt.Errorf("创建Service失败: %v", err)
	}

	// 4. 创建StatefulSet
	if err := c.createStatefulSet(ctx); err != nil {
		return resources, fmt.Errorf("创建StatefulSet失败: %v", err)
	}

	// 5. 等待StatefulSet就绪
	if err := c.waitForStatefulSetReady(ctx); err != nil {
		return resources, fmt.Errorf("等待StatefulSet就绪失败: %v", err)
	}

	// 6. 验证子网信息
	if err := c.verifySubnetExists(ctx); err != nil {
		return resources, fmt.Errorf("验证子网信息失败: %v", err)
	}

	// 7. 记录原始IPv6地址 - 严格验证Pod必须有IPv6地址
	logger.Infof(ctx, "开始严格验证Pod的IPv6地址分配情况")
	if err := c.recordOriginalIPv6(ctx); err != nil {
		logger.Errorf(ctx, "❌ PSTS IPv6 Fixed模式测试失败：%v", err)
		return resources, fmt.Errorf("PSTS IPv6 Fixed模式测试失败 - %v", err)
	}

	// 8. 测试扩缩容IPv6一致性 - 严格验证IPv6地址固定性
	logger.Infof(ctx, "开始测试IPv6地址在扩缩容过程中的固定性")
	if err := c.testIPv6Consistency(ctx); err != nil {
		logger.Errorf(ctx, "❌ PSTS IPv6 Fixed模式测试失败：%v", err)
		return resources, fmt.Errorf("PSTS IPv6 Fixed模式测试失败 - %v", err)
	}

	logger.Infof(ctx, "✅ PSTS IPv6 Fixed模式测试完全成功！")
	logger.Infof(ctx, "✅ 验证结果：")
	logger.Infof(ctx, "  - Pod成功分配到固定的IPv4地址：%s", c.originalPodIPv4)
	logger.Infof(ctx, "  - Pod成功分配到固定的IPv6地址：%s", c.originalPodIPv6)
	logger.Infof(ctx, "  - 扩缩容后IPv4和IPv6地址都保持不变")
	logger.Infof(ctx, "  - Fixed模式的IP地址固定性验证完全成功")
	return resources, nil
}

// checkSubnetNotExists 确认当前集群没有指定的子网
func (c *pstsIPV6) checkSubnetNotExists(ctx context.Context) error {
	logger.Infof(ctx, "检查集群中是否已存在子网: %s", c.config.SubnetID)

	_, err := c.base.KubeClient.GetSubnet(ctx, c.config.SubnetID, &kube.GetOptions{})
	if err != nil {
		if kerrors.IsNotFound(err) {
			logger.Infof(ctx, "确认集群中不存在子网 %s，可以继续测试", c.config.SubnetID)
			return nil
		}
		// 如果是其他错误，可能是集群不支持Subnet CRD
		logger.Warnf(ctx, "检查子网时出现错误: %v，可能集群不支持PSTS功能", err)
		return fmt.Errorf("集群可能不支持PSTS功能，跳过测试: %v", err)
	}

	logger.Warnf(ctx, "集群中已存在子网 %s，继续测试", c.config.SubnetID)
	return nil
}

// createPSTS 创建PSTS资源
func (c *pstsIPV6) createPSTS(ctx context.Context) error {
	logger.Infof(ctx, "创建PSTS资源: %s", c.testPSTSName)

	// 首先检查PSTS是否已存在，如果存在则删除
	if err := c.cleanupExistingPSTS(ctx); err != nil {
		logger.Warnf(ctx, "清理已存在的PSTS失败: %v", err)
	}

	// 检查子网是否存在，如果不存在则创建
	// 注意：如果子网已经在集群中使用，则跳过创建
	if err := c.ensureSubnetExists(ctx); err != nil {
		return fmt.Errorf("确保子网存在失败: %v", err)
	}

	psts := &types.PodSubnetTopologySpread{
		TypeMeta: metav1.TypeMeta{
			APIVersion: "cce.baidubce.com/v2",
			Kind:       "PodSubnetTopologySpread",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.testPSTSName,
			Namespace: c.testNamespace,
			Labels: map[string]string{
				"test": "psts-ipv6",
			},
		},
		Spec: types.PodSubnetTopologySpreadSpec{
			Subnets: map[string][]types.CustomAllocation{
				c.config.SubnetID: {
					{
						Family: "4",
						Range: []types.CustomIPRange{
							{
								Start: "***********",
								End:   "***********",
							},
						},
					},
					{
						Family: "6",
						Range: []types.CustomIPRange{
							{
								Start: c.config.IPv6CIDRBase,
								End:   c.getIPv6EndAddress(c.config.IPv6CIDRBase),
							},
						},
					},
				},
			},
			Strategy: &types.IPAllocationStrategy{
				ReleaseStrategy:      "Never",
				Type:                 "Fixed",
				EnableReuseIPAddress: true,
			},
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": "test-sts-ipv6",
				},
			},
		},
	}

	err := c.createPSTSResource(ctx, psts)
	if err != nil {
		return fmt.Errorf("创建PSTS资源失败: %v", err)
	}

	c.createdResources = append(c.createdResources, "psts:"+c.testPSTSName)
	logger.Infof(ctx, "PSTS资源创建成功: %s", c.testPSTSName)
	return nil
}

// cleanupExistingPSTS 清理已存在的PSTS资源
func (c *pstsIPV6) cleanupExistingPSTS(ctx context.Context) error {
	logger.Infof(ctx, "检查并清理已存在的PSTS资源: %s", c.testPSTSName)

	clientSet, err := c.base.KubeClient.NewDynamicClient("cce.baidubce.com", "v2", "podsubnettopologyspreads")
	if err != nil {
		return fmt.Errorf("创建动态客户端失败: %v", err)
	}

	// 检查PSTS是否存在
	_, err = clientSet.Namespace(c.testNamespace).Get(ctx, c.testPSTSName, metav1.GetOptions{})
	if err != nil {
		if kerrors.IsNotFound(err) {
			logger.Infof(ctx, "PSTS资源不存在，无需清理")
			return nil
		}
		return fmt.Errorf("检查PSTS资源失败: %v", err)
	}

	// 删除已存在的PSTS
	err = clientSet.Namespace(c.testNamespace).Delete(ctx, c.testPSTSName, metav1.DeleteOptions{})
	if err != nil {
		return fmt.Errorf("删除已存在的PSTS失败: %v", err)
	}

	logger.Infof(ctx, "已清理存在的PSTS资源: %s", c.testPSTSName)

	// 等待资源删除完成
	time.Sleep(10 * time.Second)
	return nil
}

// ensureSubnetExists 确保子网存在
func (c *pstsIPV6) ensureSubnetExists(ctx context.Context) error {
	logger.Infof(ctx, "确保子网资源存在: %s", c.config.SubnetID)

	// 检查子网是否已存在
	subnet, err := c.base.KubeClient.GetSubnet(ctx, c.config.SubnetID, &kube.GetOptions{})
	if err == nil {
		logger.Infof(ctx, "子网 %s 已存在，检查其配置", c.config.SubnetID)
		logger.Infof(ctx, "  子网名称: %s", subnet.Name)
		logger.Infof(ctx, "  子网CIDR: %s", subnet.Spec.CIDR)
		logger.Infof(ctx, "  可用区: %s", subnet.Spec.AvailabilityZone)
		logger.Infof(ctx, "  独占模式: %t", subnet.Spec.Exclusive)
		return nil
	}

	if !kerrors.IsNotFound(err) {
		return fmt.Errorf("检查子网失败: %v", err)
	}

	logger.Infof(ctx, "子网 %s 不存在，跳过创建（可能由集群管理）", c.config.SubnetID)
	return nil
}

// createSubnetResource 使用动态客户端创建Subnet资源
func (c *pstsIPV6) createSubnetResource(ctx context.Context, subnet *types.Subnet) error {
	clientSet, err := c.base.KubeClient.NewDynamicClient("cce.baidubce.com", "v1", "subnets")
	if err != nil {
		return fmt.Errorf("创建动态客户端失败: %v", err)
	}

	// 转换为unstructured对象
	unstructuredObj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(subnet)
	if err != nil {
		return fmt.Errorf("转换Subnet为unstructured失败: %v", err)
	}

	// 创建资源
	_, err = clientSet.Create(ctx, &unstructured.Unstructured{Object: unstructuredObj}, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建Subnet失败: %v", err)
	}

	return nil
}

// createPSTSResource 使用动态客户端创建PSTS资源
func (c *pstsIPV6) createPSTSResource(ctx context.Context, psts *types.PodSubnetTopologySpread) error {
	clientSet, err := c.base.KubeClient.NewDynamicClient("cce.baidubce.com", "v2", "podsubnettopologyspreads")
	if err != nil {
		return fmt.Errorf("创建动态客户端失败: %v", err)
	}

	// 转换为unstructured对象
	unstructuredObj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(psts)
	if err != nil {
		return fmt.Errorf("转换PSTS为unstructured失败: %v", err)
	}

	// 创建资源
	_, err = clientSet.Namespace(c.testNamespace).Create(ctx, &unstructured.Unstructured{Object: unstructuredObj}, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建PSTS失败: %v", err)
	}

	return nil
}

// createService 创建Service
func (c *pstsIPV6) createService(ctx context.Context) error {
	logger.Infof(ctx, "创建Service: %s", c.testServiceName)

	// 先删除已存在的Service
	err := c.base.K8SClient.CoreV1().Services(c.testNamespace).Delete(ctx, c.testServiceName, metav1.DeleteOptions{})
	if err != nil && !kerrors.IsNotFound(err) {
		logger.Warnf(ctx, "删除已存在的Service失败: %v", err)
	}

	service := &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.testServiceName,
			Namespace: c.testNamespace,
			Labels: map[string]string{
				"test": "psts-ipv6",
			},
		},
		Spec: corev1.ServiceSpec{
			ClusterIP: "None", // Headless Service
			Selector: map[string]string{
				"app": "test-sts-ipv6",
			},
			Ports: []corev1.ServicePort{
				{
					Name:       "web",
					Port:       80,
					TargetPort: intstr.FromInt(80),
					Protocol:   corev1.ProtocolTCP,
				},
			},
		},
	}

	_, err = c.base.K8SClient.CoreV1().Services(c.testNamespace).Create(ctx, service, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建Service失败: %v", err)
	}

	c.createdResources = append(c.createdResources, "service:"+c.testServiceName)
	logger.Infof(ctx, "Service创建成功: %s", c.testServiceName)
	return nil
}

// createStatefulSet 创建StatefulSet
func (c *pstsIPV6) createStatefulSet(ctx context.Context) error {
	logger.Infof(ctx, "创建StatefulSet: %s", c.testStatefulSetName)

	// 先删除已存在的StatefulSet
	err := c.base.K8SClient.AppsV1().StatefulSets(c.testNamespace).Delete(ctx, c.testStatefulSetName, metav1.DeleteOptions{})
	if err != nil && !kerrors.IsNotFound(err) {
		logger.Warnf(ctx, "删除已存在的StatefulSet失败: %v", err)
	}

	// 等待删除完成
	time.Sleep(10 * time.Second)

	statefulSet := &appsv1.StatefulSet{
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.testStatefulSetName,
			Namespace: c.testNamespace,
			Labels: map[string]string{
				"test": "psts-ipv6",
			},
		},
		Spec: appsv1.StatefulSetSpec{
			Replicas:    pstsIPV6Int32Ptr(1),
			ServiceName: c.testServiceName,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app":                           "test-sts-ipv6",
					"k8snet.iqiyi.com/staticip":     "true",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app":                           "test-sts-ipv6",
						"k8snet.iqiyi.com/staticip":     "true",
					},
					Annotations: map[string]string{
						"cce.baidubce.com/enable-ipv6":     "true",
						"cce.baidubce.com/ip-family":       "dual",
						"kubernetes.io/cce.enable-ipv6":   "true",
						"cce.baidubce.com/ipv6-enabled":   "true",
					},
				},
				Spec: corev1.PodSpec{
					RestartPolicy: corev1.RestartPolicyAlways,
					// 启用IPv6双栈网络
					DNSPolicy: corev1.DNSClusterFirst,
					// 明确指定IPv6双栈支持
					HostNetwork: false,
					Containers: []corev1.Container{
						{
							Name:            "nginx",
							Image:           "registry.baidubce.com/cce/nginx-alpine-go:latest",
							ImagePullPolicy: corev1.PullAlways,
							Ports: []corev1.ContainerPort{
								{
									ContainerPort: 80,
									Name:          "web",
									Protocol:      corev1.ProtocolTCP,
								},
							},
							LivenessProbe: &corev1.Probe{
								Handler: corev1.Handler{
									HTTPGet: &corev1.HTTPGetAction{
										Path: "/",
										Port: intstr.FromInt(80),
									},
								},
								InitialDelaySeconds: 20,
								TimeoutSeconds:      5,
								PeriodSeconds:       5,
								FailureThreshold:    3,
							},
							ReadinessProbe: &corev1.Probe{
								Handler: corev1.Handler{
									HTTPGet: &corev1.HTTPGetAction{
										Path: "/",
										Port: intstr.FromInt(80),
									},
								},
								InitialDelaySeconds: 5,
								TimeoutSeconds:      1,
								PeriodSeconds:       5,
								FailureThreshold:    3,
							},
						},
					},
				},
			},
			PodManagementPolicy: appsv1.OrderedReadyPodManagement,
			UpdateStrategy: appsv1.StatefulSetUpdateStrategy{
				Type: appsv1.RollingUpdateStatefulSetStrategyType,
				RollingUpdate: &appsv1.RollingUpdateStatefulSetStrategy{
					Partition: pstsIPV6Int32Ptr(0),
				},
			},
		},
	}

	_, err = c.base.K8SClient.AppsV1().StatefulSets(c.testNamespace).Create(ctx, statefulSet, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建StatefulSet失败: %v", err)
	}

	c.createdResources = append(c.createdResources, "statefulset:"+c.testStatefulSetName)
	logger.Infof(ctx, "StatefulSet创建成功: %s", c.testStatefulSetName)
	return nil
}

// waitForStatefulSetReady 等待StatefulSet就绪
func (c *pstsIPV6) waitForStatefulSetReady(ctx context.Context) error {
	logger.Infof(ctx, "等待StatefulSet就绪: %s", c.testStatefulSetName)

	// 创建StatefulSet资源管理器
	var statefulSet resource.K8SStatefulSet
	err := statefulSet.NewK8SStatefulSet(ctx, c.base, c.testNamespace, c.testStatefulSetName)
	if err != nil {
		return fmt.Errorf("创建StatefulSet资源管理器失败: %v", err)
	}

	// 等待StatefulSet就绪 - 缩短等待时间，增加调试信息
	logger.Infof(ctx, "开始等待StatefulSet就绪，最大等待时间：3分钟")
	err = wait.PollImmediate(10*time.Second, 3*time.Minute, func() (bool, error) {
		sts, err := c.base.K8SClient.AppsV1().StatefulSets(c.testNamespace).Get(ctx, c.testStatefulSetName, metav1.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取StatefulSet失败: %v", err)
			return false, err
		}

		logger.Infof(ctx, "StatefulSet状态: Ready=%d/%d", sts.Status.ReadyReplicas, *sts.Spec.Replicas)

		// 如果有Pod但不就绪，检查Pod状态
		if sts.Status.ReadyReplicas == 0 && *sts.Spec.Replicas > 0 {
			pods, err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).List(ctx, metav1.ListOptions{
				LabelSelector: "app=test-sts-ipv6",
			})
			if err == nil && len(pods.Items) > 0 {
				for _, pod := range pods.Items {
					logger.Infof(ctx, "Pod %s 状态: Phase=%s", pod.Name, pod.Status.Phase)

					// 检查Pod是否失败
					if pod.Status.Phase == corev1.PodFailed {
						return false, fmt.Errorf("Pod %s 创建失败，状态为Failed", pod.Name)
					}

					// 检查容器状态
					for _, containerStatus := range pod.Status.ContainerStatuses {
						if containerStatus.State.Waiting != nil && containerStatus.State.Waiting.Reason == "ImagePullBackOff" {
							return false, fmt.Errorf("Pod %s 容器镜像拉取失败: %s", pod.Name, containerStatus.State.Waiting.Message)
						}
						if containerStatus.State.Terminated != nil && containerStatus.State.Terminated.ExitCode != 0 {
							return false, fmt.Errorf("Pod %s 容器异常退出: %s", pod.Name, containerStatus.State.Terminated.Reason)
						}
					}
				}
			}
		}

		return sts.Status.ReadyReplicas == *sts.Spec.Replicas, nil
	})

	if err != nil {
		// 获取当前StatefulSet和Pod状态用于调试
		sts, stsErr := c.base.K8SClient.AppsV1().StatefulSets(c.testNamespace).Get(ctx, c.testStatefulSetName, metav1.GetOptions{})
		pods, podsErr := c.base.K8SClient.CoreV1().Pods(c.testNamespace).List(ctx, metav1.ListOptions{
			LabelSelector: "app=test-sts-ipv6",
		})

		var debugInfo string
		if stsErr == nil {
			debugInfo += fmt.Sprintf("StatefulSet状态: Ready=%d/%d, ", sts.Status.ReadyReplicas, *sts.Spec.Replicas)
		}
		if podsErr == nil {
			debugInfo += fmt.Sprintf("Pod数量: %d, ", len(pods.Items))
			for _, pod := range pods.Items {
				debugInfo += fmt.Sprintf("Pod %s: Phase=%s, ", pod.Name, pod.Status.Phase)
			}
		}

		return fmt.Errorf("等待StatefulSet就绪超时(3分钟): %v. 调试信息: %s", err, debugInfo)
	}

	// 检查Pod状态
	pods, err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "app=test-sts-ipv6",
	})
	if err != nil {
		return fmt.Errorf("获取Pod列表失败: %v", err)
	}

	for _, pod := range pods.Items {
		if pod.Status.Phase == corev1.PodRunning && pod.Status.PodIP != "" {
			logger.Infof(ctx, "Pod %s 运行正常，IPv4: %s", pod.Name, pod.Status.PodIP)

			// 严格检查IPv6地址 - 在Fixed模式下必须有IPv6地址
			var hasIPv6 bool
			for _, ip := range pod.Status.PodIPs {
				if strings.Contains(ip.IP, ":") {
					logger.Infof(ctx, "Pod %s IPv6地址: %s", pod.Name, ip.IP)
					hasIPv6 = true
				}
			}

			// 如果没有IPv6地址，这是一个严重问题，应该立即报告
			if !hasIPv6 {
				logger.Warnf(ctx, "⚠️ 警告：Pod %s 没有IPv6地址，这可能导致后续的Fixed模式测试失败", pod.Name)
			}
		} else {
			logger.Warnf(ctx, "Pod %s 状态异常: Phase=%s, IP=%s", pod.Name, pod.Status.Phase, pod.Status.PodIP)
		}
	}

	logger.Infof(ctx, "StatefulSet就绪: %s", c.testStatefulSetName)
	return nil
}

// verifySubnetExists 验证子网信息存在
func (c *pstsIPV6) verifySubnetExists(ctx context.Context) error {
	logger.Infof(ctx, "验证集群中是否存在子网: %s", c.config.SubnetID)

	// 等待一段时间让PSTS生效
	logger.Infof(ctx, "等待PSTS生效，等待时间：15秒")
	time.Sleep(15 * time.Second)

	// 尝试获取子网信息
	subnet, err := c.base.KubeClient.GetSubnet(ctx, c.config.SubnetID, &kube.GetOptions{})
	if err != nil {
		if kerrors.IsNotFound(err) {
			return fmt.Errorf("验证失败：集群中仍然不存在子网 %s", c.config.SubnetID)
		}
		return fmt.Errorf("获取子网信息失败: %v", err)
	}

	logger.Infof(ctx, "验证成功：在集群中找到了子网 %s", c.config.SubnetID)
	logger.Infof(ctx, "子网详细信息:")
	logger.Infof(ctx, "  名称: %s", subnet.Name)
	logger.Infof(ctx, "  CIDR: %s", subnet.Spec.CIDR)
	logger.Infof(ctx, "  可用区: %s", subnet.Spec.AvailabilityZone)
	logger.Infof(ctx, "  可用IP数量: %d", subnet.Status.AvailableIPNum)
	logger.Infof(ctx, "  是否启用: %t", subnet.Status.Enable)

	// 验证PSTS状态
	if err := c.verifyPSTSStatus(ctx); err != nil {
		return fmt.Errorf("验证PSTS状态失败: %v", err)
	}

	return nil
}

// verifyPSTSStatus 验证PSTS状态
func (c *pstsIPV6) verifyPSTSStatus(ctx context.Context) error {
	logger.Infof(ctx, "验证PSTS状态: %s", c.testPSTSName)

	// 使用动态客户端获取PSTS
	clientSet, err := c.base.KubeClient.NewDynamicClient("cce.baidubce.com", "v2", "podsubnettopologyspreads")
	if err != nil {
		logger.Warnf(ctx, "创建动态客户端失败: %v，跳过PSTS状态验证", err)
		return nil
	}

	pstsUnstructured, err := clientSet.Namespace(c.testNamespace).Get(ctx, c.testPSTSName, metav1.GetOptions{})
	if err != nil {
		logger.Warnf(ctx, "获取PSTS失败: %v，可能PSTS功能不完全支持，但基本功能已验证", err)
		return nil
	}

	logger.Infof(ctx, "PSTS状态信息:")
	logger.Infof(ctx, "  名称: %s", pstsUnstructured.GetName())
	logger.Infof(ctx, "  命名空间: %s", pstsUnstructured.GetNamespace())
	logger.Infof(ctx, "  创建时间: %s", pstsUnstructured.GetCreationTimestamp())

	// 尝试获取状态信息
	status, found, err := unstructured.NestedMap(pstsUnstructured.Object, "status")
	if err != nil {
		logger.Warnf(ctx, "解析PSTS状态失败: %v", err)
		return nil
	}

	if found && status != nil {
		logger.Infof(ctx, "  状态详情: %+v", status)
	} else {
		logger.Infof(ctx, "  PSTS状态信息暂未更新")
	}

	return nil
}

// recordOriginalIPv6 记录原始IP地址（IPv4和IPv6）
func (c *pstsIPV6) recordOriginalIPv6(ctx context.Context) error {
	logger.Infof(ctx, "记录Pod的原始IP地址（IPv4和IPv6）")

	pods, err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "app=test-sts-ipv6",
	})
	if err != nil {
		return fmt.Errorf("获取Pod列表失败: %v", err)
	}

	if len(pods.Items) == 0 {
		return fmt.Errorf("没有找到匹配的Pod")
	}

	pod := pods.Items[0]

	// 记录IPv4地址
	if pod.Status.PodIP != "" {
		logger.Infof(ctx, "记录Pod %s 的原始IPv4地址: %s", pod.Name, pod.Status.PodIP)
		c.originalPodIPv4 = pod.Status.PodIP
	}

	// 查找并记录IPv6地址
	var ipv6Found bool
	for _, ip := range pod.Status.PodIPs {
		if strings.Contains(ip.IP, ":") {
			logger.Infof(ctx, "记录Pod %s 的原始IPv6地址: %s", pod.Name, ip.IP)
			c.originalPodIPv6 = ip.IP
			ipv6Found = true
			break
		}
	}

	// 严格验证：在Fixed模式下，Pod必须同时拥有IPv4和IPv6地址
	if !ipv6Found || c.originalPodIPv6 == "" {
		return fmt.Errorf("❌ PSTS IPv6 Fixed模式测试失败：Pod %s 没有分配到IPv6地址。在Fixed模式下，Pod必须同时获得IPv4和IPv6地址，这是测试的基本要求", pod.Name)
	}

	if c.originalPodIPv4 == "" {
		return fmt.Errorf("❌ PSTS IPv6 Fixed模式测试失败：Pod %s 没有分配到IPv4地址。在Fixed模式下，Pod必须同时获得IPv4和IPv6地址", pod.Name)
	}

	// 验证IPv6地址格式是否正确
	if !strings.Contains(c.originalPodIPv6, ":") {
		return fmt.Errorf("❌ PSTS IPv6 Fixed模式测试失败：Pod %s 的IPv6地址格式不正确: %s", pod.Name, c.originalPodIPv6)
	}

	logger.Infof(ctx, "原始IP地址记录完成:")
	if c.originalPodIPv4 != "" {
		logger.Infof(ctx, "  IPv4: %s", c.originalPodIPv4)
	}
	if c.originalPodIPv6 != "" {
		logger.Infof(ctx, "  IPv6: %s", c.originalPodIPv6)
	}

	return nil
}

// testIPv6Consistency 测试扩缩容IPv6一致性
func (c *pstsIPV6) testIPv6Consistency(ctx context.Context) error {
	logger.Infof(ctx, "开始测试StatefulSet扩缩容IPv6一致性")

	// 1. 缩容到0
	if err := c.scaleStatefulSet(ctx, 0); err != nil {
		return fmt.Errorf("缩容StatefulSet到0失败: %v", err)
	}

	// 2. 等待Pod删除
	if err := c.waitForPodsDeleted(ctx); err != nil {
		return fmt.Errorf("等待Pod删除失败: %v", err)
	}

	// 3. 扩容到1
	if err := c.scaleStatefulSet(ctx, 1); err != nil {
		return fmt.Errorf("扩容StatefulSet到1失败: %v", err)
	}

	// 4. 等待Pod重新创建
	if err := c.waitForStatefulSetReady(ctx); err != nil {
		return fmt.Errorf("等待StatefulSet重新就绪失败: %v", err)
	}

	// 5. 验证IPv6地址一致性
	if err := c.verifyIPv6Consistency(ctx); err != nil {
		return fmt.Errorf("验证IPv6一致性失败: %v", err)
	}

	logger.Infof(ctx, "StatefulSet扩缩容IPv6一致性测试完成")
	return nil
}

// scaleStatefulSet 扩缩容StatefulSet
func (c *pstsIPV6) scaleStatefulSet(ctx context.Context, replicas int32) error {
	logger.Infof(ctx, "扩缩容StatefulSet %s 到 %d 个副本", c.testStatefulSetName, replicas)

	sts, err := c.base.K8SClient.AppsV1().StatefulSets(c.testNamespace).Get(ctx, c.testStatefulSetName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取StatefulSet失败: %v", err)
	}

	sts.Spec.Replicas = &replicas
	_, err = c.base.K8SClient.AppsV1().StatefulSets(c.testNamespace).Update(ctx, sts, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新StatefulSet失败: %v", err)
	}

	logger.Infof(ctx, "StatefulSet扩缩容请求已提交，目标副本数: %d", replicas)
	return nil
}

// waitForPodsDeleted 等待Pod删除
func (c *pstsIPV6) waitForPodsDeleted(ctx context.Context) error {
	logger.Infof(ctx, "等待Pod删除完成")

	err := wait.PollImmediate(5*time.Second, 2*time.Minute, func() (bool, error) {
		pods, err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).List(ctx, metav1.ListOptions{
			LabelSelector: "app=test-sts-ipv6",
		})
		if err != nil {
			return false, err
		}

		logger.Infof(ctx, "当前Pod数量: %d", len(pods.Items))
		return len(pods.Items) == 0, nil
	})

	if err != nil {
		return fmt.Errorf("等待Pod删除超时: %v", err)
	}

	logger.Infof(ctx, "所有Pod已删除")
	return nil
}

// verifyIPv6Consistency 验证IP地址一致性（Fixed模式下IPv4和IPv6都必须保持不变）
func (c *pstsIPV6) verifyIPv6Consistency(ctx context.Context) error {
	logger.Infof(ctx, "验证IP地址一致性（Fixed模式下IPv4和IPv6地址都必须保持不变）")

	pods, err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "app=test-sts-ipv6",
	})
	if err != nil {
		return fmt.Errorf("获取Pod列表失败: %v", err)
	}

	if len(pods.Items) == 0 {
		return fmt.Errorf("没有找到匹配的Pod")
	}

	pod := pods.Items[0]

	// 获取当前的IP地址
	var currentIPv4 string
	var currentIPv6 string

	// 记录IPv4地址
	if pod.Status.PodIP != "" {
		currentIPv4 = pod.Status.PodIP
	}

	// 查找IPv6地址
	for _, ip := range pod.Status.PodIPs {
		if strings.Contains(ip.IP, ":") {
			currentIPv6 = ip.IP
			break
		}
	}

	logger.Infof(ctx, "Pod %s 重新创建后的IP地址:", pod.Name)
	if currentIPv4 != "" {
		logger.Infof(ctx, "  IPv4地址: %s", currentIPv4)
	}
	if currentIPv6 != "" {
		logger.Infof(ctx, "  IPv6地址: %s", currentIPv6)
	}

	logger.Infof(ctx, "原始IP地址:")
	if c.originalPodIPv4 != "" {
		logger.Infof(ctx, "  IPv4: %s", c.originalPodIPv4)
	}
	if c.originalPodIPv6 != "" {
		logger.Infof(ctx, "  IPv6: %s", c.originalPodIPv6)
	}

	// 验证IPv4地址一致性
	var ipv4Consistent bool = true
	if c.originalPodIPv4 != "" {
		if currentIPv4 == c.originalPodIPv4 {
			logger.Infof(ctx, "✅ IPv4地址一致性验证成功：%s", currentIPv4)
		} else {
			logger.Errorf(ctx, "❌ IPv4地址一致性验证失败：%s → %s", c.originalPodIPv4, currentIPv4)
			ipv4Consistent = false
		}
	}

	// 严格验证：在Fixed模式下，必须验证IPv6地址的存在和一致性
	if c.originalPodIPv6 == "" {
		return fmt.Errorf("❌ PSTS IPv6 Fixed模式测试失败：原始Pod没有IPv6地址，这不符合Fixed模式的预期。测试要求Pod必须同时拥有固定的IPv4和IPv6地址")
	}

	if currentIPv6 == "" {
		return fmt.Errorf("❌ PSTS IPv6 Fixed模式测试失败：扩缩容后Pod %s 没有IPv6地址，这不符合Fixed模式的预期。在Fixed模式下，Pod重新创建后必须保持相同的IPv4和IPv6地址", pod.Name)
	}

	// 验证IPv6地址格式
	if !strings.Contains(currentIPv6, ":") {
		return fmt.Errorf("❌ PSTS IPv6 Fixed模式测试失败：扩缩容后Pod %s 的IPv6地址格式不正确: %s", pod.Name, currentIPv6)
	}

	// 严格验证IPv6地址一致性
	var ipv6Consistent bool = true
	if currentIPv6 == c.originalPodIPv6 {
		logger.Infof(ctx, "✅ IPv6地址一致性验证成功：%s", currentIPv6)
	} else {
		logger.Errorf(ctx, "❌ IPv6地址一致性验证失败：%s → %s", c.originalPodIPv6, currentIPv6)
		ipv6Consistent = false
	}

	// 综合验证结果 - 严格要求IPv4和IPv6地址都必须保持不变
	if ipv4Consistent && ipv6Consistent {
		logger.Infof(ctx, "✅ Fixed模式IP地址一致性验证完全成功：IPv4和IPv6地址都保持不变")
		logger.Infof(ctx, "  原始IPv4: %s → 当前IPv4: %s ✅", c.originalPodIPv4, currentIPv4)
		logger.Infof(ctx, "  原始IPv6: %s → 当前IPv6: %s ✅", c.originalPodIPv6, currentIPv6)
		return nil
	} else {
		var errorMsg string
		var detailedError string
		if !ipv4Consistent && !ipv6Consistent {
			errorMsg = "IPv4和IPv6地址都发生了变化"
			detailedError = fmt.Sprintf("IPv4: %s → %s, IPv6: %s → %s", c.originalPodIPv4, currentIPv4, c.originalPodIPv6, currentIPv6)
		} else if !ipv4Consistent {
			errorMsg = "IPv4地址发生了变化"
			detailedError = fmt.Sprintf("IPv4: %s → %s", c.originalPodIPv4, currentIPv4)
		} else {
			errorMsg = "IPv6地址发生了变化"
			detailedError = fmt.Sprintf("IPv6: %s → %s", c.originalPodIPv6, currentIPv6)
		}
		return fmt.Errorf("❌ PSTS IPv6 Fixed模式测试失败：在Fixed模式下IP地址必须保持不变，但%s。详细信息: %s", errorMsg, detailedError)
	}
}

// Clean 清理测试资源
func (c *pstsIPV6) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理PSTS IPv6测试资源")

	// 按照创建的逆序删除资源
	for i := len(c.createdResources) - 1; i >= 0; i-- {
		resource := c.createdResources[i]
		if err := c.deleteResource(ctx, resource); err != nil {
			logger.Warnf(ctx, "删除资源失败: %s, 错误: %v", resource, err)
		}
	}

	logger.Infof(ctx, "PSTS IPv6测试资源清理完成")
	return nil
}

// deleteResource 删除指定资源
func (c *pstsIPV6) deleteResource(ctx context.Context, resource string) error {
	parts := strings.Split(resource, ":")
	if len(parts) != 2 {
		return fmt.Errorf("无效的资源格式: %s", resource)
	}

	resourceType := parts[0]
	resourceName := parts[1]

	switch resourceType {
	case "statefulset":
		err := c.base.K8SClient.AppsV1().StatefulSets(c.testNamespace).Delete(ctx, resourceName, metav1.DeleteOptions{})
		if err != nil && !kerrors.IsNotFound(err) {
			return fmt.Errorf("删除StatefulSet失败: %v", err)
		}
		logger.Infof(ctx, "StatefulSet %s 已删除", resourceName)

	case "service":
		err := c.base.K8SClient.CoreV1().Services(c.testNamespace).Delete(ctx, resourceName, metav1.DeleteOptions{})
		if err != nil && !kerrors.IsNotFound(err) {
			return fmt.Errorf("删除Service失败: %v", err)
		}
		logger.Infof(ctx, "Service %s 已删除", resourceName)

	case "psts":
		err := c.deletePSTSResource(ctx, resourceName)
		if err != nil && !kerrors.IsNotFound(err) {
			return fmt.Errorf("删除PSTS失败: %v", err)
		}
		logger.Infof(ctx, "PSTS %s 已删除", resourceName)

	case "subnet":
		err := c.deleteSubnetResource(ctx, resourceName)
		if err != nil && !kerrors.IsNotFound(err) {
			return fmt.Errorf("删除Subnet失败: %v", err)
		}
		logger.Infof(ctx, "Subnet %s 已删除", resourceName)

	default:
		return fmt.Errorf("未知的资源类型: %s", resourceType)
	}

	return nil
}

// deletePSTSResource 删除PSTS资源
func (c *pstsIPV6) deletePSTSResource(ctx context.Context, name string) error {
	clientSet, err := c.base.KubeClient.NewDynamicClient("cce.baidubce.com", "v2", "podsubnettopologyspreads")
	if err != nil {
		return fmt.Errorf("创建动态客户端失败: %v", err)
	}

	err = clientSet.Namespace(c.testNamespace).Delete(ctx, name, metav1.DeleteOptions{})
	if err != nil {
		return fmt.Errorf("删除PSTS失败: %v", err)
	}

	return nil
}

// deleteSubnetResource 删除Subnet资源
func (c *pstsIPV6) deleteSubnetResource(ctx context.Context, name string) error {
	clientSet, err := c.base.KubeClient.NewDynamicClient("cce.baidubce.com", "v1", "subnets")
	if err != nil {
		return fmt.Errorf("创建动态客户端失败: %v", err)
	}

	err = clientSet.Delete(ctx, name, metav1.DeleteOptions{})
	if err != nil {
		return fmt.Errorf("删除Subnet失败: %v", err)
	}

	return nil
}

// Continue 返回是否继续执行
func (c *pstsIPV6) Continue(ctx context.Context) bool {
	return true
}

// ConfigFormat 返回配置格式
func (c *pstsIPV6) ConfigFormat() string {
	return `{
  "subnetId": "sbn-11j7f3x308jn"
}`
}

// 辅助函数
func pstsIPV6Int32Ptr(i int32) *int32 {
	return &i
}

// getIPv6EndAddress 根据IPv6基础地址构造结束地址
func (c *pstsIPV6) getIPv6EndAddress(baseAddr string) string {
	// 对于 240c:4081:8000:2b06:: 这样的地址，构造一个小范围的结束地址
	// 避免超出 /64 子网范围
	if strings.HasSuffix(baseAddr, "::") {
		// 移除末尾的 ::，然后添加一个小的范围
		prefix := strings.TrimSuffix(baseAddr, "::")
		return prefix + "::100"
	}
	// 否则直接添加后缀
	return baseAddr + "100"
}
