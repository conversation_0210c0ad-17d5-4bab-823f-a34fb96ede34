package network

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	NetworkCIDR cases.CaseName = "NetworkCIDR"
)

type networkCIDR struct {
	base *cases.BaseClient
}

func init() {
	cases.AddCase(context.TODO(), NetworkCIDR, NewNetworkCIDR)
}

func NewNetworkCIDR(ctx context.Context) cases.Interface {
	return &networkCIDR{}
}

func (c *networkCIDR) Name() cases.CaseName {
	return NetworkCIDR
}

func (c *networkCIDR) Desc() string {
	return "校验集群网络cidr接口测试"
}

func (c *networkCIDR) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	return
}

func (c *networkCIDR) Check(ctx context.Context) (resources []cases.Resource, err error) {
	// 获取集群信息
	cluster, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		err = fmt.Errorf("get cluster failed: %v", err.Error())
		return
	}

	type fields struct {
		VPCID             string
		VPCCIDR           string
		VPCCIDRIPv6       string
		ContainerCIDR     string
		ClusterIPCIDR     string
		ClusterIPCIDRIPv6 string
		MaxPodsPerNode    int
		IPVersion         ccetypes.ContainerNetworkIPType
	}

	checkClusterIPCIDRcases := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "check clusterIP cidr normal case",
			fields: fields{
				VPCID:             cluster.Cluster.Spec.VPCID,
				VPCCIDR:           cluster.Cluster.Spec.VPCCIDR,
				VPCCIDRIPv6:       cluster.Cluster.Spec.ContainerNetworkConfig.ClusterPodCIDRIPv6,
				ClusterIPCIDR:     cluster.Cluster.Spec.ContainerNetworkConfig.ClusterIPServiceCIDR,
				ClusterIPCIDRIPv6: cluster.Cluster.Spec.ContainerNetworkConfig.ClusterIPServiceCIDRIPv6,
				IPVersion:         cluster.Cluster.Spec.ContainerNetworkConfig.IPVersion,
			},
			wantErr: false,
		},
		{
			name: "check clusterIP cidr conflict",
			fields: fields{
				VPCID:             cluster.Cluster.Spec.VPCID,
				VPCCIDR:           cluster.Cluster.Spec.VPCCIDR,
				VPCCIDRIPv6:       cluster.Cluster.Spec.ContainerNetworkConfig.ClusterPodCIDRIPv6,
				ClusterIPCIDR:     cluster.Cluster.Spec.VPCCIDR,
				ClusterIPCIDRIPv6: cluster.Cluster.Spec.ContainerNetworkConfig.ClusterPodCIDRIPv6,
				IPVersion:         cluster.Cluster.Spec.ContainerNetworkConfig.IPVersion,
			},
			wantErr: true,
		},
	}

	// 校验clusterIP cidr
	for _, tt := range checkClusterIPCIDRcases {
		checkClusterIPResp, checkErr := c.base.CCEClient.CheckClusterIPCIDR(ctx, &ccev2.CheckClusterIPCIDRequest{
			VPCID:             tt.fields.VPCID,
			VPCCIDR:           tt.fields.VPCCIDR,
			VPCCIDRIPv6:       tt.fields.VPCCIDRIPv6,
			ClusterIPCIDR:     tt.fields.ClusterIPCIDR,
			ClusterIPCIDRIPv6: tt.fields.ClusterIPCIDRIPv6,
			IPVersion:         tt.fields.IPVersion,
		}, nil)
		if checkErr != nil {
			err = fmt.Errorf("check clusterIP cidr failed: %v", checkErr.Error())
			return
		}
		if checkClusterIPResp.IsConflict != tt.wantErr {
			err = fmt.Errorf("%v failed: got = %v, wantErr %v", tt.name, checkClusterIPResp.IsConflict, tt.wantErr)
			return
		}
	}

	//  校验	podIP cidr
	if cluster.Cluster.Spec.ContainerNetworkConfig.Mode == ccetypes.ContainerNetworkModeVPCRoute {
		checkContainerNetworkCIDRCases := []struct {
			name    string
			fields  fields
			wantErr bool
		}{
			{
				name: "check container network cidr normal case",
				fields: fields{
					VPCID:          cluster.Cluster.Spec.VPCID,
					VPCCIDR:        cluster.Cluster.Spec.VPCCIDR,
					ContainerCIDR:  cluster.Cluster.Spec.ContainerNetworkConfig.ClusterPodCIDR,
					ClusterIPCIDR:  cluster.Cluster.Spec.ContainerNetworkConfig.ClusterIPServiceCIDR,
					MaxPodsPerNode: cluster.Cluster.Spec.ContainerNetworkConfig.MaxPodsPerNode,
					IPVersion:      cluster.Cluster.Spec.ContainerNetworkConfig.IPVersion,
				},
				wantErr: true,
			},
			{
				name: "check container network cidr conflict",
				fields: fields{
					VPCID:          cluster.Cluster.Spec.VPCID,
					VPCCIDR:        cluster.Cluster.Spec.VPCCIDR,
					ContainerCIDR:  cluster.Cluster.Spec.ContainerNetworkConfig.ClusterPodCIDR,
					ClusterIPCIDR:  cluster.Cluster.Spec.ContainerNetworkConfig.ClusterPodCIDR,
					MaxPodsPerNode: cluster.Cluster.Spec.ContainerNetworkConfig.MaxPodsPerNode,
					IPVersion:      cluster.Cluster.Spec.ContainerNetworkConfig.IPVersion,
				},
				wantErr: true,
			},
		}

		for _, tt := range checkContainerNetworkCIDRCases {
			checkContainerNetworkCIDRResp, checkErr := c.base.CCEClient.CheckContainerNetworkCIDR(ctx, &ccev2.CheckContainerNetworkCIDRRequest{
				VPCID:          tt.fields.VPCID,
				VPCCIDR:        tt.fields.VPCCIDR,
				ContainerCIDR:  tt.fields.ContainerCIDR,
				ClusterIPCIDR:  tt.fields.ClusterIPCIDR,
				MaxPodsPerNode: tt.fields.MaxPodsPerNode,
				// 默认ipv4, ipv6不涉及容器网段的校验
				// IPVersion:              tt.fields.IPVersion,
				SkipContainerCIDRCheck: false,
			}, nil)
			if checkErr != nil {
				err = fmt.Errorf("check clusterIP cidr failed: %v", checkErr.Error())
				return
			}
			if checkContainerNetworkCIDRResp.IsConflict != tt.wantErr {
				err = fmt.Errorf("%v failed: got = %v, wantErr %v",
					tt.name, checkContainerNetworkCIDRResp.IsConflict, tt.wantErr)
				return
			}
		}
	}

	// 校验clusterIP cidr 推荐
	_, err = c.base.CCEClient.RecommendClusterIPCIDR(ctx, &ccev2.RecommendClusterIPCIDRRequest{
		VPCCIDR:              cluster.Cluster.Spec.VPCCIDR,
		ContainerCIDR:        cluster.Cluster.Spec.ContainerNetworkConfig.ClusterPodCIDR,
		ClusterMaxServiceNum: 4096,
		PrivateNetCIDRs:      []ccev2.PrivateNetString{ccev2.PrivateIPv4Net172},
		IPVersion:            "ipv4",
	}, nil)
	if err != nil {
		err = fmt.Errorf("recommend clusterip cidr failed: %v", err.Error())
		return
	}

	// 校验containerNetwork cidr 推荐
	_, err = c.base.CCEClient.RecommendContainerCIDR(context.TODO(), &ccev2.RecommendContainerCIDRRequest{
		VPCID:             cluster.Cluster.Spec.VPCID,
		VPCCIDR:           cluster.Cluster.Spec.VPCCIDR,
		ClusterMaxNodeNum: 256,
		MaxPodsPerNode:    cluster.Cluster.Spec.ContainerNetworkConfig.MaxPodsPerNode,
		PrivateNetCIDRs:   []ccev2.PrivateNetString{ccev2.PrivateIPv4Net172},
		K8SVersion:        cluster.Cluster.Spec.K8SVersion,
		IPVersion:         cluster.Cluster.Spec.ContainerNetworkConfig.IPVersion,
	}, nil)
	if err != nil {
		err = fmt.Errorf("recommend container network cidr failed: %v", err.Error())
		return
	}

	return
}

func (c *networkCIDR) Clean(ctx context.Context) error {
	return nil
}

func (c *networkCIDR) Continue(ctx context.Context) bool {
	return true
}

func (c *networkCIDR) ConfigFormat() string {
	return ""
}
