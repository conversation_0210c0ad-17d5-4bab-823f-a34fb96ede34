/*
Copyright 2024 Baidu Inc.

本文件包含了针对主网卡辅助IP节点移除时IP回收功能的自动化测试用例。
该测试主要验证在使用主网卡辅助IP的节点从集群中移出时，会回收对应ENI分配的辅助IP。

用例主要验证以下内容：
1. 确认集群中存在ENI状态为PrimaryWithSecondaryIP的节点
2. 确认主网卡辅助IP的ENI使用的子网
3. 将节点移出集群
4. 验证ENI对应的CR被清理后，子网IP是否增加

测试流程：
1. 检查集群中是否存在PrimaryWithSecondaryIP模式的ENI
2. 选择一个合适的节点进行测试
3. 记录节点移除前的子网IP使用情况
4. 将节点从集群中移除
5. 等待ENI CR被清理
6. 验证子网IP是否增加（IP回收成功）
7. 清理测试资源

测试流程详见每个函数的具体实现。
*/

package network

import (
	"context"
	"fmt"
	"strings"
	"time"

	kerrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	// PrimaryIPReleaseCaseName - case 名字
	PrimaryIPReleaseCaseName cases.CaseName = "PrimaryIPRelease"

	// ENI使用模式常量
	primaryWithSecondaryIPMode = "PrimaryWithSecondaryIP"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), PrimaryIPReleaseCaseName, NewPrimaryIPRelease)
}

// primaryIPRelease 结构体定义
type primaryIPRelease struct {
	base                 *cases.BaseClient
	resources            []cases.Resource
	targetNodeName       string
	targetENIID          string
	targetSubnetID       string
	subnetIPsBefore      int
	subnetIPsAfter       int
	nodeInstanceID       string
	nodeGroupID          string
	removedFromNodeGroup bool
}

// NewPrimaryIPRelease - 测试案例构造函数
func NewPrimaryIPRelease(ctx context.Context) cases.Interface {
	return &primaryIPRelease{}
}

// Init 初始化测试环境
func (c *primaryIPRelease) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base is nil")
	}

	c.base = base

	logger.Infof(ctx, "初始化primaryIPRelease测试，集群ID: %s", c.base.ClusterID)
	return nil
}

// Name 返回测试用例名称
func (c *primaryIPRelease) Name() cases.CaseName {
	return PrimaryIPReleaseCaseName
}

// Desc 返回测试用例描述
func (c *primaryIPRelease) Desc() string {
	return "测试在使用主网卡辅助IP的节点从集群中移出时，会回收对应ENI分配的辅助IP"
}

// ConfigFormat 返回配置格式
func (c *primaryIPRelease) ConfigFormat() string {
	return ""
}

// Continue 返回是否继续执行
func (c *primaryIPRelease) Continue(ctx context.Context) bool {
	return true
}

// Check 执行测试
func (c *primaryIPRelease) Check(ctx context.Context) ([]cases.Resource, error) {
	var err error

	// 1. 检查集群中是否存在PrimaryWithSecondaryIP模式的ENI
	logger.Infof(ctx, "第一步：检查集群中是否存在PrimaryWithSecondaryIP模式的ENI...")
	if err = c.checkPrimaryWithSecondaryIPENI(ctx); err != nil {
		return nil, fmt.Errorf("检查PrimaryWithSecondaryIP模式ENI失败: %v", err)
	}

	// 2. 选择目标节点并记录信息
	logger.Infof(ctx, "第二步：选择目标节点并记录信息...")
	if err = c.selectTargetNode(ctx); err != nil {
		return nil, fmt.Errorf("选择目标节点失败: %v", err)
	}

	// 3. 记录节点移除前的子网IP使用情况
	logger.Infof(ctx, "第三步：记录节点移除前的子网IP使用情况...")
	if err = c.recordSubnetIPsBefore(ctx); err != nil {
		return nil, fmt.Errorf("记录子网IP使用情况失败: %v", err)
	}

	// 4. 将节点从集群中移除
	logger.Infof(ctx, "第四步：将节点从集群中移除...")
	if err = c.removeNodeFromCluster(ctx); err != nil {
		return nil, fmt.Errorf("移除节点失败: %v", err)
	}

	// 5. 等待ENI CR被清理
	logger.Infof(ctx, "第五步：等待ENI CR被清理...")
	if err = c.waitForENICleanup(ctx); err != nil {
		return nil, fmt.Errorf("等待ENI清理失败: %v", err)
	}

	// 6. 验证子网IP是否增加（IP回收成功）
	logger.Infof(ctx, "第六步：验证子网IP是否增加...")
	if err = c.verifyIPRecovery(ctx); err != nil {
		return nil, fmt.Errorf("验证IP回收失败: %v", err)
	}

	logger.Infof(ctx, "✅ primaryIPRelease测试完成")
	return c.resources, nil
}

// Clean 清理测试资源
func (c *primaryIPRelease) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理测试资源")
	var lastErr error

	// 如果节点还在集群中，尝试恢复
	if c.targetNodeName != "" && !c.removedFromNodeGroup {
		logger.Infof(ctx, "检查节点是否需要恢复...")

		// 检查节点是否还在集群中
		_, err := c.base.KubeClient.GetNode(ctx, c.targetNodeName, &kube.GetOptions{})
		if err != nil && !kerrors.IsNotFound(err) {
			logger.Warnf(ctx, "检查节点状态失败: %v", err)
		} else if err == nil {
			logger.Infof(ctx, "节点 %s 仍在集群中，无需恢复", c.targetNodeName)
		} else {
			logger.Infof(ctx, "节点 %s 已从集群中移除", c.targetNodeName)
		}
	}

	logger.Infof(ctx, "资源清理完成")
	return lastErr
}

// checkPrimaryWithSecondaryIPENI 检查集群中是否存在PrimaryWithSecondaryIP模式的ENI
func (c *primaryIPRelease) checkPrimaryWithSecondaryIPENI(ctx context.Context) error {
	logger.Infof(ctx, "检查集群中的ENI资源")

	// 获取集群信息
	cluster, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		return fmt.Errorf("获取集群信息失败: %v", err)
	}

	networkMode := cluster.Cluster.Spec.ContainerNetworkConfig.Mode
	logger.Infof(ctx, "集群网络模式: %s", networkMode)

	if networkMode != "vpc-eni" {
		return fmt.Errorf("当前集群网络模式为 %s，不是 vpc-eni 模式，无法进行主网卡辅助IP测试。请使用 vpc-eni 模式的集群", networkMode)
	}

	// 获取集群中的所有ENI资源
	enis, err := c.base.KubeClient.ListENI(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取ENI列表失败: %v", err)
	}

	logger.Infof(ctx, "找到 %d 个ENI资源", len(enis.Items))

	// 查找PrimaryWithSecondaryIP模式的ENI
	primaryWithSecondaryENIs := make([]string, 0)
	for _, eni := range enis.Items {
		logger.Infof(ctx, "ENI %s: UseMode=%s, NodeName=%s", eni.Name, eni.Spec.UseMode, eni.Spec.NodeName)
		if eni.Spec.UseMode == primaryWithSecondaryIPMode {
			primaryWithSecondaryENIs = append(primaryWithSecondaryENIs, eni.Name)
			logger.Infof(ctx, "找到PrimaryWithSecondaryIP模式ENI: %s (节点: %s)", eni.Name, eni.Spec.NodeName)
		}
	}

	if len(primaryWithSecondaryENIs) == 0 {
		logger.Warnf(ctx, "集群中没有找到PrimaryWithSecondaryIP模式的ENI")
		logger.Infof(ctx, "当前集群中的ENI都是Secondary模式，无法进行主网卡辅助IP功能测试")
		logger.Infof(ctx, "如需测试主网卡辅助IP功能，请使用包含PrimaryWithSecondaryIP模式ENI的集群")
		return fmt.Errorf("集群中没有找到PrimaryWithSecondaryIP模式的ENI，当前集群不支持主网卡辅助IP功能")
	}

	logger.Infof(ctx, "✅ 找到 %d 个PrimaryWithSecondaryIP模式的ENI", len(primaryWithSecondaryENIs))
	return nil
}

// selectTargetNode 选择目标节点并记录信息
func (c *primaryIPRelease) selectTargetNode(ctx context.Context) error {
	logger.Infof(ctx, "选择合适的目标节点进行测试")

	// 获取集群中的所有ENI资源
	enis, err := c.base.KubeClient.ListENI(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取ENI列表失败: %v", err)
	}

	// 查找PrimaryWithSecondaryIP模式的ENI，并选择一个作为测试目标
	for _, eni := range enis.Items {
		if eni.Spec.UseMode == primaryWithSecondaryIPMode {
			// 检查节点是否存在
			node, err := c.base.KubeClient.GetNode(ctx, eni.Spec.NodeName, &kube.GetOptions{})
			if err != nil {
				logger.Warnf(ctx, "获取节点 %s 失败: %v", eni.Spec.NodeName, err)
				continue
			}

			// 检查节点是否属于节点组
			nodeGroupID, hasNodeGroup := node.Labels["instance-group-id"]
			if !hasNodeGroup {
				logger.Warnf(ctx, "节点 %s 不属于任何节点组，跳过", eni.Spec.NodeName)
				continue
			}

			// 获取节点的实例ID
			instanceID := ""
			if node.Spec.ProviderID != "" {
				// ProviderID格式通常为: baiducloud:///{region}/{instanceID}
				parts := strings.Split(node.Spec.ProviderID, "/")
				if len(parts) > 0 {
					instanceID = parts[len(parts)-1]
				}
			}

			if instanceID == "" {
				logger.Warnf(ctx, "无法获取节点 %s 的实例ID，跳过", eni.Spec.NodeName)
				continue
			}

			// 获取ENI使用的子网ID
			subnetID := eni.Spec.SubnetID

			if subnetID == "" {
				logger.Warnf(ctx, "ENI %s 没有子网信息，跳过", eni.Name)
				continue
			}

			// 选择这个节点作为测试目标
			c.targetNodeName = eni.Spec.NodeName
			c.targetENIID = eni.Name
			c.targetSubnetID = subnetID
			c.nodeInstanceID = instanceID
			c.nodeGroupID = nodeGroupID

			logger.Infof(ctx, "✅ 选择节点进行测试:")
			logger.Infof(ctx, "  节点名称: %s", c.targetNodeName)
			logger.Infof(ctx, "  ENI ID: %s", c.targetENIID)
			logger.Infof(ctx, "  子网ID: %s", c.targetSubnetID)
			logger.Infof(ctx, "  实例ID: %s", c.nodeInstanceID)
			logger.Infof(ctx, "  节点组ID: %s", c.nodeGroupID)

			return nil
		}
	}

	return fmt.Errorf("未找到合适的PrimaryWithSecondaryIP模式节点进行测试")
}

// recordSubnetIPsBefore 记录节点移除前的子网IP使用情况
func (c *primaryIPRelease) recordSubnetIPsBefore(ctx context.Context) error {
	logger.Infof(ctx, "记录子网 %s 移除节点前的IP使用情况", c.targetSubnetID)

	// 获取子网信息
	subnet, err := c.base.KubeClient.GetSubnet(ctx, c.targetSubnetID, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取子网信息失败: %v", err)
	}

	c.subnetIPsBefore = subnet.Status.AvailableIPNum
	logger.Infof(ctx, "移除节点前子网可用IP数量: %d", c.subnetIPsBefore)

	// 记录ENI的详细信息
	eni, err := c.base.KubeClient.GetENI(ctx, c.targetENIID, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取ENI详细信息失败: %v", err)
	}

	logger.Infof(ctx, "目标ENI详细信息:")
	logger.Infof(ctx, "  ENI名称: %s", eni.Name)
	logger.Infof(ctx, "  使用模式: %s", eni.Spec.UseMode)
	logger.Infof(ctx, "  绑定节点: %s", eni.Spec.NodeName)
	logger.Infof(ctx, "  子网ID: %s", eni.Spec.SubnetID)
	logger.Infof(ctx, "  私有IP数量: %d", len(eni.Spec.PrivateIPSet))

	return nil
}

// removeNodeFromCluster 将节点从集群中移除
func (c *primaryIPRelease) removeNodeFromCluster(ctx context.Context) error {
	logger.Infof(ctx, "开始将节点 %s 从集群中移除", c.targetNodeName)

	// 获取节点组信息
	ig, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.nodeGroupID, nil)
	if err != nil {
		return fmt.Errorf("获取节点组信息失败: %v", err)
	}

	logger.Infof(ctx, "节点组当前状态: 期望副本数=%d, 实际副本数=%d, 就绪副本数=%d",
		ig.InstanceGroup.Spec.Replicas,
		ig.InstanceGroup.Status.ActualReplicas,
		ig.InstanceGroup.Status.ReadyReplicas)

	// 从节点组中移除节点（缩容）
	newReplicas := ig.InstanceGroup.Spec.Replicas - 1
	if newReplicas < 0 {
		newReplicas = 0
	}

	logger.Infof(ctx, "将节点组 %s 从 %d 个副本缩容到 %d 个副本",
		c.nodeGroupID, ig.InstanceGroup.Spec.Replicas, newReplicas)

	// 执行缩容操作
	updateRequest := &ccev2.UpdateInstanceGroupReplicasRequest{
		Replicas: newReplicas,
	}

	_, err = c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, c.base.ClusterID, c.nodeGroupID, updateRequest, nil)
	if err != nil {
		return fmt.Errorf("缩容节点组失败: %v", err)
	}

	logger.Infof(ctx, "已发送缩容请求，等待节点移除完成...")

	// 等待节点从集群中移除
	maxWaitTime := 10 * time.Minute
	checkInterval := 30 * time.Second

	err = wait.PollImmediate(checkInterval, maxWaitTime, func() (bool, error) {
		// 检查节点是否还在集群中
		_, err := c.base.KubeClient.GetNode(ctx, c.targetNodeName, &kube.GetOptions{})
		if kerrors.IsNotFound(err) {
			logger.Infof(ctx, "✅ 节点 %s 已从集群中移除", c.targetNodeName)
			c.removedFromNodeGroup = true
			return true, nil
		}
		if err != nil {
			logger.Warnf(ctx, "检查节点状态时出错: %v", err)
			return false, nil
		}

		logger.Infof(ctx, "节点 %s 仍在集群中，继续等待移除...", c.targetNodeName)
		return false, nil
	})

	if err != nil {
		return fmt.Errorf("等待节点移除超时: %v", err)
	}

	logger.Infof(ctx, "✅ 节点移除完成")
	return nil
}

// waitForENICleanup 等待ENI CR被清理
func (c *primaryIPRelease) waitForENICleanup(ctx context.Context) error {
	logger.Infof(ctx, "等待ENI %s 的CR被清理", c.targetENIID)

	maxWaitTime := 5 * time.Minute
	checkInterval := 15 * time.Second

	err := wait.PollImmediate(checkInterval, maxWaitTime, func() (bool, error) {
		// 检查ENI CR是否还存在
		_, err := c.base.KubeClient.GetENI(ctx, c.targetENIID, &kube.GetOptions{})
		if kerrors.IsNotFound(err) {
			logger.Infof(ctx, "✅ ENI CR %s 已被清理", c.targetENIID)
			return true, nil
		}
		if err != nil {
			logger.Warnf(ctx, "检查ENI CR状态时出错: %v", err)
			return false, nil
		}

		logger.Infof(ctx, "ENI CR %s 仍然存在，继续等待清理...", c.targetENIID)
		return false, nil
	})

	if err != nil {
		return fmt.Errorf("等待ENI CR清理超时: %v", err)
	}

	logger.Infof(ctx, "✅ ENI CR清理完成")
	return nil
}

// verifyIPRecovery 验证子网IP是否增加（IP回收成功）
func (c *primaryIPRelease) verifyIPRecovery(ctx context.Context) error {
	logger.Infof(ctx, "验证子网 %s 的IP是否已回收", c.targetSubnetID)

	// 获取子网当前状态
	subnet, err := c.base.KubeClient.GetSubnet(ctx, c.targetSubnetID, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取子网信息失败: %v", err)
	}

	c.subnetIPsAfter = subnet.Status.AvailableIPNum
	logger.Infof(ctx, "移除节点后子网可用IP数量: %d", c.subnetIPsAfter)

	// 比较IP数量变化
	ipDifference := c.subnetIPsAfter - c.subnetIPsBefore
	logger.Infof(ctx, "子网IP数量变化: %d (移除前: %d, 移除后: %d)",
		ipDifference, c.subnetIPsBefore, c.subnetIPsAfter)

	if ipDifference > 0 {
		logger.Infof(ctx, "✅ IP回收成功！子网可用IP增加了 %d 个", ipDifference)
		logger.Infof(ctx, "这表明主网卡辅助IP在节点移除时得到了正确回收")
		return nil
	} else if ipDifference == 0 {
		logger.Warnf(ctx, "⚠️ 子网IP数量没有变化，可能IP回收还未完成或存在其他原因")
		return fmt.Errorf("子网IP数量没有增加，IP回收可能失败")
	} else {
		logger.Warnf(ctx, "⚠️ 子网IP数量减少了 %d 个，这是异常情况", -ipDifference)
		return fmt.Errorf("子网IP数量异常减少，可能存在其他问题")
	}
}
