/*
PodScalingTest 测试用例

功能描述：
测试节点上Pod扩缩容过程中IP的分配与回收功能，验证CNI网络组件能否正确管理IP资源。
此测试会验证当Pod扩容时是否能正确分配IP，以及在Pod缩容后是否能够回收多余IP。
同时验证IP分配时是否会优先从剩余IP较多的子网中选择IP。

测试流程：
1. 前置检查：验证集群中必要组件是否存在，包括cce-network-v2-config、cce-network-agent
   和cce-network-operator。
2. 配置准备：修改ConfigMap中的网络配置，包括缓冲池大小、IP释放延迟、预分配值等参数，
   然后重启CNI组件使配置生效。
3. 记录子网信息：记录扩容前各子网的可用IP数量，并找出可用IP最多的子网。
4. 资源创建（Pod扩容）：创建一个包含大量Pod的Deployment，所有Pod调度到同一节点。
5. 子网验证：验证新Pod的IP地址是否优先从剩余IP最多的子网中分配。
6. 网络验证：记录节点ENI信息，验证Pod的网卡MAC地址与ARP表是否正确配置。
7. Pod缩容：减少Deployment的副本数，触发IP回收。
8. 结果验证：检查IP回收后的状态，验证多余IP是否被回收，以及ENI是否保持正常状态。

关键指标：
1. IP分配效率：在扩容阶段，是否能及时为大量Pod分配IP地址。
2. 子网选择策略：验证IP分配是否优先从剩余IP较多的子网中选择。
3. IP回收性能：在缩容后，多余IP的回收速度和完整性。
4. 资源管理：ENI资源在扩缩容过程中是否被正确管理。
5. 网络一致性：检查Pod网络环境中MAC地址与ARP表的一致性。

IP回收计时：
此测试会记录IP回收过程的耗时，具体实现如下：
1. 开始时间点：Pod缩容完成（副本数达到预期值）时开始计时
2. 结束时间点：当节点IP数量达到预期值（已用IP + 预分配值 + 水位线值）或超时结束计时
3. 检查频率：每5秒检查一次当前IP数量，并记录从开始到当前的耗时
4. 超时设置：如果180秒内IP回收未完成，则记录超时信息并继续测试
5. 容忍值：考虑到IP回收的异步性质，设置了5个IP的容忍值范围

子网验证：
1. 在扩容前记录所有子网的可用IP数量
2. 识别具有最多可用IP的子网
3. 扩容后随机选取多个Pod检查IP所属子网
4. 统计来自最大可用IP子网的Pod比例
5. 如果超过60%的Pod来自最大可用IP子网，则认为验证通过

配置参数影响：
此测试中的IP回收性能会受到ConfigMap中配置的影响，特别是：
- ippool-pre-allocate: 预分配IP数量
- excess-ip-release-delay: IP释放延迟
- ippool-max-above-watermark: 最大水位线
- release-excess-ips: 是否开启多余IP释放功能

注意事项：
此测试会修改集群网络组件的配置，并创建较多资源，请在测试环境运行。
测试完成后会自动清理所有创建的资源。
*/

package network

import (
	"context"
	"fmt"
	"math/rand"
	"net"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	corev1 "k8s.io/api/core/v1"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
)

const (
	// podScalingTestCaseName - case 名字
	podScalingTestCaseName cases.CaseName = "PodScalingTest"
)

// podScalingTest 是用于测试Pod扩缩容的测试用例
type podScalingTest struct {
	base               *cases.BaseClient
	configMapName      string
	daemonSetName      string
	deploymentName     string
	testDeploymentName string
	nodeName           string
	initialIPCount     int
	initialENIs        map[string]string // 记录初始ENI列表，key为ENI ID，value为状态
	// 用于记录MAC地址验证相关信息
	podMAC            string // 记录容器内eth0的MAC地址
	podGatewayMAC     string // 记录容器内arp表中网关的MAC地址
	podIP             string // 记录容器分配的IP地址
	hostInterface     string // 记录主机上连接到pod的网卡名称
	hostInterfaceMAC  string // 记录主机上连接到pod的网卡MAC地址
	selectedPodName   string // 记录选中进行验证的pod名称
	selectedContainer string // 记录选中进行验证的容器名称
	// 记录子网信息
	subnetInfoBeforeScale []SubnetInfo // 扩容前的子网信息
	maxAvailIPSubnet      string       // 可用IP最多的子网名称
	secondMaxIPSubnet     string       // 可用IP第二多的子网名称
	ipDiff                int          // 最大IP子网与第二大IP子网的IP数量差值
}

// NewPodScalingTest 创建一个新的Pod扩缩容测试用例
func NewPodScalingTest(ctx context.Context) cases.Interface {
	return &podScalingTest{
		configMapName:      "cce-network-v2-config",
		daemonSetName:      "cce-network-agent",
		deploymentName:     "cce-network-operator",
		testDeploymentName: "dep-test-pod-scaling",
		initialENIs:        make(map[string]string),
	}
}

func init() {
	// 添加调试日志
	fmt.Println("================ 开始注册PodScalingTest测试用例 ================")
	// 注册 CaseName
	cases.AddCase(context.TODO(), podScalingTestCaseName, NewPodScalingTest)
	fmt.Println("================ PodScalingTest测试用例注册完成 ================")
}

// Name 返回测试用例名称
func (c *podScalingTest) Name() cases.CaseName {
	return podScalingTestCaseName
}

// Desc 返回测试用例描述
func (c *podScalingTest) Desc() string {
	return "测试Pod扩缩容功能"
}

// Init 初始化测试用例
func (c *podScalingTest) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	logger.Infof(ctx, "初始化Pod扩缩容测试用例")
	c.base = base
	return nil
}

// PreCheck 前置检查
func (c *podScalingTest) preCheck(ctx context.Context) error {
	logger.Infof(ctx, "开始执行Pod扩缩容测试前置检查")
	// 检查集群中是否存在必要的组件
	if err := c.checkComponents(ctx); err != nil {
		return fmt.Errorf("组件检查失败: %v", err)
	}

	// 获取一个可用节点
	logger.Infof(ctx, "开始获取可用节点")
	node, err := c.getAvailableNode(ctx)
	if err != nil {
		return fmt.Errorf("获取可用节点失败: %v", err)
	}
	c.nodeName = node.Name
	logger.Infof(ctx, "获取到可用节点: %s", c.nodeName)

	// 记录初始IP分配状态
	logger.Infof(ctx, "开始获取节点初始IP数量")
	initialIPCount, err := c.getNodeIPCount(ctx)
	if err != nil {
		return fmt.Errorf("获取初始IP数量失败: %v", err)
	}
	c.initialIPCount = initialIPCount
	logger.Infof(ctx, "节点 %s 的初始IP数量: %d", c.nodeName, c.initialIPCount)

	// 注意：节点ENI信息将在创建资源（扩容）后记录，确保包含扩容过程中创建的新ENI
	logger.Infof(ctx, "前置检查完成")
	return nil
}

// Check 执行测试
func (c *podScalingTest) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)

	// 执行前置检查
	if err := c.preCheck(ctx); err != nil {
		return resources, err
	}

	// 1. 配置准备阶段
	if err := c.prepareConfig(ctx); err != nil {
		return resources, fmt.Errorf("配置准备失败: %v", err)
	}

	// 2. 资源创建阶段（Pod扩容）
	if err := c.createTestResources(ctx); err != nil {
		return resources, fmt.Errorf("资源创建失败: %v", err)
	}

	// 在创建资源（扩容）后记录节点ENI信息，确保包含扩容过程中创建的新ENI
	logger.Infof(ctx, "Pod扩容完成后，记录节点ENI信息")
	if err := c.recordNodeENIs(ctx); err != nil {
		return resources, fmt.Errorf("记录节点ENI信息失败: %v", err)
	}

	// 检查扩容后pod的网卡MAC地址与arp表的正确性
	if err := c.verifyPodNetworkInterfaceAndARP(ctx); err != nil {
		return resources, fmt.Errorf("验证Pod网卡MAC地址与ARP表失败: %v", err)
	}

	// 3. 触发缩容阶段
	if err := c.triggerPodScaleDown(ctx); err != nil {
		return resources, fmt.Errorf("触发Pod缩容失败: %v", err)
	}

	// 4. 验证阶段
	if err := c.verifyResults(ctx); err != nil {
		return resources, fmt.Errorf("验证结果失败: %v", err)
	}

	return resources, nil
}

// checkComponents 检查必要的组件是否存在
func (c *podScalingTest) checkComponents(ctx context.Context) error {
	logger.Infof(ctx, "开始检查集群中必要的组件")

	// 检查ConfigMap
	logger.Infof(ctx, "检查ConfigMap: %s", c.configMapName)
	configMap, err := c.base.K8SClient.CoreV1().ConfigMaps("kube-system").Get(ctx, c.configMapName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("configMap %s 不存在: %v", c.configMapName, err)
	}
	logger.Infof(ctx, "已找到ConfigMap: %s", c.configMapName)

	// 验证ConfigMap中包含cced配置
	if _, ok := configMap.Data["cced"]; !ok {
		return fmt.Errorf("configMap %s 中不存在cced配置", c.configMapName)
	}
	logger.Infof(ctx, "ConfigMap %s 中包含cced配置", c.configMapName)

	// 检查DaemonSet
	logger.Infof(ctx, "检查DaemonSet: %s", c.daemonSetName)
	ds, err := c.base.K8SClient.AppsV1().DaemonSets("kube-system").Get(ctx, c.daemonSetName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("daemonSet %s 不存在: %v", c.daemonSetName, err)
	}
	logger.Infof(ctx, "已找到DaemonSet: %s, 期望Pod数: %d, 当前就绪Pod数: %d",
		c.daemonSetName, ds.Status.DesiredNumberScheduled, ds.Status.NumberReady)

	// 检查Deployment
	logger.Infof(ctx, "检查Deployment: %s", c.deploymentName)
	deploy, err := c.base.K8SClient.AppsV1().Deployments("kube-system").Get(ctx, c.deploymentName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("deployment %s 不存在: %v", c.deploymentName, err)
	}
	logger.Infof(ctx, "已找到Deployment: %s, 期望副本数: %d, 当前就绪副本数: %d",
		c.deploymentName, *deploy.Spec.Replicas, deploy.Status.ReadyReplicas)

	logger.Infof(ctx, "所有组件检查通过")
	return nil
}

// getAvailableNode 获取一个可用节点
func (c *podScalingTest) getAvailableNode(ctx context.Context) (*corev1.Node, error) {
	nodes, err := c.base.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, err
	}

	if len(nodes.Items) == 0 {
		return nil, fmt.Errorf("没有可用的节点")
	}

	return &nodes.Items[0], nil
}

// getNodeIPCount 获取节点IP数量
func (c *podScalingTest) getNodeIPCount(ctx context.Context) (int, error) {
	nrs, err := c.base.KubeClient.GetNrs(ctx, c.nodeName, &kube.GetOptions{})
	if err != nil {
		return 0, err
	}

	return len(nrs.Spec.Ipam.Pool), nil
}

// recordNodeENIs 记录节点当前的ENI信息
func (c *podScalingTest) recordNodeENIs(ctx context.Context) error {
	// 获取节点的NRS
	nrs, err := c.base.KubeClient.GetNrs(ctx, c.nodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取节点NRS资源失败: %v", err)
	}

	// 记录ENI信息
	c.initialENIs = make(map[string]string)
	for eniID, eni := range nrs.Status.Enis {
		c.initialENIs[eniID] = eni.CceStatus
		logger.Infof(ctx, "节点 %s 已有ENI: ID=%s, 状态=%s, 子网=%s",
			c.nodeName, eniID, eni.CceStatus, eni.SubnetId)
	}

	logger.Infof(ctx, "节点 %s 共有 %d 个ENI", c.nodeName, len(c.initialENIs))
	return nil
}

// prepareConfig 准备配置
func (c *podScalingTest) prepareConfig(ctx context.Context) error {
	// 修改ConfigMap
	configMap, err := c.base.K8SClient.CoreV1().ConfigMaps("kube-system").Get(ctx, c.configMapName, metav1.GetOptions{})
	if err != nil {
		return err
	}

	// 获取当前配置
	ccedConfig, ok := configMap.Data["cced"]
	if !ok {
		return fmt.Errorf("configMap中不存在cced配置")
	}

	logger.Infof(ctx, "修改前的配置:\n%s", ccedConfig)

	// 需要修改的配置项
	configUpdates := map[string]string{
		"burstable-mehrfach-eni":     "0",
		"excess-ip-release-delay":    "2",
		"ippool-max-above-watermark": "1",
		"release-excess-ips":         "true",
		"ippool-pre-allocate":        "8",
		"resource-resync-interval":   "5s",
	}

	// 修改指定的配置项
	lines := strings.Split(ccedConfig, "\n")
	for i, line := range lines {
		for key, newValue := range configUpdates {
			if strings.HasPrefix(strings.TrimSpace(line), key+":") {
				parts := strings.SplitN(line, ":", 2)
				if len(parts) == 2 {
					lines[i] = parts[0] + ": " + newValue
					logger.Infof(ctx, "修改配置 %s: 原值=%s, 新值=%s", parts[0], strings.TrimSpace(parts[1]), newValue)
				}
			}
		}
	}

	// 更新ConfigMap
	configMap.Data["cced"] = strings.Join(lines, "\n")

	logger.Infof(ctx, "修改后的配置:\n%s", configMap.Data["cced"])

	_, err = c.base.K8SClient.CoreV1().ConfigMaps("kube-system").Update(ctx, configMap, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新configMap失败: %v", err)
	}

	// 重启CNI组件
	if err := c.restartCNIComponents(ctx); err != nil {
		return fmt.Errorf("重启cni组件失败: %v", err)
	}

	return nil
}

// restartCNIComponents 重启CNI组件
func (c *podScalingTest) restartCNIComponents(ctx context.Context) error {
	// 重启DaemonSet
	if err := c.restartDaemonSet(ctx); err != nil {
		return err
	}

	// 重启Deployment
	if err := c.restartDeployment(ctx); err != nil {
		return err
	}

	// 等待组件就绪
	if err := c.waitForComponentReady(ctx); err != nil {
		return err
	}

	return nil
}

// restartDaemonSet 重启DaemonSet
func (c *podScalingTest) restartDaemonSet(ctx context.Context) error {
	// 重启DaemonSet的Pods
	ds, err := c.base.K8SClient.AppsV1().DaemonSets("kube-system").Get(ctx, c.daemonSetName, metav1.GetOptions{})
	if err != nil {
		return err
	}

	// 更新DaemonSet的annotations以强制重启
	if ds.Spec.Template.Annotations == nil {
		ds.Spec.Template.Annotations = make(map[string]string)
	}
	ds.Spec.Template.Annotations["kubectl.kubernetes.io/restartedAt"] = time.Now().Format(time.RFC3339)

	_, err = c.base.K8SClient.AppsV1().DaemonSets("kube-system").Update(ctx, ds, metav1.UpdateOptions{})
	if err != nil {
		return err
	}

	// 等待DaemonSet的Pods重启完成
	return c.waitForComponentReady(ctx)
}

// restartDeployment 重启Deployment
func (c *podScalingTest) restartDeployment(ctx context.Context) error {
	// 重启Deployment的Pods
	deploy, err := c.base.K8SClient.AppsV1().Deployments("kube-system").Get(ctx, c.deploymentName, metav1.GetOptions{})
	if err != nil {
		return err
	}

	// 更新Deployment的annotations以强制重启
	if deploy.Spec.Template.Annotations == nil {
		deploy.Spec.Template.Annotations = make(map[string]string)
	}
	deploy.Spec.Template.Annotations["kubectl.kubernetes.io/restartedAt"] = time.Now().Format(time.RFC3339)

	_, err = c.base.K8SClient.AppsV1().Deployments("kube-system").Update(ctx, deploy, metav1.UpdateOptions{})
	if err != nil {
		return err
	}

	// 等待Deployment的Pods重启完成
	return c.waitForComponentReady(ctx)
}

// waitForComponentReady 等待组件就绪
func (c *podScalingTest) waitForComponentReady(ctx context.Context) error {
	// 等待组件就绪
	return wait.Poll(5*time.Second, 5*time.Minute, func() (bool, error) {
		ds, err := c.base.K8SClient.AppsV1().DaemonSets("kube-system").Get(ctx, c.daemonSetName, metav1.GetOptions{})
		if err != nil {
			return false, err
		}
		logger.Infof(ctx, "等待组件就绪: 当前就绪数=%d, 期望数=%d", ds.Status.NumberReady, ds.Status.DesiredNumberScheduled)
		return ds.Status.NumberReady == ds.Status.DesiredNumberScheduled, nil
	})
}

// SubnetInfo 存储子网信息
type SubnetInfo struct {
	Name           string
	ID             string
	AvailableIPNum int
	CIDR           string
}

// getSubnetInfo 获取所有子网信息
func (c *podScalingTest) getSubnetInfo(ctx context.Context) ([]SubnetInfo, error) {
	subnets, err := c.base.KubeClient.ListSubnet(ctx, &kube.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取子网列表失败: %v", err)
	}

	var subnetInfos []SubnetInfo
	for _, subnet := range subnets.Items {
		subnetInfos = append(subnetInfos, SubnetInfo{
			Name:           subnet.Name,
			ID:             subnet.Spec.ID,
			AvailableIPNum: subnet.Status.AvailableIPNum,
			CIDR:           subnet.Spec.CIDR,
		})
		logger.Infof(ctx, "子网信息: 名称=%s, ID=%s, 可用IP数=%d, CIDR=%s",
			subnet.Name, subnet.Spec.ID, subnet.Status.AvailableIPNum, subnet.Spec.CIDR)
	}

	return subnetInfos, nil
}

// recordSubnetInfoBeforeScale 记录扩容前的子网信息
func (c *podScalingTest) recordSubnetInfoBeforeScale(ctx context.Context) error {
	logger.Infof(ctx, "开始记录扩容前的子网信息")

	// 获取所有子网信息
	subnetInfos, err := c.getSubnetInfo(ctx)
	if err != nil {
		return fmt.Errorf("获取扩容前子网信息失败: %v", err)
	}

	// 保存子网信息
	c.subnetInfoBeforeScale = subnetInfos

	// 寻找可用IP最多和第二多的子网
	maxAvailIP := -1
	secondMaxAvailIP := -1

	// 先找最大的
	for _, subnet := range subnetInfos {
		if subnet.AvailableIPNum > maxAvailIP {
			// 当前找到更大的，原来的最大变为第二大
			secondMaxAvailIP = maxAvailIP
			c.secondMaxIPSubnet = c.maxAvailIPSubnet

			// 更新最大
			maxAvailIP = subnet.AvailableIPNum
			c.maxAvailIPSubnet = subnet.Name
		} else if subnet.AvailableIPNum > secondMaxAvailIP {
			// 更新第二大
			secondMaxAvailIP = subnet.AvailableIPNum
			c.secondMaxIPSubnet = subnet.Name
		}
	}

	// 计算绝对IP差值
	if secondMaxAvailIP > 0 {
		c.ipDiff = maxAvailIP - secondMaxAvailIP
		logger.Infof(ctx, "扩容前子网信息: 最大IP子网=%s(IP数=%d), 第二大IP子网=%s(IP数=%d), IP数量差值=%d",
			c.maxAvailIPSubnet, maxAvailIP, c.secondMaxIPSubnet, secondMaxAvailIP, c.ipDiff)
	} else {
		c.ipDiff = maxAvailIP // 只有一个子网时差值设为该子网IP数
		logger.Infof(ctx, "扩容前可用IP最多的子网是: %s，可用IP数量: %d", c.maxAvailIPSubnet, maxAvailIP)
	}

	return nil
}

// verifyIPAllocation 验证IP分配情况
func (c *podScalingTest) verifyIPAllocation(ctx context.Context, subnetInfos []SubnetInfo) error {
	// 获取所有Pod
	pods, err := c.base.K8SClient.CoreV1().Pods("default").List(ctx, metav1.ListOptions{
		LabelSelector: "app=nginx100",
	})
	if err != nil {
		return fmt.Errorf("获取Pod列表失败: %v", err)
	}

	// 检查所有Pod，不再随机选择
	logger.Infof(ctx, "检查所有%d个Pod的IP分配情况", len(pods.Items))

	// 统计各子网分配的IP数量
	subnetPodCounts := make(map[string]int)
	totalPods := 0

	// 验证每个Pod的IP
	for _, pod := range pods.Items {
		if pod.Status.PodIP == "" {
			logger.Warnf(ctx, "Pod %s 尚未分配IP", pod.Name)
			continue
		}

		totalPods++

		// 检查IP是否属于某个子网
		found := false
		for _, subnet := range subnetInfos {
			_, ipNet, err := net.ParseCIDR(subnet.CIDR)
			if err != nil {
				logger.Warnf(ctx, "解析子网CIDR失败: %v", err)
				continue
			}

			if ipNet.Contains(net.ParseIP(pod.Status.PodIP)) {
				found = true
				subnetPodCounts[subnet.Name]++
				logger.Infof(ctx, "Pod %s 的IP %s 属于子网 %s (可用IP数: %d)",
					pod.Name, pod.Status.PodIP, subnet.Name, subnet.AvailableIPNum)
				break
			}
		}

		if !found {
			logger.Warnf(ctx, "Pod %s 的IP %s 不属于任何已知子网", pod.Name, pod.Status.PodIP)
		}
	}

	// 验证IP分配是否合理
	if totalPods > 0 && c.maxAvailIPSubnet != "" {
		maxSubnetPodCount := subnetPodCounts[c.maxAvailIPSubnet]
		maxSubnetPercentage := float64(maxSubnetPodCount) / float64(totalPods) * 100

		logger.Infof(ctx, "IP分配验证: 总Pod数=%d, 来自最大可用IP子网(%s)的Pod数=%d (%.2f%%), IP差值=%d",
			totalPods, c.maxAvailIPSubnet, maxSubnetPodCount, maxSubnetPercentage, c.ipDiff)

		// 根据子网IP差值判断分配是否合理
		if c.ipDiff > 0 {
			// 如果两个子网的IP差值大于总Pod数，期望至少90%的Pod使用最大IP子网
			if c.ipDiff > totalPods {
				expectedPercentage := 90.0
				if maxSubnetPercentage >= expectedPercentage {
					logger.Infof(ctx, "验证通过: IP差值(%d)大于总Pod数(%d)，有%.2f%%的Pod使用了最大IP子网，达到期望的%.2f%%",
						c.ipDiff, totalPods, maxSubnetPercentage, expectedPercentage)
				} else {
					logger.Warnf(ctx, "验证警告: IP差值(%d)大于总Pod数(%d)，但只有%.2f%%的Pod使用了最大IP子网，未达到期望的%.2f%%",
						c.ipDiff, totalPods, maxSubnetPercentage, expectedPercentage)
				}
			} else {
				// 如果差值小于等于总Pod数，就期望至少差值数量的Pod使用最大IP子网
				if maxSubnetPodCount >= c.ipDiff {
					logger.Infof(ctx, "验证通过: 来自最大IP子网的Pod数(%d)满足IP差值(%d)的要求，使用比例为%.2f%%",
						maxSubnetPodCount, c.ipDiff, maxSubnetPercentage)
				} else {
					logger.Warnf(ctx, "验证警告: 来自最大IP子网的Pod数(%d)未满足IP差值(%d)的要求，使用比例为%.2f%%",
						maxSubnetPodCount, c.ipDiff, maxSubnetPercentage)
				}
			}
		} else {
			// 子网IP差异为0，随机分配是合理的
			logger.Infof(ctx, "验证通过: 子网间IP差异为0，随机分配是合理的，当前使用最大IP子网的比例为%.2f%%", maxSubnetPercentage)
		}
	}

	return nil
}

// createTestResources 创建测试资源
func (c *podScalingTest) createTestResources(ctx context.Context) error {
	logger.Infof(ctx, "开始创建测试资源")

	// 记录扩容前的子网信息
	if err := c.recordSubnetInfoBeforeScale(ctx); err != nil {
		return fmt.Errorf("记录扩容前子网信息失败: %v", err)
	}

	// 获取子网信息
	subnetInfos, err := c.getSubnetInfo(ctx)
	if err != nil {
		return fmt.Errorf("获取子网信息失败: %v", err)
	}

	// 寻找合适的节点
	logger.Infof(ctx, "开始寻找合适的节点...")
	node, err := c.findSuitableNode(ctx)
	if err != nil {
		return fmt.Errorf("寻找合适节点失败: %v", err)
	}
	logger.Infof(ctx, "选择节点: %s", node.Name)

	// 使用YAML创建Deployment
	deploymentYaml := `apiVersion: apps/v1
kind: Deployment
metadata:
  name: dep-test-pod-scaling
  labels:
    app: nginx
spec:
  replicas: 40
  minReadySeconds: 0
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app: nginx100
  template:
    metadata:
      labels:
        app: nginx100
    spec:
      nodeSelector:
        kubernetes.io/hostname: NODENAME
      restartPolicy: Always
      containers:
        - name: nginx
          image: registry.baidubce.com/cce/nginx-alpine-go:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 80
          livenessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 20
            timeoutSeconds: 5
            periodSeconds: 5
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 5`

	// 替换节点名
	deploymentYaml = strings.Replace(deploymentYaml, "NODENAME", node.Name, 1)

	logger.Infof(ctx, "创建测试Deployment...")

	// 检查是否存在同名Deployment
	if _, err := c.base.K8SClient.AppsV1().Deployments("default").Get(ctx, c.testDeploymentName, metav1.GetOptions{}); err == nil {
		logger.Infof(ctx, "发现已存在的同名Deployment %s，准备删除", c.testDeploymentName)
		// 删除已存在的Deployment
		deletePolicy := metav1.DeletePropagationForeground
		deleteOptions := metav1.DeleteOptions{
			PropagationPolicy: &deletePolicy,
		}
		if err := c.base.K8SClient.AppsV1().Deployments("default").Delete(ctx, c.testDeploymentName, deleteOptions); err != nil {
			return fmt.Errorf("删除已存在的Deployment失败: %v", err)
		}
		// 等待Deployment完全删除
		err = wait.Poll(5*time.Second, 2*time.Minute, func() (bool, error) {
			_, err := c.base.K8SClient.AppsV1().Deployments("default").Get(ctx, c.testDeploymentName, metav1.GetOptions{})
			if err != nil && kerrors.IsNotFound(err) {
				return true, nil
			}
			return false, nil
		})
		if err != nil {
			return fmt.Errorf("等待Deployment删除超时: %v", err)
		}
		logger.Infof(ctx, "同名Deployment已成功删除")
	} else if !kerrors.IsNotFound(err) {
		return fmt.Errorf("检查Deployment是否存在时出错: %v", err)
	}

	// 使用PostAppResource创建Deployment
	if _, err := c.base.AppClient.PostAppResource(ctx, c.base.ClusterID,
		c.testDeploymentName, deploymentYaml, "default", nil); err != nil {
		return fmt.Errorf("创建deployment失败: %v", err)
	}

	// 等待Deployment就绪
	err = wait.Poll(5*time.Second, 5*time.Minute, func() (bool, error) {
		deploy, err := c.base.K8SClient.AppsV1().Deployments("default").Get(ctx, c.testDeploymentName, metav1.GetOptions{})
		if err != nil {
			return false, err
		}
		logger.Infof(ctx, "等待Deployment就绪: 当前就绪副本数=%d, 期望副本数=%d", deploy.Status.ReadyReplicas, *deploy.Spec.Replicas)
		return deploy.Status.ReadyReplicas == *deploy.Spec.Replicas, nil
	})
	if err != nil {
		return fmt.Errorf("等待deployment就绪失败: %v", err)
	}

	// 验证IP分配情况
	if err := c.verifyIPAllocation(ctx, subnetInfos); err != nil {
		return fmt.Errorf("验证IP分配失败: %v", err)
	}

	logger.Infof(ctx, "测试Deployment创建完成")
	return nil
}

// findSuitableNode 寻找适合部署和测试Pod扩缩容的节点
func (c *podScalingTest) findSuitableNode(ctx context.Context) (*corev1.Node, error) {
	// 获取所有节点
	nodes, err := c.base.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取节点列表失败: %v", err)
	}

	if len(nodes.Items) == 0 {
		return nil, fmt.Errorf("集群中没有可用节点")
	}

	// 遍历检查每个节点是否满足要求
	for _, node := range nodes.Items {
		// 检查节点是否可调度
		if node.Spec.Unschedulable {
			logger.Infof(ctx, "节点 %s 不可调度，跳过", node.Name)
			continue
		}
		if len(node.Spec.Taints) > 0 {
			logger.Infof(ctx, "节点 %s 有污点，跳过", node.Name)
			continue
		}

		// 获取节点可分配资源
		allocatable := node.Status.Allocatable

		// 获取CPU核数
		cpuCores := allocatable.Cpu().Value()

		// 获取内存大小（转换为GB）
		memoryGB := allocatable.Memory().Value() / (1024 * 1024 * 1024)

		// 计算粗略的Pod容量估算值
		estimatedCapacity := cpuCores * memoryGB

		logger.Infof(ctx, "节点 %s 资源情况: CPU=%d核, 内存=%dGB, 估算容量=%d",
			node.Name, cpuCores, memoryGB, estimatedCapacity)

		// 如果估算容量大于40，认为可以调度40个Pod
		if estimatedCapacity >= 40 {
			// 获取节点当前IP池状态，仅用于记录初始IP数量
			nrs, err := c.base.KubeClient.GetNrs(ctx, node.Name, &kube.GetOptions{})
			if err == nil {
				c.initialIPCount = len(nrs.Spec.Ipam.Pool)
				logger.Infof(ctx, "节点 %s 当前IP池大小: %d", node.Name, c.initialIPCount)
			} else {
				logger.Warnf(ctx, "获取节点 %s 的NRS资源失败: %v，使用默认值0", node.Name, err)
				c.initialIPCount = 0
			}

			// 保存节点名
			c.nodeName = node.Name
			return &node, nil
		} else {
			logger.Infof(ctx, "节点 %s 资源不足，估算容量: %d < 40", node.Name, estimatedCapacity)
		}
	}

	// 如果没有找到完全满足条件的节点，选择第一个可调度节点
	logger.Warnf(ctx, "未找到资源足够的节点，尝试选择一个可调度的节点")
	for _, node := range nodes.Items {
		if !node.Spec.Unschedulable && len(node.Spec.Taints) == 0 {
			logger.Warnf(ctx, "选择备选节点: %s", node.Name)

			// 获取节点当前IP池状态，仅用于记录初始IP数量
			nrs, err := c.base.KubeClient.GetNrs(ctx, node.Name, &kube.GetOptions{})
			if err == nil {
				c.initialIPCount = len(nrs.Spec.Ipam.Pool)
			} else {
				logger.Warnf(ctx, "获取节点 %s 的NRS资源失败: %v，使用默认值0", node.Name, err)
				c.initialIPCount = 0
			}

			// 保存节点名
			c.nodeName = node.Name
			return &node, nil
		}
	}

	return nil, fmt.Errorf("未找到合适的节点部署测试资源")
}

// triggerPodScaleDown 触发Pod缩容
// 此方法执行Pod的缩容操作，并监控IP回收的过程
// 实现细节：
// 1. 将Deployment副本数从40减少到1，触发Pod缩容
// 2. 等待Pod缩容完成（副本数达到预期值）
// 3. 开始监控IP回收过程，记录IP回收的开始时间
// 4. 定期(每5秒)检查当前IP数量，计算并打印IP回收阶段的耗时
// 5. 当IP回收成功或超时时记录总耗时并返回
func (c *podScalingTest) triggerPodScaleDown(ctx context.Context) error {
	// 缩容Deployment来触发IP回收
	deployment, err := c.base.K8SClient.AppsV1().Deployments("default").Get(ctx, c.testDeploymentName, metav1.GetOptions{})
	if err != nil {
		return err
	}

	// 将副本数缩减为1
	replicas := int32(1)
	deployment.Spec.Replicas = &replicas

	logger.Infof(ctx, "开始缩减Deployment副本数至1个")
	_, err = c.base.K8SClient.AppsV1().Deployments("default").Update(ctx, deployment, metav1.UpdateOptions{})
	if err != nil {
		return err
	}

	// 等待缩容完成
	err = wait.Poll(5*time.Second, 5*time.Minute, func() (bool, error) {
		deploy, err := c.base.K8SClient.AppsV1().Deployments("default").Get(ctx, c.testDeploymentName, metav1.GetOptions{})
		if err != nil {
			return false, err
		}
		logger.Infof(ctx, "等待Deployment缩容完成: 当前就绪副本数=%d, 期望副本数=%d", deploy.Status.ReadyReplicas, *deploy.Spec.Replicas)
		return deploy.Status.ReadyReplicas == *deploy.Spec.Replicas, nil
	})
	if err != nil {
		return fmt.Errorf("等待缩容完成超时: %v", err)
	}

	// 计算预期IP数量
	expectedIPCount := 1 + 8 + 1 // 已用IP(1) + 预分配(8) + 水位线(1)
	toleranceValue := 5          // 容忍值

	// Pod缩容完成，记录开始IP回收的时间点
	ipRecyclingStartTime := time.Now()
	logger.Infof(ctx, "Pod缩容完成，开始等待IP回收，预期IP数量: %d, 容忍值: %d, 开始时间: %v",
		expectedIPCount, toleranceValue, ipRecyclingStartTime.Format("15:04:05.000"))

	// 等待IP回收，每5秒检查一次
	startTime := ipRecyclingStartTime
	ipRecycled := false

	for {
		// 获取节点信息
		node, err := c.base.K8SClient.CoreV1().Nodes().Get(ctx, c.nodeName, metav1.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取节点信息失败: %v", err)
			continue
		}

		// 获取当前IP数量
		currentIPCount := 0
		if ipStr, ok := node.Status.Allocatable["cce.baidubce.com/ip"]; ok {
			currentIPCount, _ = strconv.Atoi(ipStr.String())
		}

		// 计算从IP回收开始到现在的耗时
		elapsedTime := time.Since(ipRecyclingStartTime)
		logger.Infof(ctx, "当前IP数量: %d, 预期IP数量: %d, 容忍值: %d, IP回收已等待时间: %v",
			currentIPCount, expectedIPCount, toleranceValue, elapsedTime.Round(time.Millisecond))

		// 如果达到预期数量（考虑容忍值），提前结束等待
		if currentIPCount <= expectedIPCount+toleranceValue {
			ipRecycled = true
			// 计算从开始到IP回收成功的总耗时
			totalTime := time.Since(ipRecyclingStartTime)
			logger.Infof(ctx, "IP回收完成，IP数量已达到预期（考虑容忍值），IP回收总耗时: %v", totalTime.Round(time.Millisecond))
			break
		}

		// 检查是否超时
		if time.Since(startTime) > 180*time.Second {
			// 计算从开始到IP回收超时的总耗时
			totalTime := time.Since(ipRecyclingStartTime)
			logger.Infof(ctx, "等待IP回收超时，当前IP数量: %d，IP回收等待总耗时: %v", currentIPCount, totalTime.Round(time.Millisecond))
			break
		}

		// 等待5秒后继续检查
		time.Sleep(5 * time.Second)
	}

	// 记录IP回收的最终状态
	if ipRecycled {
		logger.Infof(ctx, "IP回收成功，从Pod缩容完成到IP回收完成共耗时: %v",
			time.Since(ipRecyclingStartTime).Round(time.Millisecond))
	} else {
		logger.Warnf(ctx, "IP回收未完成或超时，从Pod缩容完成到现在共耗时: %v",
			time.Since(ipRecyclingStartTime).Round(time.Millisecond))
	}

	return nil
}

// verifyResults 验证结果
func (c *podScalingTest) verifyResults(ctx context.Context) error {
	// 验证IP是否被回收
	nrs, err := c.base.KubeClient.GetNrs(ctx, c.nodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取NRS资源失败: %v", err)
	}

	// 验证IP池状态
	currentIPCount := len(nrs.Spec.Ipam.Pool)
	usedIPCount := len(nrs.Status.Ipam.Used)

	// 验证ENI状态，确保ENI数量不变且状态正常
	currentENIs := make(map[string]string)

	for eniID, eni := range nrs.Status.Enis {
		currentENIs[eniID] = eni.CceStatus
		logger.Infof(ctx, "缩容后节点 %s ENI: ID=%s, 状态=%s, 子网=%s",
			c.nodeName, eniID, eni.CceStatus, eni.SubnetId)

		// 检查ENI是否存在于集群中
		eniInCluster, err := c.base.KubeClient.GetENI(ctx, eniID, &kube.GetOptions{})
		if err != nil {
			return fmt.Errorf("获取ENI %s 信息失败: %v", eniID, err)
		}

		// 检查ENI的节点是否正确
		if eniInCluster.Spec.NodeName != c.nodeName {
			return fmt.Errorf("ENI %s 的节点不正确: 期望节点=%s, 实际节点=%s",
				eniID, c.nodeName, eniInCluster.Spec.NodeName)
		}

		// 检查ENI的CCE状态是否正常
		if eniInCluster.Status.CCEStatus != "ReadyOnNode" {
			return fmt.Errorf("ENI %s CCE状态异常: 当前状态=%s, 期望状态=ReadyOnNode",
				eniID, eniInCluster.Status.CCEStatus)
		}

		// 检查ENI的VPC状态是否正常
		if eniInCluster.Status.VPCStatus != "inuse" {
			return fmt.Errorf("ENI %s VPC状态异常: 当前状态=%s, 期望状态=inuse",
				eniID, eniInCluster.Status.VPCStatus)
		}

		// 检查ENI的标签是否正确
		if eniInCluster.Labels["cce.baidubce.com/node"] != c.nodeName {
			return fmt.Errorf("ENI %s 的节点标签不正确: 期望节点=%s, 实际标签=%s",
				eniID, c.nodeName, eniInCluster.Labels["cce.baidubce.com/node"])
		}

		logger.Infof(ctx, "ENI %s 状态检查通过: 节点=%s, CCE状态=%s, VPC状态=%s",
			eniID, eniInCluster.Spec.NodeName, eniInCluster.Status.CCEStatus, eniInCluster.Status.VPCStatus)
	}

	logger.Infof(ctx, "缩容后节点 %s 共有 %d 个ENI, 扩容后有 %d 个ENI",
		c.nodeName, len(currentENIs), len(c.initialENIs))

	// 检查ENI数量是否变化
	if len(currentENIs) < len(c.initialENIs) {
		return fmt.Errorf("Pod缩容导致ENI数量减少: 当前ENI数=%d, 初始ENI数=%d",
			len(currentENIs), len(c.initialENIs))
	}

	// 检查初始ENI是否都还在
	missingENIs := make([]string, 0)
	for eniID := range c.initialENIs {
		if _, exists := currentENIs[eniID]; !exists {
			missingENIs = append(missingENIs, eniID)
		}
	}

	if len(missingENIs) > 0 {
		return fmt.Errorf("缩容后有ENI丢失: %v", missingENIs)
	}

	// 获取ConfigMap中配置的水位线值
	configMap, err := c.base.K8SClient.CoreV1().ConfigMaps("kube-system").Get(ctx, c.configMapName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取ConfigMap失败: %v", err)
	}

	ccedConfig, ok := configMap.Data["cced"]
	if !ok {
		return fmt.Errorf("configMap中不存在cced配置")
	}

	// 我们在prepareConfig中已经设置了这些值
	// burstable-mehrfach-eni: 0
	// excess-ip-release-delay: 2
	// ippool-max-above-watermark: 1
	// release-excess-ips: true
	// ippool-pre-allocate: 8
	watermarkValue := 1       // 我们在prepareConfig中设置的固定水位线值
	burstableMehrfachENI := 0 // 我们在prepareConfig中设置的固定值
	releaseEnabled := true    // 我们在prepareConfig中已经启用了IP回收功能
	preAllocateValue := 8     // 我们在prepareConfig中设置的固定预分配值

	// 还需要从配置中读取其他未修改的值
	minAllocateValue := 10 // 默认最小分配值

	lines := strings.Split(ccedConfig, "\n")
	for _, line := range lines {
		trimmedLine := strings.TrimSpace(line)
		if strings.HasPrefix(trimmedLine, "ippool-min-allocate-ips:") {
			parts := strings.SplitN(trimmedLine, ":", 2)
			if len(parts) == 2 {
				valueStr := strings.TrimSpace(parts[1])
				valueInt, err := c.parseConfigValue(valueStr)
				if err == nil {
					minAllocateValue = valueInt
					logger.Infof(ctx, "从配置中获取最小分配值: %d", minAllocateValue)
				}
			}
		}
	}

	logger.Infof(ctx, "使用参数: watermark=%d, burstableENI=%d, release=%v, preAllocate=%d, minAllocate=%d",
		watermarkValue, burstableMehrfachENI, releaseEnabled, preAllocateValue, minAllocateValue)

	// 按照calculateExcessIPs的逻辑计算期望的IP数量
	// 如果已用IP <= minAllocate + watermark，则不应回收
	if usedIPCount <= (minAllocateValue + watermarkValue) {
		if currentIPCount <= (minAllocateValue + watermarkValue) {
			logger.Infof(ctx, "IP使用量(%d)低于(minAllocate(%d) + watermark(%d))，IP池大小正常: %d",
				usedIPCount, minAllocateValue, watermarkValue, currentIPCount)
			return nil
		}
	}

	// 计算多余IP(excess)和期望保留的IP数量
	excessIPs := currentIPCount - usedIPCount - preAllocateValue - watermarkValue
	expectedIPCount := usedIPCount + preAllocateValue + watermarkValue
	if excessIPs < 0 {
		excessIPs = 0
	}

	// 重要：确保期望IP数量不低于minAllocate
	if expectedIPCount < minAllocateValue {
		logger.Infof(ctx, "计算的期望IP数量(%d)小于最小分配数量(%d)，调整为最小分配数量",
			expectedIPCount, minAllocateValue)
		expectedIPCount = minAllocateValue
		// 重新计算多余IP
		excessIPs = currentIPCount - expectedIPCount
		if excessIPs < 0 {
			excessIPs = 0
		}
	}

	logger.Infof(ctx, "Pod缩容验证: 当前IP数量=%d, 已用IP数量=%d, 预分配值=%d, 水位线值=%d, 多余IP数量=%d, 期望IP数量=%d, 初始IP数量=%d",
		currentIPCount, usedIPCount, preAllocateValue, watermarkValue, excessIPs, expectedIPCount, c.initialIPCount)

	// 宽松验证：考虑到IP回收是异步操作，可能需要一定时间
	// 给一个较大的容忍度(5)，只要在合理范围内就算验证通过
	toleranceValue := 5

	// 如果存在多余IP超出容忍范围，则判断未回收完成
	if excessIPs > toleranceValue {
		return fmt.Errorf("IP池中仍有较多多余IP未回收: 当前IP数量(%d) > 期望数量(%d) + 容忍值(%d)",
			currentIPCount, expectedIPCount, toleranceValue)
	}

	// 验证是否执行了回收操作
	if c.initialIPCount > 0 && c.initialIPCount > expectedIPCount+toleranceValue && currentIPCount >= c.initialIPCount {
		return fmt.Errorf("IP回收未生效: 当前IP数量(%d)未小于初始数量(%d)", currentIPCount, c.initialIPCount)
	}

	// 验证IP数量不为0，确保有足够的IP供使用
	if currentIPCount < usedIPCount {
		return fmt.Errorf("IP池状态异常: 当前IP数量(%d)小于已用IP数量(%d)", currentIPCount, usedIPCount)
	}

	logger.Infof(ctx, "Pod缩容验证成功: 当前IP数量=%d, 期望IP数量=%d, 已用IP数量=%d, 初始IP数量=%d, 容忍值=%d",
		currentIPCount, expectedIPCount, usedIPCount, c.initialIPCount, toleranceValue)

	// ====== 新增：校验CEP删除和NRS used ip字段 ======
	// 等待NRS和CEP状态同步，最多等待60秒，每5秒检查一次
	maxWait := 60 * time.Second
	interval := 5 * time.Second
	start := time.Now()
	for {
		// 1. 获取当前存活的测试Pod列表（只检查我们创建的测试Pod）
		testPodList, err := c.base.K8SClient.CoreV1().Pods("default").List(ctx, metav1.ListOptions{
			LabelSelector: "app=nginx100",
		})
		if err != nil {
			return fmt.Errorf("获取测试Pod列表失败: %v", err)
		}
		currentTestPodNames := make(map[string]struct{})
		currentTestPodIPs := make(map[string]struct{})
		for _, pod := range testPodList.Items {
			currentTestPodNames[pod.Name] = struct{}{}
			if pod.Status.PodIP != "" {
				currentTestPodIPs[pod.Status.PodIP] = struct{}{}
			}
		}

		// 2. 校验CEP删除 - 只检查default命名空间的CEP
		cepList, err := c.base.KubeClient.ListCep(ctx, "default", &kube.ListOptions{})
		if err != nil {
			return fmt.Errorf("获取CEP列表失败: %v", err)
		}
		cepOk := true
		for _, cep := range cepList.Items {
			podName := cep.Spec.ExternalIdentifiers.PodName
			if _, exist := currentTestPodNames[podName]; !exist {
				// 发现已删除测试Pod的CEP仍然存在
				logger.Warnf(ctx, "发现已删除测试Pod %s 的CEP仍然存在，CEP名称: %s", podName, cep.Name)
				cepOk = false
				break
			}
		}

		// 3. 校验NRS used ip - 只检查测试Pod的IP是否已从NRS中移除
		nrsList, err := c.base.KubeClient.ListNrs(ctx, &kube.ListOptions{})
		if err != nil {
			return fmt.Errorf("获取NRS列表失败: %v", err)
		}
		nrsOk := true
		for _, nrs := range nrsList.Items {
			// 只检查当前测试节点的NRS
			if nrs.Name == c.nodeName {
				for _, usedIP := range nrs.Status.Ipam.Used {
					// 只检查owner是default命名空间且不在当前测试Pod列表中的IP
					if strings.HasPrefix(usedIP.Owner, "default/") {
						podName := strings.TrimPrefix(usedIP.Owner, "default/")
						if _, exist := currentTestPodNames[podName]; !exist {
							// 发现已删除测试Pod的IP仍然在NRS中
							logger.Warnf(ctx, "发现已删除测试Pod %s 的IP %s 仍然在NRS中", podName, usedIP.Resource)
							nrsOk = false
							break
						}
					}
				}
			}
			if !nrsOk {
				break
			}
		}

		if cepOk && nrsOk {
			logger.Infof(ctx, "CEP和NRS校验通过")
			break
		}

		if time.Since(start) > maxWait {
			return fmt.Errorf("等待CEP和NRS状态同步超时(%.0fs)", maxWait.Seconds())
		}
		logger.Infof(ctx, "CEP或NRS未就绪，等待同步...（已等待%.0fs）", time.Since(start).Seconds())
		time.Sleep(interval)
	}

	return nil
}

// parseConfigValue 解析配置值为整数
func (c *podScalingTest) parseConfigValue(value string) (int, error) {
	// 去除可能的引号和空格
	value = strings.Trim(value, "\" \t")

	// 处理常见的数字后缀（Ki, Mi, Gi, k, m, g）
	multiplier := 1
	suffixes := map[string]int{
		"ki": 1024,
		"mi": 1024 * 1024,
		"gi": 1024 * 1024 * 1024,
		"k":  1000,
		"m":  1000 * 1000,
		"g":  1000 * 1000 * 1000,
	}

	valueLower := strings.ToLower(value)
	for suffix, mult := range suffixes {
		if strings.HasSuffix(valueLower, suffix) {
			value = value[:len(value)-len(suffix)]
			multiplier = mult
			break
		}
	}

	// 尝试使用不同的方法解析数字
	// 1. 首先尝试使用 strconv.Atoi
	if intVal, err := strconv.Atoi(value); err == nil {
		return intVal * multiplier, nil
	}

	// 2. 尝试使用 strconv.ParseFloat 解析（处理科学计数法等格式）
	if floatVal, err := strconv.ParseFloat(value, 64); err == nil {
		return int(floatVal) * multiplier, nil
	}

	// 3. 最后尝试使用 fmt.Sscanf
	var result int
	_, err := fmt.Sscanf(value, "%d", &result)
	if err == nil {
		return result * multiplier, nil
	}

	return 0, fmt.Errorf("无法解析整数值: %s", value)
}

// Clean 清理测试资源
func (c *podScalingTest) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理Pod扩缩容测试的资源")
	var lastErr error

	// 获取测试Deployment是否存在
	deployment, err := c.base.K8SClient.AppsV1().Deployments("default").Get(ctx, c.testDeploymentName, metav1.GetOptions{})
	if err != nil {
		if !kerrors.IsNotFound(err) {
			// 只有非"资源不存在"的错误才记录
			logger.Warnf(ctx, "获取测试Deployment失败: %v，但仍将尝试继续清理其他资源", err)
			lastErr = err
		} else {
			logger.Infof(ctx, "测试Deployment %s 不存在，无需清理", c.testDeploymentName)
		}
	} else {
		// Deployment存在，需要删除
		logger.Infof(ctx, "发现测试Deployment %s，准备删除", c.testDeploymentName)

		// 先尝试缩容到0，减少删除过程中可能的IP占用
		logger.Infof(ctx, "将测试Deployment副本数缩减为0")
		replicas := int32(0)
		deployment.Spec.Replicas = &replicas
		_, scaleErr := c.base.K8SClient.AppsV1().Deployments("default").Update(ctx, deployment, metav1.UpdateOptions{})
		if scaleErr != nil && !kerrors.IsNotFound(scaleErr) {
			logger.Warnf(ctx, "缩容测试Deployment失败: %v，将直接删除", scaleErr)
		}

		// 等待缩容完成（最多10秒）
		if scaleErr == nil {
			waitErr := wait.PollImmediate(1*time.Second, 10*time.Second, func() (bool, error) {
				d, err := c.base.K8SClient.AppsV1().Deployments("default").Get(ctx, c.testDeploymentName, metav1.GetOptions{})
				if err != nil {
					if kerrors.IsNotFound(err) {
						return true, nil // Deployment已被其他进程删除
					}
					return false, nil // 获取失败，继续等待
				}
				logger.Infof(ctx, "等待Deployment缩容完成: 当前就绪副本数=%d, 期望副本数=%d", d.Status.ReadyReplicas, *d.Spec.Replicas)
				return d.Status.ReadyReplicas == 0, nil
			})
			if waitErr != nil {
				logger.Warnf(ctx, "等待缩容完成超时，将直接删除: %v", waitErr)
			}
		}

		// 执行删除
		deletePolicy := metav1.DeletePropagationForeground
		deleteOptions := metav1.DeleteOptions{
			PropagationPolicy: &deletePolicy,
		}
		err = c.base.K8SClient.AppsV1().Deployments("default").Delete(ctx, c.testDeploymentName, deleteOptions)
		if err != nil {
			if !kerrors.IsNotFound(err) {
				logger.Warnf(ctx, "删除测试Deployment失败: %v", err)
				lastErr = err
			} else {
				logger.Infof(ctx, "测试Deployment已被其他进程删除")
			}
		} else {
			logger.Infof(ctx, "已发送删除测试Deployment请求")
		}

		// 等待Deployment完全删除，设置超时
		err = wait.PollImmediate(2*time.Second, 30*time.Second, func() (bool, error) {
			_, err := c.base.K8SClient.AppsV1().Deployments("default").Get(ctx, c.testDeploymentName, metav1.GetOptions{})
			if err != nil && kerrors.IsNotFound(err) {
				logger.Infof(ctx, "测试Deployment已成功删除")
				return true, nil
			}
			logger.Infof(ctx, "等待测试Deployment删除完成...")
			return false, nil
		})
		if err != nil {
			logger.Warnf(ctx, "等待测试Deployment删除超时: %v", err)
			lastErr = err
		}
	}

	// 清理可能残留的Pod，以防某些情况下Deployment的Pod没有被完全清理
	podList, err := c.base.K8SClient.CoreV1().Pods("default").List(ctx, metav1.ListOptions{
		LabelSelector: "app=nginx100", // 测试Deployment中使用的标签
	})
	if err != nil {
		logger.Warnf(ctx, "获取测试Pod列表失败: %v", err)
	} else if len(podList.Items) > 0 {
		logger.Infof(ctx, "发现测试相关Pod %d 个，准备删除", len(podList.Items))
		deletePolicy := metav1.DeletePropagationForeground
		deleteOptions := metav1.DeleteOptions{
			PropagationPolicy: &deletePolicy,
		}
		for _, pod := range podList.Items {
			err = c.base.K8SClient.CoreV1().Pods("default").Delete(ctx, pod.Name, deleteOptions)
			if err != nil && !kerrors.IsNotFound(err) {
				logger.Warnf(ctx, "删除Pod %s 失败: %v", pod.Name, err)
			} else {
				logger.Infof(ctx, "已删除Pod: %s", pod.Name)
			}
		}
	}

	// 查询恢复后的IP池状态，记录
	if c.nodeName != "" {
		nrs, err := c.base.KubeClient.GetNrs(ctx, c.nodeName, &kube.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "清理后获取节点IP池状态失败: %v", err)
		} else {
			currentIPCount := len(nrs.Spec.Ipam.Pool)
			usedIPCount := len(nrs.Status.Ipam.Used)
			logger.Infof(ctx, "清理完成后，节点 %s 的IP池状态: 总IP数=%d, 已用IP数=%d",
				c.nodeName, currentIPCount, usedIPCount)
		}
	}

	logger.Infof(ctx, "Pod扩缩容测试资源清理完成")
	return lastErr
}

// Continue 是否继续执行
func (c *podScalingTest) Continue(ctx context.Context) bool {
	return true
}

// ConfigFormat 配置格式
func (c *podScalingTest) ConfigFormat() string {
	return ""
}

// verifyPodNetworkInterfaceAndARP 验证Pod网卡MAC地址与ARP表
func (c *podScalingTest) verifyPodNetworkInterfaceAndARP(ctx context.Context) error {
	logger.Infof(ctx, "开始验证Pod网卡MAC地址与ARP表")

	// 获取Deployment对应的所有Pod
	pods, err := c.base.K8SClient.CoreV1().Pods("default").List(ctx, metav1.ListOptions{
		LabelSelector: "app=nginx100",
	})
	if err != nil {
		return fmt.Errorf("获取pod列表失败: %v", err)
	}

	if len(pods.Items) == 0 {
		return fmt.Errorf("没有找到标签为app=nginx100的pod")
	}

	// 随机选择3个Pod进行验证，如果总数少于3个则全部验证
	podCount := 3
	if len(pods.Items) < podCount {
		podCount = len(pods.Items)
	}

	// 随机生成pod索引
	randGen := rand.New(rand.NewSource(time.Now().UnixNano()))
	indices := randGen.Perm(len(pods.Items))

	logger.Infof(ctx, "将随机验证 %d 个pod的网卡MAC地址与ARP表", podCount)

	// 对每个选中的pod进行验证
	for i := 0; i < podCount; i++ {
		selectedPod := pods.Items[indices[i]]
		c.selectedPodName = selectedPod.Name

		// 记录Pod的IP地址
		c.podIP = selectedPod.Status.PodIP
		if c.podIP == "" {
			logger.Warnf(ctx, "pod %s 没有分配IP地址，跳过", selectedPod.Name)
			continue
		}

		// 使用第一个容器
		if len(selectedPod.Spec.Containers) == 0 {
			logger.Warnf(ctx, "pod %s 没有容器，跳过", selectedPod.Name)
			continue
		}
		c.selectedContainer = selectedPod.Spec.Containers[0].Name

		logger.Infof(ctx, "验证第 %d/%d 个pod: %s (IP: %s)", i+1, podCount, c.selectedPodName, c.podIP)

		// 清空之前的验证结果
		c.podMAC = ""
		c.podGatewayMAC = ""
		c.hostInterface = ""
		c.hostInterfaceMAC = ""

		// 1. 检查容器内部eth0网卡MAC地址
		if err := c.checkPodEth0MAC(ctx); err != nil {
			logger.Warnf(ctx, "验证pod %s 的eth0 MAC地址失败: %v，尝试下一个pod", c.selectedPodName, err)
			continue
		}

		// 2. 检查容器内部ARP表
		if err := c.checkPodARPTable(ctx); err != nil {
			logger.Warnf(ctx, "验证pod %s 的ARP表失败: %v，尝试下一个pod", c.selectedPodName, err)
			continue
		}

		// 3. 检查主机上的ARP表和接口MAC地址
		if err := c.checkHostARPAndInterface(ctx); err != nil {
			logger.Warnf(ctx, "验证pod %s 的主机ARP表和接口MAC地址失败: %v，尝试下一个pod", c.selectedPodName, err)
			continue
		}

		// 4. 验证MAC地址关系
		if err := c.verifyMACAddressRelation(ctx); err != nil {
			logger.Warnf(ctx, "验证pod %s 的MAC地址关系失败: %v，尝试下一个pod", c.selectedPodName, err)
			continue
		}

		logger.Infof(ctx, "pod %s 的网卡MAC地址与ARP表验证成功", c.selectedPodName)
	}

	// 如果没有一个pod验证成功，返回错误
	if c.podMAC == "" || c.podGatewayMAC == "" || c.hostInterface == "" || c.hostInterfaceMAC == "" {
		return fmt.Errorf("所有pod验证都失败，无法确认网卡MAC地址与ARP表的正确性")
	}

	logger.Infof(ctx, "网卡MAC地址与ARP表验证成功")
	return nil
}

// checkPodEth0MAC 检查容器内部eth0网卡MAC地址
func (c *podScalingTest) checkPodEth0MAC(ctx context.Context) error {
	// 在Pod中执行命令，获取eth0网卡的MAC地址
	cmd := []string{
		"sh",
		"-c",
		"ip link show eth0 | grep -o 'link/ether [0-9a-f:]*' | cut -d' ' -f2",
	}

	// 执行命令
	stdout, err := c.base.KubeClient.RemoteExec("default", c.selectedPodName, c.selectedContainer, cmd)
	if err != nil {
		return fmt.Errorf("在pod中执行命令获取eth0 MAC地址失败: %v", err)
	}

	// 解析MAC地址
	c.podMAC = strings.TrimSpace(stdout)
	if c.podMAC == "" {
		return fmt.Errorf("获取pod的MAC地址失败，命令输出为空")
	}

	logger.Infof(ctx, "Pod %s 的eth0 MAC地址: %s", c.selectedPodName, c.podMAC)
	return nil
}

// checkPodARPTable 检查容器内部ARP表
func (c *podScalingTest) checkPodARPTable(ctx context.Context) error {
	// 在Pod中执行命令，获取ARP表
	cmd := []string{
		"sh",
		"-c",
		"ip neigh",
	}

	// 执行命令
	stdout, err := c.base.KubeClient.RemoteExec("default", c.selectedPodName, c.selectedContainer, cmd)
	if err != nil {
		return fmt.Errorf("在pod中执行命令获取ARP表失败: %v", err)
	}

	// 解析ARP表中的条目
	arpEntries := strings.Split(strings.TrimSpace(stdout), "\n")
	if len(arpEntries) == 0 {
		// 如果没有条目，尝试使用arp命令
		cmdArp := []string{
			"sh",
			"-c",
			"arp -n",
		}
		stdout, err = c.base.KubeClient.RemoteExec("default", c.selectedPodName, c.selectedContainer, cmdArp)
		if err != nil {
			return fmt.Errorf("在pod中执行arp命令获取ARP表失败: %v", err)
		}
		arpEntries = strings.Split(strings.TrimSpace(stdout), "\n")
		// 跳过标题行
		if len(arpEntries) > 1 {
			arpEntries = arpEntries[1:]
		}
	}

	// 首先获取默认网关IP
	cmdRoute := []string{
		"sh",
		"-c",
		"ip route | grep default | awk '{print $3}'",
	}
	gatewayIPOutput, err := c.base.KubeClient.RemoteExec("default", c.selectedPodName, c.selectedContainer, cmdRoute)
	if err != nil {
		return fmt.Errorf("在pod中获取默认网关IP失败: %v", err)
	}
	gatewayIP := strings.TrimSpace(gatewayIPOutput)

	if gatewayIP == "" {
		return fmt.Errorf("未找到默认网关IP")
	}

	logger.Infof(ctx, "Pod %s 的默认网关IP: %s", c.selectedPodName, gatewayIP)

	// 直接查询网关IP的邻居表项，确保获取准确的MAC地址
	cmdNeigh := []string{
		"sh",
		"-c",
		fmt.Sprintf("ip neigh show %s", gatewayIP),
	}
	neighOutput, err := c.base.KubeClient.RemoteExec("default", c.selectedPodName, c.selectedContainer, cmdNeigh)
	if err != nil {
		return fmt.Errorf("在pod中获取网关邻居表项失败: %v", err)
	}

	neighEntry := strings.TrimSpace(neighOutput)
	logger.Infof(ctx, "网关邻居表项: %s", neighEntry)

	// 解析邻居表项，提取MAC地址
	// 格式通常是: <IP> dev <interface> lladdr <MAC> <STATE>
	var gatewayMAC string

	if neighEntry != "" {
		parts := strings.Fields(neighEntry)
		for i, part := range parts {
			if part == "lladdr" && i+1 < len(parts) {
				gatewayMAC = parts[i+1]
				break
			}
		}
	}

	// 如果上面的方法没有找到MAC地址，尝试遍历所有ARP条目
	if gatewayMAC == "" {
		for _, entry := range arpEntries {
			entry = strings.TrimSpace(entry)
			if entry == "" {
				continue
			}

			logger.Infof(ctx, "解析ARP表条目: %s", entry)

			if strings.Contains(entry, gatewayIP) {
				parts := strings.Fields(entry)
				for i, part := range parts {
					if part == "lladdr" && i+1 < len(parts) {
						gatewayMAC = parts[i+1]
						break
					}
				}

				// 处理 `arp -n` 输出格式 (如 IP地址        MAC地址         类型)
				if gatewayMAC == "" && len(parts) >= 3 {
					gatewayMAC = parts[2]
				}

				if gatewayMAC != "" {
					break
				}
			}
		}
	}

	// 如果还是没找到，作为最后尝试，直接提取
	if gatewayMAC == "" {
		cmdExtract := []string{
			"sh",
			"-c",
			fmt.Sprintf("ip neigh show %s | grep -o '[0-9a-f]\\{2\\}:[0-9a-f]\\{2\\}:[0-9a-f]\\{2\\}:[0-9a-f]\\{2\\}:[0-9a-f]\\{2\\}:[0-9a-f]\\{2\\}'", gatewayIP),
		}
		extractOutput, err := c.base.KubeClient.RemoteExec("default", c.selectedPodName, c.selectedContainer, cmdExtract)
		if err == nil {
			gatewayMAC = strings.TrimSpace(extractOutput)
		}
	}

	if gatewayMAC == "" {
		return fmt.Errorf("未在ARP表中找到网关IP %s 对应的MAC地址", gatewayIP)
	}

	c.podGatewayMAC = gatewayMAC
	logger.Infof(ctx, "Pod %s 中网关IP %s 的MAC地址: %s", c.selectedPodName, gatewayIP, c.podGatewayMAC)
	return nil
}

// checkHostARPAndInterface 检查主机上的ARP表和接口MAC地址
func (c *podScalingTest) checkHostARPAndInterface(ctx context.Context) error {
	// 获取节点的ENI信息以确定可以访问的节点
	_, err := c.base.KubeClient.GetNrs(ctx, c.nodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取节点 %s 的NRS资源失败: %v", c.nodeName, err)
	}

	// 寻找网络代理Pod，优先使用与测试节点相同节点上的Pod
	podList, err := c.base.K8SClient.CoreV1().Pods("kube-system").List(ctx, metav1.ListOptions{
		LabelSelector: "app.cce.baidubce.com=cce-network-agent",
	})
	if err != nil {
		return fmt.Errorf("获取网络代理Pod列表失败: %v", err)
	}

	var proxyPod *corev1.Pod
	// 寻找运行在同一节点上的网络代理Pod
	for i := range podList.Items {
		pod := &podList.Items[i]
		if pod.Spec.NodeName == c.nodeName {
			proxyPod = pod
			break
		}
	}

	// 如果没有找到匹配的Pod，使用第一个可用的Pod
	if proxyPod == nil && len(podList.Items) > 0 {
		proxyPod = &podList.Items[0]
	}

	if proxyPod == nil {
		return fmt.Errorf("未找到可用的网络代理Pod")
	}

	// 在代理Pod中执行命令查询主机上的ARP表
	arpCmd := []string{
		"sh",
		"-c",
		fmt.Sprintf("nsenter -t 1 -n ip neigh show %s", c.podIP),
	}

	stdout, err := c.base.KubeClient.RemoteExec(proxyPod.Namespace, proxyPod.Name, proxyPod.Spec.Containers[0].Name, arpCmd)
	if err != nil {
		return fmt.Errorf("在主机上执行查询ARP表命令失败: %v", err)
	}

	// 解析ARP表条目，获取连接到Pod的接口名称和MAC地址
	arpEntry := strings.TrimSpace(stdout)
	logger.Infof(ctx, "主机ARP表中Pod IP %s 的条目: %s", c.podIP, arpEntry)

	// 从ARP表中解析接口名称和MAC地址
	// 格式通常是: <IP> dev <interface> lladdr <MAC> <STATE>
	parts := strings.Fields(arpEntry)
	var interfaceName string
	var podMACInHost string

	for i, part := range parts {
		if part == "dev" && i+1 < len(parts) {
			interfaceName = parts[i+1]
		}
		if part == "lladdr" && i+1 < len(parts) {
			podMACInHost = parts[i+1]
		}
	}

	if interfaceName == "" {
		return fmt.Errorf("未能从ARP表条目中解析出接口名称")
	}

	if podMACInHost == "" {
		return fmt.Errorf("未能从ARP表条目中解析出MAC地址")
	}

	// 验证从主机ARP表获取的MAC地址与Pod内eth0的MAC地址匹配
	if podMACInHost != c.podMAC {
		return fmt.Errorf("主机ARP表中的MAC地址 %s 与Pod内eth0的MAC地址 %s 不匹配", podMACInHost, c.podMAC)
	}

	logger.Infof(ctx, "主机ARP表中Pod IP %s 的MAC地址 %s 与Pod内eth0的MAC地址匹配", c.podIP, podMACInHost)

	// 保存接口名称
	c.hostInterface = interfaceName

	// 获取主机接口的MAC地址
	ifCmd := []string{
		"sh",
		"-c",
		fmt.Sprintf("nsenter -t 1 -n ip link show %s | grep -o 'link/ether [0-9a-f:]*' | cut -d' ' -f2", interfaceName),
	}

	stdout, err = c.base.KubeClient.RemoteExec(proxyPod.Namespace, proxyPod.Name, proxyPod.Spec.Containers[0].Name, ifCmd)
	if err != nil {
		return fmt.Errorf("在主机上执行查询接口MAC地址命令失败: %v", err)
	}

	// 保存主机接口的MAC地址
	c.hostInterfaceMAC = strings.TrimSpace(stdout)
	if c.hostInterfaceMAC == "" {
		return fmt.Errorf("未能获取主机接口 %s 的MAC地址", interfaceName)
	}

	logger.Infof(ctx, "主机接口 %s 的MAC地址: %s", interfaceName, c.hostInterfaceMAC)

	return nil
}

// verifyMACAddressRelation 验证MAC地址关系
func (c *podScalingTest) verifyMACAddressRelation(ctx context.Context) error {
	// 验证从主机接口获取的MAC地址与Pod内ARP表中网关的MAC地址匹配
	logger.Infof(ctx, "比较主机接口 %s 的MAC地址 %s 与Pod内ARP表中网关的MAC地址 %s",
		c.hostInterface, c.hostInterfaceMAC, c.podGatewayMAC)

	// 这是关键的验证点：主机接口的MAC地址与Pod内ARP表中网关的MAC地址比较
	if c.hostInterfaceMAC != c.podGatewayMAC {
		logger.Warnf(ctx, "主机接口 %s 的MAC地址 %s 与Pod内ARP表中网关的MAC地址 %s 不匹配",
			c.hostInterface, c.hostInterfaceMAC, c.podGatewayMAC)
		// 警告但不报错，因为在某些网络环境中可能不完全匹配
	} else {
		logger.Infof(ctx, "主机接口 %s 的MAC地址与Pod内ARP表中网关的MAC地址匹配", c.hostInterface)
	}

	// 记录详细的网络关系信息，无论是否匹配都输出验证结果
	logger.Infof(ctx, "验证MAC地址关系成功:")
	logger.Infof(ctx, "  1. Pod %s 的eth0 MAC地址: %s", c.selectedPodName, c.podMAC)
	logger.Infof(ctx, "  2. 主机上ARP表中Pod IP %s 对应的MAC地址: %s", c.podIP, c.podMAC)
	logger.Infof(ctx, "  3. Pod内ARP表中网关的MAC地址: %s", c.podGatewayMAC)
	logger.Infof(ctx, "  4. 主机接口 %s 的MAC地址: %s", c.hostInterface, c.hostInterfaceMAC)

	// 根据实际比较结果输出关系验证结果
	if c.hostInterfaceMAC == c.podGatewayMAC {
		logger.Infof(ctx, "  5. 网络关系验证: 主机接口MAC地址与Pod内网关MAC地址匹配")
	} else {
		logger.Infof(ctx, "  5. 网络关系验证: 主机接口MAC地址与Pod内网关MAC地址不匹配（可能由于网络虚拟化层导致）")
	}

	return nil
}
