package network

import (
	"context"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	v1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	k8sResource "k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/remotecommand"
)

const (
	// CheckERIIBConnectivityCaseName - case 名字
	CheckERIIBConnectivityCaseName cases.CaseName = "CheckERIIBConnectivity"
	// ERI测试deployment名称
	eriTestDeploymentName = "eri-ib-test"
)

var (
	// 默认副本数
	defaultReplicas int32 = 2
)

func init() {
	cases.AddCase(context.Background(), CheckERIIBConnectivityCaseName, NewCheckERIIBConnectivity)
}

// checkERIIBConnectivity 结构体定义
type checkERIIBConnectivity struct {
	base *cases.BaseClient
}

// NewCheckERIIBConnectivity 构造函数
func NewCheckERIIBConnectivity(ctx context.Context) cases.Interface {
	return &checkERIIBConnectivity{}
}

func (c *checkERIIBConnectivity) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base is nil")
	}
	c.base = base
	return nil
}

func (c *checkERIIBConnectivity) Name() cases.CaseName {
	return CheckERIIBConnectivityCaseName
}

func (c *checkERIIBConnectivity) Desc() string {
	return "测试创建两个使用ERI资源的容器实例，并用IB命令测试其连通性"
}

// Check 实现Check接口
func (c *checkERIIBConnectivity) Check(ctx context.Context) ([]cases.Resource, error) {
	// 清理可能存在的残留资源
	logger.Infof(ctx, "清理可能存在的残留资源...")
	c.cleanupResources(ctx)

	// 第一步：创建使用ERI资源的deployment
	logger.Infof(ctx, "开始创建使用ERI资源的deployment...")
	if err := c.createERIDeployment(ctx); err != nil {
		return nil, fmt.Errorf("创建ERI deployment失败: %v", err)
	}

	// 第二步：等待deployment就绪
	logger.Infof(ctx, "等待deployment就绪...")
	if err := c.waitForDeploymentReady(ctx, 2*time.Minute); err != nil {
		return nil, fmt.Errorf("等待deployment就绪失败: %v", err)
	}

	// 第三步：获取pod列表并验证
	logger.Infof(ctx, "获取pod列表并验证...")
	pods, err := c.getERIPods(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取ERI pod列表失败: %v", err)
	}

	if len(pods) < 2 {
		return nil, fmt.Errorf("ERI pod数量不足，期望至少2个，实际: %d", len(pods))
	}

	// 第四步：验证pods都有IP地址且使用了ERI资源
	logger.Infof(ctx, "验证pods使用ERI资源...")
	if err := c.validateERIPods(ctx, pods); err != nil {
		return nil, fmt.Errorf("验证ERI pods失败: %v", err)
	}

	// 第五步：测试IB连通性
	logger.Infof(ctx, "测试IB连通性...")
	if err := c.testIBConnectivity(ctx, pods[0], pods[1]); err != nil {
		return nil, fmt.Errorf("IB连通性测试失败: %v", err)
	}

	logger.Infof(ctx, "ERI IB连通性测试完成，所有检查通过")
	return nil, nil
}

func (c *checkERIIBConnectivity) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理ERI IB连通性测试资源...")
	return c.cleanupResources(ctx)
}

func (c *checkERIIBConnectivity) Continue(ctx context.Context) bool {
	return true
}

// ConfigFormat 返回配置格式
func (c *checkERIIBConnectivity) ConfigFormat() string {
	return `{}`
}

// createERIDeployment 创建使用ERI资源的deployment
func (c *checkERIIBConnectivity) createERIDeployment(ctx context.Context) error {
	var terminationGracePeriodSeconds int64 = 0

	deployment := v1.Deployment{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Deployment",
			APIVersion: "apps/v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      eriTestDeploymentName,
			Namespace: corev1.NamespaceDefault,
			Labels: map[string]string{
				"app": "eri-ib-test",
			},
		},
		Spec: v1.DeploymentSpec{
			Replicas: &defaultReplicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": "eri-ib-test",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": "eri-ib-test",
					},
				},
				Spec: corev1.PodSpec{
					TerminationGracePeriodSeconds: &terminationGracePeriodSeconds,
					RestartPolicy:                 corev1.RestartPolicyAlways,
					// 添加特权模式和设备挂载
					HostIPC: true, // 允许使用主机IPC命名空间
					Containers: []corev1.Container{
						{
							Name:            "netshoot",
							Image:           "registry.baidubce.com/cce-plugin-dev/mlnx_ofed_linux-5.3-*******-centos7.9:latest",
							ImagePullPolicy: corev1.PullAlways,
							Command: []string{
								"/bin/sh",
								"-c",
								"while true;do echo hello;sleep 1;done",
							},
							Resources: corev1.ResourceRequirements{
								Limits: corev1.ResourceList{
									"rdma/ehca": k8sResource.MustParse("1"),
								},
								Requests: corev1.ResourceList{
									"rdma/ehca": k8sResource.MustParse("1"),
								},
							},
							SecurityContext: &corev1.SecurityContext{
								Capabilities: &corev1.Capabilities{
									Add: []corev1.Capability{
										"IPC_LOCK",
									},
								},
							},
						},
					},
				},
			},
		},
	}

	// 直接使用KubeClient创建deployment
	_, err := c.base.KubeClient.ClientSet.AppsV1().Deployments(corev1.NamespaceDefault).Create(ctx, &deployment, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建ERI deployment失败: %v", err)
	}

	logger.Infof(ctx, "成功创建ERI deployment: %s", eriTestDeploymentName)
	return nil
}

// waitForDeploymentReady 等待deployment就绪
func (c *checkERIIBConnectivity) waitForDeploymentReady(ctx context.Context, timeout time.Duration) error {
	start := time.Now()
	for {
		deployment, err := c.base.KubeClient.ClientSet.AppsV1().Deployments(corev1.NamespaceDefault).Get(ctx, eriTestDeploymentName, metav1.GetOptions{})
		if err != nil {
			if time.Since(start) > timeout {
				return fmt.Errorf("等待deployment超时: %v", err)
			}
			time.Sleep(5 * time.Second)
			continue
		}

		// 检查deployment状态
		if deployment.Status.ReadyReplicas >= defaultReplicas {
			logger.Infof(ctx, "Deployment就绪，ReadyReplicas: %d/%d", deployment.Status.ReadyReplicas, defaultReplicas)
			return nil
		}

		if time.Since(start) > timeout {
			return fmt.Errorf("等待deployment就绪超时，当前ReadyReplicas: %d/%d", deployment.Status.ReadyReplicas, defaultReplicas)
		}

		logger.Infof(ctx, "等待deployment就绪中... ReadyReplicas: %d/%d", deployment.Status.ReadyReplicas, defaultReplicas)
		time.Sleep(5 * time.Second)
	}
}

// getERIPods 获取ERI pod列表
func (c *checkERIIBConnectivity) getERIPods(ctx context.Context) ([]corev1.Pod, error) {
	pods, err := c.base.KubeClient.ClientSet.CoreV1().Pods(corev1.NamespaceDefault).List(ctx, metav1.ListOptions{
		LabelSelector: "app=eri-ib-test",
	})
	if err != nil {
		return nil, fmt.Errorf("获取pod列表失败: %v", err)
	}

	var runningPods []corev1.Pod
	for _, pod := range pods.Items {
		if pod.Status.Phase == corev1.PodRunning {
			runningPods = append(runningPods, pod)
		}
	}

	return runningPods, nil
}

// validateERIPods 验证pods使用了ERI资源
func (c *checkERIIBConnectivity) validateERIPods(ctx context.Context, pods []corev1.Pod) error {
	for _, pod := range pods {
		// 检查pod是否有IP地址
		if pod.Status.PodIP == "" {
			return fmt.Errorf("Pod %s 没有分配到IP地址", pod.Name)
		}

		// 检查pod是否请求了ERI资源
		for _, container := range pod.Spec.Containers {
			if rdmaLimit, ok := container.Resources.Limits["rdma/ehca"]; ok {
				if rdmaLimit.String() == "1" {
					logger.Infof(ctx, "Pod %s 正确申请了ERI资源，IP: %s", pod.Name, pod.Status.PodIP)
				} else {
					return fmt.Errorf("Pod %s 的ERI资源申请量不正确: %s", pod.Name, rdmaLimit.String())
				}
			} else {
				return fmt.Errorf("Pod %s 没有申请ERI资源", pod.Name)
			}
		}
	}
	return nil
}

// parseGidsOutput 解析show_gids命令输出
func parseGidsOutput(output string, podIP string) (string, string, error) {
	// 按行分割输出
	lines := strings.Split(output, "\n")

	// 跳过标题行
	for _, line := range lines[2:] {
		fields := strings.Fields(line)
		// 确保行有足够的字段，且IPv4字段不为空且包含有效的IPv4地址（包含点号）
		if len(fields) >= 7 && fields[5] == "v2" && fields[4] != "" && strings.Contains(fields[4], ".") {
			// 找到带IPv4地址的行
			return fields[0], fields[2], nil // 返回设备名和index
		}
	}
	return "", "", fmt.Errorf("未找到带IPv4地址的RDMA设备")
}

// testIBConnectivity 测试IB连通性
func (c *checkERIIBConnectivity) testIBConnectivity(ctx context.Context, pod1, pod2 corev1.Pod) error {
	// 在pod2（服务端）上执行show_gids命令获取设备信息
	showGidsCmd2 := []string{
		"show_gids",
	}
	stdout2, stderr2, err := c.execInPod(ctx, pod2.Namespace, pod2.Name, pod2.Spec.Containers[0].Name, showGidsCmd2)
	if err != nil {
		return fmt.Errorf("在pod2上执行show_gids命令失败: %v, stderr: %s", err, stderr2)
	}
	fmt.Printf("Pod2 (服务端) show_gids输出:\n%s\n", stdout2)

	// 解析pod2的show_gids输出
	dev2, index2, err := parseGidsOutput(stdout2, pod2.Status.PodIP)
	if err != nil {
		return fmt.Errorf("解析pod2 show_gids输出失败: %v", err)
	}

	// 从pod2的输出中提取IP地址
	serverIP, err := extractIPFromOutput(stdout2)
	if err != nil {
		return fmt.Errorf("从pod2输出中提取IP地址失败: %v", err)
	}

	// 在pod2上启动ib_write_bw服务端（后台运行）
	ibWriteCmd := []string{
		"sh", "-c",
		fmt.Sprintf("ib_write_bw -d %s -x %s -q 16 --report_gbits > /dev/null 2>&1 &", dev2, index2),
	}
	stdout2, stderr2, err = c.execInPod(ctx, pod2.Namespace, pod2.Name, pod2.Spec.Containers[0].Name, ibWriteCmd)
	if err != nil {
		return fmt.Errorf("在pod2上启动ib_write_bw服务端失败: %v, stderr: %s", err, stderr2)
	}
	fmt.Printf("Pod2 ib_write_bw服务端输出:\n%s\n", stdout2)

	// 等待服务端启动
	time.Sleep(2 * time.Second)

	// 在pod1（客户端）上执行show_gids命令获取设备信息
	showGidsCmd1 := []string{
		"show_gids",
	}
	stdout1, stderr1, err := c.execInPod(ctx, pod1.Namespace, pod1.Name, pod1.Spec.Containers[0].Name, showGidsCmd1)
	if err != nil {
		return fmt.Errorf("在pod1上执行show_gids命令失败: %v, stderr: %s", err, stderr1)
	}
	fmt.Printf("Pod1 (客户端) show_gids输出:\n%s\n", stdout1)

	// 解析pod1的show_gids输出
	dev1, index1, err := parseGidsOutput(stdout1, pod1.Status.PodIP)
	if err != nil {
		return fmt.Errorf("解析pod1 show_gids输出失败: %v", err)
	}

	// 在pod1上执行ib_write_bw客户端命令，连接到服务端IP
	ibWriteCmd = []string{
		"ib_write_bw",
		"-d", dev1,
		"-x", index1,
		"-q", "16",
		"--report_gbits",
		serverIP,
	}
	stdout1, stderr1, err = c.execInPod(ctx, pod1.Namespace, pod1.Name, pod1.Spec.Containers[0].Name, ibWriteCmd)
	if err != nil {
		return fmt.Errorf("在pod1上执行ib_write_bw客户端命令失败: %v, stderr: %s", err, stderr1)
	}
	fmt.Printf("Pod1 ib_write_bw客户端输出:\n%s\n", stdout1)

	return nil
}

// extractIPFromOutput 从show_gids输出中提取IP地址
func extractIPFromOutput(output string) (string, error) {
	lines := strings.Split(output, "\n")
	for _, line := range lines[2:] {
		fields := strings.Fields(line)
		if len(fields) >= 7 && fields[5] == "v2" && fields[4] != "" && strings.Contains(fields[4], ".") {
			return fields[4], nil
		}
	}
	return "", fmt.Errorf("未找到有效的IPv4地址")
}

// execInPod 在pod中执行命令
func (c *checkERIIBConnectivity) execInPod(ctx context.Context, namespace, podName, containerName string, command []string) (string, string, error) {
	fmt.Printf("在Pod %s 中执行命令: %v\n", podName, command)

	req := c.base.KubeClient.ClientSet.CoreV1().RESTClient().Post().
		Resource("pods").
		Name(podName).
		Namespace(namespace).
		SubResource("exec")

	req.VersionedParams(&corev1.PodExecOptions{
		Container: containerName,
		Command:   command,
		Stdin:     false,
		Stdout:    true,
		Stderr:    true,
		TTY:       false,
	}, scheme.ParameterCodec)

	exec, err := remotecommand.NewSPDYExecutor(c.base.KubeClient.RestConfig, "POST", req.URL())
	if err != nil {
		return "", "", fmt.Errorf("创建SPDY executor失败: %v", err)
	}

	var stdout, stderr strings.Builder
	err = exec.Stream(remotecommand.StreamOptions{
		Stdout: &stdout,
		Stderr: &stderr,
	})

	return stdout.String(), stderr.String(), err
}

// cleanupResources 清理测试资源
func (c *checkERIIBConnectivity) cleanupResources(ctx context.Context) error {
	// 删除deployment
	err := c.base.KubeClient.ClientSet.AppsV1().Deployments(corev1.NamespaceDefault).Delete(ctx, eriTestDeploymentName, metav1.DeleteOptions{})
	if err != nil {
		// 如果资源不存在，忽略错误
		if !strings.Contains(err.Error(), "not found") {
			logger.Warnf(ctx, "删除deployment失败: %v", err)
		}
	} else {
		logger.Infof(ctx, "成功删除deployment: %s", eriTestDeploymentName)
	}

	// 等待pods完全删除
	time.Sleep(10 * time.Second)

	return nil
}
