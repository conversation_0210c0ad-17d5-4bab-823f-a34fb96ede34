// Copyright 2024 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2024/12/19 15:00:00, by system, create
*/

/*
ResourceGCTest 资源回收测试用例

功能描述：
此测试用例验证容器网络资源回收功能，确保Pod删除后相关的网络资源能够被正确清理。
测试涵盖正常删除、强制删除等多种场景下的资源生命周期管理，以及垃圾收集器的工作状态。

测试场景：
1. Pod正常创建和删除的完整资源生命周期验证
2. Pod强制删除场景的资源回收验证
3. 大规模Pod并发创建删除的资源管理验证
4. 垃圾收集器状态和配置验证
5. 资源泄漏检测和清理验证

核心验证点：
1. 网络资源分配：Pod创建时IP地址、ENI资源的正确分配
2. 资源记录管理：BoltDB中Pod资源记录的正确存储和查询
3. 正常回收流程：Pod正常删除后资源的及时回收
4. 强制删除处理：Pod被强制删除时GC能发现并清理泄漏资源
5. 批量处理能力：大量Pod并发操作时的资源管理稳定性
6. GC工作状态：垃圾收集器的配置和运行状态验证

技术实现：
- 使用临时部署来创建测试Pod
- 通过NetworkResourceSet监控资源分配状态
- 检查ENI和IP资源的分配与回收情况
- 验证cce-network-agent的GC功能
- 模拟各种异常删除场景
*/

package network

import (
	"context"
	"fmt"
	"net"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/apimachinery/pkg/util/wait"
)

const (
	// ResourceGCTestCaseName - case 名字
	ResourceGCTestCaseName cases.CaseName = "ResourceGCTest"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), ResourceGCTestCaseName, NewResourceGCTest)
}

type resourceGCTest struct {
	base               *cases.BaseClient
	testDeploymentName string
	testNamespace      string
	nodeName           string
	// 资源状态记录
	initialIPCount    int
	initialENICount   int
	beforeCreateIPSet map[string]bool // 创建前的IP集合
	afterCreateIPSet  map[string]bool // 创建后的IP集合
	beforeDeleteIPSet map[string]bool // 删除前的IP集合
	afterDeleteIPSet  map[string]bool // 删除后的IP集合
	allocatedIPs      []string        // 本次测试分配的IP列表
	// GC配置相关
	gcIntervalSeconds  int
	gcEnabled          bool
	originalConfigData string // 保存原始ConfigMap数据用于恢复
}

// NewResourceGCTest 创建新的资源回收测试用例
func NewResourceGCTest(ctx context.Context) cases.Interface {
	return &resourceGCTest{
		testDeploymentName: "test-resource-gc-dep",
		testNamespace:      "default",
		beforeCreateIPSet:  make(map[string]bool),
		afterCreateIPSet:   make(map[string]bool),
		beforeDeleteIPSet:  make(map[string]bool),
		afterDeleteIPSet:   make(map[string]bool),
		allocatedIPs:       make([]string, 0),
	}
}

func (c *resourceGCTest) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base is nil")
	}
	c.base = base
	return nil
}

func (c *resourceGCTest) Name() cases.CaseName {
	return ResourceGCTestCaseName
}

func (c *resourceGCTest) Desc() string {
	return "测试容器网络资源回收功能，验证Pod删除后资源的正确清理"
}

func (c *resourceGCTest) Check(ctx context.Context) ([]cases.Resource, error) {
	logger.Infof(ctx, "开始执行资源回收测试")

	// 1. 前置检查和准备
	if err := c.preCheck(ctx); err != nil {
		return nil, fmt.Errorf("前置检查失败: %v", err)
	}

	// 2. 验证GC配置状态
	if err := c.checkGCConfiguration(ctx); err != nil {
		return nil, fmt.Errorf("GC配置检查失败: %v", err)
	}

	// 3. 执行正常删除场景测试
	logger.Infof(ctx, "开始执行正常删除场景测试")
	if err := c.testNormalDeletionLifecycle(ctx); err != nil {
		return nil, fmt.Errorf("正常删除场景测试失败: %v", err)
	}

	// 4. 执行强制删除场景测试
	logger.Infof(ctx, "开始执行强制删除场景测试")
	if err := c.testForceDeletionGC(ctx); err != nil {
		return nil, fmt.Errorf("强制删除场景测试失败: %v", err)
	}

	// 5. 执行批量操作场景测试
	logger.Infof(ctx, "开始执行批量操作场景测试")
	if err := c.testBatchOperationGC(ctx); err != nil {
		return nil, fmt.Errorf("批量操作场景测试失败: %v", err)
	}

	logger.Infof(ctx, "资源回收测试全部完成")
	return nil, nil
}

// preCheck 执行前置检查
func (c *resourceGCTest) preCheck(ctx context.Context) error {
	logger.Infof(ctx, "开始执行资源回收测试前置检查")

	// 检查CNI组件状态
	if err := c.checkCNIComponents(ctx); err != nil {
		return fmt.Errorf("CNI组件检查失败: %v", err)
	}

	// 选择测试节点
	if err := c.selectTestNode(ctx); err != nil {
		return fmt.Errorf("选择测试节点失败: %v", err)
	}

	// 记录初始资源状态
	if err := c.recordInitialResourceState(ctx); err != nil {
		return fmt.Errorf("记录初始资源状态失败: %v", err)
	}

	logger.Infof(ctx, "前置检查完成，选择的测试节点: %s", c.nodeName)
	return nil
}

// checkCNIComponents 检查CNI组件状态
func (c *resourceGCTest) checkCNIComponents(ctx context.Context) error {
	logger.Infof(ctx, "检查CNI组件状态")

	var missingComponents []string
	var componentStatuses []string

	// 检查cce-network-agent DaemonSet
	agentDS, err := c.base.K8SClient.AppsV1().DaemonSets("kube-system").Get(ctx, "cce-network-agent", metav1.GetOptions{})
	if err != nil {
		missingComponents = append(missingComponents, "cce-network-agent DaemonSet")
		componentStatuses = append(componentStatuses, fmt.Sprintf("❌ cce-network-agent DaemonSet: %v", err))
		logger.Warnf(ctx, "cce-network-agent DaemonSet不存在: %v", err)
	} else {
		componentStatuses = append(componentStatuses, fmt.Sprintf("✅ cce-network-agent DaemonSet: Ready (%d/%d)", agentDS.Status.NumberReady, agentDS.Status.DesiredNumberScheduled))
		logger.Infof(ctx, "cce-network-agent DaemonSet状态正常")
	}

	// 检查cce-network-operator Deployment (允许缺失)
	operatorDeploy, err := c.base.K8SClient.AppsV1().Deployments("kube-system").Get(ctx, "cce-network-operator", metav1.GetOptions{})
	if err != nil {
		componentStatuses = append(componentStatuses, fmt.Sprintf("⚠️  cce-network-operator Deployment: 未找到 (%v)", err))
		logger.Warnf(ctx, "cce-network-operator Deployment不存在，但测试可以继续: %v", err)
	} else {
		componentStatuses = append(componentStatuses, fmt.Sprintf("✅ cce-network-operator Deployment: Ready (%d/%d)", operatorDeploy.Status.ReadyReplicas, operatorDeploy.Status.Replicas))
		logger.Infof(ctx, "cce-network-operator Deployment状态正常")
	}

	// 检查网络配置ConfigMap
	configMap, err := c.base.K8SClient.CoreV1().ConfigMaps("kube-system").Get(ctx, "cce-network-v2-config", metav1.GetOptions{})
	if err != nil {
		missingComponents = append(missingComponents, "cce-network-v2-config ConfigMap")
		componentStatuses = append(componentStatuses, fmt.Sprintf("❌ cce-network-v2-config ConfigMap: %v", err))
		logger.Warnf(ctx, "cce-network-v2-config ConfigMap不存在: %v", err)
	} else {
		componentStatuses = append(componentStatuses, fmt.Sprintf("✅ cce-network-v2-config ConfigMap: 已找到 (keys: %v)", getMapKeys(configMap.Data)))
		logger.Infof(ctx, "cce-network-v2-config ConfigMap状态正常")
	}

	// 输出详细的组件状态报告
	logger.Infof(ctx, "CNI组件状态报告:")
	for _, status := range componentStatuses {
		logger.Infof(ctx, "  %s", status)
	}

	// 如果关键组件（agent和configmap）都缺失，则测试无法继续
	if len(missingComponents) >= 2 {
		return fmt.Errorf("关键CNI组件缺失，测试无法继续: %v。请确认集群的CNI网络插件已正确部署", missingComponents)
	}

	// 如果只有operator缺失，记录警告但允许测试继续
	if len(missingComponents) == 1 && strings.Contains(missingComponents[0], "operator") {
		logger.Warnf(ctx, "cce-network-operator缺失，某些网络功能可能受限，但基础测试可以继续")
	}

	logger.Infof(ctx, "CNI组件状态检查完成，测试可以继续")
	return nil
}

// 辅助函数：获取map的keys
func getMapKeys(m map[string]string) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

// selectTestNode 选择测试节点
func (c *resourceGCTest) selectTestNode(ctx context.Context) error {
	logger.Infof(ctx, "选择测试节点")

	nodes, err := c.base.KubeClient.ListNode(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取节点列表失败: %v", err)
	}

	if len(nodes.Items) == 0 {
		return fmt.Errorf("集群中没有可用节点")
	}

	// 选择第一个Ready状态且非主节点的工作节点
	for _, node := range nodes.Items {
		// 检查节点是否为Ready状态
		ready := false
		for _, condition := range node.Status.Conditions {
			if condition.Type == corev1.NodeReady && condition.Status == corev1.ConditionTrue {
				ready = true
				break
			}
		}

		// 跳过不Ready的节点和主节点
		if !ready {
			continue
		}

		// 检查是否为主节点
		if _, isMaster := node.Labels["node-role.kubernetes.io/master"]; !isMaster {
			if _, isControlPlane := node.Labels["node-role.kubernetes.io/control-plane"]; !isControlPlane {
				c.nodeName = node.Name
				logger.Infof(ctx, "选择节点: %s", c.nodeName)
				return nil
			}
		}
	}

	// 如果没有找到工作节点，使用第一个Ready节点
	for _, node := range nodes.Items {
		ready := false
		for _, condition := range node.Status.Conditions {
			if condition.Type == corev1.NodeReady && condition.Status == corev1.ConditionTrue {
				ready = true
				break
			}
		}
		if ready {
			c.nodeName = node.Name
			logger.Infof(ctx, "使用节点: %s", c.nodeName)
			return nil
		}
	}

	return fmt.Errorf("没有找到可用的Ready节点")
}

// recordInitialResourceState 记录初始资源状态
func (c *resourceGCTest) recordInitialResourceState(ctx context.Context) error {
	logger.Infof(ctx, "记录初始资源状态")

	// 获取节点的NetworkResourceSet
	nrs, err := c.base.KubeClient.GetNrs(ctx, c.nodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取节点NetworkResourceSet失败: %v", err)
	}

	// 统计初始IP数量
	c.initialIPCount = 0
	c.initialENICount = len(nrs.Status.Enis)

	// 记录当前所有已分配的IP
	for ipAddr := range nrs.Status.Ipam.Used {
		c.beforeCreateIPSet[ipAddr] = true
		c.initialIPCount++
	}

	logger.Infof(ctx, "初始资源状态 - 节点: %s, IP数量: %d, ENI数量: %d",
		c.nodeName, c.initialIPCount, c.initialENICount)

	return nil
}

// checkGCConfiguration 检查GC配置
func (c *resourceGCTest) checkGCConfiguration(ctx context.Context) error {
	logger.Infof(ctx, "检查垃圾收集器配置")

	// 获取网络配置
	configMap, err := c.base.K8SClient.CoreV1().ConfigMaps("kube-system").Get(ctx, "cce-network-v2-config", metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取网络配置失败: %v", err)
	}

	// 保存原始配置用于恢复
	c.originalConfigData = configMap.Data["cced"]

	// 解析GC相关配置
	configData := configMap.Data["cced"]
	c.gcEnabled = true       // 默认启用
	c.gcIntervalSeconds = 30 // 默认30秒

	// 解析endpoint-gc-interval配置
	if strings.Contains(configData, "endpoint-gc-interval:") {
		lines := strings.Split(configData, "\n")
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if strings.HasPrefix(line, "endpoint-gc-interval:") {
				value := strings.TrimSpace(strings.TrimPrefix(line, "endpoint-gc-interval:"))
				if strings.HasSuffix(value, "s") {
					if interval, err := strconv.Atoi(value[:len(value)-1]); err == nil {
						c.gcIntervalSeconds = interval
					}
				}
				break
			}
		}
	}

	logger.Infof(ctx, "GC配置 - 启用状态: %v, 清理间隔: %d秒", c.gcEnabled, c.gcIntervalSeconds)

	// 如果GC间隔过长，临时调整为较短间隔以加快测试
	if c.gcIntervalSeconds > 60 {
		logger.Infof(ctx, "当前GC间隔过长(%d秒)，临时调整为30秒以加快测试", c.gcIntervalSeconds)
		if err := c.adjustGCInterval(ctx, 30); err != nil {
			logger.Warnf(ctx, "调整GC间隔失败，继续使用原配置: %v", err)
		}
	}

	return nil
}

// adjustGCInterval 调整GC间隔
func (c *resourceGCTest) adjustGCInterval(ctx context.Context, intervalSeconds int) error {
	configMap, err := c.base.K8SClient.CoreV1().ConfigMaps("kube-system").Get(ctx, "cce-network-v2-config", metav1.GetOptions{})
	if err != nil {
		return err
	}

	configData := configMap.Data["cced"]
	lines := strings.Split(configData, "\n")

	// 查找并替换endpoint-gc-interval配置
	modified := false
	for i, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "endpoint-gc-interval:") {
			lines[i] = fmt.Sprintf("endpoint-gc-interval: %ds", intervalSeconds)
			modified = true
			break
		}
	}

	// 如果没有找到配置，添加新配置
	if !modified {
		lines = append(lines, fmt.Sprintf("endpoint-gc-interval: %ds", intervalSeconds))
	}

	// 更新ConfigMap
	configMap.Data["cced"] = strings.Join(lines, "\n")
	_, err = c.base.K8SClient.CoreV1().ConfigMaps("kube-system").Update(ctx, configMap, metav1.UpdateOptions{})
	if err != nil {
		return err
	}

	// 重启网络组件使配置生效
	return c.restartNetworkComponents(ctx)
}

// restartNetworkComponents 重启网络组件
func (c *resourceGCTest) restartNetworkComponents(ctx context.Context) error {
	logger.Infof(ctx, "重启网络组件使配置生效")

	// 重启cce-network-agent
	if err := c.restartDaemonSet(ctx, "kube-system", "cce-network-agent"); err != nil {
		return fmt.Errorf("重启cce-network-agent失败: %v", err)
	}

	// 重启cce-network-operator
	if err := c.restartDeployment(ctx, "kube-system", "cce-network-operator"); err != nil {
		return fmt.Errorf("重启cce-network-operator失败: %v", err)
	}

	// 等待组件就绪
	return c.waitForComponentsReady(ctx)
}

// restartDaemonSet 重启DaemonSet
func (c *resourceGCTest) restartDaemonSet(ctx context.Context, namespace, name string) error {
	ds, err := c.base.K8SClient.AppsV1().DaemonSets(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return err
	}

	// 添加重启注释
	if ds.Spec.Template.Annotations == nil {
		ds.Spec.Template.Annotations = make(map[string]string)
	}
	ds.Spec.Template.Annotations["kubectl.kubernetes.io/restartedAt"] = time.Now().Format(time.RFC3339)

	_, err = c.base.K8SClient.AppsV1().DaemonSets(namespace).Update(ctx, ds, metav1.UpdateOptions{})
	return err
}

// restartDeployment 重启Deployment
func (c *resourceGCTest) restartDeployment(ctx context.Context, namespace, name string) error {
	deploy, err := c.base.K8SClient.AppsV1().Deployments(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return err
	}

	// 添加重启注释
	if deploy.Spec.Template.Annotations == nil {
		deploy.Spec.Template.Annotations = make(map[string]string)
	}
	deploy.Spec.Template.Annotations["kubectl.kubernetes.io/restartedAt"] = time.Now().Format(time.RFC3339)

	_, err = c.base.K8SClient.AppsV1().Deployments(namespace).Update(ctx, deploy, metav1.UpdateOptions{})
	return err
}

// waitForComponentsReady 等待组件就绪
func (c *resourceGCTest) waitForComponentsReady(ctx context.Context) error {
	logger.Infof(ctx, "等待网络组件重启完成")

	// 等待DaemonSet就绪
	err := wait.PollImmediate(5*time.Second, 180*time.Second, func() (bool, error) {
		ds, err := c.base.K8SClient.AppsV1().DaemonSets("kube-system").Get(ctx, "cce-network-agent", metav1.GetOptions{})
		if err != nil {
			return false, nil
		}
		return ds.Status.NumberReady == ds.Status.DesiredNumberScheduled && ds.Status.NumberReady > 0, nil
	})
	if err != nil {
		return fmt.Errorf("等待cce-network-agent就绪超时: %v", err)
	}

	// 等待Deployment就绪
	err = wait.PollImmediate(5*time.Second, 180*time.Second, func() (bool, error) {
		deploy, err := c.base.K8SClient.AppsV1().Deployments("kube-system").Get(ctx, "cce-network-operator", metav1.GetOptions{})
		if err != nil {
			return false, nil
		}
		return deploy.Status.ReadyReplicas == *deploy.Spec.Replicas && deploy.Status.ReadyReplicas > 0, nil
	})
	if err != nil {
		return fmt.Errorf("等待cce-network-operator就绪超时: %v", err)
	}

	logger.Infof(ctx, "网络组件重启完成")
	return nil
}

// testNormalDeletionLifecycle 测试正常删除生命周期
func (c *resourceGCTest) testNormalDeletionLifecycle(ctx context.Context) error {
	logger.Infof(ctx, "测试正常删除场景的资源生命周期")

	// 创建测试Pod
	deploymentName := c.testDeploymentName + "-normal"
	if err := c.createTestDeployment(ctx, deploymentName, 3); err != nil {
		return fmt.Errorf("创建测试Deployment失败: %v", err)
	}

	// 等待Pod就绪并记录分配的资源
	if err := c.waitForPodsReady(ctx, deploymentName); err != nil {
		return fmt.Errorf("等待Pod就绪失败: %v", err)
	}

	// 记录创建后的资源状态
	if err := c.recordResourceStateAfterCreate(ctx); err != nil {
		return fmt.Errorf("记录创建后资源状态失败: %v", err)
	}

	// 验证资源正确分配
	if err := c.verifyResourceAllocation(ctx); err != nil {
		return fmt.Errorf("验证资源分配失败: %v", err)
	}

	// 正常删除Deployment
	if err := c.deleteTestDeployment(ctx, deploymentName); err != nil {
		return fmt.Errorf("删除测试Deployment失败: %v", err)
	}

	// 等待资源回收
	if err := c.waitForResourceReclamation(ctx, "正常删除"); err != nil {
		return fmt.Errorf("资源回收验证失败: %v", err)
	}

	logger.Infof(ctx, "正常删除场景测试完成")
	return nil
}

// testForceDeletionGC 测试强制删除后的GC功能
func (c *resourceGCTest) testForceDeletionGC(ctx context.Context) error {
	logger.Infof(ctx, "测试强制删除场景的GC功能")

	// 创建测试Pod
	deploymentName := c.testDeploymentName + "-force"
	if err := c.createTestDeployment(ctx, deploymentName, 2); err != nil {
		return fmt.Errorf("创建测试Deployment失败: %v", err)
	}

	// 等待Pod就绪
	if err := c.waitForPodsReady(ctx, deploymentName); err != nil {
		return fmt.Errorf("等待Pod就绪失败: %v", err)
	}

	// 记录创建后的资源状态
	if err := c.recordResourceStateAfterCreate(ctx); err != nil {
		return fmt.Errorf("记录创建后资源状态失败: %v", err)
	}

	// 强制删除Pod（模拟异常删除）
	if err := c.forceDeletePods(ctx, deploymentName); err != nil {
		return fmt.Errorf("强制删除Pod失败: %v", err)
	}

	// 删除Deployment
	if err := c.deleteTestDeployment(ctx, deploymentName); err != nil {
		return fmt.Errorf("删除测试Deployment失败: %v", err)
	}

	// 等待GC清理泄漏资源
	if err := c.waitForGCCleanup(ctx); err != nil {
		return fmt.Errorf("等待GC清理失败: %v", err)
	}

	logger.Infof(ctx, "强制删除场景测试完成")
	return nil
}

// testBatchOperationGC 测试批量操作的GC功能
func (c *resourceGCTest) testBatchOperationGC(ctx context.Context) error {
	logger.Infof(ctx, "测试批量操作场景的GC功能")

	// 创建多个测试Deployment
	deploymentNames := make([]string, 0)
	for i := 0; i < 3; i++ {
		deploymentName := fmt.Sprintf("%s-batch-%d", c.testDeploymentName, i)
		deploymentNames = append(deploymentNames, deploymentName)

		if err := c.createTestDeployment(ctx, deploymentName, 2); err != nil {
			return fmt.Errorf("创建测试Deployment %s失败: %v", deploymentName, err)
		}
	}

	// 等待所有Pod就绪
	for _, deploymentName := range deploymentNames {
		if err := c.waitForPodsReady(ctx, deploymentName); err != nil {
			return fmt.Errorf("等待Deployment %s的Pod就绪失败: %v", deploymentName, err)
		}
	}

	// 记录创建后的资源状态
	if err := c.recordResourceStateAfterCreate(ctx); err != nil {
		return fmt.Errorf("记录创建后资源状态失败: %v", err)
	}

	// 批量删除Deployment
	for _, deploymentName := range deploymentNames {
		if err := c.deleteTestDeployment(ctx, deploymentName); err != nil {
			logger.Warnf(ctx, "删除Deployment %s失败: %v", deploymentName, err)
		}
	}

	// 等待批量资源回收
	if err := c.waitForResourceReclamation(ctx, "批量删除"); err != nil {
		return fmt.Errorf("批量资源回收验证失败: %v", err)
	}

	logger.Infof(ctx, "批量操作场景测试完成")
	return nil
}

// createTestDeployment 创建测试用的Deployment
func (c *resourceGCTest) createTestDeployment(ctx context.Context, name string, replicas int) error {
	logger.Infof(ctx, "创建测试Deployment: %s (副本数: %d)", name, replicas)

	int32Replicas := int32(replicas)
	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: c.testNamespace,
			Labels: map[string]string{
				"app":      "resource-gc-test",
				"test-run": name,
			},
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &int32Replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app":      "resource-gc-test",
					"test-run": name,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app":      "resource-gc-test",
						"test-run": name,
					},
				},
				Spec: corev1.PodSpec{
					NodeSelector: map[string]string{
						"kubernetes.io/hostname": c.nodeName,
					},
					RestartPolicy: corev1.RestartPolicyAlways,
					Containers: []corev1.Container{
						{
							Name:            "test-container",
							Image:           "registry.baidubce.com/cce/nginx-alpine-go:latest",
							ImagePullPolicy: corev1.PullAlways,
							Ports: []corev1.ContainerPort{
								{
									ContainerPort: 80,
								},
							},
							LivenessProbe: &corev1.Probe{
								Handler: corev1.Handler{
									HTTPGet: &corev1.HTTPGetAction{
										Path: "/",
										Port: intstr.FromInt(80),
									},
								},
								InitialDelaySeconds: 20,
								TimeoutSeconds:      5,
								PeriodSeconds:       5,
							},
							ReadinessProbe: &corev1.Probe{
								Handler: corev1.Handler{
									HTTPGet: &corev1.HTTPGetAction{
										Path: "/",
										Port: intstr.FromInt(80),
									},
								},
								InitialDelaySeconds: 5,
								TimeoutSeconds:      1,
								PeriodSeconds:       5,
							},
						},
					},
				},
			},
		},
	}

	_, err := c.base.K8SClient.AppsV1().Deployments(c.testNamespace).Create(ctx, deployment, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建Deployment失败: %v", err)
	}

	logger.Infof(ctx, "Deployment %s 创建成功", name)
	return nil
}

// waitForPodsReady 等待Pod就绪
func (c *resourceGCTest) waitForPodsReady(ctx context.Context, deploymentName string) error {
	logger.Infof(ctx, "等待Deployment %s 的Pod就绪", deploymentName)

	return wait.PollImmediate(5*time.Second, 300*time.Second, func() (bool, error) {
		deployment, err := c.base.K8SClient.AppsV1().Deployments(c.testNamespace).Get(ctx, deploymentName, metav1.GetOptions{})
		if err != nil {
			return false, nil
		}

		return deployment.Status.ReadyReplicas == *deployment.Spec.Replicas &&
			deployment.Status.ReadyReplicas > 0, nil
	})
}

// recordResourceStateAfterCreate 记录创建后的资源状态
func (c *resourceGCTest) recordResourceStateAfterCreate(ctx context.Context) error {
	logger.Infof(ctx, "记录资源创建后的状态")

	// 获取节点的NetworkResourceSet
	nrs, err := c.base.KubeClient.GetNrs(ctx, c.nodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取节点NetworkResourceSet失败: %v", err)
	}

	// 清空并重新记录创建后的IP集合
	c.afterCreateIPSet = make(map[string]bool)
	c.allocatedIPs = make([]string, 0)

	for ipAddr := range nrs.Status.Ipam.Used {
		c.afterCreateIPSet[ipAddr] = true
		// 如果这个IP是新分配的（创建前没有），记录为本次测试分配的IP
		if !c.beforeCreateIPSet[ipAddr] {
			c.allocatedIPs = append(c.allocatedIPs, ipAddr)
		}
	}

	logger.Infof(ctx, "创建后资源状态 - 总IP数: %d, 新分配IP数: %d",
		len(c.afterCreateIPSet), len(c.allocatedIPs))

	return nil
}

// verifyResourceAllocation 验证资源分配
func (c *resourceGCTest) verifyResourceAllocation(ctx context.Context) error {
	logger.Infof(ctx, "验证资源分配情况")

	if len(c.allocatedIPs) == 0 {
		return fmt.Errorf("没有检测到新分配的IP地址")
	}

	logger.Infof(ctx, "本次测试分配的IP地址: %v", c.allocatedIPs)

	// 验证分配的IP是否都在有效范围内
	for _, ip := range c.allocatedIPs {
		if net.ParseIP(ip) == nil {
			return fmt.Errorf("分配的IP地址格式无效: %s", ip)
		}
	}

	logger.Infof(ctx, "资源分配验证通过")
	return nil
}

// deleteTestDeployment 删除测试Deployment
func (c *resourceGCTest) deleteTestDeployment(ctx context.Context, name string) error {
	logger.Infof(ctx, "删除测试Deployment: %s", name)

	err := c.base.K8SClient.AppsV1().Deployments(c.testNamespace).Delete(ctx, name, metav1.DeleteOptions{})
	if err != nil && !kerrors.IsNotFound(err) {
		return fmt.Errorf("删除Deployment失败: %v", err)
	}

	// 等待Deployment被完全删除
	return wait.PollImmediate(2*time.Second, 60*time.Second, func() (bool, error) {
		_, err := c.base.K8SClient.AppsV1().Deployments(c.testNamespace).Get(ctx, name, metav1.GetOptions{})
		return kerrors.IsNotFound(err), nil
	})
}

// forceDeletePods 强制删除Pod
func (c *resourceGCTest) forceDeletePods(ctx context.Context, deploymentName string) error {
	logger.Infof(ctx, "强制删除Deployment %s 的Pod", deploymentName)

	// 获取Deployment对应的Pod
	pods, err := c.base.KubeClient.ListPod(ctx, c.testNamespace, &kube.ListOptions{
		LabelSelector: map[string]string{
			"app":      "resource-gc-test",
			"test-run": deploymentName,
		},
	})
	if err != nil {
		return fmt.Errorf("获取Pod列表失败: %v", err)
	}

	// 强制删除所有Pod
	gracePeriod := int64(0)
	for _, pod := range pods.Items {
		err := c.base.K8SClient.CoreV1().Pods(pod.Namespace).Delete(ctx, pod.Name, metav1.DeleteOptions{
			GracePeriodSeconds: &gracePeriod,
		})
		if err != nil && !kerrors.IsNotFound(err) {
			logger.Warnf(ctx, "强制删除Pod %s失败: %v", pod.Name, err)
		}
	}

	logger.Infof(ctx, "已强制删除 %d 个Pod", len(pods.Items))
	return nil
}

// waitForResourceReclamation 等待资源回收
func (c *resourceGCTest) waitForResourceReclamation(ctx context.Context, scenario string) error {
	logger.Infof(ctx, "等待%s场景的资源回收", scenario)

	startTime := time.Now()
	timeout := 300 * time.Second // 5分钟超时

	return wait.PollImmediate(10*time.Second, timeout, func() (bool, error) {
		// 获取当前资源状态
		nrs, err := c.base.KubeClient.GetNrs(ctx, c.nodeName, &kube.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取NetworkResourceSet失败: %v", err)
			return false, nil
		}

		// 统计当前IP数量
		currentIPSet := make(map[string]bool)
		for ipAddr := range nrs.Status.Ipam.Used {
			currentIPSet[ipAddr] = true
		}

		// 检查是否有IP被回收
		reclaimedCount := 0
		for _, allocatedIP := range c.allocatedIPs {
			if !currentIPSet[allocatedIP] {
				reclaimedCount++
			}
		}

		elapsed := time.Since(startTime)
		logger.Infof(ctx, "%s - 已回收IP: %d/%d, 耗时: %.1f秒",
			scenario, reclaimedCount, len(c.allocatedIPs), elapsed.Seconds())

		// 如果所有分配的IP都被回收，则认为回收完成
		if reclaimedCount == len(c.allocatedIPs) {
			logger.Infof(ctx, "%s场景资源回收完成，总耗时: %.1f秒", scenario, elapsed.Seconds())
			return true, nil
		}

		return false, nil
	})
}

// waitForGCCleanup 等待GC清理
func (c *resourceGCTest) waitForGCCleanup(ctx context.Context) error {
	logger.Infof(ctx, "等待垃圾收集器清理泄漏资源")

	// 等待时间应该比GC间隔长一些
	gcWaitTime := time.Duration(c.gcIntervalSeconds+30) * time.Second
	if gcWaitTime < 60*time.Second {
		gcWaitTime = 60 * time.Second
	}

	logger.Infof(ctx, "等待GC清理，预计等待时间: %.0f秒", gcWaitTime.Seconds())

	return c.waitForResourceReclamation(ctx, "GC清理")
}

func (c *resourceGCTest) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理资源回收测试环境")

	// 清理所有测试Deployment
	deploymentNames := []string{
		c.testDeploymentName + "-normal",
		c.testDeploymentName + "-force",
	}

	// 添加批量测试的Deployment
	for i := 0; i < 3; i++ {
		deploymentNames = append(deploymentNames, fmt.Sprintf("%s-batch-%d", c.testDeploymentName, i))
	}

	for _, name := range deploymentNames {
		if err := c.deleteTestDeployment(ctx, name); err != nil {
			logger.Warnf(ctx, "清理Deployment %s失败: %v", name, err)
		}
	}

	// 恢复原始配置
	if c.originalConfigData != "" {
		if err := c.restoreOriginalConfig(ctx); err != nil {
			logger.Warnf(ctx, "恢复原始配置失败: %v", err)
		}
	}

	logger.Infof(ctx, "资源回收测试环境清理完成")
	return nil
}

// restoreOriginalConfig 恢复原始配置
func (c *resourceGCTest) restoreOriginalConfig(ctx context.Context) error {
	logger.Infof(ctx, "恢复原始网络配置")

	configMap, err := c.base.K8SClient.CoreV1().ConfigMaps("kube-system").Get(ctx, "cce-network-v2-config", metav1.GetOptions{})
	if err != nil {
		return err
	}

	configMap.Data["cced"] = c.originalConfigData
	_, err = c.base.K8SClient.CoreV1().ConfigMaps("kube-system").Update(ctx, configMap, metav1.UpdateOptions{})
	if err != nil {
		return err
	}

	// 重启网络组件使配置生效
	return c.restartNetworkComponents(ctx)
}

func (c *resourceGCTest) Continue(ctx context.Context) bool {
	return true
}

func (c *resourceGCTest) ConfigFormat() string {
	return ""
}
