/*
Copyright 2024 Baidu Inc.

本文件包含了针对CCE集群节点组扩缩容中网络资源管理的自动化测试用例。
maxuezhen
2024-04-16

用例主要验证以下内容：
1. 节点组扩容时NRS和ENI资源状态的正确性
   - 验证节点组扩容后，为新增节点创建的NRS资源是否正确
   - 检查为新增节点创建的ENI资源是否按预期配置
   - 确认NRS和ENI的关联关系是否正确

2. 验证手动添加ENI后资源状态
   - 测试手动创建并附加到节点的ENI不会被CCE自动纳管
   - 验证手动创建ENI的完整生命周期（创建、附加、分离、删除）
   - 检查手动创建ENI的状态变化

3. 验证节点组缩容后资源回收状态
   - 检查节点组缩容删除节点后，其对应的NRS资源是否正确回收
   - 验证删除节点的ENI资源是否被正确释放
   - 确保资源回收的完整性和及时性

测试流程：
1. 记录集群当前节点状态，获取可用的节点组
2. 随机选择一个节点组并记录其原始副本数
3. 对选定的节点组进行扩容操作（+1节点）
4. 等待节点组扩容完成并确认新节点就绪
5. 验证新增节点的NRS和ENI资源状态
6. 测试手动创建并附加ENI到新节点，确认不被CCE纳管
7. 对节点组进行缩容操作，恢复到原始副本数
8. 验证删除节点的资源回收情况
9. 清理测试过程中创建的资源

该测试确保CCE集群在节点组扩缩容过程中能够正确管理网络资源，
包括NRS和ENI的创建、配置和回收，同时验证手动添加的ENI不会被自动纳管。
测试结束后会恢复节点组到其原始副本数，不会影响集群中的其他节点组。
*/

package network

import (
	"context"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eni"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/vpc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	corev1 "k8s.io/api/core/v1"
)

const (
	// CheckNodeScaleResourcesCaseName - case 名字
	CheckNodeScaleResourcesCaseName cases.CaseName = "CheckNodeScaleResources"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CheckNodeScaleResourcesCaseName, NewCheckNodeScaleResources)
}

type checkNodeScaleResources struct {
	base             *cases.BaseClient
	instanceGroup    string
	nodeCount        int
	nodeSnapshot     map[string]struct{} // 保存节点扩容前的节点名称
	resources        []cases.Resource
	deletedNodes     map[string]struct{} // 保存被删除的节点
	originalReplicas int
	createdNodeGroup bool // 标记是否是我们创建的节点组
}

// NewCheckNodeScaleResources - 测试案例
func NewCheckNodeScaleResources(ctx context.Context) cases.Interface {
	return &checkNodeScaleResources{
		nodeSnapshot: make(map[string]struct{}),
		deletedNodes: make(map[string]struct{}),
	}
}

func (c *checkNodeScaleResources) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base is nil")
	}

	c.base = base
	return nil
}

func (c *checkNodeScaleResources) Name() cases.CaseName {
	return CheckNodeScaleResourcesCaseName
}

func (c *checkNodeScaleResources) Desc() string {
	return "测试节点扩缩容后的相关资源状态"
}

// scaleInstanceGroup 对节点组进行扩缩容操作
func (c *checkNodeScaleResources) scaleInstanceGroup(ctx context.Context, instanceGroupID string, replicas int) error {
	// 先获取节点组的当前状态
	ig, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, instanceGroupID, nil)
	if err != nil {
		return fmt.Errorf("获取节点组信息失败: %v", err)
	}

	currentReplicas := ig.InstanceGroup.Spec.Replicas
	if currentReplicas == replicas {
		logger.Infof(ctx, "节点组 %s 当前副本数已经是目标值 %d，无需调整", instanceGroupID, replicas)
		return nil
	}

	// 获取节点组当前的实例列表
	instancesResp, err := c.base.CCEClient.ListInstancesByInstanceGroupID(ctx, c.base.ClusterID, instanceGroupID, 0, 0, nil)
	if err != nil {
		logger.Warnf(ctx, "获取节点组实例列表失败: %v", err)
	} else {
		logger.Infof(ctx, "节点组 %s 当前有 %d 个实例", instanceGroupID, len(instancesResp.Page.List))
		for i, instance := range instancesResp.Page.List {
			if i < 5 { // 只显示前5个实例信息
				logger.Infof(ctx, "  实例 #%d: ID=%s, 名称=%s, 状态=%s",
					i+1, instance.Spec.CCEInstanceID, instance.Spec.InstanceName, instance.Status.InstancePhase)
			} else {
				logger.Infof(ctx, "  还有 %d 个实例未显示...", len(instancesResp.Page.List)-5)
				break
			}
		}
	}

	// 构建扩缩容请求
	request := &ccev2.UpdateInstanceGroupReplicasRequest{
		Replicas: replicas,
	}

	operation := "扩容"
	if replicas < currentReplicas {
		operation = "缩容"
	}

	logger.Infof(ctx, "开始执行节点组 %s %s操作: %d -> %d", instanceGroupID, operation, currentReplicas, replicas)

	// 发送请求
	maxRetries := 3
	retryInterval := 5 * time.Second
	var resp *ccev2.UpdateInstanceGroupReplicasResponse

	// 使用重试逻辑发送扩缩容请求
	for i := 0; i < maxRetries; i++ {
		resp, err = c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, c.base.ClusterID, instanceGroupID, request, nil)
		if err == nil {
			break
		}

		if i < maxRetries-1 {
			logger.Warnf(ctx, "更新节点组副本数失败(重试 %d/%d): %v", i+1, maxRetries, err)
			time.Sleep(retryInterval)
		}
	}

	if err != nil {
		return fmt.Errorf("更新节点组副本数失败: %v", err)
	}

	logger.Infof(ctx, "节点组 %s %s请求发送成功，RequestID: %s", instanceGroupID, operation, resp.RequestID)

	// 再次获取节点组状态以确认请求已被接受
	time.Sleep(2 * time.Second)

	ig, err = c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, instanceGroupID, nil)
	if err != nil {
		logger.Warnf(ctx, "获取更新后的节点组信息失败: %v", err)
	} else {
		if ig.InstanceGroup.Spec.Replicas == replicas {
			logger.Infof(ctx, "节点组 %s 目标副本数已更新为 %d", instanceGroupID, replicas)
		} else {
			logger.Warnf(ctx, "节点组 %s 目标副本数未更新，当前仍为 %d，期望为 %d",
				instanceGroupID, ig.InstanceGroup.Spec.Replicas, replicas)
		}

		// 显示节点组当前的扩缩容状态
		logger.Infof(ctx, "节点组 %s 当前状态: ", instanceGroupID)
		logger.Infof(ctx, "  期望副本数: %d", ig.InstanceGroup.Spec.Replicas)
		logger.Infof(ctx, "  就绪副本数: %d", ig.InstanceGroup.Status.ReadyReplicas)
		logger.Infof(ctx, "  扩缩容中副本数: %d", ig.InstanceGroup.Status.ScalingReplicas)
		logger.Infof(ctx, "  删除中副本数: %d", ig.InstanceGroup.Status.DeletingReplicas)
	}

	return nil
}

// waitForNodeScaleComplete 等待节点扩缩容操作完成
func (c *checkNodeScaleResources) waitForNodeScaleComplete(ctx context.Context, targetReplicas int) error {
	logger.Infof(ctx, "开始等待节点组副本数达到目标值: %d", targetReplicas)

	// 创建一个定时器用于检查状态
	statusCheckTicker := time.NewTicker(10 * time.Second) // 每10秒检查一次状态
	defer statusCheckTicker.Stop()

	timeout := 15 * time.Minute
	startTime := time.Now()
	endTime := startTime.Add(timeout)

	// 定义一个变量用于跟踪节点状态变化
	previousNodeCount := c.nodeCount // 初始化为记录的初始节点数量，而不是0
	var instanceGroupStatus string
	var previousStatus string

	// 记录当前所有节点的集合
	currentNodes := make(map[string]struct{})
	previousNodes := make(map[string]struct{})

	// 使用for range代替for { select {} }
	for range statusCheckTicker.C {
		if time.Now().After(endTime) {
			return fmt.Errorf("等待节点组扩缩容超时（%v）", timeout)
		}

		// 1. 获取当前节点列表
		nodes, err := c.base.KubeClient.ListNode(ctx, &kube.ListOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取节点列表失败: %v, 将在10秒后重试", err)
			continue
		}

		// 记录节点数量变化
		currentNodeCount := len(nodes.Items)
		if currentNodeCount != previousNodeCount {
			logger.Infof(ctx, "节点数量变化: %d -> %d (当前节点: %v)",
				previousNodeCount,
				currentNodeCount,
				func() []string {
					names := make([]string, 0, len(nodes.Items))
					for _, node := range nodes.Items {
						names = append(names, node.Name)
					}
					return names
				}())
			previousNodeCount = currentNodeCount
		}

		// 更新当前节点集合
		for _, node := range nodes.Items {
			currentNodes[node.Name] = struct{}{}
		}

		// 如果是第一次循环，初始化previousNodes
		if len(previousNodes) == 0 {
			previousNodes = currentNodes
			continue
		}

		// 找出新增的节点
		newNodes := make(map[string]struct{})
		for nodeName := range currentNodes {
			if _, exists := previousNodes[nodeName]; !exists {
				newNodes[nodeName] = struct{}{}
			}
		}

		// 找出被删除的节点
		deletedNodes := make(map[string]struct{})
		for nodeName := range previousNodes {
			if _, exists := currentNodes[nodeName]; !exists {
				deletedNodes[nodeName] = struct{}{}
			}
		}

		// 2. 检查节点组状态
		instanceGroup, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroup, nil)
		if err != nil {
			logger.Warnf(ctx, "获取节点组状态失败: %v, 将在10秒后重试", err)
			continue
		}

		// 获取节点组当前节点数量并输出详细信息
		logger.Infof(ctx, "节点组状态: 期望副本数=%d, 实际副本数=%d, 就绪副本数=%d",
			targetReplicas,
			instanceGroup.InstanceGroup.Status.ActualReplicas,
			instanceGroup.InstanceGroup.Status.ReadyReplicas)

		// 输出节点变化信息
		if len(newNodes) > 0 {
			nodeNames := make([]string, 0, len(newNodes))
			for nodeName := range newNodes {
				nodeNames = append(nodeNames, nodeName)
			}
			logger.Infof(ctx, "新增节点: %v (集群总节点数: %d, 节点组节点数: %d)",
				nodeNames,
				currentNodeCount,
				instanceGroup.InstanceGroup.Status.ActualReplicas)
		}

		if len(deletedNodes) > 0 {
			nodeNames := make([]string, 0, len(deletedNodes))
			for nodeName := range deletedNodes {
				nodeNames = append(nodeNames, nodeName)
			}
			logger.Infof(ctx, "删除的节点: %v (集群总节点数: %d, 节点组节点数: %d)",
				nodeNames,
				currentNodeCount,
				instanceGroup.InstanceGroup.Status.ActualReplicas)
			// 保存被删除的节点，用于后续资源回收检查
			c.deletedNodes = deletedNodes
		}

		// 记录节点组状态变化
		var currentStatus string
		if instanceGroup.InstanceGroup.Status.Pause != nil && instanceGroup.InstanceGroup.Status.Pause.Paused {
			currentStatus = "Paused"
		} else if instanceGroup.InstanceGroup.Status.ReadyReplicas == instanceGroup.InstanceGroup.Status.ActualReplicas {
			currentStatus = "Ready"
		} else if instanceGroup.InstanceGroup.Status.ScalingReplicas > 0 {
			currentStatus = "Scaling"
		} else if instanceGroup.InstanceGroup.Status.DeletingReplicas > 0 {
			currentStatus = "Deleting"
		} else if instanceGroup.InstanceGroup.Status.NotReadyReplicas > 0 {
			currentStatus = "NotReady"
		} else {
			currentStatus = "Unknown"
		}

		if currentStatus != previousStatus {
			logger.Infof(ctx, "节点组状态变化: %s -> %s", previousStatus, currentStatus)
			previousStatus = currentStatus
		}
		instanceGroupStatus = currentStatus

		// 3. 检查所有新增节点是否就绪
		allNewNodesReady := true
		notReadyNewNodes := []string{}

		for _, node := range nodes.Items {
			// 只检查新增的节点
			if _, isNewNode := newNodes[node.Name]; !isNewNode {
				continue
			}

			isReady := false
			for _, condition := range node.Status.Conditions {
				if condition.Type == corev1.NodeNetworkUnavailable && condition.Status == corev1.ConditionFalse {
					isReady = true
					break
				}
			}

			if !isReady {
				allNewNodesReady = false
				notReadyNewNodes = append(notReadyNewNodes, node.Name)
			}
		}

		if !allNewNodesReady && len(notReadyNewNodes) > 0 {
			logger.Infof(ctx, "有%d个新增节点尚未准备就绪: %v", len(notReadyNewNodes), notReadyNewNodes)
			continue
		}

		// 4. 判断节点组扩缩容是否完成
		// 扩容情况：新增节点数 >= 目标增加的副本数，且节点就绪
		// 缩容情况：当节点组状态为Ready即可
		initialNodeCount := c.nodeCount
		expectedNewNodeCount := targetReplicas - initialNodeCount

		// 快速路径：如果节点组状态为Ready且就绪副本数等于期望副本数，立即判定为完成
		if instanceGroupStatus == "Ready" && instanceGroup.InstanceGroup.Status.ReadyReplicas == targetReplicas {
			elapsedTime := time.Since(startTime).Round(time.Second)
			logger.Infof(ctx, "节点组扩缩容完成，目标副本数: %d，当前就绪副本数: %d，耗时: %v",
				targetReplicas, instanceGroup.InstanceGroup.Status.ReadyReplicas, elapsedTime)
			return nil
		}

		// 常规判断路径
		if expectedNewNodeCount > 0 { // 扩容
			// 扩容时有两种完成情况：
			// 1. 检测到了新增节点并且已就绪
			if len(newNodes) >= expectedNewNodeCount && allNewNodesReady && instanceGroupStatus == "Ready" {
				elapsedTime := time.Since(startTime).Round(time.Second)
				logger.Infof(ctx, "节点扩容完成，目标副本数: %d，当前节点数: %d，耗时: %v",
					targetReplicas, len(nodes.Items), elapsedTime)
				return nil
			}
		} else { // 缩容
			// 缩容时完成情况：
			// 1. 检测到了删除的节点且节点组状态为Ready
			if instanceGroupStatus == "Ready" && len(deletedNodes) > 0 {
				elapsedTime := time.Since(startTime).Round(time.Second)
				logger.Infof(ctx, "节点缩容完成，目标副本数: %d，当前节点数: %d，耗时: %v",
					targetReplicas, len(nodes.Items), elapsedTime)
				return nil
			}
		}

		// 更新previousNodes为当前节点集合
		previousNodes = currentNodes

		logger.Infof(ctx, "等待节点组就绪: 期望副本数=%d, 当前状态=[实际副本数=%d, 就绪副本数=%d, 扩缩容中=%d, 删除中=%d], 节点组状态=%s",
			targetReplicas,
			instanceGroup.InstanceGroup.Status.ActualReplicas,
			instanceGroup.InstanceGroup.Status.ReadyReplicas,
			instanceGroup.InstanceGroup.Status.ScalingReplicas,
			instanceGroup.InstanceGroup.Status.DeletingReplicas,
			instanceGroupStatus)
	}

	// 如果循环退出但未返回则说明出现异常
	return fmt.Errorf("等待节点组扩缩容异常退出")
}

// Check 执行测试
func (c *checkNodeScaleResources) Check(ctx context.Context) ([]cases.Resource, error) {
	mode := c.base.ClusterSpec.ContainerNetworkConfig.Mode
	logger.Infof(ctx, "当前集群网络模式: %s", mode)

	switch mode {
	case "vpc-eni", "vpc-exclusive-eni":
		// 原有ENI扩缩容逻辑
		return c.checkENIMode(ctx)
	case "vpc-route", "vpc-route-veth", "vpc-route-ipvlan", "vpc-route-auto-detect":
		// 新增：VPC路由模式扩缩容与实例路由校验
		return c.checkRouteMode(ctx)
	default:
		return nil, fmt.Errorf("不支持的网络模式: %s", mode)
	}
}

// checkENIMode 保留原有ENI扩缩容逻辑
func (c *checkNodeScaleResources) checkENIMode(ctx context.Context) ([]cases.Resource, error) {
	// 显示测试开始信息
	logger.Infof(ctx, "==== 开始执行节点扩缩容资源状态测试 ====")
	logger.Infof(ctx, "测试目标:")
	logger.Infof(ctx, "1. 验证节点扩容后NRS和ENI资源的创建")
	logger.Infof(ctx, "2. 验证手动添加ENI不会被CCE纳管")
	logger.Infof(ctx, "3. 验证节点缩容后NRS和ENI资源的回收")
	startTime := time.Now()

	// 0. 创建测试用的节点组
	logger.Infof(ctx, "\n===== 阶段0: 创建测试用的节点组 =====")
	if err := c.createInstanceGroup(ctx); err != nil {
		return nil, fmt.Errorf("创建测试用节点组失败: %v", err)
	}

	// 1. 记录当前节点状态
	logger.Infof(ctx, "\n===== 阶段1: 记录当前集群节点状态 =====")
	if err := c.recordCurrentNodeState(ctx); err != nil {
		return nil, fmt.Errorf("记录当前节点状态失败: %v", err)
	}

	// 2. 获取可用的节点组ID
	logger.Infof(ctx, "\n===== 阶段2: 获取集群节点组信息 =====")
	instanceGroupID, err := c.getInstanceGroupID(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取节点组ID失败: %v", err)
	}
	c.instanceGroup = instanceGroupID
	logger.Infof(ctx, "将使用节点组 %s 进行扩缩容测试", instanceGroupID)

	// 3. 获取节点组当前的副本数
	originalReplicas, err := c.getInstanceGroupReplicas(ctx, instanceGroupID)
	if err != nil {
		return nil, fmt.Errorf("获取节点组副本数失败: %v", err)
	}
	logger.Infof(ctx, "节点组 %s 当前副本数: %d", instanceGroupID, originalReplicas)
	c.originalReplicas = originalReplicas // 保存原始副本数

	// 4. 保存当前节点资源到资源列表中
	c.resources = append(c.resources, cases.Resource{
		CaseName: CheckNodeScaleResourcesCaseName,
		Type:     cases.ResourceTypeCCEInstanceGroup,
		ID:       instanceGroupID,
	})

	// 5. 执行节点组扩容操作
	logger.Infof(ctx, "\n===== 阶段3: 执行节点组扩容操作 =====")
	newReplicas := originalReplicas + 1 // 只扩容1个节点
	logger.Infof(ctx, "开始对节点组 %s 进行扩容操作，副本数从 %d -> %d", instanceGroupID, originalReplicas, newReplicas)
	if err := c.scaleInstanceGroup(ctx, instanceGroupID, newReplicas); err != nil {
		return c.resources, fmt.Errorf("扩容节点组失败: %v", err)
	}

	// 6. 等待扩容完成
	logger.Infof(ctx, "\n===== 阶段4: 等待节点扩容完成 =====")
	logger.Infof(ctx, "等待节点扩容完成...")
	time.Sleep(30 * time.Second) // 等待一段时间，确保扩容操作开始
	if err := c.waitForNodeScaleComplete(ctx, newReplicas); err != nil {
		return c.resources, fmt.Errorf("等待节点扩容完成失败: %v", err)
	}

	// 7. 获取新节点
	logger.Infof(ctx, "\n===== 阶段5: 获取新扩容的节点信息 =====")
	newNode, err := c.getNewNode(ctx)
	if err != nil {
		return c.resources, fmt.Errorf("获取新节点失败: %v", err)
	}
	logger.Infof(ctx, "新扩容的节点: %s", newNode)

	// 8. 检查节点扩容后的资源状态
	logger.Infof(ctx, "\n===== 阶段6: 检查节点扩容后的资源状态 =====")
	logger.Infof(ctx, "开始检查节点扩容后的资源状态...")
	if err := c.checkNodeResources(ctx, newNode); err != nil {
		return c.resources, fmt.Errorf("检查节点扩容后的资源状态失败: %v", err)
	}

	// 9. 手动添加ENI测试
	logger.Infof(ctx, "\n===== 阶段7: 测试手动添加ENI的管理状态 =====")
	logger.Infof(ctx, "测试手动添加的ENI是否会被CCE纳管...")
	if err := c.checkManualAddedENI(ctx, newNode); err != nil {
		return c.resources, fmt.Errorf("测试手动添加ENI失败: %v", err)
	}

	// 10. 执行节点组缩容操作
	logger.Infof(ctx, "\n===== 阶段8: 执行节点组缩容操作 =====")
	logger.Infof(ctx, "开始对节点组 %s 进行缩容操作，副本数从 %d -> %d", instanceGroupID, newReplicas, originalReplicas)
	if err := c.scaleInstanceGroup(ctx, instanceGroupID, originalReplicas); err != nil {
		return c.resources, fmt.Errorf("缩容节点组失败: %v", err)
	}

	// 11. 等待缩容完成
	logger.Infof(ctx, "\n===== 阶段9: 等待节点缩容完成 =====")
	logger.Infof(ctx, "等待节点缩容完成...")
	time.Sleep(30 * time.Second) // 等待一段时间，确保缩容操作开始
	if err := c.waitForNodeScaleComplete(ctx, originalReplicas); err != nil {
		return c.resources, fmt.Errorf("等待节点缩容完成失败: %v", err)
	}

	// 12. 检查资源回收情况
	logger.Infof(ctx, "\n===== 阶段10: 检查资源回收情况 =====")
	logger.Infof(ctx, "检查节点 %s 相关资源是否被正确回收...", newNode)
	if err := c.checkResourceRecycling(ctx); err != nil {
		return c.resources, fmt.Errorf("检查资源回收失败: %v", err)
	}

	// 显示测试完成信息
	totalTime := time.Since(startTime).Round(time.Second)
	logger.Infof(ctx, "\n===== 节点扩缩容资源状态测试完成 =====")
	logger.Infof(ctx, "测试总耗时: %v", totalTime)
	logger.Infof(ctx, "测试结果: 通过")

	return c.resources, nil
}

// checkRouteMode 实现VPC路由模式下的节点扩缩容与实例路由校验
func (c *checkNodeScaleResources) checkRouteMode(ctx context.Context) ([]cases.Resource, error) {
	logger.Infof(ctx, "进入VPC路由模式扩缩容测试流程")
	// 记录当前节点
	if err := c.recordCurrentNodeState(ctx); err != nil {
		return nil, fmt.Errorf("记录当前节点状态失败: %v", err)
	}

	// 1. 获取可用节点组ID
	instanceGroupID, err := c.getInstanceGroupID(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取节点组ID失败: %v", err)
	}
	logger.Infof(ctx, "将使用节点组 %s 进行扩缩容测试", instanceGroupID)
	c.instanceGroup = instanceGroupID

	// 2. 获取当前节点组副本数
	oldNodeCount, err := c.getInstanceGroupReplicas(ctx, instanceGroupID)
	if err != nil {
		return nil, fmt.Errorf("获取节点组副本数失败: %v", err)
	}

	// 3. 扩容节点组（+1）
	newReplicas := oldNodeCount + 1
	logger.Infof(ctx, "扩容节点组 %s，副本数从 %d -> %d", instanceGroupID, oldNodeCount, newReplicas)
	if err := c.scaleInstanceGroup(ctx, instanceGroupID, newReplicas); err != nil {
		return nil, fmt.Errorf("扩容节点组失败: %v", err)
	}
	logger.Infof(ctx, "扩容请求已发送，等待节点扩容完成...")
	if err := c.waitForNodeScaleComplete(ctx, newReplicas); err != nil {
		return nil, fmt.Errorf("等待节点扩容完成失败: %v", err)
	}
	logger.Infof(ctx, "新节点已Ready")

	// 4. 获取新扩容节点名称
	newNodeName, err := c.getNewNode(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取新扩容节点失败: %v", err)
	}
	logger.Infof(ctx, "新扩容节点名称: %s", newNodeName)

	// 5. 获取新节点详细信息
	nodeObj, err := c.base.KubeClient.GetNode(ctx, newNodeName, &kube.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取新节点详细信息失败: %v", err)
	}

	// 6. 查询集群信息获取VPCID
	cluster, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		return nil, fmt.Errorf("获取集群信息失败: %v", err)
	}
	vpcID := cluster.Cluster.Spec.VPCID
	logger.Infof(ctx, "集群VPCID: %s", vpcID)

	// 7. 获取云主机实例ID（ProviderID字段，需去除前缀）
	instanceID := nodeObj.Spec.ProviderID
	if instanceID == "" {
		return nil, fmt.Errorf("新节点ProviderID为空，无法查询实例路由")
	}
	// 兼容格式：providerID形如 'bcc://i-xxxx'，只取后缀
	if idx := len("cce://"); len(instanceID) > idx && instanceID[:idx] == "cce://" {
		instanceID = instanceID[idx:]
	}
	logger.Infof(ctx, "新节点云主机实例ID: %s", instanceID)

	// 8. 查询VPC实例路由，确认新节点路由已添加
	routes, err := c.base.VPCClient.ListRouteTable(ctx, &vpc.ListRouteArgs{
		VpcID: vpcID,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("查询VPC实例路由失败: %v", err)
	}
	found := false
	for _, route := range routes {
		if route.NexthopID == instanceID {
			found = true
			break
		}
	}
	if !found {
		return nil, fmt.Errorf("扩容后未查询到新节点的实例路由")
	}
	logger.Infof(ctx, "扩容后实例路由已添加")

	// 9. 缩容节点组（-1）
	logger.Infof(ctx, "缩容节点组 %s，副本数从 %d -> %d", instanceGroupID, newReplicas, oldNodeCount)
	if err := c.scaleInstanceGroup(ctx, instanceGroupID, oldNodeCount); err != nil {
		return nil, fmt.Errorf("缩容节点组失败: %v", err)
	}
	logger.Infof(ctx, "缩容请求已发送，等待节点缩容完成...")
	if err := c.waitForNodeScaleComplete(ctx, oldNodeCount); err != nil {
		return nil, fmt.Errorf("等待节点缩容完成失败: %v", err)
	}
	logger.Infof(ctx, "缩容后节点已Ready")

	// 10. 再次查询VPC实例路由，确认新节点路由已删除
	routesAfter, err := c.base.VPCClient.ListRouteTable(ctx, &vpc.ListRouteArgs{
		VpcID: vpcID,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("缩容后查询VPC实例路由失败: %v", err)
	}
	for _, route := range routesAfter {
		if route.NexthopID == instanceID {
			return nil, fmt.Errorf("缩容后新节点的实例路由未被删除")
		}
	}
	logger.Infof(ctx, "缩容后实例路由已删除")

	return nil, nil
}

// createInstanceGroup 创建测试用的节点组
func (c *checkNodeScaleResources) createInstanceGroup(ctx context.Context) error {
	// 先检查集群中是否已有节点组
	resp, err := c.base.CCEClient.ListInstanceGroups(ctx, c.base.ClusterID, nil, nil)
	if err != nil {
		return fmt.Errorf("获取节点组列表失败: %v", err)
	}

	// 如果已有节点组，则无需创建
	if len(resp.Page.List) > 0 {
		for _, ig := range resp.Page.List {
			if ig.Status.ReadyReplicas > 0 {
				logger.Infof(ctx, "集群中已有可用节点组 %s (副本数: %d)，无需创建新节点组",
					ig.Spec.CCEInstanceGroupID, ig.Status.ReadyReplicas)
				c.instanceGroup = ig.Spec.CCEInstanceGroupID
				c.createdNodeGroup = false

				// 记录原始副本数用于清理恢复
				c.originalReplicas = ig.Spec.Replicas
				logger.Infof(ctx, "记录原始节点组副本数: %d", c.originalReplicas)

				return nil
			}
		}
	}

	// 需要创建新节点组
	logger.Infof(ctx, "未找到可用节点组，开始创建测试用节点组...")

	// 获取集群中已有节点的信息，用于复用配置
	instances, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
		ClusterRole: types.ClusterRoleNode,
	}, nil)
	if err != nil {
		return fmt.Errorf("获取集群节点信息失败: %v", err)
	}
	if len(instances.InstancePage.InstanceList) == 0 {
		return fmt.Errorf("集群中没有可用的节点，无法获取配置信息")
	}

	// 使用第一个节点的配置信息
	instance := instances.InstancePage.InstanceList[0]

	// 获取镜像ID
	imageID := instance.Spec.ImageID
	if imageID == "" {
		return fmt.Errorf("获取到的镜像ID为空，无法创建节点组")
	}
	logger.Infof(ctx, "将使用已有节点的镜像ID: %s", imageID)

	// 获取集群额外信息，找到可用的子网
	clusterExtraInfo, err := c.base.CCEHostClient.GetClusterExtraInfo(ctx, c.base.ClusterID, nil)
	if err != nil {
		return fmt.Errorf("获取集群额外信息失败: %v", err)
	}

	var nodeSubnetID string
	for _, subnet := range clusterExtraInfo.Subnets {
		if subnet.SubnetCIDR != "" {
			nodeSubnetID = subnet.SubnetID
			break
		}
	}

	if nodeSubnetID == "" {
		return fmt.Errorf("未找到可用的子网")
	}

	logger.Infof(ctx, "将使用子网 %s 创建节点组", nodeSubnetID)

	// 查询集群信息
	cluster, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		return fmt.Errorf("获取集群信息失败: %v", err)
	}

	// 从集群信息获取VPCID
	vpcID := cluster.Cluster.Spec.VPCID
	logger.Infof(ctx, "将使用VPC %s 创建节点组", vpcID)

	// 创建节点组
	igName := fmt.Sprintf("test-node-scale-%d", time.Now().Unix())
	createReq := &ccev2.CreateInstanceGroupRequest{
		InstanceGroupSpec: types.InstanceGroupSpec{
			InstanceGroupName: igName,
			Replicas:          0, // 创建0个节点的节点组，避免不必要的节点创建
			InstanceTemplate: types.InstanceTemplate{
				InstanceSpec: types.InstanceSpec{
					ClusterRole:  types.ClusterRoleNode,
					MachineType:  instance.Spec.MachineType,
					InstanceType: instance.Spec.InstanceType,
					VPCConfig: types.VPCConfig{
						VPCID:       vpcID,
						VPCSubnetID: nodeSubnetID,
						SecurityGroup: types.SecurityGroup{
							EnableCCERequiredSecurityGroup: true,
						},
					},
					InstanceResource: types.InstanceResource{
						CPU: instance.Spec.InstanceResource.CPU,
						MEM: instance.Spec.InstanceResource.MEM,
					},
					ImageID:       imageID,
					AdminPassword: "Test123456!",
				},
			},
		},
	}

	// 发送创建请求
	createResp, err := c.base.CCEClient.CreateInstanceGroup(ctx, c.base.ClusterID, createReq, nil)
	if err != nil {
		return fmt.Errorf("创建节点组失败: %v", err)
	}

	// 保存节点组ID
	c.instanceGroup = createResp.InstanceGroupID
	c.createdNodeGroup = true
	logger.Infof(ctx, "成功创建节点组 %s (ID: %s)", igName, c.instanceGroup)

	// 等待节点组就绪
	logger.Infof(ctx, "等待节点组中的节点就绪...")

	// 创建instanceGroup资源对象并等待就绪
	ig, err := resource.NewInstanceGroup(ctx, c.base, c.instanceGroup, 10*time.Second, 15*time.Minute)
	if err != nil {
		return fmt.Errorf("创建instanceGroup资源对象失败: %v", err)
	}

	if err := ig.CheckResource(ctx); err != nil {
		return fmt.Errorf("等待节点组就绪失败: %v", err)
	}

	logger.Infof(ctx, "节点组 %s 已就绪", c.instanceGroup)

	// 将创建的节点组添加到资源列表
	c.resources = append(c.resources, cases.Resource{
		CaseName: CheckNodeScaleResourcesCaseName,
		Type:     cases.ResourceTypeCCEInstanceGroup,
		ID:       c.instanceGroup,
	})

	return nil
}

// recordCurrentNodeState 记录当前集群的节点状态
func (c *checkNodeScaleResources) recordCurrentNodeState(ctx context.Context) error {
	maxRetries := 3
	retryInterval := 5 * time.Second
	var nodes *corev1.NodeList
	var err error

	// 使用重试逻辑获取节点列表
	for i := 0; i < maxRetries; i++ {
		nodes, err = c.base.KubeClient.ListNode(ctx, &kube.ListOptions{})
		if err == nil {
			break
		}
		logger.Warnf(ctx, "获取节点列表失败(重试 %d/%d): %v", i+1, maxRetries, err)
		if i < maxRetries-1 {
			time.Sleep(retryInterval)
		}
	}

	if err != nil {
		return fmt.Errorf("获取节点列表失败: %v", err)
	}

	c.nodeCount = len(nodes.Items)
	logger.Infof(ctx, "当前集群节点数量: %d", c.nodeCount)

	// 记录当前所有节点名称到快照中，并检查节点状态
	readyNodes := 0
	notReadyNodes := 0

	for _, node := range nodes.Items {
		c.nodeSnapshot[node.Name] = struct{}{}

		// 检查节点是否就绪
		isReady := false
		for _, condition := range node.Status.Conditions {
			if condition.Type == corev1.NodeNetworkUnavailable && condition.Status == corev1.ConditionFalse {
				isReady = true
				readyNodes++
				break
			}
		}

		if !isReady {
			notReadyNodes++
			logger.Warnf(ctx, "节点 %s 当前未就绪", node.Name)
		} else {
			logger.Infof(ctx, "节点 %s 已就绪", node.Name)
		}
	}

	logger.Infof(ctx, "当前集群节点状态: 总数=%d, 就绪=%d, 未就绪=%d", c.nodeCount, readyNodes, notReadyNodes)

	if notReadyNodes > 0 {
		logger.Warnf(ctx, "集群中有 %d 个节点未就绪，这可能会影响测试结果", notReadyNodes)
	}

	return nil
}

// getInstanceGroupID 获取可用的节点组ID
func (c *checkNodeScaleResources) getInstanceGroupID(ctx context.Context) (string, error) {
	maxRetries := 3
	retryInterval := 5 * time.Second
	var resp *ccev2.ListInstanceGroupResponse
	var err error

	// 使用重试逻辑获取节点组列表
	for i := 0; i < maxRetries; i++ {
		resp, err = c.base.CCEClient.ListInstanceGroups(ctx, c.base.ClusterID, nil, nil)
		if err == nil {
			break
		}
		logger.Warnf(ctx, "获取节点组列表失败(重试 %d/%d): %v", i+1, maxRetries, err)
		if i < maxRetries-1 {
			time.Sleep(retryInterval)
		}
	}

	if err != nil {
		return "", fmt.Errorf("获取节点组列表失败: %v", err)
	}

	if len(resp.Page.List) == 0 {
		return "", fmt.Errorf("集群中没有可用的节点组")
	}

	// 打印所有可用的节点组信息，并选择一个合适的节点组
	logger.Infof(ctx, "集群中有 %d 个节点组", len(resp.Page.List))

	var selectedGroupID string
	var maxReplicas int = -1

	for i, ig := range resp.Page.List {
		logger.Infof(ctx, "节点组 #%d: ID=%s, 名称=%s, 副本数=%d",
			i+1, ig.Spec.CCEInstanceGroupID, ig.Spec.InstanceGroupName, ig.Spec.Replicas)

		// 优先选择有副本数的节点组
		if ig.Spec.Replicas > maxReplicas {
			maxReplicas = ig.Spec.Replicas
			selectedGroupID = ig.Spec.CCEInstanceGroupID
		}
	}

	// 如果没有找到有副本的节点组，则使用第一个
	if selectedGroupID == "" {
		selectedGroupID = resp.Page.List[0].Spec.CCEInstanceGroupID
	}

	logger.Infof(ctx, "选择节点组 %s 进行测试，当前副本数: %d", selectedGroupID, maxReplicas)

	return selectedGroupID, nil
}

// getInstanceGroupReplicas 获取节点组的副本数
func (c *checkNodeScaleResources) getInstanceGroupReplicas(ctx context.Context, instanceGroupID string) (int, error) {
	maxRetries := 3
	retryInterval := 5 * time.Second
	var resp *ccev2.GetInstanceGroupResponse
	var err error

	// 使用重试逻辑获取节点组信息
	for i := 0; i < maxRetries; i++ {
		resp, err = c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, instanceGroupID, nil)
		if err == nil {
			break
		}
		logger.Warnf(ctx, "获取节点组信息失败(重试 %d/%d): %v", i+1, maxRetries, err)
		if i < maxRetries-1 {
			time.Sleep(retryInterval)
		}
	}

	if err != nil {
		return 0, fmt.Errorf("获取节点组信息失败: %v", err)
	}

	logger.Infof(ctx, "节点组 %s 详情:", instanceGroupID)
	logger.Infof(ctx, "  名称: %s", resp.InstanceGroup.Spec.InstanceGroupName)
	logger.Infof(ctx, "  期望副本数: %d", resp.InstanceGroup.Spec.Replicas)
	logger.Infof(ctx, "  就绪副本数: %d", resp.InstanceGroup.Status.ReadyReplicas)
	logger.Infof(ctx, "  自动扩缩容: %v", resp.InstanceGroup.Spec.ClusterAutoscalerSpec != nil && resp.InstanceGroup.Spec.ClusterAutoscalerSpec.Enabled)

	return resp.InstanceGroup.Spec.Replicas, nil
}

// getNewNode 获取扩容后新增加的节点
func (c *checkNodeScaleResources) getNewNode(ctx context.Context) (string, error) {
	maxRetries := 3
	retryInterval := 5 * time.Second
	var nodes *corev1.NodeList
	var err error

	// 使用重试逻辑获取节点列表
	for i := 0; i < maxRetries; i++ {
		nodes, err = c.base.KubeClient.ListNode(ctx, &kube.ListOptions{})
		if err == nil {
			break
		}
		logger.Warnf(ctx, "获取节点列表失败(重试 %d/%d): %v", i+1, maxRetries, err)
		if i < maxRetries-1 {
			time.Sleep(retryInterval)
		}
	}

	if err != nil {
		return "", fmt.Errorf("获取节点列表失败: %v", err)
	}

	// 查找不在快照中的新节点
	var newNodes []string
	for _, node := range nodes.Items {
		if _, exists := c.nodeSnapshot[node.Name]; !exists {
			newNodes = append(newNodes, node.Name)
		}
	}

	if len(newNodes) == 0 {
		return "", fmt.Errorf("未找到新扩容的节点")
	}

	if len(newNodes) > 1 {
		logger.Warnf(ctx, "找到多个新节点: %v，将使用第一个节点进行测试", newNodes)
	}

	selectedNode := newNodes[0]

	// 检查新节点的信息
	for _, node := range nodes.Items {
		if node.Name == selectedNode {
			isReady := false
			for _, condition := range node.Status.Conditions {
				if condition.Type == corev1.NodeNetworkUnavailable && condition.Status == corev1.ConditionFalse {
					isReady = true
					break
				}
			}

			logger.Infof(ctx, "选定的新节点 %s 信息:", node.Name)
			logger.Infof(ctx, "  是否就绪: %v", isReady)
			logger.Infof(ctx, "  标签数量: %d", len(node.Labels))
			logger.Infof(ctx, "  地址数量: %d", len(node.Status.Addresses))
			logger.Infof(ctx, "  分配的CPU: %s", node.Status.Capacity.Cpu().String())
			logger.Infof(ctx, "  分配的内存: %s", node.Status.Capacity.Memory().String())

			// 打印节点的主要IP地址
			for _, addr := range node.Status.Addresses {
				if addr.Type == corev1.NodeInternalIP {
					logger.Infof(ctx, "  内部IP: %s", addr.Address)
				} else if addr.Type == corev1.NodeHostName {
					logger.Infof(ctx, "  主机名: %s", addr.Address)
				}
			}

			break
		}
	}

	return selectedNode, nil
}

// checkNodeResources 检查节点扩容后的资源状态
func (c *checkNodeScaleResources) checkNodeResources(ctx context.Context, nodeName string) error {
	maxRetries := 3
	retryInterval := 5 * time.Second

	// 1. 检查NRS资源
	logger.Infof(ctx, "检查节点 %s 对应的NRS资源...", nodeName)
	var nrs *types.NetworkResourceSet
	var err error

	// 使用重试逻辑获取NRS资源
	for i := 0; i < maxRetries; i++ {
		nrs, err = c.base.KubeClient.GetNrs(ctx, nodeName, &kube.GetOptions{})
		if err == nil {
			break
		}
		logger.Warnf(ctx, "获取节点 %s 的NRS资源失败(重试 %d/%d): %v", nodeName, i+1, maxRetries, err)
		if i < maxRetries-1 {
			time.Sleep(retryInterval)
		}
	}

	if err != nil {
		return fmt.Errorf("获取节点 %s 的NRS资源失败: %v", nodeName, err)
	}

	logger.Infof(ctx, "节点 %s 对应的NRS资源存在", nodeName)
	logger.Infof(ctx, "NRS 名称: %s", nrs.Name)
	logger.Infof(ctx, "NRS 实例ID: %s", nrs.Spec.InstanceId)
	logger.Infof(ctx, "NRS 安全组: %v", nrs.Spec.Eni.SecurityGroups)
	logger.Infof(ctx, "NRS 子网IDs: %v", nrs.Spec.Eni.SubnetIds)
	logger.Infof(ctx, "NRS 每个ENI的最大IP数: %d", nrs.Spec.Eni.MaxIPsPerENI)

	// 2. 检查ENI资源
	logger.Infof(ctx, "检查节点 %s 对应的ENI资源...", nodeName)
	var eniList *types.ENIList

	// 使用重试逻辑获取ENI资源列表
	for i := 0; i < maxRetries; i++ {
		eniList, err = c.base.KubeClient.ListENI(ctx, &kube.ListOptions{})
		if err == nil {
			break
		}
		logger.Warnf(ctx, "获取ENI资源列表失败(重试 %d/%d): %v", i+1, maxRetries, err)
		if i < maxRetries-1 {
			time.Sleep(retryInterval)
		}
	}

	if err != nil {
		return fmt.Errorf("获取ENI资源列表失败: %v", err)
	}

	// 过滤出属于该节点的ENI
	var nodeENIs []types.ENI
	for _, eni := range eniList.Items {
		if eni.Spec.NodeName == nodeName {
			nodeENIs = append(nodeENIs, eni)
		}
	}

	if len(nodeENIs) == 0 {
		logger.Warnf(ctx, "节点 %s 没有关联的ENI资源，可能创建还未完成", nodeName)
		// 等待一段时间后再次尝试获取
		time.Sleep(30 * time.Second)

		// 再次尝试获取ENI
		eniList, err = c.base.KubeClient.ListENI(ctx, &kube.ListOptions{})
		if err != nil {
			return fmt.Errorf("二次获取ENI资源列表失败: %v", err)
		}

		for _, eni := range eniList.Items {
			if eni.Spec.NodeName == nodeName {
				nodeENIs = append(nodeENIs, eni)
			}
		}

		if len(nodeENIs) == 0 {
			return fmt.Errorf("节点 %s 没有关联的ENI资源，即使等待后仍未找到", nodeName)
		}
	}

	logger.Infof(ctx, "节点 %s 关联的ENI资源数量: %d", nodeName, len(nodeENIs))
	for i, eni := range nodeENIs {
		logger.Infof(ctx, "ENI #%d:", i+1)
		logger.Infof(ctx, "  ID: %s", eni.Name)
		logger.Infof(ctx, "  MAC地址: %s", eni.Spec.MacAddress)
		logger.Infof(ctx, "  子网ID: %s", eni.Spec.SubnetID)
		logger.Infof(ctx, "  使用模式: %s", eni.Spec.UseMode)
		logger.Infof(ctx, "  CCE状态: %s", eni.Status.CCEStatus)
		logger.Infof(ctx, "  VPC状态: %s", eni.Status.VPCStatus)
		logger.Infof(ctx, "  私有IP数量: %d", len(eni.Spec.PrivateIPSet))

		// 打印更多的ENI详情
		if len(eni.Spec.PrivateIPSet) > 0 {
			logger.Infof(ctx, "  私有IP详情:")
			for j, ip := range eni.Spec.PrivateIPSet {
				logger.Infof(ctx, "    IP #%d: %s (主IP: %v)", j+1, ip.PrivateIPAddress, ip.Primary)
			}
		}
	}

	return nil
}

// checkManualAddedENI 测试手动添加的ENI不会被CCE自动纳管
func (c *checkNodeScaleResources) checkManualAddedENI(ctx context.Context, nodeName string) error {
	logger.Infof(ctx, "开始测试手动添加ENI的纳管情况...")

	// 1. 获取节点NRS资源，用于获取VPC、子网等信息
	nrs, err := c.base.KubeClient.GetNrs(ctx, nodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取节点 %s 的NRS资源失败: %v", nodeName, err)
	}

	logger.Infof(ctx, "成功获取节点 %s 的NRS资源，包含子网: %v", nodeName, nrs.Spec.Eni.SubnetIds)

	// 2. 获取节点详细信息
	node, err := c.base.KubeClient.GetNode(ctx, nodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取节点 %s 详细信息失败: %v", nodeName, err)
	}

	// 获取节点实例ID
	instanceID := ""
	for k, v := range node.Labels {
		if k == "cce.baidubce.com/instance-id" {
			instanceID = v
			break
		}
	}

	if instanceID == "" {
		logger.Warnf(ctx, "无法从节点标签中获取实例ID，尝试从NRS中获取")
		instanceID = nrs.Spec.InstanceId
	}

	if instanceID == "" {
		return fmt.Errorf("无法获取节点 %s 的实例ID", nodeName)
	}

	logger.Infof(ctx, "节点 %s 的实例ID为: %s", nodeName, instanceID)

	// 3. 检查ENIClient是否可用
	if c.base.ENIClient == nil {
		return fmt.Errorf("ENIClient未初始化，无法进行ENI测试")
	}

	// 4. 创建ENI
	logger.Infof(ctx, "开始手动创建ENI...")
	eniName := fmt.Sprintf("manual-eni-%s-%d", nodeName, time.Now().Unix())

	// 从NRS中获取参数信息
	securityGroups := nrs.Spec.Eni.SecurityGroups
	if len(securityGroups) == 0 {
		logger.Warnf(ctx, "节点 %s 的NRS中没有安全组信息，将使用默认安全组", nodeName)
		securityGroups = []string{"g-default"} // 使用默认安全组作为备选
	}

	// 获取所有子网信息并选择可用IP最多的子网
	selectedSubnetID := ""
	maxAvailableIPs := 0
	for _, currentSubnetID := range nrs.Spec.Eni.SubnetIds {
		// 获取子网信息
		subnet, err := c.base.KubeClient.GetSubnet(ctx, currentSubnetID, &kube.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取子网 %s 信息失败: %v", currentSubnetID, err)
			continue
		}

		logger.Infof(ctx, "子网 %s 详细信息:", currentSubnetID)
		logger.Infof(ctx, "  名称: %s", subnet.Spec.Name)
		logger.Infof(ctx, "  CIDR: %s", subnet.Spec.CIDR)
		logger.Infof(ctx, "  可用区: %s", subnet.Spec.AvailabilityZone)
		logger.Infof(ctx, "  是否启用: %v", subnet.Status.Enable)
		logger.Infof(ctx, "  可用IP数量: %d", subnet.Status.AvailableIPNum)
		logger.Infof(ctx, "  是否无可用IP: %v", subnet.Status.HasNoMoreIP)

		// 检查子网是否可用
		if !subnet.Status.Enable {
			logger.Warnf(ctx, "子网 %s 不可用，跳过", currentSubnetID)
			continue
		}

		// 检查子网是否有可用IP
		if subnet.Status.HasNoMoreIP {
			logger.Warnf(ctx, "子网 %s 没有可用IP，跳过", currentSubnetID)
			continue
		}

		// 获取可用IP数量
		availableIPs := subnet.Status.AvailableIPNum
		logger.Infof(ctx, "子网 %s 可用IP数量: %d", currentSubnetID, availableIPs)

		// 选择可用IP最多的子网
		if availableIPs > maxAvailableIPs {
			maxAvailableIPs = availableIPs
			selectedSubnetID = currentSubnetID
		}
	}

	if selectedSubnetID == "" {
		logger.Warnf(ctx, "节点 %s 的所有子网都没有可用IP，测试无法继续", nodeName)
		return fmt.Errorf("节点 %s 的所有子网都没有可用IP", nodeName)
	}

	logger.Infof(ctx, "选择子网 %s 创建ENI，可用IP数量: %d", selectedSubnetID, maxAvailableIPs)

	createArgs := &eni.CreateENIArgs{
		Name:             eniName,
		SubnetID:         selectedSubnetID,
		SecurityGroupIDs: securityGroups,
		Description:      "手动创建的ENI，用于测试不被纳管",
		PrivateIPSet:     []*eni.PrivateIP{{Primary: true}}, // 至少需要一个主IP
	}

	logger.Infof(ctx, "创建ENI参数: 名称=%s, 子网=%s, 安全组=%v",
		eniName, selectedSubnetID, securityGroups)

	eniID, err := c.base.ENIClient.CreateENI(ctx, createArgs, nil)
	if err != nil {
		return fmt.Errorf("创建ENI失败: %v", err)
	}

	logger.Infof(ctx, "成功创建手动ENI, ID: %s, 名称: %s", eniID, eniName)

	// 添加资源到列表中以便清理
	c.resources = append(c.resources, cases.Resource{
		CaseName: CheckNodeScaleResourcesCaseName,
		Type:     "ENI", // 使用字符串而不是常量
		ID:       eniID,
	})

	// 等待ENI创建完成并变为"available"状态
	logger.Infof(ctx, "等待ENI变为可用状态...")
	err = c.waitForENIStatus(ctx, eniID, string(eni.ENIStatusAvailable), 2*time.Minute)
	if err != nil {
		return fmt.Errorf("等待ENI变为可用状态超时: %v", err)
	}

	// 5. 尝试将ENI附加到节点
	logger.Infof(ctx, "尝试将手动创建的ENI附加到节点...")
	attachArgs := &eni.AttachENIArgs{
		InstanceID: instanceID,
		ENIID:      eniID,
	}

	// 添加容错
	for i := 0; i < 5; i++ {
		err = c.base.ENIClient.AttachENI(ctx, attachArgs, nil)
		if err != nil {
			if strings.Contains(err.Error(), "VmStatusException") {
				logger.Warnf(ctx, "将ENI附加到节点失败，重试中... %v", err)
				time.Sleep(10 * time.Second)
				continue
			}
			return fmt.Errorf("将ENI附加到节点失败: %v", err)
		}
		break
	}

	logger.Infof(ctx, "成功将ENI附加到节点 %s", nodeName)

	// 等待ENI附加完成并变为"inuse"状态
	logger.Infof(ctx, "等待ENI变为使用中状态...")
	err = c.waitForENIStatus(ctx, eniID, string(eni.ENIStatusInuse), 3*time.Minute)
	if err != nil {
		return fmt.Errorf("等待ENI变为使用中状态超时: %v", err)
	}

	// 6. 等待一段时间，让CCE有机会发现并可能纳管ENI
	waitTime := 30 * time.Second
	logger.Infof(ctx, "等待 %v 让CCE可能发现并纳管ENI...", waitTime)
	time.Sleep(waitTime)

	// 7. 检查该ENI是否被CCE纳管
	eniList, err := c.base.KubeClient.ListENI(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取ENI资源列表失败: %v", err)
	}

	// 检查手动创建的ENI是否出现在CCE管理的ENI列表中
	manualENIFound := false
	for _, cceEni := range eniList.Items {
		if cceEni.Name == eniID {
			manualENIFound = true
			logger.Warnf(ctx, "手动创建的ENI %s 已被CCE纳管，这与预期不符", eniID)
			break
		}
	}

	if !manualENIFound {
		logger.Infof(ctx, "验证成功：手动创建的ENI %s 没有被CCE自动纳管", eniID)
	}

	// 8. 使用BCE API检查ENI状态
	statResp, err := c.base.ENIClient.StatENI(ctx, eniID, nil)
	if err != nil {
		return fmt.Errorf("获取ENI状态失败: %v", err)
	}

	logger.Infof(ctx, "手动创建的ENI %s 当前状态: %s", eniID, statResp.Status)
	logger.Infof(ctx, "ENI MAC地址: %s", statResp.MacAddress)
	if statResp.InstanceID != "" {
		logger.Infof(ctx, "ENI已附加到实例: %s", statResp.InstanceID)
	} else {
		logger.Infof(ctx, "ENI未附加到任何实例")
	}

	// 打印更多ENI信息
	logger.Infof(ctx, "ENI详细信息:")
	logger.Infof(ctx, "  VPCID: %s", statResp.VPCID)
	logger.Infof(ctx, "  子网ID: %s", statResp.SubnetID)
	logger.Infof(ctx, "  私有IP数量: %d", len(statResp.PrivateIPSet))

	for i, ip := range statResp.PrivateIPSet {
		logger.Infof(ctx, "  私有IP #%d: %s (主IP: %v)",
			i+1, ip.PrivateIPAddress, ip.Primary)
	}

	// 9. 如果测试成功完成，现在尝试分离ENI并清理资源
	if statResp.InstanceID != "" {
		logger.Infof(ctx, "测试完成，尝试分离ENI...")
		detachArgs := &eni.DetachENIArgs{
			InstanceID: instanceID,
			ENIID:      eniID,
		}

		err = c.base.ENIClient.DetachENI(ctx, detachArgs, nil)
		if err != nil {
			return fmt.Errorf("分离ENI失败: %v", err)
		}

		logger.Infof(ctx, "成功分离ENI %s", eniID)

		// 等待ENI分离完成
		logger.Infof(ctx, "等待ENI分离完成...")
		err = c.waitForENIStatus(ctx, eniID, string(eni.ENIStatusAvailable), 3*time.Minute)
		if err != nil {
			return fmt.Errorf("等待ENI分离完成超时: %v", err)
		}

		// 尝试删除ENI
		logger.Infof(ctx, "尝试删除ENI %s...", eniID)
		err = c.base.ENIClient.DeleteENI(ctx, eniID, nil)
		if err != nil {
			return fmt.Errorf("删除ENI失败: %v", err)
		}

		logger.Infof(ctx, "成功删除ENI %s", eniID)
	}

	// 10. 总结测试结果
	if !manualENIFound {
		logger.Infof(ctx, "测试结论: 手动创建并附加到节点的ENI不会被CCE自动纳管")
		logger.Infof(ctx, "这符合预期，验证通过")
	} else {
		logger.Warnf(ctx, "测试结论: 手动创建的ENI被CCE纳管，这与预期不符")
		// 虽然结果与预期不符，但我们不会将其视为测试失败，而是作为观察结果
	}

	return nil
}

// waitForENIStatus 等待ENI达到特定状态
func (c *checkNodeScaleResources) waitForENIStatus(ctx context.Context, eniID string, targetStatus string, timeout time.Duration) error {
	startTime := time.Now()
	endTime := startTime.Add(timeout)
	pollInterval := 10 * time.Second

	logger.Infof(ctx, "等待ENI %s 达到状态 %s，超时时间 %v", eniID, targetStatus, timeout)

	for time.Now().Before(endTime) {
		statResp, err := c.base.ENIClient.StatENI(ctx, eniID, nil)
		if err != nil {
			logger.Warnf(ctx, "获取ENI状态失败: %v，将在 %v 后重试", err, pollInterval)
		} else {
			currentStatus := string(statResp.Status)
			logger.Infof(ctx, "ENI %s 当前状态: %s，目标状态: %s", eniID, currentStatus, targetStatus)

			if currentStatus == targetStatus {
				elapsedTime := time.Since(startTime).Round(time.Second)
				logger.Infof(ctx, "ENI %s 已达到目标状态 %s，耗时 %v", eniID, targetStatus, elapsedTime)
				return nil
			}
		}

		time.Sleep(pollInterval)
	}

	return fmt.Errorf("等待ENI %s 达到状态 %s 超时，已等待 %v", eniID, targetStatus, timeout)
}

// checkResourceRecycling 检查资源回收情况
func (c *checkNodeScaleResources) checkResourceRecycling(ctx context.Context) error {
	// 为等待资源删除设置超时时间
	timeoutCtx, cancel := context.WithTimeout(ctx, 5*time.Minute)
	defer cancel()

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	// 定义检查资源是否已删除的函数
	checkResourcesDeleted := func(nodeName string) (bool, error) {
		// 检查NRS资源是否被删除
		_, nrsErr := c.base.KubeClient.GetNrs(ctx, nodeName, &kube.GetOptions{})

		// 检查该节点关联的ENI资源是否被删除
		eniList, eniListErr := c.base.KubeClient.ListENI(ctx, &kube.ListOptions{})
		if eniListErr != nil {
			return false, fmt.Errorf("获取ENI资源列表失败: %v", eniListErr)
		}

		// 查找是否有属于该节点的ENI
		nodeHasENI := false
		for _, eni := range eniList.Items {
			if eni.Spec.NodeName == nodeName {
				nodeHasENI = true
				break
			}
		}

		// 如果NRS已删除且没有ENI资源，表示资源回收完成
		return nrsErr != nil && !nodeHasENI, nil
	}

	// 轮询等待资源删除
	for {
		select {
		case <-ticker.C:
			allNodesResourcesDeleted := true
			for nodeName := range c.deletedNodes {
				// 检查资源是否已删除
				deleted, err := checkResourcesDeleted(nodeName)
				if err != nil {
					logger.Warnf(ctx, "检查节点 %s 资源回收状态失败: %v", nodeName, err)
					allNodesResourcesDeleted = false
					continue
				}

				if !deleted {
					allNodesResourcesDeleted = false
					logger.Warnf(ctx, "节点 %s 的资源尚未完全回收", nodeName)
				}
			}

			if allNodesResourcesDeleted {
				logger.Infof(ctx, "所有被删除节点的资源已正确回收")
				return nil
			}

		case <-timeoutCtx.Done():
			// 超时后，最后检查一次资源状态
			logger.Warnf(ctx, "等待资源回收超时，进行最后状态检查")

			for nodeName := range c.deletedNodes {
				_, nrsErr := c.base.KubeClient.GetNrs(ctx, nodeName, &kube.GetOptions{})
				if nrsErr == nil {
					logger.Errorf(ctx, "资源回收失败: NRS %s 在超时后仍然存在", nodeName)
				}

				eniList, err := c.base.KubeClient.ListENI(ctx, &kube.ListOptions{})
				if err != nil {
					return fmt.Errorf("获取ENI资源列表失败: %v", err)
				}

				remainingENIs := []string{}
				for _, eni := range eniList.Items {
					if eni.Spec.NodeName == nodeName {
						remainingENIs = append(remainingENIs, eni.Name)
					}
				}

				if len(remainingENIs) > 0 {
					return fmt.Errorf("资源回收失败: %d 个ENI资源在超时后仍然关联到节点 %s: %v", len(remainingENIs), nodeName, remainingENIs)
				}
			}

			// 虽然超时，但资源已删除
			return nil
		}
	}
}

// Clean 清理测试资源
func (c *checkNodeScaleResources) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理测试资源...")
	var lastErr error

	// 1. 清理测试中可能创建的ENI资源
	for _, resource := range c.resources {
		if resource.Type == "ENI" {
			eniID := resource.ID
			logger.Infof(ctx, "清理阶段: 检查ENI %s 状态", eniID)

			// 检查ENI是否存在
			statResp, err := c.base.ENIClient.StatENI(ctx, eniID, nil)
			if err != nil {
				logger.Warnf(ctx, "获取ENI %s 状态失败，可能已被删除: %v", eniID, err)
				continue
			}

			// 如果ENI仍然附加在实例上，先尝试分离
			if statResp.InstanceID != "" {
				logger.Infof(ctx, "ENI %s 仍附加在实例 %s 上，尝试分离...", eniID, statResp.InstanceID)

				detachArgs := &eni.DetachENIArgs{
					InstanceID: statResp.InstanceID,
					ENIID:      eniID,
				}

				err = c.base.ENIClient.DetachENI(ctx, detachArgs, nil)
				if err != nil {
					logger.Warnf(ctx, "分离ENI %s 失败: %v", eniID, err)
					if lastErr == nil {
						lastErr = err
					}
					// 即使分离失败，我们仍然尝试删除
				} else {
					// 分离成功，等待ENI变为可用状态
					logger.Infof(ctx, "已请求分离ENI %s，等待ENI变为可用状态...", eniID)

					// 等待最多2分钟
					for i := 0; i < 12; i++ {
						time.Sleep(10 * time.Second)

						// 检查ENI状态
						checkResp, err := c.base.ENIClient.StatENI(ctx, eniID, nil)
						if err != nil {
							logger.Warnf(ctx, "检查ENI %s 状态失败: %v", eniID, err)
							break
						}

						if string(checkResp.Status) == string(eni.ENIStatusAvailable) {
							logger.Infof(ctx, "ENI %s 已变为可用状态", eniID)
							break
						}

						logger.Infof(ctx, "ENI %s 当前状态: %s，继续等待...", eniID, checkResp.Status)
					}
				}
			}

			// 尝试删除ENI
			logger.Infof(ctx, "尝试删除ENI %s...", eniID)
			err = c.base.ENIClient.DeleteENI(ctx, eniID, nil)
			if err != nil {
				logger.Warnf(ctx, "删除ENI %s 失败: %v", eniID, err)
				if lastErr == nil {
					lastErr = err
				}
			} else {
				logger.Infof(ctx, "成功删除ENI %s", eniID)
			}
		}
	}

	// 2. 处理节点组
	// 如果我们创建了节点组，则先缩容到0，然后删除
	if c.createdNodeGroup && c.instanceGroup != "" {
		// 先获取节点组当前状态
		ig, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroup, nil)
		if err != nil {
			logger.Warnf(ctx, "获取节点组状态失败: %v", err)
			if lastErr == nil {
				lastErr = err
			}
		} else {
			// 如果节点组当前副本数不为0，先缩容到0
			if ig.InstanceGroup.Spec.Replicas > 0 {
				logger.Infof(ctx, "节点组 %s 当前副本数: %d, 开始缩容到0...",
					c.instanceGroup, ig.InstanceGroup.Spec.Replicas)

				// 使用UpdateInstanceGroupReplicas而不是CreateScaleUpInstanceGroupTask
				request := &ccev2.UpdateInstanceGroupReplicasRequest{
					Replicas: 0,
				}

				resp, err := c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, c.base.ClusterID, c.instanceGroup, request, nil)
				if err != nil {
					logger.Warnf(ctx, "缩容节点组到0失败: %v", err)
					if lastErr == nil {
						lastErr = err
					}
				} else {
					logger.Infof(ctx, "已发送缩容节点组到0的请求，RequestID: %s", resp.RequestID)

					// 等待缩容完成，最多等待10分钟
					timeout := 10 * time.Minute
					deadline := time.Now().Add(timeout)

					logger.Infof(ctx, "等待节点组缩容到0完成...")
					for time.Now().Before(deadline) {
						// 检查当前副本数
						currentIG, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroup, nil)
						if err != nil {
							logger.Warnf(ctx, "获取节点组状态失败: %v", err)
							break
						}

						// 如果当前Ready副本数为0，说明缩容完成
						if currentIG.InstanceGroup.Status.ReadyReplicas == 0 {
							logger.Infof(ctx, "节点组 %s 已成功缩容到0个节点", c.instanceGroup)
							break
						}

						logger.Infof(ctx, "节点组 %s 当前Ready副本数: %d，继续等待...",
							c.instanceGroup, currentIG.InstanceGroup.Status.ReadyReplicas)
						time.Sleep(30 * time.Second)
					}

					// 检查是否超时
					if time.Now().After(deadline) {
						logger.Warnf(ctx, "等待节点组缩容到0超时(%v)，将继续尝试删除节点组", timeout)
					}
				}
			}

			// 缩容完成或失败后，尝试删除节点组
			logger.Infof(ctx, "删除测试创建的节点组 %s", c.instanceGroup)
			_, err = c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.instanceGroup, true, nil)
			if err != nil {
				logger.Warnf(ctx, "删除测试创建的节点组失败: %v", err)
				if lastErr == nil {
					lastErr = err
				}
			} else {
				logger.Infof(ctx, "成功删除测试创建的节点组 %s", c.instanceGroup)
			}
		}
	} else if c.instanceGroup != "" && c.originalReplicas > 0 {
		// 如果是已有节点组，恢复原始副本数
		logger.Infof(ctx, "使用保存的原始副本数: %d", c.originalReplicas)

		// 获取当前节点组状态
		ig, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroup, nil)
		if err != nil {
			logger.Warnf(ctx, "获取节点组状态失败: %v", err)
			if lastErr == nil {
				lastErr = err
			}
		} else {
			logger.Infof(ctx, "节点组 %s 当前副本数: %d,  原始副本数: %d",
				c.instanceGroup, ig.InstanceGroup.Spec.Replicas, c.originalReplicas)

			// 只有当前副本数与原始副本数不同时才需要恢复
			if ig.InstanceGroup.Spec.Replicas != c.originalReplicas {
				// 使用UpdateInstanceGroupReplicas恢复原始副本数
				request := &ccev2.UpdateInstanceGroupReplicasRequest{
					Replicas: c.originalReplicas,
				}

				resp, err := c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, c.base.ClusterID, c.instanceGroup, request, nil)
				if err != nil {
					logger.Warnf(ctx, "恢复节点组副本数失败: %v", err)
					if lastErr == nil {
						lastErr = err
					}
				} else {
					logger.Infof(ctx, "已发送恢复节点组副本数请求，RequestID: %s", resp.RequestID)
				}
			} else {
				logger.Infof(ctx, "节点组 %s 副本数已经是原始值 %d，无需恢复", c.instanceGroup, c.originalReplicas)
			}
		}
	}

	logger.Infof(ctx, "资源清理完成")
	return lastErr
}

func (c *checkNodeScaleResources) Continue(ctx context.Context) bool {
	return true
}

func (c *checkNodeScaleResources) ConfigFormat() string {
	return ""
}
