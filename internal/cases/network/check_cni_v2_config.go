// Copyright 2024 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2024/12/04 18:00:00, by r<PERSON><PERSON><PERSON><EMAIL>, create
*/

/*
check-cni-v2-config - CCE网络v2版本配置测试:
1. 检查容器网络模式（vpc-route或vpc-eni）
2. 测试修改ext-cni-plugins配置（添加portmap插件）
3. 重启daemonset cce-network-agent
4. 验证CNI配置文件是否正确生成
*/

package network

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	// CheckCNIV2ConfigCaseName - case 名字
	CheckCNIV2ConfigCaseName cases.CaseName = "CheckCNIV2Config"

	// 常量定义
	configMapName       = "cce-network-v2-config"
	daemonSetName       = "cce-network-agent"
	kubeSystemNamespace = "kube-system"
	cniConfigPath       = "/etc/cni/net.d/00-cce-cni.conflist"
	maxWaitTime         = 5 * time.Minute
	checkInterval       = 10 * time.Second
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CheckCNIV2ConfigCaseName, NewCheckCNIV2Config)
}

type checkCNIV2Config struct {
	base               *cases.BaseClient
	originalConfig     string   // 保存原始配置用于恢复
	originalExtPlugins []string // 保存原始扩展插件配置
	networkMode        string   // 网络模式（vpc-route或vpc-eni）
	agentPodName       string   // 用于执行命令的agent pod名称
	targetNodeName     string   // 目标节点名称
}

// NewCheckCNIV2Config - 测试案例
func NewCheckCNIV2Config(ctx context.Context) cases.Interface {
	return &checkCNIV2Config{}
}

func (c *checkCNIV2Config) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base is nil")
	}

	c.base = base
	return nil
}

func (c *checkCNIV2Config) Name() cases.CaseName {
	return CheckCNIV2ConfigCaseName
}

func (c *checkCNIV2Config) Desc() string {
	return "测试CCE网络v2版本配置的检查、修改和验证功能"
}

func (c *checkCNIV2Config) Check(ctx context.Context) ([]cases.Resource, error) {
	logger.Infof(ctx, "开始执行CCE网络v2配置测试...")

	// 阶段1：检查容器网络模式
	logger.Infof(ctx, "=== 阶段1：检查容器网络模式 ===")
	if err := c.checkNetworkMode(ctx); err != nil {
		return nil, fmt.Errorf("检查容器网络模式失败: %v", err)
	}

	// 阶段2：获取原始配置
	logger.Infof(ctx, "=== 阶段2：获取原始配置 ===")
	if err := c.getOriginalConfig(ctx); err != nil {
		return nil, fmt.Errorf("获取原始配置失败: %v", err)
	}

	// 阶段3：选择目标节点和agent pod
	logger.Infof(ctx, "=== 阶段3：选择目标节点和agent pod ===")
	if err := c.selectTargetNode(ctx); err != nil {
		return nil, fmt.Errorf("选择目标节点失败: %v", err)
	}

	// 阶段4：修改CNI插件配置
	logger.Infof(ctx, "=== 阶段4：修改CNI插件配置 ===")
	if err := c.modifyExtCNIPlugins(ctx); err != nil {
		return nil, fmt.Errorf("修改CNI插件配置失败: %v", err)
	}

	// 阶段5：重启daemonset
	logger.Infof(ctx, "=== 阶段5：重启daemonset ===")
	if err := c.restartDaemonSet(ctx); err != nil {
		return nil, fmt.Errorf("重启daemonset失败: %v", err)
	}

	// 阶段6：重新选择agent pod（重启后pod名称会变化）
	logger.Infof(ctx, "=== 阶段6：重新选择agent pod ===")
	if err := c.selectTargetNode(ctx); err != nil {
		return nil, fmt.Errorf("重新选择agent pod失败: %v", err)
	}

	// 阶段7：验证CNI配置文件
	logger.Infof(ctx, "=== 阶段7：验证CNI配置文件 ===")
	if err := c.verifyCNIConfigFile(ctx); err != nil {
		return nil, fmt.Errorf("验证CNI配置文件失败: %v", err)
	}

	logger.Infof(ctx, "CCE网络v2配置测试完成")
	return nil, nil
}

func (c *checkCNIV2Config) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理资源...")

	// 恢复原始配置
	if c.originalConfig != "" {
		logger.Infof(ctx, "恢复原始配置...")
		if err := c.restoreOriginalConfig(ctx); err != nil {
			logger.Warnf(ctx, "恢复原始配置失败: %v", err)
		}

		// 重启daemonset使配置生效
		if err := c.restartDaemonSet(ctx); err != nil {
			logger.Warnf(ctx, "重启daemonset失败: %v", err)
		}
	}

	logger.Infof(ctx, "资源清理完成")
	return nil
}

func (c *checkCNIV2Config) Continue(ctx context.Context) bool {
	return true
}

func (c *checkCNIV2Config) ConfigFormat() string {
	return ""
}

// checkNetworkMode 检查容器网络模式
func (c *checkCNIV2Config) checkNetworkMode(ctx context.Context) error {
	logger.Infof(ctx, "检查容器网络模式...")

	// 获取ConfigMap
	configMap, err := c.base.K8SClient.CoreV1().ConfigMaps(kubeSystemNamespace).Get(ctx, configMapName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取ConfigMap %s 失败: %v", configMapName, err)
	}

	// 检查ConfigMap中是否包含cced配置
	ccedConfig, ok := configMap.Data["cced"]
	if !ok {
		return fmt.Errorf("ConfigMap %s 中不存在cced配置", configMapName)
	}

	logger.Infof(ctx, "成功获取到cced配置，长度: %d", len(ccedConfig))

	// 提取ipam字段
	ipamValue := c.extractConfigValue(ccedConfig, "ipam")
	if ipamValue == "" {
		return fmt.Errorf("无法从配置中提取ipam字段")
	}

	// 判断网络模式
	if strings.HasPrefix(ipamValue, "vpc-route") {
		c.networkMode = "vpc-route"
		logger.Infof(ctx, "✓ 容器网络模式: 基于VPC路由的网络方案 (ipam: %s)", ipamValue)
	} else if strings.HasPrefix(ipamValue, "vpc-eni") {
		c.networkMode = "vpc-eni"
		logger.Infof(ctx, "✓ 容器网络模式: 基于弹性网卡的网络方案 (ipam: %s)", ipamValue)
	} else {
		return fmt.Errorf("未知的网络模式: %s", ipamValue)
	}

	return nil
}

// getOriginalConfig 获取原始配置
func (c *checkCNIV2Config) getOriginalConfig(ctx context.Context) error {
	logger.Infof(ctx, "获取原始配置...")

	// 获取ConfigMap
	configMap, err := c.base.K8SClient.CoreV1().ConfigMaps(kubeSystemNamespace).Get(ctx, configMapName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取ConfigMap失败: %v", err)
	}

	// 保存原始配置
	ccedConfig, ok := configMap.Data["cced"]
	if !ok {
		return fmt.Errorf("ConfigMap中不存在cced配置")
	}

	c.originalConfig = ccedConfig
	logger.Infof(ctx, "已保存原始配置，长度: %d", len(c.originalConfig))

	// 提取原始的ext-cni-plugins配置
	c.originalExtPlugins = c.extractExtCNIPlugins(ccedConfig)
	logger.Infof(ctx, "原始ext-cni-plugins配置: %v", c.originalExtPlugins)

	return nil
}

// selectTargetNode 选择目标节点和agent pod
func (c *checkCNIV2Config) selectTargetNode(ctx context.Context) error {
	logger.Infof(ctx, "选择目标节点和agent pod...")

	// 获取所有network agent pods
	podList, err := c.base.K8SClient.CoreV1().Pods(kubeSystemNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "app.cce.baidubce.com=cce-network-agent",
	})
	if err != nil {
		return fmt.Errorf("获取network agent pods失败: %v", err)
	}

	if len(podList.Items) == 0 {
		return fmt.Errorf("没有找到network agent pods")
	}

	// 选择第一个运行中的pod
	for _, pod := range podList.Items {
		if pod.Status.Phase == corev1.PodRunning {
			c.agentPodName = pod.Name
			c.targetNodeName = pod.Spec.NodeName
			logger.Infof(ctx, "选择的agent pod: %s, 节点: %s", c.agentPodName, c.targetNodeName)
			return nil
		}
	}

	return fmt.Errorf("没有找到运行中的network agent pod")
}

// modifyExtCNIPlugins 修改CNI插件配置
func (c *checkCNIV2Config) modifyExtCNIPlugins(ctx context.Context) error {
	logger.Infof(ctx, "修改CNI插件配置...")

	// 获取当前ConfigMap
	configMap, err := c.base.K8SClient.CoreV1().ConfigMaps(kubeSystemNamespace).Get(ctx, configMapName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取ConfigMap失败: %v", err)
	}

	ccedConfig, ok := configMap.Data["cced"]
	if !ok {
		return fmt.Errorf("ConfigMap中不存在cced配置")
	}

	// 修改ext-cni-plugins配置，添加portmap插件
	newConfig := c.addPortmapPlugin(ccedConfig)

	// 更新ConfigMap
	configMap.Data["cced"] = newConfig
	_, err = c.base.K8SClient.CoreV1().ConfigMaps(kubeSystemNamespace).Update(ctx, configMap, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新ConfigMap失败: %v", err)
	}

	logger.Infof(ctx, "✓ 成功修改CNI插件配置，添加了portmap插件")
	return nil
}

// restartDaemonSet 重启daemonset
func (c *checkCNIV2Config) restartDaemonSet(ctx context.Context) error {
	logger.Infof(ctx, "重启daemonset %s...", daemonSetName)

	// 获取DaemonSet
	ds, err := c.base.K8SClient.AppsV1().DaemonSets(kubeSystemNamespace).Get(ctx, daemonSetName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取DaemonSet失败: %v", err)
	}

	// 添加重启注解
	if ds.Spec.Template.Annotations == nil {
		ds.Spec.Template.Annotations = make(map[string]string)
	}
	ds.Spec.Template.Annotations["kubectl.kubernetes.io/restartedAt"] = time.Now().Format(time.RFC3339)

	// 更新DaemonSet
	_, err = c.base.K8SClient.AppsV1().DaemonSets(kubeSystemNamespace).Update(ctx, ds, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新DaemonSet失败: %v", err)
	}

	logger.Infof(ctx, "DaemonSet重启请求已发送")

	// 等待重启完成
	return c.waitForDaemonSetReady(ctx)
}

// waitForDaemonSetReady 等待DaemonSet就绪
func (c *checkCNIV2Config) waitForDaemonSetReady(ctx context.Context) error {
	logger.Infof(ctx, "等待DaemonSet就绪...")

	timeout := time.After(maxWaitTime)
	ticker := time.NewTicker(checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			return fmt.Errorf("等待DaemonSet就绪超时")
		case <-ticker.C:
			ds, err := c.base.K8SClient.AppsV1().DaemonSets(kubeSystemNamespace).Get(ctx, daemonSetName, metav1.GetOptions{})
			if err != nil {
				logger.Warnf(ctx, "获取DaemonSet状态失败: %v", err)
				continue
			}

			if ds.Status.NumberReady == ds.Status.DesiredNumberScheduled {
				logger.Infof(ctx, "✓ DaemonSet已就绪，期望Pod数: %d, 就绪Pod数: %d",
					ds.Status.DesiredNumberScheduled, ds.Status.NumberReady)
				return nil
			}

			logger.Infof(ctx, "DaemonSet状态: 期望Pod数: %d, 就绪Pod数: %d",
				ds.Status.DesiredNumberScheduled, ds.Status.NumberReady)
		}
	}
}

// verifyCNIConfigFile 验证CNI配置文件
func (c *checkCNIV2Config) verifyCNIConfigFile(ctx context.Context) error {
	logger.Infof(ctx, "验证CNI配置文件...")

	// 获取pod信息，获取第一个容器名称
	pod, err := c.base.K8SClient.CoreV1().Pods(kubeSystemNamespace).Get(ctx, c.agentPodName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取pod信息失败: %v", err)
	}

	if len(pod.Spec.Containers) == 0 {
		return fmt.Errorf("pod没有容器")
	}

	containerName := pod.Spec.Containers[0].Name
	logger.Infof(ctx, "使用容器: %s", containerName)

	// 在agent pod中执行命令查看CNI配置文件
	cmd := []string{
		"sh", "-c",
		fmt.Sprintf("nsenter -t 1 -m cat %s", cniConfigPath),
	}

	stdout, err := c.base.KubeClient.RemoteExec(kubeSystemNamespace, c.agentPodName, containerName, cmd)
	if err != nil {
		return fmt.Errorf("执行查看CNI配置文件命令失败: %v", err)
	}

	if stdout == "" {
		return fmt.Errorf("CNI配置文件为空")
	}

	logger.Infof(ctx, "CNI配置文件内容:")
	logger.Infof(ctx, "%s", stdout)

	// 解析JSON配置
	var cniConfig map[string]interface{}
	if err := json.Unmarshal([]byte(stdout), &cniConfig); err != nil {
		return fmt.Errorf("解析CNI配置文件JSON失败: %v", err)
	}

	// 验证基本结构
	if err := c.validateCNIConfig(cniConfig); err != nil {
		return fmt.Errorf("验证CNI配置失败: %v", err)
	}

	logger.Infof(ctx, "✓ CNI配置文件验证通过")
	return nil
}

// validateCNIConfig 验证CNI配置
func (c *checkCNIV2Config) validateCNIConfig(cniConfig map[string]interface{}) error {
	// 检查必需字段
	requiredFields := []string{"cniVersion", "name", "plugins"}
	for _, field := range requiredFields {
		if _, ok := cniConfig[field]; !ok {
			return fmt.Errorf("缺少必需字段: %s", field)
		}
	}

	// 检查cniVersion
	cniVersion, ok := cniConfig["cniVersion"].(string)
	if !ok {
		return fmt.Errorf("cniVersion字段类型错误")
	}
	if cniVersion != "0.4.0" {
		return fmt.Errorf("期望cniVersion为0.4.0，实际为: %s", cniVersion)
	}

	// 检查name
	name, ok := cniConfig["name"].(string)
	if !ok {
		return fmt.Errorf("name字段类型错误")
	}
	if name != "generic-veth" {
		return fmt.Errorf("期望name为generic-veth，实际为: %s", name)
	}

	// 检查plugins数组
	plugins, ok := cniConfig["plugins"].([]interface{})
	if !ok {
		return fmt.Errorf("plugins字段类型错误")
	}

	if len(plugins) == 0 {
		return fmt.Errorf("plugins数组为空")
	}

	// 检查是否包含portmap插件
	portmapFound := false
	endpointProbeFound := false
	cptpFound := false

	for _, plugin := range plugins {
		pluginMap, ok := plugin.(map[string]interface{})
		if !ok {
			continue
		}

		pluginType, ok := pluginMap["type"].(string)
		if !ok {
			continue
		}

		switch pluginType {
		case "portmap":
			portmapFound = true
		case "endpoint-probe":
			endpointProbeFound = true
		case "cptp":
			cptpFound = true
		}
	}

	if !cptpFound {
		return fmt.Errorf("未找到必需的cptp插件")
	}

	if !endpointProbeFound {
		return fmt.Errorf("未找到必需的endpoint-probe插件")
	}

	if !portmapFound {
		return fmt.Errorf("未找到新增的portmap插件")
	}

	return nil
}

// restoreOriginalConfig 恢复原始配置
func (c *checkCNIV2Config) restoreOriginalConfig(ctx context.Context) error {
	logger.Infof(ctx, "恢复原始配置...")

	// 获取ConfigMap
	configMap, err := c.base.K8SClient.CoreV1().ConfigMaps(kubeSystemNamespace).Get(ctx, configMapName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取ConfigMap失败: %v", err)
	}

	// 恢复原始配置
	configMap.Data["cced"] = c.originalConfig
	_, err = c.base.K8SClient.CoreV1().ConfigMaps(kubeSystemNamespace).Update(ctx, configMap, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新ConfigMap失败: %v", err)
	}

	logger.Infof(ctx, "✓ 成功恢复原始配置")
	return nil
}

// extractConfigValue 从配置中提取指定参数的值
func (c *checkCNIV2Config) extractConfigValue(config, key string) string {
	lines := strings.Split(config, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, key+":") {
			parts := strings.SplitN(line, ":", 2)
			if len(parts) == 2 {
				return strings.TrimSpace(parts[1])
			}
		}
	}
	return ""
}

// extractExtCNIPlugins 提取ext-cni-plugins配置
func (c *checkCNIV2Config) extractExtCNIPlugins(config string) []string {
	var plugins []string
	lines := strings.Split(config, "\n")
	inExtCNIPlugins := false

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "ext-cni-plugins:") {
			inExtCNIPlugins = true
			continue
		}
		if inExtCNIPlugins {
			if strings.HasPrefix(line, "- ") {
				plugin := strings.TrimSpace(strings.TrimPrefix(line, "- "))
				plugins = append(plugins, plugin)
			} else if line != "" && !strings.HasPrefix(line, " ") {
				// 遇到新的配置项，结束解析
				break
			}
		}
	}

	return plugins
}

// addPortmapPlugin 添加portmap插件
func (c *checkCNIV2Config) addPortmapPlugin(config string) string {
	lines := strings.Split(config, "\n")
	var newLines []string
	extCNIPluginsFound := false
	portmapExists := false

	for i, line := range lines {
		trimmedLine := strings.TrimSpace(line)

		// 检查是否找到ext-cni-plugins配置
		if strings.HasPrefix(trimmedLine, "ext-cni-plugins:") {
			extCNIPluginsFound = true
			newLines = append(newLines, line)

			// 检查是否已存在portmap插件
			j := i + 1
			for j < len(lines) && (strings.HasPrefix(strings.TrimSpace(lines[j]), "- ") || strings.TrimSpace(lines[j]) == "") {
				if strings.Contains(lines[j], "portmap") {
					portmapExists = true
				}
				j++
			}

			// 如果不存在portmap插件，添加它
			if !portmapExists {
				newLines = append(newLines, "- portmap")
			}
			continue
		}

		newLines = append(newLines, line)
	}

	// 如果没有找到ext-cni-plugins配置，添加它
	if !extCNIPluginsFound {
		newLines = append(newLines, "ext-cni-plugins:")
		newLines = append(newLines, "- portmap")
		newLines = append(newLines, "- endpoint-probe")
	}

	return strings.Join(newLines, "\n")
}
