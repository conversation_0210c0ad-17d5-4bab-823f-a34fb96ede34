package network

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eni"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	// CheckNodeMoveOutReinstallMoveInCaseName - case 名字
	CheckNodeMoveOutReinstallMoveInCaseName cases.CaseName = "CheckNodeMoveOutReinstallMoveIn"
	// 固定密码，与其他创建节点组的做法一致
	DefaultAdminPassword = "Test123456!"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CheckNodeMoveOutReinstallMoveInCaseName, NewCheckNodeMoveOutReinstallMoveIn)
}

type checkNodeMoveOutReinstallMoveIn struct {
	base *cases.BaseClient

	// 测试过程中的状态
	targetInstanceID    string    // 目标节点的CCE实例ID
	targetMachineID     string    // 目标节点的机器ID
	targetNodeName      string    // 目标节点的K8s节点名
	originalImageID     string    // 原始镜像ID
	originalOSInfo      string    // 原始操作系统信息
	createdENIIDs       []string  // 创建的ENI网卡ID列表
	originalNRSStatus   string    // 原始NRS状态快照
	originalENIStatus   string    // 原始ENI状态快照
	beforeReinstallTime time.Time // 重装前的时间点
	afterReinstallTime  time.Time // 重装后的时间点
}

// NewCheckNodeMoveOutReinstallMoveIn - 测试案例
func NewCheckNodeMoveOutReinstallMoveIn(ctx context.Context) cases.Interface {
	return &checkNodeMoveOutReinstallMoveIn{}
}

func (c *checkNodeMoveOutReinstallMoveIn) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base is nil")
	}

	c.base = base
	c.createdENIIDs = make([]string, 0)
	return nil
}

func (c *checkNodeMoveOutReinstallMoveIn) Name() cases.CaseName {
	return CheckNodeMoveOutReinstallMoveInCaseName
}

func (c *checkNodeMoveOutReinstallMoveIn) Desc() string {
	return "测试节点移出、重装、移入的完整流程，验证网络资源状态和ENI不被纳管"
}

func (c *checkNodeMoveOutReinstallMoveIn) Check(ctx context.Context) ([]cases.Resource, error) {
	logger.Infof(ctx, "开始节点移出、重装、移入测试...")

	// 阶段1：选择目标节点
	if err := c.selectTargetNode(ctx); err != nil {
		return nil, fmt.Errorf("选择目标节点失败: %v", err)
	}

	// 阶段2：记录原始状态
	if err := c.recordOriginalStatus(ctx); err != nil {
		return nil, fmt.Errorf("记录原始状态失败: %v", err)
	}

	// 阶段3：移出节点
	if err := c.moveOutNode(ctx); err != nil {
		return nil, fmt.Errorf("移出节点失败: %v", err)
	}

	// 阶段4：移入节点并重装系统
	if err := c.moveInNodeWithReinstall(ctx); err != nil {
		return nil, fmt.Errorf("移入节点并重装失败: %v", err)
	}

	// 阶段5：等待节点就绪
	if err := c.waitForNodeReady(ctx); err != nil {
		return nil, fmt.Errorf("等待节点就绪失败: %v", err)
	}

	// 阶段6：验证重装确实发生了
	if err := c.verifyReinstallOccurred(ctx); err != nil {
		return nil, fmt.Errorf("验证重装失败: %v", err)
	}

	// 阶段7：检查网络资源状态
	if err := c.checkNetworkResourceStatus(ctx); err != nil {
		return nil, fmt.Errorf("检查网络资源状态失败: %v", err)
	}

	// 阶段8：创建ENI并验证不被纳管
	if err := c.createAndVerifyENI(ctx); err != nil {
		return nil, fmt.Errorf("创建ENI并验证不被纳管失败: %v", err)
	}

	logger.Infof(ctx, "节点移出、重装、移入测试完成")
	return nil, nil
}

// selectTargetNode 选择目标节点
func (c *checkNodeMoveOutReinstallMoveIn) selectTargetNode(ctx context.Context) error {
	logger.Infof(ctx, "选择目标节点...")

	// 获取集群中的节点实例
	instances, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
		ClusterRole: ccetypes.ClusterRoleNode,
	}, nil)
	if err != nil {
		return fmt.Errorf("获取节点实例失败: %v", err)
	}

	if len(instances.InstancePage.InstanceList) == 0 {
		return fmt.Errorf("集群中没有可用的节点")
	}

	// 选择第一个运行中的节点
	var targetInstance *ccev2.Instance
	for _, instance := range instances.InstancePage.InstanceList {
		if instance.Status.InstancePhase == ccetypes.InstancePhaseRunning {
			targetInstance = instance
			break
		}
	}

	if targetInstance == nil {
		return fmt.Errorf("没有找到运行中的节点")
	}

	c.targetInstanceID = targetInstance.Spec.CCEInstanceID
	c.targetMachineID = targetInstance.Status.Machine.InstanceID
	c.targetNodeName = targetInstance.Status.Machine.K8SNodeName
	c.originalImageID = targetInstance.Spec.ImageID // 保存原始镜像ID

	logger.Infof(ctx, "选择目标节点成功: CCEInstanceID=%s, MachineID=%s, NodeName=%s, ImageID=%s",
		c.targetInstanceID, c.targetMachineID, c.targetNodeName, c.originalImageID)

	return nil
}

// recordOriginalStatus 记录原始状态
func (c *checkNodeMoveOutReinstallMoveIn) recordOriginalStatus(ctx context.Context) error {
	logger.Infof(ctx, "记录节点原始状态...")

	// 记录重装前的时间点
	c.beforeReinstallTime = time.Now()

	// 记录原始操作系统信息
	instance, err := c.base.CCEClient.GetInstance(ctx, c.base.ClusterID, c.targetInstanceID, nil)
	if err != nil {
		logger.Warnf(ctx, "获取实例详情失败: %v", err)
	} else {
		osInfo := fmt.Sprintf("%s %s %s", instance.Instance.Spec.InstanceOS.OSName,
			instance.Instance.Spec.InstanceOS.OSVersion, instance.Instance.Spec.InstanceOS.OSArch)
		c.originalOSInfo = osInfo
		logger.Infof(ctx, "记录原始操作系统信息: %s", c.originalOSInfo)
	}

	// 记录NRS状态
	nrsList, err := c.base.KubeClient.ListNrs(ctx, &kube.ListOptions{})
	if err != nil {
		logger.Warnf(ctx, "获取NRS列表失败: %v", err)
	} else {
		// 查找目标节点的NRS
		for _, nrs := range nrsList.Items {
			if strings.Contains(nrs.Name, c.targetNodeName) ||
				strings.Contains(nrs.Name, c.targetMachineID) {
				statusBytes, _ := json.Marshal(nrs.Status)
				c.originalNRSStatus = string(statusBytes)
				logger.Infof(ctx, "记录目标节点NRS状态: %s", nrs.Name)
				break
			}
		}
	}

	// 记录ENI状态
	eniList, err := c.base.BCCClient.ListInstanceEnis(ctx, c.targetMachineID, nil)
	if err != nil {
		logger.Warnf(ctx, "获取ENI列表失败: %v", err)
	} else {
		statusBytes, _ := json.Marshal(eniList.EniList)
		c.originalENIStatus = string(statusBytes)
		logger.Infof(ctx, "记录目标节点ENI状态，ENI数量: %d", len(eniList.EniList))
	}

	return nil
}

// moveOutNode 移出节点
func (c *checkNodeMoveOutReinstallMoveIn) moveOutNode(ctx context.Context) error {
	logger.Infof(ctx, "开始移出节点: %s", c.targetInstanceID)

	// 移出节点，保留虚拟机资源
	_, err := c.base.CCEClient.DeleteInstances(ctx, c.base.ClusterID, &ccev2.DeleteInstancesRequest{
		InstanceIDs: []string{c.targetInstanceID},
		DeleteOption: &ccetypes.DeleteOption{
			MoveOut:           true,  // 只移出集群，不删除资源
			DeleteResource:    false, // 保留虚拟机资源
			DeleteCDSSnapshot: false, // 保留磁盘快照
			DrainNode:         true,  // 排水节点
		},
	}, nil)
	if err != nil {
		return fmt.Errorf("移出节点API调用失败: %v", err)
	}

	// 等待节点从集群中移除
	logger.Infof(ctx, "等待节点从集群中移除...")
	timeout := time.Now().Add(10 * time.Minute)
	for time.Now().Before(timeout) {
		// 检查节点是否还在集群中
		_, err := c.base.CCEClient.GetInstance(ctx, c.base.ClusterID, c.targetInstanceID, nil)
		if err != nil {
			if strings.Contains(err.Error(), "not found") || strings.Contains(err.Error(), "not exists") {
				logger.Infof(ctx, "节点已从集群中移除")
				break
			}
			return fmt.Errorf("检查节点状态失败: %v", err)
		}

		time.Sleep(10 * time.Second)
	}

	if time.Now().After(timeout) {
		return fmt.Errorf("等待节点移除超时")
	}

	logger.Infof(ctx, "节点移出成功")
	return nil
}

// moveInNodeWithReinstall 移入节点并重装系统
func (c *checkNodeMoveOutReinstallMoveIn) moveInNodeWithReinstall(ctx context.Context) error {
	logger.Infof(ctx, "开始移入节点并重装系统: %s", c.targetMachineID)

	// 构建移入请求，包含重装选项
	instanceSets := []*ccev2.InstanceSet{
		{
			InstanceSpec: ccetypes.InstanceSpec{
				Existed: true, // 使用已存在的实例
				ExistedOption: ccetypes.ExistedOption{
					ExistedInstanceID: c.targetMachineID,
					Rebuild:           &[]bool{true}[0], // 重装系统
				},
				MachineType:   ccetypes.MachineTypeBCC,
				ClusterRole:   ccetypes.ClusterRoleNode,
				ImageID:       c.originalImageID,    // 使用原始镜像ID
				AdminPassword: DefaultAdminPassword, // 使用固定密码
			},
			Count: 1,
		},
	}

	// 调用移入API
	resp, err := c.base.CCEClient.CreateInstances(ctx, c.base.ClusterID, instanceSets, nil)
	if err != nil {
		return fmt.Errorf("移入节点API调用失败: %v", err)
	}

	if len(resp.CCEInstanceIDs) == 0 {
		return fmt.Errorf("移入节点响应中没有返回实例ID")
	}

	// 更新目标实例ID（可能会变化）
	c.targetInstanceID = resp.CCEInstanceIDs[0]
	logger.Infof(ctx, "节点移入请求成功，新的CCEInstanceID: %s", c.targetInstanceID)

	// 记录重装后的时间点
	c.afterReinstallTime = time.Now()

	return nil
}

// waitForNodeReady 等待节点就绪
func (c *checkNodeMoveOutReinstallMoveIn) waitForNodeReady(ctx context.Context) error {
	logger.Infof(ctx, "等待节点就绪...")

	// 默认等待20分钟
	timeout := 20 * time.Minute
	deadline := time.Now().Add(timeout)
	for time.Now().Before(deadline) {
		// 检查CCE实例状态
		instance, err := c.base.CCEClient.GetInstance(ctx, c.base.ClusterID, c.targetInstanceID, nil)
		if err != nil {
			logger.Warnf(ctx, "获取实例状态失败: %v", err)
			time.Sleep(30 * time.Second)
			continue
		}

		// 更新节点名称
		if instance.Instance.Status.Machine.K8SNodeName != "" {
			c.targetNodeName = instance.Instance.Status.Machine.K8SNodeName
		}

		logger.Infof(ctx, "实例状态: Phase=%s",
			instance.Instance.Status.InstancePhase)

		// 检查实例是否完全就绪
		if instance.Instance.Status.InstancePhase == ccetypes.InstancePhaseRunning {

			// 额外检查K8s节点状态
			if c.targetNodeName != "" {
				node, err := c.base.KubeClient.ClientSet.CoreV1().Nodes().Get(ctx, c.targetNodeName, metav1.GetOptions{})
				if err == nil && c.isK8sNodeReady(node) {
					logger.Infof(ctx, "节点完全就绪: CCEInstanceID=%s, NodeName=%s",
						c.targetInstanceID, c.targetNodeName)
					// 就绪后可能还在挂载ENI网卡
					time.Sleep(30 * time.Second)
					return nil
				}
			}
		}

		time.Sleep(30 * time.Second)
	}

	return fmt.Errorf("等待节点就绪超时")
}

// isK8sNodeReady 检查K8s节点是否就绪
func (c *checkNodeMoveOutReinstallMoveIn) isK8sNodeReady(node *corev1.Node) bool {
	for _, condition := range node.Status.Conditions {
		if condition.Type == corev1.NodeNetworkUnavailable {
			return condition.Status == corev1.ConditionFalse
		}
	}
	return false
}

// verifyReinstallOccurred 验证重装确实发生了
func (c *checkNodeMoveOutReinstallMoveIn) verifyReinstallOccurred(ctx context.Context) error {
	logger.Infof(ctx, "验证重装确实发生了...")

	// 1. 检查节点的创建时间
	node, err := c.base.KubeClient.ClientSet.CoreV1().Nodes().Get(ctx, c.targetNodeName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取节点信息失败: %v", err)
	}

	nodeCreationTime := node.CreationTimestamp.Time
	logger.Infof(ctx, "节点创建时间: %s", nodeCreationTime.Format(time.RFC3339))
	logger.Infof(ctx, "重装前时间: %s", c.beforeReinstallTime.Format(time.RFC3339))
	logger.Infof(ctx, "重装后时间: %s", c.afterReinstallTime.Format(time.RFC3339))

	// 检查节点创建时间是否在重装时间范围内
	if nodeCreationTime.After(c.beforeReinstallTime) && nodeCreationTime.Before(c.afterReinstallTime.Add(10*time.Minute)) {
		logger.Infof(ctx, "✓ 节点创建时间在重装时间范围内，确认重装发生")
	} else {
		logger.Warnf(ctx, "⚠ 节点创建时间不在预期范围内，可能重装未发生")
	}

	// 2. 检查节点的系统信息
	logger.Infof(ctx, "检查节点系统信息...")
	logger.Infof(ctx, "  操作系统: %s", node.Status.NodeInfo.OSImage)
	logger.Infof(ctx, "  内核版本: %s", node.Status.NodeInfo.KernelVersion)
	logger.Infof(ctx, "  容器运行时: %s", node.Status.NodeInfo.ContainerRuntimeVersion)
	logger.Infof(ctx, "  kubelet版本: %s", node.Status.NodeInfo.KubeletVersion)

	// 3. 检查节点的启动时间
	bootTime, exists := node.Annotations["node.alpha.kubernetes.io/boot-time"]
	if exists {
		logger.Infof(ctx, "  节点启动时间: %s", bootTime)
	}

	// 4. 检查实例状态确认使用了正确的镜像
	instance, err := c.base.CCEClient.GetInstance(ctx, c.base.ClusterID, c.targetInstanceID, nil)
	if err != nil {
		return fmt.Errorf("获取实例状态失败: %v", err)
	}

	currentImageID := instance.Instance.Spec.ImageID
	logger.Infof(ctx, "原始镜像ID: %s", c.originalImageID)
	logger.Infof(ctx, "当前镜像ID: %s", currentImageID)

	if currentImageID == c.originalImageID {
		logger.Infof(ctx, "✓ 镜像ID一致，确认使用了原始镜像")
	} else {
		return fmt.Errorf("镜像ID不一致，重装可能使用了错误的镜像")
	}

	logger.Infof(ctx, "✓ 重装验证通过")
	return nil
}

// checkNetworkResourceStatus 检查网络资源状态
func (c *checkNodeMoveOutReinstallMoveIn) checkNetworkResourceStatus(ctx context.Context) error {
	logger.Infof(ctx, "检查节点网络资源状态...")

	// 检查NRS状态
	if err := c.checkNRSStatus(ctx); err != nil {
		return fmt.Errorf("检查NRS状态失败: %v", err)
	}

	// 检查ENI状态
	if err := c.checkENIStatus(ctx); err != nil {
		return fmt.Errorf("检查ENI状态失败: %v", err)
	}

	logger.Infof(ctx, "网络资源状态检查完成")
	return nil
}

// checkNRSStatus 检查NRS状态
func (c *checkNodeMoveOutReinstallMoveIn) checkNRSStatus(ctx context.Context) error {
	logger.Infof(ctx, "检查NRS状态...")

	// 获取当前NRS列表
	nrsList, err := c.base.KubeClient.ListNrs(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取NRS列表失败: %v", err)
	}

	// 查找目标节点的NRS
	var targetNRS *ccetypes.NetworkResourceSet
	for _, nrs := range nrsList.Items {
		if strings.Contains(nrs.Name, c.targetNodeName) ||
			strings.Contains(nrs.Name, c.targetMachineID) {
			targetNRS = &nrs
			break
		}
	}

	if targetNRS == nil {
		return fmt.Errorf("未找到目标节点的NRS")
	}

	logger.Infof(ctx, "找到目标节点NRS: %s", targetNRS.Name)

	// 验证NRS状态正常 - 检查ENI资源状态
	if len(targetNRS.Status.Enis) == 0 {
		return fmt.Errorf("NRS中没有ENI资源")
	}

	for eniID, eniStatus := range targetNRS.Status.Enis {
		logger.Infof(ctx, "ENI %s 状态: %s", eniID, eniStatus.CceStatus)
		if eniStatus.CceStatus != "available" {
			logger.Warnf(ctx, "ENI %s 状态不是available: %s", eniID, eniStatus.CceStatus)
		}
	}

	logger.Infof(ctx, "NRS状态检查完成，ENI数量: %d", len(targetNRS.Status.Enis))
	return nil
}

// checkENIStatus 检查ENI状态
func (c *checkNodeMoveOutReinstallMoveIn) checkENIStatus(ctx context.Context) error {
	logger.Infof(ctx, "检查ENI状态...")

	// 获取节点ENI列表
	eniList, err := c.base.BCCClient.ListInstanceEnis(ctx, c.targetMachineID, nil)
	if err != nil {
		return fmt.Errorf("获取ENI列表失败: %v", err)
	}

	if len(eniList.EniList) == 0 {
		return fmt.Errorf("节点没有ENI网卡")
	}

	logger.Infof(ctx, "节点ENI数量: %d", len(eniList.EniList))

	// 检查每个ENI的状态
	for i, eniInfo := range eniList.EniList {
		// 检查是否是主网卡
		isPrimary := false
		if len(eniInfo.PrivateIPSet) > 0 {
			isPrimary = eniInfo.PrivateIPSet[0].Primary
		}

		logger.Infof(ctx, "ENI #%d: ID=%s, 状态=%s, 主网卡=%v",
			i+1, eniInfo.ENIID, eniInfo.Status, isPrimary)

		if eniInfo.Status != "inuse" {
			logger.Warnf(ctx, "ENI %s 状态不是inuse: %s", eniInfo.ENIID, eniInfo.Status)
		}
	}

	logger.Infof(ctx, "ENI状态检查完成")
	return nil
}

// createAndVerifyENI 创建ENI网卡并验证不被纳管
func (c *checkNodeMoveOutReinstallMoveIn) createAndVerifyENI(ctx context.Context) error {
	logger.Infof(ctx, "创建ENI网卡并验证不被CCE纳管...")

	// 从集群配置中获取ENI子网信息
	clusterInfo, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		return fmt.Errorf("获取集群信息失败: %v", err)
	}

	// 获取ENI子网列表
	eniSubnets := clusterInfo.Cluster.Spec.ContainerNetworkConfig.ENIVPCSubnetIDs
	if len(eniSubnets) == 0 {
		return fmt.Errorf("集群未配置ENI子网")
	}

	// 获取第一个可用区的第一个子网
	var subnetID string
	for _, subnets := range eniSubnets {
		if len(subnets) > 0 {
			subnetID = subnets[0]
			break
		}
	}

	if subnetID == "" {
		return fmt.Errorf("未找到可用的ENI子网")
	}

	// 获取ENI默认安全组
	if len(clusterInfo.Cluster.Spec.ENIDefaultSecurityGroups) == 0 {
		return fmt.Errorf("集群未配置ENI默认安全组")
	}
	securityGroupIDs := make([]string, 0)
	for _, sg := range clusterInfo.Cluster.Spec.ENIDefaultSecurityGroups {
		securityGroupIDs = append(securityGroupIDs, sg.ID)
	}

	// 创建ENI网卡
	createReq := &eni.CreateENIArgs{
		Name:             fmt.Sprintf("test-eni-%d", time.Now().Unix()),
		SubnetID:         subnetID,
		Description:      "测试用ENI网卡",
		SecurityGroupIDs: securityGroupIDs,                  // 使用集群配置的ENI默认安全组
		PrivateIPSet:     []*eni.PrivateIP{{Primary: true}}, // 自动分配主IP
	}

	eniID, err := c.base.ENIClient.CreateENI(ctx, createReq, nil)
	if err != nil {
		return fmt.Errorf("创建ENI失败: %v", err)
	}

	logger.Infof(ctx, "成功创建ENI: %s", eniID)
	c.createdENIIDs = append(c.createdENIIDs, eniID)

	// 等待ENI创建完成
	time.Sleep(10 * time.Second)

	// 挂载ENI到目标节点
	err = c.base.ENIClient.AttachENI(ctx, &eni.AttachENIArgs{
		ENIID:      eniID,
		InstanceID: c.targetMachineID,
	}, nil)
	if err != nil {
		return fmt.Errorf("挂载ENI到节点失败: %v", err)
	}

	logger.Infof(ctx, "成功挂载ENI %s 到节点 %s", eniID, c.targetMachineID)

	// 等待挂载完成
	time.Sleep(30 * time.Second)

	// 验证ENI不被CCE纳管
	if err := c.verifyENINotManaged(ctx, eniID); err != nil {
		return fmt.Errorf("验证ENI不被纳管失败: %v", err)
	}

	logger.Infof(ctx, "ENI创建和验证完成")
	return nil
}

// verifyENINotManaged 验证ENI不被CCE纳管
func (c *checkNodeMoveOutReinstallMoveIn) verifyENINotManaged(ctx context.Context, eniID string) error {
	logger.Infof(ctx, "验证ENI %s 不被CCE纳管...", eniID)

	// 获取目标节点的NRS
	nrsList, err := c.base.KubeClient.ListNrs(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取NRS列表失败: %v", err)
	}

	var targetNRS *ccetypes.NetworkResourceSet
	for _, nrs := range nrsList.Items {
		if strings.Contains(nrs.Name, c.targetNodeName) ||
			strings.Contains(nrs.Name, c.targetMachineID) {
			targetNRS = &nrs
			break
		}
	}

	if targetNRS == nil {
		return fmt.Errorf("未找到目标节点的NRS")
	}

	// 检查NRS中是否包含新创建的ENI
	// NRS中不直接包含ENI ID，而是包含子网信息，这里我们检查NRS的状态中是否有新的ENI
	for eniSpecID := range targetNRS.Status.Enis {
		if eniSpecID == eniID {
			return fmt.Errorf("ENI %s 被CCE错误地纳管了，出现在NRS中", eniID)
		}
	}

	logger.Infof(ctx, "✓ ENI %s 未被CCE纳管，验证通过", eniID)

	// 额外检查：获取ENI详情确认其状态
	eniDetail, err := c.base.ENIClient.StatENI(ctx, eniID, nil)
	if err != nil {
		logger.Warnf(ctx, "获取ENI详情失败: %v", err)
		return nil
	}

	logger.Infof(ctx, "ENI详情: 状态=%s, 挂载的实例=%s", eniDetail.Status, eniDetail.InstanceID)

	if eniDetail.Status == "inuse" && eniDetail.InstanceID == c.targetMachineID {
		logger.Infof(ctx, "✓ ENI正确挂载到节点但未被CCE纳管")
		return nil
	}

	return fmt.Errorf("ENI状态异常: 状态=%s, 挂载实例=%s", eniDetail.Status, eniDetail.InstanceID)
}

func (c *checkNodeMoveOutReinstallMoveIn) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理资源...")

	// 清理创建的ENI网卡
	for _, eniID := range c.createdENIIDs {
		logger.Infof(ctx, "清理ENI: %s", eniID)

		// 先尝试卸载ENI
		if err := c.base.ENIClient.DetachENI(ctx, &eni.DetachENIArgs{
			ENIID:      eniID,
			InstanceID: c.targetMachineID,
		}, nil); err != nil {
			logger.Warnf(ctx, "卸载ENI失败: %v", err)
		}

		// 等待卸载完成
		time.Sleep(10 * time.Second)

		// 删除ENI
		if err := c.base.ENIClient.DeleteENI(ctx, eniID, nil); err != nil {
			logger.Warnf(ctx, "删除ENI失败: %v", err)
		}
	}

	logger.Infof(ctx, "资源清理完成")
	return nil
}

func (c *checkNodeMoveOutReinstallMoveIn) Continue(ctx context.Context) bool {
	return true
}

func (c *checkNodeMoveOutReinstallMoveIn) ConfigFormat() string {
	return ""
}
