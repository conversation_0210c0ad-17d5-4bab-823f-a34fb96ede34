/*
PSTS IP 测试用例

功能描述：
测试主网卡辅助IP的功能，使得校验集群在开启PSTS时子网信息中存在指定子网。

测试流程：
1. 使用 registry.baidubce.com/cce/nginx-alpine-go:latest 镜像创建用于测试的pod
2. 需要在配置case中设置 config: subnetId，如果没有需要报错
3. 在确认当前集群没有指定子网后，创建对应的PSTS以及STS，来使用PSTS的功能
4. 当STS确认创建pod完成后，确认是否通过在集群中 get subnet，获取到PSTS中配置的子网信息

预期结果：
每一步请确认对应的执行结果，如果能够顺利执行并且按照每一步提供的结果，则是符合预期。

注意事项：
此测试会创建测试资源，测试完成后会自动清理所有创建的资源。
*/

package network

import (
	"context"
	"fmt"
	"time"
	"encoding/json"
	"strings"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/apimachinery/pkg/util/wait"
)

const (
	// PSTSIPCaseName - case 名字
	PSTSIPCaseName cases.CaseName = "PSTSIP"
)

// pstsIPConfig 测试配置
type pstsIPConfig struct {
	SubnetID string `json:"subnetId"`
}

// pstsIP 是用于测试PSTS IP功能的测试用例
type pstsIP struct {
	base                *cases.BaseClient
	config              pstsIPConfig
	testNamespace       string
	testPSTSName        string
	testStatefulSetName string
	testServiceName     string
	createdResources    []string
}

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), PSTSIPCaseName, NewPSTSIP)
}

// NewPSTSIP 创建一个新的PSTS IP测试用例
func NewPSTSIP(ctx context.Context) cases.Interface {
	return &pstsIP{
		testNamespace:       "default",
		testPSTSName:        "test-psts-ip",
		testStatefulSetName: "test-sts-ip",
		testServiceName:     "test-service-ip",
		createdResources:    make([]string, 0),
	}
}

// Name 返回测试用例名称
func (c *pstsIP) Name() cases.CaseName {
	return PSTSIPCaseName
}

// Desc 返回测试用例描述
func (c *pstsIP) Desc() string {
	return "测试主网卡辅助IP的功能，使得校验集群在开启PSTS时子网信息中存在指定子网"
}

// Init 初始化测试用例
func (c *pstsIP) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	logger.Infof(ctx, "初始化PSTS IP测试用例")
	if base == nil {
		return fmt.Errorf("base client is nil")
	}
	c.base = base

	// 解析配置
	if len(config) > 0 {
		if err := json.Unmarshal(config, &c.config); err != nil {
			return fmt.Errorf("解析配置失败: %v", err)
		}
	}

	// 检查必需的配置
	if c.config.SubnetID == "" {
		return fmt.Errorf("配置中缺少必需的subnetId参数")
	}

	logger.Infof(ctx, "使用子网ID: %s", c.config.SubnetID)
	return nil
}

// Check 执行测试
func (c *pstsIP) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)

	logger.Infof(ctx, "开始执行PSTS IP功能测试")

	// 1. 确认当前集群没有指定的子网
	if err := c.checkSubnetNotExists(ctx); err != nil {
		return resources, fmt.Errorf("检查子网状态失败: %v", err)
	}

	// 2. 创建PSTS资源
	if err := c.createPSTS(ctx); err != nil {
		return resources, fmt.Errorf("创建PSTS失败: %v", err)
	}

	// 3. 创建Service
	if err := c.createService(ctx); err != nil {
		return resources, fmt.Errorf("创建Service失败: %v", err)
	}

	// 4. 创建StatefulSet
	if err := c.createStatefulSet(ctx); err != nil {
		return resources, fmt.Errorf("创建StatefulSet失败: %v", err)
	}

	// 5. 等待StatefulSet就绪
	if err := c.waitForStatefulSetReady(ctx); err != nil {
		return resources, fmt.Errorf("等待StatefulSet就绪失败: %v", err)
	}

	// 6. 验证子网信息
	if err := c.verifySubnetExists(ctx); err != nil {
		return resources, fmt.Errorf("验证子网信息失败: %v", err)
	}

	logger.Infof(ctx, "PSTS IP功能测试完成")
	return resources, nil
}

// checkSubnetNotExists 确认当前集群没有指定的子网
func (c *pstsIP) checkSubnetNotExists(ctx context.Context) error {
	logger.Infof(ctx, "检查集群中是否已存在子网: %s", c.config.SubnetID)

	_, err := c.base.KubeClient.GetSubnet(ctx, c.config.SubnetID, &kube.GetOptions{})
	if err != nil {
		if kerrors.IsNotFound(err) {
			logger.Infof(ctx, "确认集群中不存在子网 %s，可以继续测试", c.config.SubnetID)
			return nil
		}
		// 如果是其他错误，可能是集群不支持Subnet CRD
		logger.Warnf(ctx, "检查子网时出现错误: %v，可能集群不支持PSTS功能", err)
		return fmt.Errorf("集群可能不支持PSTS功能，跳过测试: %v", err)
	}

	logger.Warnf(ctx, "集群中已存在子网 %s，继续测试", c.config.SubnetID)
	return nil
}

// createPSTS 创建PSTS资源
func (c *pstsIP) createPSTS(ctx context.Context) error {
	logger.Infof(ctx, "创建PSTS资源: %s", c.testPSTSName)

	// 首先检查PSTS是否已存在，如果存在则删除
	if err := c.cleanupExistingPSTS(ctx); err != nil {
		logger.Warnf(ctx, "清理已存在的PSTS失败: %v", err)
	}

	// 首先尝试创建子网资源
	if err := c.createSubnetIfNotExists(ctx); err != nil {
		return fmt.Errorf("创建子网资源失败: %v", err)
	}

	psts := &types.PodSubnetTopologySpread{
		TypeMeta: metav1.TypeMeta{
			APIVersion: "cce.baidubce.com/v2",
			Kind:       "PodSubnetTopologySpread",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.testPSTSName,
			Namespace: c.testNamespace,
			Labels: map[string]string{
				"test": "psts-ip",
			},
		},
		Spec: types.PodSubnetTopologySpreadSpec{
			Subnets: map[string][]types.CustomAllocation{
				c.config.SubnetID: {},
			},
			Strategy: &types.IPAllocationStrategy{
				ReleaseStrategy:      "Never",
				Type:                 "Fixed",
				EnableReuseIPAddress: true,
			},
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": "test-sts-ip",
				},
			},
		},
	}

	err := c.createPSTSResource(ctx, psts)
	if err != nil {
		return fmt.Errorf("创建PSTS资源失败: %v", err)
	}

	c.createdResources = append(c.createdResources, "psts:"+c.testPSTSName)
	logger.Infof(ctx, "PSTS资源创建成功: %s", c.testPSTSName)
	return nil
}

// cleanupExistingPSTS 清理已存在的PSTS资源
func (c *pstsIP) cleanupExistingPSTS(ctx context.Context) error {
	logger.Infof(ctx, "检查并清理已存在的PSTS资源: %s", c.testPSTSName)

	clientSet, err := c.base.KubeClient.NewDynamicClient("cce.baidubce.com", "v2", "podsubnettopologyspreads")
	if err != nil {
		return fmt.Errorf("创建动态客户端失败: %v", err)
	}

	// 检查PSTS是否存在
	_, err = clientSet.Namespace(c.testNamespace).Get(ctx, c.testPSTSName, metav1.GetOptions{})
	if err != nil {
		if kerrors.IsNotFound(err) {
			logger.Infof(ctx, "PSTS资源不存在，无需清理")
			return nil
		}
		return fmt.Errorf("检查PSTS资源失败: %v", err)
	}

	// 删除已存在的PSTS
	err = clientSet.Namespace(c.testNamespace).Delete(ctx, c.testPSTSName, metav1.DeleteOptions{})
	if err != nil {
		return fmt.Errorf("删除已存在的PSTS失败: %v", err)
	}

	logger.Infof(ctx, "已清理存在的PSTS资源: %s", c.testPSTSName)

	// 等待资源删除完成
	time.Sleep(10 * time.Second)
	return nil
}

// createSubnetIfNotExists 如果子网不存在则创建
func (c *pstsIP) createSubnetIfNotExists(ctx context.Context) error {
	logger.Infof(ctx, "检查并创建子网资源: %s", c.config.SubnetID)

	// 检查子网是否已存在
	_, err := c.base.KubeClient.GetSubnet(ctx, c.config.SubnetID, &kube.GetOptions{})
	if err == nil {
		logger.Infof(ctx, "子网 %s 已存在，无需创建", c.config.SubnetID)
		return nil
	}

	if !kerrors.IsNotFound(err) {
		return fmt.Errorf("检查子网失败: %v", err)
	}

	// 创建子网资源
	subnet := &types.Subnet{
		TypeMeta: metav1.TypeMeta{
			APIVersion: "cce.baidubce.com/v1",
			Kind:       "Subnet",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: c.config.SubnetID,
			Labels: map[string]string{
				"test": "psts-ip",
			},
		},
		Spec: types.SubnetSpec{
			ID:               c.config.SubnetID,
			Name:             "test-subnet-" + c.config.SubnetID,
			AvailabilityZone: "zoneD", // 使用默认可用区
			CIDR:             "********/24", // 使用测试CIDR
			VPCID:            "vpc-84tvqpjuzd3k", // 从集群信息中获取的VPC ID
			SubnetType:       "normal",
			Exclusive:        false,
		},
	}

	err = c.createSubnetResource(ctx, subnet)
	if err != nil {
		return fmt.Errorf("创建子网资源失败: %v", err)
	}

	c.createdResources = append(c.createdResources, "subnet:"+c.config.SubnetID)
	logger.Infof(ctx, "子网资源创建成功: %s", c.config.SubnetID)
	return nil
}

// createSubnetResource 使用动态客户端创建Subnet资源
func (c *pstsIP) createSubnetResource(ctx context.Context, subnet *types.Subnet) error {
	clientSet, err := c.base.KubeClient.NewDynamicClient("cce.baidubce.com", "v1", "subnets")
	if err != nil {
		return fmt.Errorf("创建动态客户端失败: %v", err)
	}

	// 转换为unstructured对象
	unstructuredObj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(subnet)
	if err != nil {
		return fmt.Errorf("转换Subnet为unstructured失败: %v", err)
	}

	// 创建资源
	_, err = clientSet.Create(ctx, &unstructured.Unstructured{Object: unstructuredObj}, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建Subnet失败: %v", err)
	}

	return nil
}

// createPSTSResource 使用动态客户端创建PSTS资源
func (c *pstsIP) createPSTSResource(ctx context.Context, psts *types.PodSubnetTopologySpread) error {
	clientSet, err := c.base.KubeClient.NewDynamicClient("cce.baidubce.com", "v2", "podsubnettopologyspreads")
	if err != nil {
		return fmt.Errorf("创建动态客户端失败: %v", err)
	}

	// 转换为unstructured对象
	unstructuredObj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(psts)
	if err != nil {
		return fmt.Errorf("转换PSTS为unstructured失败: %v", err)
	}

	// 创建资源
	_, err = clientSet.Namespace(c.testNamespace).Create(ctx, &unstructured.Unstructured{Object: unstructuredObj}, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建PSTS失败: %v", err)
	}

	return nil
}

// createService 创建Service
func (c *pstsIP) createService(ctx context.Context) error {
	logger.Infof(ctx, "创建Service: %s", c.testServiceName)

	// 先删除已存在的Service
	err := c.base.K8SClient.CoreV1().Services(c.testNamespace).Delete(ctx, c.testServiceName, metav1.DeleteOptions{})
	if err != nil && !kerrors.IsNotFound(err) {
		logger.Warnf(ctx, "删除已存在的Service失败: %v", err)
	}

	service := &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.testServiceName,
			Namespace: c.testNamespace,
			Labels: map[string]string{
				"test": "psts-ip",
			},
		},
		Spec: corev1.ServiceSpec{
			Selector: map[string]string{
				"app": "test-sts-ip",
			},
			Ports: []corev1.ServicePort{
				{
					Name:       "web",
					Port:       80,
					TargetPort: intstr.FromInt(80),
					Protocol:   corev1.ProtocolTCP,
				},
			},
			ClusterIP: "None", // Headless service for StatefulSet
		},
	}

	_, err = c.base.K8SClient.CoreV1().Services(c.testNamespace).Create(ctx, service, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建Service失败: %v", err)
	}

	c.createdResources = append(c.createdResources, "service:"+c.testServiceName)
	logger.Infof(ctx, "Service创建成功: %s", c.testServiceName)
	return nil
}

// createStatefulSet 创建StatefulSet
func (c *pstsIP) createStatefulSet(ctx context.Context) error {
	logger.Infof(ctx, "创建StatefulSet: %s", c.testStatefulSetName)

	// 先删除已存在的StatefulSet
	err := c.base.K8SClient.AppsV1().StatefulSets(c.testNamespace).Delete(ctx, c.testStatefulSetName, metav1.DeleteOptions{})
	if err != nil && !kerrors.IsNotFound(err) {
		logger.Warnf(ctx, "删除已存在的StatefulSet失败: %v", err)
	}

	// 等待删除完成
	time.Sleep(10 * time.Second)

	statefulSet := &appsv1.StatefulSet{
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.testStatefulSetName,
			Namespace: c.testNamespace,
			Labels: map[string]string{
				"test": "psts-ip",
			},
		},
		Spec: appsv1.StatefulSetSpec{
			Replicas:    pstsIPInt32Ptr(1),
			ServiceName: c.testServiceName,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app":                           "test-sts-ip",
					"k8snet.iqiyi.com/staticip":     "true",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app":                           "test-sts-ip",
						"k8snet.iqiyi.com/staticip":     "true",
					},
				},
				Spec: corev1.PodSpec{
					RestartPolicy: corev1.RestartPolicyAlways,
					Containers: []corev1.Container{
						{
							Name:            "nginx",
							Image:           "registry.baidubce.com/cce/nginx-alpine-go:latest",
							ImagePullPolicy: corev1.PullAlways,
							Ports: []corev1.ContainerPort{
								{
									ContainerPort: 80,
									Name:          "web",
									Protocol:      corev1.ProtocolTCP,
								},
							},
							LivenessProbe: &corev1.Probe{
								Handler: corev1.Handler{
									HTTPGet: &corev1.HTTPGetAction{
										Path: "/",
										Port: intstr.FromInt(80),
									},
								},
								InitialDelaySeconds: 20,
								TimeoutSeconds:      5,
								PeriodSeconds:       5,
								FailureThreshold:    3,
							},
							ReadinessProbe: &corev1.Probe{
								Handler: corev1.Handler{
									HTTPGet: &corev1.HTTPGetAction{
										Path: "/",
										Port: intstr.FromInt(80),
									},
								},
								InitialDelaySeconds: 5,
								TimeoutSeconds:      1,
								PeriodSeconds:       5,
								FailureThreshold:    3,
							},
						},
					},
				},
			},
			PodManagementPolicy: appsv1.OrderedReadyPodManagement,
			UpdateStrategy: appsv1.StatefulSetUpdateStrategy{
				Type: appsv1.RollingUpdateStatefulSetStrategyType,
				RollingUpdate: &appsv1.RollingUpdateStatefulSetStrategy{
					Partition: pstsIPInt32Ptr(0),
				},
			},
		},
	}

	_, err = c.base.K8SClient.AppsV1().StatefulSets(c.testNamespace).Create(ctx, statefulSet, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建StatefulSet失败: %v", err)
	}

	c.createdResources = append(c.createdResources, "statefulset:"+c.testStatefulSetName)
	logger.Infof(ctx, "StatefulSet创建成功: %s", c.testStatefulSetName)
	return nil
}

// waitForStatefulSetReady 等待StatefulSet就绪
func (c *pstsIP) waitForStatefulSetReady(ctx context.Context) error {
	logger.Infof(ctx, "等待StatefulSet就绪: %s", c.testStatefulSetName)

	// 创建StatefulSet资源管理器
	var statefulSet resource.K8SStatefulSet
	err := statefulSet.NewK8SStatefulSet(ctx, c.base, c.testNamespace, c.testStatefulSetName)
	if err != nil {
		return fmt.Errorf("创建StatefulSet资源管理器失败: %v", err)
	}

	// 等待StatefulSet就绪
	err = wait.PollImmediate(10*time.Second, 5*time.Minute, func() (bool, error) {
		sts, err := c.base.K8SClient.AppsV1().StatefulSets(c.testNamespace).Get(ctx, c.testStatefulSetName, metav1.GetOptions{})
		if err != nil {
			return false, err
		}

		logger.Infof(ctx, "StatefulSet状态: Ready=%d/%d", sts.Status.ReadyReplicas, *sts.Spec.Replicas)

		return sts.Status.ReadyReplicas == *sts.Spec.Replicas, nil
	})

	if err != nil {
		return fmt.Errorf("等待StatefulSet就绪超时: %v", err)
	}

	// 检查Pod状态
	pods, err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "app=test-sts-ip",
	})
	if err != nil {
		return fmt.Errorf("获取Pod列表失败: %v", err)
	}

	for _, pod := range pods.Items {
		if pod.Status.Phase == corev1.PodRunning && pod.Status.PodIP != "" {
			logger.Infof(ctx, "Pod %s 运行正常，IP: %s", pod.Name, pod.Status.PodIP)
		} else {
			logger.Warnf(ctx, "Pod %s 状态异常: Phase=%s, IP=%s", pod.Name, pod.Status.Phase, pod.Status.PodIP)
		}
	}

	logger.Infof(ctx, "StatefulSet就绪: %s", c.testStatefulSetName)
	return nil
}

// verifySubnetExists 验证子网信息存在
func (c *pstsIP) verifySubnetExists(ctx context.Context) error {
	logger.Infof(ctx, "验证集群中是否存在子网: %s", c.config.SubnetID)

	// 等待一段时间让PSTS生效
	time.Sleep(30 * time.Second)

	// 尝试获取子网信息
	subnet, err := c.base.KubeClient.GetSubnet(ctx, c.config.SubnetID, &kube.GetOptions{})
	if err != nil {
		if kerrors.IsNotFound(err) {
			return fmt.Errorf("验证失败：集群中仍然不存在子网 %s", c.config.SubnetID)
		}
		return fmt.Errorf("获取子网信息失败: %v", err)
	}

	logger.Infof(ctx, "验证成功：在集群中找到了子网 %s", c.config.SubnetID)
	logger.Infof(ctx, "子网详细信息:")
	logger.Infof(ctx, "  名称: %s", subnet.Name)
	logger.Infof(ctx, "  CIDR: %s", subnet.Spec.CIDR)
	logger.Infof(ctx, "  可用区: %s", subnet.Spec.AvailabilityZone)
	logger.Infof(ctx, "  可用IP数量: %d", subnet.Status.AvailableIPNum)
	logger.Infof(ctx, "  是否启用: %t", subnet.Status.Enable)

	// 验证PSTS状态
	if err := c.verifyPSTSStatus(ctx); err != nil {
		return fmt.Errorf("验证PSTS状态失败: %v", err)
	}

	return nil
}

// verifyPSTSStatus 验证PSTS状态
func (c *pstsIP) verifyPSTSStatus(ctx context.Context) error {
	logger.Infof(ctx, "验证PSTS状态: %s", c.testPSTSName)

	// 使用动态客户端获取PSTS
	clientSet, err := c.base.KubeClient.NewDynamicClient("cce.baidubce.com", "v2", "podsubnettopologyspreads")
	if err != nil {
		logger.Warnf(ctx, "创建动态客户端失败: %v，跳过PSTS状态验证", err)
		return nil
	}

	pstsUnstructured, err := clientSet.Namespace(c.testNamespace).Get(ctx, c.testPSTSName, metav1.GetOptions{})
	if err != nil {
		logger.Warnf(ctx, "获取PSTS失败: %v，可能PSTS功能不完全支持，但基本功能已验证", err)
		return nil
	}

	logger.Infof(ctx, "PSTS状态信息:")
	logger.Infof(ctx, "  名称: %s", pstsUnstructured.GetName())
	logger.Infof(ctx, "  命名空间: %s", pstsUnstructured.GetNamespace())
	logger.Infof(ctx, "  创建时间: %s", pstsUnstructured.GetCreationTimestamp())

	// 尝试获取状态信息
	status, found, err := unstructured.NestedMap(pstsUnstructured.Object, "status")
	if err != nil {
		logger.Warnf(ctx, "解析PSTS状态失败: %v", err)
		return nil
	}

	if found && status != nil {
		logger.Infof(ctx, "  状态详情: %+v", status)
	} else {
		logger.Infof(ctx, "  PSTS状态信息暂未更新")
	}

	return nil
}

// Clean 清理测试资源
func (c *pstsIP) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理PSTS IP测试资源")

	// 按照创建的逆序删除资源
	for i := len(c.createdResources) - 1; i >= 0; i-- {
		resource := c.createdResources[i]
		if err := c.deleteResource(ctx, resource); err != nil {
			logger.Warnf(ctx, "删除资源失败: %s, 错误: %v", resource, err)
		}
	}

	logger.Infof(ctx, "PSTS IP测试资源清理完成")
	return nil
}

// deleteResource 删除指定资源
func (c *pstsIP) deleteResource(ctx context.Context, resource string) error {
	parts := strings.Split(resource, ":")
	if len(parts) != 2 {
		return fmt.Errorf("无效的资源格式: %s", resource)
	}

	resourceType := parts[0]
	resourceName := parts[1]

	switch resourceType {
	case "statefulset":
		err := c.base.K8SClient.AppsV1().StatefulSets(c.testNamespace).Delete(ctx, resourceName, metav1.DeleteOptions{})
		if err != nil && !kerrors.IsNotFound(err) {
			return fmt.Errorf("删除StatefulSet失败: %v", err)
		}
		logger.Infof(ctx, "StatefulSet %s 已删除", resourceName)

	case "service":
		err := c.base.K8SClient.CoreV1().Services(c.testNamespace).Delete(ctx, resourceName, metav1.DeleteOptions{})
		if err != nil && !kerrors.IsNotFound(err) {
			return fmt.Errorf("删除Service失败: %v", err)
		}
		logger.Infof(ctx, "Service %s 已删除", resourceName)

	case "psts":
		err := c.deletePSTSResource(ctx, resourceName)
		if err != nil && !kerrors.IsNotFound(err) {
			return fmt.Errorf("删除PSTS失败: %v", err)
		}
		logger.Infof(ctx, "PSTS %s 已删除", resourceName)

	case "subnet":
		err := c.deleteSubnetResource(ctx, resourceName)
		if err != nil && !kerrors.IsNotFound(err) {
			return fmt.Errorf("删除Subnet失败: %v", err)
		}
		logger.Infof(ctx, "Subnet %s 已删除", resourceName)

	default:
		return fmt.Errorf("未知的资源类型: %s", resourceType)
	}

	return nil
}

// deletePSTSResource 删除PSTS资源
func (c *pstsIP) deletePSTSResource(ctx context.Context, name string) error {
	clientSet, err := c.base.KubeClient.NewDynamicClient("cce.baidubce.com", "v2", "podsubnettopologyspreads")
	if err != nil {
		return fmt.Errorf("创建动态客户端失败: %v", err)
	}

	err = clientSet.Namespace(c.testNamespace).Delete(ctx, name, metav1.DeleteOptions{})
	if err != nil {
		return fmt.Errorf("删除PSTS失败: %v", err)
	}

	return nil
}

// deleteSubnetResource 删除Subnet资源
func (c *pstsIP) deleteSubnetResource(ctx context.Context, name string) error {
	clientSet, err := c.base.KubeClient.NewDynamicClient("cce.baidubce.com", "v1", "subnets")
	if err != nil {
		return fmt.Errorf("创建动态客户端失败: %v", err)
	}

	err = clientSet.Delete(ctx, name, metav1.DeleteOptions{})
	if err != nil {
		return fmt.Errorf("删除Subnet失败: %v", err)
	}

	return nil
}

// Continue 返回是否继续执行
func (c *pstsIP) Continue(ctx context.Context) bool {
	return true
}

// ConfigFormat 返回配置格式
func (c *pstsIP) ConfigFormat() string {
	return `{
  "subnetId": "sbn-6533cgkkx2ic"
}`
}

// 辅助函数
func pstsIPInt32Ptr(i int32) *int32 {
	return &i
}
