package network

import (
	"context"
	"fmt"
	"net"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/remotecommand"
)

const (
	// CheckUnderlayRDMAConnectivityCaseName - case 名字
	CheckUnderlayRDMAConnectivityCaseName cases.CaseName = "CheckUnderlayRDMAConnectivity"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CheckUnderlayRDMAConnectivityCaseName, NewCheckUnderlayRDMAConnectivity)
}

type checkUnderlayRDMAConnectivity struct {
	base       *cases.BaseClient
	kubeClient *kube.Client
}

// NewCheckUnderlayRDMAConnectivity - 测试案例
func NewCheckUnderlayRDMAConnectivity(ctx context.Context) cases.Interface {
	return &checkUnderlayRDMAConnectivity{}
}

func (c *checkUnderlayRDMAConnectivity) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base is nil")
	}

	c.base = base
	c.kubeClient = base.KubeClient
	return nil
}

func (c *checkUnderlayRDMAConnectivity) Name() cases.CaseName {
	return CheckUnderlayRDMAConnectivityCaseName
}

func (c *checkUnderlayRDMAConnectivity) Desc() string {
	return "测试集群中Underlay RDMA网络的连通性"
}

func (c *checkUnderlayRDMAConnectivity) Check(ctx context.Context) ([]cases.Resource, error) {
	logger.Infof(ctx, "清理可能存在的残留资源...")
	if err := c.cleanResources(ctx); err != nil {
		return nil, fmt.Errorf("清理资源失败: %v", err)
	}

	logger.Infof(ctx, "开始创建使用Underlay RDMA资源的deployment...")
	if err := c.createRDMADeployment(ctx); err != nil {
		return nil, fmt.Errorf("创建RDMA deployment失败: %v", err)
	}

	logger.Infof(ctx, "等待deployment就绪...")
	if err := c.waitDeploymentReady(ctx); err != nil {
		return nil, fmt.Errorf("等待deployment就绪失败: %v", err)
	}

	logger.Infof(ctx, "获取pod列表并验证...")
	pods, err := c.getPodList(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取pod列表失败: %v", err)
	}

	logger.Infof(ctx, "验证pods使用RDMA资源...")
	if err := c.verifyRDMAResource(ctx, pods); err != nil {
		return nil, fmt.Errorf("验证RDMA资源失败: %v", err)
	}

	logger.Infof(ctx, "测试RDMA连通性...")
	if err := c.testRDMAConnectivity(ctx, pods[0], pods[1]); err != nil {
		return nil, fmt.Errorf("RDMA连通性测试失败: %v", err)
	}

	logger.Infof(ctx, "开始清理RDMA连通性测试资源...")
	if err := c.cleanResources(ctx); err != nil {
		return nil, fmt.Errorf("清理资源失败: %v", err)
	}

	return nil, nil
}

func (c *checkUnderlayRDMAConnectivity) Clean(ctx context.Context) error {
	return c.cleanResources(ctx)
}

func (c *checkUnderlayRDMAConnectivity) Continue(ctx context.Context) bool {
	return true
}

func (c *checkUnderlayRDMAConnectivity) ConfigFormat() string {
	return ""
}

// cleanResources 清理测试资源
func (c *checkUnderlayRDMAConnectivity) cleanResources(ctx context.Context) error {
	deployments, err := c.kubeClient.ClientSet.AppsV1().Deployments("default").List(ctx, metav1.ListOptions{
		LabelSelector: "app=underlay-rdma-test",
	})
	if err != nil {
		return fmt.Errorf("获取deployment列表失败: %v", err)
	}

	for _, deploy := range deployments.Items {
		err = c.kubeClient.ClientSet.AppsV1().Deployments("default").Delete(ctx, deploy.Name, metav1.DeleteOptions{})
		if err != nil {
			return fmt.Errorf("删除deployment %s 失败: %v", deploy.Name, err)
		}
		logger.Infof(ctx, "成功删除deployment: %s", deploy.Name)
	}

	return nil
}

// createRDMADeployment 创建使用RDMA资源的deployment
func (c *checkUnderlayRDMAConnectivity) createRDMADeployment(ctx context.Context) error {
	replicas := int32(2)
	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name: "underlay-rdma-test",
			Labels: map[string]string{
				"app": "underlay-rdma-test",
			},
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": "underlay-rdma-test",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": "underlay-rdma-test",
					},
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						{
							Name:            "rdma-test",
							Image:           "registry.baidubce.com/cce-plugin-dev/mlnx_ofed_linux-5.3-*******-centos7.9:latest",
							ImagePullPolicy: corev1.PullIfNotPresent,
							Command:         []string{"/bin/sh", "-c", "sleep 600000"},
							SecurityContext: &corev1.SecurityContext{
								Capabilities: &corev1.Capabilities{
									Add: []corev1.Capability{"IPC_LOCK"},
								},
							},
							Resources: corev1.ResourceRequirements{
								Requests: corev1.ResourceList{
									"rdma/hca": resource.MustParse("1"),
								},
								Limits: corev1.ResourceList{
									"rdma/hca": resource.MustParse("1"),
								},
							},
						},
					},
				},
			},
		},
	}

	_, err := c.kubeClient.ClientSet.AppsV1().Deployments("default").Create(ctx, deployment, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建deployment失败: %v", err)
	}

	logger.Infof(ctx, "成功创建Underlay RDMA deployment: %s", deployment.Name)
	return nil
}

// waitDeploymentReady 等待deployment就绪
func (c *checkUnderlayRDMAConnectivity) waitDeploymentReady(ctx context.Context) error {
	return wait.PollImmediate(5*time.Second, 5*time.Minute, func() (bool, error) {
		deployment, err := c.kubeClient.ClientSet.AppsV1().Deployments("default").Get(ctx, "underlay-rdma-test", metav1.GetOptions{})
		if err != nil {
			return false, fmt.Errorf("获取deployment失败: %v", err)
		}

		if deployment.Status.ReadyReplicas == *deployment.Spec.Replicas {
			logger.Infof(ctx, "Deployment就绪，ReadyReplicas: %d/%d", deployment.Status.ReadyReplicas, *deployment.Spec.Replicas)
			return true, nil
		}

		logger.Infof(ctx, "等待deployment就绪中... ReadyReplicas: %d/%d", deployment.Status.ReadyReplicas, *deployment.Spec.Replicas)
		return false, nil
	})
}

// getPodList 获取测试pod列表
func (c *checkUnderlayRDMAConnectivity) getPodList(ctx context.Context) ([]corev1.Pod, error) {
	podList, err := c.kubeClient.ClientSet.CoreV1().Pods("default").List(ctx, metav1.ListOptions{
		LabelSelector: "app=underlay-rdma-test",
	})
	if err != nil {
		return nil, fmt.Errorf("获取pod列表失败: %v", err)
	}

	if len(podList.Items) != 2 {
		return nil, fmt.Errorf("pod数量不正确，期望2个，实际%d个", len(podList.Items))
	}

	return podList.Items, nil
}

// verifyRDMAResource 验证RDMA资源分配
func (c *checkUnderlayRDMAConnectivity) verifyRDMAResource(ctx context.Context, pods []corev1.Pod) error {
	for _, pod := range pods {
		if pod.Status.Phase != corev1.PodRunning {
			return fmt.Errorf("Pod %s 未处于Running状态", pod.Name)
		}

		// 验证RDMA资源分配
		if _, ok := pod.Spec.Containers[0].Resources.Limits["rdma/hca"]; !ok {
			return fmt.Errorf("Pod %s 未正确申请RDMA资源", pod.Name)
		}

		logger.Infof(ctx, "Pod %s 正确申请了RDMA资源，IP: %s", pod.Name, pod.Status.PodIP)
	}
	return nil
}

// execInPod 在pod中执行命令
func (c *checkUnderlayRDMAConnectivity) execInPod(ctx context.Context, namespace, podName, containerName string, command []string) (string, string, error) {
	fmt.Printf("在Pod %s 中执行命令: %v\n", podName, command)

	req := c.base.KubeClient.ClientSet.CoreV1().RESTClient().Post().
		Resource("pods").
		Name(podName).
		Namespace(namespace).
		SubResource("exec")

	req.VersionedParams(&corev1.PodExecOptions{
		Container: containerName,
		Command:   command,
		Stdin:     false,
		Stdout:    true,
		Stderr:    true,
		TTY:       false,
	}, scheme.ParameterCodec)

	exec, err := remotecommand.NewSPDYExecutor(c.base.KubeClient.RestConfig, "POST", req.URL())
	if err != nil {
		return "", "", fmt.Errorf("创建SPDY executor失败: %v", err)
	}

	var stdout, stderr strings.Builder
	err = exec.Stream(remotecommand.StreamOptions{
		Stdout: &stdout,
		Stderr: &stderr,
	})

	return stdout.String(), stderr.String(), err
}

// parseGidsOutput 解析show_gids命令输出，返回设备名、index和IP地址
func (c *checkUnderlayRDMAConnectivity) parseGidsOutput(output string) ([]struct {
	device string
	index  string
	ip     string
}, error) {
	var devices []struct {
		device string
		index  string
		ip     string
	}

	lines := strings.Split(output, "\n")
	for _, line := range lines[2:] { // 跳过标题行
		fields := strings.Fields(line)
		if len(fields) >= 7 && fields[5] == "v2" && fields[4] != "" && strings.Contains(fields[4], ".") {
			devices = append(devices, struct {
				device string
				index  string
				ip     string
			}{
				device: fields[0],
				index:  fields[2],
				ip:     fields[4],
			})
		}
	}

	if len(devices) == 0 {
		return nil, fmt.Errorf("未找到带IPv4地址的RDMA设备")
	}

	return devices, nil
}

// findMatchingDevice 在客户端设备列表中找到与服务端设备在同一网段的设备
func (c *checkUnderlayRDMAConnectivity) findMatchingDevice(serverDevices, clientDevices []struct {
	device string
	index  string
	ip     string
}) (serverDevice, clientDevice struct {
	device string
	index  string
	ip     string
}, err error) {
	for _, server := range serverDevices {
		_, serverNet, err := net.ParseCIDR(server.ip + "/24")
		if err != nil {
			continue
		}

		for _, client := range clientDevices {
			clientIP := net.ParseIP(client.ip)
			if clientIP == nil {
				continue
			}

			if serverNet.Contains(clientIP) {
				return server, client, nil
			}
		}
	}

	return struct {
			device string
			index  string
			ip     string
		}{}, struct {
			device string
			index  string
			ip     string
		}{}, fmt.Errorf("未找到匹配的设备对")
}

// testRDMAConnectivity 测试RDMA连通性
func (c *checkUnderlayRDMAConnectivity) testRDMAConnectivity(ctx context.Context, pod1, pod2 corev1.Pod) error {
	// 在pod2（服务端）上执行show_gids命令获取设备信息
	showGidsCmd2 := []string{
		"show_gids",
	}
	stdout2, stderr2, err := c.execInPod(ctx, pod2.Namespace, pod2.Name, pod2.Spec.Containers[0].Name, showGidsCmd2)
	if err != nil {
		return fmt.Errorf("在pod2上执行show_gids命令失败: %v, stderr: %s", err, stderr2)
	}
	fmt.Printf("Pod2 (服务端) show_gids输出:\n%s\n", stdout2)

	// 解析pod2的show_gids输出
	serverDevices, err := c.parseGidsOutput(stdout2)
	if err != nil {
		return fmt.Errorf("解析pod2 show_gids输出失败: %v", err)
	}

	// 在pod1（客户端）上执行show_gids命令获取设备信息
	showGidsCmd1 := []string{
		"show_gids",
	}
	stdout1, stderr1, err := c.execInPod(ctx, pod1.Namespace, pod1.Name, pod1.Spec.Containers[0].Name, showGidsCmd1)
	if err != nil {
		return fmt.Errorf("在pod1上执行show_gids命令失败: %v, stderr: %s", err, stderr1)
	}
	fmt.Printf("Pod1 (客户端) show_gids输出:\n%s\n", stdout1)

	// 解析pod1的show_gids输出
	clientDevices, err := c.parseGidsOutput(stdout1)
	if err != nil {
		return fmt.Errorf("解析pod1 show_gids输出失败: %v", err)
	}

	// 找到匹配的设备对
	serverDevice, clientDevice, err := c.findMatchingDevice(serverDevices, clientDevices)
	if err != nil {
		return fmt.Errorf("查找匹配的设备对失败: %v", err)
	}

	// 在pod2上启动ib_write_bw服务端（后台运行）
	ibWriteCmd := []string{
		"sh", "-c",
		fmt.Sprintf("ib_write_bw -d %s -x %s -q 16 --report_gbits > /dev/null 2>&1 &", serverDevice.device, serverDevice.index),
	}
	stdout2, stderr2, err = c.execInPod(ctx, pod2.Namespace, pod2.Name, pod2.Spec.Containers[0].Name, ibWriteCmd)
	if err != nil {
		return fmt.Errorf("在pod2上启动ib_write_bw服务端失败: %v, stderr: %s", err, stderr2)
	}
	fmt.Printf("Pod2 ib_write_bw服务端输出:\n%s\n", stdout2)

	// 等待服务端启动
	time.Sleep(2 * time.Second)

	// 在pod1上执行ib_write_bw客户端命令，连接到服务端IP
	ibWriteCmd = []string{
		"ib_write_bw",
		"-d", clientDevice.device,
		"-x", clientDevice.index,
		"-q", "16",
		"--report_gbits",
		serverDevice.ip,
	}
	stdout1, stderr1, err = c.execInPod(ctx, pod1.Namespace, pod1.Name, pod1.Spec.Containers[0].Name, ibWriteCmd)
	if err != nil {
		return fmt.Errorf("在pod1上执行ib_write_bw客户端命令失败: %v, stderr: %s", err, stderr1)
	}
	fmt.Printf("Pod1 ib_write_bw客户端输出:\n%s\n", stdout1)

	return nil
}
