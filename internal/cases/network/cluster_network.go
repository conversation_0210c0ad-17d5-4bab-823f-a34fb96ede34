/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  cluster_network
 * @Version: 1.0.0
 * @Date: 2020/11/16 5:17 下午
 */
package network

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/tools/remotecommand"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	ClusterNetworkCaseName cases.CaseName = "ClusterNetwork"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), ClusterNetworkCaseName, NewClusterNetwork)
}

type clusterNetwork struct {
	base *cases.BaseClient
}

// NewClusterNetwork - 测试案例
func NewClusterNetwork(ctx context.Context) cases.Interface {
	return &clusterNetwork{}
}

func (c *clusterNetwork) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base

	return nil
}

func (c *clusterNetwork) Name() cases.CaseName {
	return ClusterNetworkCaseName
}

func (c *clusterNetwork) Desc() string {
	return "检查集群网络状态"
}

func (c *clusterNetwork) Check(ctx context.Context) ([]cases.Resource, error) {
	// check dns
	running := false
	for i := 0; i < 20; i++ {
		// wait at most 100s for pod running
		<-time.After(5 * time.Second)
		got, err := c.base.K8SClient.CoreV1().Services("kube-system").Get(ctx, "kube-dns", metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "fail to get svc kube-system/kube-dns (will retry): %v", err)
			continue
		}
		if got.Spec.ClusterIP != "" {
			logger.Infof(ctx, "svc kube-system/kube-dns has been ready, stop check running")
			running = true
			break
		}
		logger.Infof(ctx, "svc kube-system/kube-dns is not running yet")
	}
	if !running {
		logger.Errorf(ctx, "error: svc kube-system/kube-dns cannot be running")
		return nil, errors.New("svc kube-system/kube-dns cannot be running")
	}

	// check core-dns
	running = false
	for i := 0; i < 20; i++ {
		// wait at most 100s for pod running
		<-time.After(5 * time.Second)
		got, err := c.base.K8SClient.AppsV1().Deployments("kube-system").Get(ctx, "coredns", metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "fail to get cpredns (will retry): %v", err)
			continue
		}
		if got.Status.ReadyReplicas > 0 {
			logger.Infof(ctx, "coredns has been running, stop check running")
			running = true
			break
		}
		logger.Infof(ctx, "coredns is not running yet, ready replicas: %d", got.Status.ReadyReplicas)
	}
	if !running {
		logger.Errorf(ctx, "error: coredns cannot be running")
		return nil, errors.New("coredns cannot be running")
	}

	namespace := "default"
	deploymentName := "nginx"
	serviceName := "nginx"
	testDeployment := &appsv1.Deployment{
		TypeMeta: metav1.TypeMeta{},
		ObjectMeta: metav1.ObjectMeta{
			Namespace: namespace,
			Name:      deploymentName,
			Labels: map[string]string{
				"app": "test-nginx",
			},
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: new(int32),
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": "test-nginx",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": "test-nginx",
					},
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						{
							Name:  "container",
							Image: "registry.baidubce.com/cce-public/nginx-alpine-go:latest",
						},
					},
				},
			},
		},
	}
	*testDeployment.Spec.Replicas = 2

	testService := &corev1.Service{
		TypeMeta: metav1.TypeMeta{},
		ObjectMeta: metav1.ObjectMeta{
			Name:      serviceName,
			Namespace: namespace,
			Labels: map[string]string{
				"app": "test-nginx",
			},
		},
		Spec: corev1.ServiceSpec{
			Ports: []corev1.ServicePort{
				{
					Port:     80,
					Protocol: corev1.ProtocolTCP,
				},
			},
			Selector: map[string]string{
				"app": "test-nginx",
			},
		},
	}

	// create svc
	svc, err := c.base.K8SClient.CoreV1().Services(namespace).Create(ctx, testService, metav1.CreateOptions{})
	if err != nil {
		logger.Errorf(ctx, "k8sClient create svc %s/%s failed: %v", namespace, serviceName, err)
		return nil, err
	}
	logger.Infof(ctx, "svc %s/%s created", svc.GetNamespace(), svc.GetName())

	defer func() {
		// delete service
		if err := c.base.K8SClient.CoreV1().Services(namespace).Delete(ctx, serviceName, metav1.DeleteOptions{}); err != nil {
			logger.Errorf(ctx, "fail to delete service %s/%s, may be leaked: %v", namespace, serviceName, err)
			return
		}

		for i := 0; i < 10; i++ {
			// wait at most 50s for resource deleted
			<-time.After(5 * time.Second)
			_, err := c.base.K8SClient.CoreV1().Services(namespace).Get(ctx, serviceName, metav1.GetOptions{})
			if err != nil {
				if kerrors.IsNotFound(err) {
					logger.Infof(ctx, "service %s/%s has been deleted successfully", namespace, serviceName)
					return
				}
				logger.Errorf(ctx, "fail to get service %s/%s (will retry): %v", namespace, serviceName, err)
				continue
			}
			logger.Infof(ctx, "service %s/%s is not deleted yet", namespace, serviceName)
		}
		logger.Errorf(ctx, "error: service %s/%s cannot be deleted", namespace, serviceName)
	}()

	// create pod
	got, err := c.base.K8SClient.AppsV1().Deployments(namespace).Create(ctx, testDeployment, metav1.CreateOptions{})
	if err != nil {
		logger.Errorf(ctx, "k8sClient create deployment %s/%s failed: %v", namespace, deploymentName, err)
		return nil, err
	}
	logger.Infof(ctx, "deployment %s/%s created", got.GetNamespace(), got.GetName())
	if got.GetNamespace() != namespace || got.GetName() != deploymentName {
		logger.Warnf(ctx, "create deployment %s/%s but got %s/%s",
			namespace, deploymentName, got.GetNamespace(), got.GetName())
		namespace = got.GetNamespace()
		deploymentName = got.GetName()
	}

	defer func() {
		if running {
			if err := c.base.K8SClient.AppsV1().Deployments(namespace).Delete(ctx, deploymentName, metav1.DeleteOptions{}); err != nil {
				logger.Errorf(ctx, "fail to delete deployment %s/%s, may be leaked: %v", namespace, deploymentName, err)
				return
			}

			// check pod deleted
			for i := 0; i < 10; i++ {
				// wait at most 50s for resource deleted
				<-time.After(5 * time.Second)
				_, err := c.base.K8SClient.AppsV1().Deployments(namespace).Get(ctx, deploymentName, metav1.GetOptions{})
				if err != nil {
					if kerrors.IsNotFound(err) {
						logger.Infof(ctx, "deployment %s/%s has been deleted successfully", namespace, deploymentName)
						return
					}
					logger.Errorf(ctx, "fail to get deployment %s/%s (will retry): %v", namespace, deploymentName, err)
					continue
				}
				logger.Infof(ctx, "deployment %s/%s is not deleted yet", namespace, deploymentName)
			}
			logger.Errorf(ctx, "error: deployment %s/%s cannot be deleted", namespace, deploymentName)
		}
	}()

	// check pod running
	running = false
	for i := 0; i < 60; i++ {
		// wait at most 300s for pod running
		<-time.After(5 * time.Second)
		got, err := c.base.K8SClient.AppsV1().Deployments(namespace).Get(ctx, deploymentName, metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "fail to get deployment %s/%s (will retry): %v", namespace, deploymentName, err)
			continue
		}

		if got.Status.ReadyReplicas == 2 {
			logger.Infof(ctx, "deployment %s/%s has been running, stop check running", namespace, deploymentName)
			running = true
			break
		}
		logger.Infof(ctx, "deployment %s/%s is not running yet, replicas: %d", namespace, deploymentName, got.Status.ReadyReplicas)
	}
	if !running {
		logger.Errorf(ctx, "error: deployment %s/%s cannot be running", namespace, deploymentName)
		return nil, fmt.Errorf("deployment %s/%s cannot be running", namespace, deploymentName)
	}

	// check service
	running = false
	for i := 0; i < 20; i++ {
		// wait at most 100s for pod running
		<-time.After(5 * time.Second)
		got, err := c.base.K8SClient.CoreV1().Services(namespace).Get(ctx, serviceName, metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "fail to get svc %s/%s (will retry): %v", namespace, serviceName, err)
			continue
		}
		if got.Spec.ClusterIP != "" {
			logger.Infof(ctx, "service %s/%s has been running, stop check running", namespace, serviceName)
			running = true
			break
		}
		logger.Infof(ctx, "service %s/%s is not running yet, ready replicas: %d", namespace, serviceName)
	}
	if !running {
		logger.Errorf(ctx, "error: service %s/%s cannot be running", namespace, serviceName)
		return nil, fmt.Errorf("service %s/%s cannot be running", namespace, serviceName)
	}

	// 兼容serverless集群，serverless 集群 pod在变成running的时候，网络不是马上就通的，和neutron那边下流表的方式有关。
	// add port是nova完成，ovs-agent会定期扫本机的port，扫到以后才会添加对应流表，才能通网，扫port的间隔都2s
	for i := 0; i < 20; i++ {
		time.Sleep(time.Second * 5)
		got, err := c.base.K8SClient.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{
			LabelSelector: "app=test-nginx",
		})
		if err != nil {
			logger.Errorf(ctx, "error: failed to get pod, %v", err)
			return nil, fmt.Errorf("failed to get pod, %v", err)
		}
		if len(got.Items) == 0 {
			logger.Errorf(ctx, "error: got empty pod list")
			return nil, errors.New("got empty pod list")
		}

		podName := got.Items[0].Name

		cmd := []string{
			"sh",
			"-c",
			"curl -v http://" + serviceName,
		}

		req := c.base.K8SClient.CoreV1().RESTClient().Post().Resource("pods").Name(podName).
			Namespace(namespace).SubResource("exec").Param("container", "container")
		option := &corev1.PodExecOptions{
			Command: cmd,
			Stdin:   false,
			Stdout:  true,
			Stderr:  true,
			TTY:     false,
		}

		req.VersionedParams(
			option,
			scheme.ParameterCodec,
		)

		config, err := clientcmd.RESTConfigFromKubeConfig([]byte(c.base.KubeConfig))
		if err != nil {
			logger.Errorf(ctx, "failed to setup kubeconfig, err: %v, config: %s", err, c.base.KubeConfig)
			return nil, fmt.Errorf("failed to setup kubeconfig, %v", err)
		}
		exec, err := remotecommand.NewSPDYExecutor(config, "POST", req.URL())
		if err != nil {
			logger.Errorf(ctx, "failed to setup executor, err: %v", err)
			return nil, err
		}

		stdoutBuf := make([]byte, 0)
		stdout := bytes.NewBuffer(stdoutBuf)
		stderrBuf := make([]byte, 0)
		stderr := bytes.NewBuffer(stderrBuf)
		err = exec.Stream(remotecommand.StreamOptions{
			Stdout: stdout,
			Stderr: stderr,
		})
		if err != nil {
			logger.Errorf(ctx, "failed to exec in container, err: %v", err)
			return nil, err
		}

		stdoutOutput := stdout.String()
		stderrOutput := stderr.String()
		if !strings.Contains(stdoutOutput, "Welcome to nginx!") {
			logger.Errorf(ctx, "can not get response from nginx, curl stdout: %s, stderr: %s", stdoutOutput, stderrOutput)
			continue
		}
		return nil, nil
	}

	return nil, errors.New("timeout wait for service ready")
}

func (c *clusterNetwork) Clean(ctx context.Context) error {
	return nil
}

func (c *clusterNetwork) Continue(ctx context.Context) bool {
	return true
}

func (c *clusterNetwork) ConfigFormat() string {
	return ""
}
