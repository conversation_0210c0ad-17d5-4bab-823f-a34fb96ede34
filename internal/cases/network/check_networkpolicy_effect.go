package network

import (
	"context"
	"fmt"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	kubeerrors "k8s.io/apimachinery/pkg/api/errors"
)

const (
	CheckNetworkPolicyEffectCaseName cases.CaseName = "CheckNetworkPolicyEffect"
)

func init() {
	cases.AddCase(context.TODO(), CheckNetworkPolicyEffectCaseName, NewCheckNetworkPolicyEffect)
}

type checkNetworkPolicyEffect struct {
	base *cases.BaseClient
}

func NewCheckNetworkPolicyEffect(ctx context.Context) cases.Interface {
	return &checkNetworkPolicyEffect{}
}

func (c *checkNetworkPolicyEffect) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base is nil")
	}
	c.base = base
	return nil
}

func (c *checkNetworkPolicyEffect) Name() cases.CaseName {
	return CheckNetworkPolicyEffectCaseName
}

func (c *checkNetworkPolicyEffect) Desc() string {
	return "验证NetworkPolicy能正确限制Pod间的网络访问"
}

func (c *checkNetworkPolicyEffect) Check(ctx context.Context) ([]cases.Resource, error) {
	logger.Infof(ctx, "开始执行NetworkPolicy生效性测试...")
	var err error

	nsAllow := "ns-allow"
	nsDeny := "ns-deny"
	deployA := "nginx-a"
	deployB := "nginx-b"
	deployC := "nginx-c"
	labelKey := "app"
	labelA := "nginx-a"
	labelB := "nginx-b"
	labelC := "nginx-c"
	image := "registry.baidubce.com/cce/nginx-alpine-go:latest"

	// 1. 清理/创建命名空间
	for _, ns := range []string{nsAllow, nsDeny} {
		_ = c.base.KubeClient.ClientSet.CoreV1().Namespaces().Delete(ctx, ns, metav1.DeleteOptions{})
		// 等待命名空间删除
		for i := 0; i < 10; i++ {
			_, err = c.base.KubeClient.ClientSet.CoreV1().Namespaces().Get(ctx, ns, metav1.GetOptions{})
			if err != nil {
				break
			}
			time.Sleep(1 * time.Second)
		}
		_, err = c.base.KubeClient.ClientSet.CoreV1().Namespaces().Create(ctx, &corev1.Namespace{ObjectMeta: metav1.ObjectMeta{Name: ns}}, metav1.CreateOptions{})
		if err != nil && !kubeerrors.IsAlreadyExists(err) {
			return nil, fmt.Errorf("创建命名空间%s失败: %v", ns, err)
		}
	}

	// 2. 创建nginx deployment
	if err = c.createNginxDeployment(ctx, nsAllow, deployA, labelKey, labelA, image, 1); err != nil {
		return nil, err
	}
	if err = c.createNginxDeployment(ctx, nsDeny, deployB, labelKey, labelB, image, 1); err != nil {
		return nil, err
	}
	if err = c.createNginxDeployment(ctx, nsAllow, deployC, labelKey, labelC, image, 1); err != nil {
		return nil, err
	}

	// 等待Pod就绪
	if err = c.waitDeploymentReady(ctx, nsAllow, deployA); err != nil {
		return nil, err
	}
	if err = c.waitDeploymentReady(ctx, nsDeny, deployB); err != nil {
		return nil, err
	}
	if err = c.waitDeploymentReady(ctx, nsAllow, deployC); err != nil {
		return nil, err
	}

	// 获取Pod名称
	podC, err := c.getFirstPodName(ctx, nsAllow, labelKey, labelC)
	if err != nil {
		return nil, err
	}
	podA, err := c.getFirstPodName(ctx, nsAllow, labelKey, labelA)
	if err != nil {
		return nil, err
	}
	podB, err := c.getFirstPodName(ctx, nsDeny, labelKey, labelB)
	if err != nil {
		return nil, err
	}

	// 获取Pod IP
	ipA, err := c.getPodIP(ctx, nsAllow, podA)
	if err != nil {
		return nil, err
	}
	ipB, err := c.getPodIP(ctx, nsDeny, podB)
	if err != nil {
		return nil, err
	}

	// 3. 初始连通性验证
	if err = c.checkConnectivity(ctx, nsAllow, podC, ipA, true); err != nil {
		return nil, fmt.Errorf("策略前pod-c访问pod-a失败: %v", err)
	}
	if err = c.checkConnectivity(ctx, nsAllow, podC, ipB, true); err != nil {
		return nil, fmt.Errorf("策略前pod-c访问pod-b失败: %v", err)
	}

	// 4. 创建NetworkPolicy
	if err = c.createNetworkPolicy(ctx, nsAllow, labelKey, labelA); err != nil {
		return nil, err
	}
	// 等待策略生效
	logger.Infof(ctx, "等待NetworkPolicy生效...")
	time.Sleep(10 * time.Second)

	// 5. 策略生效性验证
	if err = c.checkConnectivity(ctx, nsAllow, podC, ipA, true); err != nil {
		return nil, fmt.Errorf("策略后pod-c访问pod-a失败: %v", err)
	}
	if err = c.checkConnectivity(ctx, nsAllow, podC, ipB, false); err != nil {
		return nil, fmt.Errorf("策略后pod-c访问pod-b未被拒绝: %v", err)
	}

	logger.Infof(ctx, "NetworkPolicy生效性测试完成")
	return nil, nil
}

// Clean 清理所有资源
func (c *checkNetworkPolicyEffect) Clean(ctx context.Context) error {
	nsAllow := "ns-allow"
	nsDeny := "ns-deny"
	for _, ns := range []string{nsAllow, nsDeny} {
		_ = c.base.KubeClient.ClientSet.CoreV1().Namespaces().Delete(ctx, ns, metav1.DeleteOptions{})
	}
	return nil
}

func (c *checkNetworkPolicyEffect) Continue(ctx context.Context) bool {
	return true
}

func (c *checkNetworkPolicyEffect) ConfigFormat() string {
	return ""
}

// createNginxDeployment 创建nginx deployment
func (c *checkNetworkPolicyEffect) createNginxDeployment(ctx context.Context, ns, name, labelKey, labelVal, image string, replicas int32) error {
	dep := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name: name,
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{labelKey: labelVal},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{labelKey: labelVal},
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{{
						Name:  name,
						Image: image,
						Ports: []corev1.ContainerPort{{ContainerPort: 80}},
					}},
				},
			},
		},
	}
	_ = c.base.KubeClient.EnsureDeploymentDeletedAppsV1(ctx, ns, name, &kube.CheckOptions{Timeout: 60 * time.Second, Interval: 2 * time.Second})
	_, err := c.base.KubeClient.CreateDeploymentAppsV1(ctx, ns, dep)
	return err
}

// waitDeploymentReady 等待deployment就绪
func (c *checkNetworkPolicyEffect) waitDeploymentReady(ctx context.Context, ns, name string) error {
	return c.base.KubeClient.EnsureDeploymentRunningAppsV1(ctx, ns, name, &kube.CheckOptions{Timeout: 120 * time.Second, Interval: 2 * time.Second})
}

// getFirstPodName 获取指定label的第一个pod名称
func (c *checkNetworkPolicyEffect) getFirstPodName(ctx context.Context, ns, labelKey, labelVal string) (string, error) {
	pods, err := c.base.KubeClient.ListPod(ctx, ns, &kube.ListOptions{LabelSelector: map[string]string{labelKey: labelVal}})
	if err != nil || len(pods.Items) == 0 {
		return "", fmt.Errorf("获取pod失败: %v", err)
	}
	return pods.Items[0].Name, nil
}

// getPodIP 获取pod的IP
func (c *checkNetworkPolicyEffect) getPodIP(ctx context.Context, ns, podName string) (string, error) {
	pod, err := c.base.KubeClient.ClientSet.CoreV1().Pods(ns).Get(ctx, podName, metav1.GetOptions{})
	if err != nil {
		return "", err
	}
	return pod.Status.PodIP, nil
}

// checkConnectivity 检查连通性
func (c *checkNetworkPolicyEffect) checkConnectivity(ctx context.Context, ns, pod, targetIP string, shouldConnect bool) error {
	cmd := []string{"wget", "-T", "3", "-O", "-", fmt.Sprintf("http://%s", targetIP)}
	stdout, err := c.base.KubeClient.RemoteExec(ns, pod, pod, cmd)
	if shouldConnect {
		if err != nil {
			return fmt.Errorf("预期可连通，但失败: %v, 输出: %s", err, stdout)
		}
	} else {
		if err == nil {
			return fmt.Errorf("预期不可连通，但实际连通，输出: %s", stdout)
		}
	}
	return nil
}

// createNetworkPolicy 创建NetworkPolicy
func (c *checkNetworkPolicyEffect) createNetworkPolicy(ctx context.Context, ns, labelKey, labelVal string) error {
	policy := &networkingv1.NetworkPolicy{
		ObjectMeta: metav1.ObjectMeta{
			Name: "allow-same-namespace",
		},
		Spec: networkingv1.NetworkPolicySpec{
			PodSelector: metav1.LabelSelector{
				MatchLabels: map[string]string{labelKey: labelVal},
			},
			Ingress: []networkingv1.NetworkPolicyIngressRule{{
				From: []networkingv1.NetworkPolicyPeer{{
					PodSelector: &metav1.LabelSelector{},
				}},
			}},
		},
	}
	_, err := c.base.KubeClient.ClientSet.NetworkingV1().NetworkPolicies(ns).Create(ctx, policy, metav1.CreateOptions{})
	return err
}
