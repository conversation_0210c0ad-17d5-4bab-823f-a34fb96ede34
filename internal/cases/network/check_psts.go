/*
<AUTHOR>
@date 2025-03-28
测试内容及场景见下方Init函数中的case设计注释

测试场景与cni_v2_fixed_ip有部分重叠，但case更丰富

此文件测试时需传入config字段，在/e2e-test/internal/casegroup/<check-e2e-test.yaml>目录下
具体json格式参考如下：
要注意的是：指定子网的ip范围的两个都应使用fixip用的子网cidr中的范围，否则会报错
"config": {
	"psts_subnet_id": ["sbn-xxx1", "sbn-xx2"],
    "fixIP_subnet_id": "sbn-xxx3",
    "ipv4_psts": [
        {
            "start": "172.22.18.xx",
            "end": "172.22.18.xx"
        }
    ],
	"ipv4_fix": [
        {
            "start": "172.22.18.xx",
            "end": "172.22.18.xx"
        }
    ]
}
或者yaml格式：
config:
  psts_subnet_id:
    - sbn-xxx1
    - sbn-xx2
  fixIP_subnet_id: sbn-xxx3
  ipv4_psts:
    - start: 172.22.18.xx
      end: 172.22.18.xx
  ipv4_fix:
    - start: 172.22.18.xx
      end: 172.22.18.xx

*/

package network

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net"
	"strings"
	"text/template"
	"time"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/dynamic"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// CheckPSTS 用于检查容器网络v2模式psts的使用相关方式是否正确
	CheckPSTS cases.CaseName = "CheckPSTS"

	// PSTS模版
	pstsTeplYaml string = `apiVersion: cce.baidubce.com/v2
kind: PodSubnetTopologySpread
metadata:
    name: {{.Name}}
    namespace: default
spec:
    priority: {{.Priority}}
    subnets:
        {{- range $_, $v := $.SubnetID }}
        {{ $v }}: 
            {{- if $.IPV4 }}
            - family: "4"
              range:
              {{- range $_, $ip :=$.IPV4 }}
              - start: {{$ip.Start}}
                end: {{$ip.End}}
              {{- end }}
            {{- else}} []
            {{- end }}
            {{- if $.IPV6 }}
            - family: "6"
              range:
              {{- range $_, $ip := $.IPV6 }}
              - start: {{$ip.Start}}
                end: {{$ip.End}}
              {{- end }}
            {{- end }}
        {{- end }}
    strategy:
        type: {{$.StrategyType}}
        releaseStrategy: {{$.ReleaseStrategy}}
        {{- if $.TTL }}
        ttl: {{$.TTL}}
        {{- end }}
        {{- if $.EnableReuseIPAddress }}
        enableReuseIPAddress: {{$.EnableReuseIPAddress}}
        {{- end }}
    {{- if .MatchLabels }}
    selector:
        matchLabels:
            {{- range $k, $v := $.MatchLabels }}
            {{ $k }}: {{ $v }}
            {{- end }}
    {{- end }}
`
)

var CheckPSTSCases []*PSTS

func init() {
	cases.AddCase(context.TODO(), CheckPSTS, NewCheckPSTS)
}

type checkPSTS struct {
	base       *cases.BaseClient
	config     checkPSTSConfig
	pstsClient dynamic.NamespaceableResourceInterface
	cepClient  dynamic.NamespaceableResourceInterface
	sbnClient  dynamic.NamespaceableResourceInterface
}

type checkPSTSConfig struct {
	// 为psts测试申请的两个sbn
	PstsSubnetID []string `json:"psts_subnet_id"`
	//测试sts fixip模式用的sbn
	FixIPSubnetID string          `json:"fixIP_subnet_id"`
	IPV4PSTS      []CustomIPRange `json:"ipv4_psts"`
	IPV4FIX       []CustomIPRange `json:"ipv4_fix"`
	IPV6          []CustomIPRange `json:"ipv6"`
}

type PSTS struct {
	index int

	Description string

	// Name strategy name
	Name string

	SubnetID []string

	// StrategyType Elastic、Fixed、Manual
	StrategyType string

	// ReleaseStrategy TTL、Never
	ReleaseStrategy string

	//the time to live for cep when releaseStrategy is TTL and related pod deleted
	TTL string

	// EnableReuseIPAddress
	EnableReuseIPAddress bool

	MatchLabels map[string]string
	IPV4        []CustomIPRange
	IPV6        []CustomIPRange

	// priority 0、1、2、3、4、5，数字越大越优先
	Priority int
}

func NewCheckPSTS(ctx context.Context) cases.Interface {
	return &checkPSTS{}
}
func (c *checkPSTS) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {

	if base == nil {
		return errors.New("base cannot be nil")
	}
	c.base = base

	logger.WithValues("case", "CheckPSTS").Infof(ctx, "json raw config: %s", string(config))
	if err := json.Unmarshal(config, &c.config); err != nil {
		return err
	}
	logger.WithValues("case", "CheckPSTS").Infof(ctx, "config: %s", utils.ToJSON(c.config))

	// 使用kube包提供的客户端
	var err error

	// 初始化PSTS客户端 - base.KubeClient 是kube.Client类型，而不是kubernetes.Interface
	clientPsts, err := base.KubeClient.NewDynamicClient("cce.baidubce.com", "v2", "podsubnettopologyspreads")
	if err != nil {
		return fmt.Errorf("创建PSTS客户端失败: %v", err)
	}
	c.pstsClient = clientPsts

	// 初始化CEP客户端
	clientCep, err := base.KubeClient.NewDynamicClient("cce.baidubce.com", "v2", "cceendpoints")
	if err != nil {
		return fmt.Errorf("创建CEP客户端失败: %v", err)
	}
	c.cepClient = clientCep

	// 初始化Subnet客户端
	clientSbn, err := base.KubeClient.NewDynamicClient("cce.baidubce.com", "v1", "subnets")
	if err != nil {
		return fmt.Errorf("创建Subnet客户端失败: %v", err)
	}
	c.sbnClient = clientSbn

	/**
	测试场景
	1：deploy使用psts，包括创建是否成功、成功后的ip检查是否指定子网、
	   扩容是否成功、扩容后ip检查是否指定子网（并让这次扩容使节点添加网卡）、
	   删除deploy是否成功删除后是否也删除了cep
	2. 对同一个deploy指定两个psts, psts指定不同子网并有不同优先级
	   当优先级都为0时候创建pod随机选一个子网分配ip、
	   当优先级为0与1时候创建pod选择优先级数字大的子网分配ip
	3. sts使用psts，包括创建是否成功、成功后的ip检查是否指定子网、
	   扩容是否成功、扩容后ip检查是否指定子网、
	   缩容是否成功、缩容后cep是否保留、
	   缩容后再扩容是否成功、扩容后同名pod的ip是否保持不变（这里也会包含检查到换节点pod ip不变的情况）
	   删除sts是否成功、删除后是否删除了cep是否释放了ip、
	4. 手动指定子网的 start 与 end，验证deploy使用psts是否成功（与1中相同）
	5. 手动指定子网的 start 与 end，验证sts使用psts是否成功（与3中相同）

	注意：每个测试场景使用唯一的标签选择器（使用时间戳生成），避免测试之间的资源相互干扰。
	这样可以确保:
	1. 不同测试之间相互隔离
	2. 测试资源能够被正确清理
	3. Pod的IP分配不会受到其他测试用例的影响
	*/
	/**
	TODO:
	1. [Bug] PSTS 增加对 CEP TTL 未过期时直接移除 Node 或 ENI 导致 CEP 后续 TTL 过期后因无对应的 NetworkResourceSet 或 ENI 而无法正常删除时的清理逻辑
		创建psts以及对应的sts，设置过期时间
		删除集群中的sts
		删除集群对应的NRS
		检测无nrs时是否psts对应的cep存在
		检测超过规定时间后，cep是否残留

	*/

	// 生成唯一标识，用于区分不同测试实例的资源
	timestamp := time.Now().Format("20060102150405")

	CheckPSTSCases = []*PSTS{
		{
			index:           1,
			Description:     "1、deploy指定子网使用psts分配IP",
			Name:            "psts-test1",
			SubnetID:        c.config.PstsSubnetID[:1],
			StrategyType:    "Elastic",
			ReleaseStrategy: "TTL",
			TTL:             "5s",
			MatchLabels:     map[string]string{"app": "pststest-case1-" + timestamp},
			Priority:        0,
		},
		{
			index:           2,
			Description:     "2、对同一个deploy指定两个psts, psts指定不同子网并有不同优先级",
			Name:            "psts-test2",
			SubnetID:        c.config.PstsSubnetID[:1],
			StrategyType:    "Elastic",
			ReleaseStrategy: "TTL",
			TTL:             "5s",
			MatchLabels:     map[string]string{"app": "pststest-case2-" + timestamp},
			Priority:        0,
		},
		{
			index:                3,
			Description:          "3、sts Pod fixed IP",
			Name:                 "psts-test3",
			SubnetID:             []string{c.config.FixIPSubnetID},
			EnableReuseIPAddress: true,
			StrategyType:         "Fixed",
			ReleaseStrategy:      "Never",
			MatchLabels:          map[string]string{"app": "pststest-case3-" + timestamp},
			Priority:             0,
		},
		{
			index:       4,
			Description: "4、deploy手动指定子网内网络段，并使用psts分配IP",
			Name:        "psts-test4",
			//指定ip范围分配时候要用exclusive为true的子网，故这里选择fixip用的子网
			SubnetID:             []string{c.config.FixIPSubnetID},
			EnableReuseIPAddress: true,
			StrategyType:         "Elastic",
			ReleaseStrategy:      "TTL",
			TTL:                  "5s",
			MatchLabels:          map[string]string{"app": "pststest-case4-" + timestamp},
			Priority:             0,
			IPV4:                 c.config.IPV4PSTS,
		},
		{
			index:                5,
			Description:          "5、statefulset手动指定子网内网络段，并使用sts Pod fixed IP",
			Name:                 "psts-test5",
			SubnetID:             []string{c.config.FixIPSubnetID},
			EnableReuseIPAddress: true,
			StrategyType:         "Fixed",
			ReleaseStrategy:      "Never",
			MatchLabels:          map[string]string{"app": "pststest-case5-" + timestamp},
			Priority:             0,
			IPV4:                 c.config.IPV4FIX,
		},
	}

	return nil
}

func (c *checkPSTS) Name() cases.CaseName {
	return CheckPSTS
}

func (c *checkPSTS) Desc() string {
	return "检查集群可以正常创建PSTS类型的资源以及部署对应的pod"
}

func (c *checkPSTS) Check(ctx context.Context) ([]cases.Resource, error) {
	var err error
	// 解析模版
	tmpl, err := template.New("checkPSTSTest").Parse(pstsTeplYaml)
	if err != nil {
		logger.Errorf(ctx, "parse template failed: %v", err.Error())
		return nil, fmt.Errorf("parse template failed: %v", err.Error())
	}

	for _, caseItem := range CheckPSTSCases {
		logger.Infof(ctx, "%s case check start", caseItem.Description)

		switch caseItem.index {
		case 1, 4:
			err = c.checkPSTS1(ctx, tmpl, caseItem)
			if err != nil {
				return nil, err
			}
		case 2:
			err = c.checkPSTS2(ctx, tmpl, caseItem)
			if err != nil {
				return nil, err
			}
		case 3, 5:
			err = c.checkPSTS3(ctx, tmpl, caseItem)
			if err != nil {
				return nil, err
			}
		}
	}

	return nil, err
}

func (c *checkPSTS) Clean(ctx context.Context) error {
	// 清理所有case相关的PSTS、Deployment、StatefulSet、CEP资源
	for _, caseItem := range CheckPSTSCases {
		// 删除PSTS
		logger.Infof(ctx, "删除PSTS: %s", caseItem.Name)
		_ = c.pstsClient.Namespace("default").Delete(ctx, caseItem.Name, metav1.DeleteOptions{})

		// 生成workload名称
		workloadName := "psts-test-workload-" + fmt.Sprintf("test%d", caseItem.index)
		// 删除Deployment
		logger.Infof(ctx, "删除Deployment: %s", workloadName)
		_ = c.base.K8SClient.AppsV1().Deployments("default").Delete(ctx, workloadName, metav1.DeleteOptions{})
		// 删除StatefulSet
		logger.Infof(ctx, "删除StatefulSet: %s", workloadName)
		_ = c.base.K8SClient.AppsV1().StatefulSets("default").Delete(ctx, workloadName, metav1.DeleteOptions{})

		// 查找和删除相关联的所有 CEP 资源，使用标签选择器
		if len(caseItem.MatchLabels) > 0 {
			logger.Infof(ctx, "查找并删除与workload相关的CEP资源(通过标签)")
			// 构建标签选择器字符串
			labelSelector := ""
			for k, v := range caseItem.MatchLabels {
				if labelSelector != "" {
					labelSelector += ","
				}
				labelSelector += fmt.Sprintf("%s=%s", k, v)
			}

			pods, err := c.base.K8SClient.CoreV1().Pods("default").List(ctx, metav1.ListOptions{
				LabelSelector: labelSelector,
			})
			if err == nil && pods != nil {
				for _, pod := range pods.Items {
					logger.Infof(ctx, "删除Pod %s 对应的CEP", pod.Name)
					_ = c.cepClient.Namespace("default").Delete(ctx, pod.Name, metav1.DeleteOptions{})
				}
			}
		}

		// 删除workload名对应的CEP（如果有）
		logger.Infof(ctx, "删除workload名对应的CEP: %s", workloadName)
		_ = c.cepClient.Namespace("default").Delete(ctx, workloadName, metav1.DeleteOptions{})
	}

	// 尝试清理额外的PSTS资源（针对case2可能创建的额外PSTS）
	logger.Infof(ctx, "清理可能存在的额外PSTS资源")
	_ = c.pstsClient.Namespace("default").Delete(ctx, "psts-test2-p1", metav1.DeleteOptions{})
	_ = c.pstsClient.Namespace("default").Delete(ctx, "psts-test2-p0", metav1.DeleteOptions{})

	logger.Infof(ctx, "所有资源清理完成")
	return nil
}

func (c *checkPSTS) Continue(ctx context.Context) bool {
	return true
}

func (c *checkPSTS) ConfigFormat() string {
	return ""
}

// WaitCEPDeleted 等待cep被删除
func (c *checkPSTS) WaitCEPDeleted(ctx context.Context, namespace, name string) (bool, error) {
	timeoutCtx, cancelFn := context.WithTimeout(ctx, time.Minute*20)
	timeout := true

	wait.UntilWithContext(timeoutCtx, func(_ context.Context) {
		_, err := c.cepClient.Namespace(namespace).Get(ctx, name, metav1.GetOptions{})
		if err != nil {
			// 删除成功或不存在时，不应该报错
			if strings.Contains(err.Error(), "not found") {
				logger.Infof(ctx, "cep %s already deleted", name)
				cancelFn()
				timeout = false
			} else {
				// 只有其他类型的错误才需要记录
				logger.Errorf(ctx, "get cep %s failed: %v", name, err.Error())
			}
			return
		}
	}, time.Second*5)

	if timeout {
		err := fmt.Errorf("wait cep %s deleted timeout", name)
		logger.Errorf(ctx, "%v", err)
		return false, err
	}

	return true, nil
}

func (c *checkPSTS) applyPSTSYaml(ctx context.Context, tmpl *template.Template, caseItem *PSTS) (err error) {
	// 解析psts模版
	var pstsYaml bytes.Buffer
	err = tmpl.Execute(&pstsYaml, caseItem)
	if err != nil {
		logger.Errorf(ctx, "template execute failed: %v", err.Error())
		return fmt.Errorf("template execute failed: %v", err.Error())
	}

	// psts yaml部署
	if _, err = c.base.AppClient.PostAppResource(ctx, c.base.ClusterID,
		caseItem.Name, pstsYaml.String(), "default", nil); err != nil {
		logger.Errorf(ctx, "Deploy PSTS default/%s failed: %s", caseItem.Name, err)
		return err
	}

	// 等待PSTS创建，如果psts创建和工作负载创建的时间间隔较短，会可能导致有工作负载容器并未生效psts规则
	time.Sleep(10 * time.Second)

	return
}

func (c *checkPSTS) applyWorkloadYaml(ctx context.Context, workloadType string, caseItem *PSTS, suffix string) (workloadName string, err error) {
	// 部署工作负载
	workloadName = "psts-test-workload" + "-" + suffix

	var workloadYaml string
	if workloadType == "StatefulSet" {
		// 创建StatefulSet
		workloadYaml = c.genStatefulSetYaml(workloadName, caseItem.MatchLabels, "default")
	} else {
		// 创建Deployment
		workloadYaml = c.genDeploymentYaml(workloadName, caseItem.MatchLabels, "default")
	}

	if _, err = c.base.AppClient.PostAppResource(ctx, c.base.ClusterID,
		workloadName, workloadYaml, "default", nil); err != nil {
		logger.Errorf(ctx, "Deploy workload for %s failed: %s", caseItem.Description, err)
		return "", err
	}
	return
}

func (c *checkPSTS) getSubnetCidr(ctx context.Context, subnetID string) (cidr *net.IPNet, err error) {
	// 修正： subnet是集群级资源，不使用namespace
	sbn, err := c.sbnClient.Get(ctx, subnetID, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "get sbn failed: %v", err.Error())
		return nil, err
	}
	rawcidr, _, err := unstructured.NestedString(sbn.Object, "spec", "cidr")
	if err != nil {
		logger.Errorf(ctx, "get cidr from sbn failed: %v", err.Error())
		return nil, err
	}
	_, cidr, err = net.ParseCIDR(rawcidr)
	if err != nil {
		logger.Errorf(ctx, "parse cidr failed: %v", err.Error())
		return nil, err
	}
	return cidr, nil
}

func (c *checkPSTS) checkPodsIpsInSubnet(ctx context.Context, pods *v1.PodList, cidr *net.IPNet) error {
	for _, pod := range pods.Items {
		if pod.Status.Phase != v1.PodRunning {
			logger.Errorf(ctx, "pod %s is not running", pod.Name)
			return fmt.Errorf("pod %s is not running", pod.Name)
		}
		ip := net.ParseIP(pod.Status.PodIP)
		isIPinCidr := cidr.Contains(ip)
		if !isIPinCidr {
			logger.Errorf(ctx, "pod %s ip %s is not in cidr %s", pod.Name, pod.Status.PodIP, cidr.String())
			return fmt.Errorf("pod %s ip %s is not in cidr %s", pod.Name, pod.Status.PodIP, cidr.String())
		}
	}
	return nil
}

// 清理用例相关资源，确保测试可以重复执行
func (c *checkPSTS) cleanupCaseResources(ctx context.Context, caseItem *PSTS, suffix string) {
	// 生成测试用的workload名称
	workloadName := "psts-test-workload" + "-" + suffix

	// 先删除相关资源
	logger.Infof(ctx, "清理同名PSTS: %s", caseItem.Name)
	_ = c.pstsClient.Namespace("default").Delete(ctx, caseItem.Name, metav1.DeleteOptions{})

	// 删除可能存在的Deployment
	logger.Infof(ctx, "清理同名Deployment: %s", workloadName)
	_ = c.base.K8SClient.AppsV1().Deployments("default").Delete(ctx, workloadName, metav1.DeleteOptions{})

	// 删除可能存在的StatefulSet
	logger.Infof(ctx, "清理同名StatefulSet: %s", workloadName)
	_ = c.base.K8SClient.AppsV1().StatefulSets("default").Delete(ctx, workloadName, metav1.DeleteOptions{})

	// 根据case index删除可能存在的workload
	indexWorkloadName := "psts-test-workload" + "-" + fmt.Sprintf("test%d", caseItem.index)
	if indexWorkloadName != workloadName {
		// 删除可能存在的Deployment
		logger.Infof(ctx, "清理索引Deployment: %s", indexWorkloadName)
		_ = c.base.K8SClient.AppsV1().Deployments("default").Delete(ctx, indexWorkloadName, metav1.DeleteOptions{})

		// 删除可能存在的StatefulSet
		logger.Infof(ctx, "清理索引StatefulSet: %s", indexWorkloadName)
		_ = c.base.K8SClient.AppsV1().StatefulSets("default").Delete(ctx, indexWorkloadName, metav1.DeleteOptions{})
	}

	// 等待资源删除完成
	time.Sleep(5 * time.Second)
}

// 检查指定子网创建psts和deployment资源，检查pod是否ip正确，然后删除pod检查ip和cep是否释放
func (c *checkPSTS) checkPSTS1(ctx context.Context, tmpl *template.Template, caseItem *PSTS) error {
	// 清理同名资源，确保测试可以重复执行
	c.cleanupCaseResources(ctx, caseItem, "test1")

	err := c.applyPSTSYaml(ctx, tmpl, caseItem)
	if err != nil {
		return err
	}

	workloadName, err := c.applyWorkloadYaml(ctx, "Deployment", caseItem, fmt.Sprintf("test%d", caseItem.index))
	if err != nil {
		return err
	}
	// 等待deployment创建完成
	var deployment common.K8SDeployment
	if err = deployment.NewK8SDeployment(ctx, c.base, "default", workloadName); err != nil {
		return err
	}
	deployment.SetWantedReplicas(1)
	if err = common.WaitForResourceReady(ctx, &deployment); err != nil {
		return err
	}

	// 获取本次psts使用的子网的cidr
	cidr, err := c.getSubnetCidr(ctx, caseItem.SubnetID[0])
	if err != nil {
		return err
	}

	//检查ip是否子网内分配
	// 使用caseItem中的MatchLabels来构建标签选择器
	labelSelector := c.getLabelSelectorString(caseItem.MatchLabels)
	pods, err := c.base.K8SClient.CoreV1().Pods("default").List(ctx, metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		logger.Errorf(ctx, "get pods of deployment failed: %v", err.Error())
		return err
	}
	if err = c.checkPodsIpsInSubnet(ctx, pods, cidr); err != nil {
		return err
	}

	//扩容deployment到节点出现新网卡，并检查ip是否为指定子网内分配
	deploy, err := c.base.K8SClient.AppsV1().Deployments("default").Get(ctx, workloadName, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "get deployment failed(checkPSTS1-初次扩容): %v", err.Error())
		return err
	}
	//这里可以修改，根据节点型号调整replicas数量，保证有新网卡创建
	newReplicaCount := int32(20)
	deploy.Spec.Replicas = &newReplicaCount
	_, err = c.base.K8SClient.AppsV1().Deployments("default").Update(ctx, deploy, metav1.UpdateOptions{})
	if err != nil {
		logger.Errorf(ctx, "update deployment failed(checkPSTS1-扩容20个副本): %v", err.Error())
		return err
	}
	//这里要相应做修改
	deployment.SetWantedReplicas(20)
	if err = common.WaitForResourceReady(ctx, &deployment); err != nil {
		return err
	}
	// 再次使用caseItem中的MatchLabels来构建标签选择器
	pods, err = c.base.K8SClient.CoreV1().Pods("default").List(ctx, metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		logger.Errorf(ctx, "get pods of deployment failed: %v", err.Error())
		return err
	}
	//再次检查ip是否子网内分配
	if err = c.checkPodsIpsInSubnet(ctx, pods, cidr); err != nil {
		return err
	}

	// 删除pod，检查ip和cep是否释放
	if err = c.base.K8SClient.AppsV1().Deployments("default").Delete(ctx, workloadName, metav1.DeleteOptions{}); err != nil {
		logger.Errorf(ctx, "delete deployment %s failed: %v", workloadName, err.Error())
		return err
	}
	//检查cep是否删除
	for _, pod := range pods.Items {
		deleted, err := c.WaitCEPDeleted(ctx, "default", pod.Name)
		if err != nil {
			// 如果是因为超时导致的错误则返回错误，其他错误（如找不到CEP）则继续测试
			if strings.Contains(err.Error(), "timeout") {
				logger.Errorf(ctx, "wait cce endpoint %s deleted failed: %v", pod.Name, err.Error())
				return err
			}
			// 找不到CEP是预期行为，继续测试
			logger.Infof(ctx, "CEP for pod %s not found, as expected after deletion", pod.Name)
			continue
		}
		if !deleted {
			logger.Errorf(ctx, "cce endpoint %s is not deleted", pod.Name)
			return fmt.Errorf("cce endpoint %s is not deleted", pod.Name)
		}
	}
	//TODO: 检查ip是否释放，可以用vpc的接口 或者检查nrs上面

	// 清理psts
	// 清理指定子网策略
	if err = c.pstsClient.Namespace("default").Delete(ctx, caseItem.Name, metav1.DeleteOptions{}); err != nil {
		logger.Errorf(ctx, "delete psts failed: %v", err.Error())
		return err
	}

	return nil
}

// deploy 的label 被两个psts指定 ，两个psts子网不同，且优先级不同
func (c *checkPSTS) checkPSTS2(ctx context.Context, tmpl *template.Template, caseItem *PSTS) error {
	// 清理同名资源，确保测试可以重复执行
	c.cleanupCaseResources(ctx, caseItem, "test2")

	// 由于这个用例还会创建另一个PSTS，也提前清理一下
	additionalPsts := "psts-test2-p1"
	logger.Infof(ctx, "清理额外PSTS: %s", additionalPsts)
	_ = c.pstsClient.Namespace("default").Delete(ctx, additionalPsts, metav1.DeleteOptions{})

	additionalPsts = "psts-test2-p0"
	logger.Infof(ctx, "清理额外PSTS: %s", additionalPsts)
	_ = c.pstsClient.Namespace("default").Delete(ctx, additionalPsts, metav1.DeleteOptions{})

	//记录第一个psts的名称
	pstsname1 := caseItem.Name
	//创建优先级为0的psts
	err := c.applyPSTSYaml(ctx, tmpl, caseItem)
	if err != nil {
		return err
	}
	// 1. 测试两个优先级不同子网如何分配ip，预期为分到优先级高的子网中
	//创建优先级为1的psts，注意给这个psts用另一个子网
	caseItem.Priority = 1
	caseItem.SubnetID = c.config.PstsSubnetID[1:2]
	caseItem.Name = "psts-test2-p1"
	err = c.applyPSTSYaml(ctx, tmpl, caseItem)
	if err != nil {
		return err
	}
	// 创建deployment
	workloadName, err := c.applyWorkloadYaml(ctx, "Deployment", caseItem, fmt.Sprintf("test%d", caseItem.index))
	if err != nil {
		return err
	}
	// 等待deployment创建完成
	var deployment common.K8SDeployment
	if err = deployment.NewK8SDeployment(ctx, c.base, "default", workloadName); err != nil {
		return err
	}
	deployment.SetWantedReplicas(1)
	if err = common.WaitForResourceReady(ctx, &deployment); err != nil {
		return err
	}

	// 获取本次psts使用的子网的cidr，这次应为priority=1的子网的cidr
	cidr1, err := c.getSubnetCidr(ctx, c.config.PstsSubnetID[1])
	if err != nil {
		return err
	}

	//检查ip是否子网内分配
	// 使用caseItem中的MatchLabels来构建标签选择器
	labelSelector := c.getLabelSelectorString(caseItem.MatchLabels)
	pods, err := c.base.K8SClient.CoreV1().Pods("default").List(ctx, metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		logger.Errorf(ctx, "get pods of deployment failed: %v", err.Error())
		return err
	}
	if err = c.checkPodsIpsInSubnet(ctx, pods, cidr1); err != nil {
		return err
	}

	//扩容deployment并检查ip是否为指定子网内分配
	deploy, err := c.base.K8SClient.AppsV1().Deployments("default").Get(ctx, workloadName, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "get deployment failed(checkPSTS2-初次扩容): %v", err.Error())
		return err
	}
	newReplicaCount := int32(10)
	deploy.Spec.Replicas = &newReplicaCount
	_, err = c.base.K8SClient.AppsV1().Deployments("default").Update(ctx, deploy, metav1.UpdateOptions{})
	if err != nil {
		logger.Errorf(ctx, "update deployment failed(checkPSTS2-扩容10个副本): %v", err.Error())
		return err
	}
	//这里要相应做修改
	deployment.SetWantedReplicas(10)
	if err = common.WaitForResourceReady(ctx, &deployment); err != nil {
		return err
	}
	// 使用caseItem中的MatchLabels来构建标签选择器
	pods, err = c.base.K8SClient.CoreV1().Pods("default").List(ctx, metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		logger.Errorf(ctx, "get pods of deployment failed: %v", err.Error())
		return err
	}
	//再次检查ip是否子网内分配
	if err1 := c.checkPodsIpsInSubnet(ctx, pods, cidr1); err1 != nil {
		return err1
	}

	//缩容deploy到0，检查cep是否按ttl时间删除，然后删除优先级为1的psts
	newReplicaCount = int32(0)
	// 重新获取最新版本的Deployment
	deploy, err = c.base.K8SClient.AppsV1().Deployments("default").Get(ctx, workloadName, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "get deployment failed(checkPSTS2-缩容前): %v", err.Error())
		return err
	}
	deploy.Spec.Replicas = &newReplicaCount
	_, err = c.base.K8SClient.AppsV1().Deployments("default").Update(ctx, deploy, metav1.UpdateOptions{})
	if err != nil {
		logger.Errorf(ctx, "update deployment failed(checkPSTS2-缩容到0个副本): %v", err.Error())
		return err
	}
	//检查cep是否删除
	for _, pod := range pods.Items {
		deleted, err := c.WaitCEPDeleted(ctx, "default", pod.Name)
		if err != nil {
			// 如果是因为超时导致的错误则返回错误，其他错误（如找不到CEP）则继续测试
			if strings.Contains(err.Error(), "timeout") {
				logger.Errorf(ctx, "wait cce endpoint %s deleted failed: %v", pod.Name, err.Error())
				return err
			}
			// 找不到CEP是预期行为，继续测试
			logger.Infof(ctx, "CEP for pod %s not found, as expected after deletion", pod.Name)
			continue
		}
		if !deleted {
			logger.Errorf(ctx, "cce endpoint %s is not deleted", pod.Name)
			return fmt.Errorf("cce endpoint %s is not deleted", pod.Name)
		}
	}

	//删除优先级为1的psts
	if err = c.pstsClient.Namespace("default").Delete(ctx, caseItem.Name, metav1.DeleteOptions{}); err != nil {
		logger.Errorf(ctx, "delete psts failed: %v", err.Error())
		return err
	}

	// 2. 测试两个优先级相同子网如何分配ip，预期为随机分配到两个子网中
	//创建另一个优先级为0的psts
	caseItem.Priority = 0
	caseItem.SubnetID = c.config.PstsSubnetID[1:2]
	caseItem.Name = "psts-test2-p0"
	err = c.applyPSTSYaml(ctx, tmpl, caseItem)
	if err != nil {
		return err
	}
	//扩容deployment出来，观察podip
	newReplicaCount = int32(10)
	// 重新获取最新版本的deployment避免ResourceVersion冲突
	deploy, err = c.base.K8SClient.AppsV1().Deployments("default").Get(ctx, workloadName, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "get deployment failed(checkPSTS2-二次扩容前): %v", err.Error())
		return err
	}
	deploy.Spec.Replicas = &newReplicaCount
	_, err = c.base.K8SClient.AppsV1().Deployments("default").Update(ctx, deploy, metav1.UpdateOptions{})
	if err != nil {
		logger.Errorf(ctx, "update deployment failed(checkPSTS2-二次扩容到10个副本): %v", err.Error())
		return err
	}
	deployment.SetWantedReplicas(10)
	if err = common.WaitForResourceReady(ctx, &deployment); err != nil {
		return err
	}
	//拿第一次创建的psts的选定子网的cidr，第二次创建的psts的cidr没变直接继续用
	cidr0, err := c.getSubnetCidr(ctx, c.config.PstsSubnetID[0])
	if err != nil {
		logger.Errorf(ctx, "get subnet cidr failed: %v", err.Error())
		return err
	}
	// 使用caseItem中的MatchLabels来构建标签选择器
	pods, err = c.base.K8SClient.CoreV1().Pods("default").List(ctx, metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		logger.Errorf(ctx, "get pods of deployment failed: %v", err.Error())
		return err
	}
	//再次检查ip，预期这次ip分配在随机两个子网中，如果某次发现ip都分配在一个子网中就再来一次
	for _, pod := range pods.Items {
		if pod.Status.Phase != v1.PodRunning {
			logger.Errorf(ctx, "pod %s is not running", pod.Name)
			return fmt.Errorf("pod %s is not running", pod.Name)
		}
		ip := net.ParseIP(pod.Status.PodIP)
		isIPinCidr := cidr1.Contains(ip) || cidr0.Contains(ip)
		if !isIPinCidr {
			logger.Errorf(ctx, "pod %s ip %s is not in cidr %s or %s", pod.Name, pod.Status.PodIP, cidr1, cidr0)
			return fmt.Errorf("pod %s ip %s is not in cidr %s or %s", pod.Name, pod.Status.PodIP, cidr1, cidr0)
		}
	}
	// 删除pod，检查ip和cep是否释放
	if err = c.base.K8SClient.AppsV1().Deployments("default").Delete(ctx, workloadName, metav1.DeleteOptions{}); err != nil {
		logger.Errorf(ctx, "delete deployment %s failed: %v", workloadName, err.Error())
		return err
	}
	//检查cep是否删除
	for _, pod := range pods.Items {
		deleted, err := c.WaitCEPDeleted(ctx, "default", pod.Name)
		if err != nil {
			// 如果是因为超时导致的错误则返回错误，其他错误（如找不到CEP）则继续测试
			if strings.Contains(err.Error(), "timeout") {
				logger.Errorf(ctx, "wait cce endpoint %s deleted failed: %v", pod.Name, err.Error())
				return err
			}
			// 找不到CEP是预期行为，继续测试
			logger.Infof(ctx, "CEP for pod %s not found, as expected after deletion", pod.Name)
			continue
		}
		if !deleted {
			logger.Errorf(ctx, "cce endpoint %s is not deleted", pod.Name)
			return fmt.Errorf("cce endpoint %s is not deleted", pod.Name)
		}
	}
	//TODO: 检查ip是否释放，可以用vpc的接口 或者检查nrs上面

	// 清理psts
	// 清理指定子网策略
	if err = c.pstsClient.Namespace("default").Delete(ctx, caseItem.Name, metav1.DeleteOptions{}); err != nil {
		logger.Errorf(ctx, "delete psts failed: %v", err.Error())
		return err
	}
	if err = c.pstsClient.Namespace("default").Delete(ctx, pstsname1, metav1.DeleteOptions{}); err != nil {
		logger.Errorf(ctx, "delete psts failed: %v", err.Error())
		return err
	}

	return nil
}

// psts用fixip模式 sts部署
func (c *checkPSTS) checkPSTS3(ctx context.Context, tmpl *template.Template, caseItem *PSTS) error {
	// 清理同名资源，确保测试可以重复执行
	c.cleanupCaseResources(ctx, caseItem, "test3")

	err := c.applyPSTSYaml(ctx, tmpl, caseItem)
	if err != nil {
		return err
	}

	workloadName, err := c.applyWorkloadYaml(ctx, "StatefulSet", caseItem, fmt.Sprintf("test%d", caseItem.index))
	if err != nil {
		return err
	}

	// 这个statefulset是本地为了执行一些功能进行封装的结构体
	var statefulSet common.K8SStatefulSet
	if err = statefulSet.NewK8SStatefulSet(ctx, c.base, "default", workloadName); err != nil {
		return err
	}
	statefulSet.SetWantedReplicas(1)
	if err := common.WaitForResourceReady(ctx, &statefulSet); err != nil {
		return err
	}

	// 获取本次psts使用的子网的cidr
	cidr, err := c.getSubnetCidr(ctx, caseItem.SubnetID[0])
	if err != nil {
		return err
	}

	// 使用caseItem中的MatchLabels来构建标签选择器
	labelSelector := c.getLabelSelectorString(caseItem.MatchLabels)
	// 拿到sts的pod
	pods, err := c.base.K8SClient.CoreV1().Pods("default").List(ctx, metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		logger.Errorf(ctx, "get pods of statefulset failed: %v", err.Error())
		return err
	}

	//检查ip是否子网内分配
	if err = c.checkPodsIpsInSubnet(ctx, pods, cidr); err != nil {
		return err
	}

	// 更新replicas 扩容到10个
	_, err = statefulSet.ScaleUpOrDown(ctx, 10)
	if err != nil {
		logger.Errorf(ctx, "scale up statefulset failed(checkPSTS3-初次扩容到10个副本): %v", err.Error())
		return err
	}
	if err = common.WaitForResourceReady(ctx, &statefulSet); err != nil {
		return err
	}

	// 使用caseItem中的MatchLabels来构建标签选择器
	pods, err = c.base.K8SClient.CoreV1().Pods("default").List(ctx, metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		logger.Errorf(ctx, "get pods of statefulset failed: %v", err.Error())
		return err
	}

	//检查ip是否子网内分配,并记录pod名字，以及pod所在的节点ip
	if err = c.checkPodsIpsInSubnet(ctx, pods, cidr); err != nil {
		return err
	}
	var (
		stsPodNames       []string
		podName2IpMap     = make(map[string]string)
		podName2NodeIpMap = make(map[string]string)
	)
	for _, pod := range pods.Items {
		stsPodNames = append(stsPodNames, pod.Name)
		podName2IpMap[pod.Name] = pod.Status.PodIP
		podName2NodeIpMap[pod.Name] = pod.Status.HostIP
	}

	// 缩容到1个
	_, err = statefulSet.ScaleUpOrDown(ctx, 1)
	if err != nil {
		logger.Errorf(ctx, "scale down statefulset failed(checkPSTS3-缩容到1个副本): %v", err.Error())
		return err
	}
	if err = common.WaitForResourceReady(ctx, &statefulSet); err != nil {
		return err
	}

	//检查cep是否正确保留，并记录cep名与对应ip

	for _, stsPodName := range stsPodNames {
		rawCep, err := c.cepClient.Namespace("default").Get(ctx, stsPodName, metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "get cep failed: %v", err.Error())
			return err
		}
		// 读取 spec 下的 pod-name
		podName, found, err := unstructured.NestedString(rawCep.Object, "spec", "external-identifiers", "pod-name")
		if err != nil || !found {
			log.Fatalf("Failed to get pod-name from spec: %v", err)
		}
		// 读取 status 下的 ips, 是一个string
		rawCepIp, found, err := unstructured.NestedString(rawCep.Object, "status", "networking", "ips")
		if err != nil || !found {
			log.Fatalf("Failed to get ips from status: %v", err)
		}
		// 检查一下是否cep的ip与pod ip一致
		//（其实没太大必要，因为psts的情况下cep ip和pod ip是同一次访问iaas获得的，pod ip是根据cep得到的写进来的）
		if rawCepIp != podName2IpMap[podName] {
			return fmt.Errorf("cep ip is not the same with pod ip, pod name: %s, pod ip: %s, cep ip: %s", podName, podName2IpMap[podName], rawCepIp)
		}
	}

	// 重新扩容到10个，根据cep记录检查ip是否保持不变
	// 这里要涵盖下述场景：
	// 跨节点调度sts fixip类型的pod时，pod的ip保持不变，
	// 实现方法为：每次扩缩容检查是不是有pod的节点ip变了，然后它的cep与pod的ip保持不变，如果这次恰好没有跨节点调度的pod就再来一次
	var (
		checked bool = false
		count   int  = 0
	)
	for {
		count++
		_, err = statefulSet.ScaleUpOrDown(ctx, 10)
		if err != nil {
			logger.Errorf(ctx, "scale up statefulset failed(checkPSTS3-二次扩容到10个副本): %v", err.Error())
			return err
		}
		if err = common.WaitForResourceReady(ctx, &statefulSet); err != nil {
			return err
		}

		// 使用caseItem中的MatchLabels来构建标签选择器
		pods, err = c.base.K8SClient.CoreV1().Pods("default").List(ctx, metav1.ListOptions{
			LabelSelector: labelSelector,
		})
		if err != nil {
			logger.Errorf(ctx, "get pods of statefulset failed: %v", err.Error())
			return err
		}

		for _, pod := range pods.Items {
			//sts pod重建之后名字不变，这里检查它ip也不变
			if pod.Status.PodIP != podName2IpMap[pod.Name] {
				return fmt.Errorf("pod ip changed after recreated, pod name: %s, pod ip(got): %s, cep ip(expected): %s", pod.Name, pod.Status.PodIP, podName2IpMap[pod.Name])
			}
			if pod.Status.HostIP != podName2NodeIpMap[pod.Name] {
				checked = true
			}
		}
		if checked {
			break
		} else {
			_, err = statefulSet.ScaleUpOrDown(ctx, 1)
			if err != nil {
				logger.Errorf(ctx, "scale down statefulset failed(checkPSTS3-二次缩容到1个副本): %v", err.Error())
				return err
			}
			if err = common.WaitForResourceReady(ctx, &statefulSet); err != nil {
				return err
			}
		}
		if count >= 5 {
			return fmt.Errorf("scale up and down 5 times, but no pod's node ip changed, check if cluster has only one node")
		}
	}

	// 删除sts，并检查cep是否都正确删除
	//【!!！】这里注意一下这个还没实现，产品上删除sts紧接着应该删除掉所有cep。
	// 但是目前逻辑中sts的cep的releaseStrategy标注为never，永不删除。
	// 故只能手动删除cep或一直保留在集群里。【！！！】
	if err = c.base.K8SClient.AppsV1().StatefulSets("default").Delete(ctx, workloadName, metav1.DeleteOptions{}); err != nil {
		logger.Errorf(ctx, "delete statefulset %s failed: %v", workloadName, err.Error())
		return err
	}
	// 这是正常流程中要检查的
	// for _, name := range stsPodNames {
	// 	_, err := c.cepClient.Namespace("default").Get(ctx, name, metav1.GetOptions{})
	// 	if err != nil {
	// 		logger.Errorf(ctx, "get cep failed: %v", err.Error())
	// 		if strings.Contains(err.Error(), "not found") {
	// 			continue
	// 		} else {
	// 			return err
	// 		}
	// 	}
	// }

	//这是这里手动删除cep的操作
	for _, name := range stsPodNames {
		if err = c.cepClient.Namespace("default").Delete(ctx, name, metav1.DeleteOptions{}); err != nil {
			// 只有在非"not found"类型的错误时才返回错误
			if !strings.Contains(err.Error(), "not found") {
				logger.Errorf(ctx, "delete cep failed: %v", err.Error())
				return err
			}
			// 找不到CEP是合理的，可能已经被其他逻辑删除了
			logger.Infof(ctx, "CEP %s already deleted", name)
		}
	}

	//删除psts，测试结束
	if err = c.pstsClient.Namespace("default").Delete(ctx, caseItem.Name, metav1.DeleteOptions{}); err != nil {
		logger.Errorf(ctx, "delete psts failed(checkPSTS3-清理测试): %v", err.Error())
		return err
	}

	return nil
}

func (c *checkPSTS) genDeploymentYaml(name string, matchLabels map[string]string, namespace string) string {
	content := fmt.Sprintf(`apiVersion: apps/v1
kind: Deployment
metadata:
  name: %s
  namespace: %s
spec:
  replicas: 1
  selector:
    matchLabels:
`, name, namespace)

	// 添加matchLabels
	for k, v := range matchLabels {
		content += fmt.Sprintf("      %s: %s\n", k, v)
	}

	content += `  template:
    metadata:
      labels:
`
	// 添加Pod标签
	for k, v := range matchLabels {
		content += fmt.Sprintf("        %s: %s\n", k, v)
	}

	content += `    spec:
      containers:
      - name: nginx
        image: hub.baidubce.com/cce/nginx-alpine-go:latest`

	return content
}

func (c *checkPSTS) genStatefulSetYaml(name string, matchLabels map[string]string, namespace string) string {
	content := fmt.Sprintf(`apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: %s
  namespace: %s
spec:
  replicas: 1
  serviceName: %s-headless
  selector:
    matchLabels:
`, name, namespace, name)

	// 添加matchLabels
	for k, v := range matchLabels {
		content += fmt.Sprintf("      %s: %s\n", k, v)
	}

	content += `  template:
    metadata:
      labels:
`
	// 添加Pod标签
	for k, v := range matchLabels {
		content += fmt.Sprintf("        %s: %s\n", k, v)
	}

	content += `    spec:
      containers:
      - name: nginx
        image: hub.baidubce.com/cce/nginx-alpine-go:latest`

	return content
}

func (c *checkPSTS) getLabelSelectorString(matchLabels map[string]string) string {
	labelSelector := ""
	for k, v := range matchLabels {
		if labelSelector != "" {
			labelSelector += ","
		}
		labelSelector += fmt.Sprintf("%s=%s", k, v)
	}
	return labelSelector
}
