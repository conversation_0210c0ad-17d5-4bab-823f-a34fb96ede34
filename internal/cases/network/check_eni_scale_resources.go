/*
CheckENIScaleResources 测试用例

@author:  max<PERSON><PERSON>
@date:    2025-04-18

功能描述：
校验指定ENI最大辅助IP数(bce-cusstomer-max-ip参数)生效
校验指定单机最大ENI数(max-allocate-eni参数)生效（产品取消支持）
校验单节点的预创建ENI数(eni-pre-allocate-num参数)生效
校验单节点预留水位参数(eni-pre-allocate-num参数)生效
*/

package network

import (
	"context"
	"fmt"
	"strings"
	"time"

	v1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	resourceapi "k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	cceresource "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	// CheckENIScaleResourcesCaseName - case 名字
	CheckENIScaleResourcesCaseName cases.CaseName = "CheckENIScaleResources"
	// 测试资源名称
	testDeploymentName      = "dep-test-ip-release"
	testDeploymentNamespace = "default"
	// CNI 组件
	cniAgentName     = "cce-network-agent"
	cniOperatorName  = "cce-network-operator"
	cniConfigMapName = "cce-network-v2-config"
	cniNamespace     = "kube-system"
	// 等待超时设置
	waitTimeout       = 10 * time.Minute
	waitInterval      = 5 * time.Second
	waitShortTimeout  = 2 * time.Minute
	waitShortInterval = 3 * time.Second
)

// checkENIScaleResources 是验证节点扩容后ENI相关资源正确性的测试用例
type checkENIScaleResources struct {
	base                  *cases.BaseClient
	resources             []cases.Resource
	instanceGroup         string                    // 用于扩容的节点组ID
	originalReplicas      int                       // 节点组原始副本数
	newNodeName           string                    // 新扩容节点名称
	initialMaxENIPerNode  int                       // 初始节点最大ENI数
	initialMaxIPPerENI    int                       // 初始节点每个ENI最大IP数
	configCustomerMaxIP   int                       // 配置的全局最大IP数
	configPreAllocateENI  int                       // 配置的预分配ENI数
	configPreAllocateIP   int                       // 配置的预分配IP数
	configMinAllocateIP   int                       // 配置的最小分配IP数
	configMaxAllocateENI  int                       // 配置的最大分配ENI数
	originalConfigContent string                    // 原始配置内容
	nodeEniInfo           map[string]map[string]int // 节点ENI信息，key为节点名称，value为ENI信息map
	initialENINames       []string                  // 初始预分配的ENI名称列表
	createdNodeGroup      bool                      // 标记是否是我们创建的节点组
	eniID                 string                    // 测试创建的ENI ID
}

func init() {
	// 注册测试用例
	cases.AddCase(context.TODO(), CheckENIScaleResourcesCaseName, NewCheckENIScaleResources)
}

// NewCheckENIScaleResources 创建新的测试用例实例
func NewCheckENIScaleResources(ctx context.Context) cases.Interface {
	return &checkENIScaleResources{
		nodeEniInfo: make(map[string]map[string]int),
	}
}

// Init 初始化测试用例
func (c *checkENIScaleResources) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base client is nil")
	}
	c.base = base
	return nil
}

// Name 返回测试用例名称
func (c *checkENIScaleResources) Name() cases.CaseName {
	return CheckENIScaleResourcesCaseName
}

// Desc 返回测试用例描述
func (c *checkENIScaleResources) Desc() string {
	return "测试节点扩容后ENI相关资源的正确性"
}

// Check 执行测试
func (c *checkENIScaleResources) Check(ctx context.Context) ([]cases.Resource, error) {
	// 显示测试开始信息
	logger.Infof(ctx, "==== 开始执行节点扩容后ENI资源状态测试 ====")
	startTime := time.Now()

	// 阶段1: 修改CNI配置，设置自定义参数
	logger.Infof(ctx, "\n===== 阶段1: 修改CNI配置，设置自定义参数 =====")
	if err := c.phaseUpdateCNIConfig(ctx); err != nil {
		return c.resources, fmt.Errorf("阶段1测试失败: %v", err)
	}

	// 阶段2: 执行节点扩容，验证ENI预创建
	logger.Infof(ctx, "\n===== 阶段2: 执行节点扩容，验证ENI预创建 =====")
	if err := c.phaseScaleNode(ctx); err != nil {
		return c.resources, fmt.Errorf("阶段2测试失败: %v", err)
	}

	// 阶段3: 验证新节点IP分配是否符合配置
	logger.Infof(ctx, "\n===== 阶段3: 验证新节点IP分配是否符合配置 =====")
	if err := c.phaseDeployWorkload(ctx); err != nil {
		return c.resources, fmt.Errorf("阶段3测试失败: %v", err)
	}

	// 显示测试完成信息
	totalTime := time.Since(startTime).Round(time.Second)
	logger.Infof(ctx, "\n===== 节点扩容ENI资源状态测试完成 =====")
	logger.Infof(ctx, "测试总耗时: %v", totalTime)
	logger.Infof(ctx, "测试结果: 通过")

	return c.resources, nil
}

// Continue 判断测试是否应该继续
func (c *checkENIScaleResources) Continue(ctx context.Context) bool {
	return true
}

// ConfigFormat 返回配置格式
func (c *checkENIScaleResources) ConfigFormat() string {
	return ""
}

// Clean 清理测试资源
func (c *checkENIScaleResources) Clean(ctx context.Context) error {
	var lastErr error

	// 1. 清理测试过程中创建的ENI
	if c.eniID != "" {
		logger.Infof(ctx, "开始删除测试创建的ENI: %s", c.eniID)
		err := c.base.ENIClient.DeleteENI(ctx, c.eniID, nil)
		if err != nil {
			logger.Warnf(ctx, "删除ENI失败: %v", err)
			lastErr = err
		} else {
			logger.Infof(ctx, "成功删除ENI: %s", c.eniID)
		}
	}

	// 2. 清理deployment
	logger.Infof(ctx, "删除测试Deployment")
	if err := c.cleanTestDeployment(ctx); err != nil {
		return fmt.Errorf("删除测试Deployment失败: %v", err)
	}

	// 3. 恢复节点组副本数量或删除节点组
	if c.instanceGroup != "" {
		// 如果是我们创建的节点组，则先缩容到0，然后删除
		if c.createdNodeGroup {
			// 先获取节点组当前状态
			ig, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroup, nil)
			if err != nil {
				logger.Warnf(ctx, "获取节点组状态失败: %v", err)
				if lastErr == nil {
					lastErr = err
				}
			} else {
				// 如果节点组当前副本数不为0，先缩容到0
				if ig.InstanceGroup.Spec.Replicas > 0 {
					logger.Infof(ctx, "节点组 %s 当前副本数: %d, 开始缩容到0...",
						c.instanceGroup, ig.InstanceGroup.Spec.Replicas)

					// 使用UpdateInstanceGroupReplicas替代CreateScaleUpInstanceGroupTask
					request := &ccev2.UpdateInstanceGroupReplicasRequest{
						Replicas: 0,
					}

					resp, err := c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, c.base.ClusterID, c.instanceGroup, request, nil)
					if err != nil {
						logger.Warnf(ctx, "缩容节点组到0失败: %v", err)
						if lastErr == nil {
							lastErr = err
						}
					} else {
						logger.Infof(ctx, "已发送缩容节点组到0的请求，RequestID: %s", resp.RequestID)

						// 等待缩容完成，最多等待10分钟
						timeout := 10 * time.Minute
						deadline := time.Now().Add(timeout)

						logger.Infof(ctx, "等待节点组缩容到0完成...")
						for time.Now().Before(deadline) {
							// 检查当前副本数
							currentIG, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroup, nil)
							if err != nil {
								logger.Warnf(ctx, "获取节点组状态失败: %v", err)
								break
							}

							// 如果当前Ready副本数为0，说明缩容完成
							if currentIG.InstanceGroup.Status.ReadyReplicas == 0 {
								logger.Infof(ctx, "节点组 %s 已成功缩容到0个节点", c.instanceGroup)
								break
							}

							logger.Infof(ctx, "节点组 %s 当前Ready副本数: %d，继续等待...",
								c.instanceGroup, currentIG.InstanceGroup.Status.ReadyReplicas)
							time.Sleep(30 * time.Second)
						}

						// 检查是否超时
						if time.Now().After(deadline) {
							logger.Warnf(ctx, "等待节点组缩容到0超时(%v)，将继续尝试删除节点组", timeout)
						}
					}
				}

				// 缩容完成或失败后，尝试删除节点组
				logger.Infof(ctx, "删除测试创建的节点组 %s", c.instanceGroup)
				_, err = c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.instanceGroup, true, nil)
				if err != nil {
					logger.Warnf(ctx, "删除测试创建的节点组失败: %v", err)
					if lastErr == nil {
						lastErr = err
					}
				} else {
					logger.Infof(ctx, "成功删除测试创建的节点组 %s", c.instanceGroup)
				}
			}
		} else if c.originalReplicas > 0 {
			// 非创建的节点组则恢复副本数
			logger.Infof(ctx, "恢复节点组 %s 副本数至 %d", c.instanceGroup, c.originalReplicas)

			// 使用UpdateInstanceGroupReplicas替代CreateScaleUpInstanceGroupTask
			request := &ccev2.UpdateInstanceGroupReplicasRequest{
				Replicas: c.originalReplicas,
			}

			resp, err := c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, c.base.ClusterID, c.instanceGroup, request, nil)
			if err != nil {
				logger.Warnf(ctx, "恢复节点组副本数失败: %v", err)
				if lastErr == nil {
					lastErr = err
				}
			} else {
				logger.Infof(ctx, "已发送恢复节点组副本数请求，RequestID: %s", resp.RequestID)
			}
		}
	}

	logger.Infof(ctx, "资源清理完成")
	return lastErr
}

// phaseUpdateCNIConfig 阶段1：修改CNI配置
func (c *checkENIScaleResources) phaseUpdateCNIConfig(ctx context.Context) error {
	// 1. 获取当前CNI配置
	logger.Infof(ctx, "获取当前CNI配置")
	configMap, err := c.base.K8SClient.CoreV1().ConfigMaps(cniNamespace).Get(ctx, cniConfigMapName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取ConfigMap失败: %v", err)
	}

	// 保存原始配置以便后续恢复
	ccedConfig, ok := configMap.Data["cced"]
	if !ok {
		return fmt.Errorf("ConfigMap中不存在cced配置")
	}
	c.originalConfigContent = ccedConfig

	logger.Infof(ctx, "当前CNI配置:\n%s", ccedConfig)

	// 2. 修改配置
	// 要修改的配置参数
	c.configCustomerMaxIP = 12
	c.configPreAllocateENI = 3
	c.configPreAllocateIP = 8
	c.configMinAllocateIP = 2
	maxAllocateENI := 4 // 设置最大分配ENI数为4
	c.configMaxAllocateENI = maxAllocateENI

	// 提取现有配置的所有行，移除要修改的参数行
	var preservedLines []string
	modificationsNeeded := map[string]bool{
		"burstable-mehrfach-eni:":  true,
		"ippool-pre-allocate:":     true,
		"eni-pre-allocate-num:":    true,
		"ippool-min-allocate-ips:": true,
		"bce-customer-max-ip:":     true,
		"max-allocate-eni:":        true,
		"release-excess-ips:":      true,
	}

	for _, line := range strings.Split(ccedConfig, "\n") {
		trimmedLine := strings.TrimSpace(line)
		skipLine := false

		for prefix := range modificationsNeeded {
			if strings.HasPrefix(trimmedLine, prefix) {
				skipLine = true
				break
			}
		}

		if !skipLine && trimmedLine != "" {
			preservedLines = append(preservedLines, line)
		}
	}

	// 构建新的配置参数
	newParams := []string{
		"burstable-mehrfach-eni: 0",
		fmt.Sprintf("ippool-pre-allocate: %d", c.configPreAllocateIP),
		fmt.Sprintf("eni-pre-allocate-num: %d", c.configPreAllocateENI),
		fmt.Sprintf("ippool-min-allocate-ips: %d", c.configMinAllocateIP),
		fmt.Sprintf("bce-customer-max-ip: %d", c.configCustomerMaxIP),
		fmt.Sprintf("max-allocate-eni: %d", maxAllocateENI),
		"release-excess-ips: false",
	}

	// 将新参数添加到配置中适当的位置
	newConfig := ""
	paramInserted := false
	for _, line := range preservedLines {
		newConfig += line + "\n"

		// 找到合适的位置插入参数（在第一个非空白行后）
		if !paramInserted && line != "" && !strings.HasPrefix(strings.TrimSpace(line), "#") {
			for _, param := range newParams {
				newConfig += param + "\n"
			}
			paramInserted = true
		}
	}

	// 如果没有找到合适的位置，则添加到末尾
	if !paramInserted {
		for _, param := range newParams {
			newConfig += param + "\n"
		}
	}

	// 3. 更新ConfigMap
	logger.Infof(ctx, "更新CNI配置")
	if err := c.updateCNIConfig(ctx, newConfig); err != nil {
		return fmt.Errorf("更新CNI配置失败: %v", err)
	}

	// 4. 重启CNI组件
	logger.Infof(ctx, "重启CNI组件使配置生效")
	if err := c.restartCNIComponents(ctx); err != nil {
		return fmt.Errorf("重启CNI组件失败: %v", err)
	}

	logger.Infof(ctx, "CNI配置已更新并重启完成")
	return nil
}

// updateCNIConfig 更新CNI配置
func (c *checkENIScaleResources) updateCNIConfig(ctx context.Context, newConfig string) error {
	// 获取当前ConfigMap
	configMap, err := c.base.K8SClient.CoreV1().ConfigMaps(cniNamespace).Get(ctx, cniConfigMapName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取ConfigMap失败: %v", err)
	}

	// 更新配置
	configMap.Data["cced"] = newConfig

	// 更新ConfigMap
	_, err = c.base.K8SClient.CoreV1().ConfigMaps(cniNamespace).Update(ctx, configMap, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新ConfigMap失败: %v", err)
	}

	return nil
}

// restartCNIComponents 重启CNI组件
func (c *checkENIScaleResources) restartCNIComponents(ctx context.Context) error {
	// 1. 重启 DaemonSet cce-network-agent
	logger.Infof(ctx, "重启 DaemonSet %s", cniAgentName)
	if err := c.restartDaemonSet(ctx, cniAgentName, cniNamespace); err != nil {
		return fmt.Errorf("重启DaemonSet %s 失败: %v", cniAgentName, err)
	}

	// 2. 重启 Deployment cce-network-operator
	logger.Infof(ctx, "重启 Deployment %s", cniOperatorName)
	if err := c.restartDeployment(ctx, cniOperatorName, cniNamespace); err != nil {
		return fmt.Errorf("重启Deployment %s 失败: %v", cniOperatorName, err)
	}

	// 3. 等待组件就绪
	logger.Infof(ctx, "等待CNI组件就绪")
	if err := c.waitForCNIComponentsReady(ctx); err != nil {
		return fmt.Errorf("等待CNI组件就绪失败: %v", err)
	}

	logger.Infof(ctx, "CNI组件已重启并就绪")
	return nil
}

// restartDaemonSet 重启DaemonSet
func (c *checkENIScaleResources) restartDaemonSet(ctx context.Context, name, namespace string) error {
	// 获取DaemonSet
	ds, err := c.base.K8SClient.AppsV1().DaemonSets(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取DaemonSet失败: %v", err)
	}

	// 添加重启注解
	if ds.Spec.Template.ObjectMeta.Annotations == nil {
		ds.Spec.Template.ObjectMeta.Annotations = make(map[string]string)
	}
	ds.Spec.Template.ObjectMeta.Annotations["kubectl.kubernetes.io/restartedAt"] = time.Now().Format(time.RFC3339)

	// 更新DaemonSet
	_, err = c.base.K8SClient.AppsV1().DaemonSets(namespace).Update(ctx, ds, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新DaemonSet失败: %v", err)
	}

	return nil
}

// restartDeployment 重启Deployment
func (c *checkENIScaleResources) restartDeployment(ctx context.Context, name, namespace string) error {
	// 获取Deployment
	deploy, err := c.base.K8SClient.AppsV1().Deployments(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取Deployment失败: %v", err)
	}

	// 添加重启注解
	if deploy.Spec.Template.ObjectMeta.Annotations == nil {
		deploy.Spec.Template.ObjectMeta.Annotations = make(map[string]string)
	}
	deploy.Spec.Template.ObjectMeta.Annotations["kubectl.kubernetes.io/restartedAt"] = time.Now().Format(time.RFC3339)

	// 更新Deployment
	_, err = c.base.K8SClient.AppsV1().Deployments(namespace).Update(ctx, deploy, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新Deployment失败: %v", err)
	}

	return nil
}

// waitForCNIComponentsReady 等待CNI组件就绪
func (c *checkENIScaleResources) waitForCNIComponentsReady(ctx context.Context) error {
	time.Sleep(waitInterval)
	// 等待DaemonSet就绪
	err := wait.PollImmediate(waitInterval, waitTimeout, func() (bool, error) {
		ds, err := c.base.K8SClient.AppsV1().DaemonSets(cniNamespace).Get(ctx, cniAgentName, metav1.GetOptions{})
		if err != nil {
			return false, err
		}

		logger.Infof(ctx, "等待DaemonSet %s 就绪: %d/%d",
			cniAgentName, ds.Status.NumberReady, ds.Status.DesiredNumberScheduled)

		if ds.Status.NumberReady == ds.Status.DesiredNumberScheduled &&
			ds.Status.NumberReady > 0 {
			return true, nil
		}
		return false, nil
	})
	if err != nil {
		return fmt.Errorf("等待DaemonSet就绪超时: %v", err)
	}

	// 等待Deployment就绪
	err = wait.PollImmediate(waitInterval, waitTimeout, func() (bool, error) {
		deploy, err := c.base.K8SClient.AppsV1().Deployments(cniNamespace).Get(ctx, cniOperatorName, metav1.GetOptions{})
		if err != nil {
			return false, err
		}

		logger.Infof(ctx, "等待Deployment %s 就绪: %d/%d",
			cniOperatorName, deploy.Status.ReadyReplicas, *deploy.Spec.Replicas)

		if deploy.Status.ReadyReplicas == *deploy.Spec.Replicas &&
			deploy.Status.ReadyReplicas > 0 {
			return true, nil
		}
		return false, nil
	})
	if err != nil {
		return fmt.Errorf("等待Deployment就绪超时: %v", err)
	}

	return nil
}

// phaseScaleNode 阶段2：执行节点扩容，验证ENI预创建
func (c *checkENIScaleResources) phaseScaleNode(ctx context.Context) error {
	// 记录扩容前已有的节点名称
	logger.Infof(ctx, "记录扩容前已有的节点名称")
	existingNodes := make(map[string]bool)
	nodes, err := c.base.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取节点列表失败: %v", err)
	}
	for _, node := range nodes.Items {
		existingNodes[node.Name] = true
		logger.Infof(ctx, "已有节点: %s", node.Name)
	}

	// 0. 尝试创建测试用的节点组
	logger.Infof(ctx, "尝试创建测试用的节点组")
	if err := c.createInstanceGroup(ctx); err != nil {
		return fmt.Errorf("创建测试用节点组失败: %v", err)
	}

	// 1. 获取可用的节点组
	logger.Infof(ctx, "获取集群中的节点组")
	instanceGroupID, err := c.getInstanceGroupID(ctx)
	if err != nil {
		return fmt.Errorf("获取节点组ID失败: %v", err)
	}
	c.instanceGroup = instanceGroupID

	// 2. 获取节点组当前副本数
	logger.Infof(ctx, "获取节点组 %s 的当前副本数", instanceGroupID)
	originalReplicas, err := c.getInstanceGroupReplicas(ctx, instanceGroupID)
	if err != nil {
		return fmt.Errorf("获取节点组副本数失败: %v", err)
	}
	c.originalReplicas = originalReplicas
	logger.Infof(ctx, "节点组 %s 当前副本数: %d", instanceGroupID, originalReplicas)

	// 3. 扩容节点组
	newReplicas := originalReplicas + 1
	logger.Infof(ctx, "扩容节点组 %s 副本数从 %d 到 %d", instanceGroupID, originalReplicas, newReplicas)
	if err := c.scaleInstanceGroup(ctx, instanceGroupID, newReplicas); err != nil {
		return fmt.Errorf("扩容节点组失败: %v", err)
	}

	// 4. 等待节点扩容完成
	logger.Infof(ctx, "等待节点扩容完成")
	if err := c.waitForNodeScaleComplete(ctx, newReplicas); err != nil {
		return fmt.Errorf("等待节点扩容完成失败: %v", err)
	}

	// 5. 识别新扩容的节点
	logger.Infof(ctx, "识别新扩容的节点")
	nodes, err = c.base.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取节点列表失败: %v", err)
	}

	var newNode *corev1.Node
	for i := range nodes.Items {
		node := &nodes.Items[i]
		if _, exists := existingNodes[node.Name]; !exists {
			// 检查节点是否就绪
			ready := false
			for _, condition := range node.Status.Conditions {
				if condition.Type == corev1.NodeNetworkUnavailable && condition.Status == corev1.ConditionFalse {
					ready = true
					break
				}
			}
			if ready {
				newNode = node
				break
			}
		}
	}

	if newNode == nil {
		return fmt.Errorf("未找到新扩容的节点")
	}

	c.newNodeName = newNode.Name
	logger.Infof(ctx, "发现新扩容的节点: %s", c.newNodeName)

	// 6. 检查新节点的ENI和IP资源
	logger.Infof(ctx, "检查新节点 %s 的ENI和IP资源", c.newNodeName)
	if err := c.checkNewNodeResources(ctx, c.newNodeName); err != nil {
		return fmt.Errorf("检查新节点资源失败: %v", err)
	}

	// 7. 计算节点可分配的最大ENI数量
	logger.Infof(ctx, "计算节点 %s 可分配的最大ENI数量", c.newNodeName)
	if err := c.calculateNodeMaxENI(ctx, c.newNodeName); err != nil {
		return fmt.Errorf("计算节点最大ENI数量失败: %v", err)
	}

	return nil
}

// getInstanceGroupID 获取一个可用的节点组ID
func (c *checkENIScaleResources) getInstanceGroupID(ctx context.Context) (string, error) {
	// 获取集群中的所有节点组
	instanceGroups, err := c.base.CCEClient.ListInstanceGroups(ctx, c.base.ClusterID, nil, nil)
	if err != nil {
		return "", fmt.Errorf("获取节点组列表失败: %v", err)
	}

	if len(instanceGroups.Page.List) == 0 {
		return "", fmt.Errorf("集群中没有节点组")
	}

	// 选择一个非空的节点组(优先选择有就绪节点的节点组)
	for _, ig := range instanceGroups.Page.List {
		if ig.Status.ReadyReplicas > 0 {
			return ig.Spec.CCEInstanceGroupID, nil
		}
	}

	// 如果没有就绪节点的节点组，选择第一个
	return instanceGroups.Page.List[0].Spec.CCEInstanceGroupID, nil
}

// getInstanceGroupReplicas 获取节点组当前的副本数
func (c *checkENIScaleResources) getInstanceGroupReplicas(ctx context.Context, instanceGroupID string) (int, error) {
	// 获取节点组详情
	instanceGroup, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, instanceGroupID, nil)
	if err != nil {
		return 0, fmt.Errorf("获取节点组详情失败: %v", err)
	}

	return instanceGroup.InstanceGroup.Spec.Replicas, nil
}

// scaleInstanceGroup 扩缩容节点组
func (c *checkENIScaleResources) scaleInstanceGroup(ctx context.Context, instanceGroupID string, replicas int) error {
	// 先获取节点组的当前状态
	ig, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, instanceGroupID, nil)
	if err != nil {
		return fmt.Errorf("获取节点组信息失败: %v", err)
	}

	currentReplicas := ig.InstanceGroup.Spec.Replicas
	if currentReplicas == replicas {
		logger.Infof(ctx, "节点组 %s 当前副本数已经是目标值 %d，无需调整", instanceGroupID, replicas)
		return nil
	}

	// 获取节点组当前的实例列表
	instancesResp, err := c.base.CCEClient.ListInstancesByInstanceGroupID(ctx, c.base.ClusterID, instanceGroupID, 0, 0, nil)
	if err != nil {
		logger.Warnf(ctx, "获取节点组实例列表失败: %v", err)
	} else {
		logger.Infof(ctx, "节点组 %s 当前有 %d 个实例", instanceGroupID, len(instancesResp.Page.List))
		for i, instance := range instancesResp.Page.List {
			if i < 5 { // 只显示前5个实例信息
				logger.Infof(ctx, "  实例 #%d: ID=%s, 名称=%s, 状态=%s",
					i+1, instance.Spec.CCEInstanceID, instance.Spec.InstanceName, instance.Status.InstancePhase)
			} else {
				logger.Infof(ctx, "  还有 %d 个实例未显示...", len(instancesResp.Page.List)-5)
				break
			}
		}
	}

	operation := "扩容"
	if replicas < currentReplicas {
		operation = "缩容"
	}

	logger.Infof(ctx, "开始执行节点组 %s %s操作: %d -> %d", instanceGroupID, operation, currentReplicas, replicas)

	// 创建扩缩容任务
	var resp *ccev2.CreateTaskResp
	if replicas > currentReplicas {
		// 扩容操作
		resp, err = c.base.CCEClient.CreateScaleUpInstanceGroupTask(ctx, c.base.ClusterID, instanceGroupID, replicas, nil)
	} else {
		// 缩容操作，不指定具体实例，让CCE根据策略选择要删除的节点
		resp, err = c.base.CCEClient.CreateScaleDownInstanceGroupTask(ctx, c.base.ClusterID, instanceGroupID, nil, nil)
	}

	if err != nil {
		return fmt.Errorf("创建节点组%s任务失败: %v", operation, err)
	}

	logger.Infof(ctx, "节点组 %s %s请求发送成功，TaskID: %s", instanceGroupID, operation, resp.TaskID)

	// 再次获取节点组状态以确认请求已被接受
	time.Sleep(2 * time.Second)
	ig, err = c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, instanceGroupID, nil)
	if err != nil {
		logger.Warnf(ctx, "获取更新后的节点组信息失败: %v", err)
	} else {
		// 显示节点组当前的扩缩容状态
		logger.Infof(ctx, "节点组 %s 当前状态: ", instanceGroupID)
		logger.Infof(ctx, "  期望副本数: %d", ig.InstanceGroup.Spec.Replicas)
		logger.Infof(ctx, "  就绪副本数: %d", ig.InstanceGroup.Status.ReadyReplicas)
		logger.Infof(ctx, "  扩缩容中副本数: %d", ig.InstanceGroup.Status.ScalingReplicas)
		logger.Infof(ctx, "  删除中副本数: %d", ig.InstanceGroup.Status.DeletingReplicas)
	}

	return nil
}

// waitForNodeScaleComplete 等待节点扩缩容完成
func (c *checkENIScaleResources) waitForNodeScaleComplete(ctx context.Context, targetReplicas int) error {
	logger.Infof(ctx, "开始等待节点组扩缩容操作完成")

	// 获取当前所有节点，记录原始节点列表
	initialNodes := make(map[string]struct{})
	nodes, err := c.base.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取节点列表失败: %v", err)
	}
	for _, node := range nodes.Items {
		initialNodes[node.Name] = struct{}{}
		logger.Infof(ctx, "初始节点: %s", node.Name)
	}

	// 开始监控节点组状态
	statusCheckInterval := 10 * time.Second
	timeout := 15 * time.Minute
	startTime := time.Now()
	endTime := startTime.Add(timeout)

	// 用于跟踪节点变化
	previousNodeCount := len(nodes.Items)

	ticker := time.NewTicker(statusCheckInterval)
	defer ticker.Stop()

	for range ticker.C {
		if time.Now().After(endTime) {
			return fmt.Errorf("等待节点组扩缩容超时 (%v)", timeout)
		}

		// 1. 获取当前节点列表
		nodes, err := c.base.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取节点列表失败: %v, 将在 %v 后重试", err, statusCheckInterval)
			continue
		}

		// 清空当前节点集合并重新填充
		currentNodes := make(map[string]struct{})
		for _, node := range nodes.Items {
			currentNodes[node.Name] = struct{}{}
		}
		currentNodeCount := len(nodes.Items)

		// 记录节点数量变化
		if currentNodeCount != previousNodeCount {
			logger.Infof(ctx, "节点数量变化: %d -> %d", previousNodeCount, currentNodeCount)
			previousNodeCount = currentNodeCount
		}

		// 找出新增的节点
		newNodes := make([]string, 0)
		for nodeName := range currentNodes {
			if _, exists := initialNodes[nodeName]; !exists {
				newNodes = append(newNodes, nodeName)
			}
		}

		// 找出被删除的节点
		deletedNodes := make([]string, 0)
		for nodeName := range initialNodes {
			if _, exists := currentNodes[nodeName]; !exists {
				deletedNodes = append(deletedNodes, nodeName)
			}
		}

		if len(newNodes) > 0 {
			logger.Infof(ctx, "新增节点: %v", newNodes)
		}
		if len(deletedNodes) > 0 {
			logger.Infof(ctx, "删除的节点: %v", deletedNodes)
		}

		// 2. 检查节点组状态
		instanceGroup, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroup, nil)
		if err != nil {
			logger.Warnf(ctx, "获取节点组状态失败: %v, 将在 %v 后重试", err, statusCheckInterval)
			continue
		}

		// 记录节点组状态
		logger.Infof(ctx, "节点组状态: 期望副本数=%d, 实际副本数=%d, 就绪副本数=%d, 删除中=%d, 扩容中=%d",
			instanceGroup.InstanceGroup.Spec.Replicas,
			instanceGroup.InstanceGroup.Status.ActualReplicas,
			instanceGroup.InstanceGroup.Status.ReadyReplicas,
			instanceGroup.InstanceGroup.Status.DeletingReplicas,
			instanceGroup.InstanceGroup.Status.ScalingReplicas)

		// 3. 判断扩缩容是否完成
		// 对于扩容操作
		if targetReplicas > c.originalReplicas {
			// 扩容完成条件: 就绪副本数等于目标副本数，且没有正在扩容的副本
			if instanceGroup.InstanceGroup.Status.ReadyReplicas == targetReplicas &&
				instanceGroup.InstanceGroup.Status.ScalingReplicas == 0 &&
				len(newNodes) > 0 {
				logger.Infof(ctx, "节点组扩容完成，耗时: %v", time.Since(startTime).Round(time.Second))
				// 设置新扩容的节点名称
				if len(newNodes) == 1 {
					c.newNodeName = newNodes[0]
					logger.Infof(ctx, "设置新扩容节点名称: %s", c.newNodeName)
					return nil
				} else if len(newNodes) > 1 {
					logger.Warnf(ctx, "检测到多个新节点: %v，使用第一个作为测试节点", newNodes)
					c.newNodeName = newNodes[0]
					return nil
				}
			}
		} else {
			// 缩容完成条件: 就绪副本数等于目标副本数，且没有正在删除的副本
			if instanceGroup.InstanceGroup.Status.ReadyReplicas == targetReplicas &&
				instanceGroup.InstanceGroup.Status.DeletingReplicas == 0 &&
				len(deletedNodes) > 0 {
				logger.Infof(ctx, "节点组缩容完成，耗时: %v", time.Since(startTime).Round(time.Second))
				return nil
			}
		}
	}

	// 添加返回语句以确保所有路径都有返回值
	return fmt.Errorf("等待节点组扩缩容操作被意外中断")
}

// checkNewNodeResources 检查新节点的资源状态
func (c *checkENIScaleResources) checkNewNodeResources(ctx context.Context, nodeName string) error {
	// 1. 检查NRS资源
	logger.Infof(ctx, "检查节点 %s 的NRS资源", nodeName)
	nrs, err := c.base.KubeClient.GetNrs(ctx, nodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取节点NRS资源失败: %v", err)
	}

	logger.Infof(ctx, "节点NRS中的配置参数:")
	logger.Infof(ctx, "  MaxIPsPerENI: %d", nrs.Spec.Eni.MaxIPsPerENI)
	logger.Infof(ctx, "  PreAllocateENI: %d", nrs.Spec.Eni.PreAllocateENI)
	logger.Infof(ctx, "  MinAllocate: %d", nrs.Spec.Ipam.MinAllocate)
	logger.Infof(ctx, "  PreAllocate: %d", nrs.Spec.Ipam.PreAllocate)
	logger.Infof(ctx, "  MaxAboveWatermark: %d", nrs.Spec.Ipam.MaxAboveWatermark)

	// 等待ENI和IP充分创建，持续检查状态并打印结果
	var nodeENIs []types.ENI
	startTime := time.Now()
	maxWaitTime := 5 * time.Minute
	checkInterval := 10 * time.Second

	logger.Infof(ctx, "开始等待节点ENI创建，最长等待时间: %v，检查间隔: %v", maxWaitTime, checkInterval)

	// 保存初始的3个ENI名称，用于后续验证
	initialENINames := []string{}

	for time.Since(startTime) < maxWaitTime {
		// 获取ENI列表
		eniList, err := c.base.KubeClient.ListENI(ctx, &kube.ListOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取ENI列表失败: %v，将在 %v 后重试", err, checkInterval)
			time.Sleep(checkInterval)
			continue
		}

		// 统计节点上的ENI数量
		nodeENIs = []types.ENI{}
		for _, eni := range eniList.Items {
			if eni.Spec.NodeName == nodeName {
				nodeENIs = append(nodeENIs, eni)
			}
		}

		// 打印每个ENI的详细信息
		logger.Infof(ctx, "当前节点ENI状态 (%s):", nodeName)
		logger.Infof(ctx, "  ENI数量: %d, 预期至少: %d", len(nodeENIs), c.configPreAllocateENI)
		for i, eni := range nodeENIs {
			logger.Infof(ctx, "  ENI-%d: ID=%s, 状态=%s, IP数量=%d",
				i+1, eni.Name, eni.Status.CCEStatus, len(eni.Spec.PrivateIPSet))
			// 打印每个IP地址
			if len(eni.Spec.PrivateIPSet) > 0 {
				ipAddrs := make([]string, 0, len(eni.Spec.PrivateIPSet))
				for _, ip := range eni.Spec.PrivateIPSet {
					ipAddrs = append(ipAddrs, ip.PrivateIPAddress)
				}
				logger.Infof(ctx, "    IP地址: %s", strings.Join(ipAddrs, ", "))
			}
		}

		// 检查是否达到预期的ENI数量
		readyENIs := 0
		initialENINames = []string{} // 清空，以便重新收集
		for _, eni := range nodeENIs {
			if eni.Status.CCEStatus == "ReadyOnNode" {
				readyENIs++
				if len(initialENINames) < c.configPreAllocateENI {
					initialENINames = append(initialENINames, eni.Name)
				}
			}
		}

		if readyENIs >= c.configPreAllocateENI {
			logger.Infof(ctx, "节点 %s 的ENI数量已达到预期 (%d >= %d)，等待耗时: %v",
				nodeName, readyENIs, c.configPreAllocateENI, time.Since(startTime).Round(time.Second))

			// 保存初始的3个ENI名称到结构体
			c.initialENINames = initialENINames
			logger.Infof(ctx, "记录初始预分配的ENI名称: %v", c.initialENINames)
			break
		}

		waitingTime := time.Since(startTime).Round(time.Second)
		remainingTime := maxWaitTime - waitingTime
		logger.Infof(ctx, "节点 %s 的ENI数量未达到预期 (%d < %d)，已等待: %v，剩余等待时间: %v",
			nodeName, readyENIs, c.configPreAllocateENI, waitingTime, remainingTime)

		// 对于小于等于2个的ENI，每次等待时间为5秒
		// 对于大于2个的ENI，每次等待时间为10秒
		// 这样可以更细致地监控前期的ENI创建
		if readyENIs <= 2 {
			time.Sleep(5 * time.Second)
		} else {
			time.Sleep(checkInterval)
		}
	}

	if len(initialENINames) < c.configPreAllocateENI {
		logger.Warnf(ctx, "等待节点ENI创建达到预期数量超时，当前数量: %d, 预期至少: %d",
			len(initialENINames), c.configPreAllocateENI)
	}

	// 验证NRS中的配置参数是否符合预期
	if nrs.Spec.Eni.PreAllocateENI != c.configPreAllocateENI {
		return fmt.Errorf("节点NRS中的PreAllocateENI参数 (%d) 与预期 (%d) 不符",
			nrs.Spec.Eni.PreAllocateENI, c.configPreAllocateENI)
	}

	if nrs.Spec.Ipam.PreAllocate != c.configPreAllocateIP {
		return fmt.Errorf("节点NRS中的PreAllocate参数 (%d) 与预期 (%d) 不符",
			nrs.Spec.Ipam.PreAllocate, c.configPreAllocateIP)
	}

	if nrs.Spec.Ipam.MinAllocate != c.configMinAllocateIP {
		return fmt.Errorf("节点NRS中的MinAllocate参数 (%d) 与预期 (%d) 不符",
			nrs.Spec.Ipam.MinAllocate, c.configMinAllocateIP)
	}

	// 2. 检查IP池大小
	ipPoolSize := len(nrs.Spec.Ipam.Pool)
	logger.Infof(ctx, "节点IP池大小: %d", ipPoolSize)

	// 验证IP池大小是否符合预期
	expectedIPPoolSize := c.configMinAllocateIP // 使用MinAllocate作为预期值
	if ipPoolSize < expectedIPPoolSize {
		return fmt.Errorf("节点IP池大小 (%d) 小于预期 (%d)", ipPoolSize, expectedIPPoolSize)
	}

	// 记录节点ENI信息
	nodeENIInfo := make(map[string]int)
	for _, eni := range nodeENIs {
		nodeENIInfo[eni.Name] = len(eni.Spec.PrivateIPSet)
		logger.Infof(ctx, "ENI %s 信息: 状态=%s, IP数量=%d",
			eni.Name, eni.Status.CCEStatus, len(eni.Spec.PrivateIPSet))
	}
	c.nodeEniInfo[nodeName] = nodeENIInfo

	// 如果最终ENI数量仍然小于预期，但测试可以继续，只发出警告
	if len(initialENINames) < c.configPreAllocateENI {
		logger.Warnf(ctx, "节点ENI数量 (%d) 小于预期 (%d), 可能是因为ENI创建过程仍在进行",
			len(initialENINames), c.configPreAllocateENI)
	} else {
		logger.Infof(ctx, "节点ENI数量符合预期: %d >= %d", len(initialENINames), c.configPreAllocateENI)
	}

	return nil
}

// calculateNodeMaxENI 计算节点可分配的最大ENI数量
func (c *checkENIScaleResources) calculateNodeMaxENI(ctx context.Context, nodeName string) error {
	// 获取节点详情
	node, err := c.base.K8SClient.CoreV1().Nodes().Get(ctx, nodeName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取节点详情失败: %v", err)
	}

	// 获取节点CPU数量
	cpuQty := node.Status.Capacity.Cpu()
	cpuNum := int(cpuQty.Value())

	// 根据CPU数量计算最大ENI数
	var maxENINum int
	if cpuNum <= 1 {
		maxENINum = 2
	} else if cpuNum <= 2 {
		maxENINum = 3
	} else if cpuNum <= 4 {
		maxENINum = 4
	} else if cpuNum <= 8 {
		maxENINum = 8
	} else if cpuNum <= 16 {
		maxENINum = 8
	} else if cpuNum <= 32 {
		maxENINum = 8
	} else {
		maxENINum = 8
	}

	c.initialMaxENIPerNode = maxENINum
	logger.Infof(ctx, "节点 %s CPU数量: %d, 计算得到的最大ENI数: %d", nodeName, cpuNum, maxENINum)

	// 获取节点NRS资源
	nrs, err := c.base.KubeClient.GetNrs(ctx, nodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取节点NRS资源失败: %v", err)
	}

	// 检查是否有设置MaxAllocateENI
	if nrs.Spec.Eni.MaxAllocateENI > 0 {
		logger.Infof(ctx, "节点NRS中设置的MaxAllocateENI: %d", nrs.Spec.Eni.MaxAllocateENI)
		// 如果设置了MaxAllocateENI，则以该值为准
		c.initialMaxENIPerNode = nrs.Spec.Eni.MaxAllocateENI
	}

	// 验证节点NRS中的MaxIPsPerENI是否被BCECustomerMaxIP覆盖
	if nrs.Spec.Eni.MaxIPsPerENI != c.configCustomerMaxIP {
		logger.Warnf(ctx, "节点NRS中的MaxIPsPerENI (%d) 与配置的BCECustomerMaxIP (%d) 不符",
			nrs.Spec.Eni.MaxIPsPerENI, c.configCustomerMaxIP)
	} else {
		logger.Infof(ctx, "节点NRS中的MaxIPsPerENI已被BCECustomerMaxIP覆盖为: %d", c.configCustomerMaxIP)
	}

	c.initialMaxIPPerENI = nrs.Spec.Eni.MaxIPsPerENI

	// 如果设置了max-allocate-eni参数，则应该使用该值与计算值取较小者
	if c.configMaxAllocateENI > 0 && c.configMaxAllocateENI < c.initialMaxENIPerNode {
		c.initialMaxENIPerNode = c.configMaxAllocateENI
		logger.Infof(ctx, "使用配置的max-allocate-eni参数 %d 覆盖计算值 %d",
			c.configMaxAllocateENI, c.initialMaxENIPerNode)
	}

	return nil
}

// phaseDeployWorkload 阶段3：验证新节点IP分配是否符合配置
func (c *checkENIScaleResources) phaseDeployWorkload(ctx context.Context) error {
	// 确保使用正确的新扩容节点
	if c.newNodeName == "" {
		return fmt.Errorf("未找到新扩容的节点，无法部署工作负载")
	}

	logger.Infof(ctx, "确认在新扩容节点 %s 上进行测试", c.newNodeName)

	// 1. 计算预期可以调度的最大Pod数量
	// 正确计算: 节点最大ENI数 * 每个ENI的最大IP数 - 预留的IP(每个ENI预留主IP)
	// 每个ENI有1个主IP不能用于Pod
	maxENI := c.initialMaxENIPerNode
	maxIPPerENI := c.initialMaxIPPerENI
	reservedIPsPerENI := 1 // 每个ENI预留一个主IP
	expectedPodCapacity := maxENI * (maxIPPerENI - reservedIPsPerENI)

	logger.Infof(ctx, "节点 %s 的配置:", c.newNodeName)
	logger.Infof(ctx, "  最大ENI数量: %d (max-allocate-eni=%d)",
		maxENI, c.configMaxAllocateENI)
	logger.Infof(ctx, "  每个ENI最大IP数: %d (bce-customer-max-ip=%d)",
		maxIPPerENI, c.configCustomerMaxIP)
	logger.Infof(ctx, "  每个ENI保留IP数: %d", reservedIPsPerENI)
	logger.Infof(ctx, "节点 %s 预期可以调度的最大Pod数量: %d = %d(ENI) * (%d - %d)(可用IP/ENI)",
		c.newNodeName, expectedPodCapacity, maxENI, maxIPPerENI, reservedIPsPerENI)

	// 检查初始记录的ENI名称
	if len(c.initialENINames) < c.configPreAllocateENI {
		logger.Warnf(ctx, "记录的初始ENI名称数量 (%d) 小于预期的预分配ENI数量 (%d), 可能影响后续验证",
			len(c.initialENINames), c.configPreAllocateENI)
	} else {
		logger.Infof(ctx, "已记录的初始预分配ENI名称: %v", c.initialENINames)
	}

	// 2. 设置要创建的Pod数量，超过预期容量以验证限制是否生效
	podReplicas := int32(expectedPodCapacity + 2) // 创建比预期容量多2个的Pod
	logger.Infof(ctx, "创建 %d 个Pod到节点 %s (预期最多 %d 个Pod能成功就绪)",
		podReplicas, c.newNodeName, expectedPodCapacity)

	if err := c.createTestDeployment(ctx, c.newNodeName, podReplicas); err != nil {
		return fmt.Errorf("创建测试Deployment失败: %v", err)
	}

	// 3. 等待Pod就绪，设置合理的超时时间
	waitTimeout := 10 * time.Minute
	checkInterval := 20 * time.Second

	logger.Infof(ctx, "等待Pod调度和IP分配，最长等待时间: %v，检查间隔: %v",
		waitTimeout, checkInterval)

	startTime := time.Now()
	var ready, total int
	var err error

	// 等待直到有预期数量的Pod就绪或超时
	for time.Since(startTime) < waitTimeout {
		ready, total, err = c.checkPodReadyStatus(ctx)
		if err != nil {
			logger.Warnf(ctx, "检查Pod就绪状态失败: %v，将重试", err)
			time.Sleep(checkInterval)
			continue
		}

		logger.Infof(ctx, "当前Pod就绪状态: %d/%d, 预期最大就绪数: %d, 等待时间: %v",
			ready, total, expectedPodCapacity, time.Since(startTime).Round(time.Second))

		// 如果就绪数达到或超过预期最大值，则继续测试
		if ready >= expectedPodCapacity {
			logger.Infof(ctx, "Pod就绪数量已达到或超过预期最大值 %d，继续测试", expectedPodCapacity)
			break
		}

		// 如果等待超过2/3的超时时间，也退出等待
		if time.Since(startTime) > waitTimeout*2/3 {
			logger.Infof(ctx, "等待时间已超过总超时时间的2/3，继续测试")
			break
		}

		time.Sleep(checkInterval)
	}

	logger.Infof(ctx, "Pod就绪状态检查完成: %d/%d，等待时间: %v",
		ready, total, time.Since(startTime).Round(time.Second))

	// 4. 检查当前节点上的ENI数量，并验证初始预分配的ENI的IP分配情况
	// 获取当前ENI和IP分配情况
	eniList, err := c.base.KubeClient.ListENI(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取ENI列表失败: %v", err)
	}

	// 统计节点上实际Ready的ENI数量
	var nodeReadyENIs []types.ENI
	initialENIs := make(map[string]types.ENI)

	for _, eni := range eniList.Items {
		if eni.Spec.NodeName == c.newNodeName && eni.Status.CCEStatus == "ReadyOnNode" {
			nodeReadyENIs = append(nodeReadyENIs, eni)

			// 检查是否是初始预分配的ENI
			for _, eniName := range c.initialENINames {
				if eni.Name == eniName {
					initialENIs[eniName] = eni
					break
				}
			}
		}
	}

	actualENICount := len(nodeReadyENIs)
	logger.Infof(ctx, "节点 %s 当前Ready状态的ENI数量: %d, 预期最大ENI数量: %d",
		c.newNodeName, actualENICount, c.initialMaxENIPerNode)

	// 验证ENI数量是否超过最大值 - 由于版本变更，仅打印警告而不终止测试
	if actualENICount > c.initialMaxENIPerNode {
		logger.Warnf(ctx, "警告: 节点上Ready状态的ENI数量 (%d) 超过预期最大值 (%d)，这可能表明ENI数量限制未正确生效",
			actualENICount, c.initialMaxENIPerNode)
	}

	// 5. 特别验证初始预分配的ENI是否已达到最大IP数量
	logger.Infof(ctx, "验证初始预分配的 %d 个ENI的IP分配情况", len(c.initialENINames))

	allInitialENIsFull := true
	for _, eniName := range c.initialENINames {
		eni, exists := initialENIs[eniName]
		if !exists {
			logger.Warnf(ctx, "初始ENI %s 未在当前节点上找到，可能已被删除或替换", eniName)
			allInitialENIsFull = false
			continue
		}

		ipCount := len(eni.Spec.PrivateIPSet)
		logger.Infof(ctx, "初始ENI %s: IP数量 = %d, 最大IP数量 = %d",
			eni.Name, ipCount, c.initialMaxIPPerENI)

		// 检查IP数量是否达到最大值
		if ipCount < c.initialMaxIPPerENI {
			logger.Warnf(ctx, "初始ENI %s 的IP数量 (%d) 未达到最大限制 (%d)",
				eni.Name, ipCount, c.initialMaxIPPerENI)
			allInitialENIsFull = false
		}

		// 打印每个IP的分配情况
		if len(eni.Spec.PrivateIPSet) > 0 {
			primaryCount := 0
			secondaryCount := 0
			for _, ip := range eni.Spec.PrivateIPSet {
				if ip.Primary {
					primaryCount++
				} else {
					secondaryCount++
				}
			}
			logger.Infof(ctx, "  ENI %s: 主IP数量 = %d, 辅助IP数量 = %d",
				eni.Name, primaryCount, secondaryCount)
		}
	}

	if allInitialENIsFull {
		logger.Infof(ctx, "验证通过: 所有初始预分配的ENI都已达到最大IP数量限制")
	} else {
		logger.Warnf(ctx, "验证结果: 部分初始预分配的ENI未达到最大IP数量限制，系统可能优先使用了新创建的ENI")
	}

	// 6. 验证每个ENI的IP数量是否符合预期
	logger.Infof(ctx, "验证新扩容节点上的ENI的IP数量是否符合预期")
	if err := c.verifyENIIPCount(ctx, c.newNodeName); err != nil {
		return fmt.Errorf("验证ENI上的IP数量失败: %v", err)
	}

	// 7. 综合验证容量限制是否生效
	logger.Infof(ctx, "验证Pod容量限制是否生效")

	// 统计节点上实际可用的IP数量
	totalIPs := 0
	usableIPs := 0

	for _, eni := range nodeReadyENIs {
		ipCount := len(eni.Spec.PrivateIPSet)
		totalIPs += ipCount
		// 每个ENI有一个主IP不可用于Pod
		usableIPs += (ipCount - 1)
	}

	logger.Infof(ctx, "节点 %s 上实际Ready的ENI数量: %d, 总IP数: %d, 可用于Pod的IP数: %d",
		c.newNodeName, actualENICount, totalIPs, usableIPs)

	// 重新计算实际的容量上限，因为实际情况可能与预期不同
	actualPodCapacity := usableIPs
	logger.Infof(ctx, "基于当前分配的ENI和IP，节点实际可调度的最大Pod数量: %d", actualPodCapacity)

	if ready > actualPodCapacity {
		return fmt.Errorf("就绪Pod数量 (%d) 超过实际IP容量 (%d)，容量限制未生效",
			ready, actualPodCapacity)
	}

	// 验证是否有Pod因容量限制而处于Pending状态
	if total > actualPodCapacity {
		pendingPods := total - ready
		if pendingPods == 0 {
			return fmt.Errorf("所有Pod都就绪 (%d/%d)，但预期应有 %d 个Pod因容量限制而无法就绪",
				ready, total, total-actualPodCapacity)
		}
		logger.Infof(ctx, "容量限制验证通过: %d个Pod处于Pending状态，符合预期", pendingPods)
	}

	logger.Infof(ctx, "容量限制验证通过: 就绪Pod数 (%d) <= 实际可用IP数 (%d)", ready, actualPodCapacity)
	return nil
}

// checkPodReadyStatus 检查Pod就绪状态
func (c *checkENIScaleResources) checkPodReadyStatus(ctx context.Context) (int, int, error) {
	// 获取Deployment的Pod
	podList, err := c.base.K8SClient.CoreV1().Pods(testDeploymentNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "app=nginx-eni-scale",
	})
	if err != nil {
		return 0, 0, fmt.Errorf("获取Pod列表失败: %v", err)
	}

	total := len(podList.Items)
	ready := 0
	pending := 0
	running := 0
	other := 0

	// 统计各状态Pod数量
	for _, pod := range podList.Items {
		switch pod.Status.Phase {
		case corev1.PodRunning:
			running++
			// 检查Pod是否就绪
			isReady := true
			for _, condition := range pod.Status.Conditions {
				if condition.Type == corev1.PodReady && condition.Status != corev1.ConditionTrue {
					isReady = false
					break
				}
			}
			if isReady {
				ready++
			}
		case corev1.PodPending:
			pending++
		default:
			other++
		}
	}

	logger.Infof(ctx, "Pod状态统计: 总数=%d, 就绪=%d, 运行中=%d, 等待中=%d, 其他=%d",
		total, ready, running, pending, other)

	return ready, total, nil
}

// verifyENIIPCount 验证每个ENI的IP数量
func (c *checkENIScaleResources) verifyENIIPCount(ctx context.Context, nodeName string) error {
	// 确保我们检查的是扩容后的新节点
	if nodeName != c.newNodeName {
		logger.Warnf(ctx, "当前检查的节点 %s 不是新扩容的节点 %s", nodeName, c.newNodeName)
		nodeName = c.newNodeName
	}
	logger.Infof(ctx, "开始验证新扩容节点 %s 上的ENI的IP数量", nodeName)

	// 获取ENI列表
	eniList, err := c.base.KubeClient.ListENI(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取ENI列表失败: %v", err)
	}

	// 过滤出目标节点上的ENI
	var nodeENIs []types.ENI
	for _, eni := range eniList.Items {
		if eni.Spec.NodeName == nodeName {
			nodeENIs = append(nodeENIs, eni)
		}
	}

	if len(nodeENIs) == 0 {
		return fmt.Errorf("节点 %s 上未找到ENI", nodeName)
	}

	// 验证每个ENI的IP数量
	for _, eni := range nodeENIs {
		ipCount := len(eni.Spec.PrivateIPSet)
		logger.Infof(ctx, "ENI %s: IP数量 = %d, 最大IP数量 = %d",
			eni.Name, ipCount, c.initialMaxIPPerENI)

		// 打印每个IP的分配情况
		allocatedCount := 0
		for _, ip := range eni.Spec.PrivateIPSet {
			// PrivateIP没有Status字段，打印IP地址信息
			if ip.Primary {
				logger.Infof(ctx, "  主IP: %s", ip.PrivateIPAddress)
			} else {
				allocatedCount++
			}
		}
		logger.Infof(ctx, "  辅助IP数量: %d/%d", allocatedCount, ipCount)

		// 验证IP数量不超过最大限制
		if ipCount > c.initialMaxIPPerENI {
			return fmt.Errorf("ENI %s 的IP数量 (%d) 超过了最大限制 (%d)",
				eni.Name, ipCount, c.initialMaxIPPerENI)
		}
	}

	logger.Infof(ctx, "所有ENI的IP数量验证通过")
	return nil
}

// createTestDeployment 创建测试Deployment
func (c *checkENIScaleResources) createTestDeployment(ctx context.Context, nodeName string, replicas int32) error {
	// 1. 先检查是否已存在，如果存在则删除
	logger.Infof(ctx, "检查是否已存在测试Deployment")
	_, err := c.base.K8SClient.AppsV1().Deployments(testDeploymentNamespace).Get(ctx, testDeploymentName, metav1.GetOptions{})
	if err == nil {
		// 如果存在，先删除
		logger.Infof(ctx, "删除已存在的测试Deployment")
		if err := c.cleanTestDeployment(ctx); err != nil {
			return fmt.Errorf("删除已存在的测试Deployment失败: %v", err)
		}

		// 等待删除完成
		err = wait.PollImmediate(5*time.Second, 1*time.Minute, func() (bool, error) {
			_, err := c.base.K8SClient.AppsV1().Deployments(testDeploymentNamespace).Get(ctx, testDeploymentName, metav1.GetOptions{})
			return err != nil, nil
		})
		if err != nil {
			return fmt.Errorf("等待删除Deployment超时: %v", err)
		}
	}

	// 2. 创建新的Deployment
	logger.Infof(ctx, "创建测试Deployment，副本数: %d", replicas)

	// 创建Deployment对象
	deployment := &v1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      testDeploymentName,
			Namespace: testDeploymentNamespace,
		},
		Spec: v1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": "nginx-eni-scale",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": "nginx-eni-scale",
					},
				},
				Spec: corev1.PodSpec{
					NodeSelector: map[string]string{
						"kubernetes.io/hostname": nodeName,
					},
					Containers: []corev1.Container{
						{
							Name:            "nginx",
							Image:           "registry.baidubce.com/cce/nginx-alpine-go:latest",
							ImagePullPolicy: corev1.PullIfNotPresent,
							Command:         []string{"sh", "-c", "sleep 3600"},
							Ports: []corev1.ContainerPort{
								{
									ContainerPort: 80,
								},
							},
							Resources: corev1.ResourceRequirements{
								Requests: corev1.ResourceList{
									corev1.ResourceCPU:    resourceapi.MustParse("10m"),
									corev1.ResourceMemory: resourceapi.MustParse("20Mi"),
								},
								Limits: corev1.ResourceList{
									corev1.ResourceCPU:    resourceapi.MustParse("20m"),
									corev1.ResourceMemory: resourceapi.MustParse("40Mi"),
								},
							},
							ReadinessProbe: &corev1.Probe{
								Handler: corev1.Handler{
									Exec: &corev1.ExecAction{
										Command: []string{"sh", "-c", "echo ready"},
									},
								},
								InitialDelaySeconds: 2,
								TimeoutSeconds:      1,
								PeriodSeconds:       5,
							},
						},
					},
				},
			},
		},
	}

	// 创建Deployment
	_, err = c.base.K8SClient.AppsV1().Deployments(testDeploymentNamespace).Create(ctx, deployment, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建Deployment失败: %v", err)
	}

	logger.Infof(ctx, "测试Deployment创建成功")
	return nil
}

// cleanTestDeployment 清理测试Deployment
func (c *checkENIScaleResources) cleanTestDeployment(ctx context.Context) error {
	_, err := c.base.K8SClient.AppsV1().Deployments(testDeploymentNamespace).Get(ctx, testDeploymentName, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return nil
		}
		return err
	}

	logger.Infof(ctx, "清理Deployment %s/%s", testDeploymentNamespace, testDeploymentName)
	err = c.base.K8SClient.AppsV1().Deployments(testDeploymentNamespace).Delete(ctx, testDeploymentName, metav1.DeleteOptions{})
	if err != nil && !k8serrors.IsNotFound(err) {
		return err
	}

	// 等待所有相关Pod被完全删除
	logger.Infof(ctx, "等待所有Pod完全删除...")
	err = wait.PollImmediate(5*time.Second, 3*time.Minute, func() (bool, error) {
		pods, err := c.base.K8SClient.CoreV1().Pods(testDeploymentNamespace).List(ctx, metav1.ListOptions{
			LabelSelector: "app=nginx-eni-scale",
		})
		if err != nil {
			return false, err
		}

		if len(pods.Items) > 0 {
			logger.Infof(ctx, "还有 %d 个Pod未删除，继续等待...", len(pods.Items))
			return false, nil
		}

		logger.Infof(ctx, "所有Pod已成功删除")
		return true, nil
	})

	if err != nil {
		logger.Warnf(ctx, "等待Pod删除超时: %v", err)
		return err
	}

	return nil
}

// createInstanceGroup 添加createInstanceGroup方法
func (c *checkENIScaleResources) createInstanceGroup(ctx context.Context) error {
	// 先检查集群中是否已有节点组
	resp, err := c.base.CCEClient.ListInstanceGroups(ctx, c.base.ClusterID, nil, nil)
	if err != nil {
		return fmt.Errorf("获取节点组列表失败: %v", err)
	}

	// 如果已有节点组，则无需创建
	if len(resp.Page.List) > 0 {
		for _, ig := range resp.Page.List {
			if ig.Status.ReadyReplicas > 0 {
				logger.Infof(ctx, "集群中已有可用节点组 %s (副本数: %d)，无需创建新节点组",
					ig.Spec.CCEInstanceGroupID, ig.Status.ReadyReplicas)

				// 设置节点组ID和标记为非创建
				c.instanceGroup = ig.Spec.CCEInstanceGroupID
				c.createdNodeGroup = false

				// 记录原始副本数用于清理恢复
				c.originalReplicas = ig.Spec.Replicas
				logger.Infof(ctx, "记录原始节点组副本数: %d", c.originalReplicas)

				return nil
			}
		}
	}

	// 需要创建新节点组
	logger.Infof(ctx, "未找到可用节点组，开始创建测试用节点组...")

	// 获取集群中已有节点的信息，用于复用配置
	instances, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
		ClusterRole: types.ClusterRoleNode,
	}, nil)
	if err != nil {
		return fmt.Errorf("获取集群节点信息失败: %v", err)
	}
	if len(instances.InstancePage.InstanceList) == 0 {
		return fmt.Errorf("集群中没有可用的节点，无法获取配置信息")
	}

	// 使用第一个节点的配置信息
	instance := instances.InstancePage.InstanceList[0]

	// 获取镜像ID
	imageID := instance.Spec.ImageID
	if imageID == "" {
		return fmt.Errorf("获取到的镜像ID为空，无法创建节点组")
	}
	logger.Infof(ctx, "将使用已有节点的镜像ID: %s", imageID)

	// 获取集群额外信息，找到可用的子网
	clusterExtraInfo, err := c.base.CCEHostClient.GetClusterExtraInfo(ctx, c.base.ClusterID, nil)
	if err != nil {
		return fmt.Errorf("获取集群额外信息失败: %v", err)
	}

	var nodeSubnetID string
	for _, subnet := range clusterExtraInfo.Subnets {
		if subnet.SubnetCIDR != "" {
			nodeSubnetID = subnet.SubnetID
			break
		}
	}

	if nodeSubnetID == "" {
		return fmt.Errorf("未找到可用的子网")
	}

	logger.Infof(ctx, "将使用子网 %s 创建节点组", nodeSubnetID)

	// 查询集群信息
	cluster, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		return fmt.Errorf("获取集群信息失败: %v", err)
	}

	// 从集群信息获取VPCID
	vpcID := cluster.Cluster.Spec.VPCID
	logger.Infof(ctx, "将使用VPC %s 创建节点组", vpcID)

	// 创建节点组
	igName := fmt.Sprintf("test-eni-scale-%d", time.Now().Unix())
	createReq := &ccev2.CreateInstanceGroupRequest{
		InstanceGroupSpec: types.InstanceGroupSpec{
			InstanceGroupName: igName,
			Replicas:          0, // 创建0个节点的节点组，避免不必要的节点创建
			InstanceTemplate: types.InstanceTemplate{
				InstanceSpec: types.InstanceSpec{
					ClusterRole:  types.ClusterRoleNode,
					MachineType:  instance.Spec.MachineType,
					InstanceType: instance.Spec.InstanceType,
					VPCConfig: types.VPCConfig{
						VPCID:       vpcID,
						VPCSubnetID: nodeSubnetID,
						SecurityGroup: types.SecurityGroup{
							EnableCCERequiredSecurityGroup: true,
						},
					},
					InstanceResource: types.InstanceResource{
						CPU: instance.Spec.InstanceResource.CPU,
						MEM: instance.Spec.InstanceResource.MEM,
					},
					ImageID:       imageID,
					AdminPassword: "Test123456!",
				},
			},
		},
	}

	// 发送创建请求
	createResp, err := c.base.CCEClient.CreateInstanceGroup(ctx, c.base.ClusterID, createReq, nil)
	if err != nil {
		return fmt.Errorf("创建节点组失败: %v", err)
	}

	// 保存节点组ID
	c.instanceGroup = createResp.InstanceGroupID
	c.createdNodeGroup = true
	logger.Infof(ctx, "成功创建节点组 %s (ID: %s)", igName, c.instanceGroup)

	// 等待节点组就绪
	logger.Infof(ctx, "等待节点组中的节点就绪...")

	// 创建instanceGroup资源对象并等待就绪
	ig, err := cceresource.NewInstanceGroup(ctx, c.base, c.instanceGroup, 10*time.Second, 15*time.Minute)
	if err != nil {
		return fmt.Errorf("创建instanceGroup资源对象失败: %v", err)
	}

	if err := ig.CheckResource(ctx); err != nil {
		return fmt.Errorf("等待节点组就绪失败: %v", err)
	}

	logger.Infof(ctx, "节点组 %s 已就绪", c.instanceGroup)

	// 将创建的节点组添加到资源列表
	c.resources = append(c.resources, cases.Resource{
		CaseName: CheckENIScaleResourcesCaseName,
		Type:     cases.ResourceTypeCCEInstanceGroup,
		ID:       c.instanceGroup,
	})

	return nil
}
