/*
Primary IP 测试用例

功能描述：
测试主网卡辅助IP的功能，使得校验使用主网卡辅助IP的节点使用是符合预期的。

测试流程：
1. 使用 registry.baidubce.com/cce/nginx-alpine-go:latest 镜像创建用于测试的pod
2. 确认当前集群中是否有能够测试主网卡辅助IP的节点，通过查看集群中的eni的信息，mode是否为PrimaryWithSecondaryIP
3. 在使用主网卡辅助IP的节点上创建pod分配IP，校验能够申请到的最大的IP不超过节点对应的nrs中max-allocate配置的最大pod数量
4. 查看cce-network-v2-config，如果burstable-mehrfach-eni参数不为0，则当前的容器网络模式是burst模式
5. 修改网络配置为burst模式，并重启对应的operator组件，校验容器缩容后节点仍然会保留辅助IP资源
6. 修改网络配置关闭burst模式并开启release-excess-ips，校验辅助IP资源能够释放

预期结果：
每一步请确认对应的执行结果，如果能够顺利执行并且按照每一步提供的结果，则是符合预期。

注意事项：
此测试会创建测试资源，测试完成后会自动清理所有创建的资源。
*/

package network

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/apimachinery/pkg/util/wait"
)

const (
	// PrimaryIPCaseName - case 名字
	PrimaryIPCaseName cases.CaseName = "PrimaryIP"
)

// primaryIP 是用于测试主网卡辅助IP功能的测试用例
type primaryIP struct {
	base                     *cases.BaseClient
	testNamespace            string
	testDeploymentName       string
	primaryIPNodeName        string
	originalNRSMaxAllocate   int
	originalConfigMapData    string
	originalBurstMode        int
	originalReleaseExcessIPs bool
	testPodsCreated          []string
}

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), PrimaryIPCaseName, NewPrimaryIP)
}

// NewPrimaryIP 创建一个新的主网卡辅助IP测试用例
func NewPrimaryIP(ctx context.Context) cases.Interface {
	return &primaryIP{
		testNamespace:      "default",
		testDeploymentName: "test-primary-ip-deployment",
		testPodsCreated:    make([]string, 0),
	}
}

// Name 返回测试用例名称
func (c *primaryIP) Name() cases.CaseName {
	return PrimaryIPCaseName
}

// Desc 返回测试用例描述
func (c *primaryIP) Desc() string {
	return "测试主网卡辅助IP的功能，使得校验使用主网卡辅助IP的节点使用是符合预期的"
}

// Init 初始化测试用例
func (c *primaryIP) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	logger.Infof(ctx, "初始化主网卡辅助IP测试用例")
	if base == nil {
		return fmt.Errorf("base client is nil")
	}
	c.base = base
	return nil
}

// Check 执行测试
func (c *primaryIP) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)

	logger.Infof(ctx, "开始执行主网卡辅助IP功能测试")

	// 1. 检查集群中是否有支持主网卡辅助IP的节点
	if err := c.checkPrimaryIPNodes(ctx); err != nil {
		return resources, fmt.Errorf("检查主网卡辅助IP节点失败: %v", err)
	}

	// 2. 修改NRS配置，设置max-allocate为50
	if err := c.modifyNRSMaxAllocate(ctx, 50); err != nil {
		return resources, fmt.Errorf("修改NRS max-allocate配置失败: %v", err)
	}

	// 3. 检查当前网络配置的burst模式状态
	if err := c.checkBurstModeStatus(ctx); err != nil {
		return resources, fmt.Errorf("检查burst模式状态失败: %v", err)
	}

	// 4. 创建测试Pod验证IP分配
	if err := c.testIPAllocation(ctx); err != nil {
		return resources, fmt.Errorf("测试IP分配失败: %v", err)
	}

	// 5. 测试burst模式下的IP保留功能
	if err := c.testBurstModeIPRetention(ctx); err != nil {
		return resources, fmt.Errorf("测试burst模式IP保留功能失败: %v", err)
	}

	// 6. 测试IP释放功能
	if err := c.testIPRelease(ctx); err != nil {
		return resources, fmt.Errorf("测试IP释放功能失败: %v", err)
	}

	// 7. 测试Pod删除后的CEP资源回收
	if err := c.testCEPResourceRecycling(ctx); err != nil {
		return resources, fmt.Errorf("测试CEP资源回收失败: %v", err)
	}

	// 8. 测试NRS IP信息更新
	if err := c.testNRSIPInfoUpdate(ctx); err != nil {
		return resources, fmt.Errorf("测试NRS IP信息更新失败: %v", err)
	}

	// 9. 测试辅助IP资源释放水位控制
	if err := c.testIPReleaseWatermark(ctx); err != nil {
		return resources, fmt.Errorf("测试IP释放水位控制失败: %v", err)
	}

	logger.Infof(ctx, "主网卡辅助IP功能测试完成")
	return resources, nil
}

// checkPrimaryIPNodes 检查集群中是否有支持主网卡辅助IP的节点
func (c *primaryIP) checkPrimaryIPNodes(ctx context.Context) error {
	logger.Infof(ctx, "检查集群中是否有支持主网卡辅助IP的节点")

	// 获取所有ENI资源
	eniList, err := c.base.KubeClient.ListENI(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取ENI列表失败: %v", err)
	}

	logger.Infof(ctx, "找到 %d 个ENI资源", len(eniList.Items))

	primaryIPNodes := make([]string, 0)
	for _, eni := range eniList.Items {
		logger.Infof(ctx, "检查ENI: %s, 节点: %s, UseMode: %s", eni.Name, eni.Spec.NodeName, eni.Spec.UseMode)

		// 检查UseMode是否为PrimaryWithSecondaryIP
		if eni.Spec.UseMode == "PrimaryWithSecondaryIP" {
			primaryIPNodes = append(primaryIPNodes, eni.Spec.NodeName)
			logger.Infof(ctx, "发现支持主网卡辅助IP的节点: %s", eni.Spec.NodeName)
		}
	}

	if len(primaryIPNodes) == 0 {
		logger.Warnf(ctx, "集群中没有找到支持主网卡辅助IP的节点（UseMode=PrimaryWithSecondaryIP），跳过主网卡辅助IP测试")
		logger.Infof(ctx, "当前集群中的ENI都是Secondary模式，无法进行主网卡辅助IP功能测试")
		logger.Infof(ctx, "如需测试主网卡辅助IP功能，请使用包含PrimaryWithSecondaryIP模式ENI的集群")
		return fmt.Errorf("集群中没有找到支持主网卡辅助IP的节点，跳过测试")
	}

	// 选择第一个支持主网卡辅助IP的节点进行测试
	c.primaryIPNodeName = primaryIPNodes[0]
	logger.Infof(ctx, "选择节点 %s 进行主网卡辅助IP测试", c.primaryIPNodeName)

	return nil
}

// modifyNRSMaxAllocate 修改NRS的max-allocate配置
func (c *primaryIP) modifyNRSMaxAllocate(ctx context.Context, maxAllocate int) error {
	logger.Infof(ctx, "修改节点 %s 的NRS max-allocate配置为 %d", c.primaryIPNodeName, maxAllocate)

	// 获取节点对应的NRS
	nrs, err := c.base.KubeClient.GetNrs(ctx, c.primaryIPNodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取节点 %s 的NRS失败: %v", c.primaryIPNodeName, err)
	}

	// 保存原始的max-allocate值
	c.originalNRSMaxAllocate = nrs.Spec.Ipam.MaxAllocate
	logger.Infof(ctx, "节点 %s 原始的max-allocate值: %d", c.primaryIPNodeName, c.originalNRSMaxAllocate)

	// 修改max-allocate值
	nrs.Spec.Ipam.MaxAllocate = maxAllocate

	// 更新NRS
	err = c.updateNRS(ctx, nrs)
	if err != nil {
		return fmt.Errorf("更新节点 %s 的NRS失败: %v", c.primaryIPNodeName, err)
	}

	logger.Infof(ctx, "成功修改节点 %s 的NRS max-allocate配置为 %d", c.primaryIPNodeName, maxAllocate)
	return nil
}

// checkBurstModeStatus 检查当前网络配置的burst模式状态
func (c *primaryIP) checkBurstModeStatus(ctx context.Context) error {
	logger.Infof(ctx, "检查当前网络配置的burst模式状态")

	// 获取cce-network-v2-config ConfigMap
	configMap, err := c.base.K8SClient.CoreV1().ConfigMaps("kube-system").Get(ctx, "cce-network-v2-config", metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取cce-network-v2-config ConfigMap失败: %v", err)
	}

	// 保存原始配置
	ccedConfig, ok := configMap.Data["cced"]
	if !ok {
		return fmt.Errorf("ConfigMap中不存在cced配置")
	}
	c.originalConfigMapData = ccedConfig

	// 解析burstable-mehrfach-eni参数
	burstMode := 0
	lines := strings.Split(ccedConfig, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "burstable-mehrfach-eni:") {
			parts := strings.SplitN(line, ":", 2)
			if len(parts) == 2 {
				value := strings.TrimSpace(parts[1])
				if val, err := strconv.Atoi(value); err == nil {
					burstMode = val
				}
			}
			break
		}
	}

	c.originalBurstMode = burstMode
	logger.Infof(ctx, "当前burstable-mehrfach-eni配置值: %d", burstMode)

	if burstMode != 0 {
		logger.Infof(ctx, "当前容器网络模式是burst模式")
	} else {
		logger.Infof(ctx, "当前容器网络模式不是burst模式")
	}

	return nil
}

// testIPAllocation 测试IP分配功能
func (c *primaryIP) testIPAllocation(ctx context.Context) error {
	logger.Infof(ctx, "测试IP分配功能")

	// 创建测试Deployment，指定节点选择器
	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.testDeploymentName,
			Namespace: c.testNamespace,
			Labels: map[string]string{
				"app":  "test-primary-ip",
				"test": "primary-ip",
			},
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: primaryIPInt32Ptr(10), // 创建10个Pod进行测试
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": "test-primary-ip",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": "test-primary-ip",
					},
				},
				Spec: corev1.PodSpec{
					NodeSelector: map[string]string{
						"kubernetes.io/hostname": c.primaryIPNodeName,
					},
					RestartPolicy: corev1.RestartPolicyAlways,
					Containers: []corev1.Container{
						{
							Name:            "nginx",
							Image:           "registry.baidubce.com/cce/nginx-alpine-go:latest",
							ImagePullPolicy: corev1.PullAlways,
							Ports: []corev1.ContainerPort{
								{
									ContainerPort: 80,
								},
							},
							LivenessProbe: &corev1.Probe{
								Handler: corev1.Handler{
									HTTPGet: &corev1.HTTPGetAction{
										Path: "/",
										Port: intstr.FromInt(80),
									},
								},
								InitialDelaySeconds: 20,
								TimeoutSeconds:      5,
								PeriodSeconds:       5,
							},
							ReadinessProbe: &corev1.Probe{
								Handler: corev1.Handler{
									HTTPGet: &corev1.HTTPGetAction{
										Path: "/",
										Port: intstr.FromInt(80),
									},
								},
								InitialDelaySeconds: 5,
								TimeoutSeconds:      1,
								PeriodSeconds:       5,
							},
						},
					},
				},
			},
		},
	}

	// 创建Deployment
	_, err := c.base.K8SClient.AppsV1().Deployments(c.testNamespace).Create(ctx, deployment, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建测试Deployment失败: %v", err)
	}

	logger.Infof(ctx, "成功创建测试Deployment: %s", c.testDeploymentName)

	// 等待Pod创建并运行
	err = wait.PollImmediate(10*time.Second, 5*time.Minute, func() (bool, error) {
		deployment, err := c.base.K8SClient.AppsV1().Deployments(c.testNamespace).Get(ctx, c.testDeploymentName, metav1.GetOptions{})
		if err != nil {
			return false, err
		}

		logger.Infof(ctx, "Deployment状态: Ready=%d/%d, Available=%d",
			deployment.Status.ReadyReplicas,
			*deployment.Spec.Replicas,
			deployment.Status.AvailableReplicas)

		return deployment.Status.ReadyReplicas >= 5, nil // 至少5个Pod就绪即可继续
	})

	if err != nil {
		return fmt.Errorf("等待测试Pod就绪超时: %v", err)
	}

	// 检查Pod的IP分配情况
	pods, err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "app=test-primary-ip",
	})
	if err != nil {
		return fmt.Errorf("获取测试Pod列表失败: %v", err)
	}

	runningPods := 0
	for _, pod := range pods.Items {
		if pod.Status.Phase == corev1.PodRunning && pod.Status.PodIP != "" {
			runningPods++
			c.testPodsCreated = append(c.testPodsCreated, pod.Name)
			logger.Infof(ctx, "Pod %s 运行在节点 %s，IP: %s", pod.Name, pod.Spec.NodeName, pod.Status.PodIP)
		}
	}

	logger.Infof(ctx, "成功创建并运行了 %d 个测试Pod", runningPods)

	// 验证IP分配数量不超过max-allocate配置
	if runningPods > 50 {
		return fmt.Errorf("运行的Pod数量 %d 超过了max-allocate配置 50", runningPods)
	}

	logger.Infof(ctx, "IP分配测试通过，Pod数量 %d 未超过max-allocate配置 50", runningPods)
	return nil
}

// testBurstModeIPRetention 测试burst模式下的IP保留功能
func (c *primaryIP) testBurstModeIPRetention(ctx context.Context) error {
	logger.Infof(ctx, "测试burst模式下的IP保留功能")

	// 如果当前不是burst模式，则开启burst模式
	if c.originalBurstMode == 0 {
		logger.Infof(ctx, "当前不是burst模式，开启burst模式")
		if err := c.enableBurstMode(ctx); err != nil {
			return fmt.Errorf("开启burst模式失败: %v", err)
		}
	}

	// 获取缩容前的NRS状态
	nrsBefore, err := c.base.KubeClient.GetNrs(ctx, c.primaryIPNodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取缩容前NRS状态失败: %v", err)
	}

	allocatedIPsBefore := len(nrsBefore.Status.Ipam.Used)
	logger.Infof(ctx, "缩容前节点 %s 已分配IP数量: %d", c.primaryIPNodeName, allocatedIPsBefore)

	// 缩容部分Pod
	logger.Infof(ctx, "缩容测试Deployment到5个副本")
	deployment, err := c.base.K8SClient.AppsV1().Deployments(c.testNamespace).Get(ctx, c.testDeploymentName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取Deployment失败: %v", err)
	}

	deployment.Spec.Replicas = primaryIPInt32Ptr(5)
	_, err = c.base.K8SClient.AppsV1().Deployments(c.testNamespace).Update(ctx, deployment, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新Deployment失败: %v", err)
	}

	// 等待缩容完成
	time.Sleep(30 * time.Second)

	// 检查缩容后的NRS状态
	nrsAfter, err := c.base.KubeClient.GetNrs(ctx, c.primaryIPNodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取缩容后NRS状态失败: %v", err)
	}

	allocatedIPsAfter := len(nrsAfter.Status.Ipam.Used)
	logger.Infof(ctx, "缩容后节点 %s 已分配IP数量: %d", c.primaryIPNodeName, allocatedIPsAfter)

	// 在burst模式下，缩容后应该仍然保留一些辅助IP资源
	if allocatedIPsAfter < allocatedIPsBefore-2 {
		logger.Warnf(ctx, "burst模式下IP释放过多，可能存在问题")
	} else {
		logger.Infof(ctx, "burst模式下IP保留功能正常，缩容后仍保留了部分IP资源")
	}

	return nil
}

// testIPRelease 测试IP释放功能
func (c *primaryIP) testIPRelease(ctx context.Context) error {
	logger.Infof(ctx, "测试IP释放功能")

	// 修改网络配置，关闭burst模式并开启IP释放
	logger.Infof(ctx, "修改网络配置，关闭burst模式并开启IP释放")
	if err := c.enableIPRelease(ctx); err != nil {
		return fmt.Errorf("开启IP释放功能失败: %v", err)
	}

	// 获取修改配置前的NRS状态
	nrsBefore, err := c.base.KubeClient.GetNrs(ctx, c.primaryIPNodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取修改配置前NRS状态失败: %v", err)
	}

	allocatedIPsBefore := len(nrsBefore.Status.Ipam.Used)
	logger.Infof(ctx, "修改配置前节点 %s 已分配IP数量: %d", c.primaryIPNodeName, allocatedIPsBefore)

	// 继续缩容Pod
	logger.Infof(ctx, "继续缩容测试Deployment到2个副本")
	deployment, err := c.base.K8SClient.AppsV1().Deployments(c.testNamespace).Get(ctx, c.testDeploymentName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取Deployment失败: %v", err)
	}

	deployment.Spec.Replicas = primaryIPInt32Ptr(2)
	_, err = c.base.K8SClient.AppsV1().Deployments(c.testNamespace).Update(ctx, deployment, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新Deployment失败: %v", err)
	}

	// 等待缩容和IP释放完成
	time.Sleep(60 * time.Second)

	// 检查缩容后的NRS状态
	nrsAfter, err := c.base.KubeClient.GetNrs(ctx, c.primaryIPNodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取缩容后NRS状态失败: %v", err)
	}

	allocatedIPsAfter := len(nrsAfter.Status.Ipam.Used)
	logger.Infof(ctx, "缩容后节点 %s 已分配IP数量: %d", c.primaryIPNodeName, allocatedIPsAfter)

	// 验证IP释放功能
	if allocatedIPsAfter < allocatedIPsBefore {
		logger.Infof(ctx, "IP释放功能正常，已释放 %d 个IP", allocatedIPsBefore-allocatedIPsAfter)
	} else {
		logger.Warnf(ctx, "IP释放功能可能存在问题，IP数量未减少")
	}

	return nil
}

// enableBurstMode 开启burst模式
func (c *primaryIP) enableBurstMode(ctx context.Context) error {
	logger.Infof(ctx, "开启burst模式")

	configUpdates := map[string]string{
		"burstable-mehrfach-eni": "1",
	}

	if err := c.updateNetworkConfig(ctx, configUpdates); err != nil {
		return fmt.Errorf("更新网络配置失败: %v", err)
	}

	// 重启operator组件
	if err := c.restartOperator(ctx); err != nil {
		return fmt.Errorf("重启operator组件失败: %v", err)
	}

	logger.Infof(ctx, "成功开启burst模式")
	return nil
}

// enableIPRelease 开启IP释放功能
func (c *primaryIP) enableIPRelease(ctx context.Context) error {
	logger.Infof(ctx, "开启IP释放功能")

	configUpdates := map[string]string{
		"burstable-mehrfach-eni":     "0",
		"release-excess-ips":         "true",
		"ippool-max-above-watermark": "1",
		"ippool-min-allocate-ips":    "1",
		"ippool-pre-allocate":        "1",
	}

	if err := c.updateNetworkConfig(ctx, configUpdates); err != nil {
		return fmt.Errorf("更新网络配置失败: %v", err)
	}

	// 重启operator组件
	if err := c.restartOperator(ctx); err != nil {
		return fmt.Errorf("重启operator组件失败: %v", err)
	}

	logger.Infof(ctx, "成功开启IP释放功能")
	return nil
}

// updateNetworkConfig 更新网络配置
func (c *primaryIP) updateNetworkConfig(ctx context.Context, configUpdates map[string]string) error {
	logger.Infof(ctx, "更新网络配置")

	// 获取ConfigMap
	configMap, err := c.base.K8SClient.CoreV1().ConfigMaps("kube-system").Get(ctx, "cce-network-v2-config", metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取ConfigMap失败: %v", err)
	}

	ccedConfig := configMap.Data["cced"]
	lines := strings.Split(ccedConfig, "\n")

	// 修改指定的配置项
	for key, newValue := range configUpdates {
		found := false
		for i, line := range lines {
			if strings.HasPrefix(strings.TrimSpace(line), key+":") {
				parts := strings.SplitN(line, ":", 2)
				if len(parts) == 2 {
					lines[i] = parts[0] + ": " + newValue
					logger.Infof(ctx, "修改配置 %s: 原值=%s, 新值=%s", parts[0], strings.TrimSpace(parts[1]), newValue)
					found = true
				}
			}
		}

		// 如果配置项不存在，则添加
		if !found {
			lines = append(lines, key+": "+newValue)
			logger.Infof(ctx, "添加配置 %s: %s", key, newValue)
		}
	}

	// 更新ConfigMap
	configMap.Data["cced"] = strings.Join(lines, "\n")
	_, err = c.base.K8SClient.CoreV1().ConfigMaps("kube-system").Update(ctx, configMap, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新ConfigMap失败: %v", err)
	}

	logger.Infof(ctx, "网络配置更新成功")
	return nil
}

// restartOperator 重启operator组件
func (c *primaryIP) restartOperator(ctx context.Context) error {
	logger.Infof(ctx, "重启operator组件")

	// 获取operator deployment
	deployment, err := c.base.K8SClient.AppsV1().Deployments("kube-system").Get(ctx, "cce-network-operator", metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取operator deployment失败: %v", err)
	}

	// 添加annotation触发重启
	if deployment.Spec.Template.Annotations == nil {
		deployment.Spec.Template.Annotations = make(map[string]string)
	}
	deployment.Spec.Template.Annotations["kubectl.kubernetes.io/restartedAt"] = time.Now().Format(time.RFC3339)

	// 更新deployment
	_, err = c.base.K8SClient.AppsV1().Deployments("kube-system").Update(ctx, deployment, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新operator deployment失败: %v", err)
	}

	// 等待operator重启完成
	err = wait.PollImmediate(10*time.Second, 3*time.Minute, func() (bool, error) {
		deployment, err := c.base.K8SClient.AppsV1().Deployments("kube-system").Get(ctx, "cce-network-operator", metav1.GetOptions{})
		if err != nil {
			return false, err
		}

		return deployment.Status.ReadyReplicas == *deployment.Spec.Replicas, nil
	})

	if err != nil {
		return fmt.Errorf("等待operator重启完成超时: %v", err)
	}

	logger.Infof(ctx, "operator组件重启完成")
	return nil
}

// Clean 清理测试资源
func (c *primaryIP) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理主网卡辅助IP测试资源")

	// 删除测试Deployment
	if c.testDeploymentName != "" {
		err := c.base.K8SClient.AppsV1().Deployments(c.testNamespace).Delete(ctx, c.testDeploymentName, metav1.DeleteOptions{})
		if err != nil && !kerrors.IsNotFound(err) {
			logger.Warnf(ctx, "删除测试Deployment失败: %v", err)
		} else {
			logger.Infof(ctx, "测试Deployment %s 已删除", c.testDeploymentName)
		}
	}

	// 等待Pod删除完成
	if len(c.testPodsCreated) > 0 {
		logger.Infof(ctx, "等待测试Pod删除完成")
		time.Sleep(30 * time.Second)
	}

	// 恢复NRS的max-allocate配置
	if c.primaryIPNodeName != "" && c.originalNRSMaxAllocate > 0 {
		logger.Infof(ctx, "恢复节点 %s 的NRS max-allocate配置为 %d", c.primaryIPNodeName, c.originalNRSMaxAllocate)
		nrs, err := c.base.KubeClient.GetNrs(ctx, c.primaryIPNodeName, &kube.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取节点 %s 的NRS失败: %v", c.primaryIPNodeName, err)
		} else {
			nrs.Spec.Ipam.MaxAllocate = c.originalNRSMaxAllocate
			err = c.updateNRS(ctx, nrs)
			if err != nil {
				logger.Warnf(ctx, "恢复节点 %s 的NRS max-allocate配置失败: %v", c.primaryIPNodeName, err)
			} else {
				logger.Infof(ctx, "成功恢复节点 %s 的NRS max-allocate配置", c.primaryIPNodeName)
			}
		}
	}

	// 恢复原始网络配置
	if c.originalConfigMapData != "" {
		logger.Infof(ctx, "恢复原始网络配置")
		configMap, err := c.base.K8SClient.CoreV1().ConfigMaps("kube-system").Get(ctx, "cce-network-v2-config", metav1.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取ConfigMap失败: %v", err)
		} else {
			configMap.Data["cced"] = c.originalConfigMapData
			_, err = c.base.K8SClient.CoreV1().ConfigMaps("kube-system").Update(ctx, configMap, metav1.UpdateOptions{})
			if err != nil {
				logger.Warnf(ctx, "恢复原始网络配置失败: %v", err)
			} else {
				logger.Infof(ctx, "成功恢复原始网络配置")

				// 重启operator使配置生效
				if err := c.restartOperator(ctx); err != nil {
					logger.Warnf(ctx, "重启operator失败: %v", err)
				}
			}
		}
	}

	logger.Infof(ctx, "主网卡辅助IP测试资源清理完成")
	return nil
}

// testCEPResourceRecycling 测试Pod删除后的CEP资源回收
func (c *primaryIP) testCEPResourceRecycling(ctx context.Context) error {
	logger.Infof(ctx, "测试Pod删除后的CEP资源回收")

	// 获取当前运行的Pod列表
	pods, err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "app=test-primary-ip",
	})
	if err != nil {
		return fmt.Errorf("获取Pod列表失败: %v", err)
	}

	if len(pods.Items) == 0 {
		logger.Infof(ctx, "没有找到测试Pod，跳过CEP资源回收测试")
		return nil
	}

	// 选择一个Pod进行测试
	testPod := pods.Items[0]
	podName := testPod.Name
	podIP := testPod.Status.PodIP

	logger.Infof(ctx, "选择Pod %s (IP: %s) 进行CEP资源回收测试", podName, podIP)

	// 构造CEP资源名称（通常与Pod名称相同）
	targetCEP := podName
	logger.Infof(ctx, "检查Pod %s 对应的CEP资源: %s", podName, targetCEP)

	// 验证CEP资源是否存在
	_, err = c.base.KubeClient.GetCep(ctx, c.testNamespace, targetCEP, &kube.GetOptions{})
	if err != nil {
		if kerrors.IsNotFound(err) {
			logger.Warnf(ctx, "未找到Pod %s 对应的CEP资源 %s", podName, targetCEP)
			return nil
		}
		return fmt.Errorf("获取CEP资源失败: %v", err)
	}

	logger.Infof(ctx, "找到Pod %s 对应的CEP资源: %s", podName, targetCEP)

	// 删除Pod
	logger.Infof(ctx, "删除Pod %s", podName)
	err = c.base.K8SClient.CoreV1().Pods(c.testNamespace).Delete(ctx, podName, metav1.DeleteOptions{})
	if err != nil {
		return fmt.Errorf("删除Pod失败: %v", err)
	}

	// 等待Pod删除完成
	err = wait.PollImmediate(5*time.Second, 2*time.Minute, func() (bool, error) {
		_, err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).Get(ctx, podName, metav1.GetOptions{})
		if kerrors.IsNotFound(err) {
			return true, nil
		}
		return false, err
	})

	if err != nil {
		return fmt.Errorf("等待Pod删除完成超时: %v", err)
	}

	logger.Infof(ctx, "Pod %s 删除完成", podName)

	// 等待CEP资源回收
	logger.Infof(ctx, "等待CEP资源回收...")
	time.Sleep(30 * time.Second)

	// 检查CEP资源是否被回收
	err = wait.PollImmediate(10*time.Second, 3*time.Minute, func() (bool, error) {
		_, err := c.base.KubeClient.GetCep(ctx, c.testNamespace, targetCEP, &kube.GetOptions{})
		if kerrors.IsNotFound(err) {
			logger.Infof(ctx, "✅ CEP资源 %s 已被成功回收", targetCEP)
			return true, nil
		}
		if err != nil {
			logger.Warnf(ctx, "检查CEP资源时出错: %v", err)
			return false, nil
		}
		logger.Infof(ctx, "CEP资源 %s 仍然存在，继续等待回收...", targetCEP)
		return false, nil
	})

	if err != nil {
		return fmt.Errorf("CEP资源 %s 在3分钟内未被回收", targetCEP)
	}

	logger.Infof(ctx, "✅ CEP资源回收测试通过")
	return nil
}

// testNRSIPInfoUpdate 测试NRS IP信息更新
func (c *primaryIP) testNRSIPInfoUpdate(ctx context.Context) error {
	logger.Infof(ctx, "测试NRS IP信息更新")

	// 获取当前运行的Pod列表
	pods, err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "app=test-primary-ip",
	})
	if err != nil {
		return fmt.Errorf("获取Pod列表失败: %v", err)
	}

	if len(pods.Items) == 0 {
		logger.Infof(ctx, "没有找到测试Pod，跳过NRS IP信息更新测试")
		return nil
	}

	// 选择一个Pod进行测试
	testPod := pods.Items[0]
	podName := testPod.Name
	podIP := testPod.Status.PodIP

	logger.Infof(ctx, "选择Pod %s (IP: %s) 进行NRS IP信息更新测试", podName, podIP)

	// 获取删除前的NRS状态
	nrsBefore, err := c.base.KubeClient.GetNrs(ctx, c.primaryIPNodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取删除前NRS状态失败: %v", err)
	}

	// 检查Pod IP是否在NRS的Used列表中
	var ipFoundInUsed bool
	for usedIPStr := range nrsBefore.Status.Ipam.Used {
		if usedIPStr == podIP {
			ipFoundInUsed = true
			logger.Infof(ctx, "Pod IP %s 在NRS Used列表中找到", podIP)
			break
		}
	}

	if !ipFoundInUsed {
		logger.Warnf(ctx, "Pod IP %s 未在NRS Used列表中找到", podIP)
		return nil
	}

	usedIPsBefore := len(nrsBefore.Status.Ipam.Used)
	logger.Infof(ctx, "删除前NRS Used IP数量: %d", usedIPsBefore)

	// 删除Pod
	logger.Infof(ctx, "删除Pod %s", podName)
	err = c.base.K8SClient.CoreV1().Pods(c.testNamespace).Delete(ctx, podName, metav1.DeleteOptions{})
	if err != nil {
		return fmt.Errorf("删除Pod失败: %v", err)
	}

	// 等待Pod删除完成
	err = wait.PollImmediate(5*time.Second, 2*time.Minute, func() (bool, error) {
		_, err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).Get(ctx, podName, metav1.GetOptions{})
		if kerrors.IsNotFound(err) {
			return true, nil
		}
		return false, err
	})

	if err != nil {
		return fmt.Errorf("等待Pod删除完成超时: %v", err)
	}

	logger.Infof(ctx, "Pod %s 删除完成", podName)

	// 等待NRS状态更新
	logger.Infof(ctx, "等待NRS IP信息更新...")
	time.Sleep(30 * time.Second)

	// 检查NRS状态是否更新
	err = wait.PollImmediate(10*time.Second, 3*time.Minute, func() (bool, error) {
		nrsAfter, err := c.base.KubeClient.GetNrs(ctx, c.primaryIPNodeName, &kube.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取NRS状态失败: %v", err)
			return false, nil
		}

		// 检查Pod IP是否已从Used列表中移除
		for usedIPStr := range nrsAfter.Status.Ipam.Used {
			if usedIPStr == podIP {
				logger.Infof(ctx, "Pod IP %s 仍在NRS Used列表中，继续等待更新...", podIP)
				return false, nil
			}
		}

		usedIPsAfter := len(nrsAfter.Status.Ipam.Used)
		logger.Infof(ctx, "✅ Pod IP %s 已从NRS Used列表中移除", podIP)
		logger.Infof(ctx, "删除后NRS Used IP数量: %d (减少了 %d 个)", usedIPsAfter, usedIPsBefore-usedIPsAfter)
		return true, nil
	})

	if err != nil {
		return fmt.Errorf("Pod IP %s 在3分钟内未从NRS Used列表中移除", podIP)
	}

	logger.Infof(ctx, "✅ NRS IP信息更新测试通过")
	return nil
}

// testIPReleaseWatermark 测试辅助IP资源释放水位控制
func (c *primaryIP) testIPReleaseWatermark(ctx context.Context) error {
	logger.Infof(ctx, "测试辅助IP资源释放水位控制")

	// 获取当前NRS状态
	_, err := c.base.KubeClient.GetNrs(ctx, c.primaryIPNodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取NRS状态失败: %v", err)
	}

	// 获取当前网络配置
	configMap, err := c.base.K8SClient.CoreV1().ConfigMaps("kube-system").Get(ctx, "cce-network-v2-config", metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取网络配置失败: %v", err)
	}

	// 解析当前的水位配置
	ccedConfig := configMap.Data["cced"]
	logger.Infof(ctx, "当前网络配置: %s", ccedConfig)

	// 设置水位参数进行测试
	watermarkConfig := map[string]string{
		"ippool-max-above-watermark": "3",    // 水位线以上保留3个IP
		"ippool-min-allocate-ips":    "2",    // 最少分配2个IP
		"ippool-pre-allocate":        "1",    // 预分配1个IP
		"release-excess-ips":         "true", // 开启IP释放
		"burstable-mehrfach-eni":     "0",    // 关闭burst模式
	}

	logger.Infof(ctx, "设置水位控制参数")
	if err := c.updateNetworkConfig(ctx, watermarkConfig); err != nil {
		return fmt.Errorf("更新水位控制配置失败: %v", err)
	}

	// 重启operator使配置生效
	if err := c.restartOperator(ctx); err != nil {
		return fmt.Errorf("重启operator失败: %v", err)
	}

	// 获取当前运行的Pod数量
	pods, err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "app=test-primary-ip",
	})
	if err != nil {
		return fmt.Errorf("获取Pod列表失败: %v", err)
	}

	runningPods := 0
	for _, pod := range pods.Items {
		if pod.Status.Phase == corev1.PodRunning && pod.Spec.NodeName == c.primaryIPNodeName {
			runningPods++
		}
	}

	logger.Infof(ctx, "当前运行的Pod数量: %d", runningPods)

	// 获取配置生效后的NRS状态
	time.Sleep(30 * time.Second) // 等待配置生效

	nrsAfterConfig, err := c.base.KubeClient.GetNrs(ctx, c.primaryIPNodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取配置生效后NRS状态失败: %v", err)
	}

	usedIPsAfterConfig := len(nrsAfterConfig.Status.Ipam.Used)
	poolIPsAfterConfig := len(nrsAfterConfig.Spec.Ipam.Pool)

	logger.Infof(ctx, "配置生效后 - Used IP数量: %d, Pool IP数量: %d", usedIPsAfterConfig, poolIPsAfterConfig)

	// 缩容Pod到1个，测试水位控制
	logger.Infof(ctx, "缩容Deployment到1个副本，测试水位控制")
	deployment, err := c.base.K8SClient.AppsV1().Deployments(c.testNamespace).Get(ctx, c.testDeploymentName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取Deployment失败: %v", err)
	}

	deployment.Spec.Replicas = primaryIPInt32Ptr(1)
	_, err = c.base.K8SClient.AppsV1().Deployments(c.testNamespace).Update(ctx, deployment, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新Deployment失败: %v", err)
	}

	// 等待缩容完成和IP释放
	logger.Infof(ctx, "等待缩容完成和IP释放...")
	time.Sleep(60 * time.Second)

	// 检查缩容后的NRS状态
	nrsAfterScale, err := c.base.KubeClient.GetNrs(ctx, c.primaryIPNodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取缩容后NRS状态失败: %v", err)
	}

	usedIPsAfterScale := len(nrsAfterScale.Status.Ipam.Used)
	poolIPsAfterScale := len(nrsAfterScale.Spec.Ipam.Pool)

	logger.Infof(ctx, "缩容后 - Used IP数量: %d, Pool IP数量: %d", usedIPsAfterScale, poolIPsAfterScale)

	// 验证水位控制效果
	expectedMinIPs := 1 + 3 // 1个Pod使用 + 3个水位线以上保留
	if poolIPsAfterScale >= expectedMinIPs {
		logger.Infof(ctx, "✅ 水位控制正常，Pool中保留了 %d 个IP (期望至少 %d 个)", poolIPsAfterScale, expectedMinIPs)
	} else {
		logger.Warnf(ctx, "⚠️ 水位控制可能异常，Pool中只有 %d 个IP (期望至少 %d 个)", poolIPsAfterScale, expectedMinIPs)
	}

	// 验证只释放了水位数量以上的资源
	releasedIPs := poolIPsAfterConfig - poolIPsAfterScale
	if releasedIPs > 0 {
		logger.Infof(ctx, "✅ 成功释放了 %d 个多余的IP资源", releasedIPs)
	} else {
		logger.Infof(ctx, "没有释放IP资源，可能是因为当前IP数量已在水位线内")
	}

	// 验证Used IP数量的变化
	usedIPsReduced := usedIPsAfterConfig - usedIPsAfterScale
	if usedIPsReduced > 0 {
		logger.Infof(ctx, "✅ Used IP数量减少了 %d 个，符合Pod缩容预期", usedIPsReduced)
	}

	logger.Infof(ctx, "✅ 辅助IP资源释放水位控制测试完成")
	return nil
}

// Continue 返回是否继续执行
func (c *primaryIP) Continue(ctx context.Context) bool {
	return true
}

// ConfigFormat 返回配置格式
func (c *primaryIP) ConfigFormat() string {
	return ""
}

// updateNRS 更新NRS资源
func (c *primaryIP) updateNRS(ctx context.Context, nrs *types.NetworkResourceSet) error {
	// 使用动态客户端更新NRS
	clientSet, err := c.base.KubeClient.NewDynamicClient("cce.baidubce.com", "v2", "netresourcesets")
	if err != nil {
		return fmt.Errorf("创建动态客户端失败: %v", err)
	}

	// 转换为unstructured对象
	unstructuredObj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(nrs)
	if err != nil {
		return fmt.Errorf("转换NRS为unstructured失败: %v", err)
	}

	// 更新资源
	_, err = clientSet.Update(ctx, &unstructured.Unstructured{Object: unstructuredObj}, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新NRS失败: %v", err)
	}

	return nil
}

// 辅助函数
func primaryIPInt32Ptr(i int32) *int32 {
	return &i
}
