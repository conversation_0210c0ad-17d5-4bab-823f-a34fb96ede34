/*
TenNodes1000ContainersScaling 测试用例

功能描述：
测试10节点1000容器的大规模扩容耗时性能，验证CCE集群在大规模Pod创建场景下的调度效率、
资源分配能力和网络配置性能。此测试模拟真实生产环境中的批量任务扩容场景。

测试流程：
1. 前置检查：验证集群有至少10个Ready状态的工作节点
2. 资源验证：检查多节点资源充足性（CPU、内存、Pod数量限制）
3. 网络检查：基于NRS信息计算可用IP总数量，确保网络资源充足
4. 创建Deployment：初始副本数为0，配置节点亲和性策略
5. 扩容执行：从0副本快速扩容到1000副本
6. 实时监控：并发监控所有Pod的创建和就绪过程
7. 节点分布统计：实时统计各节点Pod分布情况
8. 关键节点记录：记录100、250、500、750、1000个Pod就绪的时间点
9. 性能分析：计算扩容时间统计和节点分布均匀性
10. 资源监控：监控集群资源使用情况变化

关键性能指标：
1. 总扩容耗时：从开始扩容到全部Pod就绪的总时间
2. 平均调度时间：Pod从创建到被调度到节点的平均时间
3. 平均启动时间：Pod从调度到容器启动的平均时间
4. 阶段扩容速率：各阶段的扩容速度（Pods/秒）
5. 节点分布均匀性：Pod在各节点间的分布差异分析
6. 资源使用率：扩容过程中各节点CPU、内存使用率变化

性能基准目标：
- 总扩容时间: < 10分钟（1000个Pod全部就绪）
- 调度延迟: 平均 < 5秒/Pod
- 分布均匀性: 各节点Pod数量差异 < 20%
- 成功率: > 99%（失败Pod < 10个）

技术实现特点：
- 使用轻量级pause镜像减少启动时间
- Pod反亲和性策略确保均匀分布
- 分阶段监控机制（30秒采样间隔）
- 完善的超时保护和错误处理
- 自动资源清理机制

注意事项：
此测试会在集群中创建大量Pod，建议在测试环境运行。测试期间会对集群造成较大负载，
可能影响其他工作负载的性能。测试完成后会自动清理所有资源。
*/

package network

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	resourcelogic "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	// TenNodes1000ContainersScalingCaseName - case 名字
	TenNodes1000ContainersScalingCaseName cases.CaseName = "TenNodes1000ContainersScaling"
)

// TenNodes1000ContainersScalingConfig 大规模扩容测试配置
type TenNodes1000ContainersScalingConfig struct {
	// 目标节点数量
	RequiredNodes int `json:"requiredNodes"`

	// 总Pod数量
	TotalPods int `json:"totalPods"`

	// Pod规格配置
	PodSpec struct {
		Image     string                      `json:"image"`
		Resources corev1.ResourceRequirements `json:"resources"`
	} `json:"podSpec"`

	// 节点选择器
	NodeSelector map[string]string `json:"nodeSelector"`

	// 节点创建配置
	NodeCreation struct {
		Enable        bool   `json:"enable"`        // 是否启用自动创建节点
		InstanceType  string `json:"instanceType"`  // 实例规格
		ImageID       string `json:"imageID"`       // 镜像ID
		AdminPassword string `json:"adminPassword"` // 管理员密码
		CPU           int    `json:"cpu"`           // CPU核数
		Memory        int    `json:"memory"`        // 内存大小(GB)
		DiskSize      int    `json:"diskSize"`      // 磁盘大小(GB)
		MachineSpec   string `json:"machineSpec"`   // 机器规格
		SpecID        string `json:"specID"`        // 规格ID
	} `json:"nodeCreation"`

	// 超时配置
	Timeouts struct {
		TotalTimeout     int `json:"totalTimeout"`     // 总超时时间（秒）
		PodReadyTimeout  int `json:"podReadyTimeout"`  // Pod就绪超时（秒）
		CleanupTimeout   int `json:"cleanupTimeout"`   // 清理超时（秒）
		NodeReadyTimeout int `json:"nodeReadyTimeout"` // 节点就绪超时（秒）
	} `json:"timeouts"`

	// 监控配置
	Monitoring struct {
		SampleInterval int  `json:"sampleInterval"` // 采样间隔（秒）
		EnableMetrics  bool `json:"enableMetrics"`  // 启用详细指标
	} `json:"monitoring"`
}

// ScalingMilestone 扩容里程碑
type ScalingMilestone struct {
	TargetPods  int           `json:"targetPods"`  // 目标Pod数量
	ActualPods  int           `json:"actualPods"`  // 实际Pod数量
	ReadyPods   int           `json:"readyPods"`   // 就绪Pod数量
	ElapsedTime time.Duration `json:"elapsedTime"` // 已用时间
	Timestamp   time.Time     `json:"timestamp"`   // 记录时间
	ScalingRate float64       `json:"scalingRate"` // 扩容速率(Pods/秒)
	Percentage  float64       `json:"percentage"`  // 完成百分比
}

// NodeDistribution 节点分布统计
type NodeDistribution struct {
	NodeName      string  `json:"nodeName"`      // 节点名称
	PodCount      int     `json:"podCount"`      // Pod数量
	ScheduledPods int     `json:"scheduledPods"` // 已调度Pod数
	RunningPods   int     `json:"runningPods"`   // 运行Pod数
	ReadyPods     int     `json:"readyPods"`     // 就绪Pod数
	FailedPods    int     `json:"failedPods"`    // 失败Pod数
	CPUUsage      string  `json:"cpuUsage"`      // CPU使用率
	MemoryUsage   string  `json:"memoryUsage"`   // 内存使用率
	Percentage    float64 `json:"percentage"`    // 占总数百分比
}

// ResourceCapacity 资源容量统计
type ResourceCapacity struct {
	NodeName          string `json:"nodeName"`          // 节点名称
	MaxPods           int64  `json:"maxPods"`           // 最大Pod数
	AllocatableCPU    string `json:"allocatableCPU"`    // 可分配CPU
	AllocatableMemory string `json:"allocatableMemory"` // 可分配内存
	AvailableIPs      int    `json:"availableIPs"`      // 可用IP数量
}

// TenNodes1000ContainersScalingStats 扩容测试统计
type TenNodes1000ContainersScalingStats struct {
	// 基本信息
	TestStartTime time.Time     `json:"testStartTime"` // 测试开始时间
	TestEndTime   time.Time     `json:"testEndTime"`   // 测试结束时间
	TotalDuration time.Duration `json:"totalDuration"` // 总耗时

	// 里程碑记录
	Milestones []ScalingMilestone `json:"milestones"` // 扩容里程碑

	// 节点分布
	NodeDistributions []NodeDistribution `json:"nodeDistributions"` // 节点分布统计

	// 资源容量
	ResourceCapacities []ResourceCapacity `json:"resourceCapacities"` // 资源容量统计

	// 性能指标
	SuccessPods         int           `json:"successPods"`         // 成功Pod数
	FailedPods          int           `json:"failedPods"`          // 失败Pod数
	SuccessRate         float64       `json:"successRate"`         // 成功率
	AverageScheduleTime time.Duration `json:"averageScheduleTime"` // 平均调度时间
	AverageStartupTime  time.Duration `json:"averageStartupTime"`  // 平均启动时间
	ThroughputPPS       float64       `json:"throughputPPS"`       // 吞吐量(Pods/秒)

	// 分布均匀性
	DistributionVariance float64 `json:"distributionVariance"` // 分布方差
	MaxNodePods          int     `json:"maxNodePods"`          // 单节点最大Pod数
	MinNodePods          int     `json:"minNodePods"`          // 单节点最小Pod数
	UniformityRatio      float64 `json:"uniformityRatio"`      // 均匀性比率
}

type tenNodes1000ContainersScaling struct {
	base      *cases.BaseClient
	config    TenNodes1000ContainersScalingConfig
	stats     TenNodes1000ContainersScalingStats
	resources []cases.Resource

	// 测试资源
	deploymentName string
	namespace      string
	targetNodes    []string
	testStartTime  time.Time

	// 里程碑阈值 [100, 250, 500, 750, 1000]
	milestoneThresholds []int

	// 创建的实例组
	createdInstanceGroupID string
	nodeCreationEnabled    bool
}

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), TenNodes1000ContainersScalingCaseName, NewTenNodes1000ContainersScaling)
}

// NewTenNodes1000ContainersScaling - 创建测试案例
func NewTenNodes1000ContainersScaling(ctx context.Context) cases.Interface {
	return &tenNodes1000ContainersScaling{
		deploymentName:      "test-ten-nodes-1000-containers",
		namespace:           "default",
		milestoneThresholds: []int{100, 250, 500, 750, 1000},
	}
}

func (c *tenNodes1000ContainersScaling) Name() cases.CaseName {
	return TenNodes1000ContainersScalingCaseName
}

func (c *tenNodes1000ContainersScaling) Desc() string {
	return "测试10节点大规模容器的扩容耗时性能（根据IP资源动态调整）"
}

func (c *tenNodes1000ContainersScaling) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base client is nil")
	}
	c.base = base

	// 解析配置
	if len(config) > 0 {
		if err := json.Unmarshal(config, &c.config); err != nil {
			return fmt.Errorf("解析配置失败: %v", err)
		}
	}

	// 设置默认配置
	c.setDefaultConfig()

	logger.Infof(ctx, "TenNodes1000ContainersScaling测试初始化完成，目标: %d节点%d容器（可动态调整），自动创建节点: %v",
		c.config.RequiredNodes, c.config.TotalPods, c.config.NodeCreation.Enable)

	return nil
}

func (c *tenNodes1000ContainersScaling) Check(ctx context.Context) ([]cases.Resource, error) {
	logger.Infof(ctx, "🚀 开始10节点1000容器扩容耗时测试...")

	// 初始化统计数据
	c.stats = TenNodes1000ContainersScalingStats{
		TestStartTime: time.Now(),
	}
	c.testStartTime = c.stats.TestStartTime

	// 1. 检查集群节点
	if err := c.checkClusterNodes(ctx); err != nil {
		return nil, fmt.Errorf("集群节点检查失败: %v", err)
	}

	// 2. 验证多节点资源充足性
	if err := c.validateMultiNodeResources(ctx); err != nil {
		return nil, fmt.Errorf("多节点资源验证失败: %v", err)
	}

	// 3. 检查网络资源充足性
	if err := c.checkNetworkResources(ctx); err != nil {
		return nil, fmt.Errorf("网络资源检查失败: %v", err)
	}

	// 4. 创建大规模Deployment
	if err := c.createMassiveDeployment(ctx); err != nil {
		return nil, fmt.Errorf("创建大规模Deployment失败: %v", err)
	}

	// 5. 执行扩容并监控
	if err := c.executeScalingAndMonitor(ctx); err != nil {
		return nil, fmt.Errorf("扩容监控失败: %v", err)
	}

	// 6. 分析结果
	c.analyzeResults(ctx)

	// 7. 生成报告
	c.generateReport(ctx)

	c.stats.TestEndTime = time.Now()
	c.stats.TotalDuration = c.stats.TestEndTime.Sub(c.stats.TestStartTime)

	logger.Infof(ctx, "✅ 10节点1000容器扩容测试完成，总耗时: %v", c.stats.TotalDuration)
	return c.resources, nil
}

func (c *tenNodes1000ContainersScaling) Clean(ctx context.Context) error {
	logger.Infof(ctx, "🧹 开始清理测试资源...")

	// 删除Deployment
	if err := c.cleanupDeployment(ctx); err != nil {
		logger.Warnf(ctx, "清理Deployment失败: %v", err)
	}

	// 等待Pod完全删除
	if err := c.waitForCleanup(ctx); err != nil {
		logger.Warnf(ctx, "等待资源清理失败: %v", err)
	}

	// 清理创建的实例组
	if c.createdInstanceGroupID != "" {
		if err := c.cleanupInstanceGroup(ctx); err != nil {
			logger.Warnf(ctx, "清理实例组失败: %v", err)
		}
	}

	logger.Infof(ctx, "✅ 测试资源清理完成")
	return nil
}

func (c *tenNodes1000ContainersScaling) Continue(ctx context.Context) bool {
	return true
}

func (c *tenNodes1000ContainersScaling) ConfigFormat() string {
	return `{
  "requiredNodes": 10,
  "totalPods": 1000,
  "podSpec": {
    "image": "registry.baidubce.com/cce/nginx-alpine-go:latest",
    "resources": {
      "requests": {
        "cpu": "50m",
        "memory": "64Mi"
      },
      "limits": {
        "cpu": "200m",
        "memory": "256Mi"
      }
    }
  },
  "nodeSelector": {
    "node-role.kubernetes.io/worker": "true"
  },
  "nodeCreation": {
    "enable": true,
    "instanceType": "N5",
    "imageID": "",
    "adminPassword": "Test123456!",
    "cpu": 4,
    "memory": 16,
    "diskSize": 100,
    "machineSpec": "bcc.g4.c4m16",
    "specID": "g4"
  },
  "timeouts": {
    "totalTimeout": 900,
    "podReadyTimeout": 300,
    "cleanupTimeout": 300,
    "nodeReadyTimeout": 600
  },
  "monitoring": {
    "sampleInterval": 30,
    "enableMetrics": true
  }
}`
}

// 设置默认配置
func (c *tenNodes1000ContainersScaling) setDefaultConfig() {
	if c.config.RequiredNodes == 0 {
		c.config.RequiredNodes = 10
	}
	if c.config.TotalPods == 0 {
		c.config.TotalPods = 1000
	}
	if c.config.PodSpec.Image == "" {
		c.config.PodSpec.Image = "registry.baidubce.com/cce/nginx-alpine-go:latest"
	}
	if c.config.Timeouts.TotalTimeout == 0 {
		c.config.Timeouts.TotalTimeout = 900 // 15分钟
	}
	if c.config.Timeouts.PodReadyTimeout == 0 {
		c.config.Timeouts.PodReadyTimeout = 300 // 5分钟
	}
	if c.config.Timeouts.CleanupTimeout == 0 {
		c.config.Timeouts.CleanupTimeout = 300 // 5分钟
	}
	if c.config.Timeouts.NodeReadyTimeout == 0 {
		c.config.Timeouts.NodeReadyTimeout = 600 // 10分钟
	}
	if c.config.Monitoring.SampleInterval == 0 {
		c.config.Monitoring.SampleInterval = 30 // 30秒
	}

	// 默认启用节点创建功能
	c.config.NodeCreation.Enable = true
	c.nodeCreationEnabled = true

	// 节点创建默认配置
	if c.config.NodeCreation.InstanceType == "" {
		c.config.NodeCreation.InstanceType = "N5"
	}
	if c.config.NodeCreation.AdminPassword == "" {
		c.config.NodeCreation.AdminPassword = "Test123456!"
	}
	if c.config.NodeCreation.CPU == 0 {
		c.config.NodeCreation.CPU = 4
	}
	if c.config.NodeCreation.Memory == 0 {
		c.config.NodeCreation.Memory = 16
	}
	if c.config.NodeCreation.DiskSize == 0 {
		c.config.NodeCreation.DiskSize = 100
	}
	if c.config.NodeCreation.MachineSpec == "" {
		c.config.NodeCreation.MachineSpec = "bcc.g4.c4m16"
	}
	if c.config.NodeCreation.SpecID == "" {
		c.config.NodeCreation.SpecID = "g4"
	}

	// 设置默认资源请求和限制
	if c.config.PodSpec.Resources.Requests == nil {
		c.config.PodSpec.Resources.Requests = corev1.ResourceList{
			corev1.ResourceCPU:    resource.MustParse("50m"),
			corev1.ResourceMemory: resource.MustParse("64Mi"),
		}
	}
	if c.config.PodSpec.Resources.Limits == nil {
		c.config.PodSpec.Resources.Limits = corev1.ResourceList{
			corev1.ResourceCPU:    resource.MustParse("200m"),
			corev1.ResourceMemory: resource.MustParse("256Mi"),
		}
	}
}

// checkClusterNodes - 检查集群节点
func (c *tenNodes1000ContainersScaling) checkClusterNodes(ctx context.Context) error {
	logger.Infof(ctx, "📋 检查集群节点状态...")

	nodeList, err := c.base.KubeClient.ListNode(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取节点列表失败: %v", err)
	}

	readyNodes := 0
	var availableNodes []string

	for _, node := range nodeList.Items {
		// 检查节点状态
		isReady := false
		for _, condition := range node.Status.Conditions {
			if condition.Type == corev1.NodeNetworkUnavailable && condition.Status == corev1.ConditionFalse {
				isReady = true
				break
			}
		}

		if isReady && c.nodeMatchesSelector(&node) {
			readyNodes++
			availableNodes = append(availableNodes, node.Name)
		}
	}

	logger.Infof(ctx, "当前可用节点数: %d，需要节点数: %d", readyNodes, c.config.RequiredNodes)

	// 如果节点不足，检查是否启用自动创建
	if readyNodes < c.config.RequiredNodes {
		if c.nodeCreationEnabled {
			logger.Infof(ctx, "节点数量不足，将自动创建节点...")
			if err := c.createNodesIfNeeded(ctx, c.config.RequiredNodes-readyNodes); err != nil {
				return fmt.Errorf("创建节点失败: %v", err)
			}

			// 重新获取节点列表
			return c.waitForNodesReady(ctx)
		} else {
			return fmt.Errorf("可用节点数量不足: 需要%d个，实际%d个。请启用nodeCreation.enable或手动添加节点",
				c.config.RequiredNodes, readyNodes)
		}
	}

	// 选择目标节点
	if len(availableNodes) >= c.config.RequiredNodes {
		c.targetNodes = availableNodes[:c.config.RequiredNodes]
	} else {
		c.targetNodes = availableNodes
	}

	logger.Infof(ctx, "✅ 节点检查通过，选中节点: %v", c.targetNodes)
	return nil
}

// nodeMatchesSelector - 检查节点是否匹配选择器
func (c *tenNodes1000ContainersScaling) nodeMatchesSelector(node *corev1.Node) bool {
	if len(c.config.NodeSelector) == 0 {
		return true
	}

	for key, value := range c.config.NodeSelector {
		if nodeValue, exists := node.Labels[key]; !exists || nodeValue != value {
			return false
		}
	}
	return true
}

// validateMultiNodeResources - 验证多节点资源充足性
func (c *tenNodes1000ContainersScaling) validateMultiNodeResources(ctx context.Context) error {
	logger.Infof(ctx, "📊 验证多节点资源充足性...")

	var totalMaxPods int64
	var resourceCapacities []ResourceCapacity

	for _, nodeName := range c.targetNodes {
		node, err := c.base.KubeClient.GetNode(ctx, nodeName, &kube.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取节点%s信息失败: %v", nodeName, err)
			continue
		}

		capacity := ResourceCapacity{
			NodeName: nodeName,
		}

		// 获取最大Pod数量
		if maxPods, exists := node.Status.Allocatable[corev1.ResourcePods]; exists {
			capacity.MaxPods = maxPods.Value()
			totalMaxPods += capacity.MaxPods
		}

		// 获取可分配CPU和内存
		if allocCPU, exists := node.Status.Allocatable[corev1.ResourceCPU]; exists {
			capacity.AllocatableCPU = allocCPU.String()
		}
		if allocMem, exists := node.Status.Allocatable[corev1.ResourceMemory]; exists {
			capacity.AllocatableMemory = allocMem.String()
		}

		resourceCapacities = append(resourceCapacities, capacity)

		logger.Infof(ctx, "  节点 %s: 最大Pod数=%d, CPU=%s, 内存=%s",
			nodeName, capacity.MaxPods, capacity.AllocatableCPU, capacity.AllocatableMemory)
	}

	c.stats.ResourceCapacities = resourceCapacities

	// 检查总Pod容量
	if totalMaxPods < int64(c.config.TotalPods) {
		return fmt.Errorf("节点总Pod容量不足: 需要%d，实际%d", c.config.TotalPods, totalMaxPods)
	}

	logger.Infof(ctx, "✅ 资源验证通过，总Pod容量: %d", totalMaxPods)
	return nil
}

// checkNetworkResources - 检查网络资源并动态调整Pod数量
func (c *tenNodes1000ContainersScaling) checkNetworkResources(ctx context.Context) error {
	logger.Infof(ctx, "🌐 检查网络资源并动态调整测试规模...")

	totalAvailableIPs := 0

	// 遍历每个目标节点检查网络资源
	for _, nodeName := range c.targetNodes {
		availableIPs, err := c.calculateNodeAvailableIPs(ctx, nodeName)
		if err != nil {
			logger.Warnf(ctx, "节点 %s 网络资源检查失败: %v，跳过该节点", nodeName, err)
			continue
		}

		totalAvailableIPs += availableIPs

		// 更新资源容量信息
		for i, capacity := range c.stats.ResourceCapacities {
			if capacity.NodeName == nodeName {
				c.stats.ResourceCapacities[i].AvailableIPs = availableIPs
				break
			}
		}

		logger.Infof(ctx, "  节点 %s: 可用IP数量=%d", nodeName, availableIPs)
	}

	logger.Infof(ctx, "🎯 原计划测试Pod数量: %d", c.config.TotalPods)
	logger.Infof(ctx, "🔧 实际可用IP数量: %d", totalAvailableIPs)

	// 动态调整测试Pod数量
	if totalAvailableIPs < c.config.TotalPods {
		logger.Infof(ctx, "📊 根据网络资源调整测试目标: %d -> %d (受限于可用IP数量)",
			c.config.TotalPods, totalAvailableIPs)
		c.config.TotalPods = totalAvailableIPs

		// 同时更新里程碑阈值，按比例调整
		if c.config.TotalPods > 0 {
			newMilestones := []int{}
			for _, milestone := range c.milestoneThresholds {
				newMilestone := (milestone * c.config.TotalPods) / 1000
				if newMilestone > 0 && newMilestone <= c.config.TotalPods {
					newMilestones = append(newMilestones, newMilestone)
				}
			}
			// 确保最后一个里程碑是总数
			if len(newMilestones) == 0 || newMilestones[len(newMilestones)-1] != c.config.TotalPods {
				newMilestones = append(newMilestones, c.config.TotalPods)
			}
			c.milestoneThresholds = newMilestones
			logger.Infof(ctx, "📈 里程碑已调整为: %v", c.milestoneThresholds)
		}
	}

	// 检查是否至少能测试一些Pod
	if totalAvailableIPs <= 0 {
		return fmt.Errorf("可用IP数量为0，无法进行任何Pod测试")
	}

	logger.Infof(ctx, "✅ 网络资源检查完成，将测试 %d 个Pod的扩容", c.config.TotalPods)
	return nil
}

// calculateNodeAvailableIPs - 计算单个节点的可用IP数量
func (c *tenNodes1000ContainersScaling) calculateNodeAvailableIPs(ctx context.Context, nodeName string) (int, error) {
	// 获取节点的NRS信息
	nrs, err := c.base.KubeClient.GetNrs(ctx, nodeName, &kube.GetOptions{})
	if err != nil {
		return 0, fmt.Errorf("获取节点NRS失败: %v", err)
	}

	// 获取当前ENI列表
	eniList, err := c.base.KubeClient.ListENI(ctx, &kube.ListOptions{})
	if err != nil {
		return 0, fmt.Errorf("获取ENI列表失败: %v", err)
	}

	// 过滤出属于该节点的ENI
	var nodeENIs []types.ENI
	for _, eni := range eniList.Items {
		if eni.Spec.NodeName == nodeName {
			nodeENIs = append(nodeENIs, eni)
		}
	}

	// 计算可用IP数量
	maxIPsPerENI := nrs.Spec.Eni.MaxIPsPerENI
	maxENIs := nrs.Spec.Eni.MaxAllocateENI
	usedIPs := len(nrs.Status.Ipam.Used)

	// 计算总可用IP数量（减去ENI自身占用的IP）
	totalAvailableIPs := maxIPsPerENI*maxENIs - maxENIs - usedIPs

	logger.Infof(ctx, "节点 %s 网络资源详情: 每ENI最大IP=%d, 最大ENI数=%d, 当前ENI数=%d, 已用IP=%d, 可用IP=%d",
		nodeName, maxIPsPerENI, maxENIs, len(nodeENIs), usedIPs, totalAvailableIPs)

	return totalAvailableIPs, nil
}

// createMassiveDeployment - 创建大规模Deployment
func (c *tenNodes1000ContainersScaling) createMassiveDeployment(ctx context.Context) error {
	logger.Infof(ctx, "🚀 创建大规模Deployment...")

	// 初始副本数为0
	replicas := int32(0)

	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.deploymentName,
			Namespace: c.namespace,
			Labels: map[string]string{
				"app":       "ten-nodes-1000-containers-test",
				"test-type": "massive-scaling",
			},
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": "ten-nodes-1000-containers-test",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": "ten-nodes-1000-containers-test",
					},
				},
				Spec: corev1.PodSpec{
					RestartPolicy: corev1.RestartPolicyAlways,
					Containers: []corev1.Container{
						{
							Name:  "nginx",
							Image: c.config.PodSpec.Image,
							Ports: []corev1.ContainerPort{
								{
									ContainerPort: 80,
									Protocol:      corev1.ProtocolTCP,
								},
							},
							Resources: c.config.PodSpec.Resources,
						},
					},
					NodeSelector: c.config.NodeSelector,
					// 使用Pod反亲和性确保均匀分布
					Affinity: &corev1.Affinity{
						PodAntiAffinity: &corev1.PodAntiAffinity{
							PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{
								{
									Weight: 100,
									PodAffinityTerm: corev1.PodAffinityTerm{
										LabelSelector: &metav1.LabelSelector{
											MatchLabels: map[string]string{
												"app": "ten-nodes-1000-containers-test",
											},
										},
										TopologyKey: "kubernetes.io/hostname",
									},
								},
							},
						},
					},
				},
			},
		},
	}

	_, err := c.base.KubeClient.CreateDeploymentAppsV1(ctx, c.namespace, deployment)
	if err != nil {
		return fmt.Errorf("创建Deployment失败: %v", err)
	}

	// 记录资源用于清理
	c.resources = append(c.resources, cases.Resource{
		CaseName: c.Name(),
		Type:     "Deployment",
		ID:       fmt.Sprintf("%s/%s", c.namespace, c.deploymentName),
	})

	logger.Infof(ctx, "✅ Deployment创建成功，开始扩容...")
	return nil
}

// executeScalingAndMonitor - 执行扩容并监控
func (c *tenNodes1000ContainersScaling) executeScalingAndMonitor(ctx context.Context) error {
	logger.Infof(ctx, "📈 开始扩容并监控进度...")

	// 记录扩容开始时间
	scalingStartTime := time.Now()

	// 使用Scale API扩容到目标副本数
	scale, err := c.base.KubeClient.GetDeploymentScaleAppsV1(ctx, c.namespace, c.deploymentName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取Deployment Scale失败: %v", err)
	}

	scale.Spec.Replicas = int32(c.config.TotalPods)
	_, err = c.base.KubeClient.ScaleDeploymentAppsV1(ctx, c.namespace, c.deploymentName, scale)
	if err != nil {
		return fmt.Errorf("扩容Deployment失败: %v", err)
	}

	logger.Infof(ctx, "🎯 开始扩容到%d个副本...", c.config.TotalPods)

	// 监控扩容进度
	timeout := time.Duration(c.config.Timeouts.TotalTimeout) * time.Second
	sampleInterval := time.Duration(c.config.Monitoring.SampleInterval) * time.Second

	milestoneIndex := 0
	var milestones []ScalingMilestone

	return wait.PollImmediate(sampleInterval, timeout, func() (done bool, err error) {
		// 获取当前Pod状态
		pods, err := c.base.KubeClient.ListPod(ctx, c.namespace, &kube.ListOptions{
			LabelSelector: map[string]string{"app": "ten-nodes-1000-containers-test"},
		})
		if err != nil {
			logger.Warnf(ctx, "获取Pod列表失败: %v", err)
			return false, nil
		}

		currentTime := time.Now()
		elapsed := currentTime.Sub(scalingStartTime)

		// 统计Pod状态
		totalPods := len(pods.Items)
		readyPods := 0
		scheduledPods := 0
		runningPods := 0

		for _, pod := range pods.Items {
			if pod.Spec.NodeName != "" {
				scheduledPods++
			}
			if pod.Status.Phase == corev1.PodRunning {
				runningPods++
			}
			// 检查Pod是否Ready
			for _, condition := range pod.Status.Conditions {
				if condition.Type == corev1.PodReady && condition.Status == corev1.ConditionTrue {
					readyPods++
					break
				}
			}
		}

		// 检查是否达到里程碑
		if milestoneIndex < len(c.milestoneThresholds) && readyPods >= c.milestoneThresholds[milestoneIndex] {
			milestone := ScalingMilestone{
				TargetPods:  c.milestoneThresholds[milestoneIndex],
				ActualPods:  totalPods,
				ReadyPods:   readyPods,
				ElapsedTime: elapsed,
				Timestamp:   currentTime,
				Percentage:  float64(readyPods) / float64(c.config.TotalPods) * 100,
			}

			// 计算扩容速率
			if elapsed.Seconds() > 0 {
				milestone.ScalingRate = float64(readyPods) / elapsed.Seconds()
			}

			milestones = append(milestones, milestone)
			c.stats.Milestones = milestones

			logger.Infof(ctx, "🎯 里程碑达成: %d/%d Pod就绪 (%.1f%%), 耗时: %v, 速率: %.2f pods/sec",
				readyPods, c.config.TotalPods, milestone.Percentage, elapsed, milestone.ScalingRate)

			milestoneIndex++
		}

		// 定期输出进度
		if int(elapsed.Seconds())%30 == 0 || readyPods == c.config.TotalPods {
			logger.Infof(ctx, "进度更新: 总计%d, 调度%d, 运行%d, 就绪%d, 耗时%v",
				totalPods, scheduledPods, runningPods, readyPods, elapsed)

			// 统计节点分布
			c.updateNodeDistribution(ctx, pods.Items)
		}

		// 检查是否完成
		if readyPods >= c.config.TotalPods {
			logger.Infof(ctx, "🎉 扩容完成! 全部%d个Pod已就绪，总耗时: %v", readyPods, elapsed)
			return true, nil
		}

		return false, nil
	})
}

// updateNodeDistribution - 更新节点分布统计
func (c *tenNodes1000ContainersScaling) updateNodeDistribution(ctx context.Context, pods []corev1.Pod) {
	nodeDistMap := make(map[string]*NodeDistribution)

	// 初始化节点分布
	for _, nodeName := range c.targetNodes {
		nodeDistMap[nodeName] = &NodeDistribution{
			NodeName: nodeName,
		}
	}

	// 统计Pod分布
	for _, pod := range pods {
		if pod.Spec.NodeName == "" {
			continue // 未调度的Pod
		}

		nodeDist, exists := nodeDistMap[pod.Spec.NodeName]
		if !exists {
			// Pod调度到了非目标节点
			nodeDistMap[pod.Spec.NodeName] = &NodeDistribution{
				NodeName: pod.Spec.NodeName,
			}
			nodeDist = nodeDistMap[pod.Spec.NodeName]
		}

		nodeDist.PodCount++
		nodeDist.ScheduledPods++

		if pod.Status.Phase == corev1.PodRunning {
			nodeDist.RunningPods++
		}
		if pod.Status.Phase == corev1.PodFailed {
			nodeDist.FailedPods++
		}

		// 检查Pod是否Ready
		for _, condition := range pod.Status.Conditions {
			if condition.Type == corev1.PodReady && condition.Status == corev1.ConditionTrue {
				nodeDist.ReadyPods++
				break
			}
		}
	}

	// 转换为slice并计算百分比
	var distributions []NodeDistribution
	for _, dist := range nodeDistMap {
		if c.config.TotalPods > 0 {
			dist.Percentage = float64(dist.PodCount) / float64(c.config.TotalPods) * 100
		}
		distributions = append(distributions, *dist)
	}

	sort.Slice(distributions, func(i, j int) bool {
		return distributions[i].NodeName < distributions[j].NodeName
	})

	c.stats.NodeDistributions = distributions
}

// analyzeResults - 分析结果
func (c *tenNodes1000ContainersScaling) analyzeResults(ctx context.Context) {
	logger.Infof(ctx, "📊 分析测试结果...")

	// 统计成功和失败的Pod
	for _, dist := range c.stats.NodeDistributions {
		c.stats.SuccessPods += dist.ReadyPods
		c.stats.FailedPods += dist.FailedPods
	}

	// 计算成功率
	if c.config.TotalPods > 0 {
		c.stats.SuccessRate = float64(c.stats.SuccessPods) / float64(c.config.TotalPods) * 100
	}

	// 计算吞吐量
	if c.stats.TotalDuration > 0 {
		c.stats.ThroughputPPS = float64(c.stats.SuccessPods) / c.stats.TotalDuration.Seconds()
	}

	// 分析分布均匀性
	c.analyzeDistributionUniformity(ctx)

	logger.Infof(ctx, "结果分析完成 - 成功: %d, 失败: %d, 成功率: %.2f%%, 吞吐量: %.2f pods/sec",
		c.stats.SuccessPods, c.stats.FailedPods, c.stats.SuccessRate, c.stats.ThroughputPPS)
}

// analyzeDistributionUniformity - 分析分布均匀性
func (c *tenNodes1000ContainersScaling) analyzeDistributionUniformity(ctx context.Context) {
	if len(c.stats.NodeDistributions) == 0 {
		return
	}

	podCounts := []int{}
	maxPods := 0
	minPods := c.config.TotalPods

	for _, dist := range c.stats.NodeDistributions {
		podCounts = append(podCounts, dist.PodCount)
		if dist.PodCount > maxPods {
			maxPods = dist.PodCount
		}
		if dist.PodCount < minPods {
			minPods = dist.PodCount
		}
	}

	c.stats.MaxNodePods = maxPods
	c.stats.MinNodePods = minPods

	// 计算均匀性比率 (最小值/最大值)
	if maxPods > 0 {
		c.stats.UniformityRatio = float64(minPods) / float64(maxPods)
	}

	// 计算分布方差
	if len(podCounts) > 0 {
		c.stats.DistributionVariance = calculateVariance(podCounts)
	}

	logger.Infof(ctx, "分布均匀性 - 最大: %d, 最小: %d, 均匀性: %.2f, 方差: %.2f",
		maxPods, minPods, c.stats.UniformityRatio, c.stats.DistributionVariance)
}

// generateReport - 生成测试报告
func (c *tenNodes1000ContainersScaling) generateReport(ctx context.Context) {
	logger.Infof(ctx, "📋 生成测试报告...")
	logger.Infof(ctx, "")
	logger.Infof(ctx, "==================== 10节点1000容器扩容测试报告 ====================")
	logger.Infof(ctx, "")
	logger.Infof(ctx, "📊 基本信息:")
	logger.Infof(ctx, "  目标节点数: %d", c.config.RequiredNodes)
	logger.Infof(ctx, "  目标容器数: %d", c.config.TotalPods)
	logger.Infof(ctx, "  测试开始时间: %s", c.stats.TestStartTime.Format("2006-01-02 15:04:05"))
	logger.Infof(ctx, "  测试结束时间: %s", c.stats.TestEndTime.Format("2006-01-02 15:04:05"))
	logger.Infof(ctx, "  总耗时: %v", c.stats.TotalDuration)
	logger.Infof(ctx, "")
	logger.Infof(ctx, "🎯 扩容里程碑:")
	for _, milestone := range c.stats.Milestones {
		logger.Infof(ctx, "  %d个Pod就绪 (%.1f%%): 耗时%v, 速率%.2f pods/sec",
			milestone.TargetPods, milestone.Percentage, milestone.ElapsedTime, milestone.ScalingRate)
	}
	logger.Infof(ctx, "")
	logger.Infof(ctx, "📈 性能指标:")
	logger.Infof(ctx, "  成功Pod数: %d", c.stats.SuccessPods)
	logger.Infof(ctx, "  失败Pod数: %d", c.stats.FailedPods)
	logger.Infof(ctx, "  成功率: %.2f%%", c.stats.SuccessRate)
	logger.Infof(ctx, "  吞吐量: %.2f pods/sec", c.stats.ThroughputPPS)
	logger.Infof(ctx, "")
	logger.Infof(ctx, "🏗️ 节点分布:")
	for _, dist := range c.stats.NodeDistributions {
		logger.Infof(ctx, "  %s: %d个Pod (%.1f%%) [调度%d, 运行%d, 就绪%d, 失败%d]",
			dist.NodeName, dist.PodCount, dist.Percentage,
			dist.ScheduledPods, dist.RunningPods, dist.ReadyPods, dist.FailedPods)
	}
	logger.Infof(ctx, "")
	logger.Infof(ctx, "⚖️ 分布均匀性:")
	logger.Infof(ctx, "  最大节点Pod数: %d", c.stats.MaxNodePods)
	logger.Infof(ctx, "  最小节点Pod数: %d", c.stats.MinNodePods)
	logger.Infof(ctx, "  均匀性比率: %.2f", c.stats.UniformityRatio)
	logger.Infof(ctx, "  分布方差: %.2f", c.stats.DistributionVariance)
	logger.Infof(ctx, "")
	logger.Infof(ctx, "🎖️ 性能基准评估:")

	// 评估是否达到性能基准
	benchmarkPass := true

	// 总扩容时间 < 10分钟
	if c.stats.TotalDuration > 10*time.Minute {
		logger.Infof(ctx, "  ❌ 总扩容时间: %v (超过10分钟基准)", c.stats.TotalDuration)
		benchmarkPass = false
	} else {
		logger.Infof(ctx, "  ✅ 总扩容时间: %v (符合<10分钟基准)", c.stats.TotalDuration)
	}

	// 成功率 > 99%
	if c.stats.SuccessRate < 99.0 {
		logger.Infof(ctx, "  ❌ 成功率: %.2f%% (低于99%%基准)", c.stats.SuccessRate)
		benchmarkPass = false
	} else {
		logger.Infof(ctx, "  ✅ 成功率: %.2f%% (符合>99%%基准)", c.stats.SuccessRate)
	}

	// 分布均匀性: 各节点Pod数量差异 < 20%
	uniformityDiff := float64(c.stats.MaxNodePods-c.stats.MinNodePods) / float64(c.stats.MaxNodePods) * 100
	if uniformityDiff > 20.0 {
		logger.Infof(ctx, "  ❌ 分布均匀性: %.1f%% 差异 (超过20%%基准)", uniformityDiff)
		benchmarkPass = false
	} else {
		logger.Infof(ctx, "  ✅ 分布均匀性: %.1f%% 差异 (符合<20%%基准)", uniformityDiff)
	}

	logger.Infof(ctx, "")
	if benchmarkPass {
		logger.Infof(ctx, "🏆 基准测试结果: 全部通过")
	} else {
		logger.Infof(ctx, "⚠️ 基准测试结果: 部分未达标")
	}
	logger.Infof(ctx, "================================================================")
}

// cleanupDeployment - 清理Deployment
func (c *tenNodes1000ContainersScaling) cleanupDeployment(ctx context.Context) error {
	logger.Infof(ctx, "删除Deployment: %s", c.deploymentName)

	err := c.base.KubeClient.DeleteDeploymentAppsV1(ctx, c.namespace, c.deploymentName)
	if err != nil {
		return fmt.Errorf("删除Deployment失败: %v", err)
	}

	return nil
}

// waitForCleanup - 等待资源清理完成
func (c *tenNodes1000ContainersScaling) waitForCleanup(ctx context.Context) error {
	logger.Infof(ctx, "等待Pod清理完成...")

	timeout := time.Duration(c.config.Timeouts.CleanupTimeout) * time.Second

	return wait.PollImmediate(10*time.Second, timeout, func() (done bool, err error) {
		pods, err := c.base.KubeClient.ListPod(ctx, c.namespace, &kube.ListOptions{
			LabelSelector: map[string]string{"app": "ten-nodes-1000-containers-test"},
		})
		if err != nil {
			logger.Warnf(ctx, "检查Pod清理状态失败: %v", err)
			return false, nil
		}

		if len(pods.Items) == 0 {
			logger.Infof(ctx, "✅ 所有Pod已清理完成")
			return true, nil
		}

		logger.Infof(ctx, "还有%d个Pod待清理...", len(pods.Items))
		return false, nil
	})
}

// createNodesIfNeeded - 根据需要创建节点
func (c *tenNodes1000ContainersScaling) createNodesIfNeeded(ctx context.Context, neededNodes int) error {
	logger.Infof(ctx, "🚀 创建%d个节点...", neededNodes)

	// 获取集群中已有节点的信息，用于复用配置
	instances, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
		ClusterRole: types.ClusterRoleNode,
	}, nil)
	if err != nil {
		return fmt.Errorf("获取集群节点信息失败: %v", err)
	}
	if len(instances.InstancePage.InstanceList) == 0 {
		return fmt.Errorf("集群中没有可用的节点，无法获取配置信息")
	}

	// 使用第一个节点的配置信息
	instance := instances.InstancePage.InstanceList[0]

	// 获取镜像ID
	imageID := instance.Spec.ImageID
	if imageID == "" {
		return fmt.Errorf("获取到的镜像ID为空，无法创建节点组")
	}
	logger.Infof(ctx, "将使用已有节点的镜像ID: %s", imageID)

	// 获取集群额外信息，找到可用的子网
	clusterExtraInfo, err := c.base.CCEHostClient.GetClusterExtraInfo(ctx, c.base.ClusterID, nil)
	if err != nil {
		return fmt.Errorf("获取集群额外信息失败: %v", err)
	}

	var nodeSubnetID string
	for _, subnet := range clusterExtraInfo.Subnets {
		if subnet.SubnetCIDR != "" {
			nodeSubnetID = subnet.SubnetID
			break
		}
	}

	if nodeSubnetID == "" {
		return fmt.Errorf("未找到可用的子网")
	}

	logger.Infof(ctx, "将使用子网 %s 创建节点组", nodeSubnetID)

	// 查询集群信息
	cluster, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		return fmt.Errorf("获取集群信息失败: %v", err)
	}

	// 从集群信息获取VPCID
	vpcID := cluster.Cluster.Spec.VPCID
	logger.Infof(ctx, "将使用VPC %s 创建节点组", vpcID)

	// 创建实例组
	igName := fmt.Sprintf("test-ten-nodes-scaling-%d", time.Now().Unix())
	createReq := &ccev2.CreateInstanceGroupRequest{
		InstanceGroupSpec: types.InstanceGroupSpec{
			InstanceGroupName: igName,
			Replicas:          neededNodes, // 创建所需数量的节点
			InstanceTemplate: types.InstanceTemplate{
				InstanceSpec: types.InstanceSpec{
					ClusterRole:  types.ClusterRoleNode,
					MachineType:  types.MachineTypeBCC,
					InstanceType: instance.Spec.InstanceType,
					VPCConfig: types.VPCConfig{
						VPCID:       vpcID,
						VPCSubnetID: nodeSubnetID,
						SecurityGroup: types.SecurityGroup{
							EnableCCERequiredSecurityGroup: true,
						},
					},
					InstanceResource: types.InstanceResource{
						CPU:          c.config.NodeCreation.CPU,
						MEM:          c.config.NodeCreation.Memory,
						MachineSpec:  c.config.NodeCreation.MachineSpec,
						RootDiskType: "cloud_hp1",
						RootDiskSize: c.config.NodeCreation.DiskSize,
					},
					ImageID:       imageID,
					AdminPassword: c.config.NodeCreation.AdminPassword,
				},
			},
		},
	}

	// 发送创建请求
	createResp, err := c.base.CCEClient.CreateInstanceGroup(ctx, c.base.ClusterID, createReq, nil)
	if err != nil {
		return fmt.Errorf("创建实例组失败: %v", err)
	}

	c.createdInstanceGroupID = createResp.InstanceGroupID
	logger.Infof(ctx, "✅ 成功创建实例组 %s (ID: %s)", igName, c.createdInstanceGroupID)

	// 记录资源用于清理
	c.resources = append(c.resources, cases.Resource{
		CaseName: c.Name(),
		Type:     cases.ResourceTypeCCEInstanceGroup,
		ID:       c.createdInstanceGroupID,
	})

	return nil
}

// waitForNodesReady - 等待节点就绪
func (c *tenNodes1000ContainersScaling) waitForNodesReady(ctx context.Context) error {
	logger.Infof(ctx, "⏳ 等待节点就绪...")

	if c.createdInstanceGroupID == "" {
		return fmt.Errorf("实例组ID为空")
	}

	// 创建instanceGroup资源对象并等待就绪
	ig, err := resourcelogic.NewInstanceGroup(ctx, c.base, c.createdInstanceGroupID,
		30*time.Second, time.Duration(c.config.Timeouts.NodeReadyTimeout)*time.Second)
	if err != nil {
		return fmt.Errorf("创建instanceGroup资源对象失败: %v", err)
	}

	err = ig.CheckResource(ctx)
	if err != nil {
		return fmt.Errorf("等待实例组就绪失败: %v", err)
	}

	// 重新获取节点列表
	nodeList, err := c.base.KubeClient.ListNode(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("重新获取节点列表失败: %v", err)
	}

	readyNodes := 0
	var availableNodes []string

	for _, node := range nodeList.Items {
		// 检查节点状态
		isReady := false
		for _, condition := range node.Status.Conditions {
			if condition.Type == corev1.NodeNetworkUnavailable && condition.Status == corev1.ConditionFalse {
				isReady = true
				break
			}
		}

		if isReady && c.nodeMatchesSelector(&node) {
			readyNodes++
			availableNodes = append(availableNodes, node.Name)
		}
	}

	if readyNodes < c.config.RequiredNodes {
		return fmt.Errorf("节点创建后仍然不足: 需要%d个，实际%d个", c.config.RequiredNodes, readyNodes)
	}

	// 选择目标节点
	if len(availableNodes) >= c.config.RequiredNodes {
		c.targetNodes = availableNodes[:c.config.RequiredNodes]
	} else {
		c.targetNodes = availableNodes
	}

	logger.Infof(ctx, "✅ 节点创建完成，总共%d个节点就绪，选中节点: %v", readyNodes, c.targetNodes)
	return nil
}

// cleanupInstanceGroup - 清理实例组
func (c *tenNodes1000ContainersScaling) cleanupInstanceGroup(ctx context.Context) error {
	logger.Infof(ctx, "删除实例组: %s", c.createdInstanceGroupID)

	// 先将实例组缩容到0
	_, err := c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, c.base.ClusterID, c.createdInstanceGroupID,
		&ccev2.UpdateInstanceGroupReplicasRequest{
			Replicas: 0,
		}, nil)
	if err != nil {
		logger.Warnf(ctx, "缩容实例组失败: %v", err)
	} else {
		logger.Infof(ctx, "实例组已缩容到0，等待节点删除...")
		time.Sleep(30 * time.Second) // 等待节点删除
	}

	// 删除实例组
	_, err = c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.createdInstanceGroupID, true, nil)
	if err != nil {
		return fmt.Errorf("删除实例组失败: %v", err)
	}

	logger.Infof(ctx, "✅ 实例组已删除")
	return nil
}

// calculateVariance - 计算方差
func calculateVariance(values []int) float64 {
	if len(values) == 0 {
		return 0
	}

	// 计算平均值
	sum := 0
	for _, v := range values {
		sum += v
	}
	mean := float64(sum) / float64(len(values))

	// 计算方差
	sumSquaredDiffs := 0.0
	for _, v := range values {
		diff := float64(v) - mean
		sumSquaredDiffs += diff * diff
	}

	return sumSquaredDiffs / float64(len(values))
}
