// Copyright 2022 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2022/11/07 , by <PERSON><PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
check cross vpc eni status
*/

package network

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/serializer"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/network/eni-resource-controller/api/v1alpha1"
)

const (
	// CrossVPCENICaseName 跨VPC ENI状态检查
	CrossVPCENICaseName cases.CaseName = "CrossVPCENI"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CrossVPCENICaseName, NewCrossVPCENI)
}

var _ cases.Interface = &crossVPCENI{}

type crossVPCENI struct {
	base      *cases.BaseClient
	crossVpc  *common.CrossVpcEni
	k8sClient kubernetes.Interface
}

type metaConfig struct {
	ClusterID      string                `json:"ResClusterID"`
	KubeConfigType models.KubeConfigType `json:"KubeConfigType"`
}

// NewCrossVPCENI 跨vpc eni状态检查
func NewCrossVPCENI(ctx context.Context) cases.Interface {
	return &crossVPCENI{}
}

func (c *crossVPCENI) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	c.base = base
	var cfg metaConfig
	if err := json.Unmarshal(config, &cfg); err != nil {
		return fmt.Errorf("json.Unmarshal bos config failed: %s", err)
	}
	response, err := c.base.MetaClient.GetAdminKubeConfig(ctx, cfg.ClusterID, cfg.KubeConfigType, nil)
	if err != nil {
		logger.Errorf(ctx, "GetKubeConfig failed: %s", err)
		return err
	}

	restConfig, err := clientcmd.RESTConfigFromKubeConfig([]byte(response.KubeConfig))
	if err != nil {
		logger.Errorf(ctx, "RESTConfigFromKubeConfig failed: %v", err)
		return err
	}
	con := restConfig
	con.GroupVersion = &v1alpha1.GroupVersion
	con.APIPath = "/apis"
	con.NegotiatedSerializer = serializer.WithoutConversionCodecFactory{CodecFactory: scheme.Codecs}
	con.UserAgent = rest.DefaultKubernetesUserAgent()
	// 和 controller worker 数量一致
	con.QPS = 400
	con.Burst = 400
	restClient, err := rest.RESTClientFor(con)
	if err != nil {
		logger.Errorf(ctx, "failed create rest client: %v", err)
		return err
	}

	// 初始化 K8S Client
	restConfig.Timeout = 30 * time.Second
	restConfig.ContentType = "application/vnd.kubernetes.protobuf"

	clientset, err := kubernetes.NewForConfig(restConfig)
	if err != nil {
		logger.Errorf(ctx, "kubernetes.NewForConfig failed: %s", err)
		return err
	}

	c.k8sClient = clientset

	crossVpc, err := common.NewCrossVpcEni(ctx, restClient, "default")
	if err != nil {
		logger.Errorf(ctx, "new crossVpcEni failed:%v", err)
		return err
	}

	c.crossVpc = crossVpc

	return nil
}

func (c *crossVPCENI) Name() cases.CaseName {
	return CrossVPCENICaseName
}

func (c *crossVPCENI) Desc() string {
	return "[待补充]"
}

func (c *crossVPCENI) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)
	ns := c.base.ClusterID

	labelSelector := metav1.LabelSelector{
		MatchLabels: map[string]string{
			"app": "kube-apiserver",
		},
	}
	labelSelectorStr := metav1.FormatLabelSelector(&labelSelector)

	pods, err := c.k8sClient.CoreV1().Pods(ns).List(ctx, metav1.ListOptions{
		LabelSelector: labelSelectorStr,
	},
	)
	if err != nil {
		logger.Errorf(ctx, "get cross vpc pod in ns %s failed:%v", ns, err)
		return nil, err
	}

	if pods == nil || len(pods.Items) == 0 {
		return nil, fmt.Errorf("the apiserver pod in %s list is empty", ns)
	}

	for _, pod := range pods.Items {
		err := c.crossVpc.WaitCrossVpcEniInUsed(ctx, pod.Name)
		if err != nil {
			return nil, fmt.Errorf("wait crossVpcEni failed:%v", err)
		}
		_, id, _ := c.crossVpc.GetCrossVpcEniStatusByPodName(ctx, pod.Name)
		resp, err := c.base.ENIClient.StatENI(ctx, id, nil)
		if err != nil {
			logger.Errorf(ctx, "get eni status is failed:%v", err)
			return nil, err
		}
		if resp.Status != "inuse" {
			logger.Errorf(ctx, "the eni status is not inuse")
			return nil, errors.New("the eni status is not inuse")
		}
		logger.Infof(ctx, "the eni %s is inuse as expected", id)
	}
	return resources, nil
}

func (c *crossVPCENI) Clean(ctx context.Context) error {
	return nil
}

func (c *crossVPCENI) Continue(ctx context.Context) bool {
	return true
}

func (c *crossVPCENI) ConfigFormat() string {
	return ""
}
