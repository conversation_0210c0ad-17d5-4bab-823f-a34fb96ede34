/*
Copyright 2024 Baidu Inc.

本文件包含了针对terminating pod网络资源清理功能的自动化测试用例。
该测试主要验证在异常情况下，kubelet删除容器失败，未调用CNI，pod处于terminating状态，
用户--force强删pod后，节点上veth和路由能够正确清理。

用例主要验证以下内容：
1. 创建测试Deployment
2. 创建node-shell登录节点，查看当前创建pod相关的veth和路由
3. 模拟CNI调用异常，使pod卡在terminating状态
4. 强制删除pod，验证节点上veth和路由是否被正确清理

测试流程：
1. 使用指定镜像创建Deployment
2. 等待Pod就绪并获取Pod信息
3. 创建node-shell DaemonSet用于节点操作
4. 查看Pod相关的veth和路由信息
5. 模拟CNI异常，删除Pod使其进入terminating状态
6. 强制删除Pod
7. 验证veth和路由是否被清理
8. 清理测试资源

测试流程详见每个函数的具体实现。
*/

package network

import (
	"context"
	"fmt"
	"strings"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	// TerminatingPodCaseName - case 名字
	TerminatingPodCaseName cases.CaseName = "TerminatingPod"

	// 测试相关常量
	terminatingTestDeploymentName = "test-terminating-pod"
	terminatingTestNamespace      = "default"
	terminatingTestImage          = "registry.baidubce.com/csm-offline/dnstools:zzw-test"
	terminatingTestContainerName  = "dnstools"
	terminatingTestReplicas       = 1

	// node-shell相关常量
	nodeShellDaemonSetName = "node-shell-debug"
	nodeShellNamespace     = "default"
	nodeShellImage         = "registry.baidubce.com/test-stack/node-shell:dev"
	nodeShellContainerName = "shell"

	// 清理等待时间（5分钟）
	cleanupWaitTimeout = 5 * time.Minute
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), TerminatingPodCaseName, NewTerminatingPod)
}

// terminatingPod 结构体定义
type terminatingPod struct {
	base              *cases.BaseClient
	resources         []cases.Resource
	deploymentCreated bool
	nodeShellCreated  bool
	selectedPod       *corev1.Pod
	podIP             string
	nodeName          string
	vethInterface     string
	routeEntries      []string
	nodeShellPod      *corev1.Pod
}

// NewTerminatingPod - 测试案例构造函数
func NewTerminatingPod(ctx context.Context) cases.Interface {
	return &terminatingPod{}
}

// Init 初始化测试环境
func (c *terminatingPod) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base is nil")
	}

	c.base = base

	logger.Infof(ctx, "初始化terminating-pod测试，集群ID: %s", c.base.ClusterID)
	return nil
}

// Name 返回测试用例名称
func (c *terminatingPod) Name() cases.CaseName {
	return TerminatingPodCaseName
}

// Desc 返回测试用例描述
func (c *terminatingPod) Desc() string {
	return "测试在异常情况下，kubelet删除容器失败，未调用CNI，pod处于terminating状态，用户--force强删pod后，节点上veth和路由的清理功能"
}

// ConfigFormat 返回配置格式
func (c *terminatingPod) ConfigFormat() string {
	return ""
}

// Continue 返回是否继续执行
func (c *terminatingPod) Continue(ctx context.Context) bool {
	return true
}

// Check 执行测试
func (c *terminatingPod) Check(ctx context.Context) ([]cases.Resource, error) {
	var err error

	// 1. 创建测试Deployment
	logger.Infof(ctx, "第一步：创建测试Deployment...")
	if err = c.createTestDeployment(ctx); err != nil {
		return nil, fmt.Errorf("创建测试Deployment失败: %v", err)
	}

	// 2. 等待Pod就绪并获取Pod信息
	logger.Infof(ctx, "第二步：等待Pod就绪并获取Pod信息...")
	if err = c.waitForPodReady(ctx); err != nil {
		return nil, fmt.Errorf("等待Pod就绪失败: %v", err)
	}

	// 3. 创建node-shell DaemonSet
	logger.Infof(ctx, "第三步：创建node-shell DaemonSet...")
	if err = c.createNodeShell(ctx); err != nil {
		return nil, fmt.Errorf("创建node-shell失败: %v", err)
	}

	// 4. 等待node-shell就绪
	logger.Infof(ctx, "第四步：等待node-shell就绪...")
	if err = c.waitForNodeShellReady(ctx); err != nil {
		return nil, fmt.Errorf("等待node-shell就绪失败: %v", err)
	}

	// 5. 查看Pod相关的veth和路由信息
	logger.Infof(ctx, "第五步：查看Pod相关的veth和路由信息...")
	if err = c.collectPodNetworkInfo(ctx); err != nil {
		return nil, fmt.Errorf("收集Pod网络信息失败: %v", err)
	}

	// 6. 模拟CNI异常并删除Pod
	logger.Infof(ctx, "第六步：模拟CNI异常并删除Pod...")
	if err = c.simulateCNIFailureAndDeletePod(ctx); err != nil {
		return nil, fmt.Errorf("模拟CNI异常并删除Pod失败: %v", err)
	}

	// 7. 强制删除Pod
	logger.Infof(ctx, "第七步：强制删除Pod...")
	if err = c.forceDeletePod(ctx); err != nil {
		return nil, fmt.Errorf("强制删除Pod失败: %v", err)
	}

	// 8. 验证veth和路由清理
	logger.Infof(ctx, "第八步：验证veth和路由清理...")
	if err = c.verifyNetworkCleanup(ctx); err != nil {
		return nil, fmt.Errorf("验证网络清理失败: %v", err)
	}

	logger.Infof(ctx, "✅ terminating-pod测试完成")
	return c.resources, nil
}

// Clean 清理测试资源
func (c *terminatingPod) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理测试资源")
	var lastErr error

	// 清理Deployment
	if c.deploymentCreated {
		logger.Infof(ctx, "删除测试Deployment: %s", terminatingTestDeploymentName)
		err := c.base.KubeClient.ClientSet.AppsV1().Deployments(terminatingTestNamespace).Delete(ctx, terminatingTestDeploymentName, metav1.DeleteOptions{})
		if err != nil && !kerrors.IsNotFound(err) {
			logger.Warnf(ctx, "删除Deployment失败: %v", err)
			lastErr = err
		}

		// 等待Pod删除完成
		err = wait.Poll(5*time.Second, 2*time.Minute, func() (bool, error) {
			pods, err := c.base.KubeClient.ClientSet.CoreV1().Pods(terminatingTestNamespace).List(ctx, metav1.ListOptions{
				LabelSelector: "app=" + terminatingTestDeploymentName,
			})
			if err != nil {
				logger.Warnf(ctx, "获取Pod列表失败: %v", err)
				return false, nil
			}
			return len(pods.Items) == 0, nil
		})

		if err != nil {
			logger.Warnf(ctx, "等待Pod删除完成超时: %v", err)
			if lastErr == nil {
				lastErr = err
			}
		} else {
			logger.Infof(ctx, "Pod删除完成")
		}
	}

	// 清理node-shell DaemonSet
	if c.nodeShellCreated {
		logger.Infof(ctx, "删除node-shell DaemonSet: %s", nodeShellDaemonSetName)
		err := c.base.KubeClient.ClientSet.AppsV1().DaemonSets(nodeShellNamespace).Delete(ctx, nodeShellDaemonSetName, metav1.DeleteOptions{})
		if err != nil && !kerrors.IsNotFound(err) {
			logger.Warnf(ctx, "删除node-shell DaemonSet失败: %v", err)
			if lastErr == nil {
				lastErr = err
			}
		}

		// 等待node-shell Pod删除完成
		err = wait.Poll(5*time.Second, 2*time.Minute, func() (bool, error) {
			pods, err := c.base.KubeClient.ClientSet.CoreV1().Pods(nodeShellNamespace).List(ctx, metav1.ListOptions{
				LabelSelector: "name=node-shell-debug",
			})
			if err != nil {
				logger.Warnf(ctx, "获取node-shell Pod列表失败: %v", err)
				return false, nil
			}
			return len(pods.Items) == 0, nil
		})

		if err != nil {
			logger.Warnf(ctx, "等待node-shell Pod删除完成超时: %v", err)
			if lastErr == nil {
				lastErr = err
			}
		} else {
			logger.Infof(ctx, "node-shell Pod删除完成")
		}
	}

	c.deploymentCreated = false
	c.nodeShellCreated = false
	logger.Infof(ctx, "资源清理完成")
	return lastErr
}

// createTestDeployment 创建测试Deployment
func (c *terminatingPod) createTestDeployment(ctx context.Context) error {
	logger.Infof(ctx, "开始创建测试Deployment: %s", terminatingTestDeploymentName)

	// 先清理可能存在的同名资源
	err := c.base.KubeClient.ClientSet.AppsV1().Deployments(terminatingTestNamespace).Delete(ctx, terminatingTestDeploymentName, metav1.DeleteOptions{})
	if err != nil && !kerrors.IsNotFound(err) {
		logger.Warnf(ctx, "清理已存在的Deployment失败: %v", err)
	}

	// 等待删除完成
	time.Sleep(5 * time.Second)

	replicas := int32(terminatingTestReplicas)
	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      terminatingTestDeploymentName,
			Namespace: terminatingTestNamespace,
			Labels: map[string]string{
				"app":  terminatingTestDeploymentName,
				"test": "terminating-pod",
			},
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": terminatingTestDeploymentName,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app":  terminatingTestDeploymentName,
						"test": "terminating-pod",
					},
				},
				Spec: corev1.PodSpec{
					RestartPolicy: corev1.RestartPolicyAlways,
					Containers: []corev1.Container{
						{
							Name:  terminatingTestContainerName,
							Image: terminatingTestImage,
							Command: []string{
								"sleep",
								"3600", // 睡眠1小时，确保有足够时间进行测试
							},
							ImagePullPolicy: corev1.PullIfNotPresent,
						},
					},
				},
			},
		},
	}

	// 创建Deployment
	_, err = c.base.KubeClient.ClientSet.AppsV1().Deployments(terminatingTestNamespace).Create(ctx, deployment, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建Deployment失败: %v", err)
	}

	c.deploymentCreated = true
	logger.Infof(ctx, "测试Deployment %s 创建成功", terminatingTestDeploymentName)

	// 将创建的Deployment添加到资源列表
	c.resources = append(c.resources, cases.Resource{
		CaseName: TerminatingPodCaseName,
		Type:     "K8SDeployment",
		ID:       terminatingTestDeploymentName,
	})

	return nil
}

// waitForPodReady 等待Pod就绪并获取Pod信息
func (c *terminatingPod) waitForPodReady(ctx context.Context) error {
	logger.Infof(ctx, "等待Deployment %s 就绪", terminatingTestDeploymentName)

	// 等待Deployment就绪
	err := wait.PollImmediate(10*time.Second, 5*time.Minute, func() (bool, error) {
		deployment, err := c.base.KubeClient.ClientSet.AppsV1().Deployments(terminatingTestNamespace).Get(ctx, terminatingTestDeploymentName, metav1.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取Deployment状态失败: %v", err)
			return false, nil
		}

		logger.Infof(ctx, "Deployment状态: 期望副本数=%d, 可用副本数=%d, 就绪副本数=%d",
			*deployment.Spec.Replicas,
			deployment.Status.AvailableReplicas,
			deployment.Status.ReadyReplicas)

		// 检查是否达到期望状态
		if deployment.Status.ReadyReplicas == *deployment.Spec.Replicas {
			return true, nil
		}

		return false, nil
	})

	if err != nil {
		return fmt.Errorf("等待Deployment就绪超时: %v", err)
	}

	// 获取Pod列表并选择一个Pod进行测试
	pods, err := c.base.KubeClient.ClientSet.CoreV1().Pods(terminatingTestNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "app=" + terminatingTestDeploymentName,
	})
	if err != nil {
		return fmt.Errorf("获取Pod列表失败: %v", err)
	}

	if len(pods.Items) == 0 {
		return fmt.Errorf("未找到任何Pod")
	}

	// 选择第一个Running状态的Pod
	for _, pod := range pods.Items {
		if pod.Status.Phase == corev1.PodRunning {
			c.selectedPod = &pod
			c.podIP = pod.Status.PodIP
			c.nodeName = pod.Spec.NodeName
			logger.Infof(ctx, "选择Pod进行测试: %s (IP: %s, Node: %s)", pod.Name, c.podIP, c.nodeName)
			break
		}
	}

	if c.selectedPod == nil {
		return fmt.Errorf("未找到Running状态的Pod")
	}

	return nil
}

// createNodeShell 创建node-shell DaemonSet
func (c *terminatingPod) createNodeShell(ctx context.Context) error {
	logger.Infof(ctx, "检查node-shell DaemonSet是否存在")

	// 检查是否已存在node-shell DaemonSet
	_, err := c.base.KubeClient.ClientSet.AppsV1().DaemonSets(nodeShellNamespace).Get(ctx, nodeShellDaemonSetName, metav1.GetOptions{})
	if err == nil {
		logger.Infof(ctx, "node-shell DaemonSet已存在，无需创建")
		return nil
	}

	if !kerrors.IsNotFound(err) {
		return fmt.Errorf("检查node-shell DaemonSet时出错: %v", err)
	}

	// 创建node-shell DaemonSet
	logger.Infof(ctx, "创建node-shell DaemonSet")
	nodeShellDaemonSet := &appsv1.DaemonSet{
		ObjectMeta: metav1.ObjectMeta{
			Name:      nodeShellDaemonSetName,
			Namespace: nodeShellNamespace,
			Labels: map[string]string{
				"name": "node-shell-debug",
			},
		},
		Spec: appsv1.DaemonSetSpec{
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"name": "node-shell-debug",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"name": "node-shell-debug",
					},
				},
				Spec: corev1.PodSpec{
					HostNetwork: true,
					HostPID:     true,
					Containers: []corev1.Container{
						{
							Name:  nodeShellContainerName,
							Image: nodeShellImage,
							Command: []string{
								"sleep",
								"3600",
							},
							SecurityContext: &corev1.SecurityContext{
								Privileged: &[]bool{true}[0],
							},
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      "host-root",
									MountPath: "/host",
								},
							},
						},
					},
					Volumes: []corev1.Volume{
						{
							Name: "host-root",
							VolumeSource: corev1.VolumeSource{
								HostPath: &corev1.HostPathVolumeSource{
									Path: "/",
								},
							},
						},
					},
					Tolerations: []corev1.Toleration{
						{
							Operator: corev1.TolerationOpExists,
						},
					},
				},
			},
		},
	}

	_, err = c.base.KubeClient.ClientSet.AppsV1().DaemonSets(nodeShellNamespace).Create(ctx, nodeShellDaemonSet, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建node-shell DaemonSet失败: %v", err)
	}

	c.nodeShellCreated = true
	logger.Infof(ctx, "node-shell DaemonSet创建成功")

	// 将创建的DaemonSet添加到资源列表
	c.resources = append(c.resources, cases.Resource{
		CaseName: TerminatingPodCaseName,
		Type:     "K8SDaemonSet",
		ID:       nodeShellDaemonSetName,
	})

	return nil
}

// waitForNodeShellReady 等待node-shell DaemonSet就绪
func (c *terminatingPod) waitForNodeShellReady(ctx context.Context) error {
	logger.Infof(ctx, "等待node-shell DaemonSet就绪")

	err := wait.Poll(5*time.Second, 3*time.Minute, func() (bool, error) {
		daemonSet, err := c.base.KubeClient.ClientSet.AppsV1().DaemonSets(nodeShellNamespace).Get(ctx, nodeShellDaemonSetName, metav1.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取DaemonSet状态失败: %v", err)
			return false, nil
		}

		if daemonSet.Status.NumberReady > 0 {
			logger.Infof(ctx, "node-shell DaemonSet已就绪，就绪Pod数: %d", daemonSet.Status.NumberReady)
			return true, nil
		}

		logger.Infof(ctx, "node-shell DaemonSet尚未就绪，就绪Pod数: %d", daemonSet.Status.NumberReady)
		return false, nil
	})

	if err != nil {
		return fmt.Errorf("等待node-shell DaemonSet就绪超时: %v", err)
	}

	// 获取指定节点上的node-shell Pod
	nodeShellPod, err := c.getNodeShellPod(ctx, c.nodeName)
	if err != nil {
		return fmt.Errorf("获取node-shell Pod失败: %v", err)
	}

	c.nodeShellPod = nodeShellPod
	logger.Infof(ctx, "获取到节点 %s 上的node-shell Pod: %s", c.nodeName, nodeShellPod.Name)

	return nil
}

// getNodeShellPod 获取指定节点上的node-shell Pod
func (c *terminatingPod) getNodeShellPod(ctx context.Context, nodeName string) (*corev1.Pod, error) {
	pods, err := c.base.KubeClient.ClientSet.CoreV1().Pods(nodeShellNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "name=node-shell-debug",
	})
	if err != nil {
		return nil, fmt.Errorf("获取node-shell Pod列表失败: %v", err)
	}

	for _, pod := range pods.Items {
		if pod.Spec.NodeName == nodeName && pod.Status.Phase == corev1.PodRunning {
			return &pod, nil
		}
	}

	return nil, fmt.Errorf("未找到节点 %s 上运行的node-shell Pod", nodeName)
}

// collectPodNetworkInfo 收集Pod相关的网络信息
func (c *terminatingPod) collectPodNetworkInfo(ctx context.Context) error {
	logger.Infof(ctx, "开始收集Pod %s 的网络信息", c.selectedPod.Name)

	// 1. 查找Pod对应的veth接口
	if err := c.findPodVethInterface(ctx); err != nil {
		return fmt.Errorf("查找Pod veth接口失败: %v", err)
	}

	// 2. 收集相关的路由信息
	if err := c.collectRouteInfo(ctx); err != nil {
		return fmt.Errorf("收集路由信息失败: %v", err)
	}

	logger.Infof(ctx, "✅ Pod网络信息收集完成")
	logger.Infof(ctx, "Pod IP: %s", c.podIP)
	logger.Infof(ctx, "Veth接口: %s", c.vethInterface)
	logger.Infof(ctx, "相关路由条目数: %d", len(c.routeEntries))

	return nil
}

// findPodVethInterface 查找Pod对应的veth接口
func (c *terminatingPod) findPodVethInterface(ctx context.Context) error {
	logger.Infof(ctx, "查找Pod %s (IP: %s) 对应的veth接口", c.selectedPod.Name, c.podIP)

	// 在node-shell中执行命令查找veth接口
	// 通过ARP表查找Pod IP对应的MAC地址和接口
	cmd := []string{
		"sh", "-c",
		fmt.Sprintf("nsenter -t 1 -n ip neigh show %s", c.podIP),
	}

	stdout, err := c.base.KubeClient.RemoteExec(c.nodeShellPod.Namespace, c.nodeShellPod.Name, nodeShellContainerName, cmd)
	if err != nil {
		return fmt.Errorf("执行ip neigh命令失败: %v", err)
	}

	logger.Infof(ctx, "ARP表查询结果: %s", strings.TrimSpace(stdout))

	// 解析ARP表输出，提取接口名称
	// 输出格式类似: ********** dev veth12345678 lladdr 02:42:ac:11:00:02 REACHABLE
	fields := strings.Fields(strings.TrimSpace(stdout))
	for i, field := range fields {
		if field == "dev" && i+1 < len(fields) {
			c.vethInterface = fields[i+1]
			logger.Infof(ctx, "✅ 找到Pod对应的veth接口: %s", c.vethInterface)
			break
		}
	}

	if c.vethInterface == "" {
		return fmt.Errorf("未找到Pod对应的veth接口")
	}

	// 验证接口是否存在
	verifyCmd := []string{
		"sh", "-c",
		fmt.Sprintf("nsenter -t 1 -n ip link show %s", c.vethInterface),
	}

	verifyOut, err := c.base.KubeClient.RemoteExec(c.nodeShellPod.Namespace, c.nodeShellPod.Name, nodeShellContainerName, verifyCmd)
	if err != nil {
		return fmt.Errorf("验证veth接口失败: %v", err)
	}

	logger.Infof(ctx, "Veth接口详情: %s", strings.TrimSpace(verifyOut))
	return nil
}

// collectRouteInfo 收集相关的路由信息
func (c *terminatingPod) collectRouteInfo(ctx context.Context) error {
	logger.Infof(ctx, "收集与Pod %s 相关的路由信息", c.podIP)

	// 查找指向Pod IP的路由
	cmd := []string{
		"sh", "-c",
		fmt.Sprintf("nsenter -t 1 -n ip route show | grep %s", c.podIP),
	}

	stdout, err := c.base.KubeClient.RemoteExec(c.nodeShellPod.Namespace, c.nodeShellPod.Name, nodeShellContainerName, cmd)
	if err != nil {
		logger.Warnf(ctx, "查找Pod路由失败: %v", err)
		// 不返回错误，因为可能没有直接的路由条目
	}

	if strings.TrimSpace(stdout) != "" {
		c.routeEntries = strings.Split(strings.TrimSpace(stdout), "\n")
		logger.Infof(ctx, "找到 %d 条与Pod相关的路由:", len(c.routeEntries))
		for i, route := range c.routeEntries {
			logger.Infof(ctx, "路由 %d: %s", i+1, route)
		}
	} else {
		logger.Infof(ctx, "未找到直接指向Pod IP的路由条目")
		c.routeEntries = []string{}
	}

	// 同时收集所有路由信息作为参考
	allRoutesCmd := []string{
		"sh", "-c",
		"nsenter -t 1 -n ip route show",
	}

	allRoutesOut, err := c.base.KubeClient.RemoteExec(c.nodeShellPod.Namespace, c.nodeShellPod.Name, nodeShellContainerName, allRoutesCmd)
	if err != nil {
		logger.Warnf(ctx, "获取所有路由信息失败: %v", err)
	} else {
		logger.Infof(ctx, "节点所有路由信息:")
		logger.Infof(ctx, "%s", allRoutesOut)
	}

	return nil
}

// simulateCNIFailureAndDeletePod 模拟CNI异常并删除Pod
func (c *terminatingPod) simulateCNIFailureAndDeletePod(ctx context.Context) error {
	logger.Infof(ctx, "模拟CNI异常并删除Pod %s", c.selectedPod.Name)

	// 在实际环境中，我们通过删除Pod来模拟CNI调用失败的情况
	// 这里我们直接删除Pod，让它进入terminating状态
	logger.Infof(ctx, "删除Pod %s，使其进入terminating状态", c.selectedPod.Name)

	err := c.base.KubeClient.ClientSet.CoreV1().Pods(c.selectedPod.Namespace).Delete(ctx, c.selectedPod.Name, metav1.DeleteOptions{})
	if err != nil {
		return fmt.Errorf("删除Pod失败: %v", err)
	}

	// 等待Pod进入terminating状态
	logger.Infof(ctx, "等待Pod进入terminating状态...")
	err = wait.Poll(5*time.Second, 2*time.Minute, func() (bool, error) {
		pod, err := c.base.KubeClient.ClientSet.CoreV1().Pods(c.selectedPod.Namespace).Get(ctx, c.selectedPod.Name, metav1.GetOptions{})
		if err != nil {
			if kerrors.IsNotFound(err) {
				logger.Infof(ctx, "Pod已被删除")
				return false, fmt.Errorf("Pod已被删除，未进入terminating状态")
			}
			logger.Warnf(ctx, "获取Pod状态失败: %v", err)
			return false, nil
		}

		if pod.DeletionTimestamp != nil {
			logger.Infof(ctx, "✅ Pod已进入terminating状态")
			return true, nil
		}

		logger.Infof(ctx, "Pod状态: %s，等待进入terminating状态", pod.Status.Phase)
		return false, nil
	})

	if err != nil {
		return fmt.Errorf("等待Pod进入terminating状态失败: %v", err)
	}

	logger.Infof(ctx, "✅ Pod成功进入terminating状态")
	return nil
}

// forceDeletePod 强制删除Pod
func (c *terminatingPod) forceDeletePod(ctx context.Context) error {
	logger.Infof(ctx, "强制删除Pod %s", c.selectedPod.Name)

	// 使用强制删除选项
	gracePeriodSeconds := int64(0)
	deleteOptions := metav1.DeleteOptions{
		GracePeriodSeconds: &gracePeriodSeconds,
	}

	err := c.base.KubeClient.ClientSet.CoreV1().Pods(c.selectedPod.Namespace).Delete(ctx, c.selectedPod.Name, deleteOptions)
	if err != nil && !kerrors.IsNotFound(err) {
		return fmt.Errorf("强制删除Pod失败: %v", err)
	}

	// 等待Pod完全删除
	logger.Infof(ctx, "等待Pod完全删除...")
	err = wait.Poll(5*time.Second, 1*time.Minute, func() (bool, error) {
		_, err := c.base.KubeClient.ClientSet.CoreV1().Pods(c.selectedPod.Namespace).Get(ctx, c.selectedPod.Name, metav1.GetOptions{})
		if kerrors.IsNotFound(err) {
			logger.Infof(ctx, "✅ Pod已完全删除")
			return true, nil
		}
		if err != nil {
			logger.Warnf(ctx, "检查Pod状态失败: %v", err)
			return false, nil
		}
		logger.Infof(ctx, "Pod仍然存在，继续等待删除...")
		return false, nil
	})

	if err != nil {
		return fmt.Errorf("等待Pod删除完成超时: %v", err)
	}

	logger.Infof(ctx, "✅ Pod强制删除完成")
	return nil
}

// verifyNetworkCleanup 验证网络资源清理
func (c *terminatingPod) verifyNetworkCleanup(ctx context.Context) error {
	logger.Infof(ctx, "开始验证网络资源清理，等待时间: %v", cleanupWaitTimeout)

	// 等待一段时间让网络清理机制生效
	logger.Infof(ctx, "等待网络清理机制生效...")
	time.Sleep(30 * time.Second)

	// 验证veth接口是否被清理
	if err := c.verifyVethCleanup(ctx); err != nil {
		return fmt.Errorf("veth接口清理验证失败: %v", err)
	}

	// 验证路由是否被清理
	if err := c.verifyRouteCleanup(ctx); err != nil {
		return fmt.Errorf("路由清理验证失败: %v", err)
	}

	logger.Infof(ctx, "✅ 网络资源清理验证完成")
	return nil
}

// verifyVethCleanup 验证veth接口清理
func (c *terminatingPod) verifyVethCleanup(ctx context.Context) error {
	logger.Infof(ctx, "验证veth接口 %s 是否被清理", c.vethInterface)

	// 在指定的等待时间内检查veth接口是否被清理
	startTime := time.Now()
	for time.Since(startTime) < cleanupWaitTimeout {
		// 检查veth接口是否还存在
		cmd := []string{
			"sh", "-c",
			fmt.Sprintf("nsenter -t 1 -n ip link show %s 2>/dev/null || echo 'NOT_FOUND'", c.vethInterface),
		}

		stdout, err := c.base.KubeClient.RemoteExec(c.nodeShellPod.Namespace, c.nodeShellPod.Name, nodeShellContainerName, cmd)
		if err != nil {
			logger.Warnf(ctx, "检查veth接口失败: %v", err)
			time.Sleep(10 * time.Second)
			continue
		}

		output := strings.TrimSpace(stdout)
		if strings.Contains(output, "NOT_FOUND") || output == "" {
			logger.Infof(ctx, "✅ veth接口 %s 已被成功清理", c.vethInterface)
			return nil
		}

		logger.Infof(ctx, "veth接口 %s 仍然存在，继续等待清理...", c.vethInterface)
		logger.Infof(ctx, "接口信息: %s", output)
		time.Sleep(10 * time.Second)
	}

	// 超时后再次检查
	cmd := []string{
		"sh", "-c",
		fmt.Sprintf("nsenter -t 1 -n ip link show %s 2>/dev/null || echo 'NOT_FOUND'", c.vethInterface),
	}

	stdout, err := c.base.KubeClient.RemoteExec(c.nodeShellPod.Namespace, c.nodeShellPod.Name, nodeShellContainerName, cmd)
	if err != nil {
		return fmt.Errorf("最终检查veth接口失败: %v", err)
	}

	output := strings.TrimSpace(stdout)
	if strings.Contains(output, "NOT_FOUND") || output == "" {
		logger.Infof(ctx, "✅ veth接口 %s 已被成功清理", c.vethInterface)
		return nil
	}

	return fmt.Errorf("❌ veth接口 %s 在 %v 后仍未被清理，接口信息: %s", c.vethInterface, cleanupWaitTimeout, output)
}

// verifyRouteCleanup 验证路由清理
func (c *terminatingPod) verifyRouteCleanup(ctx context.Context) error {
	logger.Infof(ctx, "验证与Pod IP %s 相关的路由是否被清理", c.podIP)

	// 在指定的等待时间内检查路由是否被清理
	startTime := time.Now()
	for time.Since(startTime) < cleanupWaitTimeout {
		// 检查是否还有指向Pod IP的路由
		cmd := []string{
			"sh", "-c",
			fmt.Sprintf("nsenter -t 1 -n ip route show | grep %s || echo 'NO_ROUTES'", c.podIP),
		}

		stdout, err := c.base.KubeClient.RemoteExec(c.nodeShellPod.Namespace, c.nodeShellPod.Name, nodeShellContainerName, cmd)
		if err != nil {
			logger.Warnf(ctx, "检查路由失败: %v", err)
			time.Sleep(10 * time.Second)
			continue
		}

		output := strings.TrimSpace(stdout)
		if strings.Contains(output, "NO_ROUTES") || output == "" {
			logger.Infof(ctx, "✅ 与Pod IP %s 相关的路由已被成功清理", c.podIP)
			return nil
		}

		logger.Infof(ctx, "仍然存在与Pod IP %s 相关的路由，继续等待清理...", c.podIP)
		logger.Infof(ctx, "路由信息: %s", output)
		time.Sleep(10 * time.Second)
	}

	// 超时后再次检查
	cmd := []string{
		"sh", "-c",
		fmt.Sprintf("nsenter -t 1 -n ip route show | grep %s || echo 'NO_ROUTES'", c.podIP),
	}

	stdout, err := c.base.KubeClient.RemoteExec(c.nodeShellPod.Namespace, c.nodeShellPod.Name, nodeShellContainerName, cmd)
	if err != nil {
		return fmt.Errorf("最终检查路由失败: %v", err)
	}

	output := strings.TrimSpace(stdout)
	if strings.Contains(output, "NO_ROUTES") || output == "" {
		logger.Infof(ctx, "✅ 与Pod IP %s 相关的路由已被成功清理", c.podIP)
		return nil
	}

	return fmt.Errorf("❌ 与Pod IP %s 相关的路由在 %v 后仍未被清理，路由信息: %s", c.podIP, cleanupWaitTimeout, output)
}
