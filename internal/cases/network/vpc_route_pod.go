/*
Copyright 2024 Baidu Inc.

本文件包含了针对vpc-route模式集群中Pod网络配置和公网访问功能的自动化测试用例。
该测试主要验证在vpc-route模式下，Pod扩容时新创建的Pod的网络配置是否符合预期。

用例主要验证以下内容：
1. 创建Deployment并进行扩缩容
2. 验证Pod内部的路由、IP、MAC、ARP等网络配置
3. 验证集群中对应的CEP（CCEEndpoint）资源
4. 验证Pod的公网访问能力

测试流程：
1. 使用指定镜像创建Deployment
2. 扩容Deployment并等待Pod就绪
3. 进入Pod验证内部网络配置
4. 验证集群中的CEP资源
5. 测试Pod的公网访问能力
6. 清理测试资源

测试流程详见每个函数的具体实现。
*/

package network

import (
	"context"
	"fmt"
	"net"
	"strings"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	// VpcRoutePodCaseName - case 名字
	VpcRoutePodCaseName cases.CaseName = "VpcRoutePod"

	// 测试相关常量
	vpcRouteTestDeploymentName = "test-vpc-route-pod"
	vpcRouteTestNamespace      = "default"
	vpcRouteTestImage          = "registry.baidubce.com/csm-offline/dnstools:zzw-test"
	vpcRouteTestContainerName  = "dnstools"
	vpcRouteTestReplicas       = 2
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), VpcRoutePodCaseName, NewVpcRoutePod)
}

// vpcRoutePod 结构体定义
type vpcRoutePod struct {
	base              *cases.BaseClient
	resources         []cases.Resource
	deploymentCreated bool
	selectedPod       *corev1.Pod
	podIP             string
	podMAC            string
	podGateway        string
	podGatewayMAC     string
	podRoutes         []string
	podARPEntries     []string
	cepName           string
}

// NewVpcRoutePod - 测试案例构造函数
func NewVpcRoutePod(ctx context.Context) cases.Interface {
	return &vpcRoutePod{}
}

// Init 初始化测试环境
func (c *vpcRoutePod) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base is nil")
	}

	c.base = base

	logger.Infof(ctx, "初始化vpc-route-pod测试，集群ID: %s", c.base.ClusterID)
	return nil
}

// Name 返回测试用例名称
func (c *vpcRoutePod) Name() cases.CaseName {
	return VpcRoutePodCaseName
}

// Desc 返回测试用例描述
func (c *vpcRoutePod) Desc() string {
	return "测试vpc-route模式集群中Pod的网络配置和公网访问功能，验证容器内的路由、IP、MAC、ARP等配置以及CEP资源"
}

// Continue 返回是否继续测试
func (c *vpcRoutePod) Continue(ctx context.Context) bool {
	return true
}

// ConfigFormat 返回配置格式
func (c *vpcRoutePod) ConfigFormat() string {
	return ""
}

// Check 执行测试
func (c *vpcRoutePod) Check(ctx context.Context) ([]cases.Resource, error) {
	logger.Infof(ctx, "开始执行vpc-route模式Pod网络配置和公网访问测试")

	// 0. 检查集群是否有节点
	logger.Infof(ctx, "第零步：检查集群是否有可用节点...")
	if err := c.checkClusterNodes(ctx); err != nil {
		return c.resources, fmt.Errorf("集群节点检查失败: %v", err)
	}

	// 1. 清理可能存在的测试资源
	logger.Infof(ctx, "第一步：清理可能存在的测试资源...")
	if err := c.cleanupTestResources(ctx); err != nil {
		logger.Warnf(ctx, "清理测试资源失败: %v", err)
	}

	// 2. 创建测试Deployment
	logger.Infof(ctx, "第二步：创建测试Deployment...")
	if err := c.createTestDeployment(ctx); err != nil {
		return c.resources, fmt.Errorf("创建测试Deployment失败: %v", err)
	}

	// 3. 等待Pod就绪并选择测试Pod
	logger.Infof(ctx, "第三步：等待Pod就绪并选择测试Pod...")
	if err := c.waitForPodReady(ctx); err != nil {
		return c.resources, fmt.Errorf("等待Pod就绪失败: %v", err)
	}

	// 4. 验证Pod内部网络配置
	logger.Infof(ctx, "第四步：验证Pod内部网络配置...")
	if err := c.verifyPodNetworkConfig(ctx); err != nil {
		return c.resources, fmt.Errorf("验证Pod内部网络配置失败: %v", err)
	}

	// 5. 验证集群中的CEP资源
	logger.Infof(ctx, "第五步：验证集群中的CEP资源...")
	if err := c.verifyCEPResource(ctx); err != nil {
		return c.resources, fmt.Errorf("验证CEP资源失败: %v", err)
	}

	// 6. 测试Pod的公网访问能力
	logger.Infof(ctx, "第六步：测试Pod的公网访问能力...")
	if err := c.testPublicNetworkAccess(ctx); err != nil {
		return c.resources, fmt.Errorf("测试公网访问失败: %v", err)
	}

	// 7. 进行扩缩容测试
	logger.Infof(ctx, "第七步：进行扩缩容测试...")
	if err := c.testScaling(ctx); err != nil {
		return c.resources, fmt.Errorf("扩缩容测试失败: %v", err)
	}

	logger.Infof(ctx, "✅ vpc-route模式Pod网络配置和公网访问测试完成！")
	return c.resources, nil
}

// Clean 清理资源
func (c *vpcRoutePod) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理测试资源")
	return c.cleanupTestResources(ctx)
}

// cleanupTestResources 清理测试资源
func (c *vpcRoutePod) cleanupTestResources(ctx context.Context) error {
	var lastErr error

	// 删除Deployment
	err := c.base.KubeClient.ClientSet.AppsV1().Deployments(vpcRouteTestNamespace).Delete(ctx, vpcRouteTestDeploymentName, metav1.DeleteOptions{})
	if err != nil && !kerrors.IsNotFound(err) {
		logger.Warnf(ctx, "删除Deployment失败: %v", err)
		if lastErr == nil {
			lastErr = err
		}
	} else if err == nil {
		logger.Infof(ctx, "成功删除Deployment %s", vpcRouteTestDeploymentName)
	}

	// 等待Pod删除完成
	err = wait.PollImmediate(5*time.Second, 2*time.Minute, func() (bool, error) {
		pods, err := c.base.KubeClient.ClientSet.CoreV1().Pods(vpcRouteTestNamespace).List(ctx, metav1.ListOptions{
			LabelSelector: "app=" + vpcRouteTestDeploymentName,
		})
		if err != nil {
			return false, nil
		}
		return len(pods.Items) == 0, nil
	})

	if err != nil {
		logger.Warnf(ctx, "等待Pod删除完成超时: %v", err)
		if lastErr == nil {
			lastErr = err
		}
	} else {
		logger.Infof(ctx, "Pod删除完成")
	}

	c.deploymentCreated = false
	logger.Infof(ctx, "资源清理完成")
	return lastErr
}

// createTestDeployment 创建测试Deployment
func (c *vpcRoutePod) createTestDeployment(ctx context.Context) error {
	logger.Infof(ctx, "开始创建测试Deployment: %s", vpcRouteTestDeploymentName)

	replicas := int32(vpcRouteTestReplicas)
	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      vpcRouteTestDeploymentName,
			Namespace: vpcRouteTestNamespace,
			Labels: map[string]string{
				"app":  vpcRouteTestDeploymentName,
				"test": "vpc-route-pod",
			},
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": vpcRouteTestDeploymentName,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app":  vpcRouteTestDeploymentName,
						"test": "vpc-route-pod",
					},
				},
				Spec: corev1.PodSpec{
					RestartPolicy: corev1.RestartPolicyAlways,
					Containers: []corev1.Container{
						{
							Name:  vpcRouteTestContainerName,
							Image: vpcRouteTestImage,
							Command: []string{
								"sleep",
								"3600", // 睡眠1小时，确保有足够时间进行测试
							},
							ImagePullPolicy: corev1.PullIfNotPresent,
						},
					},
				},
			},
		},
	}

	// 创建Deployment
	_, err := c.base.KubeClient.ClientSet.AppsV1().Deployments(vpcRouteTestNamespace).Create(ctx, deployment, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建Deployment失败: %v", err)
	}

	c.deploymentCreated = true
	logger.Infof(ctx, "测试Deployment %s 创建成功", vpcRouteTestDeploymentName)

	// 将创建的Deployment添加到资源列表
	c.resources = append(c.resources, cases.Resource{
		CaseName: VpcRoutePodCaseName,
		Type:     "K8SDeployment",
		ID:       vpcRouteTestDeploymentName,
	})

	return nil
}

// waitForPodReady 等待Pod就绪并选择测试Pod
func (c *vpcRoutePod) waitForPodReady(ctx context.Context) error {
	logger.Infof(ctx, "等待Deployment %s 就绪", vpcRouteTestDeploymentName)

	// 等待Deployment就绪
	err := wait.PollImmediate(10*time.Second, 5*time.Minute, func() (bool, error) {
		deployment, err := c.base.KubeClient.ClientSet.AppsV1().Deployments(vpcRouteTestNamespace).Get(ctx, vpcRouteTestDeploymentName, metav1.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取Deployment状态失败: %v", err)
			return false, nil
		}

		logger.Infof(ctx, "Deployment状态: 期望副本数=%d, 可用副本数=%d, 就绪副本数=%d",
			*deployment.Spec.Replicas,
			deployment.Status.AvailableReplicas,
			deployment.Status.ReadyReplicas)

		// 检查是否达到期望状态
		if deployment.Status.ReadyReplicas == *deployment.Spec.Replicas {
			return true, nil
		}

		return false, nil
	})

	if err != nil {
		return fmt.Errorf("等待Deployment就绪超时: %v", err)
	}

	// 获取Pod列表并选择一个Pod进行测试
	pods, err := c.base.KubeClient.ClientSet.CoreV1().Pods(vpcRouteTestNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "app=" + vpcRouteTestDeploymentName,
	})
	if err != nil {
		return fmt.Errorf("获取Pod列表失败: %v", err)
	}

	if len(pods.Items) == 0 {
		return fmt.Errorf("未找到任何Pod")
	}

	// 选择第一个Running状态的Pod
	for _, pod := range pods.Items {
		if pod.Status.Phase == corev1.PodRunning {
			c.selectedPod = &pod
			c.podIP = pod.Status.PodIP
			logger.Infof(ctx, "选择Pod进行测试: %s (IP: %s)", pod.Name, c.podIP)
			break
		}
	}

	if c.selectedPod == nil {
		return fmt.Errorf("未找到Running状态的Pod")
	}

	return nil
}

// verifyPodNetworkConfig 验证Pod内部网络配置
func (c *vpcRoutePod) verifyPodNetworkConfig(ctx context.Context) error {
	logger.Infof(ctx, "开始验证Pod %s 的网络配置", c.selectedPod.Name)

	// 1. 获取Pod的IP地址信息
	if err := c.getPodIPInfo(ctx); err != nil {
		return fmt.Errorf("获取Pod IP信息失败: %v", err)
	}

	// 2. 获取Pod的MAC地址信息
	if err := c.getPodMACInfo(ctx); err != nil {
		return fmt.Errorf("获取Pod MAC信息失败: %v", err)
	}

	// 3. 获取Pod的路由信息
	if err := c.getPodRouteInfo(ctx); err != nil {
		return fmt.Errorf("获取Pod路由信息失败: %v", err)
	}

	// 4. 获取Pod的ARP信息
	if err := c.getPodARPInfo(ctx); err != nil {
		return fmt.Errorf("获取Pod ARP信息失败: %v", err)
	}

	// 5. 验证网络配置的合理性
	if err := c.validateNetworkConfig(ctx); err != nil {
		return fmt.Errorf("验证网络配置失败: %v", err)
	}

	logger.Infof(ctx, "✅ Pod网络配置验证完成")
	return nil
}

// getPodIPInfo 获取Pod的IP地址信息
func (c *vpcRoutePod) getPodIPInfo(ctx context.Context) error {
	logger.Infof(ctx, "获取Pod IP地址信息")

	// 执行ip addr命令
	cmd := []string{"ip", "addr", "show"}
	stdout, err := c.base.KubeClient.RemoteExec(vpcRouteTestNamespace, c.selectedPod.Name, vpcRouteTestContainerName, cmd)
	if err != nil {
		return fmt.Errorf("执行ip addr命令失败: %v", err)
	}

	logger.Infof(ctx, "Pod IP地址信息:")
	logger.Infof(ctx, "%s", stdout)

	// 验证Pod IP是否在输出中
	if !strings.Contains(stdout, c.podIP) {
		return fmt.Errorf("Pod IP %s 未在ip addr输出中找到", c.podIP)
	}

	logger.Infof(ctx, "✅ Pod IP地址 %s 验证成功", c.podIP)
	return nil
}

// getPodMACInfo 获取Pod的MAC地址信息
func (c *vpcRoutePod) getPodMACInfo(ctx context.Context) error {
	logger.Infof(ctx, "获取Pod MAC地址信息")

	// 执行ip link命令
	cmd := []string{"ip", "link", "show"}
	stdout, err := c.base.KubeClient.RemoteExec(vpcRouteTestNamespace, c.selectedPod.Name, vpcRouteTestContainerName, cmd)
	if err != nil {
		return fmt.Errorf("执行ip link命令失败: %v", err)
	}

	logger.Infof(ctx, "Pod MAC地址信息:")
	logger.Infof(ctx, "%s", stdout)

	// 从输出中提取eth0的MAC地址
	lines := strings.Split(stdout, "\n")
	for i, line := range lines {
		if strings.Contains(line, "eth0") && i+1 < len(lines) {
			nextLine := lines[i+1]
			if strings.Contains(nextLine, "link/ether") {
				parts := strings.Fields(nextLine)
				if len(parts) >= 2 {
					c.podMAC = parts[1]
					logger.Infof(ctx, "✅ 提取到Pod MAC地址: %s", c.podMAC)
					break
				}
			}
		}
	}

	if c.podMAC == "" {
		logger.Warnf(ctx, "⚠️ 未能提取到Pod MAC地址")
	}

	return nil
}

// getPodRouteInfo 获取Pod的路由信息
func (c *vpcRoutePod) getPodRouteInfo(ctx context.Context) error {
	logger.Infof(ctx, "获取Pod路由信息")

	// 执行ip route命令
	cmd := []string{"ip", "route", "show"}
	stdout, err := c.base.KubeClient.RemoteExec(vpcRouteTestNamespace, c.selectedPod.Name, vpcRouteTestContainerName, cmd)
	if err != nil {
		return fmt.Errorf("执行ip route命令失败: %v", err)
	}

	logger.Infof(ctx, "Pod路由信息:")
	logger.Infof(ctx, "%s", stdout)

	// 解析路由信息
	c.podRoutes = strings.Split(strings.TrimSpace(stdout), "\n")

	// 提取默认网关信息
	for _, route := range c.podRoutes {
		if strings.HasPrefix(route, "default via") {
			parts := strings.Fields(route)
			if len(parts) >= 3 {
				c.podGateway = parts[2]
				logger.Infof(ctx, "✅ 提取到默认网关: %s", c.podGateway)
				break
			}
		}
	}

	if c.podGateway == "" {
		logger.Warnf(ctx, "⚠️ 未能提取到默认网关信息")
	}

	return nil
}

// getPodARPInfo 获取Pod的ARP信息
func (c *vpcRoutePod) getPodARPInfo(ctx context.Context) error {
	logger.Infof(ctx, "获取Pod ARP信息")

	// 执行arp -a命令
	cmd := []string{"arp", "-a"}
	stdout, err := c.base.KubeClient.RemoteExec(vpcRouteTestNamespace, c.selectedPod.Name, vpcRouteTestContainerName, cmd)
	if err != nil {
		// 如果arp命令失败，尝试使用ip neigh命令
		logger.Warnf(ctx, "arp命令失败，尝试使用ip neigh: %v", err)
		cmd = []string{"ip", "neigh", "show"}
		stdout, err = c.base.KubeClient.RemoteExec(vpcRouteTestNamespace, c.selectedPod.Name, vpcRouteTestContainerName, cmd)
		if err != nil {
			return fmt.Errorf("执行ip neigh命令失败: %v", err)
		}
	}

	logger.Infof(ctx, "Pod ARP信息:")
	logger.Infof(ctx, "%s", stdout)

	// 解析ARP信息
	c.podARPEntries = strings.Split(strings.TrimSpace(stdout), "\n")

	// 提取网关的MAC地址
	if c.podGateway != "" {
		for _, arpEntry := range c.podARPEntries {
			if strings.Contains(arpEntry, c.podGateway) {
				// 从ARP条目中提取MAC地址
				if strings.Contains(arpEntry, "at") {
					parts := strings.Split(arpEntry, "at")
					if len(parts) >= 2 {
						macPart := strings.TrimSpace(parts[1])
						macFields := strings.Fields(macPart)
						if len(macFields) >= 1 {
							c.podGatewayMAC = macFields[0]
							logger.Infof(ctx, "✅ 提取到网关MAC地址: %s", c.podGatewayMAC)
							break
						}
					}
				} else {
					// ip neigh格式
					parts := strings.Fields(arpEntry)
					if len(parts) >= 5 {
						c.podGatewayMAC = parts[4]
						logger.Infof(ctx, "✅ 提取到网关MAC地址: %s", c.podGatewayMAC)
						break
					}
				}
			}
		}
	}

	if c.podGatewayMAC == "" {
		logger.Warnf(ctx, "⚠️ 未能提取到网关MAC地址")
	}

	return nil
}

// validateNetworkConfig 验证网络配置的合理性
func (c *vpcRoutePod) validateNetworkConfig(ctx context.Context) error {
	logger.Infof(ctx, "验证网络配置的合理性")

	// 1. 验证Pod IP是否有效
	if c.podIP == "" {
		return fmt.Errorf("Pod IP为空")
	}

	podIPAddr := net.ParseIP(c.podIP)
	if podIPAddr == nil {
		return fmt.Errorf("Pod IP格式无效: %s", c.podIP)
	}

	logger.Infof(ctx, "✅ Pod IP格式验证通过: %s", c.podIP)

	// 2. 验证网关IP是否有效
	if c.podGateway != "" {
		gatewayIPAddr := net.ParseIP(c.podGateway)
		if gatewayIPAddr == nil {
			logger.Warnf(ctx, "⚠️ 网关IP格式无效: %s", c.podGateway)
		} else {
			logger.Infof(ctx, "✅ 网关IP格式验证通过: %s", c.podGateway)
		}
	}

	// 3. 验证MAC地址格式
	if c.podMAC != "" {
		if len(c.podMAC) == 17 && strings.Count(c.podMAC, ":") == 5 {
			logger.Infof(ctx, "✅ Pod MAC地址格式验证通过: %s", c.podMAC)
		} else {
			logger.Warnf(ctx, "⚠️ Pod MAC地址格式可能无效: %s", c.podMAC)
		}
	}

	// 4. 验证路由表是否包含默认路由
	hasDefaultRoute := false
	for _, route := range c.podRoutes {
		if strings.Contains(route, "default") {
			hasDefaultRoute = true
			break
		}
	}

	if hasDefaultRoute {
		logger.Infof(ctx, "✅ 路由表包含默认路由")
	} else {
		logger.Warnf(ctx, "⚠️ 路由表不包含默认路由")
	}

	logger.Infof(ctx, "网络配置验证完成")
	return nil
}

// verifyCEPResource 验证集群中的CEP资源
func (c *vpcRoutePod) verifyCEPResource(ctx context.Context) error {
	logger.Infof(ctx, "验证集群中的CEP资源")

	if c.selectedPod == nil {
		return fmt.Errorf("未选择测试Pod")
	}

	// 首先检查集群网络模式
	cluster, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		logger.Warnf(ctx, "获取集群信息失败，跳过CEP验证: %v", err)
		return nil
	}

	networkMode := cluster.Cluster.Spec.ContainerNetworkConfig.Mode
	if networkMode != "vpc-route" {
		logger.Warnf(ctx, "⚠️ 集群网络模式为 %s，不是vpc-route模式", networkMode)
		logger.Infof(ctx, "CEP资源主要用于vpc-route模式，在当前模式下可能不存在或命名规则不同")
		logger.Infof(ctx, "跳过CEP验证，这不影响其他网络功能的测试")
		return nil
	}

	// 使用动态客户端获取CEP资源
	cepClient, err := c.base.KubeClient.NewDynamicClient("cce.baidubce.com", "v2", "cceendpoints")
	if err != nil {
		return fmt.Errorf("创建CEP客户端失败: %v", err)
	}

	// 首先尝试列出所有CEP资源，了解实际的命名规则
	logger.Infof(ctx, "列出所有CEP资源以了解命名规则")
	cepList, err := cepClient.List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.Warnf(ctx, "列出CEP资源失败: %v", err)
		logger.Infof(ctx, "可能是权限问题或CEP资源不存在，跳过CEP验证")
		return nil
	}

	logger.Infof(ctx, "找到 %d 个CEP资源", len(cepList.Items))

	// 查找与当前Pod相关的CEP资源
	var foundCEP *unstructured.Unstructured
	possibleNames := []string{
		fmt.Sprintf("%s-%s", c.selectedPod.Namespace, c.selectedPod.Name), // namespace-podname
		c.selectedPod.Name, // podname
		fmt.Sprintf("%s.%s", c.selectedPod.Name, c.selectedPod.Namespace), // podname.namespace
	}

	for _, cep := range cepList.Items {
		cepName := cep.GetName()
		logger.Infof(ctx, "发现CEP资源: %s", cepName)

		// 检查是否匹配任何可能的命名规则
		for _, possibleName := range possibleNames {
			if cepName == possibleName {
				logger.Infof(ctx, "✅ 找到匹配的CEP资源: %s", cepName)
				foundCEP = &cep
				c.cepName = cepName
				break
			}
		}

		// 也检查CEP是否包含Pod的IP地址
		if foundCEP == nil {
			if spec, ok := cep.Object["spec"].(map[string]interface{}); ok {
				if networking, ok := spec["networking"].(map[string]interface{}); ok {
					if addressing, ok := networking["addressing"].([]interface{}); ok && len(addressing) > 0 {
						if addr, ok := addressing[0].(map[string]interface{}); ok {
							if ipv4, ok := addr["ipv4"].(string); ok && ipv4 == c.podIP {
								logger.Infof(ctx, "✅ 通过IP地址找到匹配的CEP资源: %s (IP: %s)", cepName, ipv4)
								foundCEP = &cep
								c.cepName = cepName
								break
							}
						}
					}
				}
			}
		}
	}

	if foundCEP == nil {
		logger.Warnf(ctx, "⚠️ 未找到与Pod %s (IP: %s) 匹配的CEP资源", c.selectedPod.Name, c.podIP)
		logger.Infof(ctx, "尝试的命名规则: %v", possibleNames)
		logger.Infof(ctx, "这可能是正常现象，跳过CEP验证")
		return nil
	}

	cep := *foundCEP

	logger.Infof(ctx, "✅ 成功找到CEP资源: %s", c.cepName)

	// 验证CEP中的IP地址是否与Pod IP一致
	if spec, ok := cep.Object["spec"].(map[string]interface{}); ok {
		if networking, ok := spec["networking"].(map[string]interface{}); ok {
			if addressing, ok := networking["addressing"].([]interface{}); ok && len(addressing) > 0 {
				if addr, ok := addressing[0].(map[string]interface{}); ok {
					if ipv4, ok := addr["ipv4"].(string); ok {
						if ipv4 == c.podIP {
							logger.Infof(ctx, "✅ CEP中的IP地址与Pod IP一致: %s", ipv4)
						} else {
							return fmt.Errorf("CEP中的IP地址(%s)与Pod IP(%s)不一致", ipv4, c.podIP)
						}
					}
				}
			}
		}
	}

	logger.Infof(ctx, "CEP资源验证完成")
	return nil
}

// testPublicNetworkAccess 测试Pod的公网访问能力
func (c *vpcRoutePod) testPublicNetworkAccess(ctx context.Context) error {
	logger.Infof(ctx, "测试Pod的公网访问能力")

	testSites := []string{"www.baidu.com", "www.sina.com"}

	for _, site := range testSites {
		logger.Infof(ctx, "测试访问网站: %s", site)

		// 首先测试DNS解析
		if err := c.testDNSResolution(ctx, site); err != nil {
			logger.Warnf(ctx, "DNS解析失败: %v", err)
		} else {
			logger.Infof(ctx, "✅ DNS解析成功: %s", site)
		}

		// 测试HTTP访问
		if err := c.testHTTPAccess(ctx, site); err != nil {
			logger.Warnf(ctx, "HTTP访问失败: %v", err)
		} else {
			logger.Infof(ctx, "✅ HTTP访问成功: %s", site)
		}
	}

	logger.Infof(ctx, "公网访问测试完成")
	return nil
}

// testDNSResolution 测试DNS解析
func (c *vpcRoutePod) testDNSResolution(ctx context.Context, site string) error {
	cmd := []string{"nslookup", site}
	stdout, err := c.base.KubeClient.RemoteExec(vpcRouteTestNamespace, c.selectedPod.Name, vpcRouteTestContainerName, cmd)
	if err != nil {
		return fmt.Errorf("DNS解析失败: %v", err)
	}

	logger.Infof(ctx, "DNS解析结果: %s", strings.TrimSpace(stdout))
	return nil
}

// testHTTPAccess 测试HTTP访问
func (c *vpcRoutePod) testHTTPAccess(ctx context.Context, site string) error {
	cmd := []string{
		"curl",
		"-4", // 强制使用IPv4
		"-s",
		"-o", "/dev/null",
		"-w", "%{http_code}",
		"--connect-timeout", "5",
		"--max-time", "10",
		fmt.Sprintf("http://%s", site),
	}

	stdout, err := c.base.KubeClient.RemoteExec(vpcRouteTestNamespace, c.selectedPod.Name, vpcRouteTestContainerName, cmd)
	if err != nil {
		return fmt.Errorf("HTTP访问失败: %v", err)
	}

	httpCode := strings.TrimSpace(stdout)
	logger.Infof(ctx, "HTTP状态码: %s", httpCode)

	// 检查是否为成功的HTTP状态码（2xx或3xx）
	if len(httpCode) >= 1 {
		firstChar := httpCode[0]
		if firstChar == '2' || firstChar == '3' {
			return nil
		}
	}

	return fmt.Errorf("HTTP访问失败，状态码: %s", httpCode)
}

// checkClusterNodes 检查集群是否有可用节点
func (c *vpcRoutePod) checkClusterNodes(ctx context.Context) error {
	logger.Infof(ctx, "检查集群是否有可用节点")

	// 获取集群信息
	cluster, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		return fmt.Errorf("获取集群信息失败: %v", err)
	}

	logger.Infof(ctx, "集群信息:")
	logger.Infof(ctx, "  集群ID: %s", cluster.Cluster.Spec.ClusterID)
	logger.Infof(ctx, "  集群名称: %s", cluster.Cluster.Spec.ClusterName)
	logger.Infof(ctx, "  集群状态: %s", cluster.Cluster.Status.ClusterPhase)
	logger.Infof(ctx, "  节点数量: %d", cluster.Cluster.Status.NodeNum)
	logger.Infof(ctx, "  网络模式: %s", cluster.Cluster.Spec.ContainerNetworkConfig.Mode)
	logger.Infof(ctx, "  Pod CIDR: %s", cluster.Cluster.Spec.ContainerNetworkConfig.ClusterPodCIDR)

	// 检查集群网络模式
	if cluster.Cluster.Spec.ContainerNetworkConfig.Mode != "vpc-route" {
		logger.Warnf(ctx, "⚠️ 集群网络模式不是vpc-route，当前模式: %s", cluster.Cluster.Spec.ContainerNetworkConfig.Mode)
		logger.Infof(ctx, "测试将继续进行，但可能不完全适用于当前网络模式")
		logger.Infof(ctx, "注意：本测试主要针对vpc-route模式设计，在其他网络模式下部分功能可能不适用")
	} else {
		logger.Infof(ctx, "✅ 集群网络模式为vpc-route，适合进行本测试")
	}

	// 检查集群是否有节点
	if cluster.Cluster.Status.NodeNum == 0 {
		logger.Errorf(ctx, "❌ 集群没有节点，无法进行Pod网络测试")
		logger.Infof(ctx, "请按以下步骤添加节点:")
		logger.Infof(ctx, "1. 登录CCE控制台")
		logger.Infof(ctx, "2. 进入集群 %s (%s)", cluster.Cluster.Spec.ClusterName, cluster.Cluster.Spec.ClusterID)
		logger.Infof(ctx, "3. 创建节点组或添加节点")
		logger.Infof(ctx, "4. 等待节点状态变为Running")
		logger.Infof(ctx, "5. 重新运行测试")
		return fmt.Errorf("集群没有节点，请先添加节点后再进行测试")
	}

	// 获取节点列表
	instances, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
		ClusterRole: types.ClusterRoleNode,
	}, nil)
	if err != nil {
		return fmt.Errorf("获取集群节点列表失败: %v", err)
	}

	// 检查是否有Running状态的节点
	runningNodes := 0
	for _, instance := range instances.InstancePage.InstanceList {
		logger.Infof(ctx, "节点: %s, 状态: %s", instance.Spec.CCEInstanceID, instance.Status.InstancePhase)
		if instance.Status.InstancePhase == types.InstancePhaseRunning {
			runningNodes++
		}
	}

	if runningNodes == 0 {
		logger.Errorf(ctx, "❌ 集群没有Running状态的节点，无法进行Pod网络测试")
		return fmt.Errorf("集群没有Running状态的节点，请确保至少有一个节点处于Running状态")
	}

	logger.Infof(ctx, "✅ 集群有 %d 个Running状态的节点，可以进行测试", runningNodes)
	return nil
}

// testScaling 进行扩缩容测试
func (c *vpcRoutePod) testScaling(ctx context.Context) error {
	logger.Infof(ctx, "开始进行扩缩容测试")

	// 1. 扩容到4个副本
	logger.Infof(ctx, "扩容Deployment到4个副本")
	if err := c.scaleDeployment(ctx, 4); err != nil {
		return fmt.Errorf("扩容失败: %v", err)
	}

	// 2. 等待扩容完成并验证新Pod
	if err := c.waitForScalingComplete(ctx, 4); err != nil {
		return fmt.Errorf("等待扩容完成失败: %v", err)
	}

	// 3. 缩容到1个副本
	logger.Infof(ctx, "缩容Deployment到1个副本")
	if err := c.scaleDeployment(ctx, 1); err != nil {
		return fmt.Errorf("缩容失败: %v", err)
	}

	// 4. 等待缩容完成
	if err := c.waitForScalingComplete(ctx, 1); err != nil {
		return fmt.Errorf("等待缩容完成失败: %v", err)
	}

	logger.Infof(ctx, "✅ 扩缩容测试完成")
	return nil
}

// scaleDeployment 扩缩容Deployment
func (c *vpcRoutePod) scaleDeployment(ctx context.Context, replicas int32) error {
	deployment, err := c.base.KubeClient.ClientSet.AppsV1().Deployments(vpcRouteTestNamespace).Get(ctx, vpcRouteTestDeploymentName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取Deployment失败: %v", err)
	}

	deployment.Spec.Replicas = &replicas

	_, err = c.base.KubeClient.ClientSet.AppsV1().Deployments(vpcRouteTestNamespace).Update(ctx, deployment, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新Deployment失败: %v", err)
	}

	logger.Infof(ctx, "已发送扩缩容请求，目标副本数: %d", replicas)
	return nil
}

// waitForScalingComplete 等待扩缩容完成
func (c *vpcRoutePod) waitForScalingComplete(ctx context.Context, expectedReplicas int32) error {
	logger.Infof(ctx, "等待扩缩容完成，期望副本数: %d", expectedReplicas)

	err := wait.PollImmediate(10*time.Second, 8*time.Minute, func() (bool, error) {
		deployment, err := c.base.KubeClient.ClientSet.AppsV1().Deployments(vpcRouteTestNamespace).Get(ctx, vpcRouteTestDeploymentName, metav1.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取Deployment状态失败: %v", err)
			return false, nil
		}

		logger.Infof(ctx, "当前状态: 期望副本数=%d, 可用副本数=%d, 就绪副本数=%d",
			*deployment.Spec.Replicas,
			deployment.Status.AvailableReplicas,
			deployment.Status.ReadyReplicas)

		// 检查Deployment是否达到期望状态
		if deployment.Status.ReadyReplicas == expectedReplicas {
			// 额外检查实际的Running Pod数量
			pods, err := c.base.KubeClient.ClientSet.CoreV1().Pods(vpcRouteTestNamespace).List(ctx, metav1.ListOptions{
				LabelSelector: "app=" + vpcRouteTestDeploymentName,
			})
			if err != nil {
				logger.Warnf(ctx, "获取Pod列表失败: %v", err)
				return false, nil
			}

			runningPods := 0
			for _, pod := range pods.Items {
				if pod.Status.Phase == corev1.PodRunning && pod.DeletionTimestamp == nil {
					runningPods++
				}
			}

			logger.Infof(ctx, "实际Running Pod数量: %d, 期望: %d", runningPods, expectedReplicas)

			// 只有当实际Running Pod数量也等于期望值时才认为完成
			if int32(runningPods) == expectedReplicas {
				return true, nil
			}
		}

		return false, nil
	})

	if err != nil {
		return fmt.Errorf("等待扩缩容完成超时: %v", err)
	}

	// 获取当前Pod列表并验证
	pods, err := c.base.KubeClient.ClientSet.CoreV1().Pods(vpcRouteTestNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "app=" + vpcRouteTestDeploymentName,
	})
	if err != nil {
		return fmt.Errorf("获取Pod列表失败: %v", err)
	}

	runningPods := 0
	terminatingPods := 0
	for _, pod := range pods.Items {
		if pod.Status.Phase == corev1.PodRunning && pod.DeletionTimestamp == nil {
			runningPods++
			logger.Infof(ctx, "Running Pod: %s (IP: %s)", pod.Name, pod.Status.PodIP)
		} else if pod.DeletionTimestamp != nil {
			terminatingPods++
			logger.Infof(ctx, "Terminating Pod: %s", pod.Name)
		}
	}

	logger.Infof(ctx, "Pod状态统计: Running=%d, Terminating=%d, 期望=%d", runningPods, terminatingPods, expectedReplicas)

	// 由于前面的wait.PollImmediate已经确保了Deployment和实际Pod数量都达到期望状态，
	// 这里只做最终确认，如果不匹配则记录警告但不失败
	if int32(runningPods) != expectedReplicas {
		logger.Warnf(ctx, "⚠️ 最终验证: Running Pod数量(%d)与期望副本数(%d)不一致，但Deployment状态已正确", runningPods, expectedReplicas)
		logger.Infof(ctx, "这可能是由于Pod删除的时间延迟，通常是正常现象")
	}

	logger.Infof(ctx, "✅ 扩缩容完成，当前Running Pod数量: %d", runningPods)
	return nil
}
