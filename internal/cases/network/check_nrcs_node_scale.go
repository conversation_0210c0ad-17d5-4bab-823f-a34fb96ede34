/*
Copyright 2024 Baidu Inc.

本文件包含了针对NRCS在节点扩缩容场景下正确性的自动化测试用例。
该测试主要验证NRCS配置对新节点和已有节点的生效情况。

用例主要验证以下内容：
1. 在VPC-ENI网络模式下，创建NRCS资源并验证其是否正确应用于特定节点组
2. 节点组扩容后，验证新节点的NRS资源是否正确应用了NRCS的配置
3. 重启网络组件后，验证NRCS配置对已有节点的生效情况
4. 创建更高优先级的NRCS资源，验证优先级机制是否正常工作

测试流程：
0. 创建测试用的节点组（如有需要）
0.5. 修改集群ConfigMap中的burstable-mehrfach-eni配置为0，并重启网络组件使配置生效
1. 记录当前集群节点状态
2. 获取并保存网络配置信息
3. 创建第一个NRCS资源
4. 扩容节点组
5. 等待新节点就绪
6. 验证新节点NRS配置
7. 重启网络组件使NRCS对已有节点生效
8. 验证已有节点的NRS配置
9. 创建高优先级NRCS资源
10. 重启网络组件使高优先级NRCS资源生效
11. 验证高优先级NRCS的优先级机制

测试流程详见每个函数的具体实现。
*/

/*
使用config示例：
- name: CheckNrcsNodeScale
  config:
    subnetId: sbn-5ybs97i3irrc
*/

/*
注意事项，要求集群中有节点组，并且节点组要有存量节点。
（为了验证nrcs配置对节点组内新增节点生效，重启组件对存量节点生效）
*/
package network

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	// CheckNrcsNodeScaleCaseName - case 名字
	CheckNrcsNodeScaleCaseName cases.CaseName = "CheckNrcsNodeScale"

	// 网络组件名称常量
	networkAgentName     = "cce-network-agent"
	networkNamespace     = "kube-system"
	networkConfigMapName = "cce-network-v2-config"

	// NRCS资源名称
	nrcsResourceName             = "nrcs-example"
	nrcsResourceNameHighPriority = "nrcs-example-high-priority"
)

type nrcsNodeConfig struct {
	SubnetId string `json:"subnetId"` //创建nrcs需要指定给匹配的节点使用的子网
}

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CheckNrcsNodeScaleCaseName, NewCheckNrcsNodeScale)
}

// checkNrcsNodeScale 结构体定义
type checkNrcsNodeScale struct {
	base              *cases.BaseClient
	resources         []cases.Resource
	instanceGroupID   string              // 选择的节点组ID
	nodeSnapshot      map[string]struct{} // 保存测试前的节点信息
	originalReplicas  int                 // 原始节点组副本数
	nodeGroupNodes    []string            // 节点组内的节点列表
	nonNodeGroupNodes []string            // 不在节点组内的节点列表
	expandedNode      string              // 扩容后的新节点
	configMapData     map[string]string   // 原始网络配置信息
	createdNrcs       []string            // 创建的NRCS资源名称列表
	config            nrcsNodeConfig      //配置传入信息
	createdNodeGroup  bool                // 标记是否是我们创建的节点组
}

// NewCheckNrcsNodeScale - 测试案例构造函数
func NewCheckNrcsNodeScale(ctx context.Context) cases.Interface {
	return &checkNrcsNodeScale{
		nodeSnapshot: make(map[string]struct{}),
		createdNrcs:  make([]string, 0),
	}
}

// Init 初始化测试环境
func (c *checkNrcsNodeScale) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base is nil")
	}
	var cfg nrcsNodeConfig
	if err := json.Unmarshal(config, &cfg); err != nil {
		return fmt.Errorf("json.Unmarshal config failed: %s", err)
	}
	c.config = cfg

	c.base = base

	// 初始化随机数种子
	rand.Seed(time.Now().UnixNano())

	return nil
}

// Name 返回测试用例名称
func (c *checkNrcsNodeScale) Name() cases.CaseName {
	return CheckNrcsNodeScaleCaseName
}

// Desc 返回测试用例描述
func (c *checkNrcsNodeScale) Desc() string {
	return "验证在节点扩容后，集群相关资源的正确性，特别是NRCS配置的生效情况"
}

// Continue 返回是否继续测试
func (c *checkNrcsNodeScale) Continue(ctx context.Context) bool {
	return true
}

// ConfigFormat 返回配置格式
func (c *checkNrcsNodeScale) ConfigFormat() string {
	return ""
}

// Check 执行测试
func (c *checkNrcsNodeScale) Check(ctx context.Context) ([]cases.Resource, error) {
	var err error

	// 0. 创建测试用的节点组（如果需要）
	logger.Infof(ctx, "第零步：创建测试用的节点组（如有需要）...")
	if err = c.createInstanceGroup(ctx); err != nil {
		return nil, fmt.Errorf("创建测试用节点组失败: %v", err)
	}

	// 0.5. 修改configmap中的burstable-mehrfach-eni为0并重启网络组件
	logger.Infof(ctx, "第零点五步：修改网络配置并重启组件...")
	if err = c.prepareNetworkConfig(ctx); err != nil {
		return nil, fmt.Errorf("修改网络配置失败: %v", err)
	}

	// 1. 记录当前节点状态
	logger.Infof(ctx, "第一步：记录当前集群节点状态...")
	if err = c.recordCurrentNodeState(ctx); err != nil {
		return nil, fmt.Errorf("记录当前节点状态失败: %v", err)
	}

	// 2. 获取并保存网络配置
	logger.Infof(ctx, "第二步：获取集群网络配置信息...")
	if err = c.getNetworkConfig(ctx); err != nil {
		return nil, fmt.Errorf("获取网络配置失败: %v", err)
	}

	// 3. 创建第一个NRCS资源
	logger.Infof(ctx, "第三步：创建NRCS资源...")
	if err = c.createNrcs(ctx, nrcsResourceName, 0); err != nil {
		return nil, fmt.Errorf("创建NRCS资源失败: %v", err)
	}

	// 4. 扩容节点组
	logger.Infof(ctx, "第四步：扩容节点组...")
	if err = c.scaleInstanceGroup(ctx); err != nil {
		return nil, fmt.Errorf("扩容节点组失败: %v", err)
	}

	// 5. 等待新节点就绪
	logger.Infof(ctx, "第五步：等待新节点就绪...")
	if c.expandedNode, err = c.getNewNode(ctx); err != nil {
		return nil, fmt.Errorf("等待新节点就绪失败: %v", err)
	}

	// 6. 验证新节点NRS配置
	logger.Infof(ctx, "第六步：验证新节点NRS配置...")
	if err = c.verifyNewNodeNRS(ctx, c.expandedNode); err != nil {
		return nil, fmt.Errorf("验证新节点NRS配置失败: %v", err)
	}

	// 7. 重启网络组件
	logger.Infof(ctx, "第七步：重启网络组件使NRCS对已有节点生效...")
	if err = c.restartNetworkAgent(ctx); err != nil {
		return nil, fmt.Errorf("重启网络组件失败: %v", err)
	}

	// 8. 验证已有节点的NRS配置
	logger.Infof(ctx, "第八步：验证已有节点的NRS配置...")
	if err = c.verifyExistingNodesNRS(ctx); err != nil {
		return nil, fmt.Errorf("验证已有节点NRS配置失败: %v", err)
	}

	// 9. 创建高优先级NRCS资源
	logger.Infof(ctx, "第九步：创建高优先级NRCS资源...")
	if err = c.createNrcs(ctx, nrcsResourceNameHighPriority, 10); err != nil {
		return nil, fmt.Errorf("创建高优先级NRCS资源失败: %v", err)
	}

	// 10. 重启网络组件使高优先级NRCS生效
	logger.Infof(ctx, "第十步：重启网络组件使高优先级NRCS资源生效...")
	if err = c.restartNetworkAgent(ctx); err != nil {
		return nil, fmt.Errorf("重启网络组件失败: %v", err)
	}

	// 11. 验证高优先级NRCS生效
	logger.Infof(ctx, "第十一步：验证高优先级NRCS的优先级机制...")
	if err = c.verifyNRCSPriority(ctx); err != nil {
		return nil, fmt.Errorf("验证NRCS优先级机制失败: %v", err)
	}

	logger.Infof(ctx, "测试完成！")
	return c.resources, nil
}

// Clean 清理资源
func (c *checkNrcsNodeScale) Clean(ctx context.Context) error {
	var lastErr error

	// 1. 删除创建的NRCS资源
	for _, nrcsName := range c.createdNrcs {
		logger.Infof(ctx, "开始删除NRCS资源 %s", nrcsName)
		err := c.base.KubeClient.DeleteNrcs(ctx, nrcsName, metav1.DeleteOptions{})
		if err != nil {
			logger.Warnf(ctx, "删除NRCS %s 失败: %v", nrcsName, err)
			lastErr = err
		} else {
			logger.Infof(ctx, "成功删除NRCS %s", nrcsName)
		}
	}

	// 2. 恢复原始配置
	if len(c.configMapData) > 0 {
		logger.Infof(ctx, "恢复原始CNI配置")
		configMap, err := c.base.KubeClient.ClientSet.CoreV1().ConfigMaps("kube-system").Get(ctx, "cce-network-v2-config", metav1.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取ConfigMap失败: %v", err)
			if lastErr == nil {
				lastErr = err
			}
		} else {
			// 恢复原始数据
			configMap.Data = c.configMapData
			_, err = c.base.KubeClient.ClientSet.CoreV1().ConfigMaps("kube-system").Update(ctx, configMap, metav1.UpdateOptions{})
			if err != nil {
				logger.Warnf(ctx, "恢复ConfigMap失败: %v", err)
				if lastErr == nil {
					lastErr = err
				}
			} else {
				logger.Infof(ctx, "已恢复原始CNI配置")

				// 恢复配置后重启网络组件使配置生效
				logger.Infof(ctx, "重启网络组件使配置恢复生效")
				if err = c.restartNetworkComponents(ctx); err != nil {
					logger.Warnf(ctx, "重启网络组件失败: %v", err)
					if lastErr == nil {
						lastErr = err
					}
				} else {
					logger.Infof(ctx, "网络组件重启完成，配置恢复生效")
				}
			}
		}
	}

	// 3. 恢复节点组副本数量
	if c.instanceGroupID != "" && c.originalReplicas > 0 {
		// 如果是我们创建的节点组，则先缩容到0，然后删除
		if c.createdNodeGroup {
			// 先获取节点组当前状态
			ig, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, nil)
			if err != nil {
				logger.Warnf(ctx, "获取节点组状态失败: %v", err)
				if lastErr == nil {
					lastErr = err
				}
			} else {
				// 如果节点组当前副本数不为0，先缩容到0
				if ig.InstanceGroup.Spec.Replicas > 0 {
					logger.Infof(ctx, "节点组 %s 当前副本数: %d, 开始缩容到0...",
						c.instanceGroupID, ig.InstanceGroup.Spec.Replicas)

					// 记录缩容前节点组中的节点，用于后续验证删除情况
					nodeList, err := c.base.KubeClient.ClientSet.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
					if err != nil {
						logger.Warnf(ctx, "获取节点列表失败: %v", err)
					}

					var nodesInGroup []string
					if err == nil {
						for _, node := range nodeList.Items {
							if groupID, exists := node.Labels["instance-group-id"]; exists && groupID == c.instanceGroupID {
								nodesInGroup = append(nodesInGroup, node.Name)
							}
						}
						logger.Infof(ctx, "缩容前节点组 %s 中有 %d 个节点: %v", c.instanceGroupID, len(nodesInGroup), nodesInGroup)
					}

					// 使用UpdateInstanceGroupReplicas替代CreateScaleUpInstanceGroupTask
					request := &ccev2.UpdateInstanceGroupReplicasRequest{
						Replicas: 0,
					}

					resp, err := c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, c.base.ClusterID, c.instanceGroupID, request, nil)
					if err != nil {
						logger.Warnf(ctx, "缩容节点组到0失败: %v", err)
						if lastErr == nil {
							lastErr = err
						}
					} else {
						logger.Infof(ctx, "已发送缩容节点组到0的请求，RequestID: %s", resp.RequestID)

						// 等待缩容完成，最多等待15分钟
						timeout := 15 * time.Minute
						deadline := time.Now().Add(timeout)
						checkInterval := 30 * time.Second

						logger.Infof(ctx, "等待节点组缩容到0完成...")
						for time.Now().Before(deadline) {
							// 检查当前副本数
							currentIG, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, nil)
							if err != nil {
								logger.Warnf(ctx, "获取节点组状态失败: %v，将在 %s 后重试", err, checkInterval)
								time.Sleep(checkInterval)
								continue
							}

							logger.Infof(ctx, "节点组状态: 期望副本数=%d, 实际副本数=%d, 就绪副本数=%d, 缩容中=%d, 删除中=%d",
								currentIG.InstanceGroup.Spec.Replicas,
								currentIG.InstanceGroup.Status.ActualReplicas,
								currentIG.InstanceGroup.Status.ReadyReplicas,
								currentIG.InstanceGroup.Status.ScalingReplicas,
								currentIG.InstanceGroup.Status.DeletingReplicas)

							// 检查Kubernetes集群中节点组的节点是否都已删除
							currentNodeList, err := c.base.KubeClient.ClientSet.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
							if err != nil {
								logger.Warnf(ctx, "获取当前节点列表失败: %v，将在 %s 后重试", err, checkInterval)
								time.Sleep(checkInterval)
								continue
							}

							var remainingNodes []string
							for _, node := range currentNodeList.Items {
								if groupID, exists := node.Labels["instance-group-id"]; exists && groupID == c.instanceGroupID {
									remainingNodes = append(remainingNodes, node.Name)
								}
							}

							logger.Infof(ctx, "Kubernetes集群中节点组 %s 剩余节点数: %d", c.instanceGroupID, len(remainingNodes))
							if len(remainingNodes) > 0 {
								logger.Infof(ctx, "剩余节点: %v", remainingNodes)
							}

							// 判断缩容是否完成：
							// 1. 节点组Ready副本数为0
							// 2. 节点组实际副本数为0
							// 3. Kubernetes集群中不再有属于该节点组的节点
							// 4. 节点组不在缩容或删除状态
							if currentIG.InstanceGroup.Status.ReadyReplicas == 0 &&
								currentIG.InstanceGroup.Status.ActualReplicas == 0 &&
								len(remainingNodes) == 0 &&
								currentIG.InstanceGroup.Status.ScalingReplicas == 0 &&
								currentIG.InstanceGroup.Status.DeletingReplicas == 0 {
								logger.Infof(ctx, "节点组 %s 已成功缩容到0个节点，所有节点已从集群中删除", c.instanceGroupID)
								break
							}

							logger.Infof(ctx, "节点组 %s 缩容尚未完成，等待 %s 后重新检查...", c.instanceGroupID, checkInterval)
							time.Sleep(checkInterval)
						}

						// 检查是否超时
						if time.Now().After(deadline) {
							logger.Warnf(ctx, "等待节点组缩容到0超时(%v)，将继续尝试删除节点组", timeout)

							// 即使超时也要打印最终状态用于调试
							finalIG, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, nil)
							if err == nil {
								logger.Warnf(ctx, "超时时节点组最终状态: 期望副本数=%d, 实际副本数=%d, 就绪副本数=%d, 缩容中=%d, 删除中=%d",
									finalIG.InstanceGroup.Spec.Replicas,
									finalIG.InstanceGroup.Status.ActualReplicas,
									finalIG.InstanceGroup.Status.ReadyReplicas,
									finalIG.InstanceGroup.Status.ScalingReplicas,
									finalIG.InstanceGroup.Status.DeletingReplicas)
							}
						} else {
							// 额外等待一些时间确保节点组状态稳定
							stabilizeWait := 10 * time.Second
							logger.Infof(ctx, "等待 %s 确保节点组状态稳定...", stabilizeWait)
							time.Sleep(stabilizeWait)

							// 最终确认状态
							finalIG, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, nil)
							if err == nil {
								logger.Infof(ctx, "删除前节点组最终状态: 期望副本数=%d, 实际副本数=%d, 就绪副本数=%d, 缩容中=%d, 删除中=%d",
									finalIG.InstanceGroup.Spec.Replicas,
									finalIG.InstanceGroup.Status.ActualReplicas,
									finalIG.InstanceGroup.Status.ReadyReplicas,
									finalIG.InstanceGroup.Status.ScalingReplicas,
									finalIG.InstanceGroup.Status.DeletingReplicas)
							}
						}
					}
				}

				// 缩容完成或失败后，尝试删除节点组
				logger.Infof(ctx, "删除测试创建的节点组 %s", c.instanceGroupID)
				_, err = c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, true, nil)
				if err != nil {
					logger.Warnf(ctx, "删除测试创建的节点组失败: %v", err)
					if lastErr == nil {
						lastErr = err
					}
				} else {
					logger.Infof(ctx, "成功删除测试创建的节点组 %s", c.instanceGroupID)
				}
			}
		} else {
			// 非创建的节点组则恢复副本数
			logger.Infof(ctx, "恢复节点组 %s 副本数至 %d", c.instanceGroupID, c.originalReplicas)
			// 使用UpdateInstanceGroupReplicas替代CreateScaleUpInstanceGroupTask
			if c.originalReplicas > 0 {
				request := &ccev2.UpdateInstanceGroupReplicasRequest{
					Replicas: c.originalReplicas,
				}

				resp, err := c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, c.base.ClusterID, c.instanceGroupID, request, nil)
				if err != nil {
					logger.Warnf(ctx, "恢复节点组副本数失败: %v", err)
					if lastErr == nil {
						lastErr = err
					}
				} else {
					logger.Infof(ctx, "已发送恢复节点组副本数请求，RequestID: %s", resp.RequestID)
				}
			}
		}
	}

	logger.Infof(ctx, "资源清理完成")
	return lastErr
}

// recordCurrentNodeState 记录当前节点状态
func (c *checkNrcsNodeScale) recordCurrentNodeState(ctx context.Context) error {
	// 获取所有节点
	nodeList, err := c.base.KubeClient.ClientSet.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取节点列表失败: %v", err)
	}

	if len(nodeList.Items) == 0 {
		return fmt.Errorf("集群中没有找到节点")
	}

	logger.Infof(ctx, "集群中共有 %d 个节点", len(nodeList.Items))

	// 记录所有节点名称
	for _, node := range nodeList.Items {
		c.nodeSnapshot[node.Name] = struct{}{}
		logger.Infof(ctx, "节点: %s, 状态: %s", node.Name, getNodeReadyStatus(&node))
	}

	// 获取一个节点组ID
	c.instanceGroupID, err = c.getInstanceGroupID(ctx, nodeList.Items)
	if err != nil {
		return fmt.Errorf("获取节点组ID失败: %v", err)
	}

	// 获取节点组中的节点和不在节点组中的节点
	for _, node := range nodeList.Items {
		if nodeGroupID, exists := node.Labels["instance-group-id"]; exists && nodeGroupID == c.instanceGroupID {
			c.nodeGroupNodes = append(c.nodeGroupNodes, node.Name)
		} else {
			c.nonNodeGroupNodes = append(c.nonNodeGroupNodes, node.Name)
		}
	}

	if len(c.nodeGroupNodes) == 0 {
		return fmt.Errorf("未找到节点组 %s 中的节点", c.instanceGroupID)
	}

	if len(c.nonNodeGroupNodes) == 0 {
		logger.Warnf(ctx, "所有节点都属于选定的节点组 %s", c.instanceGroupID)
	}

	// 获取节点组的当前副本数
	c.originalReplicas, err = c.getInstanceGroupReplicas(ctx)
	if err != nil {
		return fmt.Errorf("获取节点组副本数失败: %v", err)
	}

	logger.Infof(ctx, "选定节点组: %s, 当前副本数: %d", c.instanceGroupID, c.originalReplicas)
	logger.Infof(ctx, "节点组中的节点数量: %d, 不在节点组中的节点数量: %d",
		len(c.nodeGroupNodes), len(c.nonNodeGroupNodes))

	return nil
}

// getNodeReadyStatus 获取节点就绪状态
func getNodeReadyStatus(node *corev1.Node) string {
	for _, condition := range node.Status.Conditions {
		if condition.Type == corev1.NodeReady {
			return string(condition.Status)
		}
	}
	return "Unknown"
}

// getInstanceGroupID 获取一个有效的节点组ID
func (c *checkNrcsNodeScale) getInstanceGroupID(ctx context.Context, nodes []corev1.Node) (string, error) {
	// 统计每个节点组的节点数量
	instanceGroupCounts := make(map[string]int)

	for _, node := range nodes {
		if igID, exists := node.Labels["instance-group-id"]; exists && igID != "" {
			instanceGroupCounts[igID]++
		}
	}

	if len(instanceGroupCounts) == 0 {
		return "", fmt.Errorf("没有找到带有 instance-group-id 标签的节点")
	}

	// 选择节点数量最多的节点组
	var selectedGroup string
	maxNodes := 0

	for igID, count := range instanceGroupCounts {
		logger.Infof(ctx, "节点组 %s 有 %d 个节点", igID, count)
		if count > maxNodes {
			selectedGroup = igID
			maxNodes = count
		}
	}

	// 确保选择的节点组至少有一个节点
	if maxNodes == 0 {
		return "", fmt.Errorf("所有节点组都没有节点")
	}

	logger.Infof(ctx, "选择节点组 %s 进行测试，该节点组有 %d 个节点", selectedGroup, maxNodes)

	return selectedGroup, nil
}

// getInstanceGroupReplicas 获取节点组的当前副本数
func (c *checkNrcsNodeScale) getInstanceGroupReplicas(ctx context.Context) (int, error) {
	// 检查实例组ID是否已设置
	if c.instanceGroupID == "" {
		return 0, fmt.Errorf("未选择节点组")
	}

	// 获取节点组信息
	ig, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, nil)
	if err != nil {
		return 0, fmt.Errorf("获取节点组信息失败: %v", err)
	}

	replicas := ig.InstanceGroup.Spec.Replicas
	logger.Infof(ctx, "节点组 %s 当前副本数: %d", c.instanceGroupID, replicas)

	return replicas, nil
}

// getNetworkConfig 获取集群默认网络配置
func (c *checkNrcsNodeScale) getNetworkConfig(ctx context.Context) error {
	// 获取网络配置ConfigMap
	cm, err := c.base.KubeClient.ClientSet.CoreV1().ConfigMaps(networkNamespace).Get(ctx, networkConfigMapName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取网络配置ConfigMap失败: %v", err)
	}

	// 保存ConfigMap数据
	c.configMapData = cm.Data

	// 输出关键的网络配置参数
	logger.Infof(ctx, "获取网络配置成功，ConfigMap有 %d 个配置项", len(cm.Data))

	// 显示所有配置项的键名
	var keys []string
	for k := range cm.Data {
		keys = append(keys, k)
	}
	logger.Infof(ctx, "ConfigMap包含以下配置项: %v", keys)

	ceedData, ok := cm.Data["cced"]
	if !ok {
		return fmt.Errorf("ConfigMap中没有cced数据")
	}

	// 记录一小部分ceedData内容用于调试
	dataPreview := ""
	if len(ceedData) > 200 {
		dataPreview = ceedData[:200] + "..."
	} else {
		dataPreview = ceedData
	}
	logger.Infof(ctx, "ceed配置数据预览: %s", dataPreview)

	// 记录关键配置参数
	var keyParams = []string{
		"eni-subnet-ids",
		"burstable-mehrfach-eni",
		"eni-pre-allocate-num",
		"ippool-min-allocate-ips",
		"ippool-pre-allocate",
		"ippool-max-above-watermark",
	}

	logger.Infof(ctx, "从ceed中获取的配置参数值:")
	for _, param := range keyParams {
		value := getConfigValue(ceedData, param)
		logger.Infof(ctx, "  %s: %s", param, value)
	}

	return nil
}

// getConfigValue 从配置字符串中提取特定参数的值
func getConfigValue(config string, key string) string {
	// 按行解析YAML格式的配置字符串
	lines := strings.Split(config, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		// 检查是否是我们要找的键
		if strings.HasPrefix(line, key+":") || strings.HasPrefix(line, key+" :") {
			// 提取值部分
			parts := strings.SplitN(line, ":", 2)
			if len(parts) == 2 {
				value := strings.TrimSpace(parts[1])
				// 移除可能的引号
				value = strings.Trim(value, "\"'")
				return value
			}
		}
	}
	return "未找到"
}

// getConfigValueInt 将配置值转换为整数
func getConfigValueInt(config string, key string) int {
	value := getConfigValue(config, key)
	if value == "未找到" {
		return 0
	}

	// 尝试将字符串转换为整数
	intValue, err := strconv.Atoi(value)
	if err != nil {
		// 如果转换失败，尝试检查是否是布尔值（true/false）
		if strings.ToLower(value) == "true" {
			return 1
		}
		// 否则返回默认值0
		return 0
	}
	return intValue
}

// createNrcs 创建NRCS资源
func (c *checkNrcsNodeScale) createNrcs(ctx context.Context, name string, priority int32) error {
	// 判断节点组ID是否已设置
	if c.instanceGroupID == "" {
		return fmt.Errorf("未选择节点组")
	}

	// 创建matchLabels
	matchLabels := map[string]string{
		"instance-group-id": c.instanceGroupID,
	}

	// 定义测试所需的NRCS参数
	burstableMehrfachENI := 0
	ippoolPreAllocateENI := 2
	ippoolMinAllocate := 9
	ippoolPreAllocate := 1
	ippoolMaxAboveWatermark := 23

	// 如果是高优先级的NRCS，使用不同的参数
	if priority > 0 {
		burstableMehrfachENI = 0
		ippoolPreAllocateENI = 3
		ippoolMinAllocate = 11
		ippoolPreAllocate = 3
		ippoolMaxAboveWatermark = 25
	}

	// 构建NRCS资源对象
	nrcs := &types.NetResourceConfigSet{
		TypeMeta: metav1.TypeMeta{
			APIVersion: "cce.baidubce.com/v2alpha1",
			Kind:       "NetResourceConfigSet",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: name,
		},
		Spec: types.NetResourceConfigSetSpec{
			Priority: priority,
			Selector: &metav1.LabelSelector{
				MatchLabels: matchLabels,
			},
			AgentConfig: types.AgentConfig{
				EniSubnetIDs:            []string{c.config.SubnetId},
				BurstableMehrfachENI:    &burstableMehrfachENI,
				IPPoolPreAllocateENI:    &ippoolPreAllocateENI,
				IPPoolMinAllocate:       &ippoolMinAllocate,
				IPPoolPreAllocate:       &ippoolPreAllocate,
				IPPoolMaxAboveWatermark: &ippoolMaxAboveWatermark,
			},
		},
	}

	// 先检查是否已存在同名资源
	existingNrcs, err := c.base.KubeClient.GetNrcs(ctx, name, &kube.GetOptions{})
	if err == nil && existingNrcs != nil {
		// 资源已存在，先删除再创建
		logger.Infof(ctx, "找到已存在的NRCS资源 %s，先删除", name)
		err = c.base.KubeClient.DeleteNrcs(ctx, name, metav1.DeleteOptions{})
		if err != nil {
			return fmt.Errorf("删除已存在的NRCS资源失败: %v", err)
		}

		// 等待NRCS资源被完全删除
		logger.Infof(ctx, "等待NRCS资源 %s 被完全删除", name)
		maxRetries := 10
		retryInterval := 2 * time.Second
		deleted := false

		for i := 0; i < maxRetries; i++ {
			_, err := c.base.KubeClient.GetNrcs(ctx, name, &kube.GetOptions{})
			if err != nil {
				// 资源已经被删除
				deleted = true
				break
			}
			logger.Infof(ctx, "NRCS资源 %s 仍在删除中，等待 %v 后重试 (%d/%d)",
				name, retryInterval, i+1, maxRetries)
			time.Sleep(retryInterval)
		}

		if !deleted {
			logger.Warnf(ctx, "等待NRCS资源 %s 删除超时，将尝试继续创建", name)
		} else {
			logger.Infof(ctx, "NRCS资源 %s 已成功删除，继续创建新资源", name)
		}
	}

	// 创建新资源
	logger.Infof(ctx, "创建新的NRCS资源 %s", name)
	_, err = c.base.KubeClient.CreateNrcs(ctx, nrcs)
	if err != nil {
		return fmt.Errorf("创建NRCS资源失败: %v", err)
	}

	// 确保记录到创建的资源列表中
	alreadyInList := false
	for _, existingName := range c.createdNrcs {
		if existingName == name {
			alreadyInList = true
			break
		}
	}
	if !alreadyInList {
		// 添加到创建的资源列表
		c.createdNrcs = append(c.createdNrcs, name)
	}

	// 验证NRCS是否创建成功
	createdNrcs, err := c.base.KubeClient.GetNrcs(ctx, name, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("验证NRCS资源创建失败: %v", err)
	}

	logger.Infof(ctx, "NRCS资源 %s 创建成功:", name)
	logger.Infof(ctx, "  优先级: %d", createdNrcs.Spec.Priority)
	logger.Infof(ctx, "  标签选择器: %v", createdNrcs.Spec.Selector.MatchLabels)
	logger.Infof(ctx, "  配置参数:")
	logger.Infof(ctx, "    子网ID: %v", createdNrcs.Spec.AgentConfig.EniSubnetIDs)
	logger.Infof(ctx, "    burstable-mehrfach-eni: %d", *createdNrcs.Spec.AgentConfig.BurstableMehrfachENI)
	logger.Infof(ctx, "    eni-pre-allocate-num: %d", *createdNrcs.Spec.AgentConfig.IPPoolPreAllocateENI)
	logger.Infof(ctx, "    ippool-min-allocate: %d", *createdNrcs.Spec.AgentConfig.IPPoolMinAllocate)
	logger.Infof(ctx, "    ippool-pre-allocate: %d", *createdNrcs.Spec.AgentConfig.IPPoolPreAllocate)
	logger.Infof(ctx, "    ippool-max-above-watermark: %d", *createdNrcs.Spec.AgentConfig.IPPoolMaxAboveWatermark)

	return nil
}

// scaleInstanceGroup 设置节点组副本数
func (c *checkNrcsNodeScale) scaleInstanceGroup(ctx context.Context) error {
	logger.Infof(ctx, "scaling instance group to add new node")

	// 获取实例组信息
	igResp, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, nil)
	if err != nil {
		logger.Errorf(ctx, "failed to get instance group: %v", err)
		return err
	}

	// 准备扩容请求
	currentReplicas := igResp.InstanceGroup.Spec.Replicas
	targetReplicas := currentReplicas + 1
	logger.Infof(ctx, "scaling instance group from %d to %d replicas", currentReplicas, targetReplicas)

	// 创建扩容请求
	updateRequest := &ccev2.UpdateInstanceGroupReplicasRequest{
		Replicas: targetReplicas,
	}

	// 执行扩容
	_, err = c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, c.base.ClusterID, c.instanceGroupID, updateRequest, nil)
	if err != nil {
		logger.Errorf(ctx, "failed to update instance group replicas: %v", err)
		return err
	}

	return nil
}

// getNewNode 等待新节点就绪并返回节点名称
func (c *checkNrcsNodeScale) getNewNode(ctx context.Context) (string, error) {
	logger.Infof(ctx, "等待新节点就绪...")

	// 设置等待时间参数
	waitStart := time.Now()
	maxWaitTime := 15 * time.Minute
	checkInterval := 15 * time.Second

	// 获取当前集群中所有节点的初始快照
	initialNodeList, err := c.base.KubeClient.ClientSet.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return "", fmt.Errorf("获取初始节点列表失败: %v", err)
	}
	initialNodeCount := len(initialNodeList.Items)
	logger.Infof(ctx, "初始节点数量: %d", initialNodeCount)

	initialNodes := make(map[string]struct{})
	for _, node := range initialNodeList.Items {
		initialNodes[node.Name] = struct{}{}
	}

	// 循环等待新节点
	statusCheckTicker := time.NewTicker(checkInterval)
	defer statusCheckTicker.Stop()

	var previousStatus string

	for range statusCheckTicker.C {
		if time.Since(waitStart) > maxWaitTime {
			return "", fmt.Errorf("等待新节点超时（%v）", maxWaitTime)
		}

		// 1. 获取当前所有节点
		nodeList, err := c.base.KubeClient.ClientSet.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取节点列表失败: %v，将在 %s 后重试", err, checkInterval)
			continue
		}

		currentNodeCount := len(nodeList.Items)

		// 2. 获取节点组状态
		instanceGroup, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.instanceGroupID, nil)
		if err != nil {
			logger.Warnf(ctx, "获取节点组状态失败: %v，将在 %s 后重试", err, checkInterval)
			continue
		}

		// 输出节点组状态信息
		logger.Infof(ctx, "节点组状态: 期望副本数=%d, 实际副本数=%d, 就绪副本数=%d, 扩容中=%d",
			instanceGroup.InstanceGroup.Spec.Replicas,
			instanceGroup.InstanceGroup.Status.ActualReplicas,
			instanceGroup.InstanceGroup.Status.ReadyReplicas,
			instanceGroup.InstanceGroup.Status.ScalingReplicas)

		// 获取节点组当前状态
		var currentStatus string
		if instanceGroup.InstanceGroup.Status.Pause != nil && instanceGroup.InstanceGroup.Status.Pause.Paused {
			currentStatus = "Paused"
		} else if instanceGroup.InstanceGroup.Status.ReadyReplicas == instanceGroup.InstanceGroup.Status.ActualReplicas {
			currentStatus = "Ready"
		} else if instanceGroup.InstanceGroup.Status.ScalingReplicas > 0 {
			currentStatus = "Scaling"
		} else if instanceGroup.InstanceGroup.Status.DeletingReplicas > 0 {
			currentStatus = "Deleting"
		} else if instanceGroup.InstanceGroup.Status.NotReadyReplicas > 0 {
			currentStatus = "NotReady"
		} else {
			currentStatus = "Unknown"
		}

		if currentStatus != previousStatus {
			logger.Infof(ctx, "节点组状态变化: %s -> %s", previousStatus, currentStatus)
			previousStatus = currentStatus
		}

		// 3. 找出新增的节点
		var newNodes []string
		for _, node := range nodeList.Items {
			if _, exists := initialNodes[node.Name]; !exists {
				// 如果节点不在初始节点快照中
				if groupID, exists := node.Labels["instance-group-id"]; exists && groupID == c.instanceGroupID {
					// 确认该节点属于指定的节点组
					isReady := isNodeReady(&node)
					readyStatus := "未就绪"
					if isReady {
						readyStatus = "已就绪"
					}

					logger.Infof(ctx, "发现新节点 %s 属于节点组 %s，状态: %s",
						node.Name, c.instanceGroupID, readyStatus)

					// 打印节点的更多信息
					logger.Infof(ctx, "  节点 %s 详情:", node.Name)
					logger.Infof(ctx, "    标签数量: %d", len(node.Labels))

					// 打印节点的主要IP地址
					for _, addr := range node.Status.Addresses {
						if addr.Type == corev1.NodeInternalIP {
							logger.Infof(ctx, "    内部IP: %s", addr.Address)
						} else if addr.Type == corev1.NodeHostName {
							logger.Infof(ctx, "    主机名: %s", addr.Address)
						}
					}

					if isReady {
						newNodes = append(newNodes, node.Name)
					}
				}
			}
		}

		// 快速路径：如果节点组状态为Ready且找到了新的就绪节点，立即返回
		if currentStatus == "Ready" && len(newNodes) > 0 {
			waitTime := time.Since(waitStart).Round(time.Second)
			logger.Infof(ctx, "扩容成功，经过 %v 后找到新就绪节点: %v", waitTime, newNodes)
			return newNodes[0], nil
		}

		// 4. 检查新节点是否就绪，如果有就绪的新节点则返回
		if len(newNodes) > 0 {
			waitTime := time.Since(waitStart).Round(time.Second)
			logger.Infof(ctx, "扩容成功，经过 %v 后找到新就绪节点: %v", waitTime, newNodes)
			return newNodes[0], nil
		}

		elapsedTime := time.Since(waitStart).Round(time.Second)
		remainingTime := (maxWaitTime - elapsedTime).Round(time.Second)
		logger.Infof(ctx, "等待新节点就绪中，已等待: %v，剩余时间: %v，节点数量: %d，将在 %s 后重新检查...",
			elapsedTime, remainingTime, currentNodeCount, checkInterval)
	}

	return "", fmt.Errorf("等待新节点异常退出")
}

// isNodeReady 检查节点是否就绪
func isNodeReady(node *corev1.Node) bool {
	for _, condition := range node.Status.Conditions {
		if condition.Type == corev1.NodeNetworkUnavailable {
			return condition.Status == corev1.ConditionFalse
		}
	}
	return false
}

// restartNetworkAgent 重启网络组件
func (c *checkNrcsNodeScale) restartNetworkAgent(ctx context.Context) error {
	logger.Infof(ctx, "开始重启网络组件 %s...", networkAgentName)

	// 获取DaemonSet
	ds, err := c.base.KubeClient.ClientSet.AppsV1().DaemonSets(networkNamespace).Get(ctx, networkAgentName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取DaemonSet %s 失败: %v", networkAgentName, err)
	}

	// 添加或更新重启注解
	if ds.Spec.Template.Annotations == nil {
		ds.Spec.Template.Annotations = make(map[string]string)
	}

	// 使用当前时间作为重启注解值
	restartTimestamp := fmt.Sprintf("restart-at-%d", time.Now().Unix())
	ds.Spec.Template.Annotations["kubectl.kubernetes.io/restartedAt"] = restartTimestamp

	logger.Infof(ctx, "更新 DaemonSet %s 添加重启注解: %s", networkAgentName, restartTimestamp)

	// 更新DaemonSet
	_, err = c.base.KubeClient.ClientSet.AppsV1().DaemonSets(networkNamespace).Update(ctx, ds, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新DaemonSet失败: %v", err)
	}

	logger.Infof(ctx, "已发送重启命令，等待 DaemonSet 组件重启完成...")

	// 等待DaemonSet更新完成
	waitStart := time.Now()
	maxWaitTime := 10 * time.Minute
	checkInterval := 5 * time.Second // 减少检查间隔，加快验证速度

	for {
		if time.Since(waitStart) > maxWaitTime {
			return fmt.Errorf("等待DaemonSet更新超时")
		}

		// 等待一个短暂的时间确保网络组件稳定
		waitTime := 5 * time.Second
		logger.Infof(ctx, "等待 %s 让组件稳定...", waitTime)
		time.Sleep(waitTime)

		// 获取当前DaemonSet状态
		ds, err := c.base.KubeClient.ClientSet.AppsV1().DaemonSets(networkNamespace).Get(ctx, networkAgentName, metav1.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取DaemonSet状态失败: %v，将继续重试", err)
		} else {
			logger.Infof(ctx, "DaemonSet状态: 期望数=%d, 当前数=%d, 就绪数=%d, 更新数=%d, 可用数=%d",
				ds.Status.DesiredNumberScheduled,
				ds.Status.CurrentNumberScheduled,
				ds.Status.NumberReady,
				ds.Status.UpdatedNumberScheduled,
				ds.Status.NumberAvailable)

			// 当所有期望的Pod都已更新且就绪时，重启完成
			if ds.Status.UpdatedNumberScheduled == ds.Status.DesiredNumberScheduled &&
				ds.Status.NumberReady == ds.Status.DesiredNumberScheduled {
				logger.Infof(ctx, "网络组件重启完成")

				// 检查DaemonSet的pod是否都已经Ready
				podList, err := c.base.KubeClient.ClientSet.CoreV1().Pods(networkNamespace).List(ctx, metav1.ListOptions{
					LabelSelector: "app=" + networkAgentName,
				})
				if err != nil {
					logger.Warnf(ctx, "获取DaemonSet的Pod列表失败: %v，将继续", err)
				} else {
					allPodsReady := true
					for _, pod := range podList.Items {
						isReady := false
						for _, condition := range pod.Status.Conditions {
							if condition.Type == corev1.PodReady && condition.Status == corev1.ConditionTrue {
								isReady = true
								break
							}
						}
						if !isReady {
							allPodsReady = false
							logger.Infof(ctx, "Pod %s 尚未就绪", pod.Name)
							break
						}
					}

					if allPodsReady {
						logger.Infof(ctx, "所有网络组件Pod已就绪，继续测试")
						return nil
					}
				}

				return nil
			}
		}

		logger.Infof(ctx, "网络组件重启中，等待 %s 后重新检查...", checkInterval)
		time.Sleep(checkInterval)
	}
}

// verifyNewNodeNRS 验证新节点的NRS配置
func (c *checkNrcsNodeScale) verifyNewNodeNRS(ctx context.Context, nodeName string) error {
	logger.Infof(ctx, "验证新节点 %s 的NRS配置...", nodeName)

	// 获取节点NRS资源
	nrs, err := c.base.KubeClient.GetNrs(ctx, nodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取节点 %s 的NRS资源失败: %v", nodeName, err)
	}

	// 获取NRCS资源，以便获取预期的配置值
	nrcsResource, err := c.base.KubeClient.GetNrcs(ctx, nrcsResourceName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取NRCS资源失败: %v", err)
	}

	// 验证NRS配置
	logger.Infof(ctx, "节点 %s 的NRS配置:", nodeName)
	logger.Infof(ctx, "  子网配置: %v", nrs.Spec.Eni.SubnetIds)
	logger.Infof(ctx, "  eni.preAllocateENI: %d", nrs.Spec.Eni.PreAllocateENI)
	logger.Infof(ctx, "  eni.burstableMehrfachENI: %d", nrs.Spec.Eni.BurstableMehrfachENI)
	logger.Infof(ctx, "  ipam.max-above-watermark: %d", nrs.Spec.Ipam.MaxAboveWatermark)
	logger.Infof(ctx, "  ipam.min-allocate: %d", nrs.Spec.Ipam.MinAllocate)
	logger.Infof(ctx, "  ipam.pre-allocate: %d", nrs.Spec.Ipam.PreAllocate)

	// 输出NRCS中的预期值
	logger.Infof(ctx, "NRCS配置的预期值:")
	logger.Infof(ctx, "  子网ID: %v", nrcsResource.Spec.AgentConfig.EniSubnetIDs)
	logger.Infof(ctx, "  burstable-mehrfach-eni: %d", *nrcsResource.Spec.AgentConfig.BurstableMehrfachENI)
	logger.Infof(ctx, "  eni-pre-allocate-num: %d", *nrcsResource.Spec.AgentConfig.IPPoolPreAllocateENI)
	logger.Infof(ctx, "  ippool-min-allocate: %d", *nrcsResource.Spec.AgentConfig.IPPoolMinAllocate)
	logger.Infof(ctx, "  ippool-pre-allocate: %d", *nrcsResource.Spec.AgentConfig.IPPoolPreAllocate)
	logger.Infof(ctx, "  ippool-max-above-watermark: %d", *nrcsResource.Spec.AgentConfig.IPPoolMaxAboveWatermark)

	// 验证子网配置
	if !containsString(nrs.Spec.Eni.SubnetIds, c.config.SubnetId) {
		return fmt.Errorf("节点 %s 的NRS子网配置未包含预期的子网ID %s", nodeName, c.config.SubnetId)
	}

	// 验证ENI预分配配置
	if nrs.Spec.Eni.PreAllocateENI != *nrcsResource.Spec.AgentConfig.IPPoolPreAllocateENI {
		return fmt.Errorf("节点 %s 的NRS eni.preAllocateENI配置不正确，期望值%d，实际值%d",
			nodeName, *nrcsResource.Spec.AgentConfig.IPPoolPreAllocateENI, nrs.Spec.Eni.PreAllocateENI)
	}

	// 验证burstable-mehrfach-eni配置
	if nrs.Spec.Eni.BurstableMehrfachENI != *nrcsResource.Spec.AgentConfig.BurstableMehrfachENI {
		return fmt.Errorf("节点 %s 的NRS eni.burstableMehrfachENI配置不正确，期望值%d，实际值%d",
			nodeName, *nrcsResource.Spec.AgentConfig.BurstableMehrfachENI, nrs.Spec.Eni.BurstableMehrfachENI)
	}

	// 验证IPAM配置
	if nrs.Spec.Ipam.MaxAboveWatermark != *nrcsResource.Spec.AgentConfig.IPPoolMaxAboveWatermark {
		return fmt.Errorf("节点 %s 的NRS ipam.max-above-watermark配置不正确，期望值%d，实际值%d",
			nodeName, *nrcsResource.Spec.AgentConfig.IPPoolMaxAboveWatermark, nrs.Spec.Ipam.MaxAboveWatermark)
	}

	if nrs.Spec.Ipam.MinAllocate != *nrcsResource.Spec.AgentConfig.IPPoolMinAllocate {
		return fmt.Errorf("节点 %s 的NRS ipam.min-allocate配置不正确，期望值%d，实际值%d",
			nodeName, *nrcsResource.Spec.AgentConfig.IPPoolMinAllocate, nrs.Spec.Ipam.MinAllocate)
	}

	if nrs.Spec.Ipam.PreAllocate != *nrcsResource.Spec.AgentConfig.IPPoolPreAllocate {
		return fmt.Errorf("节点 %s 的NRS ipam.pre-allocate配置不正确，期望值%d，实际值%d",
			nodeName, *nrcsResource.Spec.AgentConfig.IPPoolPreAllocate, nrs.Spec.Ipam.PreAllocate)
	}

	logger.Infof(ctx, "节点 %s 的NRS配置验证成功，符合NRCS配置预期", nodeName)
	return nil
}

// verifyExistingNodesNRS 验证已有节点的NRS配置
func (c *checkNrcsNodeScale) verifyExistingNodesNRS(ctx context.Context) error {
	// 1. 验证节点组内的节点
	if len(c.nodeGroupNodes) < 2 {
		logger.Warnf(ctx, "节点组内的节点数量不足2个，将跳过部分验证")
	} else {
		// 随机选择两个不是新节点的节点进行验证
		var selectedNodes []string
		for _, nodeName := range c.nodeGroupNodes {
			if nodeName != c.expandedNode {
				selectedNodes = append(selectedNodes, nodeName)
				if len(selectedNodes) >= 2 {
					break
				}
			}
		}

		if len(selectedNodes) == 0 {
			logger.Warnf(ctx, "未找到合适的节点组内节点进行验证")
		} else {
			for _, nodeName := range selectedNodes {
				logger.Infof(ctx, "验证节点组内的节点 %s...", nodeName)
				if err := c.verifyNodeNRS(ctx, nodeName, true); err != nil {
					return fmt.Errorf("验证节点组内节点 %s 的NRS配置失败: %v", nodeName, err)
				}
			}
		}
	}

	// 2. 验证节点组外的节点
	if len(c.nonNodeGroupNodes) == 0 {
		logger.Warnf(ctx, "没有节点组外的节点，将跳过相关验证")
	} else {
		// 随机选择一个节点组外的节点
		randomIndex := rand.Intn(len(c.nonNodeGroupNodes))
		nodeName := c.nonNodeGroupNodes[randomIndex]

		logger.Infof(ctx, "验证节点组外的节点 %s...", nodeName)
		if err := c.verifyNodeNRS(ctx, nodeName, false); err != nil {
			return fmt.Errorf("验证节点组外节点 %s 的NRS配置失败: %v", nodeName, err)
		}
	}

	return nil
}

// verifyNodeNRS 验证节点的NRS配置
func (c *checkNrcsNodeScale) verifyNodeNRS(ctx context.Context, nodeName string, shouldMatchNRCS bool) error {
	// 获取节点NRS资源
	nrs, err := c.base.KubeClient.GetNrs(ctx, nodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取节点 %s 的NRS资源失败: %v", nodeName, err)
	}

	// 输出关键配置
	logger.Infof(ctx, "节点 %s 的NRS配置:", nodeName)
	logger.Infof(ctx, "  子网配置: %v", nrs.Spec.Eni.SubnetIds)
	logger.Infof(ctx, "  eni.preAllocateENI: %d", nrs.Spec.Eni.PreAllocateENI)
	logger.Infof(ctx, "  eni.burstableMehrfachENI: %d", nrs.Spec.Eni.BurstableMehrfachENI)
	logger.Infof(ctx, "  ipam.max-above-watermark: %d", nrs.Spec.Ipam.MaxAboveWatermark)
	logger.Infof(ctx, "  ipam.min-allocate: %d", nrs.Spec.Ipam.MinAllocate)
	logger.Infof(ctx, "  ipam.pre-allocate: %d", nrs.Spec.Ipam.PreAllocate)

	// 根据是否应匹配NRCS配置进行不同的验证
	if shouldMatchNRCS {
		// 获取NRCS中设置的值
		var nrcsResource *types.NetResourceConfigSet
		nrcsResource, err := c.base.KubeClient.GetNrcs(ctx, nrcsResourceName, &kube.GetOptions{})
		if err != nil {
			return fmt.Errorf("获取NRCS资源失败: %v", err)
		}

		// 记录NRCS中的值
		logger.Infof(ctx, "  NRCS配置值:")
		logger.Infof(ctx, "    burstable-mehrfach-eni: %d", *nrcsResource.Spec.AgentConfig.BurstableMehrfachENI)
		logger.Infof(ctx, "    eni-pre-allocate-num: %d", *nrcsResource.Spec.AgentConfig.IPPoolPreAllocateENI)
		logger.Infof(ctx, "    ippool-min-allocate: %d", *nrcsResource.Spec.AgentConfig.IPPoolMinAllocate)
		logger.Infof(ctx, "    ippool-pre-allocate: %d", *nrcsResource.Spec.AgentConfig.IPPoolPreAllocate)
		logger.Infof(ctx, "    ippool-max-above-watermark: %d", *nrcsResource.Spec.AgentConfig.IPPoolMaxAboveWatermark)

		// 应该匹配NRCS配置的节点
		if !containsString(nrs.Spec.Eni.SubnetIds, c.config.SubnetId) {
			return fmt.Errorf("节点 %s 的NRS子网配置未包含预期的子网ID %s", nodeName, c.config.SubnetId)
		}

		if nrs.Spec.Eni.PreAllocateENI != *nrcsResource.Spec.AgentConfig.IPPoolPreAllocateENI {
			return fmt.Errorf("节点 %s 的NRS eni.preAllocateENI配置不正确，期望值%d，实际值%d",
				nodeName, *nrcsResource.Spec.AgentConfig.IPPoolPreAllocateENI, nrs.Spec.Eni.PreAllocateENI)
		}

		if nrs.Spec.Eni.BurstableMehrfachENI != *nrcsResource.Spec.AgentConfig.BurstableMehrfachENI {
			return fmt.Errorf("节点 %s 的NRS eni.burstableMehrfachENI配置不正确，期望值%d，实际值%d",
				nodeName, *nrcsResource.Spec.AgentConfig.BurstableMehrfachENI, nrs.Spec.Eni.BurstableMehrfachENI)
		}

		if nrs.Spec.Ipam.MaxAboveWatermark != *nrcsResource.Spec.AgentConfig.IPPoolMaxAboveWatermark {
			return fmt.Errorf("节点 %s 的NRS ipam.max-above-watermark配置不正确，期望值%d，实际值%d",
				nodeName, *nrcsResource.Spec.AgentConfig.IPPoolMaxAboveWatermark, nrs.Spec.Ipam.MaxAboveWatermark)
		}

		if nrs.Spec.Ipam.MinAllocate != *nrcsResource.Spec.AgentConfig.IPPoolMinAllocate {
			return fmt.Errorf("节点 %s 的NRS ipam.min-allocate配置不正确，期望值%d，实际值%d",
				nodeName, *nrcsResource.Spec.AgentConfig.IPPoolMinAllocate, nrs.Spec.Ipam.MinAllocate)
		}

		if nrs.Spec.Ipam.PreAllocate != *nrcsResource.Spec.AgentConfig.IPPoolPreAllocate {
			return fmt.Errorf("节点 %s 的NRS ipam.pre-allocate配置不正确，期望值%d，实际值%d",
				nodeName, *nrcsResource.Spec.AgentConfig.IPPoolPreAllocate, nrs.Spec.Ipam.PreAllocate)
		}

		logger.Infof(ctx, "节点 %s 的NRS配置验证成功，符合NRCS配置预期", nodeName)
	} else {
		// 不应匹配NRCS配置的节点，应该使用集群默认配置
		// 获取configmap中的默认值
		ceedData, ok := c.configMapData["cced"]
		if !ok {
			return fmt.Errorf("ConfigMap中没有cced数据")
		}

		// 验证子网配置，这里略过具体检查，因为默认配置的子网可能有多个

		// 获取configmap中的默认值, 注意参数名称需要与configmap中的匹配
		burstableValue := getConfigValue(ceedData, "burstable-mehrfach-eni")
		preAllocateENIValue := getConfigValue(ceedData, "eni-pre-allocate-num")
		minAllocateValue := getConfigValue(ceedData, "ippool-min-allocate-ips") // 修改为 ippool-min-allocate-ips
		preAllocateValue := getConfigValue(ceedData, "ippool-pre-allocate")
		maxAboveWatermarkValue := getConfigValue(ceedData, "ippool-max-above-watermark")

		// 记录期望值
		logger.Infof(ctx, "  ConfigMap默认配置值:")
		logger.Infof(ctx, "    burstable-mehrfach-eni: %s", burstableValue)
		logger.Infof(ctx, "    eni-pre-allocate-num: %s", preAllocateENIValue)
		logger.Infof(ctx, "    ippool-min-allocate-ips: %s", minAllocateValue)
		logger.Infof(ctx, "    ippool-pre-allocate: %s", preAllocateValue)
		logger.Infof(ctx, "    ippool-max-above-watermark: %s", maxAboveWatermarkValue)

		// 将字符串值转换为整数
		defaultBurstable := getConfigValueInt(ceedData, "burstable-mehrfach-eni")
		defaultPreAllocateENI := getConfigValueInt(ceedData, "eni-pre-allocate-num")
		defaultMinAllocate := getConfigValueInt(ceedData, "ippool-min-allocate-ips") // 修改为 ippool-min-allocate-ips
		defaultPreAllocate := getConfigValueInt(ceedData, "ippool-pre-allocate")
		defaultMaxAboveWatermark := getConfigValueInt(ceedData, "ippool-max-above-watermark")

		// 只验证在configmap中显式设置的参数
		if burstableValue != "未找到" {
			if nrs.Spec.Eni.BurstableMehrfachENI != defaultBurstable {
				return fmt.Errorf("节点 %s 的NRS burstableMehrfachENI配置不符合默认值，期望%d，实际%d",
					nodeName, defaultBurstable, nrs.Spec.Eni.BurstableMehrfachENI)
			}
		} else {
			logger.Infof(ctx, "  ConfigMap未设置burstable-mehrfach-eni，跳过验证")
		}

		if preAllocateENIValue != "未找到" {
			if nrs.Spec.Eni.PreAllocateENI != defaultPreAllocateENI {
				return fmt.Errorf("节点 %s 的NRS preAllocateENI配置不符合默认值，期望%d，实际%d",
					nodeName, defaultPreAllocateENI, nrs.Spec.Eni.PreAllocateENI)
			}
		} else {
			logger.Infof(ctx, "  ConfigMap未设置eni-pre-allocate-num，跳过验证")
		}

		if minAllocateValue != "未找到" {
			if nrs.Spec.Ipam.MinAllocate != defaultMinAllocate {
				return fmt.Errorf("节点 %s 的NRS minAllocate配置不符合默认值，期望%d，实际%d",
					nodeName, defaultMinAllocate, nrs.Spec.Ipam.MinAllocate)
			}
		} else {
			logger.Infof(ctx, "  ConfigMap未设置ippool-min-allocate-ips，跳过验证")
		}

		if preAllocateValue != "未找到" {
			if nrs.Spec.Ipam.PreAllocate != defaultPreAllocate {
				return fmt.Errorf("节点 %s 的NRS preAllocate配置不符合默认值，期望%d，实际%d",
					nodeName, defaultPreAllocate, nrs.Spec.Ipam.PreAllocate)
			}
		} else {
			logger.Infof(ctx, "  ConfigMap未设置ippool-pre-allocate，跳过验证")
		}

		if maxAboveWatermarkValue != "未找到" {
			if nrs.Spec.Ipam.MaxAboveWatermark != defaultMaxAboveWatermark {
				return fmt.Errorf("节点 %s 的NRS maxAboveWatermark配置不符合默认值，期望%d，实际%d",
					nodeName, defaultMaxAboveWatermark, nrs.Spec.Ipam.MaxAboveWatermark)
			}
		} else {
			logger.Infof(ctx, "  ConfigMap未设置ippool-max-above-watermark，跳过验证")
		}

		logger.Infof(ctx, "节点 %s 的NRS配置验证成功，符合默认配置预期", nodeName)
	}

	return nil
}

// containsString 检查字符串切片是否包含特定字符串
func containsString(slice []string, s string) bool {
	for _, item := range slice {
		if item == s {
			return true
		}
	}
	return false
}

// verifyNRCSPriority 验证高优先级NRCS的优先级机制
func (c *checkNrcsNodeScale) verifyNRCSPriority(ctx context.Context) error {
	// 随机选择一个节点组内的节点进行验证
	if len(c.nodeGroupNodes) == 0 {
		return fmt.Errorf("未找到节点组内的节点")
	}

	// 获取高优先级NRCS资源
	highPriorityNrcs, err := c.base.KubeClient.GetNrcs(ctx, nrcsResourceNameHighPriority, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取高优先级NRCS资源失败: %v", err)
	}

	// 输出高优先级NRCS的预期值
	logger.Infof(ctx, "高优先级NRCS配置的预期值:")
	logger.Infof(ctx, "  优先级: %d", highPriorityNrcs.Spec.Priority)
	logger.Infof(ctx, "  burstable-mehrfach-eni: %d", *highPriorityNrcs.Spec.AgentConfig.BurstableMehrfachENI)
	logger.Infof(ctx, "  eni-pre-allocate-num: %d", *highPriorityNrcs.Spec.AgentConfig.IPPoolPreAllocateENI)
	logger.Infof(ctx, "  ippool-min-allocate: %d", *highPriorityNrcs.Spec.AgentConfig.IPPoolMinAllocate)
	logger.Infof(ctx, "  ippool-pre-allocate: %d", *highPriorityNrcs.Spec.AgentConfig.IPPoolPreAllocate)
	logger.Infof(ctx, "  ippool-max-above-watermark: %d", *highPriorityNrcs.Spec.AgentConfig.IPPoolMaxAboveWatermark)

	var selectedNodes []string
	for _, nodeName := range c.nodeGroupNodes {
		selectedNodes = append(selectedNodes, nodeName)
		if len(selectedNodes) >= 2 {
			break
		}
	}

	if len(selectedNodes) == 0 {
		return fmt.Errorf("未找到合适的节点进行验证")
	}

	for _, nodeName := range selectedNodes {
		logger.Infof(ctx, "验证节点 %s 是否应用了高优先级NRCS配置...", nodeName)

		// 获取节点NRS资源
		nrs, err := c.base.KubeClient.GetNrs(ctx, nodeName, &kube.GetOptions{})
		if err != nil {
			return fmt.Errorf("获取节点 %s 的NRS资源失败: %v", nodeName, err)
		}

		// 验证关键配置
		logger.Infof(ctx, "节点 %s 的NRS配置:", nodeName)
		logger.Infof(ctx, "  eni.burstableMehrfachENI: %d", nrs.Spec.Eni.BurstableMehrfachENI)
		logger.Infof(ctx, "  eni.preAllocateENI: %d", nrs.Spec.Eni.PreAllocateENI)
		logger.Infof(ctx, "  ipam.min-allocate: %d", nrs.Spec.Ipam.MinAllocate)
		logger.Infof(ctx, "  ipam.pre-allocate: %d", nrs.Spec.Ipam.PreAllocate)
		logger.Infof(ctx, "  ipam.max-above-watermark: %d", nrs.Spec.Ipam.MaxAboveWatermark)

		// 验证高优先级NRCS的配置已生效
		if nrs.Spec.Eni.BurstableMehrfachENI != *highPriorityNrcs.Spec.AgentConfig.BurstableMehrfachENI {
			return fmt.Errorf("节点 %s 未应用高优先级NRCS的burstableMehrfachENI配置，期望值%d，实际值%d",
				nodeName, *highPriorityNrcs.Spec.AgentConfig.BurstableMehrfachENI, nrs.Spec.Eni.BurstableMehrfachENI)
		}

		if nrs.Spec.Eni.PreAllocateENI != *highPriorityNrcs.Spec.AgentConfig.IPPoolPreAllocateENI {
			return fmt.Errorf("节点 %s 未应用高优先级NRCS的preAllocateENI配置，期望值%d，实际值%d",
				nodeName, *highPriorityNrcs.Spec.AgentConfig.IPPoolPreAllocateENI, nrs.Spec.Eni.PreAllocateENI)
		}

		if nrs.Spec.Ipam.MinAllocate != *highPriorityNrcs.Spec.AgentConfig.IPPoolMinAllocate {
			return fmt.Errorf("节点 %s 未应用高优先级NRCS的min-allocate配置，期望值%d，实际值%d",
				nodeName, *highPriorityNrcs.Spec.AgentConfig.IPPoolMinAllocate, nrs.Spec.Ipam.MinAllocate)
		}

		if nrs.Spec.Ipam.PreAllocate != *highPriorityNrcs.Spec.AgentConfig.IPPoolPreAllocate {
			return fmt.Errorf("节点 %s 未应用高优先级NRCS的pre-allocate配置，期望值%d，实际值%d",
				nodeName, *highPriorityNrcs.Spec.AgentConfig.IPPoolPreAllocate, nrs.Spec.Ipam.PreAllocate)
		}

		if nrs.Spec.Ipam.MaxAboveWatermark != *highPriorityNrcs.Spec.AgentConfig.IPPoolMaxAboveWatermark {
			return fmt.Errorf("节点 %s 未应用高优先级NRCS的max-above-watermark配置，期望值%d，实际值%d",
				nodeName, *highPriorityNrcs.Spec.AgentConfig.IPPoolMaxAboveWatermark, nrs.Spec.Ipam.MaxAboveWatermark)
		}

		logger.Infof(ctx, "节点 %s 成功应用了高优先级NRCS配置", nodeName)
	}

	return nil
}

// createInstanceGroup 创建测试用的节点组
func (c *checkNrcsNodeScale) createInstanceGroup(ctx context.Context) error {
	// 先检查集群中是否已有节点组
	resp, err := c.base.CCEClient.ListInstanceGroups(ctx, c.base.ClusterID, nil, nil)
	if err != nil {
		return fmt.Errorf("获取节点组列表失败: %v", err)
	}

	// 如果已有节点组，则无需创建
	if len(resp.Page.List) > 0 {
		for _, ig := range resp.Page.List {
			if ig.Status.ReadyReplicas > 0 {
				logger.Infof(ctx, "集群中已有可用节点组 %s (副本数: %d)，无需创建新节点组",
					ig.Spec.CCEInstanceGroupID, ig.Status.ReadyReplicas)

				// 设置节点组ID和标记为非创建
				c.instanceGroupID = ig.Spec.CCEInstanceGroupID
				c.createdNodeGroup = false

				// 记录原始副本数用于清理恢复
				c.originalReplicas = ig.Spec.Replicas
				logger.Infof(ctx, "记录原始节点组副本数: %d", c.originalReplicas)

				return nil
			}
		}
	}

	// 需要创建新节点组
	logger.Infof(ctx, "未找到可用节点组，开始创建测试用节点组...")

	// 获取集群中已有节点的信息，用于复用配置
	instances, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
		ClusterRole: types.ClusterRoleNode,
	}, nil)
	if err != nil {
		return fmt.Errorf("获取集群节点信息失败: %v", err)
	}
	if len(instances.InstancePage.InstanceList) == 0 {
		return fmt.Errorf("集群中没有可用的节点，无法获取配置信息")
	}

	// 使用第一个节点的配置信息
	instance := instances.InstancePage.InstanceList[0]

	// 获取镜像ID
	imageID := instance.Spec.ImageID
	if imageID == "" {
		return fmt.Errorf("获取到的镜像ID为空，无法创建节点组")
	}
	logger.Infof(ctx, "将使用已有节点的镜像ID: %s", imageID)

	// 获取集群额外信息，找到可用的子网
	clusterExtraInfo, err := c.base.CCEHostClient.GetClusterExtraInfo(ctx, c.base.ClusterID, nil)
	if err != nil {
		return fmt.Errorf("获取集群额外信息失败: %v", err)
	}

	var nodeSubnetID string
	for _, subnet := range clusterExtraInfo.Subnets {
		if subnet.SubnetCIDR != "" {
			nodeSubnetID = subnet.SubnetID
			break
		}
	}

	if nodeSubnetID == "" {
		return fmt.Errorf("未找到可用的子网")
	}

	logger.Infof(ctx, "将使用子网 %s 创建节点组", nodeSubnetID)

	// 查询集群信息
	cluster, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		return fmt.Errorf("获取集群信息失败: %v", err)
	}

	// 从集群信息获取VPCID
	vpcID := cluster.Cluster.Spec.VPCID
	logger.Infof(ctx, "将使用VPC %s 创建节点组", vpcID)

	// 创建节点组
	igName := fmt.Sprintf("test-nrcs-node-scale-%d", time.Now().Unix())
	createReq := &ccev2.CreateInstanceGroupRequest{
		InstanceGroupSpec: types.InstanceGroupSpec{
			InstanceGroupName: igName,
			Replicas:          1,
			InstanceTemplate: types.InstanceTemplate{
				InstanceSpec: types.InstanceSpec{
					ClusterRole:  types.ClusterRoleNode,
					MachineType:  instance.Spec.MachineType,
					InstanceType: instance.Spec.InstanceType,
					VPCConfig: types.VPCConfig{
						VPCID:       vpcID,
						VPCSubnetID: nodeSubnetID,
						SecurityGroup: types.SecurityGroup{
							EnableCCERequiredSecurityGroup: true,
						},
					},
					InstanceResource: types.InstanceResource{
						CPU: instance.Spec.InstanceResource.CPU,
						MEM: instance.Spec.InstanceResource.MEM,
					},
					ImageID:       imageID,
					AdminPassword: "Test123456!",
				},
			},
		},
	}

	// 发送创建请求
	createResp, err := c.base.CCEClient.CreateInstanceGroup(ctx, c.base.ClusterID, createReq, nil)
	if err != nil {
		return fmt.Errorf("创建节点组失败: %v", err)
	}

	// 保存节点组ID
	c.instanceGroupID = createResp.InstanceGroupID
	c.createdNodeGroup = true
	logger.Infof(ctx, "成功创建节点组 %s (ID: %s)", igName, c.instanceGroupID)

	// 等待节点组就绪
	logger.Infof(ctx, "等待节点组中的节点就绪...")

	// 创建instanceGroup资源对象并等待就绪
	ig, err := resource.NewInstanceGroup(ctx, c.base, c.instanceGroupID, 10*time.Second, 15*time.Minute)
	if err != nil {
		return fmt.Errorf("创建instanceGroup资源对象失败: %v", err)
	}

	if err := ig.CheckResource(ctx); err != nil {
		return fmt.Errorf("等待节点组就绪失败: %v", err)
	}

	logger.Infof(ctx, "节点组 %s 已就绪", c.instanceGroupID)

	// 将创建的节点组添加到资源列表
	c.resources = append(c.resources, cases.Resource{
		CaseName: CheckNrcsNodeScaleCaseName,
		Type:     cases.ResourceTypeCCEInstanceGroup,
		ID:       c.instanceGroupID,
	})

	return nil
}

// prepareNetworkConfig 修改网络配置并重启网络组件
func (c *checkNrcsNodeScale) prepareNetworkConfig(ctx context.Context) error {
	// 获取ConfigMap
	configMap, err := c.base.KubeClient.ClientSet.CoreV1().ConfigMaps(networkNamespace).Get(ctx, networkConfigMapName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取网络配置ConfigMap失败: %v", err)
	}

	// 获取当前配置
	ccedConfig, ok := configMap.Data["cced"]
	if !ok {
		return fmt.Errorf("ConfigMap中不存在cced配置")
	}

	logger.Infof(ctx, "修改前burstable-mehrfach-eni配置值: %s", getConfigValue(ccedConfig, "burstable-mehrfach-eni"))

	// 修改burstable-mehrfach-eni为0
	lines := strings.Split(ccedConfig, "\n")
	configModified := false
	for i, line := range lines {
		if strings.HasPrefix(strings.TrimSpace(line), "burstable-mehrfach-eni:") {
			parts := strings.SplitN(line, ":", 2)
			if len(parts) == 2 {
				oldValue := strings.TrimSpace(parts[1])
				lines[i] = parts[0] + ": 0"
				logger.Infof(ctx, "修改配置 burstable-mehrfach-eni: 原值=%s, 新值=0", oldValue)
				configModified = true
				break
			}
		}
	}

	if !configModified {
		// 如果没有找到配置项，则添加到末尾
		lines = append(lines, "burstable-mehrfach-eni: 0")
		logger.Infof(ctx, "未找到burstable-mehrfach-eni配置项，已添加新配置: burstable-mehrfach-eni: 0")
	}

	// 更新ConfigMap
	configMap.Data["cced"] = strings.Join(lines, "\n")
	_, err = c.base.KubeClient.ClientSet.CoreV1().ConfigMaps(networkNamespace).Update(ctx, configMap, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新ConfigMap失败: %v", err)
	}

	logger.Infof(ctx, "成功修改ConfigMap中的burstable-mehrfach-eni配置为0")

	// 重启网络组件
	if err := c.restartNetworkComponents(ctx); err != nil {
		return fmt.Errorf("重启网络组件失败: %v", err)
	}

	return nil
}

// restartNetworkComponents 重启网络组件
func (c *checkNrcsNodeScale) restartNetworkComponents(ctx context.Context) error {
	logger.Infof(ctx, "开始重启网络组件...")

	// 重启网络agent DaemonSet
	if err := c.restartNetworkAgentDaemonSet(ctx); err != nil {
		return fmt.Errorf("重启网络agent DaemonSet失败: %v", err)
	}

	// 等待组件就绪
	if err := c.waitForNetworkComponentReady(ctx); err != nil {
		return fmt.Errorf("等待网络组件就绪失败: %v", err)
	}

	logger.Infof(ctx, "网络组件重启完成")
	return nil
}

// restartNetworkAgentDaemonSet 重启网络agent DaemonSet
func (c *checkNrcsNodeScale) restartNetworkAgentDaemonSet(ctx context.Context) error {
	// 获取DaemonSet
	ds, err := c.base.KubeClient.ClientSet.AppsV1().DaemonSets(networkNamespace).Get(ctx, networkAgentName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取DaemonSet %s 失败: %v", networkAgentName, err)
	}

	// 添加或更新重启注解
	if ds.Spec.Template.Annotations == nil {
		ds.Spec.Template.Annotations = make(map[string]string)
	}

	// 使用当前时间作为重启注解值
	restartTimestamp := fmt.Sprintf("restart-at-%d", time.Now().Unix())
	ds.Spec.Template.Annotations["kubectl.kubernetes.io/restartedAt"] = restartTimestamp

	logger.Infof(ctx, "更新 DaemonSet %s 添加重启注解: %s", networkAgentName, restartTimestamp)

	// 更新DaemonSet
	_, err = c.base.KubeClient.ClientSet.AppsV1().DaemonSets(networkNamespace).Update(ctx, ds, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新DaemonSet失败: %v", err)
	}

	return nil
}

// waitForNetworkComponentReady 等待网络组件就绪
func (c *checkNrcsNodeScale) waitForNetworkComponentReady(ctx context.Context) error {
	logger.Infof(ctx, "等待网络组件就绪...")

	maxWaitTime := 5 * time.Minute
	checkInterval := 5 * time.Second
	waitStart := time.Now()

	for {
		if time.Since(waitStart) > maxWaitTime {
			return fmt.Errorf("等待网络组件就绪超时")
		}

		// 获取DaemonSet状态
		ds, err := c.base.KubeClient.ClientSet.AppsV1().DaemonSets(networkNamespace).Get(ctx, networkAgentName, metav1.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取DaemonSet状态失败: %v，将继续重试", err)
		} else {
			logger.Infof(ctx, "DaemonSet状态: 期望数=%d, 当前数=%d, 就绪数=%d, 更新数=%d, 可用数=%d",
				ds.Status.DesiredNumberScheduled,
				ds.Status.CurrentNumberScheduled,
				ds.Status.NumberReady,
				ds.Status.UpdatedNumberScheduled,
				ds.Status.NumberAvailable)

			// 当所有期望的Pod都已更新且就绪时，重启完成
			if ds.Status.UpdatedNumberScheduled == ds.Status.DesiredNumberScheduled &&
				ds.Status.NumberReady == ds.Status.DesiredNumberScheduled {
				logger.Infof(ctx, "网络组件就绪")

				// 等待一个短暂的时间确保网络组件稳定
				waitTime := 10 * time.Second
				logger.Infof(ctx, "等待 %s 让组件稳定...", waitTime)
				time.Sleep(waitTime)
				return nil
			}
		}

		logger.Infof(ctx, "网络组件未就绪，等待 %s 后重新检查...", checkInterval)
		time.Sleep(checkInterval)
	}
}
