/*
modification history
--------------------
2024/9/9, by shimingming<PERSON>, create
*/
/*
DESCRIPTION
场景：测试集群所有节点上的ip分配情况是否和cep资源种的数量一致。
1. 固定节点扩容deployment达到节点的eni可申请ip的上限且不超过节点的pod上限（一般为128），对比cep的数量。
2. 扩容到上限+1测试ip不足失败的情况。
3. 缩容deployment到0个副本，对比cep的数量。
4. 校验所有网卡没有被更名。
*/

package network

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"time"

	v1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/yaml"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases/plugin"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CheckIPAllocation cases.CaseName = "CheckIPAllocation"
)

const (
	testIPAllocationDeploymentName = "test-ip-allocation"
)

type checkIPAllocation struct {
	base *cases.BaseClient
}

func init() {
	cases.AddCase(context.TODO(), CheckIPAllocation, NewCheckIPAllocation)
}

func NewCheckIPAllocation(ctx context.Context) cases.Interface {
	return &checkIPAllocation{}
}

func (c *checkIPAllocation) Name() cases.CaseName {
	return CheckIPAllocation
}

func (c *checkIPAllocation) Desc() string {
	return "检测集群ip分配"
}

func (c *checkIPAllocation) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	return
}

func (c *checkIPAllocation) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID
	// 获取集群额外的信息
	clusterExtraInfo, getExtraInfoErr := c.base.CCEHostClient.GetClusterExtraInfo(ctx, clusterID, nil)
	if getExtraInfoErr != nil {
		err = fmt.Errorf("CCEHostClient.GetClusterExtraInfo failed, err: %v", getExtraInfoErr)
		return
	}
	if len(clusterExtraInfo.ENISubnets) == 0 {
		err = errors.New("cluster has no eni subnets")
		return
	}
	// 随机选择一个节点做测试
	nodeList, listNodeErr := c.base.KubeClient.ListNode(ctx, &kube.ListOptions{
		LabelSelector: map[string]string{
			"cluster-role": "node",
		},
	})
	if listNodeErr != nil {
		err = fmt.Errorf("list node failed, err: %v", listNodeErr)
		return
	}

	for _, node := range nodeList.Items {
		hostName := node.Labels["kubernetes.io/hostname"]
		logger.Infof(ctx, "node: %s", hostName)

		// 获取这个节点上已经存在的pod，计算出hostNetwork pod数量，因为这些pod不会实际消耗掉eni分配出来的ip，但会消耗掉节点的maxPod数量
		pods, listPodErr := c.base.KubeClient.ListPod(ctx, metav1.NamespaceAll, &kube.ListOptions{
			FieldSelector: map[string]string{
				"spec.nodeName": node.Name,
			},
		})
		if listPodErr != nil {
			err = fmt.Errorf("list pod failed, err: %v", listPodErr)
			return
		}

		var hostNetworkPodNum int
		for _, pod := range pods.Items {
			if pod.Spec.HostNetwork {
				hostNetworkPodNum++
			}
		}

		nrs, getNrsErr := c.base.KubeClient.GetNrs(ctx, node.Name, &kube.GetOptions{})
		if getNrsErr != nil {
			err = fmt.Errorf("get nrs failed, err: %v", getNrsErr)
			return
		}
		nrsEni := nrs.Spec.Eni

		// 节点剩余可分配的ip数量 = 最大eni数量 * 每个eni可分配ip数量 - eni自身占用的ip - 实际已经占用的ip
		// 节点最大可被调度的pod数量 = 节点maxPods - 实际已经占用的ip - hostNetwork pod数量
		// 节点当前的极限pod调度数量 = min(节点剩余可分配的ip数量, 节点最大可被调度的pod数量)
		var maxDeploymentReplicas int
		unUsedEniIPNum := nrsEni.MaxIPsPerENI*nrsEni.MaxAllocateENI - nrsEni.MaxAllocateENI - len(nrs.Status.Ipam.Used)
		nodeMaxPodNum := int(node.Status.Capacity.Pods().Value()) - len(nrs.Status.Ipam.Used) - hostNetworkPodNum
		maxDeploymentReplicas = unUsedEniIPNum
		if maxDeploymentReplicas > nodeMaxPodNum {
			maxDeploymentReplicas = nodeMaxPodNum
		}
		// TODO 为了降低case的复杂度，这里假设容器子网是足够的，足够创建eni出来

		// 首先清理之前部署的deployment，防止之前的失败case导致没有进行清理影响下一次测试
		err = c.Clean(ctx)
		if err != nil {
			return
		}

		// 1. 测试正常部署到上限，检查cep数量
		logger.Infof(ctx, "start to test max replicas %d", maxDeploymentReplicas)
		err = c.applyDeployment(ctx, int32(maxDeploymentReplicas), hostName)
		if err != nil {
			return
		}
		err = c.ensureDeploymentRunning(ctx, time.Second*5, time.Minute*6)
		if err != nil {
			err = fmt.Errorf("scale deployment to max replicas %d failed, err: %v", maxDeploymentReplicas, err)
			return
		}
		err = c.checkCep(ctx, maxDeploymentReplicas)
		if err != nil {
			return
		}

		// 2. 扩容到上限 + 1，预期是失败的
		overMaxDeploymentReplicas := maxDeploymentReplicas + 1
		logger.Infof(ctx, "start to test over max replicas %d", overMaxDeploymentReplicas)
		err = c.scaleDeployment(ctx, int32(overMaxDeploymentReplicas))
		if err != nil {
			return
		}
		err = c.ensureDeploymentRunning(ctx, time.Second*5, time.Second*20)
		if err == nil {
			err = fmt.Errorf("scale deployment to replicas %d success, expect failed, max replicas: %d", overMaxDeploymentReplicas, maxDeploymentReplicas)
			return
		}
		err = nil

		// 3. 缩容到0，预期是成功的，检查cep数量
		logger.Infof(ctx, "start to test scale down replicas to 0")
		err = c.scaleDeployment(ctx, 0)
		if err != nil {
			return
		}
		err = c.ensureDeploymentRunning(ctx, time.Second*5, time.Minute*3)
		if err != nil {
			err = fmt.Errorf("scale deployment to replicas 0 failed, err: %v", err)
			return
		}
		err = c.checkCep(ctx, 0)
		if err != nil {
			return
		}

		logger.Infof(ctx, "start to delete deployment %s", testIPAllocationDeploymentName)
		err = c.base.KubeClient.EnsureDeploymentDeletedAppsV1(ctx,
			metav1.NamespaceDefault,
			testIPAllocationDeploymentName,
			&kube.CheckOptions{
				Interval: time.Second * 5,
				Timeout:  time.Minute * 3,
			},
		)
		if err != nil {
			return
		}
	}

	// 4. 校验所有网卡没有被更名。
	err = plugin.CheckNetworkDevice(ctx, c.base.KubeClient)
	if err != nil {
		return
	}

	return
}

func (c *checkIPAllocation) Clean(ctx context.Context) error {
	return nil
}

func (c *checkIPAllocation) Continue(ctx context.Context) bool {
	return true
}

func (c *checkIPAllocation) ConfigFormat() string {
	return ""
}

func (c *checkIPAllocation) getSubnetInfo(ctx context.Context, subnetID string) (availableIPNum int, err error) {
	logger.Infof(ctx, "start to get subnet available IP num for `%s`", subnetID)
	subnet, descSubnetErr := c.base.VPCClient.DescribeSubnet(ctx, subnetID, nil)
	if descSubnetErr != nil {
		err = fmt.Errorf("describe subnet `%s` failed, err: %v", subnetID, descSubnetErr)
		return
	}
	availableIPNum = subnet.AvailableIPNum
	logger.Infof(ctx, "get subnet available IP num for `%s`, result: %d", subnetID, availableIPNum)
	return
}

func (c *checkIPAllocation) sampleNode(nodeList []corev1.Node) (node corev1.Node) {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	randomIndex := r.Intn(len(nodeList))
	return nodeList[randomIndex]
}

func (c *checkIPAllocation) applyDeployment(ctx context.Context, replicas int32, hostName string) error {
	logger.Infof(ctx, "start to deploy test deployment, replicas: %d", replicas)
	var terminationGracePeriodSeconds int64
	deployment := v1.Deployment{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Deployment",
			APIVersion: "apps/v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      testIPAllocationDeploymentName,
			Namespace: corev1.NamespaceDefault,
			Labels: map[string]string{
				"app": testIPAllocationDeploymentName,
			},
		},
		Spec: v1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": testIPAllocationDeploymentName,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": testIPAllocationDeploymentName,
					},
				},
				Spec: corev1.PodSpec{
					TerminationGracePeriodSeconds: &terminationGracePeriodSeconds,
					NodeSelector: map[string]string{
						"kubernetes.io/hostname": hostName,
					},
					Containers: []corev1.Container{{
						Name:            testIPAllocationDeploymentName,
						Image:           "registry.baidubce.com/cce-public/busybox",
						ImagePullPolicy: corev1.PullIfNotPresent,
						Command:         []string{"sleep", "3600"},
					}},
				},
			},
		},
	}
	out, err := yaml.Marshal(&deployment)
	if err != nil {
		return fmt.Errorf("marshal deployment failed, err: %v", err)
	}
	deploymentYaml := string(out)
	_, err = c.base.AppClient.PostAppResource(ctx, c.base.ClusterID, testIPAllocationDeploymentName, deploymentYaml, corev1.NamespaceDefault, nil)
	if err != nil {
		return fmt.Errorf("create deployment failed, err: %v", err)
	}
	return nil
}

func (c *checkIPAllocation) ensureDeploymentRunning(ctx context.Context, interval, timeout time.Duration) error {
	// 等待所有副本running
	logger.Infof(ctx, "start to ensure deployment %s running", testIPAllocationDeploymentName)
	err := resource.WaitForFunc(ctx, func(ctx context.Context) (err error) {
		deploymentResource, getErr := c.base.AppClient.GetAppDeploymentByName(ctx, c.base.ClusterID, testIPAllocationDeploymentName, corev1.NamespaceDefault, nil)
		if getErr != nil {
			err = fmt.Errorf("get deployment Resource: %v failed, err: %v", testIPAllocationDeploymentName, err)
			return
		}
		if deploymentResource.Status != "Running" || deploymentResource.StatusInfo.Available != deploymentResource.StatusInfo.Replicas {
			err = fmt.Errorf("deployment status: %v, available: %d, expect replicas: %d", deploymentResource.Status,
				deploymentResource.StatusInfo.Available, deploymentResource.StatusInfo.Replicas)
			return
		}
		// 其实更加准确的是判断事件里面是否存在 ENICapacityExceed，更加能确认是ip分配问题造成的状态没达到预期
		return
	}, interval, timeout)
	if err != nil {
		return fmt.Errorf("wait for deployment running failed, err: %v", err)
	}
	logger.Infof(ctx, "deployment %s is running", testIPAllocationDeploymentName)
	return nil
}

func (c *checkIPAllocation) scaleDeployment(ctx context.Context, replicas int32) (err error) {
	scale, getScaleErr := c.base.KubeClient.GetDeploymentScaleAppsV1(ctx, corev1.NamespaceDefault, testIPAllocationDeploymentName, &kube.GetOptions{})
	if getScaleErr != nil {
		err = fmt.Errorf("get deployment scale failed, err: %v", getScaleErr)
		return
	}
	scale.Spec.Replicas = replicas
	_, err = c.base.KubeClient.ScaleDeploymentAppsV1(ctx, corev1.NamespaceDefault, testIPAllocationDeploymentName, scale)
	if err != nil {
		err = fmt.Errorf("scale deployment to replicas %d failed, err: %v", replicas, err)
		return
	}
	return
}

func (c *checkIPAllocation) checkCep(ctx context.Context, expectedNum int) (err error) {
	logger.Infof(ctx, "start to check cep")
	ceps, listCepErr := c.base.KubeClient.ListCep(ctx, metav1.NamespaceDefault, &kube.ListOptions{
		LabelSelector: map[string]string{
			"app": testIPAllocationDeploymentName,
		},
	})
	if listCepErr != nil {
		err = fmt.Errorf("list ceps failed, err: %v", listCepErr)
		return
	}
	cepsNum := len(ceps.Items)
	if cepsNum != expectedNum {
		err = fmt.Errorf("ceps num: %d, expect: %d", cepsNum, expectedNum)
		return
	}
	return
}
