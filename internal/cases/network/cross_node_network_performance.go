package network

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	// PodToPodCrossNodeNetworkPerformanceCaseName - case 名字
	PodToPodCrossNodeNetworkPerformanceCaseName cases.CaseName = "PodToPodCrossNodeNetworkPerformance"
)

// NodeToContainerNetworkPerformanceConfig 节点到容器网络性能测试配置
type NodeToContainerNetworkPerformanceConfig struct {
	PingCount       int    `json:"pingCount"`       // ping测试次数，默认50
	PacketSizes     []int  `json:"packetSizes"`     // 测试包大小，默认[64, 512, 1024, 1500]
	TestDurationSec int    `json:"testDurationSec"` // 单项测试持续时间(秒)，默认10
	IperfPort       int    `json:"iperfPort"`       // iperf服务端口，默认5201
	ParallelStreams int    `json:"parallelStreams"` // iperf并发流数，默认1
	TestNamespace   string `json:"testNamespace"`   // 测试命名空间，默认default
	ClientImage     string `json:"clientImage"`     // 客户端镜像
	ServerImage     string `json:"serverImage"`     // 服务端镜像

	// 带宽测试配置
	BandwidthTestDuration int      `json:"bandwidthTestDuration"` // 带宽测试持续时间(秒)，默认10
	BandwidthProtocols    []string `json:"bandwidthProtocols"`    // 协议类型 [tcp, udp]

	// PPS测试配置
	PPSTestDuration int   `json:"ppsTestDuration"` // PPS测试持续时间(秒)，默认10
	PPSPacketSizes  []int `json:"ppsPacketSizes"`  // PPS测试包大小，默认[64, 1500]
	PPSTargetRates  []int `json:"ppsTargetRates"`  // 目标PPS率，默认[1000, 10000, 100000]
}

// NodeToContainerLatencyTestResult 延迟测试结果
type NodeToContainerLatencyTestResult struct {
	PacketSize      int           `json:"packetSize"`
	PacketsSent     int           `json:"packetsSent"`
	PacketsReceived int           `json:"packetsReceived"`
	PacketLoss      float64       `json:"packetLoss"`
	AvgLatency      time.Duration `json:"avgLatency"`
	MinLatency      time.Duration `json:"minLatency"`
	MaxLatency      time.Duration `json:"maxLatency"`
	StdDevLatency   time.Duration `json:"stdDevLatency"`
	TestDuration    time.Duration `json:"testDuration"`
}

// NodeToContainerBandwidthTestResult 带宽测试结果
type NodeToContainerBandwidthTestResult struct {
	Protocol      string  `json:"protocol"`      // tcp或udp
	TestDuration  int     `json:"testDuration"`  // 测试持续时间(秒)
	Bandwidth     float64 `json:"bandwidth"`     // 带宽(Mbps)
	TransferBytes int64   `json:"transferBytes"` // 传输字节数
	Retransmits   int     `json:"retransmits"`   // 重传次数(仅TCP)
	JitterMs      float64 `json:"jitterMs"`      // 抖动(仅UDP，毫秒)
	LostPackets   int     `json:"lostPackets"`   // 丢包数(仅UDP)
	TotalPackets  int     `json:"totalPackets"`  // 总包数(仅UDP)
}

// NodeToContainerPPSTestResult PPS测试结果
type NodeToContainerPPSTestResult struct {
	PacketSize      int     `json:"packetSize"`      // 包大小
	TargetPPS       int     `json:"targetPPS"`       // 目标PPS
	AchievedPPS     int     `json:"achievedPPS"`     // 实际达到的PPS
	PacketsSent     int     `json:"packetsSent"`     // 发送包数
	PacketsReceived int     `json:"packetsReceived"` // 接收包数
	PacketLoss      float64 `json:"packetLoss"`      // 丢包率
	TestDuration    int     `json:"testDuration"`    // 测试持续时间(秒)
	CPUUsagePercent float64 `json:"cpuUsagePercent"` // CPU使用率
	MemoryUsageMB   float64 `json:"memoryUsageMB"`   // 内存使用量(MB)
}

// NodeToContainerNetworkStats 网络性能统计
type NodeToContainerNetworkStats struct {
	TestStartTime    time.Time                            `json:"testStartTime"`
	TestEndTime      time.Time                            `json:"testEndTime"`
	TotalDuration    time.Duration                        `json:"totalDuration"`
	SourceNode       string                               `json:"sourceNode"`
	SourceNodeIP     string                               `json:"sourceNodeIP"`
	DestinationNode  string                               `json:"destinationNode"`
	DestinationPodIP string                               `json:"destinationPodIP"`
	LatencyResults   []NodeToContainerLatencyTestResult   `json:"latencyResults"`
	BandwidthResults []NodeToContainerBandwidthTestResult `json:"bandwidthResults"`
	PPSResults       []NodeToContainerPPSTestResult       `json:"ppsResults"`
}

type nodeToContainerNetworkPerformance struct {
	base      *cases.BaseClient
	config    NodeToContainerNetworkPerformanceConfig
	stats     NodeToContainerNetworkStats
	resources []cases.Resource

	// 测试资源
	serverPod  string // 服务端Pod名称
	clientPod  string // 客户端Pod名称
	sourceNode string // 源节点名称
	targetNode string // 目标节点名称
}

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), PodToPodCrossNodeNetworkPerformanceCaseName, NewPodToPodCrossNodeNetworkPerformance)
}

// NewPodToPodCrossNodeNetworkPerformance - 创建测试案例
func NewPodToPodCrossNodeNetworkPerformance(ctx context.Context) cases.Interface {
	return &nodeToContainerNetworkPerformance{}
}

func (c *nodeToContainerNetworkPerformance) Name() cases.CaseName {
	return PodToPodCrossNodeNetworkPerformanceCaseName
}

func (c *nodeToContainerNetworkPerformance) Desc() string {
	return "Pod到Pod跨节点网络性能测试，使用ping测试延迟，netcat测试带宽，快速ping测试PPS"
}

func (c *nodeToContainerNetworkPerformance) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	c.base = base

	// 设置默认配置
	c.config = NodeToContainerNetworkPerformanceConfig{
		PingCount:       50,
		PacketSizes:     []int{64, 512, 1024, 1500},
		TestDurationSec: 10,
		IperfPort:       5201,
		ParallelStreams: 1,
		TestNamespace:   "default",
		ClientImage:     "registry.baidubce.com/cce-plugin-dev/netshoot:v1",
		ServerImage:     "registry.baidubce.com/cce-plugin-dev/netshoot:v1",

		// 带宽测试默认配置
		BandwidthTestDuration: 10,
		BandwidthProtocols:    []string{"tcp"},

		// PPS测试默认配置
		PPSTestDuration: 10,
		PPSPacketSizes:  []int{64, 1500},
		PPSTargetRates:  []int{1000, 10000, 100000},
	}

	// 如果有配置文件，解析配置
	if len(config) > 0 {
		// 这里可以添加配置解析逻辑
		logger.Infof(ctx, "收到配置: %s", string(config))
	}

	logger.Infof(ctx, "Pod到Pod跨节点网络性能测试配置: %+v", c.config)
	return nil
}

func (c *nodeToContainerNetworkPerformance) Check(ctx context.Context) ([]cases.Resource, error) {
	logger.Infof(ctx, "开始Pod到Pod跨节点网络性能测试...")

	// 初始化统计数据
	c.stats = NodeToContainerNetworkStats{
		TestStartTime: time.Now(),
	}

	// 获取集群节点
	nodes, err := c.base.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取集群节点失败: %v", err)
	}

	var availableNodes []corev1.Node
	for _, node := range nodes.Items {
		if c.isNodeReady(&node) && !c.isNodeMaster(&node) {
			availableNodes = append(availableNodes, node)
		}
	}

	if len(availableNodes) < 2 {
		return nil, fmt.Errorf("需要至少2个可用节点进行跨节点测试，当前可用节点数: %d", len(availableNodes))
	}

	// 选择源节点和目标节点
	c.sourceNode = availableNodes[0].Name
	c.targetNode = availableNodes[1].Name

	// 获取源节点IP
	for _, addr := range availableNodes[0].Status.Addresses {
		if addr.Type == corev1.NodeInternalIP {
			c.stats.SourceNodeIP = addr.Address
			break
		}
	}

	c.stats.SourceNode = c.sourceNode
	c.stats.DestinationNode = c.targetNode

	logger.Infof(ctx, "选择测试节点: 源节点=%s (IP:%s), 目标节点=%s",
		c.sourceNode, c.stats.SourceNodeIP, c.targetNode)

	// 部署测试Pod（客户端和服务端）
	if err := c.deployTestPods(ctx); err != nil {
		return nil, fmt.Errorf("部署测试Pod失败: %v", err)
	}

	// 等待Pod就绪
	if err := c.waitForPodsReady(ctx); err != nil {
		return nil, fmt.Errorf("等待Pod就绪失败: %v", err)
	}

	// 获取目标Pod IP
	pod, err := c.base.K8SClient.CoreV1().Pods(c.config.TestNamespace).Get(ctx, c.serverPod, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取服务端Pod信息失败: %v", err)
	}
	c.stats.DestinationPodIP = pod.Status.PodIP

	logger.Infof(ctx, "目标Pod IP: %s", c.stats.DestinationPodIP)

	// 执行网络测试
	if err := c.executeNetworkTests(ctx); err != nil {
		return nil, fmt.Errorf("网络测试执行失败: %v", err)
	}

	// 生成测试报告
	c.generatePerformanceReport(ctx)

	c.stats.TestEndTime = time.Now()
	c.stats.TotalDuration = c.stats.TestEndTime.Sub(c.stats.TestStartTime)

	logger.Infof(ctx, "Pod到Pod跨节点网络性能测试完成")
	return c.resources, nil
}

func (c *nodeToContainerNetworkPerformance) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理Pod到Pod跨节点网络性能测试资源...")

	// 清理测试Pod
	if err := c.cleanupTestPods(ctx); err != nil {
		logger.Warnf(ctx, "清理测试Pod失败: %v", err)
	}

	logger.Infof(ctx, "Pod到Pod跨节点网络性能测试资源清理完成")
	return nil
}

func (c *nodeToContainerNetworkPerformance) Continue(ctx context.Context) bool {
	return true
}

func (c *nodeToContainerNetworkPerformance) ConfigFormat() string {
	return `{
  "pingCount": 50,
  "packetSizes": [64, 512, 1024, 1500],
  "testDurationSec": 10,
  "iperfPort": 5201,
  "parallelStreams": 1,
  "testNamespace": "default",
  "clientImage": "registry.baidubce.com/cce-plugin-dev/netshoot:v1",
  "serverImage": "registry.baidubce.com/cce-plugin-dev/netshoot:v1",
  "bandwidthTestDuration": 10,
  "bandwidthProtocols": ["tcp"],
  "ppsTestDuration": 10,
  "ppsPacketSizes": [64, 1500],
  "ppsTargetRates": [1000, 10000, 100000]
}`
}

func (c *nodeToContainerNetworkPerformance) deployTestPods(ctx context.Context) error {
	// 生成唯一的Pod名称
	timestamp := time.Now().Format("20060102150405")
	c.serverPod = fmt.Sprintf("netperf-server-%s", timestamp)
	c.clientPod = fmt.Sprintf("netperf-client-%s", timestamp)

	// 创建服务端Pod (部署到目标节点)
	serverPodSpec := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.serverPod,
			Namespace: c.config.TestNamespace,
			Labels: map[string]string{
				"app":  "netperf-server",
				"test": "pod-to-pod-performance",
			},
		},
		Spec: corev1.PodSpec{
			NodeSelector: map[string]string{
				"kubernetes.io/hostname": c.targetNode,
			},
			Containers: []corev1.Container{
				{
					Name:  "netperf-server",
					Image: c.config.ServerImage,
					Command: []string{
						"/bin/sh",
						"-c",
						"sleep infinity",
					},
					SecurityContext: &corev1.SecurityContext{
						Capabilities: &corev1.Capabilities{
							Add: []corev1.Capability{
								"NET_ADMIN",
								"NET_RAW",
								"SYS_ADMIN",
							},
						},
						Privileged: &[]bool{true}[0],
					},
				},
			},
			RestartPolicy: corev1.RestartPolicyNever,
		},
	}

	// 创建客户端Pod (部署到源节点)
	clientPodSpec := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.clientPod,
			Namespace: c.config.TestNamespace,
			Labels: map[string]string{
				"app":  "netperf-client",
				"test": "pod-to-pod-performance",
			},
		},
		Spec: corev1.PodSpec{
			NodeSelector: map[string]string{
				"kubernetes.io/hostname": c.sourceNode,
			},
			Containers: []corev1.Container{
				{
					Name:  "netperf-client",
					Image: c.config.ClientImage,
					Command: []string{
						"/bin/sh",
						"-c",
						"sleep infinity",
					},
					SecurityContext: &corev1.SecurityContext{
						Capabilities: &corev1.Capabilities{
							Add: []corev1.Capability{
								"NET_ADMIN",
								"NET_RAW",
								"SYS_ADMIN",
							},
						},
						Privileged: &[]bool{true}[0],
					},
				},
			},
			RestartPolicy: corev1.RestartPolicyNever,
		},
	}

	// 创建服务端Pod
	_, err := c.base.K8SClient.CoreV1().Pods(c.config.TestNamespace).Create(ctx, serverPodSpec, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建服务端Pod失败: %v", err)
	}

	// 创建客户端Pod
	_, err = c.base.K8SClient.CoreV1().Pods(c.config.TestNamespace).Create(ctx, clientPodSpec, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建客户端Pod失败: %v", err)
	}

	// 记录资源用于清理
	c.resources = append(c.resources, cases.Resource{
		CaseName: c.Name(),
		Type:     "Pod",
		ID:       c.serverPod,
	})
	c.resources = append(c.resources, cases.Resource{
		CaseName: c.Name(),
		Type:     "Pod",
		ID:       c.clientPod,
	})

	logger.Infof(ctx, "服务端Pod %s 创建成功，部署到节点 %s", c.serverPod, c.targetNode)
	logger.Infof(ctx, "客户端Pod %s 创建成功，部署到节点 %s", c.clientPod, c.sourceNode)
	return nil
}

func (c *nodeToContainerNetworkPerformance) waitForPodsReady(ctx context.Context) error {
	logger.Infof(ctx, "等待Pod就绪...")

	timeout := time.After(5 * time.Minute)
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	serverReady := false
	clientReady := false

	for {
		select {
		case <-timeout:
			return fmt.Errorf("等待Pod就绪超时")
		case <-ticker.C:
			// 检查服务端Pod
			if !serverReady {
				serverPod, err := c.base.K8SClient.CoreV1().Pods(c.config.TestNamespace).Get(ctx, c.serverPod, metav1.GetOptions{})
				if err == nil && serverPod.Status.Phase == corev1.PodRunning {
					allReady := true
					for _, condition := range serverPod.Status.Conditions {
						if condition.Type == corev1.PodReady && condition.Status != corev1.ConditionTrue {
							allReady = false
							break
						}
					}
					if allReady {
						logger.Infof(ctx, "服务端Pod %s 已就绪", c.serverPod)
						serverReady = true
					}
				}
			}

			// 检查客户端Pod
			if !clientReady {
				clientPod, err := c.base.K8SClient.CoreV1().Pods(c.config.TestNamespace).Get(ctx, c.clientPod, metav1.GetOptions{})
				if err == nil && clientPod.Status.Phase == corev1.PodRunning {
					allReady := true
					for _, condition := range clientPod.Status.Conditions {
						if condition.Type == corev1.PodReady && condition.Status != corev1.ConditionTrue {
							allReady = false
							break
						}
					}
					if allReady {
						logger.Infof(ctx, "客户端Pod %s 已就绪", c.clientPod)
						clientReady = true
					}
				}
			}

			// 两个Pod都就绪时返回
			if serverReady && clientReady {
				logger.Infof(ctx, "所有Pod已就绪")
				return nil
			}
		}
	}
}

func (c *nodeToContainerNetworkPerformance) executeNetworkTests(ctx context.Context) error {
	logger.Infof(ctx, "🚀 开始执行网络性能测试...")

	// 执行延迟测试
	logger.Infof(ctx, "📊 执行延迟测试...")
	if err := c.executeLatencyTests(ctx); err != nil {
		logger.Warnf(ctx, "延迟测试失败: %v", err)
	}

	// 执行简化带宽测试
	logger.Infof(ctx, "🌐 执行简化带宽测试...")
	if err := c.executeSimpleBandwidthTests(ctx); err != nil {
		logger.Warnf(ctx, "带宽测试失败: %v", err)
	}

	// 执行简化PPS测试
	logger.Infof(ctx, "📦 执行简化PPS测试...")
	if err := c.executeSimplePPSTests(ctx); err != nil {
		logger.Warnf(ctx, "PPS测试失败: %v", err)
	}

	return nil
}

func (c *nodeToContainerNetworkPerformance) executeLatencyTests(ctx context.Context) error {
	for _, packetSize := range c.config.PacketSizes {
		logger.Infof(ctx, "  测试延迟 (包大小: %d bytes)", packetSize)

		result, err := c.measureLatency(ctx, packetSize)
		if err != nil {
			logger.Warnf(ctx, "延迟测试失败: %v", err)
			continue
		}

		c.stats.LatencyResults = append(c.stats.LatencyResults, result)
		logger.Infof(ctx, "    平均延迟: %.3fms, 丢包率: %.2f%%",
			float64(result.AvgLatency.Nanoseconds())/1e6, result.PacketLoss)
	}

	return nil
}

func (c *nodeToContainerNetworkPerformance) measureLatency(ctx context.Context, packetSize int) (NodeToContainerLatencyTestResult, error) {
	startTime := time.Now()

	// 从源节点执行ping命令到目标容器
	var cmd string
	if packetSize <= 56 {
		// 对于小包，直接使用默认大小，不指定-s参数
		cmd = fmt.Sprintf("ping -c %d -i 0.1 %s",
			c.config.PingCount, c.stats.DestinationPodIP)
	} else {
		// 对于大包，尝试使用-s参数
		cmd = fmt.Sprintf("ping -c %d -s %d -i 0.1 %s",
			c.config.PingCount, packetSize, c.stats.DestinationPodIP)
	}

	logger.Infof(ctx, "在客户端Pod %s 执行ping命令: %s", c.clientPod, cmd)
	output, err := c.execInPod(ctx, c.clientPod, cmd)
	if err != nil {
		return NodeToContainerLatencyTestResult{}, err
	}

	logger.Infof(ctx, "ping命令输出:\n%s", output)

	result := NodeToContainerLatencyTestResult{
		PacketSize:   packetSize,
		TestDuration: time.Since(startTime),
	}

	// 解析ping输出
	c.parsePingOutput(output, &result)

	logger.Infof(ctx, "解析结果: 平均延迟=%.3fms, 最小=%.3fms, 最大=%.3fms, 丢包率=%.2f%%",
		float64(result.AvgLatency.Nanoseconds())/1e6,
		float64(result.MinLatency.Nanoseconds())/1e6,
		float64(result.MaxLatency.Nanoseconds())/1e6,
		result.PacketLoss)

	return result, nil
}

func (c *nodeToContainerNetworkPerformance) parsePingOutput(output string, result *NodeToContainerLatencyTestResult) {
	logger.Infof(nil, "开始解析ping输出，输出长度: %d 字符", len(output))
	logger.Infof(nil, "原始ping输出:\n<<<RAW_OUTPUT_START>>>\n%s\n<<<RAW_OUTPUT_END>>>", output)

	lines := strings.Split(output, "\n")
	logger.Infof(nil, "分割为 %d 行", len(lines))

	for i, line := range lines {
		line = strings.TrimSpace(line)
		logger.Infof(nil, "第%d行: %q", i+1, line)

		// 解析丢包信息
		if strings.Contains(line, "packets transmitted") {
			logger.Infof(nil, "找到packets transmitted行，开始解析")
			re := regexp.MustCompile(`(\d+) packets transmitted,\s*(\d+)\s+(?:packets\s+)?received,\s*(\d+(?:\.\d+)?)%\s+packet loss`)
			matches := re.FindStringSubmatch(line)
			logger.Infof(nil, "丢包率正则匹配结果: %v", matches)
			if len(matches) == 4 {
				if sent, err := strconv.Atoi(matches[1]); err == nil {
					result.PacketsSent = sent
					logger.Infof(nil, "解析发送包数: %d", sent)
				}
				if recv, err := strconv.Atoi(matches[2]); err == nil {
					result.PacketsReceived = recv
					logger.Infof(nil, "解析接收包数: %d", recv)
				}
				if loss, err := strconv.ParseFloat(matches[3], 64); err == nil {
					result.PacketLoss = loss
					logger.Infof(nil, "解析丢包率: %.2f%%", loss)
				}
			}
		}

		// 解析延迟统计信息
		if strings.Contains(line, "min/avg/max") && strings.Contains(line, "=") {
			logger.Infof(nil, "找到min/avg/max行，开始解析")
			re := regexp.MustCompile(`min/avg/max/(?:mdev|stddev)\s*=\s*([\d.]+)/([\d.]+)/([\d.]+)/([\d.]+)\s*ms`)
			matches := re.FindStringSubmatch(line)
			logger.Infof(nil, "延迟统计正则匹配结果: %v", matches)
			if len(matches) == 5 {
				if min, err := strconv.ParseFloat(matches[1], 64); err == nil {
					result.MinLatency = time.Duration(min * float64(time.Millisecond))
					logger.Infof(nil, "解析最小延迟: %.3fms", min)
				}
				if avg, err := strconv.ParseFloat(matches[2], 64); err == nil {
					result.AvgLatency = time.Duration(avg * float64(time.Millisecond))
					logger.Infof(nil, "解析平均延迟: %.3fms", avg)
				}
				if max, err := strconv.ParseFloat(matches[3], 64); err == nil {
					result.MaxLatency = time.Duration(max * float64(time.Millisecond))
					logger.Infof(nil, "解析最大延迟: %.3fms", max)
				}
				if stddev, err := strconv.ParseFloat(matches[4], 64); err == nil {
					result.StdDevLatency = time.Duration(stddev * float64(time.Millisecond))
					logger.Infof(nil, "解析标准差: %.3fms", stddev)
				}
			}
		}

		// 从单个ping响应中提取延迟信息作为fallback
		if strings.Contains(line, "time=") && strings.Contains(line, "ms") {
			logger.Infof(nil, "找到time=行，开始解析单个ping响应")
			re := regexp.MustCompile(`time=([\d.]+)\s*ms`)
			matches := re.FindStringSubmatch(line)
			logger.Infof(nil, "单个ping响应正则匹配结果: %v", matches)
			if len(matches) == 2 {
				if latency, err := strconv.ParseFloat(matches[1], 64); err == nil {
					logger.Infof(nil, "解析单个ping延迟: %.3fms", latency)
					pingTime := time.Duration(latency * float64(time.Millisecond))
					if result.AvgLatency == 0 {
						result.AvgLatency = pingTime
						result.MinLatency = pingTime
						result.MaxLatency = pingTime
						logger.Infof(nil, "使用单个ping响应作为初始延迟值")
					} else {
						if pingTime < result.MinLatency || result.MinLatency == 0 {
							result.MinLatency = pingTime
						}
						if pingTime > result.MaxLatency {
							result.MaxLatency = pingTime
						}
						logger.Infof(nil, "更新延迟统计值")
					}
				}
			}
		}
	}

	logger.Infof(nil, "ping解析完成，最终结果: 平均延迟=%.3fms, 最小=%.3fms, 最大=%.3fms",
		float64(result.AvgLatency.Nanoseconds())/1e6,
		float64(result.MinLatency.Nanoseconds())/1e6,
		float64(result.MaxLatency.Nanoseconds())/1e6)
}

func (c *nodeToContainerNetworkPerformance) executeBandwidthTests(ctx context.Context) error {
	// 启动简单HTTP服务器进行带宽测试
	if err := c.startSimpleHTTPServer(ctx); err != nil {
		return fmt.Errorf("启动HTTP服务器失败: %v", err)
	}

	// 等待服务器启动
	time.Sleep(3 * time.Second)

	for _, protocol := range c.config.BandwidthProtocols {
		logger.Infof(ctx, "  测试带宽 (协议: %s)", protocol)

		result, err := c.measureBandwidthSimple(ctx, protocol)
		if err != nil {
			logger.Warnf(ctx, "带宽测试失败 (%s): %v", protocol, err)
			continue
		}

		c.stats.BandwidthResults = append(c.stats.BandwidthResults, result)
		logger.Infof(ctx, "    带宽: %.2f Mbps", result.Bandwidth)
	}

	return nil
}

func (c *nodeToContainerNetworkPerformance) executePPSTests(ctx context.Context) error {
	for _, packetSize := range c.config.PPSPacketSizes {
		for _, targetPPS := range c.config.PPSTargetRates {
			logger.Infof(ctx, "  测试PPS (包大小: %d bytes, 目标PPS: %d)", packetSize, targetPPS)

			result, err := c.measurePPS(ctx, packetSize, targetPPS)
			if err != nil {
				logger.Warnf(ctx, "PPS测试失败 (包大小: %d, 目标PPS: %d): %v", packetSize, targetPPS, err)
				continue
			}

			c.stats.PPSResults = append(c.stats.PPSResults, result)
			logger.Infof(ctx, "    实际PPS: %d packets/sec, 丢包率: %.2f%%", result.AchievedPPS, result.PacketLoss)
		}
	}

	return nil
}

func (c *nodeToContainerNetworkPerformance) startSimpleHTTPServer(ctx context.Context) error {
	// 跳过复杂的HTTP服务器，直接准备测试文件
	logger.Infof(ctx, "准备简单的网络传输测试...")

	// 只创建小的测试文件
	createFileCmd := "dd if=/dev/zero of=/tmp/testfile_1m bs=1M count=1 2>/dev/null"
	logger.Infof(ctx, "创建1MB测试文件: %s", createFileCmd)
	_, err := c.execInPod(ctx, c.serverPod, createFileCmd)
	if err != nil {
		return fmt.Errorf("创建测试文件失败: %v", err)
	}

	logger.Infof(ctx, "测试文件准备完成")
	return nil
}

func (c *nodeToContainerNetworkPerformance) measureBandwidthSimple(ctx context.Context, protocol string) (NodeToContainerBandwidthTestResult, error) {
	httpPort := 8080

	// 快速连通性检查，避免卡住
	logger.Infof(ctx, "跳过详细连通性测试，直接开始带宽测试")

	result := NodeToContainerBandwidthTestResult{
		Protocol:     protocol,
		TestDuration: c.config.BandwidthTestDuration,
	}

	var err error
	if protocol == "tcp" {
		// TCP带宽测试 - 使用HTTP下载
		err = c.measureHTTPDownloadBandwidth(ctx, httpPort, &result)
	} else {
		// UDP带宽测试 - 使用简单的UDP数据传输
		err = c.measureUDPSimpleBandwidth(ctx, &result)
	}

	if err != nil {
		return NodeToContainerBandwidthTestResult{}, fmt.Errorf("%s带宽测试失败: %v", protocol, err)
	}

	return result, nil
}

func (c *nodeToContainerNetworkPerformance) measurePPS(ctx context.Context, packetSize int, targetPPS int) (NodeToContainerPPSTestResult, error) {
	// 检查可用的PPS测试工具
	var cmd string
	var useTool string

	// 首先尝试hping3
	checkHping3 := "which hping3"
	_, err := c.execInPod(ctx, c.clientPod, checkHping3)
	if err == nil {
		// 使用hping3进行PPS测试
		interval := 1.0 / float64(targetPPS) * 1000000 // 转换为微秒
		totalPackets := targetPPS * c.config.PPSTestDuration
		cmd = fmt.Sprintf("hping3 -c %d -i u%d -p 80 -S -s %d %s",
			totalPackets, int(interval), packetSize, c.stats.DestinationPodIP)
		useTool = "hping3"
	} else {
		// hping3不可用，尝试使用nping
		checkNping := "which nping"
		_, err = c.execInPod(ctx, c.clientPod, checkNping)
		if err == nil {
			// 使用nping进行PPS测试
			cmd = fmt.Sprintf("nping --tcp -p 80 --data-length %d --rate %d --count %d %s",
				packetSize, targetPPS, targetPPS*c.config.PPSTestDuration, c.stats.DestinationPodIP)
			useTool = "nping"
		} else {
			// 都不可用，使用ping flood模式作为替代（近似PPS测试）
			count := targetPPS * c.config.PPSTestDuration
			if count > 10000 {
				count = 10000 // 限制最大包数
			}
			interval := 1.0 / float64(targetPPS)
			cmd = fmt.Sprintf("ping -c %d -i %.6f -s %d %s",
				count, interval, packetSize, c.stats.DestinationPodIP)
			useTool = "ping"
		}
	}

	logger.Infof(ctx, "在客户端Pod %s 使用 %s 执行PPS测试: %s", c.clientPod, useTool, cmd)

	startTime := time.Now()
	output, err := c.execInPod(ctx, c.clientPod, cmd)
	testDuration := time.Since(startTime)

	if err != nil {
		return NodeToContainerPPSTestResult{}, fmt.Errorf("PPS测试工具 %s 执行失败: %v", useTool, err)
	}

	logger.Infof(ctx, "%s输出: %s", useTool, output)

	result := NodeToContainerPPSTestResult{
		PacketSize:   packetSize,
		TargetPPS:    targetPPS,
		TestDuration: int(testDuration.Seconds()),
	}

	// 根据使用的工具解析输出
	switch useTool {
	case "hping3":
		c.parseHpingOutput(output, &result)
	case "nping":
		c.parseNpingOutput(output, &result)
	case "ping":
		c.parsePingPPSOutput(output, &result)
	}

	return result, nil
}

func (c *nodeToContainerNetworkPerformance) measureHTTPDownloadBandwidth(ctx context.Context, httpPort int, result *NodeToContainerBandwidthTestResult) error {
	// 使用简单的TCP数据传输测试带宽，不依赖HTTP服务器
	testPort := 12345
	dataSize := 1 * 1024 * 1024 // 1MB数据

	// 在服务端启动简单的TCP监听，接收数据
	serverCmd := fmt.Sprintf("timeout 15 nc -l %d > /tmp/received_data &", testPort)
	logger.Infof(ctx, "启动TCP接收端: %s", serverCmd)
	_, err := c.execInPod(ctx, c.serverPod, serverCmd)
	if err != nil {
		return fmt.Errorf("启动TCP接收端失败: %v", err)
	}

	// 等待服务端启动
	time.Sleep(2 * time.Second)

	// 客户端发送数据并测量时间
	clientCmd := fmt.Sprintf("timeout 10 sh -c 'dd if=/dev/zero bs=1024 count=%d 2>/dev/null | nc %s %d'",
		dataSize/1024, c.stats.DestinationPodIP, testPort)

	logger.Infof(ctx, "执行TCP数据传输: %s", clientCmd)
	startTime := time.Now()
	output, err := c.execInPod(ctx, c.clientPod, clientCmd)
	elapsed := time.Since(startTime)

	if err != nil {
		return fmt.Errorf("TCP传输失败: %v, 输出: %s", err, output)
	}

	logger.Infof(ctx, "TCP传输完成，耗时: %v", elapsed)
	logger.Infof(ctx, "命令输出: %s", output)

	// 计算带宽（Mbps）
	durationSec := elapsed.Seconds()
	if durationSec > 0 && durationSec < 15 { // 确保是合理的传输时间
		bandwidthMbps := (float64(dataSize) * 8) / (durationSec * 1000000) // 转换为Mbps
		result.Bandwidth = bandwidthMbps
		result.TransferBytes = int64(dataSize)
		logger.Infof(ctx, "TCP传输带宽: %.2f Mbps (传输 %d bytes 耗时 %.2f 秒)",
			bandwidthMbps, dataSize, durationSec)
	} else {
		// 传输异常，直接返回错误，不使用fallback
		return fmt.Errorf("TCP传输异常，耗时: %.2f 秒", durationSec)
	}

	// 清理TCP监听进程
	cleanupCmd := "pkill -f 'nc.*-l.*12345' || true"
	_, _ = c.execInPod(ctx, c.serverPod, cleanupCmd)

	return nil
}

func (c *nodeToContainerNetworkPerformance) measureUDPSimpleBandwidth(ctx context.Context, result *NodeToContainerBandwidthTestResult) error {
	// 使用简单的UDP数据传输测试带宽
	// 服务端启动UDP监听
	udpPort := 9999

	// 启动UDP服务端 (使用nc或socat)
	serverCmd := fmt.Sprintf("nc -u -l %d > /tmp/udp_received &", udpPort)
	logger.Infof(ctx, "启动UDP服务端: %s", serverCmd)
	_, err := c.execInPod(ctx, c.serverPod, serverCmd)
	if err != nil {
		return fmt.Errorf("启动UDP服务端失败: %v", err)
	}

	time.Sleep(2 * time.Second) // 等待服务端启动

	// 客户端发送数据，减小数据量避免卡住
	dataSize := 1 * 1024 * 1024 // 1MB，减小测试数据量

	// 使用dd生成数据并通过nc发送，添加超时保护
	clientCmd := fmt.Sprintf("timeout 10 sh -c 'dd if=/dev/zero bs=1024 count=%d 2>/dev/null | nc -u %s %d'",
		dataSize/1024, c.stats.DestinationPodIP, udpPort)

	logger.Infof(ctx, "执行UDP数据传输: %s", clientCmd)
	startTime := time.Now()
	output, err := c.execInPod(ctx, c.clientPod, clientCmd)
	elapsed := time.Since(startTime)

	if err != nil {
		return fmt.Errorf("UDP传输失败: %v, 输出: %s", err, output)
	}

	logger.Infof(ctx, "UDP传输完成，耗时: %v", elapsed)
	logger.Infof(ctx, "命令输出: %s", output)

	// 计算带宽（Mbps）
	durationSec := elapsed.Seconds()
	if durationSec > 0 {
		bandwidthMbps := (float64(dataSize) * 8) / (durationSec * 1000000) // 转换为Mbps
		result.Bandwidth = bandwidthMbps
		result.TransferBytes = int64(dataSize)
		logger.Infof(ctx, "UDP传输带宽: %.2f Mbps (传输 %d bytes 耗时 %.2f 秒)",
			bandwidthMbps, dataSize, durationSec)
	}

	// 清理UDP服务端进程
	cleanupCmd := "pkill -f 'nc.*-u.*-l' || true"
	_, _ = c.execInPod(ctx, c.serverPod, cleanupCmd)

	return nil
}

func (c *nodeToContainerNetworkPerformance) parseHpingOutput(output string, result *NodeToContainerPPSTestResult) {
	logger.Infof(nil, "开始解析hping3输出")

	lines := strings.Split(output, "\n")
	receivedCount := 0

	// 统计成功的响应
	for _, line := range lines {
		if strings.Contains(line, "bytes from") && strings.Contains(line, "time=") {
			receivedCount++
		}
	}

	result.PacketsReceived = receivedCount

	// 查找统计信息
	for _, line := range lines {
		if strings.Contains(line, "packets transmitted") {
			re := regexp.MustCompile(`(\d+) packets transmitted, (\d+) packets received`)
			matches := re.FindStringSubmatch(line)
			if len(matches) >= 3 {
				if sent, err := strconv.Atoi(matches[1]); err == nil {
					result.PacketsSent = sent
				}
				if received, err := strconv.Atoi(matches[2]); err == nil {
					result.PacketsReceived = received
				}
			}
		}
	}

	// 计算丢包率
	if result.PacketsSent > 0 {
		result.PacketLoss = float64(result.PacketsSent-result.PacketsReceived) / float64(result.PacketsSent) * 100
	}

	// 计算实际PPS
	if result.TestDuration > 0 {
		result.AchievedPPS = result.PacketsReceived / result.TestDuration
	}

	logger.Infof(nil, "hping3解析完成: 发送=%d, 接收=%d, 丢包率=%.2f%%, 实际PPS=%d",
		result.PacketsSent, result.PacketsReceived, result.PacketLoss, result.AchievedPPS)
}

func (c *nodeToContainerNetworkPerformance) parseNpingOutput(output string, result *NodeToContainerPPSTestResult) {
	logger.Infof(nil, "开始解析nping输出")

	lines := strings.Split(output, "\n")

	// 查找统计信息
	for _, line := range lines {
		// nping通常显示如："Sent 1000 packets, received 950 valid responses"
		if strings.Contains(line, "Sent") && strings.Contains(line, "packets") {
			re := regexp.MustCompile(`Sent (\d+) packets.*received (\d+)`)
			matches := re.FindStringSubmatch(line)
			if len(matches) >= 3 {
				if sent, err := strconv.Atoi(matches[1]); err == nil {
					result.PacketsSent = sent
				}
				if received, err := strconv.Atoi(matches[2]); err == nil {
					result.PacketsReceived = received
				}
			}
		}
	}

	// 计算丢包率
	if result.PacketsSent > 0 {
		result.PacketLoss = float64(result.PacketsSent-result.PacketsReceived) / float64(result.PacketsSent) * 100
	}

	// 计算实际PPS
	if result.TestDuration > 0 {
		result.AchievedPPS = result.PacketsReceived / result.TestDuration
	}

	logger.Infof(nil, "nping解析完成: 发送=%d, 接收=%d, 丢包率=%.2f%%, 实际PPS=%d",
		result.PacketsSent, result.PacketsReceived, result.PacketLoss, result.AchievedPPS)
}

func (c *nodeToContainerNetworkPerformance) parsePingPPSOutput(output string, result *NodeToContainerPPSTestResult) {
	logger.Infof(nil, "开始解析ping PPS输出")

	// 使用现有的ping解析逻辑
	var tempLatencyResult NodeToContainerLatencyTestResult
	c.parsePingOutput(output, &tempLatencyResult)

	// 将延迟测试结果转换为PPS结果
	result.PacketsSent = tempLatencyResult.PacketsSent
	result.PacketsReceived = tempLatencyResult.PacketsReceived
	result.PacketLoss = tempLatencyResult.PacketLoss

	// 计算实际PPS
	if result.TestDuration > 0 {
		result.AchievedPPS = result.PacketsReceived / result.TestDuration
	}

	logger.Infof(nil, "ping PPS解析完成: 发送=%d, 接收=%d, 丢包率=%.2f%%, 实际PPS=%d",
		result.PacketsSent, result.PacketsReceived, result.PacketLoss, result.AchievedPPS)
}

func (c *nodeToContainerNetworkPerformance) execInPod(ctx context.Context, podName, command string) (string, error) {
	// 使用真实的Kubernetes exec API
	commandArray := []string{"/bin/sh", "-c", command}

	output, err := c.base.KubeClient.RemoteExec("default", podName, "", commandArray)
	if err != nil {
		return "", fmt.Errorf("在Pod %s 中执行命令失败: %v", podName, err)
	}

	return output, nil
}

func (c *nodeToContainerNetworkPerformance) generatePerformanceReport(ctx context.Context) {
	logger.Infof(ctx, "=== Pod到Pod跨节点网络性能测试报告 ===")
	logger.Infof(ctx, "测试时间: %v - %v",
		c.stats.TestStartTime.Format("2006-01-02 15:04:05"),
		c.stats.TestEndTime.Format("2006-01-02 15:04:05"))
	logger.Infof(ctx, "总测试时长: %v", c.stats.TotalDuration)

	logger.Infof(ctx, "--- 测试环境 ---")
	logger.Infof(ctx, "客户端Pod: %s (在节点 %s)", c.clientPod, c.stats.SourceNode)
	logger.Infof(ctx, "服务端Pod: %s (在节点 %s, IP: %s)", c.serverPod, c.stats.DestinationNode, c.stats.DestinationPodIP)

	// 延迟测试结果
	logger.Infof(ctx, "--- 延迟测试结果 ---")
	for _, result := range c.stats.LatencyResults {
		logger.Infof(ctx, "包大小 %d bytes: 平均延迟 %.3fms, 最小 %.3fms, 最大 %.3fms, 丢包率 %.2f%%",
			result.PacketSize,
			float64(result.AvgLatency.Nanoseconds())/1e6,
			float64(result.MinLatency.Nanoseconds())/1e6,
			float64(result.MaxLatency.Nanoseconds())/1e6,
			result.PacketLoss)
	}

	// 带宽测试结果
	logger.Infof(ctx, "--- 带宽测试结果 ---")
	if len(c.stats.BandwidthResults) > 0 {
		for _, result := range c.stats.BandwidthResults {
			if result.Protocol == "tcp" {
				logger.Infof(ctx, "TCP带宽: %.2f Mbps, 传输: %d bytes, 重传: %d 次",
					result.Bandwidth, result.TransferBytes, result.Retransmits)
			} else {
				logger.Infof(ctx, "UDP带宽: %.2f Mbps, 抖动: %.3f ms, 丢包: %d/%d (%.2f%%)",
					result.Bandwidth, result.JitterMs, result.LostPackets, result.TotalPackets,
					float64(result.LostPackets)/float64(result.TotalPackets)*100)
			}
		}
	} else {
		logger.Infof(ctx, "无带宽测试结果")
	}

	// PPS测试结果
	logger.Infof(ctx, "--- PPS测试结果 ---")
	if len(c.stats.PPSResults) > 0 {
		for _, result := range c.stats.PPSResults {
			logger.Infof(ctx, "包大小 %d bytes, 目标PPS %d packets/sec: 实际PPS %d packets/sec, 丢包率 %.2f%% (%d/%d)",
				result.PacketSize, result.TargetPPS, result.AchievedPPS, result.PacketLoss,
				result.PacketsSent-result.PacketsReceived, result.PacketsSent)
		}
	} else {
		logger.Infof(ctx, "无PPS测试结果")
	}

	// 性能损耗分析
	logger.Infof(ctx, "--- 性能损耗分析 ---")
	c.analyzePerformanceLoss(ctx)

	logger.Infof(ctx, "================================")
}

func (c *nodeToContainerNetworkPerformance) analyzePerformanceLoss(ctx context.Context) {
	// 延迟损耗分析 (基线延迟假设为0.1ms)
	baselineLatencyMs := 0.1
	for _, result := range c.stats.LatencyResults {
		actualLatencyMs := float64(result.AvgLatency.Nanoseconds()) / 1e6
		latencyIncrease := ((actualLatencyMs - baselineLatencyMs) / baselineLatencyMs) * 100
		if latencyIncrease > 0 {
			logger.Infof(ctx, "延迟损耗 (包大小 %d bytes): +%.1f%% (+%.3fms)",
				result.PacketSize, latencyIncrease, actualLatencyMs-baselineLatencyMs)
		}
	}

	// 带宽损耗分析 (基线假设为1Gbps)
	if len(c.stats.BandwidthResults) > 0 {
		baselineBandwidthMbps := 1000.0
		for _, result := range c.stats.BandwidthResults {
			bandwidthLoss := ((baselineBandwidthMbps - result.Bandwidth) / baselineBandwidthMbps) * 100
			if bandwidthLoss > 0 {
				logger.Infof(ctx, "带宽损耗 (%s): -%.1f%% (-%.2f Mbps)",
					result.Protocol, bandwidthLoss, baselineBandwidthMbps-result.Bandwidth)
			}
		}
	}

	// PPS损耗分析
	if len(c.stats.PPSResults) > 0 {
		for _, result := range c.stats.PPSResults {
			ppsLoss := float64(result.TargetPPS-result.AchievedPPS) / float64(result.TargetPPS) * 100
			if ppsLoss > 0 {
				logger.Infof(ctx, "PPS损耗 (包大小 %d bytes, 目标 %d packets/sec): -%.1f%% (-%d packets/sec)",
					result.PacketSize, result.TargetPPS, ppsLoss, result.TargetPPS-result.AchievedPPS)
			}
		}
	}
}

func (c *nodeToContainerNetworkPerformance) cleanupTestPods(ctx context.Context) error {
	// 删除服务端Pod
	if c.serverPod != "" {
		err := c.base.K8SClient.CoreV1().Pods(c.config.TestNamespace).Delete(ctx, c.serverPod, metav1.DeleteOptions{})
		if err != nil {
			logger.Warnf(ctx, "删除服务端Pod失败: %v", err)
		} else {
			logger.Infof(ctx, "服务端Pod %s 删除成功", c.serverPod)
		}
	}

	// 删除客户端Pod
	if c.clientPod != "" {
		err := c.base.K8SClient.CoreV1().Pods(c.config.TestNamespace).Delete(ctx, c.clientPod, metav1.DeleteOptions{})
		if err != nil {
			logger.Warnf(ctx, "删除客户端Pod失败: %v", err)
		} else {
			logger.Infof(ctx, "客户端Pod %s 删除成功", c.clientPod)
		}
	}

	// 清理可能残留的测试Pod
	pods, err := c.base.K8SClient.CoreV1().Pods(c.config.TestNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "test=pod-to-pod-performance",
	})
	if err != nil {
		logger.Warnf(ctx, "列出测试Pod失败: %v", err)
	} else {
		for _, pod := range pods.Items {
			err := c.base.K8SClient.CoreV1().Pods(c.config.TestNamespace).Delete(ctx, pod.Name, metav1.DeleteOptions{})
			if err != nil {
				logger.Warnf(ctx, "删除测试Pod %s 失败: %v", pod.Name, err)
			} else {
				logger.Infof(ctx, "测试Pod %s 删除成功", pod.Name)
			}
		}
	}

	return nil
}

// 辅助方法
func (c *nodeToContainerNetworkPerformance) isNodeReady(node *corev1.Node) bool {
	for _, condition := range node.Status.Conditions {
		if condition.Type == corev1.NodeNetworkUnavailable {
			return condition.Status == corev1.ConditionFalse
		}
	}
	return false
}

func (c *nodeToContainerNetworkPerformance) isNodeMaster(node *corev1.Node) bool {
	_, hasMasterLabel := node.Labels["node-role.kubernetes.io/master"]
	_, hasControlPlaneLabel := node.Labels["node-role.kubernetes.io/control-plane"]
	return hasMasterLabel || hasControlPlaneLabel
}

// executeSimpleBandwidthTests 执行简化的带宽测试
func (c *nodeToContainerNetworkPerformance) executeSimpleBandwidthTests(ctx context.Context) error {
	logger.Infof(ctx, "开始简化带宽测试...")

	for _, protocol := range c.config.BandwidthProtocols {
		logger.Infof(ctx, "  测试 %s 带宽...", protocol)

		result, err := c.measureSimpleBandwidth(ctx, protocol)
		if err != nil {
			logger.Warnf(ctx, "%s带宽测试失败: %v", protocol, err)
			continue
		}

		c.stats.BandwidthResults = append(c.stats.BandwidthResults, result)
		logger.Infof(ctx, "    %s带宽: %.2f Mbps", protocol, result.Bandwidth)
	}

	return nil
}

// executeSimplePPSTests 执行简化的PPS测试
func (c *nodeToContainerNetworkPerformance) executeSimplePPSTests(ctx context.Context) error {
	logger.Infof(ctx, "开始简化PPS测试...")

	for _, packetSize := range c.config.PPSPacketSizes {
		for _, targetPPS := range c.config.PPSTargetRates {
			logger.Infof(ctx, "  测试PPS (包大小: %d bytes, 目标PPS: %d)", packetSize, targetPPS)

			result, err := c.measureSimplePPS(ctx, packetSize, targetPPS)
			if err != nil {
				logger.Warnf(ctx, "PPS测试失败 (包大小: %d, 目标PPS: %d): %v", packetSize, targetPPS, err)
				continue
			}

			c.stats.PPSResults = append(c.stats.PPSResults, result)
			logger.Infof(ctx, "    实际PPS: %d packets/sec, 丢包率: %.2f%%", result.AchievedPPS, result.PacketLoss)
		}
	}

	return nil
}

// measureSimpleBandwidth 简化的带宽测试 - 使用基础netcat
func (c *nodeToContainerNetworkPerformance) measureSimpleBandwidth(ctx context.Context, protocol string) (NodeToContainerBandwidthTestResult, error) {
	result := NodeToContainerBandwidthTestResult{
		Protocol:     protocol,
		TestDuration: c.config.BandwidthTestDuration,
	}

	if protocol == "tcp" {
		return c.measureTCPBandwidthWithNC(ctx, &result)
	} else {
		return c.measureUDPBandwidthWithDD(ctx, &result)
	}
}

// measureTCPBandwidthWithNC 使用netcat测试TCP带宽
func (c *nodeToContainerNetworkPerformance) measureTCPBandwidthWithNC(ctx context.Context, result *NodeToContainerBandwidthTestResult) (NodeToContainerBandwidthTestResult, error) {
	testPort := 9999
	dataSize := 512 * 1024 // 512KB，小数据量确保成功

	// 启动TCP服务端
	serverCmd := fmt.Sprintf("timeout 30 nc -l %d > /dev/null &", testPort)
	logger.Infof(ctx, "启动TCP服务端: %s", serverCmd)
	_, err := c.execInPod(ctx, c.serverPod, serverCmd)
	if err != nil {
		return *result, fmt.Errorf("启动TCP服务端失败: %v", err)
	}

	// 等待服务端启动
	time.Sleep(2 * time.Second)

	// 客户端发送数据
	clientCmd := fmt.Sprintf("timeout 20 sh -c 'dd if=/dev/zero bs=1024 count=%d 2>/dev/null | nc %s %d'",
		dataSize/1024, c.stats.DestinationPodIP, testPort)

	logger.Infof(ctx, "发送TCP数据: %s", clientCmd)
	startTime := time.Now()
	_, err = c.execInPod(ctx, c.clientPod, clientCmd)
	elapsed := time.Since(startTime)

	// 计算真实带宽，不使用fallback
	durationSec := elapsed.Seconds()
	if durationSec > 0 && durationSec < 25 { // 合理的传输时间
		bandwidthMbps := (float64(dataSize) * 8) / (durationSec * 1000000)
		result.Bandwidth = bandwidthMbps
		result.TransferBytes = int64(dataSize)
		logger.Infof(ctx, "TCP传输完成: %.2f Mbps (%.2f秒)", bandwidthMbps, durationSec)
	} else {
		// 时间异常，返回0带宽
		result.Bandwidth = 0.0
		result.TransferBytes = int64(dataSize)
		logger.Warnf(ctx, "TCP传输时间异常(%.2f秒)，带宽测量失败", durationSec)
		return *result, fmt.Errorf("TCP传输时间异常: %.2f秒", durationSec)
	}

	// 清理TCP监听进程
	cleanupCmd := fmt.Sprintf("pkill -f 'nc.*-l.*%d' || true", testPort)
	_, _ = c.execInPod(ctx, c.serverPod, cleanupCmd)

	return *result, nil
}

// measureUDPBandwidthWithDD 使用dd+nc测试UDP带宽
func (c *nodeToContainerNetworkPerformance) measureUDPBandwidthWithDD(ctx context.Context, result *NodeToContainerBandwidthTestResult) (NodeToContainerBandwidthTestResult, error) {
	testPort := 9998
	dataSize := 256 * 1024 // 256KB，UDP更小的数据量

	// 启动UDP服务端
	serverCmd := fmt.Sprintf("timeout 30 nc -u -l %d > /tmp/udp_received &", testPort)
	logger.Infof(ctx, "启动UDP服务端: %s", serverCmd)
	_, err := c.execInPod(ctx, c.serverPod, serverCmd)
	if err != nil {
		return *result, fmt.Errorf("启动UDP服务端失败: %v", err)
	}

	// 等待服务端启动
	time.Sleep(2 * time.Second)

	// 客户端发送数据
	clientCmd := fmt.Sprintf("timeout 15 sh -c 'dd if=/dev/zero bs=1024 count=%d 2>/dev/null | nc -u %s %d'",
		dataSize/1024, c.stats.DestinationPodIP, testPort)

	logger.Infof(ctx, "发送UDP数据: %s", clientCmd)
	startTime := time.Now()
	_, err = c.execInPod(ctx, c.clientPod, clientCmd)
	elapsed := time.Since(startTime)

	// 计算真实UDP带宽，不使用fallback
	durationSec := elapsed.Seconds()
	if durationSec > 0 && durationSec < 20 {
		bandwidthMbps := (float64(dataSize) * 8) / (durationSec * 1000000)
		result.Bandwidth = bandwidthMbps
		result.TransferBytes = int64(dataSize)
		logger.Infof(ctx, "UDP传输完成: %.2f Mbps (%.2f秒)", bandwidthMbps, durationSec)
	} else {
		// 时间异常，返回0带宽
		result.Bandwidth = 0.0
		result.TransferBytes = int64(dataSize)
		logger.Warnf(ctx, "UDP传输时间异常(%.2f秒)，带宽测量失败", durationSec)
		return *result, fmt.Errorf("UDP传输时间异常: %.2f秒", durationSec)
	}

	// 清理UDP监听进程
	cleanupCmd := fmt.Sprintf("pkill -f 'nc.*-u.*-l.*%d' || true", testPort)
	_, _ = c.execInPod(ctx, c.serverPod, cleanupCmd)

	return *result, nil
}

// measureSimplePPS 简化的PPS测试 - 使用ping快速发包
func (c *nodeToContainerNetworkPerformance) measureSimplePPS(ctx context.Context, packetSize int, targetPPS int) (NodeToContainerPPSTestResult, error) {
	result := NodeToContainerPPSTestResult{
		PacketSize:   packetSize,
		TargetPPS:    targetPPS,
		TestDuration: c.config.PPSTestDuration,
	}

	// 简化版PPS：在固定时间内快速ping，计算实际PPS
	testDurationSec := 5                         // 固定5秒测试时间
	targetPackets := targetPPS * testDurationSec // 正确计算目标包数

	// 根据目标PPS调整测试参数
	if targetPPS >= 50000 {
		// 高PPS: 限制包数避免过载，缩短测试时间
		targetPackets = 10000
		testDurationSec = 2
	} else if targetPPS >= 10000 {
		// 中等PPS: 适度限制
		targetPackets = 20000
		testDurationSec = 3
	} else {
		// 低PPS: 正常测试
		if targetPackets > 10000 {
			targetPackets = 10000
		}
		if targetPackets < 50 {
			targetPackets = 50
		}
	}

	// 计算ping间隔
	interval := float64(testDurationSec) / float64(targetPackets)
	if interval < 0.001 {
		interval = 0.001 // 最小1ms间隔
	}

	var cmd string
	if packetSize <= 56 {
		// 小包，使用默认大小
		cmd = fmt.Sprintf("ping -c %d -i %.3f %s",
			targetPackets, interval, c.stats.DestinationPodIP)
	} else {
		// 大包，指定大小
		cmd = fmt.Sprintf("ping -c %d -i %.3f -s %d %s",
			targetPackets, interval, packetSize, c.stats.DestinationPodIP)
	}

	logger.Infof(ctx, "PPS测试命令: %s", cmd)
	startTime := time.Now()
	output, err := c.execInPod(ctx, c.clientPod, cmd)
	actualDuration := time.Since(startTime)

	if err != nil {
		logger.Warnf(ctx, "PPS ping命令执行失败: %v", err)
	}

	// 解析ping输出获取统计
	var tempLatencyResult NodeToContainerLatencyTestResult
	c.parsePingOutput(output, &tempLatencyResult)

	// 转换为PPS结果
	result.PacketsSent = tempLatencyResult.PacketsSent
	result.PacketsReceived = tempLatencyResult.PacketsReceived
	result.PacketLoss = tempLatencyResult.PacketLoss

	// 计算实际PPS
	if actualDuration.Seconds() > 0 {
		result.AchievedPPS = int(float64(result.PacketsReceived) / actualDuration.Seconds())
	}

	logger.Infof(ctx, "PPS测试结果: 发送%d包，接收%d包，实际PPS: %d packets/sec",
		result.PacketsSent, result.PacketsReceived, result.AchievedPPS)

	return result, nil
}
