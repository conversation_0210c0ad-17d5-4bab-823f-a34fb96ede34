/*
Local DNS 测试用例

功能描述：
测试local-dns功能，使得校验容器能够通过开源dns测试用例。

测试流程：
1. 使用 registry.baidubce.com/csm-offline/dnstools:zzw-test 镜像创建用于测试的pod
2. pod创建之前将对应的namespace打上标签 cce.baidu.com/node-local-dns-injection=enabled
3. 查看当前集群是否部署了localdns组件，如果没有部署请部署CCE定制版的localdns组件
4. 在部署完成localdns后，使用第一步创建的pod，完成开源dns的测试用例，并确认执行结果

预期结果：
预期能够顺利完成开源的测试

注意事项：
此测试会创建测试资源，测试完成后会自动清理所有创建的资源。
*/

package network

import (
	"context"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/apimachinery/pkg/util/wait"
)

const (
	// LocalDNSCaseName - case 名字
	LocalDNSCaseName cases.CaseName = "LocalDNS"
)

// localDNS 是用于测试local-dns功能的测试用例
type localDNS struct {
	base                   *cases.BaseClient
	testNamespace          string
	testPodName            string
	originalNamespaceLabel map[string]string
	localDNSDeployed       bool
}

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), LocalDNSCaseName, NewLocalDNS)
}

// NewLocalDNS 创建一个新的LocalDNS测试用例
func NewLocalDNS(ctx context.Context) cases.Interface {
	return &localDNS{
		testNamespace:    "default",
		testPodName:      "dns-test-pod",
		localDNSDeployed: false,
	}
}

// Name 返回测试用例名称
func (c *localDNS) Name() cases.CaseName {
	return LocalDNSCaseName
}

// Desc 返回测试用例描述
func (c *localDNS) Desc() string {
	return "测试local-dns功能，使得校验容器能够通过开源dns测试用例"
}

// Init 初始化测试用例
func (c *localDNS) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	logger.Infof(ctx, "初始化LocalDNS测试用例")
	if base == nil {
		return fmt.Errorf("base client is nil")
	}
	c.base = base
	return nil
}

// Check 执行测试
func (c *localDNS) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)

	logger.Infof(ctx, "开始执行LocalDNS功能测试")

	// 1. 准备测试namespace，添加local-dns注入标签
	if err := c.prepareTestNamespace(ctx); err != nil {
		return resources, fmt.Errorf("准备测试namespace失败: %v", err)
	}

	// 2. 检查并部署LocalDNS组件
	if err := c.ensureLocalDNSDeployed(ctx); err != nil {
		return resources, fmt.Errorf("确保LocalDNS组件部署失败: %v", err)
	}

	// 3. 创建DNS测试Pod
	if err := c.createDNSTestPod(ctx); err != nil {
		return resources, fmt.Errorf("创建DNS测试Pod失败: %v", err)
	}

	// 4. 等待Pod就绪
	if err := c.waitForPodReady(ctx); err != nil {
		return resources, fmt.Errorf("等待Pod就绪失败: %v", err)
	}

	// 5. 执行DNS测试用例
	if err := c.runDNSTests(ctx); err != nil {
		return resources, fmt.Errorf("执行DNS测试用例失败: %v", err)
	}

	logger.Infof(ctx, "LocalDNS功能测试完成")
	return resources, nil
}

// prepareTestNamespace 准备测试namespace，添加local-dns注入标签
func (c *localDNS) prepareTestNamespace(ctx context.Context) error {
	logger.Infof(ctx, "准备测试namespace: %s", c.testNamespace)

	// 获取当前namespace
	namespace, err := c.base.K8SClient.CoreV1().Namespaces().Get(ctx, c.testNamespace, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取namespace %s 失败: %v", c.testNamespace, err)
	}

	// 保存原始标签
	c.originalNamespaceLabel = make(map[string]string)
	if namespace.Labels != nil {
		for k, v := range namespace.Labels {
			c.originalNamespaceLabel[k] = v
		}
	}

	// 添加local-dns注入标签
	if namespace.Labels == nil {
		namespace.Labels = make(map[string]string)
	}
	namespace.Labels["cce.baidu.com/node-local-dns-injection"] = "enabled"

	// 更新namespace
	_, err = c.base.K8SClient.CoreV1().Namespaces().Update(ctx, namespace, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新namespace标签失败: %v", err)
	}

	logger.Infof(ctx, "成功为namespace %s 添加local-dns注入标签", c.testNamespace)
	return nil
}

// ensureLocalDNSDeployed 检查并部署LocalDNS组件
func (c *localDNS) ensureLocalDNSDeployed(ctx context.Context) error {
	logger.Infof(ctx, "检查LocalDNS组件部署状态")

	// 检查是否已经部署了node-local-dns DaemonSet
	_, err := c.base.K8SClient.AppsV1().DaemonSets("kube-system").Get(ctx, "node-local-dns", metav1.GetOptions{})
	if err == nil {
		logger.Infof(ctx, "LocalDNS组件已经部署，无需重复部署")
		return nil
	}

	if !kerrors.IsNotFound(err) {
		return fmt.Errorf("检查LocalDNS组件状态失败: %v", err)
	}

	// 部署LocalDNS组件
	logger.Infof(ctx, "开始部署CCE定制版LocalDNS组件")

	// 真正部署LocalDNS组件
	if err := c.deployLocalDNSManually(ctx); err != nil {
		return fmt.Errorf("部署LocalDNS组件失败: %v", err)
	}

	// 等待LocalDNS组件就绪
	if err := c.waitForLocalDNSReady(ctx); err != nil {
		return fmt.Errorf("等待LocalDNS组件就绪失败: %v", err)
	}

	c.localDNSDeployed = true
	logger.Infof(ctx, "LocalDNS组件部署并就绪成功")

	return nil
}

// deployLocalDNSManually 手动部署LocalDNS组件
func (c *localDNS) deployLocalDNSManually(ctx context.Context) error {
	logger.Infof(ctx, "开始手动部署LocalDNS组件")

	// 获取kube-dns service的ClusterIP作为上游DNS
	kubeDNSService, err := c.base.K8SClient.CoreV1().Services("kube-system").Get(ctx, "kube-dns", metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取kube-dns service失败: %v", err)
	}

	clusterDNSAddr := kubeDNSService.Spec.ClusterIP
	if clusterDNSAddr == "" {
		return fmt.Errorf("kube-dns service的ClusterIP为空")
	}

	logger.Infof(ctx, "使用kube-dns ClusterIP作为上游DNS: %s", clusterDNSAddr)

	// 创建ServiceAccount
	if err := c.createLocalDNSServiceAccount(ctx); err != nil {
		return fmt.Errorf("创建LocalDNS ServiceAccount失败: %v", err)
	}

	// 创建ConfigMap
	if err := c.createLocalDNSConfigMap(ctx, clusterDNSAddr); err != nil {
		return fmt.Errorf("创建LocalDNS ConfigMap失败: %v", err)
	}

	// 创建DaemonSet
	if err := c.createLocalDNSDaemonSet(ctx); err != nil {
		return fmt.Errorf("创建LocalDNS DaemonSet失败: %v", err)
	}

	return nil
}

// createLocalDNSServiceAccount 创建LocalDNS ServiceAccount
func (c *localDNS) createLocalDNSServiceAccount(ctx context.Context) error {
	logger.Infof(ctx, "创建LocalDNS ServiceAccount")

	serviceAccount := &corev1.ServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "node-local-dns",
			Namespace: "kube-system",
			Labels: map[string]string{
				"test": "localdns",
			},
		},
	}

	_, err := c.base.K8SClient.CoreV1().ServiceAccounts("kube-system").Create(ctx, serviceAccount, metav1.CreateOptions{})
	if err != nil && !kerrors.IsAlreadyExists(err) {
		return fmt.Errorf("创建ServiceAccount失败: %v", err)
	}

	logger.Infof(ctx, "LocalDNS ServiceAccount创建成功")
	return nil
}

// createLocalDNSConfigMap 创建LocalDNS ConfigMap
func (c *localDNS) createLocalDNSConfigMap(ctx context.Context, clusterDNSAddr string) error {
	logger.Infof(ctx, "创建LocalDNS ConfigMap")

	localDNSAddr := "*************" // 默认的LocalDNS地址

	corefileContent := fmt.Sprintf(`cluster.local:53 {
    errors
    log
    cache {
        success 9984 30
        denial 9984 5
    }
    reload
    loop
    bind %s
    forward . %s {
        force_tcp
    }
    prometheus :9253
    health %s:8080
}
in-addr.arpa:53 {
    errors
    log
    cache 30
    reload
    loop
    bind %s
    forward . %s {
        force_tcp
    }
    prometheus :9253
}
ip6.arpa:53 {
    errors
    log
    cache 30
    reload
    loop
    bind %s
    forward . %s {
        force_tcp
    }
    prometheus :9253
}
.:53 {
    errors
    log
    cache {
        success 9984 30
        denial 9984 5
    }
    reload
    loop
    bind %s
    forward . /etc/resolv.conf {
        prefer_udp
    }
    prometheus :9253
}`, localDNSAddr, clusterDNSAddr, localDNSAddr, localDNSAddr, clusterDNSAddr, localDNSAddr, clusterDNSAddr, localDNSAddr)

	configMap := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "node-local-dns",
			Namespace: "kube-system",
			Labels: map[string]string{
				"addonmanager.kubernetes.io/mode": "Reconcile",
				"test":                            "localdns",
			},
		},
		Data: map[string]string{
			"Corefile": corefileContent,
		},
	}

	_, err := c.base.K8SClient.CoreV1().ConfigMaps("kube-system").Create(ctx, configMap, metav1.CreateOptions{})
	if err != nil && !kerrors.IsAlreadyExists(err) {
		return fmt.Errorf("创建ConfigMap失败: %v", err)
	}

	logger.Infof(ctx, "LocalDNS ConfigMap创建成功")
	return nil
}

// createLocalDNSDaemonSet 创建LocalDNS DaemonSet
func (c *localDNS) createLocalDNSDaemonSet(ctx context.Context) error {
	logger.Infof(ctx, "创建LocalDNS DaemonSet")

	// 需要导入appsv1包
	daemonSet := &appsv1.DaemonSet{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "node-local-dns",
			Namespace: "kube-system",
			Labels: map[string]string{
				"k8s-app": "node-local-dns",
				"test":    "localdns",
			},
		},
		Spec: appsv1.DaemonSetSpec{
			UpdateStrategy: appsv1.DaemonSetUpdateStrategy{
				RollingUpdate: &appsv1.RollingUpdateDaemonSet{
					MaxUnavailable: &intstr.IntOrString{
						Type:   intstr.String,
						StrVal: "10%",
					},
				},
			},
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"k8s-app": "node-local-dns",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"k8s-app": "node-local-dns",
					},
					Annotations: map[string]string{
						"prometheus.io/port":   "9253",
						"prometheus.io/scrape": "true",
					},
				},
				Spec: corev1.PodSpec{
					PriorityClassName:  "system-node-critical",
					ServiceAccountName: "node-local-dns",
					HostNetwork:        true,
					DNSPolicy:          corev1.DNSDefault,
					Tolerations: []corev1.Toleration{
						{
							Key:      "CriticalAddonsOnly",
							Operator: corev1.TolerationOpExists,
						},
						{
							Effect:   corev1.TaintEffectNoExecute,
							Operator: corev1.TolerationOpExists,
						},
						{
							Effect:   corev1.TaintEffectNoSchedule,
							Operator: corev1.TolerationOpExists,
						},
					},
					Containers: []corev1.Container{
						{
							Name:  "node-cache",
							Image: "registry.baidubce.com/cce-plugin-pro/k8s-dns-node-cache:1.22.23",
							Resources: corev1.ResourceRequirements{
								Requests: corev1.ResourceList{
									corev1.ResourceCPU:    resource.MustParse("25m"),
									corev1.ResourceMemory: resource.MustParse("5Mi"),
								},
							},
							Args: []string{
								"-localip",
								"*************,*********",
								"-conf",
								"/etc/Corefile",
								"-upstreamsvc",
								"kube-dns-upstream",
							},
							SecurityContext: &corev1.SecurityContext{
								Privileged: &[]bool{true}[0],
								Capabilities: &corev1.Capabilities{
									Add: []corev1.Capability{
										"NET_ADMIN",
									},
								},
							},
							Ports: []corev1.ContainerPort{
								{
									ContainerPort: 53,
									Name:          "dns",
									Protocol:      corev1.ProtocolUDP,
								},
								{
									ContainerPort: 53,
									Name:          "dns-tcp",
									Protocol:      corev1.ProtocolTCP,
								},
								{
									ContainerPort: 9253,
									Name:          "metrics",
									Protocol:      corev1.ProtocolTCP,
								},
							},

							VolumeMounts: []corev1.VolumeMount{
								{
									MountPath: "/run/xtables.lock",
									Name:      "xtables-lock",
									ReadOnly:  false,
								},
								{
									MountPath: "/etc/coredns",
									Name:      "config-volume",
								},
								{
									MountPath: "/etc/kube-dns",
									Name:      "kube-dns-config",
								},
							},
						},
					},
					Volumes: []corev1.Volume{
						{
							Name: "xtables-lock",
							VolumeSource: corev1.VolumeSource{
								HostPath: &corev1.HostPathVolumeSource{
									Path: "/run/xtables.lock",
									Type: &[]corev1.HostPathType{corev1.HostPathFileOrCreate}[0],
								},
							},
						},
						{
							Name: "kube-dns-config",
							VolumeSource: corev1.VolumeSource{
								ConfigMap: &corev1.ConfigMapVolumeSource{
									LocalObjectReference: corev1.LocalObjectReference{
										Name: "kube-dns",
									},
									Optional: &[]bool{true}[0],
								},
							},
						},
						{
							Name: "config-volume",
							VolumeSource: corev1.VolumeSource{
								ConfigMap: &corev1.ConfigMapVolumeSource{
									LocalObjectReference: corev1.LocalObjectReference{
										Name: "node-local-dns",
									},
									Items: []corev1.KeyToPath{
										{
											Key:  "Corefile",
											Path: "Corefile.base",
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	_, err := c.base.K8SClient.AppsV1().DaemonSets("kube-system").Create(ctx, daemonSet, metav1.CreateOptions{})
	if err != nil && !kerrors.IsAlreadyExists(err) {
		return fmt.Errorf("创建DaemonSet失败: %v", err)
	}

	logger.Infof(ctx, "LocalDNS DaemonSet创建成功")
	return nil
}

// waitForLocalDNSReady 等待LocalDNS组件就绪
func (c *localDNS) waitForLocalDNSReady(ctx context.Context) error {
	logger.Infof(ctx, "等待LocalDNS组件就绪")

	err := wait.Poll(10*time.Second, 5*time.Minute, func() (bool, error) {
		daemonSet, err := c.base.K8SClient.AppsV1().DaemonSets("kube-system").Get(ctx, "node-local-dns", metav1.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取LocalDNS DaemonSet状态失败: %v", err)
			return false, nil
		}

		if daemonSet.Status.NumberReady > 0 && daemonSet.Status.NumberReady == daemonSet.Status.DesiredNumberScheduled {
			logger.Infof(ctx, "LocalDNS DaemonSet已就绪，就绪Pod数: %d/%d",
				daemonSet.Status.NumberReady, daemonSet.Status.DesiredNumberScheduled)
			return true, nil
		}

		logger.Infof(ctx, "LocalDNS DaemonSet尚未就绪，就绪Pod数: %d/%d",
			daemonSet.Status.NumberReady, daemonSet.Status.DesiredNumberScheduled)
		return false, nil
	})

	if err != nil {
		return fmt.Errorf("等待LocalDNS组件就绪超时: %v", err)
	}

	return nil
}

// createDNSTestPod 创建DNS测试Pod
func (c *localDNS) createDNSTestPod(ctx context.Context) error {
	logger.Infof(ctx, "创建DNS测试Pod: %s", c.testPodName)

	// 清理可能存在的同名Pod
	err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).Delete(ctx, c.testPodName, metav1.DeleteOptions{})
	if err != nil && !kerrors.IsNotFound(err) {
		logger.Warnf(ctx, "删除已存在的测试Pod失败: %v", err)
	}

	// 等待Pod完全删除
	wait.Poll(2*time.Second, 30*time.Second, func() (bool, error) {
		_, err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).Get(ctx, c.testPodName, metav1.GetOptions{})
		if err != nil && kerrors.IsNotFound(err) {
			return true, nil
		}
		return false, nil
	})

	// 创建测试Pod
	pod := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.testPodName,
			Namespace: c.testNamespace,
			Labels: map[string]string{
				"app":  "dns-test",
				"test": "localdns",
			},
		},
		Spec: corev1.PodSpec{
			RestartPolicy: corev1.RestartPolicyNever,
			Containers: []corev1.Container{
				{
					Name:  "dnstools",
					Image: "registry.baidubce.com/csm-offline/dnstools:zzw-test",
					Command: []string{
						"sleep",
						"3600",
					},
					ImagePullPolicy: corev1.PullIfNotPresent,
				},
			},
		},
	}

	_, err = c.base.K8SClient.CoreV1().Pods(c.testNamespace).Create(ctx, pod, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建DNS测试Pod失败: %v", err)
	}

	logger.Infof(ctx, "DNS测试Pod %s 创建成功", c.testPodName)
	return nil
}

// waitForPodReady 等待Pod就绪
func (c *localDNS) waitForPodReady(ctx context.Context) error {
	logger.Infof(ctx, "等待DNS测试Pod就绪")

	err := wait.Poll(5*time.Second, 3*time.Minute, func() (bool, error) {
		pod, err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).Get(ctx, c.testPodName, metav1.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取Pod状态失败: %v", err)
			return false, nil
		}

		if pod.Status.Phase == corev1.PodRunning {
			// 检查容器是否就绪
			for _, containerStatus := range pod.Status.ContainerStatuses {
				if !containerStatus.Ready {
					logger.Infof(ctx, "Pod %s 容器 %s 尚未就绪", c.testPodName, containerStatus.Name)
					return false, nil
				}
			}
			logger.Infof(ctx, "DNS测试Pod %s 已就绪", c.testPodName)
			return true, nil
		}

		logger.Infof(ctx, "DNS测试Pod %s 状态: %s", c.testPodName, pod.Status.Phase)
		return false, nil
	})

	if err != nil {
		return fmt.Errorf("等待Pod就绪超时: %v", err)
	}

	return nil
}

// runDNSTests 执行DNS测试用例
func (c *localDNS) runDNSTests(ctx context.Context) error {
	logger.Infof(ctx, "开始执行DNS测试用例")

	// 测试用例列表
	testCases := []struct {
		name        string
		description string
		testFunc    func(context.Context) error
	}{
		{
			name:        "基础DNS解析测试",
			description: "测试基本的DNS解析功能",
			testFunc:    c.testBasicDNSResolution,
		},
		{
			name:        "Kubernetes服务发现测试",
			description: "测试Kubernetes内部服务发现",
			testFunc:    c.testKubernetesServiceDiscovery,
		},
		{
			name:        "外部DNS解析测试",
			description: "测试外部域名解析",
			testFunc:    c.testExternalDNSResolution,
		},
		{
			name:        "LocalDNS缓存测试",
			description: "测试LocalDNS缓存功能",
			testFunc:    c.testLocalDNSCache,
		},
	}

	// 执行所有测试用例
	for _, testCase := range testCases {
		logger.Infof(ctx, "执行测试用例: %s - %s", testCase.name, testCase.description)

		if err := testCase.testFunc(ctx); err != nil {
			logger.Errorf(ctx, "测试用例 %s 失败: %v", testCase.name, err)
			return fmt.Errorf("测试用例 %s 失败: %v", testCase.name, err)
		}

		logger.Infof(ctx, "测试用例 %s 执行成功", testCase.name)
	}

	logger.Infof(ctx, "所有DNS测试用例执行完成")
	return nil
}

// testBasicDNSResolution 测试基本的DNS解析功能
func (c *localDNS) testBasicDNSResolution(ctx context.Context) error {
	logger.Infof(ctx, "执行基础DNS解析测试")

	// 测试nslookup命令
	nslookupCmd := []string{
		"nslookup",
		"kubernetes.default.svc.cluster.local",
	}

	stdout, err := c.base.KubeClient.RemoteExec(c.testNamespace, c.testPodName, "dnstools", nslookupCmd)
	if err != nil {
		return fmt.Errorf("nslookup命令执行失败: %v, 输出: %s", err, stdout)
	}

	logger.Infof(ctx, "nslookup测试结果: %s", strings.TrimSpace(stdout))

	// 检查结果
	if !strings.Contains(stdout, "kubernetes.default.svc.cluster.local") {
		return fmt.Errorf("nslookup结果不包含预期的域名")
	}

	// 测试dig命令
	digCmd := []string{
		"dig",
		"+short",
		"kubernetes.default.svc.cluster.local",
	}

	stdout, err = c.base.KubeClient.RemoteExec(c.testNamespace, c.testPodName, "dnstools", digCmd)
	if err != nil {
		return fmt.Errorf("dig命令执行失败: %v, 输出: %s", err, stdout)
	}

	logger.Infof(ctx, "dig测试结果: %s", strings.TrimSpace(stdout))

	// 检查dig结果
	if strings.TrimSpace(stdout) == "" {
		return fmt.Errorf("dig命令未返回有效的IP地址")
	}

	return nil
}

// testKubernetesServiceDiscovery 测试Kubernetes内部服务发现
func (c *localDNS) testKubernetesServiceDiscovery(ctx context.Context) error {
	logger.Infof(ctx, "执行Kubernetes服务发现测试")

	// 测试不同格式的服务名解析
	serviceNames := []string{
		"kubernetes",                             // 短名称
		"kubernetes.default",                     // 带namespace
		"kubernetes.default.svc",                 // 带svc
		"kubernetes.default.svc.cluster.local",   // 完整FQDN
		"kube-dns.kube-system.svc.cluster.local", // kube-dns服务
	}

	for _, serviceName := range serviceNames {
		logger.Infof(ctx, "测试服务名解析: %s", serviceName)

		digCmd := []string{
			"dig",
			"+short",
			serviceName,
		}

		stdout, err := c.base.KubeClient.RemoteExec(c.testNamespace, c.testPodName, "dnstools", digCmd)
		if err != nil {
			logger.Warnf(ctx, "解析服务名 %s 失败: %v", serviceName, err)
			continue
		}

		result := strings.TrimSpace(stdout)
		if result != "" {
			logger.Infof(ctx, "服务名 %s 解析结果: %s", serviceName, result)
		} else {
			logger.Warnf(ctx, "服务名 %s 解析结果为空", serviceName)
		}
	}

	return nil
}

// testExternalDNSResolution 测试外部域名解析
func (c *localDNS) testExternalDNSResolution(ctx context.Context) error {
	logger.Infof(ctx, "执行外部DNS解析测试")

	// 测试外部域名解析
	externalDomains := []string{
		"www.baidu.com",
		"www.google.com",
		"*******", // 直接IP测试
	}

	for _, domain := range externalDomains {
		logger.Infof(ctx, "测试外部域名解析: %s", domain)

		digCmd := []string{
			"dig",
			"+short",
			"+time=5",
			"+tries=2",
			domain,
		}

		stdout, err := c.base.KubeClient.RemoteExec(c.testNamespace, c.testPodName, "dnstools", digCmd)
		if err != nil {
			logger.Warnf(ctx, "解析外部域名 %s 失败: %v", domain, err)
			continue
		}

		result := strings.TrimSpace(stdout)
		if result != "" {
			logger.Infof(ctx, "外部域名 %s 解析结果: %s", domain, result)
		} else {
			logger.Warnf(ctx, "外部域名 %s 解析结果为空", domain)
		}
	}

	return nil
}

// testLocalDNSCache 测试LocalDNS缓存功能
func (c *localDNS) testLocalDNSCache(ctx context.Context) error {
	logger.Infof(ctx, "执行LocalDNS缓存测试")

	// 检查LocalDNS配置
	configCmd := []string{
		"cat",
		"/etc/resolv.conf",
	}

	stdout, err := c.base.KubeClient.RemoteExec(c.testNamespace, c.testPodName, "dnstools", configCmd)
	if err != nil {
		logger.Warnf(ctx, "获取DNS配置失败: %v", err)
	} else {
		logger.Infof(ctx, "Pod DNS配置:\n%s", stdout)
	}

	// 测试DNS查询性能（多次查询同一域名，观察缓存效果）
	domain := "kubernetes.default.svc.cluster.local"

	for i := 1; i <= 3; i++ {
		logger.Infof(ctx, "第 %d 次DNS查询测试", i)

		start := time.Now()

		digCmd := []string{
			"dig",
			"+short",
			"+time=5",
			domain,
		}

		stdout, err := c.base.KubeClient.RemoteExec(c.testNamespace, c.testPodName, "dnstools", digCmd)

		duration := time.Since(start)

		if err != nil {
			logger.Warnf(ctx, "第 %d 次DNS查询失败: %v", i, err)
		} else {
			logger.Infof(ctx, "第 %d 次DNS查询成功，耗时: %v，结果: %s", i, duration, strings.TrimSpace(stdout))
		}

		// 短暂等待
		time.Sleep(1 * time.Second)
	}

	return nil
}

// Clean 清理测试资源
func (c *localDNS) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理LocalDNS测试资源")

	// 删除测试Pod
	err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).Delete(ctx, c.testPodName, metav1.DeleteOptions{})
	if err != nil && !kerrors.IsNotFound(err) {
		logger.Warnf(ctx, "删除测试Pod失败: %v", err)
	} else {
		logger.Infof(ctx, "测试Pod %s 已删除", c.testPodName)
	}

	// 恢复namespace标签
	if c.originalNamespaceLabel != nil {
		logger.Infof(ctx, "恢复namespace标签")

		namespace, err := c.base.K8SClient.CoreV1().Namespaces().Get(ctx, c.testNamespace, metav1.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取namespace失败: %v", err)
		} else {
			// 恢复原始标签
			namespace.Labels = c.originalNamespaceLabel

			_, err = c.base.K8SClient.CoreV1().Namespaces().Update(ctx, namespace, metav1.UpdateOptions{})
			if err != nil {
				logger.Warnf(ctx, "恢复namespace标签失败: %v", err)
			} else {
				logger.Infof(ctx, "namespace标签已恢复")
			}
		}
	}

	// 如果是本次测试部署的LocalDNS，则删除它
	if c.localDNSDeployed {
		logger.Infof(ctx, "删除本次测试部署的LocalDNS组件")

		err := c.base.PluginClient.EnsurePluginDeleted(ctx, "node-local-dns")
		if err != nil {
			logger.Warnf(ctx, "删除LocalDNS组件失败: %v", err)
		} else {
			logger.Infof(ctx, "LocalDNS组件已删除")
		}
	}

	logger.Infof(ctx, "LocalDNS测试资源清理完成")
	return nil
}

// Continue 是否继续执行
func (c *localDNS) Continue(ctx context.Context) bool {
	return true
}

// ConfigFormat 配置格式
func (c *localDNS) ConfigFormat() string {
	return ""
}
