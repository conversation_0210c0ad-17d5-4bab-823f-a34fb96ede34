/*
Copyright 2024 Baidu Inc.

本文件包含了针对节点subnet annotation功能的自动化测试用例。
该测试主要验证通过在节点上添加 baidubce.com/node-eni-subnet-ids 注解来指定节点使用的subnet功能。

用例主要验证以下内容：
1. 创建节点组并设置subnet annotation
2. 扩容节点组，验证新节点是否使用了指定的subnet
3. 为已有节点添加annotation，删除对应的NRS，重启agent验证功能
4. 验证节点的NRS配置是否正确使用了annotation中指定的subnet

测试流程：
1. 创建带有subnet annotation的节点组
2. 扩容节点组到2个节点
3. 验证新节点的NRS配置
4. 为已有节点添加annotation
5. 删除已有节点的NRS
6. 重启网络agent
7. 验证已有节点的NRS配置

测试流程详见每个函数的具体实现。
*/

/*
使用config示例：
- name: SubnetAnnotation
  config:
    subnetId: sbn-e4gfu7w56z1a
*/

package network

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
)

const (
	// SubnetAnnotationCaseName - case 名字
	SubnetAnnotationCaseName cases.CaseName = "SubnetAnnotation"

	// 节点subnet annotation key
	nodeSubnetAnnotationKey = "baidubce.com/node-eni-subnet-ids"

	// 网络组件相关常量
	networkAgentDaemonSet = "cce-network-agent"
)

type subnetAnnotationConfig struct {
	SubnetId string `json:"subnetId"` // 要测试的subnet ID
}

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), SubnetAnnotationCaseName, NewSubnetAnnotation)
}

// subnetAnnotation 结构体定义
type subnetAnnotation struct {
	base             *cases.BaseClient
	resources        []cases.Resource
	config           subnetAnnotationConfig
	createdNodeGroup bool
	nodeGroupID      string
	originalReplicas int
	newNodes         []string
	existingNodes    []string
	modifiedNodes    []string // 记录被修改annotation的节点
	deletedNRSNodes  []string // 记录被删除NRS的节点
}

// NewSubnetAnnotation - 测试案例构造函数
func NewSubnetAnnotation(ctx context.Context) cases.Interface {
	return &subnetAnnotation{
		newNodes:        make([]string, 0),
		existingNodes:   make([]string, 0),
		modifiedNodes:   make([]string, 0),
		deletedNRSNodes: make([]string, 0),
	}
}

// Init 初始化测试环境
func (c *subnetAnnotation) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base is nil")
	}

	var cfg subnetAnnotationConfig
	if err := json.Unmarshal(config, &cfg); err != nil {
		return fmt.Errorf("json.Unmarshal config failed: %s", err)
	}

	if cfg.SubnetId == "" {
		return fmt.Errorf("subnetId is required in config")
	}

	c.config = cfg
	c.base = base

	logger.Infof(ctx, "初始化subnet annotation测试，使用subnet: %s", c.config.SubnetId)
	return nil
}

// Name 返回测试用例名称
func (c *subnetAnnotation) Name() cases.CaseName {
	return SubnetAnnotationCaseName
}

// Desc 返回测试用例描述
func (c *subnetAnnotation) Desc() string {
	return "测试节点subnet annotation功能，验证通过baidubce.com/node-eni-subnet-ids注解指定节点使用的subnet"
}

// Continue 返回是否继续测试
func (c *subnetAnnotation) Continue(ctx context.Context) bool {
	return true
}

// ConfigFormat 返回配置格式
func (c *subnetAnnotation) ConfigFormat() string {
	return `{
	"subnetId": "sbn-xxxxxx"
}`
}

// Check 执行测试
func (c *subnetAnnotation) Check(ctx context.Context) ([]cases.Resource, error) {
	var err error

	// 0. 启用node annotation sync配置并重启operator
	logger.Infof(ctx, "第零步：启用node annotation sync配置并重启operator...")
	if err = c.enableNodeAnnotationSync(ctx); err != nil {
		logger.Warnf(ctx, "启用node annotation sync配置失败: %v", err)
		logger.Infof(ctx, "跳过配置修改，继续进行测试（假设配置已经正确）")
	}

	// 1. 创建带有subnet annotation的节点组
	logger.Infof(ctx, "第一步：创建带有subnet annotation的节点组...")
	if err = c.createNodeGroupWithAnnotation(ctx); err != nil {
		return nil, fmt.Errorf("创建节点组失败: %v", err)
	}

	// 2. 扩容节点组到2个节点
	logger.Infof(ctx, "第二步：扩容节点组到2个节点...")
	if err = c.scaleNodeGroup(ctx, 2); err != nil {
		return nil, fmt.Errorf("扩容节点组失败: %v", err)
	}

	// 3. 等待新节点就绪并验证NRS配置
	logger.Infof(ctx, "第三步：等待新节点就绪并验证NRS配置...")
	if err = c.waitAndVerifyNewNodes(ctx); err != nil {
		return nil, fmt.Errorf("验证新节点失败: %v", err)
	}

	// 4. 为已有节点添加annotation并验证
	logger.Infof(ctx, "第四步：为已有节点添加annotation...")
	if err = c.addAnnotationToExistingNodes(ctx); err != nil {
		return nil, fmt.Errorf("为已有节点添加annotation失败: %v", err)
	}

	// 5. 删除已有节点的NRS并重启agent
	logger.Infof(ctx, "第五步：删除已有节点的NRS并重启agent...")
	if err = c.deleteNRSAndRestartAgent(ctx); err != nil {
		return nil, fmt.Errorf("删除NRS并重启agent失败: %v", err)
	}

	// 6. 验证已有节点的NRS配置
	logger.Infof(ctx, "第六步：验证已有节点的NRS配置...")
	if err = c.verifyExistingNodesNRS(ctx); err != nil {
		return nil, fmt.Errorf("验证已有节点NRS配置失败: %v", err)
	}

	logger.Infof(ctx, "✅ subnet annotation功能测试完成！")
	return c.resources, nil
}

// Clean 清理资源
func (c *subnetAnnotation) Clean(ctx context.Context) error {
	var lastErr error

	// 1. 恢复节点annotation
	for _, nodeName := range c.modifiedNodes {
		logger.Infof(ctx, "恢复节点 %s 的annotation", nodeName)
		node, err := c.base.KubeClient.GetNode(ctx, nodeName, &kube.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取节点 %s 失败: %v", nodeName, err)
			if lastErr == nil {
				lastErr = err
			}
			continue
		}

		if node.Annotations != nil {
			delete(node.Annotations, nodeSubnetAnnotationKey)
			_, err = c.base.KubeClient.UpdateNode(ctx, node)
			if err != nil {
				logger.Warnf(ctx, "恢复节点 %s annotation失败: %v", nodeName, err)
				if lastErr == nil {
					lastErr = err
				}
			} else {
				logger.Infof(ctx, "成功恢复节点 %s 的annotation", nodeName)
			}
		}
	}

	// 2. 重启网络agent使配置恢复生效
	if len(c.modifiedNodes) > 0 {
		logger.Infof(ctx, "重启网络agent使配置恢复生效")
		if err := c.restartNetworkAgent(ctx); err != nil {
			logger.Warnf(ctx, "重启网络agent失败: %v", err)
			if lastErr == nil {
				lastErr = err
			}
		}
	}

	// 3. 删除创建的节点组
	if c.createdNodeGroup && c.nodeGroupID != "" {
		logger.Infof(ctx, "删除创建的节点组 %s", c.nodeGroupID)

		// 先缩容到0
		if err := c.scaleNodeGroup(ctx, 0); err != nil {
			logger.Warnf(ctx, "缩容节点组失败: %v", err)
			if lastErr == nil {
				lastErr = err
			}
		} else {
			// 等待缩容完成
			time.Sleep(30 * time.Second)

			// 删除节点组
			_, err := c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.nodeGroupID, true, nil)
			if err != nil {
				logger.Warnf(ctx, "删除节点组失败: %v", err)
				if lastErr == nil {
					lastErr = err
				}
			} else {
				logger.Infof(ctx, "成功删除节点组 %s", c.nodeGroupID)
			}
		}
	}

	logger.Infof(ctx, "资源清理完成")
	return lastErr
}

// createNodeGroupWithAnnotation 创建带有subnet annotation的节点组
func (c *subnetAnnotation) createNodeGroupWithAnnotation(ctx context.Context) error {
	// 获取集群中已有节点的信息，用于复用配置
	instances, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
		ClusterRole: types.ClusterRoleNode,
	}, nil)
	if err != nil {
		return fmt.Errorf("获取集群节点信息失败: %v", err)
	}
	if len(instances.InstancePage.InstanceList) == 0 {
		return fmt.Errorf("集群中没有可用的节点，无法获取配置信息")
	}

	// 记录已有节点
	nodeList, err := c.base.KubeClient.ListNode(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取节点列表失败: %v", err)
	}

	for _, node := range nodeList.Items {
		c.existingNodes = append(c.existingNodes, node.Name)
	}
	logger.Infof(ctx, "记录到 %d 个已有节点", len(c.existingNodes))

	// 使用第一个节点的配置信息
	instance := instances.InstancePage.InstanceList[0]

	// 获取镜像ID
	imageID := instance.Spec.ImageID
	if imageID == "" {
		return fmt.Errorf("获取到的镜像ID为空，无法创建节点组")
	}
	logger.Infof(ctx, "将使用已有节点的镜像ID: %s", imageID)

	// 获取集群额外信息，找到可用的子网
	clusterExtraInfo, err := c.base.CCEHostClient.GetClusterExtraInfo(ctx, c.base.ClusterID, nil)
	if err != nil {
		return fmt.Errorf("获取集群额外信息失败: %v", err)
	}

	var nodeSubnetID string
	for _, subnet := range clusterExtraInfo.Subnets {
		if subnet.SubnetCIDR != "" {
			nodeSubnetID = subnet.SubnetID
			break
		}
	}

	if nodeSubnetID == "" {
		return fmt.Errorf("未找到可用的子网")
	}

	logger.Infof(ctx, "将使用子网 %s 创建节点组", nodeSubnetID)

	// 查询集群信息
	cluster, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		return fmt.Errorf("获取集群信息失败: %v", err)
	}

	// 从集群信息获取VPCID
	vpcID := cluster.Cluster.Spec.VPCID
	logger.Infof(ctx, "将使用VPC %s 创建节点组", vpcID)

	// 创建节点组，初始副本数为0
	igName := fmt.Sprintf("test-subnet-annotation-%d", time.Now().Unix())
	createReq := &ccev2.CreateInstanceGroupRequest{
		InstanceGroupSpec: types.InstanceGroupSpec{
			InstanceGroupName: igName,
			Replicas:          0, // 初始为0个节点
			InstanceTemplate: types.InstanceTemplate{
				InstanceSpec: types.InstanceSpec{
					ClusterRole:  types.ClusterRoleNode,
					MachineType:  instance.Spec.MachineType,
					InstanceType: instance.Spec.InstanceType,
					VPCConfig: types.VPCConfig{
						VPCID:       vpcID,
						VPCSubnetID: nodeSubnetID,
						SecurityGroup: types.SecurityGroup{
							EnableCCERequiredSecurityGroup: true,
						},
					},
					InstanceResource: types.InstanceResource{
						CPU: instance.Spec.InstanceResource.CPU,
						MEM: instance.Spec.InstanceResource.MEM,
					},
					ImageID:       imageID,
					AdminPassword: "Test123456!",
				},
			},
		},
	}

	// 发送创建请求
	createResp, err := c.base.CCEClient.CreateInstanceGroup(ctx, c.base.ClusterID, createReq, nil)
	if err != nil {
		return fmt.Errorf("创建节点组失败: %v", err)
	}

	// 保存节点组ID
	c.nodeGroupID = createResp.InstanceGroupID
	c.createdNodeGroup = true
	logger.Infof(ctx, "成功创建节点组 %s (ID: %s)，设置了subnet annotation: %s=%s",
		igName, c.nodeGroupID, nodeSubnetAnnotationKey, c.config.SubnetId)

	// 将创建的节点组添加到资源列表
	c.resources = append(c.resources, cases.Resource{
		CaseName: SubnetAnnotationCaseName,
		Type:     cases.ResourceTypeCCEInstanceGroup,
		ID:       c.nodeGroupID,
	})

	return nil
}

// scaleNodeGroup 扩容节点组
func (c *subnetAnnotation) scaleNodeGroup(ctx context.Context, replicas int) error {
	logger.Infof(ctx, "扩容节点组 %s 到 %d 个副本", c.nodeGroupID, replicas)

	// 创建扩容请求
	updateRequest := &ccev2.UpdateInstanceGroupReplicasRequest{
		Replicas: replicas,
	}

	// 执行扩容
	_, err := c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, c.base.ClusterID, c.nodeGroupID, updateRequest, nil)
	if err != nil {
		return fmt.Errorf("扩容节点组失败: %v", err)
	}

	logger.Infof(ctx, "已发送扩容请求，目标副本数: %d", replicas)
	return nil
}

// waitAndVerifyNewNodes 等待新节点就绪并验证NRS配置
func (c *subnetAnnotation) waitAndVerifyNewNodes(ctx context.Context) error {
	logger.Infof(ctx, "等待新节点就绪...")

	// 等待节点组就绪
	maxWaitTime := 15 * time.Minute
	checkInterval := 30 * time.Second

	err := wait.PollImmediate(checkInterval, maxWaitTime, func() (bool, error) {
		// 获取节点组状态
		ig, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, c.nodeGroupID, nil)
		if err != nil {
			logger.Warnf(ctx, "获取节点组状态失败: %v", err)
			return false, nil
		}

		logger.Infof(ctx, "节点组状态: 期望副本数=%d, 实际副本数=%d, 就绪副本数=%d",
			ig.InstanceGroup.Spec.Replicas,
			ig.InstanceGroup.Status.ActualReplicas,
			ig.InstanceGroup.Status.ReadyReplicas)

		// 检查是否达到期望状态
		if ig.InstanceGroup.Status.ReadyReplicas == ig.InstanceGroup.Spec.Replicas {
			return true, nil
		}

		return false, nil
	})

	if err != nil {
		return fmt.Errorf("等待节点组就绪超时: %v", err)
	}

	// 获取新节点列表
	currentNodeList, err := c.base.KubeClient.ListNode(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取当前节点列表失败: %v", err)
	}

	// 找出新增的节点
	existingNodeMap := make(map[string]bool)
	for _, nodeName := range c.existingNodes {
		existingNodeMap[nodeName] = true
	}

	for _, node := range currentNodeList.Items {
		if !existingNodeMap[node.Name] {
			// 检查是否属于我们创建的节点组
			if groupID, exists := node.Labels["instance-group-id"]; exists && groupID == c.nodeGroupID {
				c.newNodes = append(c.newNodes, node.Name)
				logger.Infof(ctx, "发现新节点: %s", node.Name)
			}
		}
	}

	if len(c.newNodes) == 0 {
		return fmt.Errorf("未找到新创建的节点")
	}

	logger.Infof(ctx, "找到 %d 个新节点: %v", len(c.newNodes), c.newNodes)

	// 为新节点添加annotation
	for _, nodeName := range c.newNodes {
		if err := c.addNodeAnnotation(ctx, nodeName); err != nil {
			return fmt.Errorf("为节点 %s 添加annotation失败: %v", nodeName, err)
		}
	}

	// 验证新节点的annotation
	for _, nodeName := range c.newNodes {
		if err := c.verifyNodeAnnotation(ctx, nodeName); err != nil {
			return fmt.Errorf("验证节点 %s annotation失败: %v", nodeName, err)
		}
	}

	// 等待一段时间让NRS生成
	logger.Infof(ctx, "等待30秒让NRS生成...")
	time.Sleep(30 * time.Second)

	// 验证新节点的NRS配置
	for _, nodeName := range c.newNodes {
		if err := c.verifyNodeNRS(ctx, nodeName); err != nil {
			return fmt.Errorf("验证节点 %s NRS配置失败: %v", nodeName, err)
		}
	}

	return nil
}

// addAnnotationToExistingNodes 为已有节点添加annotation
func (c *subnetAnnotation) addAnnotationToExistingNodes(ctx context.Context) error {
	if len(c.existingNodes) == 0 {
		logger.Warnf(ctx, "没有已有节点，跳过添加annotation步骤")
		return nil
	}

	// 检查是否有BCC和EBC机器
	bccNodes := make([]string, 0)
	ebcNodes := make([]string, 0)

	for _, nodeName := range c.existingNodes {
		node, err := c.base.KubeClient.GetNode(ctx, nodeName, &kube.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取节点 %s 失败: %v", nodeName, err)
			continue
		}

		// 检查节点类型
		if machineType, exists := node.Labels["node.kubernetes.io/machine-type"]; exists {
			if strings.Contains(machineType, "bcc") {
				bccNodes = append(bccNodes, nodeName)
			} else if strings.Contains(machineType, "ebc") {
				ebcNodes = append(ebcNodes, nodeName)
			}
		}
	}

	logger.Infof(ctx, "发现 %d 个BCC节点，%d 个EBC节点", len(bccNodes), len(ebcNodes))

	if len(bccNodes) == 0 && len(ebcNodes) == 0 {
		logger.Warnf(ctx, "⚠️ 当前集群没有BCC和EBC机器，测试用例有部分缺失")
		return nil
	}

	// 选择一些节点进行annotation添加测试
	var selectedNodes []string
	if len(bccNodes) > 0 {
		selectedNodes = append(selectedNodes, bccNodes[0])
	}
	if len(ebcNodes) > 0 {
		selectedNodes = append(selectedNodes, ebcNodes[0])
	}

	// 为选中的节点添加annotation
	for _, nodeName := range selectedNodes {
		logger.Infof(ctx, "为节点 %s 添加subnet annotation", nodeName)

		node, err := c.base.KubeClient.GetNode(ctx, nodeName, &kube.GetOptions{})
		if err != nil {
			return fmt.Errorf("获取节点 %s 失败: %v", nodeName, err)
		}

		// 添加annotation
		if node.Annotations == nil {
			node.Annotations = make(map[string]string)
		}
		node.Annotations[nodeSubnetAnnotationKey] = c.config.SubnetId

		// 更新节点
		_, err = c.base.KubeClient.UpdateNode(ctx, node)
		if err != nil {
			return fmt.Errorf("更新节点 %s annotation失败: %v", nodeName, err)
		}

		c.modifiedNodes = append(c.modifiedNodes, nodeName)
		logger.Infof(ctx, "成功为节点 %s 添加annotation: %s=%s",
			nodeName, nodeSubnetAnnotationKey, c.config.SubnetId)
	}

	return nil
}

// deleteNRSAndRestartAgent 删除已有节点的NRS并重启agent
func (c *subnetAnnotation) deleteNRSAndRestartAgent(ctx context.Context) error {
	if len(c.modifiedNodes) == 0 {
		logger.Infof(ctx, "没有修改过annotation的节点，跳过删除NRS步骤")
		return nil
	}

	// 删除修改过annotation的节点的NRS
	for _, nodeName := range c.modifiedNodes {
		logger.Infof(ctx, "删除节点 %s 的NRS", nodeName)

		// 使用动态客户端删除NRS
		nrsClient, err := c.base.KubeClient.NewDynamicClient("cce.baidubce.com", "v2", "netresourcesets")
		if err != nil {
			logger.Warnf(ctx, "创建NRS客户端失败: %v", err)
			continue
		}

		err = nrsClient.Delete(ctx, nodeName, metav1.DeleteOptions{})
		if err != nil {
			logger.Warnf(ctx, "删除节点 %s 的NRS失败: %v", nodeName, err)
			// 不返回错误，继续处理其他节点
		} else {
			c.deletedNRSNodes = append(c.deletedNRSNodes, nodeName)
			logger.Infof(ctx, "成功删除节点 %s 的NRS", nodeName)
		}
	}

	// 重启网络agent
	logger.Infof(ctx, "重启网络agent使annotation生效")
	return c.restartNetworkAgent(ctx)
}

// restartNetworkAgent 重启网络agent
func (c *subnetAnnotation) restartNetworkAgent(ctx context.Context) error {
	logger.Infof(ctx, "开始重启网络agent...")

	// 获取DaemonSet
	ds, err := c.base.KubeClient.ClientSet.AppsV1().DaemonSets("kube-system").Get(ctx, networkAgentDaemonSet, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取DaemonSet %s 失败: %v", networkAgentDaemonSet, err)
	}

	// 添加重启注解
	if ds.Spec.Template.Annotations == nil {
		ds.Spec.Template.Annotations = make(map[string]string)
	}

	restartTimestamp := fmt.Sprintf("restart-at-%d", time.Now().Unix())
	ds.Spec.Template.Annotations["kubectl.kubernetes.io/restartedAt"] = restartTimestamp

	// 更新DaemonSet
	_, err = c.base.KubeClient.ClientSet.AppsV1().DaemonSets("kube-system").Update(ctx, ds, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新DaemonSet失败: %v", err)
	}

	logger.Infof(ctx, "已发送重启命令，等待DaemonSet重启完成...")

	// 等待重启完成
	maxWaitTime := 10 * time.Minute
	checkInterval := 15 * time.Second

	err = wait.PollImmediate(checkInterval, maxWaitTime, func() (bool, error) {
		ds, err := c.base.KubeClient.ClientSet.AppsV1().DaemonSets("kube-system").Get(ctx, networkAgentDaemonSet, metav1.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取DaemonSet状态失败: %v", err)
			return false, nil
		}

		logger.Infof(ctx, "DaemonSet状态: 期望数=%d, 就绪数=%d, 更新数=%d",
			ds.Status.DesiredNumberScheduled,
			ds.Status.NumberReady,
			ds.Status.UpdatedNumberScheduled)

		// 检查是否重启完成
		if ds.Status.UpdatedNumberScheduled == ds.Status.DesiredNumberScheduled &&
			ds.Status.NumberReady == ds.Status.DesiredNumberScheduled {
			return true, nil
		}

		return false, nil
	})

	if err != nil {
		return fmt.Errorf("等待DaemonSet重启超时: %v", err)
	}

	logger.Infof(ctx, "网络agent重启完成")
	return nil
}

// verifyExistingNodesNRS 验证已有节点的NRS配置
func (c *subnetAnnotation) verifyExistingNodesNRS(ctx context.Context) error {
	if len(c.modifiedNodes) == 0 {
		logger.Infof(ctx, "没有修改过annotation的节点，跳过验证")
		return nil
	}

	// 等待一段时间让NRS重新生成
	logger.Infof(ctx, "等待60秒让NRS重新生成...")
	time.Sleep(60 * time.Second)

	// 验证修改过annotation的节点的NRS配置
	for _, nodeName := range c.modifiedNodes {
		logger.Infof(ctx, "验证节点 %s 的NRS配置", nodeName)
		if err := c.verifyNodeNRS(ctx, nodeName); err != nil {
			return fmt.Errorf("验证节点 %s NRS配置失败: %v", nodeName, err)
		}
	}

	return nil
}

// verifyNodeAnnotation 验证节点的annotation
func (c *subnetAnnotation) verifyNodeAnnotation(ctx context.Context, nodeName string) error {
	node, err := c.base.KubeClient.GetNode(ctx, nodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取节点失败: %v", err)
	}

	// 检查annotation是否存在
	if node.Annotations == nil {
		return fmt.Errorf("节点没有annotations")
	}

	subnetValue, exists := node.Annotations[nodeSubnetAnnotationKey]
	if !exists {
		return fmt.Errorf("节点缺少subnet annotation: %s", nodeSubnetAnnotationKey)
	}

	if subnetValue != c.config.SubnetId {
		return fmt.Errorf("节点subnet annotation值不正确，期望: %s，实际: %s", c.config.SubnetId, subnetValue)
	}

	logger.Infof(ctx, "✅ 节点 %s annotation验证成功: %s=%s", nodeName, nodeSubnetAnnotationKey, subnetValue)
	return nil
}

// verifyNodeNRS 验证节点的NRS配置
func (c *subnetAnnotation) verifyNodeNRS(ctx context.Context, nodeName string) error {
	// 获取节点的NRS
	nrs, err := c.base.KubeClient.GetNrs(ctx, nodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取节点NRS失败: %v", err)
	}

	logger.Infof(ctx, "节点 %s 的NRS配置:", nodeName)
	logger.Infof(ctx, "  子网配置: %v", nrs.Spec.Eni.SubnetIds)

	// 检查NRS中是否包含指定的subnet
	found := false
	for _, subnetId := range nrs.Spec.Eni.SubnetIds {
		if subnetId == c.config.SubnetId {
			found = true
			break
		}
	}

	if !found {
		return fmt.Errorf("节点 %s 的NRS配置中未找到指定的subnet %s，当前配置: %v",
			nodeName, c.config.SubnetId, nrs.Spec.Eni.SubnetIds)
	}

	logger.Infof(ctx, "✅ 节点 %s NRS配置验证成功，包含指定的subnet: %s", nodeName, c.config.SubnetId)
	return nil
}

// addNodeAnnotation 为节点添加annotation
func (c *subnetAnnotation) addNodeAnnotation(ctx context.Context, nodeName string) error {
	logger.Infof(ctx, "为节点 %s 添加subnet annotation", nodeName)

	node, err := c.base.KubeClient.GetNode(ctx, nodeName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取节点失败: %v", err)
	}

	// 添加annotation
	if node.Annotations == nil {
		node.Annotations = make(map[string]string)
	}
	node.Annotations[nodeSubnetAnnotationKey] = c.config.SubnetId

	// 更新节点
	_, err = c.base.KubeClient.UpdateNode(ctx, node)
	if err != nil {
		return fmt.Errorf("更新节点annotation失败: %v", err)
	}

	logger.Infof(ctx, "成功为节点 %s 添加annotation: %s=%s",
		nodeName, nodeSubnetAnnotationKey, c.config.SubnetId)
	return nil
}

// enableNodeAnnotationSync 启用node annotation sync配置并重启operator
func (c *subnetAnnotation) enableNodeAnnotationSync(ctx context.Context) error {
	logger.Infof(ctx, "启用node annotation sync配置")

	// 1. 获取cce-network-v2-config ConfigMap
	configMap, err := c.base.KubeClient.ClientSet.CoreV1().ConfigMaps("kube-system").Get(ctx, "cce-network-v2-config", metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取cce-network-v2-config ConfigMap失败: %v", err)
	}

	// 2. 检查并修改配置
	ccedConfig, exists := configMap.Data["cced"]
	if !exists {
		return fmt.Errorf("ConfigMap中未找到cced配置")
	}

	logger.Infof(ctx, "当前cced配置:\n%s", ccedConfig)

	// 检查是否已经包含enable-node-annotation-sync配置
	if strings.Contains(ccedConfig, "enable-node-annotation-sync") {
		if strings.Contains(ccedConfig, "enable-node-annotation-sync: true") {
			logger.Infof(ctx, "enable-node-annotation-sync已经设置为true，无需修改")
			return nil
		}
		// 如果存在但不是true，则替换
		ccedConfig = strings.ReplaceAll(ccedConfig, "enable-node-annotation-sync: false", "enable-node-annotation-sync: true")
	} else {
		// 如果不存在，则添加配置
		ccedConfig = ccedConfig + "\n  enable-node-annotation-sync: true"
	}

	// 3. 更新ConfigMap
	configMap.Data["cced"] = ccedConfig
	logger.Infof(ctx, "修改后的cced配置:\n%s", ccedConfig)

	_, err = c.base.KubeClient.ClientSet.CoreV1().ConfigMaps("kube-system").Update(ctx, configMap, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新cce-network-v2-config ConfigMap失败: %v", err)
	}

	logger.Infof(ctx, "成功更新cce-network-v2-config ConfigMap")

	// 4. 重启cce-network-operator
	logger.Infof(ctx, "重启cce-network-operator使配置生效")
	return c.restartNetworkOperator(ctx)
}

// restartNetworkOperator 重启cce-network-operator
func (c *subnetAnnotation) restartNetworkOperator(ctx context.Context) error {
	// 获取cce-network-operator deployment
	deployment, err := c.base.KubeClient.ClientSet.AppsV1().Deployments("kube-system").Get(ctx, "cce-network-operator", metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取cce-network-operator deployment失败: %v", err)
	}

	// 记录原始副本数
	originalReplicas := *deployment.Spec.Replicas

	// 先缩容到0
	logger.Infof(ctx, "缩容cce-network-operator到0个副本")
	replicas := int32(0)
	deployment.Spec.Replicas = &replicas
	_, err = c.base.KubeClient.ClientSet.AppsV1().Deployments("kube-system").Update(ctx, deployment, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("缩容cce-network-operator失败: %v", err)
	}

	// 等待Pod删除
	err = wait.Poll(5*time.Second, 2*time.Minute, func() (bool, error) {
		pods, err := c.base.KubeClient.ClientSet.CoreV1().Pods("kube-system").List(ctx, metav1.ListOptions{
			LabelSelector: "app=cce-network-operator",
		})
		if err != nil {
			logger.Warnf(ctx, "获取cce-network-operator Pod列表失败: %v", err)
			return false, nil
		}

		if len(pods.Items) == 0 {
			logger.Infof(ctx, "cce-network-operator Pod已全部删除")
			return true, nil
		}

		logger.Infof(ctx, "等待cce-network-operator Pod删除，当前Pod数量: %d", len(pods.Items))
		return false, nil
	})

	if err != nil {
		return fmt.Errorf("等待cce-network-operator Pod删除超时: %v", err)
	}

	// 恢复原始副本数
	logger.Infof(ctx, "恢复cce-network-operator到%d个副本", originalReplicas)

	// 重新获取最新的deployment对象以避免冲突
	deployment, err = c.base.KubeClient.ClientSet.AppsV1().Deployments("kube-system").Get(ctx, "cce-network-operator", metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("重新获取cce-network-operator deployment失败: %v", err)
	}

	deployment.Spec.Replicas = &originalReplicas
	_, err = c.base.KubeClient.ClientSet.AppsV1().Deployments("kube-system").Update(ctx, deployment, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("恢复cce-network-operator副本数失败: %v", err)
	}

	// 等待Pod就绪
	err = wait.Poll(5*time.Second, 5*time.Minute, func() (bool, error) {
		deployment, err := c.base.KubeClient.ClientSet.AppsV1().Deployments("kube-system").Get(ctx, "cce-network-operator", metav1.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取cce-network-operator deployment状态失败: %v", err)
			return false, nil
		}

		if deployment.Status.ReadyReplicas == originalReplicas {
			logger.Infof(ctx, "cce-network-operator已就绪，副本数: %d/%d", deployment.Status.ReadyReplicas, originalReplicas)
			return true, nil
		}

		logger.Infof(ctx, "等待cce-network-operator就绪，当前副本数: %d/%d", deployment.Status.ReadyReplicas, originalReplicas)
		return false, nil
	})

	if err != nil {
		return fmt.Errorf("等待cce-network-operator就绪超时: %v", err)
	}

	logger.Infof(ctx, "cce-network-operator重启完成")
	return nil
}
