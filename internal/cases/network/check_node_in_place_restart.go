/*
Copyright 2024 Baidu Inc.

本文件包含了针对节点原地重启的自动化测试用例。
该测试主要验证节点重启后，网络组件的状态是否正常。

用例主要验证以下内容：
1. 选择集群中的一个工作节点进行重启
2. 记录重启前的网络组件状态（agent、nrs、eni等）
3. 执行节点重启操作
4. 等待节点重启完成并重新就绪
5. 检查重启后的网络组件状态是否正常
6. 验证网络功能的连通性

测试流程：
1. 选择目标节点
2. 记录原始状态
3. 重启节点
4. 等待节点就绪
5. 检查网络组件状态
6. 验证网络功能

测试流程详见每个函数的具体实现。
*/

/*
使用config示例：
- name: CheckNodeInPlaceRestart
  config:
    waitTimeoutMinutes: 15
*/

package network

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	// CheckNodeInPlaceRestartCaseName - case 名字
	CheckNodeInPlaceRestartCaseName cases.CaseName = "CheckNodeInPlaceRestart"
)

// nodeRestartConfig 节点重启测试配置
type nodeRestartConfig struct {
	WaitTimeoutMinutes int `json:"waitTimeoutMinutes"` // 等待节点重启完成的超时时间（分钟）
}

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CheckNodeInPlaceRestartCaseName, NewCheckNodeInPlaceRestart)
}

// checkNodeInPlaceRestart 结构体定义
type checkNodeInPlaceRestart struct {
	base              *cases.BaseClient
	config            nodeRestartConfig
	targetNodeName    string        // 目标节点的K8s节点名
	targetInstanceID  string        // 目标节点的CCE实例ID
	targetMachineID   string        // 目标节点的机器ID
	originalNRSStatus string        // 原始NRS状态快照
	originalENIStatus string        // 原始ENI状态快照
	originalNodeInfo  *corev1.Node  // 原始节点信息
	waitTimeout       time.Duration // 等待超时时间
}

// NewCheckNodeInPlaceRestart - 测试案例构造函数
func NewCheckNodeInPlaceRestart(ctx context.Context) cases.Interface {
	return &checkNodeInPlaceRestart{}
}

// Init 初始化测试环境
func (c *checkNodeInPlaceRestart) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base is nil")
	}

	c.base = base

	// 解析配置，如果配置为空使用默认值
	if len(config) > 0 {
		if err := json.Unmarshal(config, &c.config); err != nil {
			return fmt.Errorf("解析配置失败: %v", err)
		}
	}

	// 设置默认配置
	if c.config.WaitTimeoutMinutes == 0 {
		c.config.WaitTimeoutMinutes = 15
	}

	c.waitTimeout = time.Duration(c.config.WaitTimeoutMinutes) * time.Minute

	logger.Infof(ctx, "节点原地重启测试初始化完成，等待超时时间: %d分钟", c.config.WaitTimeoutMinutes)
	return nil
}

// Name 返回测试用例名称
func (c *checkNodeInPlaceRestart) Name() cases.CaseName {
	return CheckNodeInPlaceRestartCaseName
}

// Desc 返回测试用例描述
func (c *checkNodeInPlaceRestart) Desc() string {
	return "验证节点原地重启后，网络组件（agent、nrs、eni）状态是否正常"
}

// Continue 返回是否继续测试
func (c *checkNodeInPlaceRestart) Continue(ctx context.Context) bool {
	return true
}

// ConfigFormat 返回配置格式
func (c *checkNodeInPlaceRestart) ConfigFormat() string {
	return `{
	"waitTimeoutMinutes": 15
}`
}

// Check 执行测试
func (c *checkNodeInPlaceRestart) Check(ctx context.Context) ([]cases.Resource, error) {
	logger.Infof(ctx, "开始执行节点原地重启测试...")

	// 阶段1：选择目标节点
	if err := c.selectTargetNode(ctx); err != nil {
		return nil, fmt.Errorf("选择目标节点失败: %v", err)
	}

	// 阶段2：记录原始状态
	if err := c.recordOriginalStatus(ctx); err != nil {
		return nil, fmt.Errorf("记录原始状态失败: %v", err)
	}

	// 阶段3：重启节点
	if err := c.restartNode(ctx); err != nil {
		return nil, fmt.Errorf("重启节点失败: %v", err)
	}

	// 阶段4：等待节点就绪
	if err := c.waitForNodeReady(ctx); err != nil {
		return nil, fmt.Errorf("等待节点就绪失败: %v", err)
	}

	// 阶段5：检查网络组件状态
	if err := c.checkNetworkComponentStatus(ctx); err != nil {
		return nil, fmt.Errorf("检查网络组件状态失败: %v", err)
	}

	logger.Infof(ctx, "节点原地重启测试完成")
	return nil, nil
}

// selectTargetNode 选择目标节点
func (c *checkNodeInPlaceRestart) selectTargetNode(ctx context.Context) error {
	logger.Infof(ctx, "选择目标节点...")

	// 获取集群节点列表
	instances, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
		ClusterRole: ccetypes.ClusterRoleNode,
		PageNo:      1,
		PageSize:    100,
	}, nil)
	if err != nil {
		return fmt.Errorf("获取节点列表失败: %v", err)
	}

	if len(instances.InstancePage.InstanceList) == 0 {
		return fmt.Errorf("集群中没有可用的节点")
	}

	// 选择第一个运行中的工作节点作为目标
	var targetInstance *ccev2.Instance
	for _, instance := range instances.InstancePage.InstanceList {
		if instance.Status.InstancePhase == ccetypes.InstancePhaseRunning &&
			instance.Spec.ClusterRole == ccetypes.ClusterRoleNode {
			targetInstance = instance
			break
		}
	}

	if targetInstance == nil {
		return fmt.Errorf("没有找到运行中的工作节点")
	}

	c.targetInstanceID = targetInstance.Spec.CCEInstanceID
	c.targetMachineID = targetInstance.Status.Machine.InstanceID
	c.targetNodeName = targetInstance.Status.Machine.K8SNodeName

	logger.Infof(ctx, "选择目标节点成功: CCEInstanceID=%s, MachineID=%s, NodeName=%s",
		c.targetInstanceID, c.targetMachineID, c.targetNodeName)

	return nil
}

// recordOriginalStatus 记录原始状态
func (c *checkNodeInPlaceRestart) recordOriginalStatus(ctx context.Context) error {
	logger.Infof(ctx, "记录节点原始状态...")

	// 记录K8s节点信息
	node, err := c.base.K8SClient.CoreV1().Nodes().Get(ctx, c.targetNodeName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取节点信息失败: %v", err)
	}
	c.originalNodeInfo = node.DeepCopy()

	// 记录NRS状态
	nrsList, err := c.base.KubeClient.ListNrs(ctx, &kube.ListOptions{})
	if err != nil {
		logger.Warnf(ctx, "获取NRS列表失败: %v", err)
	} else {
		// 查找目标节点的NRS
		for _, nrs := range nrsList.Items {
			if strings.Contains(nrs.Name, c.targetNodeName) ||
				strings.Contains(nrs.Name, c.targetMachineID) {
				statusBytes, _ := json.Marshal(nrs.Status)
				c.originalNRSStatus = string(statusBytes)
				logger.Infof(ctx, "记录目标节点NRS状态: %s", nrs.Name)
				break
			}
		}
	}

	// 记录ENI状态
	eniList, err := c.base.BCCClient.ListInstanceEnis(ctx, c.targetMachineID, nil)
	if err != nil {
		logger.Warnf(ctx, "获取ENI列表失败: %v", err)
	} else {
		statusBytes, _ := json.Marshal(eniList)
		c.originalENIStatus = string(statusBytes)
		logger.Infof(ctx, "记录目标节点ENI状态，ENI数量: %d", len(eniList.EniList))
	}

	logger.Infof(ctx, "原始状态记录完成")
	return nil
}

// restartNode 重启节点
func (c *checkNodeInPlaceRestart) restartNode(ctx context.Context) error {
	logger.Infof(ctx, "开始重启节点: %s", c.targetMachineID)

	// 步骤1：停止实例
	logger.Infof(ctx, "停止实例...")
	stopArgs := &bccapi.StopInstanceArgs{
		ForceStop:        false,
		StopWithNoCharge: true,
	}

	if err := c.base.BCCClient.StopInstanceWithNoCharge(ctx, c.targetMachineID, stopArgs, nil); err != nil {
		return fmt.Errorf("停止实例失败: %v", err)
	}

	// 步骤2：等待实例停止
	logger.Infof(ctx, "等待实例停止...")
	if err := c.waitForInstanceStatus(ctx, "Stopped", 5*time.Minute); err != nil {
		return fmt.Errorf("等待实例停止失败: %v", err)
	}

	// 步骤3：启动实例
	logger.Infof(ctx, "启动实例...")
	if err := c.base.BCCClient.StartInstance(ctx, c.targetMachineID, nil); err != nil {
		return fmt.Errorf("启动实例失败: %v", err)
	}

	// 步骤4：等待实例运行
	logger.Infof(ctx, "等待实例运行...")
	if err := c.waitForInstanceStatus(ctx, "Running", 5*time.Minute); err != nil {
		return fmt.Errorf("等待实例运行失败: %v", err)
	}

	logger.Infof(ctx, "节点重启完成")
	return nil
}

// waitForInstanceStatus 等待实例状态
func (c *checkNodeInPlaceRestart) waitForInstanceStatus(ctx context.Context, targetStatus string, timeout time.Duration) error {
	startTime := time.Now()
	checkInterval := 10 * time.Second

	for {
		if time.Since(startTime) > timeout {
			return fmt.Errorf("等待实例状态 %s 超时", targetStatus)
		}

		// 获取实例状态
		instance, err := c.base.BCCClient.DescribeInstance(ctx, c.targetMachineID, nil)
		if err != nil {
			logger.Warnf(ctx, "获取实例状态失败: %v，将重试", err)
			time.Sleep(checkInterval)
			continue
		}

		logger.Infof(ctx, "当前实例状态: %s，目标状态: %s", instance.Status, targetStatus)

		if string(instance.Status) == targetStatus {
			logger.Infof(ctx, "实例已达到目标状态: %s", targetStatus)
			return nil
		}

		time.Sleep(checkInterval)
	}
}

// waitForNodeReady 等待节点就绪
func (c *checkNodeInPlaceRestart) waitForNodeReady(ctx context.Context) error {
	logger.Infof(ctx, "等待节点就绪...")

	startTime := time.Now()
	checkInterval := 15 * time.Second

	for {
		if time.Since(startTime) > c.waitTimeout {
			return fmt.Errorf("等待节点就绪超时")
		}

		// 获取节点状态
		node, err := c.base.K8SClient.CoreV1().Nodes().Get(ctx, c.targetNodeName, metav1.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取节点状态失败: %v，将重试", err)
			time.Sleep(checkInterval)
			continue
		}

		// 检查节点是否就绪
		if c.isNodeReady(node) {
			logger.Infof(ctx, "节点已就绪")
			return nil
		}

		logger.Infof(ctx, "节点尚未就绪，继续等待...")
		time.Sleep(checkInterval)
	}
}

// isNodeReady 检查节点是否就绪
func (c *checkNodeInPlaceRestart) isNodeReady(node *corev1.Node) bool {
	for _, condition := range node.Status.Conditions {
		if condition.Type == corev1.NodeNetworkUnavailable {
			return condition.Status == corev1.ConditionFalse
		}
	}
	return false
}

// checkNetworkComponentStatus 检查网络组件状态
func (c *checkNodeInPlaceRestart) checkNetworkComponentStatus(ctx context.Context) error {
	logger.Infof(ctx, "检查网络组件状态...")

	// 1. 检查网络agent状态
	if err := c.checkNetworkAgent(ctx); err != nil {
		return fmt.Errorf("检查网络agent状态失败: %v", err)
	}

	// 2. 检查NRS状态
	if err := c.checkNRSStatus(ctx); err != nil {
		return fmt.Errorf("检查NRS状态失败: %v", err)
	}

	// 3. 检查ENI状态
	if err := c.checkENIStatus(ctx); err != nil {
		return fmt.Errorf("检查ENI状态失败: %v", err)
	}

	logger.Infof(ctx, "所有网络组件状态检查完成")
	return nil
}

// checkNetworkAgent 检查网络agent状态
func (c *checkNodeInPlaceRestart) checkNetworkAgent(ctx context.Context) error {
	logger.Infof(ctx, "检查网络agent状态...")

	// 获取目标节点上的网络agent Pod
	podList, err := c.base.K8SClient.CoreV1().Pods(cniNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("app.cce.baidubce.com=%s", cniAgentName),
		FieldSelector: fmt.Sprintf("spec.nodeName=%s", c.targetNodeName),
	})
	if err != nil {
		return fmt.Errorf("获取网络agent Pod失败: %v", err)
	}

	if len(podList.Items) == 0 {
		return fmt.Errorf("没有找到目标节点上的网络agent Pod")
	}

	// 检查Pod状态
	for _, pod := range podList.Items {
		if pod.Status.Phase != corev1.PodRunning {
			return fmt.Errorf("网络agent Pod %s 状态不正常: %s", pod.Name, pod.Status.Phase)
		}

		// 检查Pod是否就绪
		isPodReady := false
		for _, condition := range pod.Status.Conditions {
			if condition.Type == corev1.PodReady && condition.Status == corev1.ConditionTrue {
				isPodReady = true
				break
			}
		}

		if !isPodReady {
			return fmt.Errorf("网络agent Pod %s 尚未就绪", pod.Name)
		}

		logger.Infof(ctx, "网络agent Pod %s 状态正常", pod.Name)
	}

	logger.Infof(ctx, "网络agent状态检查完成")
	return nil
}

// checkNRSStatus 检查NRS状态
func (c *checkNodeInPlaceRestart) checkNRSStatus(ctx context.Context) error {
	logger.Infof(ctx, "检查NRS状态...")

	// 获取NRS列表
	nrsList, err := c.base.KubeClient.ListNrs(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取NRS列表失败: %v", err)
	}

	// 查找目标节点的NRS
	var targetNRS *ccetypes.NetworkResourceSet
	for _, nrs := range nrsList.Items {
		if strings.Contains(nrs.Name, c.targetNodeName) ||
			strings.Contains(nrs.Name, c.targetMachineID) {
			targetNRS = &nrs
			break
		}
	}

	if targetNRS == nil {
		return fmt.Errorf("没有找到目标节点的NRS")
	}

	// 检查NRS状态
	if len(targetNRS.Status.Enis) == 0 {
		return fmt.Errorf("NRS %s 没有ENI资源", targetNRS.Name)
	}

	// 检查NRS是否有IP资源
	if len(targetNRS.Status.Ipam.Used) == 0 {
		logger.Warnf(ctx, "NRS %s 没有已分配的IP资源", targetNRS.Name)
	}

	logger.Infof(ctx, "NRS %s 状态正常，ENI数量: %d", targetNRS.Name, len(targetNRS.Status.Enis))
	return nil
}

// checkENIStatus 检查ENI状态
func (c *checkNodeInPlaceRestart) checkENIStatus(ctx context.Context) error {
	logger.Infof(ctx, "检查ENI状态...")

	// 获取ENI列表
	eniList, err := c.base.BCCClient.ListInstanceEnis(ctx, c.targetMachineID, nil)
	if err != nil {
		return fmt.Errorf("获取ENI列表失败: %v", err)
	}

	if len(eniList.EniList) == 0 {
		return fmt.Errorf("目标节点没有ENI网卡")
	}

	// 检查ENI状态
	for _, eni := range eniList.EniList {
		if eni.Status != "inuse" {
			return fmt.Errorf("ENI %s 状态不正常: %s", eni.ENIID, eni.Status)
		}
		logger.Infof(ctx, "ENI %s 状态正常", eni.ENIID)
	}

	logger.Infof(ctx, "ENI状态检查完成，ENI数量: %d", len(eniList.EniList))
	return nil
}

// Clean 清理测试环境
func (c *checkNodeInPlaceRestart) Clean(ctx context.Context) error {
	logger.Infof(ctx, "清理测试环境...")

	// 这个测试用例不需要清理额外的资源
	// 因为我们只是重启了节点，没有创建新的资源

	logger.Infof(ctx, "测试环境清理完成")
	return nil
}
