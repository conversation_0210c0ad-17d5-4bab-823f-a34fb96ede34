package network

import (
	"context"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	NetworkPreCheck cases.CaseName = "NetworkPreCheck"
)

type networkPreCheck struct {
	base *cases.BaseClient
}

func init() {
	cases.AddCase(context.TODO(), NetworkPreCheck, NewNetworkPreCheck)
}

func NewNetworkPreCheck(ctx context.Context) cases.Interface {
	return &networkPreCheck{}
}

func (c *networkPreCheck) Name() cases.CaseName {
	return NetworkPreCheck
}

func (c *networkPreCheck) Desc() string {
	return "容器网络用例前置准备"
}

func (c *networkPreCheck) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	c.base = base
	return
}

func (c *networkPreCheck) Check(ctx context.Context) (resources []cases.Resource, err error) {
	// 获取第一个节点组
	resp, err := c.base.CCEClient.ListInstanceGroups(ctx, c.base.ClusterID, nil, nil)
	if err != nil {
		return nil, fmt.Errorf("list instance groups: %v", err)
	}
	if len(resp.Page.List) == 0 {
		return nil, fmt.Errorf("no instance group found")
	}

	instanceGroupID := resp.Page.List[0].Spec.CCEInstanceGroupID
	currentReplicas := resp.Page.List[0].Spec.Replicas

	// 将所有节点的关闭缩容保护
	err = c.setInstanceGroupInstanceScaleDown(ctx, instanceGroupID, false)
	if err != nil {
		return nil, fmt.Errorf("set instance group instance scale down: %v", err)
	}

	// 更新节点组，将replicas设置为0
	err = c.scaleInstanceGroup(ctx, instanceGroupID, 0)
	if err != nil {
		return nil, fmt.Errorf("scale instance group: %v", err)
	}

	// 等待节点组更新完成
	err = c.checkInstanceGroupReplicas(ctx, instanceGroupID)
	if err != nil {
		return nil, fmt.Errorf("check instance group replicas: %v", err)
	}

	// 更新节点组，将replicas恢复为原始值
	err = c.scaleInstanceGroup(ctx, instanceGroupID, currentReplicas)
	if err != nil {
		return nil, fmt.Errorf("scale instance group: %v", err)
	}

	// 等待节点组更新完成
	err = c.checkInstanceGroupReplicas(ctx, instanceGroupID)
	if err != nil {
		return nil, fmt.Errorf("check instance group replicas: %v", err)
	}

	// 将所有节点的开启缩容保护
	err = c.setInstanceGroupInstanceScaleDown(ctx, instanceGroupID, true)
	if err != nil {
		return nil, fmt.Errorf("set instance group instance scale down: %v", err)
	}

	return
}

func (c *networkPreCheck) Clean(ctx context.Context) error {
	return nil
}

func (c *networkPreCheck) Continue(ctx context.Context) bool {
	return true
}

func (c *networkPreCheck) ConfigFormat() string {
	return ""
}

// setInstanceGroupInstanceScaleDown 设置节点组内所有实例的缩容保护状态
func (c *networkPreCheck) setInstanceGroupInstanceScaleDown(ctx context.Context, instanceGroupID string, scaleDownEnabled bool) error {
	instancesResp, err := c.base.CCEClient.ListInstancesByInstanceGroupID(ctx, c.base.ClusterID, instanceGroupID, 0, 0, nil)
	if err != nil {
		logger.Warnf(ctx, "获取节点组实例列表失败: %v", err)
	}
	instanceIDs := make([]string, 0)
	for _, instance := range instancesResp.Page.List {
		instanceIDs = append(instanceIDs, instance.Spec.CCEInstanceID)
	}
	_, err = c.base.CCEClient.UpdateInstanceScaleDownDisabled(ctx, c.base.ClusterID, &ccev2.UpdateNodeScaleDownRequest{
		InstanceIDs:       instanceIDs,
		ScaleDownDisabled: scaleDownEnabled,
	})
	if err != nil {
		return fmt.Errorf("update node scale down disabled: %v", err)
	}
	return err
}

// scaleInstanceGroup 对节点组进行扩缩容操作
func (c *networkPreCheck) scaleInstanceGroup(ctx context.Context, instanceGroupID string, replicas int) error {
	// 先获取节点组的当前状态
	ig, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, instanceGroupID, nil)
	if err != nil {
		return fmt.Errorf("获取节点组信息失败: %v", err)
	}

	currentReplicas := ig.InstanceGroup.Spec.Replicas
	if currentReplicas == replicas {
		logger.Infof(ctx, "节点组 %s 当前副本数已经是目标值 %d，无需调整", instanceGroupID, replicas)
		return nil
	}

	// 获取节点组当前的实例列表
	instancesResp, err := c.base.CCEClient.ListInstancesByInstanceGroupID(ctx, c.base.ClusterID, instanceGroupID, 0, 0, nil)
	if err != nil {
		logger.Warnf(ctx, "获取节点组实例列表失败: %v", err)
	} else {
		logger.Infof(ctx, "节点组 %s 当前有 %d 个实例", instanceGroupID, len(instancesResp.Page.List))
		for i, instance := range instancesResp.Page.List {
			if i < 5 { // 只显示前5个实例信息
				logger.Infof(ctx, "  实例 #%d: ID=%s, 名称=%s, 状态=%s",
					i+1, instance.Spec.CCEInstanceID, instance.Spec.InstanceName, instance.Status.InstancePhase)
			} else {
				logger.Infof(ctx, "  还有 %d 个实例未显示...", len(instancesResp.Page.List)-5)
				break
			}
		}
	}

	// 构建扩缩容请求
	request := &ccev2.UpdateInstanceGroupReplicasRequest{
		Replicas: replicas,
	}

	operation := "扩容"
	if replicas < currentReplicas {
		operation = "缩容"
	}

	logger.Infof(ctx, "开始执行节点组 %s %s操作: %d -> %d", instanceGroupID, operation, currentReplicas, replicas)

	// 发送请求
	maxRetries := 3
	retryInterval := 5 * time.Second
	var resp *ccev2.UpdateInstanceGroupReplicasResponse

	// 使用重试逻辑发送扩缩容请求
	for i := 0; i < maxRetries; i++ {
		resp, err = c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, c.base.ClusterID, instanceGroupID, request, nil)
		if err == nil {
			break
		}

		if i < maxRetries-1 {
			logger.Warnf(ctx, "更新节点组副本数失败(重试 %d/%d): %v", i+1, maxRetries, err)
			time.Sleep(retryInterval)
		}
	}

	if err != nil {
		return fmt.Errorf("更新节点组副本数失败: %v", err)
	}

	logger.Infof(ctx, "节点组 %s %s请求发送成功，RequestID: %s", instanceGroupID, operation, resp.RequestID)

	// 再次获取节点组状态以确认请求已被接受
	time.Sleep(2 * time.Second)

	ig, err = c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, instanceGroupID, nil)
	if err != nil {
		logger.Warnf(ctx, "获取更新后的节点组信息失败: %v", err)
	} else {
		if ig.InstanceGroup.Spec.Replicas == replicas {
			logger.Infof(ctx, "节点组 %s 目标副本数已更新为 %d", instanceGroupID, replicas)
		} else {
			logger.Warnf(ctx, "节点组 %s 目标副本数未更新，当前仍为 %d，期望为 %d",
				instanceGroupID, ig.InstanceGroup.Spec.Replicas, replicas)
		}

		// 显示节点组当前的扩缩容状态
		logger.Infof(ctx, "节点组 %s 当前状态: ", instanceGroupID)
		logger.Infof(ctx, "  期望副本数: %d", ig.InstanceGroup.Spec.Replicas)
		logger.Infof(ctx, "  就绪副本数: %d", ig.InstanceGroup.Status.ReadyReplicas)
		logger.Infof(ctx, "  扩缩容中副本数: %d", ig.InstanceGroup.Status.ScalingReplicas)
		logger.Infof(ctx, "  删除中副本数: %d", ig.InstanceGroup.Status.DeletingReplicas)
	}

	return nil
}

// checkInstanceGroupReplicas 等待节点组副本数达到目标值
func (c *networkPreCheck) checkInstanceGroupReplicas(ctx context.Context, instanceGroupID string) error {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()
	timer := time.NewTimer(15 * time.Minute)
	defer timer.Stop()

	for {
		select {
		case <-ticker.C:
			getIGResp, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, instanceGroupID, nil)
			if err != nil {
				logger.WithValues("instanceGroupID", instanceGroupID).Errorf(ctx, "failed to get instanceGroup, err: %v", err)
				continue
			}

			logger.WithValues("instanceGroupID", instanceGroupID).
				Infof(ctx, "instanceGroup expected replicas: %d, actual: %d",
					getIGResp.InstanceGroup.Spec.Replicas, getIGResp.InstanceGroup.Status.ReadyReplicas)
			if getIGResp.InstanceGroup.Spec.Replicas != getIGResp.InstanceGroup.Status.ReadyReplicas {
				continue
			}
			getIGInstances, err := c.base.CCEClient.ListInstancesByInstanceGroupID(ctx, c.base.ClusterID, instanceGroupID, 0, 0, nil)
			if err != nil {
				logger.Errorf(ctx, "list instance by instancegroupID failed: %v", err)
				continue
			}
			allInstanceReady := true
			for _, instance := range getIGInstances.Page.List {
				if instance.Status.InstancePhase != ccetypes.InstancePhaseRunning {
					logger.Warnf(ctx, "instance %s phase is %s, not running", instance.Spec.CCEInstanceID, instance.Status.InstancePhase)
					allInstanceReady = false
				}
			}
			if !allInstanceReady {
				logger.Warnf(ctx, "not all instances are running")
				continue
			}

			return nil
		case <-timer.C:
			logger.WithValues("instanceGroupID", instanceGroupID).Errorf(ctx, "timeout waiting for instanceGroup replicas ready")
			return errors.New("timeout waiting for instanceGroup replicas ready")
		}
	}
}
