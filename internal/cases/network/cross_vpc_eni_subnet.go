/*
modification history
--------------------
2024/9/2, by shimingming<PERSON>, create
*/
/*
DESCRIPTION
场景：跨VPC挂ENI不支持VPC辅助网段，导致CCE metapro集群分配apiserver ip网关与node ip冲突导致单节点访问apiserver不通。具体原因是：路由没有使得流量正确从该网卡出去。
1. 校验apiserver容器内的ip route使用的cidr是否和选择的apiserver子网cidr一致。

测试流程：
1. 用户托管集群在A区资源账户下，需要先获取集群的kubeconfig。
2. 获取用户托管集群的子网信息。
3. 获取用户托管集群apiserver的pod名称。
4. exec apiserver pod，执行ip route命令，比对cidr是否一致。
*/

package network

import (
	"bufio"
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"sigs.k8s.io/yaml"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CrossVPCENISubnet cases.CaseName = "CrossVPCENISubnet"
)

const (
	apiServerDeploymentName = "kube-apiserver"
	apiServerContainerName  = "apiserver"
)

type crossVPCENISubnet struct {
	base              *cases.BaseClient
	proMetaKubeClient *kube.Client
}

type crossVPCENISubnetConfig struct {
	ClusterID      string                `json:"ResClusterID"`
	KubeConfigType models.KubeConfigType `json:"KubeConfigType"`
}

func init() {
	cases.AddCase(context.TODO(), CrossVPCENISubnet, NewCrossVPCENISubnet)
}

func NewCrossVPCENISubnet(ctx context.Context) cases.Interface {
	return &crossVPCENISubnet{}
}

func (c *crossVPCENISubnet) Name() cases.CaseName {
	return CrossVPCENISubnet
}

func (c *crossVPCENISubnet) Desc() string {
	return "跨vpc弹性网卡子网选取验证"
}

func (c *crossVPCENISubnet) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	if base == nil {
		return errors.New("base is nil")
	}
	var cfg crossVPCENISubnetConfig
	err = yaml.Unmarshal(config, &cfg)
	if err != nil {
		err = fmt.Errorf("unmarshal case config failed: %v", err)
		return
	}
	if cfg.ClusterID == "" || cfg.KubeConfigType == "" {
		err = errors.New("case config is invalid, `ClusterID` or `KubeConfigType` is empty")
		return
	}

	// 获得资源账号集群的kube client
	kubeClient, newKubeClientErr := kube.NewClientByClusterID(ctx, cfg.ClusterID, cfg.KubeConfigType, base.MetaClient, 30*time.Second, true)
	if newKubeClientErr != nil {
		err = fmt.Errorf("create kube client failed: %v", newKubeClientErr)
		return
	}
	c.base = base
	c.proMetaKubeClient = kubeClient
	return
}

func (c *crossVPCENISubnet) Check(ctx context.Context) (resources []cases.Resource, err error) {
	clusterID := c.base.ClusterID
	clusterBLBVPCSubnetID := c.base.ClusterSpec.MasterConfig.ClusterBLBVPCSubnetID

	// 获取托管集群APIServer的子网信息
	subnetInfo, getSubnetErr := c.base.VPCClient.DescribeSubnet(ctx, clusterBLBVPCSubnetID, nil)
	if getSubnetErr != nil {
		err = fmt.Errorf("VPCClient.DescribeSubnet failed: %v", getSubnetErr)
		return
	}

	logger.Infof(ctx, "clusterBLBVPCSubnetID: %s, subnetCIDR: %s", clusterID, subnetInfo.CIDR)

	// 获取apiserver的pod名称，由于deployment的缘故，pod名称后缀是随机的，这里通过label去获取pod并选择第一个pod
	deployment, getDeployErr := c.proMetaKubeClient.GetDeploymentAppsV1(ctx, clusterID, apiServerDeploymentName, &kube.GetOptions{})
	if getDeployErr != nil {
		err = fmt.Errorf("proMetaClientSet.AppsV1().Deployments get failed: %v", getDeployErr)
		return
	}
	deploymentMatchLabels := deployment.Spec.Selector.MatchLabels

	pods, getPodsErr := c.proMetaKubeClient.ListPod(ctx, clusterID, &kube.ListOptions{
		LabelSelector: deploymentMatchLabels,
	})
	if getPodsErr != nil {
		err = fmt.Errorf("proMetaClientSet.CoreV1().Pods failed: %v", getPodsErr)
		return
	}
	if len(pods.Items) == 0 {
		err = fmt.Errorf("list pod empty for deployment: %s", apiServerDeploymentName)
		return
	}
	onePod := pods.Items[0]

	// 创建一个exec的请求，command = `ip route`，这里it都是false
	stdout, execErr := c.proMetaKubeClient.RemoteExec(clusterID, onePod.Name, apiServerContainerName, []string{"ip", "route"})
	if execErr != nil {
		err = fmt.Errorf("pod RemoteExec failed: %v", execErr)
		return
	}
	if len(stdout) == 0 {
		err = errors.New("exec stream failed, exec command `ip route` result empty")
		return
	}
	// ip route 输出样例，这里需要检查 subnetInfo.CIDR 是否在规则结果中
	// 这条路由规则告诉系统，当需要向***********/16网络发送数据包时，应该通过名为apiserver的网络接口，使用************作为源IP地址发送数据包。
	// default dev apiserver
	// *******/8 via *********** dev eth0
	// *********** dev eth0 scope link
	// ***********/16 dev apiserver proto kernel scope link src ************
	var isSubnetCIDREqual bool
	scanner := bufio.NewScanner(strings.NewReader(stdout))
	for scanner.Scan() {
		line := scanner.Text()
		if strings.Contains(line, fmt.Sprintf("dev %s", apiServerContainerName)) {
			logger.Infof(ctx, "matched route rule line: %s", line)
			if strings.Contains(line, subnetInfo.CIDR) {
				isSubnetCIDREqual = true
				break
			}
		}
	}
	if !isSubnetCIDREqual {
		err = fmt.Errorf("subnetInfo CIDR %s not in `ip route` output", subnetInfo.CIDR)
		return
	}
	return
}

func (c *crossVPCENISubnet) Clean(ctx context.Context) error {
	return nil
}

func (c *crossVPCENISubnet) Continue(ctx context.Context) bool {
	return true
}

func (c *crossVPCENISubnet) ConfigFormat() string {
	return ""
}
