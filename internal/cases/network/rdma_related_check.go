/* rdma_related_check.go */
/*
modification history
--------------------
2025/3/17, by <PERSON><PERSON>, create
----------------------
2025/4/25, by <PERSON>, modified
*/
/*
DESCRIPTION
2025/3/17
nodeName is the node name of the node to be checked
rdmaNetCount is the number of RDMA network resources expected on the node
ipPerRdmaEni is the number of IPs expected on each RDMA ENI
  - name: RdmaRelatedCheck
    config:
      nodeName: *************
      rdmaNetCount: 4
      ipPerRdmaEni: 12
--------------------
2025/4/30 已实现功能，未实验验证
补充rdma容器间ib命令互通性校验方法（新方法，主流程调用）。
补充NRS maxIpsPerENI参数校验（在ValidateRDMAResources中）。
补充rdma pod内网卡、路由、mac、邻表校验（扩展ValidateRDMAPodNetInterfaces）。
补充手动添加ENI/ERI后资源校验（新方法，主流程调用）。
补充节点名>48和节点删除后资源回收校验（新方法，主流程调用）。
*/

package network

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
	v1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	k8sResource "k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/tools/remotecommand"
	"sigs.k8s.io/yaml"

	bcc "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	bccimage "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bccimage"
	ccev2 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	internalvpc "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/internalvpc"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	RdmaRelatedCheck        = "RdmaRelatedCheck"
	rdmaWorkloadName        = "deployment-test"
	maxIpsPerENIforRdmaRoce = 13
)

type rdmaNodeConfig struct {
	NodeName     string `json:"nodeName"`
	RdmaNetCount int    `json:"rdmaNetCount"`
	IpPerRdmaEni int    `json:"ipPerRdmaEni"`
}

type rdmaRelatedCheck struct {
	base      *cases.BaseClient
	Config    rdmaNodeConfig `json:"config"`
	nrsClient dynamic.NamespaceableResourceInterface
	cepClient dynamic.NamespaceableResourceInterface
	eniClient dynamic.NamespaceableResourceInterface
}

func init() {
	cases.AddCase(context.TODO(), RdmaRelatedCheck, NewRdmaRelatedCheck)
}

func NewRdmaRelatedCheck(ctx context.Context) cases.Interface {
	return &rdmaRelatedCheck{}
}

func (c *rdmaRelatedCheck) Name() cases.CaseName {
	return RdmaRelatedCheck
}

func (c *rdmaRelatedCheck) Desc() string {
	return "Check RDMA related resources and configurations"
}

func (c *rdmaRelatedCheck) Init(ctx context.Context, config []byte, base *cases.BaseClient) (err error) {
	if base == nil {
		return errors.New("base is nil")
	}

	logger.WithValues("case", "RdmaRelatedCheck").Infof(ctx, "json raw config: %s", string(config))
	if err := json.Unmarshal(config, &c.Config); err != nil {
		return err
	}
	logger.WithValues("case", "RdmaRelatedCheck").Infof(ctx, "config: %s", utils.ToJSON(c.Config))

	c.base = base
	nrsClient, err := NewNRSClient(ctx, []byte(c.base.KubeConfig))
	if err != nil {
		return fmt.Errorf("new netresourceset client failed: %v", err.Error())
	}
	c.nrsClient = nrsClient

	eniClient, err := NewENIClient(ctx, []byte(c.base.KubeConfig))
	if err != nil {
		return fmt.Errorf("new eni client failed: %v", err.Error())
	}
	c.eniClient = eniClient

	cepClient, err := NewCepClient(ctx, []byte(c.base.KubeConfig))
	if err != nil {
		return fmt.Errorf("new eni client failed: %v", err.Error())
	}
	c.cepClient = cepClient

	return nil

}

func (c *rdmaRelatedCheck) Check(ctx context.Context) (resources []cases.Resource, err error) {
	nodeName := c.Config.NodeName
	// Stage 1: Validate RDMA metadata resources
	if err := c.ValidateRDMAResources(ctx, c.eniClient, c.nrsClient, nodeName); err != nil {
		return nil, fmt.Errorf("RDMA resources validation failed for node %s: %v", nodeName, err)
	}

	// 新增：节点名>48和节点删除后资源回收校验
	if err := c.ValidateNodeNameLengthAndDeleteResource(ctx); err != nil {
		return nil, fmt.Errorf("节点名长度和节点删除后资源回收校验失败: %v", err)
	}

	defer func() {
		c.deleteRDMADeployment(ctx)
	}()

	//Stage 2: Create test deployment if configured
	if c.Config.RdmaNetCount > 0 {
		err = c.applyRDMADeployment(ctx, 4, nodeName)
		if err != nil {
			return nil, fmt.Errorf("create deployment failed, err: %v", err)
		}

		// Wait for deployment to be ready
		if err := c.ensureDeploymentRunning(ctx, 1*time.Second, 10*time.Second); err != nil {
			return nil, fmt.Errorf("wait for deployment running failed, err: %v", err)
		}

		// 新增：检查 NRS 状态中的 pod 引用
		if err := c.ValidateNRSPodReference(ctx, nodeName, rdmaWorkloadName); err != nil {
			return nil, fmt.Errorf("验证 NRS 中的 pod 引用失败: %v", err)
		}

		// Stage 2: Validate RDMA pod endpoints
		if err := c.ValidateRDMAPodEndpoints(ctx, c.nrsClient, c.cepClient, nodeName, rdmaWorkloadName); err != nil {
			return nil, fmt.Errorf("RDMA pod endpoints validation failed for node %s: %v", nodeName, err)
		}

		// Stage 3: Validate RDMA pod network interfaces
		if err := c.ValidateRDMAPodNetInterfaces(ctx, c.nrsClient, c.base, rdmaWorkloadName); err != nil {
			return nil, fmt.Errorf("RDMA pod network interfaces validation failed for node %s: %v", nodeName, err)
		}

		// 新增：校验rdma容器间ib命令互通性
		if err := c.ValidateRDMAPodIBConnectivity(ctx, rdmaWorkloadName); err != nil {
			return nil, fmt.Errorf("RDMA pod IB互通性校验失败: %v", err)
		}

		// Stage 4: Scale up deployment and validate pod count
		desiredReplicas := int32(c.Config.IpPerRdmaEni + 2) // Scale to more than RdmaNetCount
		err = c.scaleDeployment(ctx, desiredReplicas)
		if err != nil {
			return nil, fmt.Errorf("failed to scale deployment: %v", err)
		}

		// Wait for deployment status to stabilize
		time.Sleep(10 * time.Second)

		// Get running pods
		pods, err := c.getPodsByDeployment(ctx, rdmaWorkloadName, corev1.NamespaceDefault)
		if err != nil {
			return nil, fmt.Errorf("failed to get pods: %v", err)
		}

		// Count ready pods
		readyPods := 0
		for _, pod := range pods {
			for _, condition := range pod.Status.Conditions {
				if condition.Type == corev1.PodReady && condition.Status == corev1.ConditionTrue {
					readyPods++
					break
				}
			}
		}

		// Verify ready pod count matches RdmaNetCount
		if readyPods != c.Config.IpPerRdmaEni {
			return nil, fmt.Errorf("expected %d ready pods but got %d", c.Config.RdmaNetCount, readyPods)
		}

		// Stage 5: Scale down deployment and validate pod count
		desiredReplicas = 1
		err = c.scaleDeployment(ctx, desiredReplicas)
		if err != nil {
			return nil, fmt.Errorf("failed to scale deployment: %v", err)
		}

		// Wait for deployment status to stabilize
		time.Sleep(10 * time.Second)

		// 新增：检查被删除的 pod 对应的 NRS 状态是否被清理
		if err := c.ValidateNRSPodCleanup(ctx, nodeName, rdmaWorkloadName); err != nil {
			return nil, fmt.Errorf("验证 NRS 中已删除 pod 的清理失败: %v", err)
		}
	}

	return
}

func (c *rdmaRelatedCheck) ensureDeploymentRunning(ctx context.Context, interval, timeout time.Duration) error {
	// Wait for all replicas to be running
	logger.Infof(ctx, "start to ensure deployment %s running", rdmaWorkloadName)
	err := resource.WaitForFunc(ctx, func(ctx context.Context) (err error) {
		deploymentResource, getErr := c.base.AppClient.GetAppDeploymentByName(ctx, c.base.ClusterID, rdmaWorkloadName, corev1.NamespaceDefault, nil)
		if getErr != nil {
			err = fmt.Errorf("get deployment Resource: %v failed, err: %v", rdmaWorkloadName, err)
			return
		}
		if deploymentResource.Status != "Running" || deploymentResource.StatusInfo.Available != deploymentResource.StatusInfo.Replicas {
			err = fmt.Errorf("deployment status: %v, available: %d, expect replicas: %d", deploymentResource.Status,
				deploymentResource.StatusInfo.Available, deploymentResource.StatusInfo.Replicas)
			return
		}
		return
	}, interval, timeout)
	if err != nil {
		return fmt.Errorf("wait for deployment running failed, err: %v", err)
	}
	logger.Infof(ctx, "deployment %s is running", rdmaWorkloadName)
	return nil
}

func (c *rdmaRelatedCheck) Clean(ctx context.Context) error {
	return nil
}

func (c *rdmaRelatedCheck) Continue(ctx context.Context) bool {
	return true
}

func (c *rdmaRelatedCheck) ConfigFormat() string {
	return ""
}

// NewCEPClient 自定义资源cceEndpoint的client
func NewNRSClient(ctx context.Context, configBytes []byte) (dynamic.NamespaceableResourceInterface, error) {
	config, err := clientcmd.RESTConfigFromKubeConfig(configBytes)
	if err != nil {
		logger.Errorf(ctx, "RESTConfigFromKubeConfig failed: %s", err)
		return nil, err
	}

	// 获取 Dynamic 客户端
	dynamicClient := dynamic.NewForConfigOrDie(config)

	// 指定自定义资源的 Group 和 Version
	groupVersion := schema.GroupVersion{
		Group:   "cce.baidubce.com",
		Version: "v2",
	}

	// 获取自定义资源的 Dynamic 客户端
	return dynamicClient.Resource(
		schema.GroupVersionResource{
			Group:    groupVersion.Group,
			Version:  groupVersion.Version,
			Resource: "netresourcesets",
		},
	), nil
}

func NewENIClient(ctx context.Context, configBytes []byte) (dynamic.NamespaceableResourceInterface, error) {
	config, err := clientcmd.RESTConfigFromKubeConfig(configBytes)
	if err != nil {
		logger.Errorf(ctx, "RESTConfigFromKubeConfig failed: %s", err)
		return nil, err
	}

	// 获取 Dynamic 客户端
	dynamicClient := dynamic.NewForConfigOrDie(config)

	// 指定自定义资源的 Group 和 Version
	groupVersion := schema.GroupVersion{
		Group:   "cce.baidubce.com",
		Version: "v2",
	}

	// 获取自定义资源的 Dynamic 客户端
	return dynamicClient.Resource(
		schema.GroupVersionResource{
			Group:    groupVersion.Group,
			Version:  groupVersion.Version,
			Resource: "enis",
		},
	), nil
}

func NewCepClient(ctx context.Context, configBytes []byte) (dynamic.NamespaceableResourceInterface, error) {
	config, err := clientcmd.RESTConfigFromKubeConfig(configBytes)
	if err != nil {
		logger.Errorf(ctx, "RESTConfigFromKubeConfig failed: %s", err)
		return nil, err
	}

	// 获取 Dynamic 客户端
	dynamicClient := dynamic.NewForConfigOrDie(config)

	// 指定自定义资源的 Group 和 Version
	groupVersion := schema.GroupVersion{
		Group:   "cce.baidubce.com",
		Version: "v2",
	}

	// 获取自定义资源的 Dynamic 客户端
	return dynamicClient.Resource(
		schema.GroupVersionResource{
			Group:    groupVersion.Group,
			Version:  groupVersion.Version,
			Resource: "cceendpoints",
		},
	), nil
}

func (c *rdmaRelatedCheck) GetNodeRDMANRSs(ctx context.Context, client dynamic.NamespaceableResourceInterface, nodeName string) (*unstructured.UnstructuredList, error) {
	// List all NRS resources
	nrsList, err := client.List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "Failed to list NRS for node %s: %v", nodeName, err)
		return nil, err
	}

	// Filter items where name contains both nodeName and rdma
	var filtered []unstructured.Unstructured
	for _, item := range nrsList.Items {
		if strings.Contains(item.GetName(), nodeName) && strings.Contains(item.GetName(), "rdma") {
			filtered = append(filtered, item)
		}
	}
	nrsList.Items = filtered

	return nrsList, nil
}

// 检查项目1，检查节点上的rdma的元数据是否符合要求
func (c *rdmaRelatedCheck) ValidateRDMAResources(ctx context.Context, eniClient, nrsClient dynamic.NamespaceableResourceInterface, nodeName string) error {
	// Get all NRSs for this node
	nodeNRSs, err := c.GetNodeRDMANRSs(ctx, nrsClient, nodeName)
	if err != nil {
		return fmt.Errorf("failed to get NRSs for node %s: %v", nodeName, err)
	}

	rdmaNRSs := nodeNRSs.Items
	// Check RDMA NRS count matches configuration
	if len(rdmaNRSs) != c.Config.RdmaNetCount {
		return fmt.Errorf("RDMA NRS count mismatch: found %d, expected %d", len(rdmaNRSs), c.Config.RdmaNetCount)
	}

	// Check each RDMA NRS
	for _, nrs := range rdmaNRSs {
		// 只在instance-type为rdma_roce或eri时校验maxIpsPerENI，期望值分别为13和40
		instanceType, found, err := unstructured.NestedString(nrs.Object, "spec", "eni", "instance-type")
		if err != nil {
			return fmt.Errorf("failed to get instance-type from NRS %s: %v", nrs.GetName(), err)
		}
		if found && (instanceType == "rdma_roce" || instanceType == "elastic_rdma") {
			maxIpsPerENI, found, err := unstructured.NestedInt64(nrs.Object, "spec", "eni", "maxIpsPerENI")
			if err != nil {
				return fmt.Errorf("failed to get maxIpsPerENI from NRS %s: %v", nrs.GetName(), err)
			}
			if !found {
				return fmt.Errorf("NRS %s 未设置maxIpsPerENI参数", nrs.GetName())
			}
			var expectMax int
			if instanceType == "rdma_roce" {
				expectMax = 13
			} else if instanceType == "elastic_rdma" {
				expectMax = 40
			}
			if int(maxIpsPerENI) != expectMax {
				return fmt.Errorf("NRS %s 的maxIpsPerENI参数为%d, 期望%d", nrs.GetName(), maxIpsPerENI, expectMax)
			}
		}

		// Get IPAM pool size
		pool, found, err := unstructured.NestedMap(nrs.Object, "spec", "ipam", "pool")
		if err != nil {
			return fmt.Errorf("failed to get pool from NRS %s: %v", nrs.GetName(), err)
		}
		if !found {
			return fmt.Errorf("no pool found in NRS %s spec", nrs.GetName())
		}
		poolSize := len(pool)
		if poolSize == 0 {
			return fmt.Errorf("pool size is 0 in NRS %s", nrs.GetName())
		}

		if poolSize != c.Config.IpPerRdmaEni {
			return fmt.Errorf("pool size %d does not match expected size %d for NRS %s",
				poolSize, c.Config.IpPerRdmaEni, nrs.GetName())
		}
	}

	return nil
}

// 检查项目2，检查创建的工作负载对应的cep是否符合要求
func (c *rdmaRelatedCheck) ValidateRDMAPodEndpoints(ctx context.Context, nrsClient dynamic.NamespaceableResourceInterface, endpointClient dynamic.NamespaceableResourceInterface, nodeName string, deploymentName string) error {
	// Get all pods on the node
	targetPods, err := c.getPodsByDeployment(ctx, deploymentName, corev1.NamespaceDefault)
	if err != nil {
		return fmt.Errorf("failed to list pods on node %s: %v", nodeName, err)
	}
	rdmaNRSs, err := c.GetNodeRDMANRSs(ctx, nrsClient, nodeName)
	if err != nil {
		return fmt.Errorf("failed to get NRSs for node %s: %v", nodeName, err)
	}

	nrsList := rdmaNRSs.Items
	for _, pod := range targetPods {
		for _, nrs := range nrsList {
			nrsName := nrs.GetName()
			var suffix string

			// Determine the suffix based on NRS name
			if strings.Contains(nrsName, "rdmaroce") {
				suffix = "rdmaroce"
			} else if strings.Contains(nrsName, "elasticrdma") {
				suffix = "elasticrdma"
			} else {
				continue
			}

			// Extract middle part from NRS name and remove hyphens
			prefix := nodeName
			middlePart := strings.ReplaceAll(strings.TrimSuffix(strings.TrimPrefix(nrsName, prefix), suffix), "-", "")

			// Check each pod's corresponding endpoint
			expectedEndpoint := pod.Name + "-" + "rdma" + "-" + middlePart
			_, err := endpointClient.Namespace(corev1.NamespaceDefault).Get(ctx, expectedEndpoint, metav1.GetOptions{})
			if err != nil {
				return fmt.Errorf("endpoint %s not found for pod %s: %v", expectedEndpoint, pod.Name, err)
			}
		}
	}

	return nil
}

// 检查项目3，检查创建的工作负载的容器内的网口设备是否符合要求
func (c *rdmaRelatedCheck) ValidateRDMAPodNetInterfaces(ctx context.Context, nrsClient dynamic.NamespaceableResourceInterface, base *cases.BaseClient, deploymentName string) error {
	// Get all pods matching the prefix
	targetPods, err := c.getPodsByDeployment(ctx, deploymentName, corev1.NamespaceDefault)
	if err != nil {
		return fmt.Errorf("failed to list pods for deployment %s: %v", deploymentName, err)
	}

	for _, pod := range targetPods {
		nodeName := pod.Spec.NodeName

		rdmaNRSs, err := c.GetNodeRDMANRSs(ctx, nrsClient, nodeName)
		if err != nil {
			return fmt.Errorf("failed to get NRSs for node %s: %v", nodeName, err)
		}
		// Count RDMA NRSs
		rdmaNRSCount := len(rdmaNRSs.Items)
		// Execute command in pod to list all网卡
		cmdList := []string{"sh", "-c", "ls /sys/class/net"}
		netOut, _, err := execInPod(ctx, base.KubeClient.ClientSet, base.KubeClient.RestConfig, pod.Namespace, pod.Name, pod.Spec.Containers[0].Name, cmdList)
		if err != nil {
			return fmt.Errorf("pod %s 获取网卡列表失败: %v", pod.Name, err)
		}
		netIfaces := strings.Fields(strings.TrimSpace(netOut))
		// 只校验rdmaN类型网卡（排除lo、eth0）
		var rdmaIfaces []string
		for _, iface := range netIfaces {
			if iface == "lo" || iface == "eth0" {
				continue
			}
			rdmaIfaces = append(rdmaIfaces, iface)
		}
		// 数量检查
		if len(rdmaIfaces) != rdmaNRSCount {
			return fmt.Errorf("pod %s rdma网卡数量为%d, 期望%d", pod.Name, len(rdmaIfaces), rdmaNRSCount)
		}
		// 获取路由表和邻居表
		cmdRoute := []string{"sh", "-c", "ip route"}
		routeOut, _, err := execInPod(ctx, base.KubeClient.ClientSet, base.KubeClient.RestConfig, pod.Namespace, pod.Name, pod.Spec.Containers[0].Name, cmdRoute)
		if err != nil {
			return fmt.Errorf("pod %s 获取路由表失败: %v", pod.Name, err)
		}
		cmdNeigh := []string{"sh", "-c", "ip neigh"}
		neighOut, _, err := execInPod(ctx, base.KubeClient.ClientSet, base.KubeClient.RestConfig, pod.Namespace, pod.Name, pod.Spec.Containers[0].Name, cmdNeigh)
		if err != nil {
			return fmt.Errorf("pod %s 获取邻居表失败: %v", pod.Name, err)
		}
		// 对每个rdma网卡都做路由和邻居表校验
		for _, iface := range rdmaIfaces {
			// 路由表校验：有dev iface的路由
			foundRoute := false
			routes := strings.Split(routeOut, "\n")
			for _, r := range routes {
				if strings.Contains(r, "dev "+iface) {
					foundRoute = true
					break
				}
			}
			if !foundRoute {
				return fmt.Errorf("pod %s 网卡%s未在路由表中找到对应路由", pod.Name, iface)
			}
			// 邻居表校验：有dev iface且包含lladdr和PERMANENT
			foundNeigh := false
			neighs := strings.Split(neighOut, "\n")
			for _, n := range neighs {
				if strings.Contains(n, "dev "+iface) && strings.Contains(n, "lladdr") && strings.Contains(n, "PERMANENT") {
					foundNeigh = true
					break
				}
			}
			if !foundNeigh {
				return fmt.Errorf("pod %s 网卡%s未在邻居表中找到PERMANENT类型lladdr项", pod.Name, iface)
			}
		}
	}

	return nil
}

func execInPod(ctx context.Context, clientset *kubernetes.Clientset, config *rest.Config, namespace, podName, containerName string, command []string) (string, string, error) {
	req := clientset.CoreV1().RESTClient().Post().
		Resource("pods").
		Name(podName).
		Namespace(namespace).
		SubResource("exec")

	req.VersionedParams(&corev1.PodExecOptions{
		Container: containerName,
		Command:   command,
		Stdin:     false,
		Stdout:    true,
		Stderr:    true,
		TTY:       false,
	}, scheme.ParameterCodec)

	exec, err := remotecommand.NewSPDYExecutor(config, "POST", req.URL())
	if err != nil {
		return "", "", err
	}

	var stdout, stderr bytes.Buffer
	err = exec.Stream(remotecommand.StreamOptions{
		Stdout: &stdout,
		Stderr: &stderr,
		Tty:    false,
	})

	return stdout.String(), stderr.String(), err
}

func (c *rdmaRelatedCheck) applyRDMADeployment(ctx context.Context, replicas int32, hostName string) error {
	logger.Infof(ctx, "start to deploy RDMA test deployment, replicas: %d", replicas)
	var terminationGracePeriodSeconds int64
	deployment := v1.Deployment{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Deployment",
			APIVersion: "apps/v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      rdmaWorkloadName,
			Namespace: corev1.NamespaceDefault,
			Labels: map[string]string{
				"app": "nginx",
			},
		},
		Spec: v1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": "nginx",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app":                              "nginx",
						"kubernetes.io/mutate-pod-webhook": "unavailable",
					},
				},
				Spec: corev1.PodSpec{
					TerminationGracePeriodSeconds: &terminationGracePeriodSeconds,
					NodeName:                      hostName,
					Containers: []corev1.Container{
						{
							Name:            "nginx",
							Image:           "registry.baidubce.com/qa-test/nginx:1.17",
							ImagePullPolicy: corev1.PullAlways,
							Ports: []corev1.ContainerPort{
								{
									ContainerPort: 80,
								},
							},
							Resources: corev1.ResourceRequirements{
								Limits: corev1.ResourceList{
									"rdma/hca": k8sResource.MustParse("1"),
								},
							},
						},
					},
				},
			},
		},
	}

	out, err := yaml.Marshal(&deployment)
	if err != nil {
		return fmt.Errorf("marshal deployment failed, err: %v", err)
	}

	deploymentYaml := string(out)
	_, err = c.base.AppClient.PostAppResource(ctx, c.base.ClusterID, rdmaWorkloadName, deploymentYaml, corev1.NamespaceDefault, nil)
	if err != nil {
		return fmt.Errorf("create RDMA deployment failed, err: %v", err)
	}
	return nil
}

func (c *rdmaRelatedCheck) scaleDeployment(ctx context.Context, replicas int32) error {
	scale, err := c.base.KubeClient.GetDeploymentScaleAppsV1(ctx, corev1.NamespaceDefault, rdmaWorkloadName, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("get deployment scale failed, err: %v", err)
	}
	scale.Spec.Replicas = replicas
	for i := 0; i < 15; i++ {
		_, err = c.base.KubeClient.ScaleDeploymentAppsV1(ctx, corev1.NamespaceDefault, rdmaWorkloadName, scale)
		if err != nil {
			if strings.Contains(err.Error(), "Operation cannot be fulfilled on deployments.apps") {
				logger.Warnf(ctx, "Deployment更新失败，重试中...")
				time.Sleep(2 * time.Second)
				continue
			}
			return fmt.Errorf("更新Deployment副本数失败: %v", err)
		}
		break
	}
	return nil
}

func (c *rdmaRelatedCheck) getPodsByDeployment(ctx context.Context, deploymentName, namespace string) ([]corev1.Pod, error) {
	// Get the deployment
	deployment, err := c.base.KubeClient.ClientSet.AppsV1().Deployments(namespace).Get(ctx, deploymentName, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get deployment %s: %v", deploymentName, err)
	}

	// Get the labels used by the deployment selector
	labelSelector := metav1.LabelSelector{
		MatchLabels: deployment.Spec.Selector.MatchLabels,
	}
	selector, err := metav1.LabelSelectorAsSelector(&labelSelector)
	if err != nil {
		return nil, fmt.Errorf("failed to create selector: %v", err)
	}

	// List pods matching the selector
	pods, err := c.base.KubeClient.ClientSet.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{
		LabelSelector: selector.String(),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to list pods: %v", err)
	}

	return pods.Items, nil
}

func (c *rdmaRelatedCheck) deleteRDMADeployment(ctx context.Context) error {
	logger.Infof(ctx, "start to delete RDMA test deployment")
	err := c.base.KubeClient.ClientSet.AppsV1().Deployments(corev1.NamespaceDefault).Delete(ctx, rdmaWorkloadName, metav1.DeleteOptions{})
	if err != nil {
		return fmt.Errorf("delete RDMA deployment failed, err: %v", err)
	}
	return nil
}

// 校验rdma容器间ib命令互通性
// 只做简单的两两互通性校验，实际可根据需求扩展
func (c *rdmaRelatedCheck) ValidateRDMAPodIBConnectivity(ctx context.Context, deploymentName string) error {
	pods, err := c.getPodsByDeployment(ctx, deploymentName, corev1.NamespaceDefault)
	if err != nil {
		return fmt.Errorf("获取rdma pod列表失败: %v", err)
	}
	if len(pods) < 2 {
		return fmt.Errorf("rdma pod数量不足2，无法做互通性校验")
	}
	// 取前两个pod
	podA := pods[0]
	podB := pods[1]
	containerA := podA.Spec.Containers[0].Name
	ipB := podB.Status.PodIP
	if ipB == "" {
		return fmt.Errorf("podB没有分配到IP")
	}
	// 在podA中执行ib_write_bw到podB
	cmd := []string{"sh", "-c", fmt.Sprintf("timeout 5 ib_write_bw %s -d mlx5_0 -F", ipB)}
	stdout, stderr, err := execInPod(ctx, c.base.KubeClient.ClientSet, c.base.KubeClient.RestConfig, podA.Namespace, podA.Name, containerA, cmd)
	if err != nil {
		return fmt.Errorf("podA(%s) -> podB(%s) ib_write_bw 失败: %v, stdout: %s, stderr: %s", podA.Name, podB.Name, err, stdout, stderr)
	}
	if !strings.Contains(stdout, "BW") {
		return fmt.Errorf("podA(%s) -> podB(%s) ib_write_bw 未检测到带宽输出, stdout: %s", podA.Name, podB.Name, stdout)
	}
	return nil
}

// 节点名>48和节点删除后NRS资源回收校验
func (c *rdmaRelatedCheck) ValidateNodeNameLengthAndDeleteResource(ctx context.Context) error {
	// 1. 构造一个长度>48的节点名
	longNodeName := "testnode-" + strings.Repeat("a", 50)
	logger.Infof(ctx, "模拟节点名>48: %s", longNodeName)

	// 2. 获取所有subnet，选择可用IP数最多的subnet
	subnetList, err := c.base.KubeClient.ListSubnet(ctx, &kube.ListOptions{})
	if err != nil || len(subnetList.Items) == 0 {
		return fmt.Errorf("获取subnet列表失败: %v", err)
	}

	// 3. 通过平台API创建节点，补全所有字段，支持多可用区重试
	subnetsTried := make(map[string]bool)
	var instanceID string
	var lastCreateErr error
	for retry := 0; retry < len(subnetList.Items); retry++ {
		// 选择当前未尝试过且可用IP最多的subnet
		var bestSubnet *ccetypes.Subnet
		maxAvailableIP := -1
		for i, s := range subnetList.Items {
			if subnetsTried[s.Spec.ID] {
				continue
			}
			if s.Status.AvailableIPNum > maxAvailableIP {
				maxAvailableIP = s.Status.AvailableIPNum
				bestSubnet = &subnetList.Items[i]
			}
		}
		if bestSubnet == nil {
			break // 没有更多可用subnet
		}
		subnetsTried[bestSubnet.Spec.ID] = true
		logger.Infof(ctx, "尝试在可用区 %s 的subnet %s 创建节点，剩余IP: %d", bestSubnet.Spec.AvailabilityZone, bestSubnet.Spec.ID, bestSubnet.Status.AvailableIPNum)

		instanceSet := &ccev2.InstanceSet{
			Count: 1,
			InstanceSpec: ccetypes.InstanceSpec{
				InstanceName: longNodeName,
				ClusterRole:  ccetypes.ClusterRoleNode,
				Existed:      false,
				MachineType:  ccetypes.MachineTypeBCC,
				InstanceType: "N3",
				VPCConfig: ccetypes.VPCConfig{
					VPCID:         bestSubnet.Spec.VPCID,
					VPCSubnetID:   bestSubnet.Spec.ID,
					AvailableZone: internalvpc.AvailableZone(bestSubnet.Spec.AvailabilityZone),
					SecurityGroup: ccetypes.SecurityGroup{
						EnableCCERequiredSecurityGroup: true,
						EnableCCEOptionalSecurityGroup: true,
						CustomSecurityGroupIDs:         []string{},
					},
				},
				InstanceResource: ccetypes.InstanceResource{
					CPU:           4,
					MEM:           8,
					RootDiskType:  bcc.StorageTypeHP1,
					RootDiskSize:  100,
					LocalDiskSize: 0,
					CDSList:       nil,
				},
				ImageID: "m-gTpZ1k6n",
				InstanceOS: ccetypes.InstanceOS{
					ImageType: bccimage.ImageTypeSystem,
				},
				NeedEIP:              false,
				Bid:                  false,
				AdminPassword:        "test123!T",
				InstanceChargingType: bcc.PaymentTimingPostpaid,
				RuntimeType:          "docker",
			},
		}
		instanceSets := []*ccev2.InstanceSet{instanceSet}
		ids, err := c.base.CreateInstances(ctx, c.base.ClusterID, instanceSets, false)
		if err == nil && len(ids) > 0 {
			instanceID = ids[0]
			logger.Infof(ctx, "成功创建节点: %s, 实例ID: %s", longNodeName, instanceID)
			lastCreateErr = nil
			break
		}
		lastCreateErr = err
		logger.Warnf(ctx, "在可用区 %s 的subnet %s 创建节点失败: %v", bestSubnet.Spec.AvailabilityZone, bestSubnet.Spec.ID, err)
	}
	if instanceID == "" {
		return fmt.Errorf("所有可用区subnet均尝试后仍未能成功创建节点，最后错误: %v", lastCreateErr)
	}

	// 4. 获取NRS，检查是否正常创建
	nrsList, err := c.GetNodeRDMANRSs(ctx, c.nrsClient, longNodeName)
	if err != nil || len(nrsList.Items) == 0 {
		return fmt.Errorf("获取节点 %s 的NRS资源失败: %v", longNodeName, err)
	}
	logger.Infof(ctx, "节点创建后NRS正常创建, NRS数量: %d", len(nrsList.Items))

	// 5. 从集群移除节点（平台API移除节点）
	deleteReq := &ccev2.DeleteInstancesRequest{
		InstanceIDs: []string{instanceID},
		DeleteOption: &ccetypes.DeleteOption{
			MoveOut: true, // 只移出集群，不销毁资源
		},
	}
	_, err = c.base.CCEClient.DeleteInstances(ctx, c.base.ClusterID, deleteReq, nil)
	if err != nil {
		return fmt.Errorf("移除节点失败: %v", err)
	}
	logger.Infof(ctx, "已移除节点: %s", longNodeName)

	// 6. 等待并检查NRS是否被清理
	time.Sleep(10 * time.Second)
	nrsList, err = c.GetNodeRDMANRSs(ctx, c.nrsClient, longNodeName)
	if err == nil && len(nrsList.Items) > 0 {
		return fmt.Errorf("节点移出集群后NRS资源未被清理，仍有 %d 个NRS", len(nrsList.Items))
	}
	logger.Infof(ctx, "节点移出集群后NRS资源已被清理")

	return nil
}

// ValidateNRSPodReference 验证 NRS 状态中的 pod 引用
func (c *rdmaRelatedCheck) ValidateNRSPodReference(ctx context.Context, nodeName, deploymentName string) error {
	// 获取所有 pod
	pods, err := c.getPodsByDeployment(ctx, deploymentName, corev1.NamespaceDefault)
	if err != nil {
		return fmt.Errorf("获取 pod 列表失败: %v", err)
	}

	// 获取节点上的 RDMA NRS 列表
	nrsList, err := c.GetNodeRDMANRSs(ctx, c.nrsClient, nodeName)
	if err != nil {
		return fmt.Errorf("获取节点 RDMA NRS 失败: %v", err)
	}

	// 遍历每个 pod，检查其在对应 NRS 中的引用
	for _, pod := range pods {
		found := false
		// 遍历所有 RDMA NRS
		for _, nrs := range nrsList.Items {
			// 获取 NRS 状态中的 ipam.used
			ipamStatus, exists, err := unstructured.NestedMap(nrs.Object, "status", "ipam", "used")
			if err != nil {
				return fmt.Errorf("获取 NRS %s 的 ipam.used 失败: %v", nrs.GetName(), err)
			}
			if !exists {
				continue
			}

			// 检查是否存在且仅存在一个对应的 pod 引用
			podReferenceCount := 0
			for _, ipInfo := range ipamStatus {
				ipInfoMap, ok := ipInfo.(map[string]interface{})
				if !ok {
					continue
				}
				owner, ok := ipInfoMap["owner"].(string)
				if !ok {
					continue
				}
				if owner == fmt.Sprintf("%s/%s", pod.Namespace, pod.Name) {
					podReferenceCount++
					found = true
				}
			}

			if podReferenceCount > 1 {
				return fmt.Errorf("pod %s 在 NRS %s 中有多个引用: %d", pod.Name, nrs.GetName(), podReferenceCount)
			}
		}

		if !found {
			return fmt.Errorf("pod %s 在任何 RDMA NRS 中都没有找到引用", pod.Name)
		}

		logger.Infof(ctx, "Pod %s 在 NRS 中的引用验证通过", pod.Name)
	}

	logger.Infof(ctx, "所有 pod 在 NRS 中的引用验证通过")
	return nil
}

// ValidateNRSPodCleanup 验证已删除的 pod 在 NRS 状态中的清理情况
func (c *rdmaRelatedCheck) ValidateNRSPodCleanup(ctx context.Context, nodeName, deploymentName string) error {
	// 获取当前存活的 pod 列表
	currentPods, err := c.getPodsByDeployment(ctx, deploymentName, corev1.NamespaceDefault)
	if err != nil {
		return fmt.Errorf("获取当前 pod 列表失败: %v", err)
	}

	// 创建当前 pod 引用的集合，用于快速查找
	currentPodRefs := make(map[string]bool)
	for _, pod := range currentPods {
		currentPodRefs[fmt.Sprintf("%s/%s", pod.Namespace, pod.Name)] = true
	}

	// 获取节点上的 RDMA NRS 列表
	nrsList, err := c.GetNodeRDMANRSs(ctx, c.nrsClient, nodeName)
	if err != nil {
		return fmt.Errorf("获取节点 RDMA NRS 失败: %v", err)
	}

	// 检查每个 NRS 的状态
	for _, nrs := range nrsList.Items {
		// 获取 NRS 状态中的 ipam.used
		ipamStatus, exists, err := unstructured.NestedMap(nrs.Object, "status", "ipam", "used")
		if err != nil {
			return fmt.Errorf("获取 NRS %s 的 ipam.used 失败: %v", nrs.GetName(), err)
		}
		if !exists {
			continue
		}

		// 检查是否存在已删除 pod 的引用
		for ip, ipInfo := range ipamStatus {
			ipInfoMap, ok := ipInfo.(map[string]interface{})
			if !ok {
				continue
			}
			owner, ok := ipInfoMap["owner"].(string)
			if !ok {
				continue
			}

			// 检查这个引用是否属于当前存活的 pod
			if !currentPodRefs[owner] {
				return fmt.Errorf("NRS %s 中存在已删除 pod 的引用: IP %s 被 %s 占用", nrs.GetName(), ip, owner)
			}
		}
	}

	logger.Infof(ctx, "已删除 pod 的 NRS 状态清理验证通过")
	return nil
}
