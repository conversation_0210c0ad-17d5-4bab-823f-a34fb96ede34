/*
Copyright 2024 Baidu Inc.

本文件包含了针对CCE集群单节点100容器扩容耗时的自动化测试用例。

用例主要验证以下内容：
1. 检查节点资源是否充足支持100个容器（CPU、内存、Pod数量限制）
2. 基于NRS信息验证网络资源是否充足（IP数量、ENI容量）
3. 创建Deployment并快速扩容到100个副本
4. 并发监控所有Pod的创建和就绪过程
5. 记录关键时间节点和分位数统计

测试流程：
1. 选择资源充足的节点作为测试目标
2. 验证节点CPU、内存、Pod数量限制
3. 检查网络资源（基于NRS和ENI信息）
4. 创建Deployment（初始副本数为0）
5. 快速扩容到100个副本
6. 并发监控所有Pod状态变化
7. 记录里程碑时间（1、10、50、100个Pod就绪）
8. 计算分位数统计（P50、P90、P95、P99）
9. 监控节点资源使用情况变化
10. 输出详细的性能分析报告

该测试用于评估单节点大规模容器扩容性能，为容量规划和性能优化提供数据支撑。
*/

package network

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"sync"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
)

const (
	// SingleNode100ContainersScalingCaseName - case 名字
	SingleNode100ContainersScalingCaseName cases.CaseName = "SingleNode100ContainersScaling"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), SingleNode100ContainersScalingCaseName, NewSingleNode100ContainersScaling)
}

// Single100ContainersConfig 100容器扩容测试配置
type Single100ContainersConfig struct {
	Image             string            `json:"image"`             // 容器镜像，默认轻量级nginx
	TargetReplicas    int               `json:"targetReplicas"`    // 目标副本数，默认100
	CPURequest        string            `json:"cpuRequest"`        // CPU请求，默认50m
	MemoryRequest     string            `json:"memoryRequest"`     // 内存请求，默认64Mi
	CPULimit          string            `json:"cpuLimit"`          // CPU限制，默认200m
	MemoryLimit       string            `json:"memoryLimit"`       // 内存限制，默认256Mi
	TimeoutMinutes    int               `json:"timeoutMinutes"`    // 超时时间，默认20分钟
	MonitorInterval   int               `json:"monitorInterval"`   // 监控间隔（秒），默认2秒
	NodeSelector      map[string]string `json:"nodeSelector"`      // 节点选择器
	CheckNodeResource bool              `json:"checkNodeResource"` // 是否检查节点资源，默认true
	ExtraLabels       map[string]string `json:"extraLabels"`       // 额外标签
}

// PodReadinessRecord Pod就绪记录
type PodReadinessRecord struct {
	PodName       string        `json:"podName"`
	CreatedTime   time.Time     `json:"createdTime"`
	RunningTime   time.Time     `json:"runningTime"`
	ReadyTime     time.Time     `json:"readyTime"`
	CreationDelay time.Duration `json:"creationDelay"` // 从扩容开始到Pod创建的时间
	ReadyDelay    time.Duration `json:"readyDelay"`    // 从扩容开始到Pod就绪的时间
	IsReady       bool          `json:"isReady"`
}

// MilestoneTimestamps 里程碑时间戳
type MilestoneTimestamps struct {
	ScalingStartTime time.Time `json:"scalingStartTime"`
	FirstPodReady    time.Time `json:"firstPodReady"`
	Pod10Ready       time.Time `json:"pod10Ready"`
	Pod50Ready       time.Time `json:"pod50Ready"`
	Pod100Ready      time.Time `json:"pod100Ready"`
	AllPodsReady     time.Time `json:"allPodsReady"`
}

// PercentileStats 分位数统计
type PercentileStat struct {
	P50         time.Duration `json:"p50"`
	P90         time.Duration `json:"p90"`
	P95         time.Duration `json:"p95"`
	P99         time.Duration `json:"p99"`
	Min         time.Duration `json:"min"`
	Max         time.Duration `json:"max"`
	Mean        time.Duration `json:"mean"`
	TotalPods   int           `json:"totalPods"`
	ReadyPods   int           `json:"readyPods"`
	SuccessRate float64       `json:"successRate"`
}

// NodeResourceInfo 节点资源信息
type NodeResourceInfo struct {
	NodeName          string                    `json:"nodeName"`
	CPUCapacity       resource.Quantity         `json:"cpuCapacity"`
	MemoryCapacity    resource.Quantity         `json:"memoryCapacity"`
	PodCapacity       resource.Quantity         `json:"podCapacity"`
	CPUAllocatable    resource.Quantity         `json:"cpuAllocatable"`
	MemoryAllocatable resource.Quantity         `json:"memoryAllocatable"`
	PodAllocatable    resource.Quantity         `json:"podAllocatable"`
	NRS               *types.NetworkResourceSet `json:"nrs"`
	AvailableIPs      int                       `json:"availableIPs"`
	CurrentPods       int                       `json:"currentPods"`
	CanSchedulePods   int                       `json:"canSchedulePods"`
}

type singleNode100ContainersScaling struct {
	base           *cases.BaseClient
	config         Single100ContainersConfig
	deploymentName string
	selectedNode   string
	timestamps     MilestoneTimestamps
	resources      []cases.Resource

	// 监控相关
	podRecords      map[string]*PodReadinessRecord
	podRecordsMutex sync.RWMutex
	readyPodCount   int
	nodeInfo        NodeResourceInfo
}

// NewSingleNode100ContainersScaling - 测试案例
func NewSingleNode100ContainersScaling(ctx context.Context) cases.Interface {
	return &singleNode100ContainersScaling{
		podRecords: make(map[string]*PodReadinessRecord),
	}
}

func (c *singleNode100ContainersScaling) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base is nil")
	}

	c.base = base

	// 解析配置，如果没有配置则使用默认值
	if len(config) > 0 {
		if err := json.Unmarshal(config, &c.config); err != nil {
			return fmt.Errorf("解析配置失败: %v", err)
		}
	}

	// 设置默认值
	if c.config.Image == "" {
		c.config.Image = "registry.baidubce.com/cce/nginx-alpine-go:latest"
	}
	if c.config.TargetReplicas <= 0 {
		c.config.TargetReplicas = 100
	}
	if c.config.CPURequest == "" {
		c.config.CPURequest = "50m"
	}
	if c.config.MemoryRequest == "" {
		c.config.MemoryRequest = "64Mi"
	}
	if c.config.CPULimit == "" {
		c.config.CPULimit = "200m"
	}
	if c.config.MemoryLimit == "" {
		c.config.MemoryLimit = "256Mi"
	}
	if c.config.TimeoutMinutes <= 0 {
		c.config.TimeoutMinutes = 20
	}
	if c.config.MonitorInterval <= 0 {
		c.config.MonitorInterval = 2
	}
	c.config.CheckNodeResource = true // 总是检查节点资源

	logger.Infof(ctx, "100容器扩容测试配置: 镜像=%s, 目标副本数=%d, 超时时间=%d分钟",
		c.config.Image, c.config.TargetReplicas, c.config.TimeoutMinutes)

	return nil
}

func (c *singleNode100ContainersScaling) Name() cases.CaseName {
	return SingleNode100ContainersScalingCaseName
}

func (c *singleNode100ContainersScaling) Desc() string {
	if c.config.TargetReplicas == 100 {
		return "测试单节点100个容器扩容时间"
	}
	return fmt.Sprintf("测试单节点%d个容器扩容时间（基于节点资源动态调整）", c.config.TargetReplicas)
}

func (c *singleNode100ContainersScaling) Check(ctx context.Context) ([]cases.Resource, error) {
	logger.Infof(ctx, "开始执行单节点%d容器扩容耗时测试", c.config.TargetReplicas)

	// 1. 选择资源充足的节点
	selectedNode, err := c.selectResourceRichNode(ctx)
	if err != nil {
		return nil, fmt.Errorf("选择资源充足节点失败: %v", err)
	}
	c.selectedNode = selectedNode
	logger.Infof(ctx, "选择节点 %s 作为测试目标", selectedNode)

	// 2. 验证节点资源是否充足
	if c.config.CheckNodeResource {
		err = c.validateNodeResources(ctx)
		if err != nil {
			return nil, fmt.Errorf("节点资源验证失败: %v", err)
		}
	}

	// 3. 创建测试Deployment（副本数为0）
	deploymentName, err := c.createTestDeployment(ctx)
	if err != nil {
		return nil, fmt.Errorf("创建测试Deployment失败: %v", err)
	}
	c.deploymentName = deploymentName

	// 4. 记录扩容开始时间
	c.timestamps.ScalingStartTime = time.Now()
	logger.Infof(ctx, "🚀 扩容开始时间: %v (目标: %d个容器)",
		c.timestamps.ScalingStartTime.Format("2006-01-02 15:04:05.000"), c.config.TargetReplicas)

	// 5. 执行快速扩容操作
	err = c.scaleDeployment(ctx, c.config.TargetReplicas)
	if err != nil {
		return nil, fmt.Errorf("扩容Deployment失败: %v", err)
	}

	// 6. 并发监控所有Pod状态
	err = c.monitorPodsLifecycle(ctx)
	if err != nil {
		return nil, fmt.Errorf("监控Pod生命周期失败: %v", err)
	}

	// 7. 计算并输出统计报告
	c.generateStatisticsReport(ctx)

	return c.resources, nil
}

// selectResourceRichNode 选择资源充足的节点
func (c *singleNode100ContainersScaling) selectResourceRichNode(ctx context.Context) (string, error) {
	nodes, err := c.base.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return "", fmt.Errorf("获取节点列表失败: %v", err)
	}

	if len(nodes.Items) == 0 {
		return "", fmt.Errorf("集群中没有可用节点")
	}

	// 选择资源最充足的Ready节点
	var bestNode *corev1.Node
	var maxScore float64 = 0

	for _, node := range nodes.Items {
		if !c.isNodeReady(&node) {
			continue
		}

		// 计算节点资源评分
		score := c.calculateNodeResourceScore(&node)
		logger.Infof(ctx, "节点 %s 资源评分: %.2f", node.Name, score)

		if score > maxScore {
			maxScore = score
			bestNode = &node
		}
	}

	if bestNode == nil {
		return "", fmt.Errorf("没有找到可用的Ready节点")
	}

	logger.Infof(ctx, "选择资源最充足的节点: %s (评分: %.2f)", bestNode.Name, maxScore)
	return bestNode.Name, nil
}

// calculateNodeResourceScore 计算节点资源评分
func (c *singleNode100ContainersScaling) calculateNodeResourceScore(node *corev1.Node) float64 {
	// 获取节点容量信息
	cpuCapacity := node.Status.Capacity.Cpu()
	memoryCapacity := node.Status.Capacity.Memory()
	podCapacity := node.Status.Capacity.Pods()

	cpuAllocatable := node.Status.Allocatable.Cpu()
	memoryAllocatable := node.Status.Allocatable.Memory()
	podAllocatable := node.Status.Allocatable.Pods()

	// 计算可用资源百分比
	cpuScore := float64(cpuAllocatable.MilliValue()) / float64(cpuCapacity.MilliValue()) * 100
	memoryScore := float64(memoryAllocatable.Value()) / float64(memoryCapacity.Value()) * 100
	podScore := float64(podAllocatable.Value()) / float64(podCapacity.Value()) * 100

	// 综合评分（权重：CPU 30%，内存 30%，Pod 40%）
	return cpuScore*0.3 + memoryScore*0.3 + podScore*0.4
}

// validateNodeResources 验证节点资源
func (c *singleNode100ContainersScaling) validateNodeResources(ctx context.Context) error {
	logger.Infof(ctx, "===============================")
	logger.Infof(ctx, "🔍 开始验证节点资源...")
	logger.Infof(ctx, "目标节点: %s", c.selectedNode)

	// 获取节点信息
	node, err := c.base.K8SClient.CoreV1().Nodes().Get(ctx, c.selectedNode, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取节点信息失败: %v", err)
	}

	// 获取节点基本资源信息
	c.nodeInfo.NodeName = c.selectedNode
	c.nodeInfo.CPUCapacity = node.Status.Capacity[corev1.ResourceCPU]
	c.nodeInfo.MemoryCapacity = node.Status.Capacity[corev1.ResourceMemory]
	c.nodeInfo.PodCapacity = node.Status.Capacity[corev1.ResourcePods]
	c.nodeInfo.CPUAllocatable = node.Status.Allocatable[corev1.ResourceCPU]
	c.nodeInfo.MemoryAllocatable = node.Status.Allocatable[corev1.ResourceMemory]
	c.nodeInfo.PodAllocatable = node.Status.Allocatable[corev1.ResourcePods]

	// 获取当前Pod数量
	pods, err := c.base.K8SClient.CoreV1().Pods("").List(ctx, metav1.ListOptions{
		FieldSelector: "spec.nodeName=" + c.selectedNode,
	})
	if err != nil {
		return fmt.Errorf("获取Pod列表失败: %v", err)
	}

	c.nodeInfo.CurrentPods = len(pods.Items)
	c.nodeInfo.CanSchedulePods = int(c.nodeInfo.PodAllocatable.Value()) - c.nodeInfo.CurrentPods

	logger.Infof(ctx, "=== 节点基本资源 ===")
	logger.Infof(ctx, "CPU容量: %s, 可分配: %s",
		c.nodeInfo.CPUCapacity.String(), c.nodeInfo.CPUAllocatable.String())
	logger.Infof(ctx, "内存容量: %s, 可分配: %s",
		c.nodeInfo.MemoryCapacity.String(), c.nodeInfo.MemoryAllocatable.String())
	logger.Infof(ctx, "Pod容量: %d, 可分配: %d",
		int(c.nodeInfo.PodCapacity.Value()), int(c.nodeInfo.PodAllocatable.Value()))
	logger.Infof(ctx, "当前Pod数量: %d", c.nodeInfo.CurrentPods)
	logger.Infof(ctx, "可调度Pod数量: %d", c.nodeInfo.CanSchedulePods)

	// 获取网络资源信息
	err = c.validateNetworkResources(ctx)
	if err != nil {
		logger.Warnf(ctx, "网络资源验证失败: %v", err)
	}

	// 计算各种资源约束下的最大pod数量
	maxPodsByPodLimit := c.nodeInfo.CanSchedulePods
	maxPodsByNetwork := c.nodeInfo.AvailableIPs

	// 计算CPU约束下的最大pod数量
	requestCPU := resource.MustParse(c.config.CPURequest)
	maxPodsByCPU := int(c.nodeInfo.CPUAllocatable.MilliValue() / requestCPU.MilliValue())

	// 计算内存约束下的最大pod数量
	requestMemory := resource.MustParse(c.config.MemoryRequest)
	maxPodsByMemory := int(c.nodeInfo.MemoryAllocatable.Value() / requestMemory.Value())

	logger.Infof(ctx, "=== 资源约束分析 ===")
	logger.Infof(ctx, "Pod数量限制约束: %d", maxPodsByPodLimit)
	logger.Infof(ctx, "网络IP资源约束: %d", maxPodsByNetwork)
	logger.Infof(ctx, "CPU资源约束: %d (单Pod需求: %s)", maxPodsByCPU, c.config.CPURequest)
	logger.Infof(ctx, "内存资源约束: %d (单Pod需求: %s)", maxPodsByMemory, c.config.MemoryRequest)

	// 找到最小的约束作为实际能部署的最大数量
	actualMaxPods := maxPodsByPodLimit
	limitingFactor := "Pod数量限制"

	if maxPodsByNetwork < actualMaxPods {
		actualMaxPods = maxPodsByNetwork
		limitingFactor = "网络IP资源"
	}

	if maxPodsByCPU < actualMaxPods {
		actualMaxPods = maxPodsByCPU
		limitingFactor = "CPU资源"
	}

	if maxPodsByMemory < actualMaxPods {
		actualMaxPods = maxPodsByMemory
		limitingFactor = "内存资源"
	}

	logger.Infof(ctx, "=== 最终测试方案 ===")
	logger.Infof(ctx, "🎯 原计划测试Pod数量: %d", c.config.TargetReplicas)
	logger.Infof(ctx, "🔧 实际最大可部署Pod数量: %d", actualMaxPods)
	logger.Infof(ctx, "⚠️  限制因素: %s", limitingFactor)

	// 动态调整目标副本数
	if actualMaxPods < c.config.TargetReplicas {
		logger.Infof(ctx, "📊 调整测试目标: %d -> %d (受限于%s)",
			c.config.TargetReplicas, actualMaxPods, limitingFactor)
		c.config.TargetReplicas = actualMaxPods
	}

	// 检查是否至少能部署一些pod
	if actualMaxPods <= 0 {
		return fmt.Errorf("节点资源不足，无法部署任何Pod (限制因素: %s)", limitingFactor)
	}

	logger.Infof(ctx, "✅ 节点资源验证通过，将测试 %d 个Pod的扩容", c.config.TargetReplicas)
	logger.Infof(ctx, "===============================")
	return nil
}

// validateNetworkResources 验证网络资源
func (c *singleNode100ContainersScaling) validateNetworkResources(ctx context.Context) error {
	// 获取节点的NRS信息
	nrs, err := c.base.KubeClient.GetNrs(ctx, c.selectedNode, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取节点NRS失败: %v", err)
	}
	c.nodeInfo.NRS = nrs

	// 获取当前ENI列表
	eniList, err := c.base.KubeClient.ListENI(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取ENI列表失败: %v", err)
	}

	// 过滤出属于该节点的ENI
	var nodeENIs []types.ENI
	for _, eni := range eniList.Items {
		if eni.Spec.NodeName == c.selectedNode {
			nodeENIs = append(nodeENIs, eni)
		}
	}

	// 计算可用IP数量
	maxIPsPerENI := nrs.Spec.Eni.MaxIPsPerENI
	maxENIs := nrs.Spec.Eni.MaxAllocateENI
	usedIPs := len(nrs.Status.Ipam.Used)

	totalAvailableIPs := maxIPsPerENI*maxENIs - maxENIs - usedIPs // 减去ENI自身占用的IP
	c.nodeInfo.AvailableIPs = totalAvailableIPs

	logger.Infof(ctx, "=== 网络资源信息 ===")
	logger.Infof(ctx, "每个ENI最大IP数: %d", maxIPsPerENI)
	logger.Infof(ctx, "最大ENI数量: %d", maxENIs)
	logger.Infof(ctx, "当前ENI数量: %d", len(nodeENIs))
	logger.Infof(ctx, "已使用IP数量: %d", usedIPs)
	logger.Infof(ctx, "可用IP数量: %d", totalAvailableIPs)

	if totalAvailableIPs < c.config.TargetReplicas {
		return fmt.Errorf("网络IP资源不足: 需要 %d，可用 %d",
			c.config.TargetReplicas, totalAvailableIPs)
	}

	logger.Infof(ctx, "✅ 网络资源充足")
	logger.Infof(ctx, "==================")
	return nil
}

// createTestDeployment 创建测试Deployment
func (c *singleNode100ContainersScaling) createTestDeployment(ctx context.Context) (string, error) {
	deploymentName := fmt.Sprintf("test-100-containers-%d", time.Now().Unix())

	// 构建节点选择器
	nodeSelector := map[string]string{
		"kubernetes.io/hostname": c.selectedNode,
	}
	// 添加用户自定义的节点选择器
	for k, v := range c.config.NodeSelector {
		nodeSelector[k] = v
	}

	// 构建标签
	labels := map[string]string{
		"app":      "test-100-containers",
		"test":     "mass-container-scaling",
		"scenario": "single-node",
	}
	for k, v := range c.config.ExtraLabels {
		labels[k] = v
	}

	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      deploymentName,
			Namespace: "default",
			Labels:    labels,
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: func(i int32) *int32 { return &i }(0), // 初始副本数为0
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": "test-100-containers",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app":      "test-100-containers",
						"scenario": "single-node",
					},
				},
				Spec: corev1.PodSpec{
					NodeSelector: nodeSelector,
					Containers: []corev1.Container{
						{
							Name:  "nginx",
							Image: c.config.Image,
							Ports: []corev1.ContainerPort{
								{
									ContainerPort: 80,
									Protocol:      corev1.ProtocolTCP,
								},
							},
							Resources: corev1.ResourceRequirements{
								Requests: corev1.ResourceList{
									corev1.ResourceCPU:    resource.MustParse(c.config.CPURequest),
									corev1.ResourceMemory: resource.MustParse(c.config.MemoryRequest),
								},
								Limits: corev1.ResourceList{
									corev1.ResourceCPU:    resource.MustParse(c.config.CPULimit),
									corev1.ResourceMemory: resource.MustParse(c.config.MemoryLimit),
								},
							},
						},
					},
					RestartPolicy: corev1.RestartPolicyAlways,
				},
			},
		},
	}

	createdDeployment, err := c.base.K8SClient.AppsV1().Deployments("default").Create(ctx, deployment, metav1.CreateOptions{})
	if err != nil {
		return "", fmt.Errorf("创建Deployment失败: %v", err)
	}

	logger.Infof(ctx, "成功创建Deployment %s，初始副本数: 0", deploymentName)

	// 将创建的Deployment添加到资源列表
	c.resources = append(c.resources, cases.Resource{
		CaseName: c.Name(),
		Type:     cases.ResourceTypeCCEInstance,
		ID:       fmt.Sprintf("default/%s", deploymentName),
	})

	return createdDeployment.Name, nil
}

// scaleDeployment 扩容Deployment
func (c *singleNode100ContainersScaling) scaleDeployment(ctx context.Context, replicas int) error {
	deployment, err := c.base.K8SClient.AppsV1().Deployments("default").Get(ctx, c.deploymentName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取Deployment失败: %v", err)
	}

	deployment.Spec.Replicas = func(i int32) *int32 { return &i }(int32(replicas))

	_, err = c.base.K8SClient.AppsV1().Deployments("default").Update(ctx, deployment, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新Deployment副本数失败: %v", err)
	}

	logger.Infof(ctx, "🚀 成功将Deployment %s 扩容到 %d 个副本", c.deploymentName, replicas)
	return nil
}

// monitorPodsLifecycle 监控Pod生命周期
func (c *singleNode100ContainersScaling) monitorPodsLifecycle(ctx context.Context) error {
	timeout := time.Duration(c.config.TimeoutMinutes) * time.Minute
	monitorCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	monitorInterval := time.Duration(c.config.MonitorInterval) * time.Second
	logger.Infof(ctx, "🔍 开始监控%d个Pod生命周期，超时时间: %v，监控间隔: %v",
		c.config.TargetReplicas, timeout, monitorInterval)

	return wait.PollImmediate(monitorInterval, timeout, func() (bool, error) {
		// 获取所有相关Pod
		pods, err := c.base.K8SClient.CoreV1().Pods("default").List(monitorCtx, metav1.ListOptions{
			LabelSelector: "app=test-100-containers",
		})
		if err != nil {
			logger.Warnf(monitorCtx, "获取Pod列表失败: %v", err)
			return false, nil
		}

		// 更新Pod记录
		c.updatePodRecords(pods.Items)

		// 检查里程碑
		c.checkMilestones()

		// 统计当前就绪Pod数量
		readyCount := c.getReadyPodCount()
		logger.Infof(monitorCtx, "📊 Pod就绪状态: %d/%d (%.1f%%)",
			readyCount, c.config.TargetReplicas,
			float64(readyCount)/float64(c.config.TargetReplicas)*100)

		// 检查是否全部就绪
		if readyCount >= c.config.TargetReplicas {
			c.timestamps.AllPodsReady = time.Now()
			logger.Infof(monitorCtx, "🎉 所有 %d 个Pod已就绪！", c.config.TargetReplicas)
			return true, nil
		}

		return false, nil
	})
}

// updatePodRecords 更新Pod记录
func (c *singleNode100ContainersScaling) updatePodRecords(pods []corev1.Pod) {
	c.podRecordsMutex.Lock()
	defer c.podRecordsMutex.Unlock()

	now := time.Now()

	for _, pod := range pods {
		// 跳过不在目标节点的Pod
		if pod.Spec.NodeName != c.selectedNode {
			continue
		}

		record, exists := c.podRecords[pod.Name]
		if !exists {
			// 新发现的Pod
			record = &PodReadinessRecord{
				PodName:       pod.Name,
				CreatedTime:   pod.CreationTimestamp.Time,
				CreationDelay: pod.CreationTimestamp.Time.Sub(c.timestamps.ScalingStartTime),
				IsReady:       false,
			}
			c.podRecords[pod.Name] = record
		}

		// 更新Pod状态
		if record.RunningTime.IsZero() && pod.Status.Phase == corev1.PodRunning {
			record.RunningTime = now
		}

		if !record.IsReady && c.isPodReady(&pod) {
			record.IsReady = true
			record.ReadyTime = now
			record.ReadyDelay = now.Sub(c.timestamps.ScalingStartTime)
		}
	}
}

// checkMilestones 检查里程碑
func (c *singleNode100ContainersScaling) checkMilestones() {
	readyCount := c.getReadyPodCount()

	if c.timestamps.FirstPodReady.IsZero() && readyCount >= 1 {
		c.timestamps.FirstPodReady = time.Now()
		duration := c.timestamps.FirstPodReady.Sub(c.timestamps.ScalingStartTime)
		logger.Infof(context.Background(), "🥇 第1个Pod就绪! 耗时: %v (%.3f秒)", duration, duration.Seconds())
	}

	// 动态计算里程碑点
	target := c.config.TargetReplicas
	milestone10 := target / 10
	milestone50 := target / 2

	// 只有当目标数量足够大时才设置这些里程碑
	if target >= 10 && c.timestamps.Pod10Ready.IsZero() && readyCount >= milestone10 {
		c.timestamps.Pod10Ready = time.Now()
		duration := c.timestamps.Pod10Ready.Sub(c.timestamps.ScalingStartTime)
		logger.Infof(context.Background(), "🔥 第%d个Pod就绪! (10%%) 耗时: %v (%.3f秒)", milestone10, duration, duration.Seconds())
	}

	if target >= 20 && c.timestamps.Pod50Ready.IsZero() && readyCount >= milestone50 {
		c.timestamps.Pod50Ready = time.Now()
		duration := c.timestamps.Pod50Ready.Sub(c.timestamps.ScalingStartTime)
		logger.Infof(context.Background(), "⚡ 第%d个Pod就绪! (50%%) 耗时: %v (%.3f秒)", milestone50, duration, duration.Seconds())
	}

	// 只有当目标是100个或更多时才显示"第100个Pod"里程碑
	if target >= 100 && c.timestamps.Pod100Ready.IsZero() && readyCount >= 100 {
		c.timestamps.Pod100Ready = time.Now()
		duration := c.timestamps.Pod100Ready.Sub(c.timestamps.ScalingStartTime)
		logger.Infof(context.Background(), "💯 第100个Pod就绪! 耗时: %v (%.3f秒)", duration, duration.Seconds())
	}

	// 检查是否所有Pod都就绪了
	if c.timestamps.AllPodsReady.IsZero() && readyCount >= target {
		c.timestamps.AllPodsReady = time.Now()
		duration := c.timestamps.AllPodsReady.Sub(c.timestamps.ScalingStartTime)
		logger.Infof(context.Background(), "🎉 全部%d个Pod就绪! 耗时: %v (%.3f秒)", target, duration, duration.Seconds())
	}
}

// getReadyPodCount 获取就绪Pod数量
func (c *singleNode100ContainersScaling) getReadyPodCount() int {
	c.podRecordsMutex.RLock()
	defer c.podRecordsMutex.RUnlock()

	count := 0
	for _, record := range c.podRecords {
		if record.IsReady {
			count++
		}
	}
	return count
}

// generateStatisticsReport 生成统计报告
func (c *singleNode100ContainersScaling) generateStatisticsReport(ctx context.Context) {
	c.podRecordsMutex.RLock()
	defer c.podRecordsMutex.RUnlock()

	// 收集就绪时间数据
	var readyDelays []time.Duration
	var readyPods []string
	var notReadyPods []string

	for podName, record := range c.podRecords {
		if record.IsReady {
			readyDelays = append(readyDelays, record.ReadyDelay)
			readyPods = append(readyPods, podName)
		} else {
			notReadyPods = append(notReadyPods, podName)
		}
	}

	// 计算分位数统计
	stats := c.calculatePercentiles(readyDelays)

	// 输出详细统计报告
	logger.Infof(ctx, "=== 单节点%d容器扩容时间统计 ===", c.config.TargetReplicas)
	logger.Infof(ctx, "扩容开始时间: %v", c.timestamps.ScalingStartTime.Format("2006-01-02 15:04:05.000"))

	// 里程碑时间
	if !c.timestamps.FirstPodReady.IsZero() {
		duration := c.timestamps.FirstPodReady.Sub(c.timestamps.ScalingStartTime)
		logger.Infof(ctx, "第1个Pod就绪: %v (%.3f秒)",
			c.timestamps.FirstPodReady.Format("2006-01-02 15:04:05.000"), duration.Seconds())
	}

	target := c.config.TargetReplicas
	milestone10 := target / 10
	milestone50 := target / 2

	if target >= 10 && !c.timestamps.Pod10Ready.IsZero() {
		duration := c.timestamps.Pod10Ready.Sub(c.timestamps.ScalingStartTime)
		logger.Infof(ctx, "第%d个Pod就绪 (10%%): %v (%.3f秒)",
			milestone10, c.timestamps.Pod10Ready.Format("2006-01-02 15:04:05.000"), duration.Seconds())
	}

	if target >= 20 && !c.timestamps.Pod50Ready.IsZero() {
		duration := c.timestamps.Pod50Ready.Sub(c.timestamps.ScalingStartTime)
		logger.Infof(ctx, "第%d个Pod就绪 (50%%): %v (%.3f秒)",
			milestone50, c.timestamps.Pod50Ready.Format("2006-01-02 15:04:05.000"), duration.Seconds())
	}

	if target >= 100 && !c.timestamps.Pod100Ready.IsZero() {
		duration := c.timestamps.Pod100Ready.Sub(c.timestamps.ScalingStartTime)
		logger.Infof(ctx, "第100个Pod就绪: %v (%.3f秒)",
			c.timestamps.Pod100Ready.Format("2006-01-02 15:04:05.000"), duration.Seconds())
	}

	if !c.timestamps.AllPodsReady.IsZero() {
		duration := c.timestamps.AllPodsReady.Sub(c.timestamps.ScalingStartTime)
		logger.Infof(ctx, "全部%d个Pod就绪: %v (%.3f秒)",
			target, c.timestamps.AllPodsReady.Format("2006-01-02 15:04:05.000"), duration.Seconds())
	}

	// 分位数统计
	logger.Infof(ctx, "--- Pod就绪时间分位数统计 ---")
	logger.Infof(ctx, "P50 (中位数): %v (%.3f秒)", stats.P50, stats.P50.Seconds())
	logger.Infof(ctx, "P90: %v (%.3f秒)", stats.P90, stats.P90.Seconds())
	logger.Infof(ctx, "P95: %v (%.3f秒)", stats.P95, stats.P95.Seconds())
	logger.Infof(ctx, "P99: %v (%.3f秒)", stats.P99, stats.P99.Seconds())
	logger.Infof(ctx, "最快就绪: %v (%.3f秒)", stats.Min, stats.Min.Seconds())
	logger.Infof(ctx, "最慢就绪: %v (%.3f秒)", stats.Max, stats.Max.Seconds())
	logger.Infof(ctx, "平均就绪时间: %v (%.3f秒)", stats.Mean, stats.Mean.Seconds())

	// 成功率统计
	logger.Infof(ctx, "--- 扩容结果统计 ---")
	logger.Infof(ctx, "目标Pod数量: %d", c.config.TargetReplicas)
	logger.Infof(ctx, "成功创建Pod数量: %d", len(c.podRecords))
	logger.Infof(ctx, "成功就绪Pod数量: %d", stats.ReadyPods)
	logger.Infof(ctx, "成功率: %.1f%%", stats.SuccessRate)

	// 节点资源信息
	logger.Infof(ctx, "--- 节点资源信息 ---")
	logger.Infof(ctx, "测试节点: %s", c.nodeInfo.NodeName)
	logger.Infof(ctx, "节点Pod容量: %d", int(c.nodeInfo.PodCapacity.Value()))
	logger.Infof(ctx, "节点可分配Pod: %d", int(c.nodeInfo.PodAllocatable.Value()))
	logger.Infof(ctx, "测试前Pod数量: %d", c.nodeInfo.CurrentPods)
	logger.Infof(ctx, "网络可用IP数量: %d", c.nodeInfo.AvailableIPs)

	if len(notReadyPods) > 0 {
		logger.Warnf(ctx, "未就绪Pod数量: %d", len(notReadyPods))
		if len(notReadyPods) <= 10 {
			logger.Warnf(ctx, "未就绪Pod列表: %v", notReadyPods)
		}
	}

	logger.Infof(ctx, "===================================")
}

// calculatePercentiles 计算分位数统计
func (c *singleNode100ContainersScaling) calculatePercentiles(durations []time.Duration) PercentileStat {
	if len(durations) == 0 {
		return PercentileStat{}
	}

	// 排序
	sort.Slice(durations, func(i, j int) bool {
		return durations[i] < durations[j]
	})

	stats := PercentileStat{
		TotalPods: c.config.TargetReplicas,
		ReadyPods: len(durations),
		Min:       durations[0],
		Max:       durations[len(durations)-1],
	}

	// 计算成功率
	stats.SuccessRate = float64(stats.ReadyPods) / float64(stats.TotalPods) * 100

	// 计算平均值
	var total time.Duration
	for _, d := range durations {
		total += d
	}
	stats.Mean = total / time.Duration(len(durations))

	// 计算分位数
	n := len(durations)
	stats.P50 = durations[n*50/100]
	stats.P90 = durations[n*90/100]
	stats.P95 = durations[n*95/100]
	stats.P99 = durations[n*99/100]

	return stats
}

// 辅助函数
func (c *singleNode100ContainersScaling) isNodeReady(node *corev1.Node) bool {
	for _, condition := range node.Status.Conditions {
		if condition.Type == corev1.NodeNetworkUnavailable {
			return condition.Status == corev1.ConditionFalse
		}
	}
	return false
}

func (c *singleNode100ContainersScaling) isPodReady(pod *corev1.Pod) bool {
	for _, condition := range pod.Status.Conditions {
		if condition.Type == corev1.PodReady {
			return condition.Status == corev1.ConditionTrue
		}
	}
	return false
}

// Clean 清理测试资源
func (c *singleNode100ContainersScaling) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理100容器扩容测试资源")

	var errors []error

	// 删除Deployment
	if c.deploymentName != "" {
		err := c.base.K8SClient.AppsV1().Deployments("default").Delete(ctx, c.deploymentName, metav1.DeleteOptions{})
		if err != nil {
			logger.Errorf(ctx, "删除Deployment %s 失败: %v", c.deploymentName, err)
			errors = append(errors, err)
		} else {
			logger.Infof(ctx, "成功删除Deployment %s", c.deploymentName)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("清理过程中发生 %d 个错误", len(errors))
	}

	logger.Infof(ctx, "100容器扩容测试资源清理完成")
	return nil
}

func (c *singleNode100ContainersScaling) Continue(ctx context.Context) bool {
	return true
}

func (c *singleNode100ContainersScaling) ConfigFormat() string {
	return `{
  "image": "registry.baidubce.com/cce/nginx-alpine-go:latest",
  "targetReplicas": 100,
  "cpuRequest": "50m",
  "memoryRequest": "64Mi",
  "cpuLimit": "200m",
  "memoryLimit": "256Mi",
  "timeoutMinutes": 20,
  "monitorInterval": 2,
  "nodeSelector": {},
  "checkNodeResource": true,
  "extraLabels": {}
}`
}
