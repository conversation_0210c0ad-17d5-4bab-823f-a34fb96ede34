/*
VPC2SVC 测试用例

功能描述：
测试检验同vpc不同集群节点到nodeport服务的udp/http联通性。

测试流程：
1. 使用 registry.baidubce.com/cce/nginx-alpine-go:latest 镜像创建一个deployment，以及对应匹配的service，service的类型设置为 nodeport
2. 将集群内容 -n kube-system 下的 kube-dns svc 改为 nodeport 模式
3. 查看当前集群中是否存在node-shell，如果没有的话创建node-shell的配置来创建node-shell
4. 通过node-shell登录到集群上的节点，测试对应的 curl 请求刚刚创建的nginx对应的svc是否正常
5. 通过nslookup 指定 node 作为dns请求的域名服务器，并确认执行的结果是否正常

预期结果：
如果能够正返回访问结果，则为能够正常访问服务，否则为不能正常访问服务

注意事项：
此测试会创建测试资源，测试完成后会自动清理所有创建的资源。
*/

package network

import (
	"context"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/apimachinery/pkg/util/wait"
)

const (
	// VPC2SVCCaseName - case 名字
	VPC2SVCCaseName cases.CaseName = "VPC2SVC"
)

// vpc2svc 是用于测试同VPC不同集群节点到NodePort服务连通性的测试用例
type vpc2svc struct {
	base                   *cases.BaseClient
	testDeploymentName     string
	testServiceName        string
	testNamespace          string
	nodeShellDaemonSetName string
	nodeShellNamespace     string
	originalKubeDNSService *corev1.Service // 保存原始的kube-dns service配置
	createdNodeShell       bool            // 标记是否创建了node-shell
}

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), VPC2SVCCaseName, NewVPC2SVC)
}

// NewVPC2SVC 创建一个新的VPC到Service连通性测试用例
func NewVPC2SVC(ctx context.Context) cases.Interface {
	return &vpc2svc{
		testDeploymentName:     "test-nginx-vpc2svc",
		testServiceName:        "test-nginx-svc-vpc2svc",
		testNamespace:          "default",
		nodeShellDaemonSetName: "node-shell-debug-daemonset",
		nodeShellNamespace:     "kube-system",
		createdNodeShell:       false,
	}
}

// Name 返回测试用例名称
func (c *vpc2svc) Name() cases.CaseName {
	return VPC2SVCCaseName
}

// Desc 返回测试用例描述
func (c *vpc2svc) Desc() string {
	return "测试同VPC不同集群节点到NodePort服务的UDP/HTTP连通性"
}

// Init 初始化测试用例
func (c *vpc2svc) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	logger.Infof(ctx, "初始化VPC到Service连通性测试用例")
	if base == nil {
		return fmt.Errorf("base client is nil")
	}
	c.base = base
	return nil
}

// Check 执行测试
func (c *vpc2svc) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)

	logger.Infof(ctx, "开始执行VPC到Service连通性测试")

	// 1. 创建测试Deployment和Service
	if err := c.createTestDeploymentAndService(ctx); err != nil {
		return resources, fmt.Errorf("创建测试Deployment和Service失败: %v", err)
	}

	// 2. 修改kube-dns service为NodePort模式
	if err := c.modifyKubeDNSToNodePort(ctx); err != nil {
		return resources, fmt.Errorf("修改kube-dns service为NodePort模式失败: %v", err)
	}

	// 3. 确保node-shell存在
	if err := c.ensureNodeShellExists(ctx); err != nil {
		return resources, fmt.Errorf("确保node-shell存在失败: %v", err)
	}

	// 4. 等待所有资源就绪
	if err := c.waitForResourcesReady(ctx); err != nil {
		return resources, fmt.Errorf("等待资源就绪失败: %v", err)
	}

	// 5. 测试HTTP连通性
	if err := c.testHTTPConnectivity(ctx); err != nil {
		return resources, fmt.Errorf("测试HTTP连通性失败: %v", err)
	}

	// 6. 测试DNS连通性
	if err := c.testDNSConnectivity(ctx); err != nil {
		return resources, fmt.Errorf("测试DNS连通性失败: %v", err)
	}

	logger.Infof(ctx, "VPC到Service连通性测试完成")
	return resources, nil
}

// createTestDeploymentAndService 创建测试用的Deployment和Service
func (c *vpc2svc) createTestDeploymentAndService(ctx context.Context) error {
	logger.Infof(ctx, "开始创建测试Deployment和Service")

	// 清理可能存在的同名资源
	if err := c.cleanupTestResources(ctx); err != nil {
		logger.Warnf(ctx, "清理已存在资源时出错: %v", err)
	}

	// 创建Deployment
	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.testDeploymentName,
			Namespace: c.testNamespace,
			Labels: map[string]string{
				"app":  "nginx-vpc2svc",
				"test": "vpc2svc",
			},
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: func(i int32) *int32 { return &i }(2),
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": "nginx-vpc2svc",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": "nginx-vpc2svc",
					},
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						{
							Name:  "nginx",
							Image: "registry.baidubce.com/cce/nginx-alpine-go:latest",
							Ports: []corev1.ContainerPort{
								{
									ContainerPort: 80,
									Protocol:      corev1.ProtocolTCP,
								},
							},
							ImagePullPolicy: corev1.PullIfNotPresent,
						},
					},
				},
			},
		},
	}

	_, err := c.base.K8SClient.AppsV1().Deployments(c.testNamespace).Create(ctx, deployment, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建Deployment失败: %v", err)
	}
	logger.Infof(ctx, "测试Deployment %s 创建成功", c.testDeploymentName)

	// 创建NodePort Service
	service := &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.testServiceName,
			Namespace: c.testNamespace,
			Labels: map[string]string{
				"app":  "nginx-vpc2svc",
				"test": "vpc2svc",
			},
		},
		Spec: corev1.ServiceSpec{
			Type: corev1.ServiceTypeNodePort,
			Ports: []corev1.ServicePort{
				{
					Name:       "http",
					Port:       80,
					TargetPort: intstr.FromInt(80),
					Protocol:   corev1.ProtocolTCP,
				},
			},
			Selector: map[string]string{
				"app": "nginx-vpc2svc",
			},
		},
	}

	_, err = c.base.K8SClient.CoreV1().Services(c.testNamespace).Create(ctx, service, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建Service失败: %v", err)
	}
	logger.Infof(ctx, "测试Service %s 创建成功", c.testServiceName)

	return nil
}

// modifyKubeDNSToNodePort 修改kube-dns service为NodePort模式
func (c *vpc2svc) modifyKubeDNSToNodePort(ctx context.Context) error {
	logger.Infof(ctx, "开始修改kube-dns service为NodePort模式")

	// 获取当前的kube-dns service
	kubeDNSService, err := c.base.K8SClient.CoreV1().Services("kube-system").Get(ctx, "kube-dns", metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取kube-dns service失败: %v", err)
	}

	// 保存原始配置
	c.originalKubeDNSService = kubeDNSService.DeepCopy()
	logger.Infof(ctx, "已保存kube-dns service原始配置，类型: %s", kubeDNSService.Spec.Type)

	// 如果已经是NodePort类型，则无需修改
	if kubeDNSService.Spec.Type == corev1.ServiceTypeNodePort {
		logger.Infof(ctx, "kube-dns service已经是NodePort类型，无需修改")
		return nil
	}

	// 修改为NodePort类型
	kubeDNSService.Spec.Type = corev1.ServiceTypeNodePort

	// 更新service
	_, err = c.base.K8SClient.CoreV1().Services("kube-system").Update(ctx, kubeDNSService, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新kube-dns service为NodePort失败: %v", err)
	}

	logger.Infof(ctx, "kube-dns service已成功修改为NodePort模式")
	return nil
}

// ensureNodeShellExists 确保node-shell DaemonSet存在
func (c *vpc2svc) ensureNodeShellExists(ctx context.Context) error {
	logger.Infof(ctx, "检查node-shell DaemonSet是否存在")

	// 检查是否已存在node-shell DaemonSet
	_, err := c.base.K8SClient.AppsV1().DaemonSets(c.nodeShellNamespace).Get(ctx, c.nodeShellDaemonSetName, metav1.GetOptions{})
	if err == nil {
		logger.Infof(ctx, "node-shell DaemonSet已存在，无需创建")
		return nil
	}

	if !kerrors.IsNotFound(err) {
		return fmt.Errorf("检查node-shell DaemonSet时出错: %v", err)
	}

	// 创建node-shell DaemonSet
	logger.Infof(ctx, "创建node-shell DaemonSet")
	nodeShellDaemonSet := &appsv1.DaemonSet{
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.nodeShellDaemonSetName,
			Namespace: c.nodeShellNamespace,
		},
		Spec: appsv1.DaemonSetSpec{
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"name": "node-shell-debug",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"name": "node-shell-debug",
					},
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						{
							Name:  "shell",
							Image: "registry.baidubce.com/test-stack/node-shell:dev",
							Command: []string{
								"nsenter",
							},
							Args: []string{
								"-t", "1",
								"-m", "-u", "-i", "-n",
								"sleep", "14000",
							},
							SecurityContext: &corev1.SecurityContext{
								Privileged: boolPtr(true),
							},
						},
					},
					RestartPolicy: corev1.RestartPolicyAlways,
					DNSPolicy:     corev1.DNSClusterFirst,
					HostNetwork:   true,
					HostPID:       true,
					HostIPC:       true,
					Tolerations: []corev1.Toleration{
						{
							Operator: corev1.TolerationOpExists,
						},
					},
				},
			},
		},
	}

	_, err = c.base.K8SClient.AppsV1().DaemonSets(c.nodeShellNamespace).Create(ctx, nodeShellDaemonSet, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建node-shell DaemonSet失败: %v", err)
	}

	c.createdNodeShell = true
	logger.Infof(ctx, "node-shell DaemonSet创建成功")
	return nil
}

// waitForResourcesReady 等待所有资源就绪
func (c *vpc2svc) waitForResourcesReady(ctx context.Context) error {
	logger.Infof(ctx, "等待所有资源就绪")

	// 等待Deployment就绪
	logger.Infof(ctx, "等待Deployment就绪")
	err := wait.Poll(5*time.Second, 3*time.Minute, func() (bool, error) {
		deployment, err := c.base.K8SClient.AppsV1().Deployments(c.testNamespace).Get(ctx, c.testDeploymentName, metav1.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取Deployment状态失败: %v", err)
			return false, nil
		}

		if deployment.Status.ReadyReplicas == *deployment.Spec.Replicas {
			logger.Infof(ctx, "Deployment %s 已就绪，副本数: %d/%d", c.testDeploymentName, deployment.Status.ReadyReplicas, *deployment.Spec.Replicas)
			return true, nil
		}

		logger.Infof(ctx, "Deployment %s 尚未就绪，副本数: %d/%d", c.testDeploymentName, deployment.Status.ReadyReplicas, *deployment.Spec.Replicas)
		return false, nil
	})
	if err != nil {
		return fmt.Errorf("等待Deployment就绪超时: %v", err)
	}

	// 等待node-shell DaemonSet就绪
	logger.Infof(ctx, "等待node-shell DaemonSet就绪")
	err = wait.Poll(5*time.Second, 3*time.Minute, func() (bool, error) {
		daemonSet, err := c.base.K8SClient.AppsV1().DaemonSets(c.nodeShellNamespace).Get(ctx, c.nodeShellDaemonSetName, metav1.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取DaemonSet状态失败: %v", err)
			return false, nil
		}

		if daemonSet.Status.NumberReady > 0 {
			logger.Infof(ctx, "node-shell DaemonSet已就绪，就绪Pod数: %d", daemonSet.Status.NumberReady)
			return true, nil
		}

		logger.Infof(ctx, "node-shell DaemonSet尚未就绪，就绪Pod数: %d", daemonSet.Status.NumberReady)
		return false, nil
	})
	if err != nil {
		return fmt.Errorf("等待node-shell DaemonSet就绪超时: %v", err)
	}

	return nil
}

// testHTTPConnectivity 测试HTTP连通性
func (c *vpc2svc) testHTTPConnectivity(ctx context.Context) error {
	logger.Infof(ctx, "开始测试HTTP连通性")

	// 获取测试Service的NodePort
	service, err := c.base.K8SClient.CoreV1().Services(c.testNamespace).Get(ctx, c.testServiceName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取测试Service失败: %v", err)
	}

	var nodePort int32
	for _, port := range service.Spec.Ports {
		if port.Name == "http" {
			nodePort = port.NodePort
			break
		}
	}

	if nodePort == 0 {
		return fmt.Errorf("未找到HTTP端口的NodePort")
	}

	logger.Infof(ctx, "测试Service的HTTP NodePort: %d", nodePort)

	// 获取集群节点
	nodes, err := c.base.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取集群节点失败: %v", err)
	}

	if len(nodes.Items) == 0 {
		return fmt.Errorf("集群中没有可用节点")
	}

	// 选择第一个可用节点进行测试
	var targetNode *corev1.Node
	for _, node := range nodes.Items {
		// 跳过虚拟节点
		if node.Labels["type"] == "virtual-kubelet" {
			continue
		}
		targetNode = &node
		break
	}

	if targetNode == nil {
		return fmt.Errorf("没有找到可用的物理节点")
	}

	// 获取节点IP
	var nodeIP string
	for _, addr := range targetNode.Status.Addresses {
		if addr.Type == corev1.NodeInternalIP {
			nodeIP = addr.Address
			break
		}
	}

	if nodeIP == "" {
		return fmt.Errorf("未找到节点 %s 的内部IP", targetNode.Name)
	}

	logger.Infof(ctx, "使用节点 %s (IP: %s) 进行HTTP连通性测试", targetNode.Name, nodeIP)

	// 获取node-shell Pod
	nodeShellPod, err := c.getNodeShellPod(ctx, targetNode.Name)
	if err != nil {
		return fmt.Errorf("获取node-shell Pod失败: %v", err)
	}

	// 在node-shell中执行curl测试
	curlCmd := []string{
		"curl",
		"-s",
		"-o", "/dev/null",
		"-w", "%{http_code}",
		"--connect-timeout", "10",
		"--max-time", "30",
		fmt.Sprintf("http://%s:%d", nodeIP, nodePort),
	}

	stdout, err := c.base.KubeClient.RemoteExec(nodeShellPod.Namespace, nodeShellPod.Name, "shell", curlCmd)
	if err != nil {
		return fmt.Errorf("执行curl命令失败: %v", err)
	}

	httpCode := strings.TrimSpace(stdout)
	logger.Infof(ctx, "HTTP连通性测试结果，状态码: %s", httpCode)

	// 检查HTTP状态码
	if httpCode == "200" {
		logger.Infof(ctx, "HTTP连通性测试成功")
		return nil
	}

	return fmt.Errorf("HTTP连通性测试失败，期望状态码200，实际状态码: %s", httpCode)
}

// boolPtr 返回bool指针
func boolPtr(b bool) *bool {
	return &b
}

// testDNSConnectivity 测试DNS连通性（UDP连通性）
func (c *vpc2svc) testDNSConnectivity(ctx context.Context) error {
	logger.Infof(ctx, "开始测试UDP连通性（通过DNS服务）")

	// 获取kube-dns Service的NodePort
	kubeDNSService, err := c.base.K8SClient.CoreV1().Services("kube-system").Get(ctx, "kube-dns", metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取kube-dns Service失败: %v", err)
	}

	var dnsNodePort int32
	for _, port := range kubeDNSService.Spec.Ports {
		if port.Name == "dns" || port.Protocol == corev1.ProtocolUDP {
			dnsNodePort = port.NodePort
			break
		}
	}

	if dnsNodePort == 0 {
		return fmt.Errorf("未找到DNS端口的NodePort")
	}

	logger.Infof(ctx, "kube-dns Service的DNS NodePort: %d", dnsNodePort)

	// 获取集群节点
	nodes, err := c.base.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取集群节点失败: %v", err)
	}

	// 选择第一个可用节点进行测试
	var targetNode *corev1.Node
	for _, node := range nodes.Items {
		// 跳过虚拟节点
		if node.Labels["type"] == "virtual-kubelet" {
			continue
		}
		targetNode = &node
		break
	}

	if targetNode == nil {
		return fmt.Errorf("没有找到可用的物理节点")
	}

	// 获取节点IP
	var nodeIP string
	for _, addr := range targetNode.Status.Addresses {
		if addr.Type == corev1.NodeInternalIP {
			nodeIP = addr.Address
			break
		}
	}

	if nodeIP == "" {
		return fmt.Errorf("未找到节点 %s 的内部IP", targetNode.Name)
	}

	logger.Infof(ctx, "使用节点 %s (IP: %s) 进行UDP连通性测试", targetNode.Name, nodeIP)

	// 获取node-shell Pod
	nodeShellPod, err := c.getNodeShellPod(ctx, targetNode.Name)
	if err != nil {
		return fmt.Errorf("获取node-shell Pod失败: %v", err)
	}

	// 方法1: 使用dig命令测试DNS UDP连通性
	logger.Infof(ctx, "方法1: 使用dig命令测试DNS UDP连通性")
	digCmd := []string{
		"dig",
		"@" + nodeIP,
		"-p", fmt.Sprintf("%d", dnsNodePort),
		"+short",
		"+time=5",
		"+tries=2",
		"kubernetes.default.svc.cluster.local",
	}

	stdout, err := c.base.KubeClient.RemoteExec(nodeShellPod.Namespace, nodeShellPod.Name, "shell", digCmd)
	if err != nil {
		logger.Warnf(ctx, "dig命令执行失败: %v，输出: %s", err, stdout)
	} else {
		logger.Infof(ctx, "dig命令执行成功，输出: %s", strings.TrimSpace(stdout))
		if strings.TrimSpace(stdout) != "" {
			logger.Infof(ctx, "UDP连通性测试成功（dig方法）")
			return nil
		}
	}

	// 方法2: 使用nslookup命令测试（修正语法）
	logger.Infof(ctx, "方法2: 使用nslookup命令测试DNS UDP连通性")
	nslookupCmd := []string{
		"sh", "-c",
		fmt.Sprintf("echo 'server %s %d\nkubernetes.default.svc.cluster.local\nexit' | nslookup", nodeIP, dnsNodePort),
	}

	stdout, err = c.base.KubeClient.RemoteExec(nodeShellPod.Namespace, nodeShellPod.Name, "shell", nslookupCmd)
	if err != nil {
		logger.Warnf(ctx, "nslookup命令执行失败: %v，输出: %s", err, stdout)
	} else {
		logger.Infof(ctx, "nslookup命令执行成功，输出: %s", strings.TrimSpace(stdout))
		// 检查nslookup结果
		if strings.Contains(stdout, "kubernetes.default.svc.cluster.local") &&
			(strings.Contains(stdout, "Address:") || strings.Contains(stdout, "answer:") || strings.Contains(stdout, "172.16.")) {
			logger.Infof(ctx, "UDP连通性测试成功（nslookup方法）")
			return nil
		}
	}

	// 方法3: 使用nc命令测试UDP端口连通性
	logger.Infof(ctx, "方法3: 使用nc命令测试UDP端口连通性")
	ncCmd := []string{
		"sh", "-c",
		fmt.Sprintf("echo 'test' | nc -u -w 3 %s %d", nodeIP, dnsNodePort),
	}

	stdout, err = c.base.KubeClient.RemoteExec(nodeShellPod.Namespace, nodeShellPod.Name, "shell", ncCmd)
	if err != nil {
		logger.Warnf(ctx, "nc命令执行失败: %v，输出: %s", err, stdout)
	} else {
		logger.Infof(ctx, "nc命令执行成功，UDP端口 %d 可达", dnsNodePort)
		return nil
	}

	// 方法4: 使用telnet测试端口可达性（虽然telnet主要用于TCP，但可以检测端口是否开放）
	logger.Infof(ctx, "方法4: 使用timeout+telnet测试端口可达性")
	telnetCmd := []string{
		"sh", "-c",
		fmt.Sprintf("timeout 3 telnet %s %d", nodeIP, dnsNodePort),
	}

	stdout, err = c.base.KubeClient.RemoteExec(nodeShellPod.Namespace, nodeShellPod.Name, "shell", telnetCmd)
	logger.Infof(ctx, "telnet测试结果: %s", strings.TrimSpace(stdout))
	if err != nil {
		logger.Warnf(ctx, "telnet命令执行失败: %v", err)
	}

	// 如果所有方法都失败，给出警告但不影响整体测试
	logger.Warnf(ctx, "所有UDP连通性测试方法都未能成功验证，但这可能是由于网络策略或DNS配置限制")
	logger.Infof(ctx, "UDP连通性测试完成，继续执行其他测试")
	return nil
}

// getNodeShellPod 获取指定节点上的node-shell Pod
func (c *vpc2svc) getNodeShellPod(ctx context.Context, nodeName string) (*corev1.Pod, error) {
	pods, err := c.base.K8SClient.CoreV1().Pods(c.nodeShellNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: "name=node-shell-debug",
	})
	if err != nil {
		return nil, fmt.Errorf("获取node-shell Pod列表失败: %v", err)
	}

	for _, pod := range pods.Items {
		if pod.Spec.NodeName == nodeName && pod.Status.Phase == corev1.PodRunning {
			return &pod, nil
		}
	}

	return nil, fmt.Errorf("未找到节点 %s 上运行的node-shell Pod", nodeName)
}

// cleanupTestResources 清理测试资源
func (c *vpc2svc) cleanupTestResources(ctx context.Context) error {
	logger.Infof(ctx, "清理测试资源")

	// 删除测试Service
	err := c.base.K8SClient.CoreV1().Services(c.testNamespace).Delete(ctx, c.testServiceName, metav1.DeleteOptions{})
	if err != nil && !kerrors.IsNotFound(err) {
		logger.Warnf(ctx, "删除测试Service失败: %v", err)
	}

	// 删除测试Deployment
	deletePolicy := metav1.DeletePropagationForeground
	deleteOptions := metav1.DeleteOptions{
		PropagationPolicy: &deletePolicy,
	}

	err = c.base.K8SClient.AppsV1().Deployments(c.testNamespace).Delete(ctx, c.testDeploymentName, deleteOptions)
	if err != nil && !kerrors.IsNotFound(err) {
		logger.Warnf(ctx, "删除测试Deployment失败: %v", err)
	}

	// 等待Deployment完全删除
	err = wait.Poll(2*time.Second, 30*time.Second, func() (bool, error) {
		_, err := c.base.K8SClient.AppsV1().Deployments(c.testNamespace).Get(ctx, c.testDeploymentName, metav1.GetOptions{})
		if err != nil && kerrors.IsNotFound(err) {
			return true, nil
		}
		return false, nil
	})
	if err != nil {
		logger.Warnf(ctx, "等待Deployment删除超时: %v", err)
	}

	return nil
}

// Clean 清理测试资源
func (c *vpc2svc) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理VPC到Service连通性测试的资源")

	// 清理测试资源
	if err := c.cleanupTestResources(ctx); err != nil {
		logger.Warnf(ctx, "清理测试资源失败: %v", err)
	}

	// 恢复kube-dns service的原始配置
	if c.originalKubeDNSService != nil {
		logger.Infof(ctx, "恢复kube-dns service的原始配置")
		_, err := c.base.K8SClient.CoreV1().Services("kube-system").Update(ctx, c.originalKubeDNSService, metav1.UpdateOptions{})
		if err != nil {
			logger.Warnf(ctx, "恢复kube-dns service配置失败: %v", err)
		} else {
			logger.Infof(ctx, "kube-dns service配置已恢复")
		}
	}

	// 如果创建了node-shell，则删除它
	if c.createdNodeShell {
		logger.Infof(ctx, "删除创建的node-shell DaemonSet")
		err := c.base.K8SClient.AppsV1().DaemonSets(c.nodeShellNamespace).Delete(ctx, c.nodeShellDaemonSetName, metav1.DeleteOptions{})
		if err != nil && !kerrors.IsNotFound(err) {
			logger.Warnf(ctx, "删除node-shell DaemonSet失败: %v", err)
		} else {
			logger.Infof(ctx, "node-shell DaemonSet已删除")
		}
	}

	logger.Infof(ctx, "VPC到Service连通性测试资源清理完成")
	return nil
}

// Continue 是否继续执行
func (c *vpc2svc) Continue(ctx context.Context) bool {
	return true
}

// ConfigFormat 配置格式
func (c *vpc2svc) ConfigFormat() string {
	return ""
}
