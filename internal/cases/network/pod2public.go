/*
Pod2Public 测试用例

功能描述：
测试当前集群的公网访问能力，整个流程需要创建一个pod，并进入pod内执行curl命令，来验证是否具有公网访问能力。

测试流程：
1. 使用 registry.baidubce.com/csm-offline/dnstools:zzw-test 镜像创建一个 pod
2. 进入对应的pod中，访问 www.baidu.com 以及 www.sina.com
3. 确认一定时间内是否存在返回结果

预期结果：
如果能够正返回访问结果，则为能够正常访问公网，否则为不能正常访问公网

注意事项：
此测试会创建测试Pod，测试完成后会自动清理所有创建的资源。
*/

package network

import (
	"context"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	corev1 "k8s.io/api/core/v1"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
)

const (
	// Pod2PublicCaseName - case 名字
	Pod2PublicCaseName cases.CaseName = "Pod2Public"
)

// pod2Public 是用于测试Pod公网访问能力的测试用例
type pod2Public struct {
	base          *cases.BaseClient
	testPodName   string
	testNamespace string
	containerName string
	testSites     []string // 测试的网站列表
}

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), Pod2PublicCaseName, NewPod2Public)
}

// NewPod2Public 创建一个新的Pod公网访问测试用例
func NewPod2Public(ctx context.Context) cases.Interface {
	return &pod2Public{
		testPodName:   "test-pod2public",
		testNamespace: "default",
		containerName: "dnstools",
		testSites:     []string{"www.baidu.com", "www.sina.com"},
	}
}

// Name 返回测试用例名称
func (c *pod2Public) Name() cases.CaseName {
	return Pod2PublicCaseName
}

// Desc 返回测试用例描述
func (c *pod2Public) Desc() string {
	return "测试当前集群的公网访问能力"
}

// Init 初始化测试用例
func (c *pod2Public) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	logger.Infof(ctx, "初始化Pod公网访问测试用例")
	if base == nil {
		return fmt.Errorf("base client is nil")
	}
	c.base = base
	return nil
}

// Check 执行测试
func (c *pod2Public) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)

	logger.Infof(ctx, "开始执行Pod公网访问测试")

	// 1. 创建测试Pod
	if err := c.createTestPod(ctx); err != nil {
		return resources, fmt.Errorf("创建测试Pod失败: %v", err)
	}

	// 2. 等待Pod就绪
	if err := c.waitForPodReady(ctx); err != nil {
		return resources, fmt.Errorf("等待Pod就绪失败: %v", err)
	}

	// 3. 测试公网访问
	if err := c.testPublicAccess(ctx); err != nil {
		return resources, fmt.Errorf("测试公网访问失败: %v", err)
	}

	logger.Infof(ctx, "Pod公网访问测试完成")
	return resources, nil
}

// createTestPod 创建测试Pod
func (c *pod2Public) createTestPod(ctx context.Context) error {
	logger.Infof(ctx, "开始创建测试Pod: %s", c.testPodName)

	// 检查是否存在同名Pod，如果存在则删除
	if err := c.cleanupExistingPod(ctx); err != nil {
		return fmt.Errorf("清理已存在的Pod失败: %v", err)
	}

	// 定义Pod规格
	pod := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.testPodName,
			Namespace: c.testNamespace,
			Labels: map[string]string{
				"app":  "pod2public-test",
				"test": "network-access",
			},
		},
		Spec: corev1.PodSpec{
			RestartPolicy: corev1.RestartPolicyNever,
			Containers: []corev1.Container{
				{
					Name:  c.containerName,
					Image: "registry.baidubce.com/csm-offline/dnstools:zzw-test",
					Command: []string{
						"sleep",
						"3600", // 睡眠1小时，确保有足够时间进行测试
					},
					ImagePullPolicy: corev1.PullIfNotPresent,
				},
			},
		},
	}

	// 创建Pod
	_, err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).Create(ctx, pod, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建Pod失败: %v", err)
	}

	logger.Infof(ctx, "测试Pod %s 创建成功", c.testPodName)
	return nil
}

// cleanupExistingPod 清理已存在的Pod
func (c *pod2Public) cleanupExistingPod(ctx context.Context) error {
	// 检查Pod是否存在
	_, err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).Get(ctx, c.testPodName, metav1.GetOptions{})
	if err != nil {
		if kerrors.IsNotFound(err) {
			// Pod不存在，无需清理
			return nil
		}
		return fmt.Errorf("检查Pod是否存在失败: %v", err)
	}

	logger.Infof(ctx, "发现已存在的Pod %s，准备删除", c.testPodName)

	// 删除已存在的Pod，使用强制删除
	gracePeriodSeconds := int64(0)
	deletePolicy := metav1.DeletePropagationForeground
	deleteOptions := metav1.DeleteOptions{
		PropagationPolicy:  &deletePolicy,
		GracePeriodSeconds: &gracePeriodSeconds,
	}

	err = c.base.K8SClient.CoreV1().Pods(c.testNamespace).Delete(ctx, c.testPodName, deleteOptions)
	if err != nil && !kerrors.IsNotFound(err) {
		return fmt.Errorf("删除已存在的Pod失败: %v", err)
	}

	// 等待Pod完全删除，减少等待时间
	err = wait.Poll(1*time.Second, 10*time.Second, func() (bool, error) {
		_, err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).Get(ctx, c.testPodName, metav1.GetOptions{})
		if err != nil && kerrors.IsNotFound(err) {
			return true, nil
		}
		logger.Infof(ctx, "等待Pod删除完成...")
		return false, nil
	})
	if err != nil {
		logger.Warnf(ctx, "等待Pod删除超时，但继续创建新Pod: %v", err)
		// 不返回错误，继续尝试创建新Pod
	}

	logger.Infof(ctx, "已存在的Pod删除完成")
	return nil
}

// waitForPodReady 等待Pod就绪
func (c *pod2Public) waitForPodReady(ctx context.Context) error {
	logger.Infof(ctx, "等待Pod %s 就绪", c.testPodName)

	err := wait.Poll(5*time.Second, 3*time.Minute, func() (bool, error) {
		pod, err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).Get(ctx, c.testPodName, metav1.GetOptions{})
		if err != nil {
			logger.Warnf(ctx, "获取Pod状态失败: %v", err)
			return false, nil
		}

		// 检查Pod状态
		if pod.Status.Phase == corev1.PodRunning {
			// 检查所有容器是否就绪
			for _, containerStatus := range pod.Status.ContainerStatuses {
				if !containerStatus.Ready {
					logger.Infof(ctx, "容器 %s 尚未就绪", containerStatus.Name)
					return false, nil
				}
			}
			logger.Infof(ctx, "Pod %s 已就绪", c.testPodName)
			return true, nil
		}

		logger.Infof(ctx, "Pod %s 当前状态: %s", c.testPodName, pod.Status.Phase)

		// 如果Pod失败，输出详细信息
		if pod.Status.Phase == corev1.PodFailed {
			logger.Errorf(ctx, "Pod %s 启动失败", c.testPodName)
			for _, containerStatus := range pod.Status.ContainerStatuses {
				if containerStatus.State.Waiting != nil {
					logger.Errorf(ctx, "容器 %s 等待中: %s - %s",
						containerStatus.Name,
						containerStatus.State.Waiting.Reason,
						containerStatus.State.Waiting.Message)
				}
				if containerStatus.State.Terminated != nil {
					logger.Errorf(ctx, "容器 %s 已终止: %s - %s",
						containerStatus.Name,
						containerStatus.State.Terminated.Reason,
						containerStatus.State.Terminated.Message)
				}
			}
			return false, fmt.Errorf("Pod启动失败")
		}

		return false, nil
	})

	if err != nil {
		return fmt.Errorf("等待Pod就绪超时: %v", err)
	}

	return nil
}

// testPublicAccess 测试公网访问
func (c *pod2Public) testPublicAccess(ctx context.Context) error {
	logger.Infof(ctx, "开始测试公网访问能力")

	// 测试每个网站
	for _, site := range c.testSites {
		if err := c.testSiteAccess(ctx, site); err != nil {
			return fmt.Errorf("访问 %s 失败: %v", site, err)
		}
	}

	logger.Infof(ctx, "所有网站访问测试通过")
	return nil
}

// testSiteAccess 测试单个网站访问
func (c *pod2Public) testSiteAccess(ctx context.Context, site string) error {
	logger.Infof(ctx, "测试访问网站: %s", site)

	var dnsSuccess, httpSuccess bool
	var dnsErr, httpErr error

	// 首先测试DNS解析
	dnsErr = c.testDNSResolution(ctx, site)
	if dnsErr != nil {
		logger.Warnf(ctx, "DNS解析失败: %v", dnsErr)
	} else {
		dnsSuccess = true
		logger.Infof(ctx, "DNS解析成功")
	}

	// 尝试ping测试网络连通性
	pingErr := c.testPing(ctx, site)
	if pingErr != nil {
		logger.Warnf(ctx, "ping测试失败: %v", pingErr)
	} else {
		logger.Infof(ctx, "ping测试成功")
	}

	// 构造curl命令，设置超时时间为10秒
	cmd := []string{
		"curl",
		"-4", // 强制使用IPv4
		"-s",
		"-o", "/dev/null",
		"-w", "%{http_code}",
		"--connect-timeout", "5",
		"--max-time", "10",
		fmt.Sprintf("http://%s", site),
	}

	// 在Pod中执行curl命令
	stdout, httpErr := c.base.KubeClient.RemoteExec(c.testNamespace, c.testPodName, c.containerName, cmd)
	if httpErr != nil {
		logger.Warnf(ctx, "HTTP访问失败: %v", httpErr)
	} else {
		// 检查HTTP状态码
		httpCode := strings.TrimSpace(stdout)
		logger.Infof(ctx, "访问 %s 的HTTP状态码: %s", site, httpCode)

		// 检查是否为成功的HTTP状态码（2xx或3xx）
		if len(httpCode) >= 1 {
			firstChar := httpCode[0]
			if firstChar == '2' || firstChar == '3' {
				httpSuccess = true
				logger.Infof(ctx, "HTTP访问成功，状态码: %s", httpCode)
			}
		}
	}

	// 根据测试结果给出综合评估
	if httpSuccess {
		logger.Infof(ctx, "网站 %s 完全可访问（DNS解析、HTTP访问均成功）", site)
		return nil
	} else if dnsSuccess {
		logger.Warnf(ctx, "网站 %s 部分可访问（DNS解析成功，但HTTP访问失败）", site)
		logger.Infof(ctx, "这表明集群具有基本的公网DNS解析能力，但可能存在出站HTTP连接限制")
		// DNS解析成功说明有基本的公网访问能力，不算完全失败
		return nil
	} else {
		logger.Errorf(ctx, "网站 %s 完全不可访问（DNS解析和HTTP访问均失败）", site)
		return fmt.Errorf("网站 %s 完全不可访问，DNS解析失败: %v，HTTP访问失败: %v", site, dnsErr, httpErr)
	}
}

// testDNSResolution 测试DNS解析
func (c *pod2Public) testDNSResolution(ctx context.Context, site string) error {
	logger.Infof(ctx, "测试DNS解析: %s", site)

	cmd := []string{
		"nslookup",
		site,
	}

	stdout, err := c.base.KubeClient.RemoteExec(c.testNamespace, c.testPodName, c.containerName, cmd)
	if err != nil {
		return fmt.Errorf("DNS解析失败: %v", err)
	}

	logger.Infof(ctx, "DNS解析结果: %s", strings.TrimSpace(stdout))
	return nil
}

// testPing 测试ping连通性
func (c *pod2Public) testPing(ctx context.Context, site string) error {
	logger.Infof(ctx, "测试ping连通性: %s", site)

	pingCmd := []string{
		"ping",
		"-c", "3",
		"-W", "10",
		site,
	}

	pingOutput, err := c.base.KubeClient.RemoteExec(c.testNamespace, c.testPodName, c.containerName, pingCmd)
	if err != nil {
		return fmt.Errorf("ping失败: %v", err)
	}

	logger.Infof(ctx, "ping结果: %s", strings.TrimSpace(pingOutput))
	return nil
}

// Clean 清理测试资源
func (c *pod2Public) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理Pod公网访问测试的资源")

	// 删除测试Pod，使用强制删除
	gracePeriodSeconds := int64(0)
	deleteOptions := metav1.DeleteOptions{
		GracePeriodSeconds: &gracePeriodSeconds,
	}

	err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).Delete(ctx, c.testPodName, deleteOptions)
	if err != nil && !kerrors.IsNotFound(err) {
		logger.Warnf(ctx, "删除测试Pod失败: %v", err)
		return err
	}

	// 等待Pod完全删除，减少等待时间
	err = wait.Poll(1*time.Second, 10*time.Second, func() (bool, error) {
		_, err := c.base.K8SClient.CoreV1().Pods(c.testNamespace).Get(ctx, c.testPodName, metav1.GetOptions{})
		if err != nil && kerrors.IsNotFound(err) {
			return true, nil
		}
		logger.Infof(ctx, "等待测试Pod删除完成...")
		return false, nil
	})
	if err != nil {
		logger.Warnf(ctx, "等待测试Pod删除超时，但继续: %v", err)
		// 不返回错误，因为Pod可能已经被删除了
	}

	logger.Infof(ctx, "Pod公网访问测试资源清理完成")
	return nil
}

// Continue 是否继续执行
func (c *pod2Public) Continue(ctx context.Context) bool {
	return true
}

// ConfigFormat 配置格式
func (c *pod2Public) ConfigFormat() string {
	return ""
}
