/*
Copyright 2024 Baidu Inc.

本文件包含了针对CCE集群大规模节点网络就绪分位时间的自动化测试用例。

用例主要验证以下内容：
1. 检查集群是否有可用节点组，没有就新建一个节点组
2. 执行大规模扩容操作（默认200个节点）
3. 并发监控所有新节点的就绪状态
4. 记录每个节点的就绪时间点
5. 计算并输出详细的分位时间统计

测试流程：
1. 检查集群当前节点组状态，获取可用的节点组
2. 如果没有可用节点组，则创建一个新的节点组
3. 记录扩容开始时间
4. 对选定的节点组进行大规模扩容操作
5. 并发监控所有新节点的就绪状态，记录每个节点的就绪时间
6. 等待所有节点就绪或超时
7. 计算并输出分位时间统计（P50, P90, P95, P99等）
8. 清理测试过程中创建的资源

该测试用于评估CCE集群大规模节点扩容的网络就绪性能分布，为性能优化和容量规划提供数据支撑。
*/

package network

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"sync"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	// MassNodeNetworkReadinessCaseName - case 名字
	MassNodeNetworkReadinessCaseName cases.CaseName = "MassNodeNetworkReadiness"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), MassNodeNetworkReadinessCaseName, NewMassNodeNetworkReadiness)
}

// NodeReadinessRecord 记录节点就绪状态
type NodeReadinessRecord struct {
	NodeName    string        `json:"nodeName"`
	ReadyTime   time.Time     `json:"readyTime"`
	Duration    time.Duration `json:"duration"`
	IsReady     bool          `json:"isReady"`
	FirstSeenAt time.Time     `json:"firstSeenAt"`
}

// PercentileStats 分位数统计
type PercentileStats struct {
	P50         time.Duration `json:"p50"`
	P90         time.Duration `json:"p90"`
	P95         time.Duration `json:"p95"`
	P99         time.Duration `json:"p99"`
	Min         time.Duration `json:"min"`
	Max         time.Duration `json:"max"`
	Mean        time.Duration `json:"mean"`
	TotalNodes  int           `json:"totalNodes"`
	ReadyNodes  int           `json:"readyNodes"`
	SuccessRate float64       `json:"successRate"`
}

// MassNodeConfig 大规模节点测试配置
type MassNodeConfig struct {
	TargetNodeCount int           `json:"targetNodeCount"` // 目标扩容节点数，默认200
	TimeoutMinutes  int           `json:"timeoutMinutes"`  // 超时时间（分钟），默认30
	MonitorInterval time.Duration `json:"monitorInterval"` // 监控间隔，默认10秒
}

type massNodeNetworkReadiness struct {
	base             *cases.BaseClient
	config           MassNodeConfig
	instanceGroup    string
	originalReplicas int
	createdNodeGroup bool // 标记是否是我们创建的节点组
	startTime        time.Time
	resources        []cases.Resource

	// 节点监控相关
	nodeRecords      map[string]*NodeReadinessRecord
	nodeRecordsMutex sync.RWMutex
	initialNodes     map[string]struct{}
}

// NewMassNodeNetworkReadiness - 测试案例
func NewMassNodeNetworkReadiness(ctx context.Context) cases.Interface {
	return &massNodeNetworkReadiness{
		nodeRecords:  make(map[string]*NodeReadinessRecord),
		initialNodes: make(map[string]struct{}),
	}
}

func (c *massNodeNetworkReadiness) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base is nil")
	}

	c.base = base

	// 解析配置，如果没有配置则使用默认值
	if len(config) > 0 {
		if err := json.Unmarshal(config, &c.config); err != nil {
			return fmt.Errorf("解析配置失败: %v", err)
		}
	}

	// 设置默认值
	if c.config.TargetNodeCount <= 0 {
		c.config.TargetNodeCount = 200
	}
	if c.config.TimeoutMinutes <= 0 {
		c.config.TimeoutMinutes = 30
	}
	if c.config.MonitorInterval <= 0 {
		c.config.MonitorInterval = 10 * time.Second
	}

	logger.Infof(ctx, "大规模节点测试配置: 目标节点数=%d, 超时时间=%d分钟, 监控间隔=%v",
		c.config.TargetNodeCount, c.config.TimeoutMinutes, c.config.MonitorInterval)

	return nil
}

func (c *massNodeNetworkReadiness) Name() cases.CaseName {
	return MassNodeNetworkReadinessCaseName
}

func (c *massNodeNetworkReadiness) Desc() string {
	return "测试200节点网络就绪分位时间（创建小规格节点组）"
}

func (c *massNodeNetworkReadiness) Check(ctx context.Context) ([]cases.Resource, error) {
	logger.Infof(ctx, "开始执行大规模节点网络就绪分位时间测试，目标节点数: %d", c.config.TargetNodeCount)

	// 1. 记录初始节点状态
	err := c.recordInitialNodes(ctx)
	if err != nil {
		return nil, fmt.Errorf("记录初始节点状态失败: %v", err)
	}

	// 2. 获取或创建节点组
	instanceGroupID, err := c.getOrCreateInstanceGroup(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取或创建节点组失败: %v", err)
	}
	c.instanceGroup = instanceGroupID

	// 3. 获取原始副本数
	originalReplicas, err := c.getInstanceGroupReplicas(ctx, instanceGroupID)
	if err != nil {
		return nil, fmt.Errorf("获取节点组副本数失败: %v", err)
	}
	c.originalReplicas = originalReplicas
	logger.Infof(ctx, "节点组 %s 当前副本数: %d", instanceGroupID, originalReplicas)

	// 4. 记录扩容开始时间
	c.startTime = time.Now()
	logger.Infof(ctx, "大规模扩容开始时间: %v", c.startTime.Format("2006-01-02 15:04:05"))

	// 5. 执行大规模扩容操作
	targetReplicas := originalReplicas + c.config.TargetNodeCount
	err = c.scaleInstanceGroup(ctx, instanceGroupID, targetReplicas)
	if err != nil {
		return nil, fmt.Errorf("扩容节点组失败: %v", err)
	}

	// 6. 启动节点监控
	monitorCtx, cancel := context.WithTimeout(ctx, time.Duration(c.config.TimeoutMinutes)*time.Minute)
	defer cancel()

	err = c.monitorNodesReadiness(monitorCtx)
	if err != nil {
		logger.Errorf(ctx, "节点监控过程中出现错误: %v", err)
		// 不直接返回错误，继续生成统计报告
	}

	// 7. 计算并输出分位时间统计
	c.generateStatisticsReport(ctx)

	return c.resources, nil
}

// recordInitialNodes 记录初始节点状态
func (c *massNodeNetworkReadiness) recordInitialNodes(ctx context.Context) error {
	nodes, err := c.getClusterNodes(ctx)
	if err != nil {
		return fmt.Errorf("获取集群节点列表失败: %v", err)
	}

	for _, node := range nodes {
		c.initialNodes[node.Name] = struct{}{}
	}

	logger.Infof(ctx, "记录初始节点数量: %d", len(c.initialNodes))
	return nil
}

// getOrCreateInstanceGroup 获取或创建节点组
func (c *massNodeNetworkReadiness) getOrCreateInstanceGroup(ctx context.Context) (string, error) {
	// 为了批量节点测试，直接创建新的小规格节点组，避免大规格节点供应量不足的问题
	logger.Infof(ctx, "为大规模批量节点测试创建新的小规格节点组")
	return c.createInstanceGroup(ctx)
}

// createInstanceGroup 创建新的节点组
func (c *massNodeNetworkReadiness) createInstanceGroup(ctx context.Context) (string, error) {
	// 获取集群信息用于创建节点组
	cluster, err := c.base.CCEClient.GetCluster(ctx, c.base.ClusterID, nil)
	if err != nil {
		return "", fmt.Errorf("获取集群信息失败: %v", err)
	}

	// 获取集群中已有节点的信息，仅用于获取镜像ID
	instances, err := c.base.CCEClient.ListInstancesByPage(ctx, c.base.ClusterID, &ccev2.ListInstancesByPageParams{
		ClusterRole: types.ClusterRoleNode,
	}, nil)
	if err != nil {
		return "", fmt.Errorf("获取集群节点信息失败: %v", err)
	}
	if len(instances.InstancePage.InstanceList) == 0 {
		return "", fmt.Errorf("集群中没有可用的节点，无法获取配置信息")
	}

	// 仅获取镜像ID，其他配置使用固定小规格
	instance := instances.InstancePage.InstanceList[0]
	imageID := instance.Spec.ImageID
	if imageID == "" {
		return "", fmt.Errorf("获取到的镜像ID为空，无法创建节点组")
	}
	logger.Infof(ctx, "将使用已有节点的镜像ID: %s", imageID)

	// 获取集群额外信息，找到可用的子网
	clusterExtraInfo, err := c.base.CCEHostClient.GetClusterExtraInfo(ctx, c.base.ClusterID, nil)
	if err != nil {
		return "", fmt.Errorf("获取集群额外信息失败: %v", err)
	}

	var nodeSubnetID string
	for _, subnet := range clusterExtraInfo.Subnets {
		if subnet.SubnetCIDR != "" {
			nodeSubnetID = subnet.SubnetID
			break
		}
	}

	if nodeSubnetID == "" {
		return "", fmt.Errorf("未找到可用的子网")
	}

	logger.Infof(ctx, "将使用子网 %s 创建节点组", nodeSubnetID)

	// 从集群信息获取VPCID
	vpcID := cluster.Cluster.Spec.VPCID
	logger.Infof(ctx, "将使用VPC %s 创建节点组", vpcID)

	// 创建节点组，使用固定的小规格配置，按照标准格式
	igName := fmt.Sprintf("test-mass-network-readiness-%d", time.Now().Unix())
	createReq := &ccev2.CreateInstanceGroupRequest{
		InstanceGroupSpec: types.InstanceGroupSpec{
			InstanceGroupName: igName,
			Replicas:          0, // 创建0个节点的节点组，避免不必要的节点创建
			InstanceTemplate: types.InstanceTemplate{
				InstanceSpec: types.InstanceSpec{
					ClusterRole:  types.ClusterRoleNode,
					MachineType:  types.MachineTypeBCC, // 使用BCC机型
					InstanceType: "N5",                 // 小规格：2核8G
					VPCConfig: types.VPCConfig{
						VPCID:       vpcID,
						VPCSubnetID: nodeSubnetID,
						SecurityGroup: types.SecurityGroup{
							EnableCCERequiredSecurityGroup: true,
						},
					},
					InstanceResource: types.InstanceResource{
						CPU:          2,             // 固定2核
						MEM:          8,             // 固定8G内存
						MachineSpec:  "bcc.g4.c2m8", // 机器规格
						SpecID:       "g4",          // 规格ID
						RootDiskType: "cloud_hp1",   // 系统盘类型
						RootDiskSize: 100,           // 系统盘大小(GB)
					},
					ImageID:       imageID,
					AdminPassword: "Test123456!",
				},
			},
		},
	}

	// 发送创建请求
	createResp, err := c.base.CCEClient.CreateInstanceGroup(ctx, c.base.ClusterID, createReq, nil)
	if err != nil {
		return "", fmt.Errorf("创建节点组失败: %v", err)
	}

	// 保存节点组ID
	instanceGroupID := createResp.InstanceGroupID
	c.createdNodeGroup = true
	logger.Infof(ctx, "成功创建节点组 %s (ID: %s)", igName, instanceGroupID)

	// 将创建的节点组添加到资源列表
	c.resources = append(c.resources, cases.Resource{
		CaseName: c.Name(),
		Type:     cases.ResourceTypeCCEInstanceGroup,
		ID:       instanceGroupID,
	})

	// 等待节点组就绪
	logger.Infof(ctx, "等待节点组就绪...")

	// 创建instanceGroup资源对象并等待就绪
	ig, err := resource.NewInstanceGroup(ctx, c.base, instanceGroupID, 10*time.Second, 15*time.Minute)
	if err != nil {
		return "", fmt.Errorf("创建instanceGroup资源对象失败: %v", err)
	}

	if err := ig.CheckResource(ctx); err != nil {
		return "", fmt.Errorf("等待节点组就绪失败: %v", err)
	}

	logger.Infof(ctx, "节点组 %s 已就绪", instanceGroupID)

	return instanceGroupID, nil
}

// scaleInstanceGroup 扩容节点组
func (c *massNodeNetworkReadiness) scaleInstanceGroup(ctx context.Context, instanceGroupID string, replicas int) error {
	request := &ccev2.UpdateInstanceGroupReplicasRequest{
		Replicas: replicas,
	}

	logger.Infof(ctx, "开始大规模扩容节点组 %s 到 %d 个副本", instanceGroupID, replicas)

	resp, err := c.base.CCEClient.UpdateInstanceGroupReplicas(ctx, c.base.ClusterID, instanceGroupID, request, nil)
	if err != nil {
		return fmt.Errorf("更新节点组副本数失败: %v", err)
	}

	logger.Infof(ctx, "大规模扩容请求发送成功，RequestID: %s", resp.RequestID)
	return nil
}

// getInstanceGroupReplicas 获取节点组副本数
func (c *massNodeNetworkReadiness) getInstanceGroupReplicas(ctx context.Context, instanceGroupID string) (int, error) {
	ig, err := c.base.CCEClient.GetInstanceGroup(ctx, c.base.ClusterID, instanceGroupID, nil)
	if err != nil {
		return 0, fmt.Errorf("获取节点组信息失败: %v", err)
	}

	return ig.InstanceGroup.Spec.Replicas, nil
}

// monitorNodesReadiness 监控节点就绪状态
func (c *massNodeNetworkReadiness) monitorNodesReadiness(ctx context.Context) error {
	logger.Infof(ctx, "开始监控大规模节点就绪状态，目标节点数: %d", c.config.TargetNodeCount)

	ticker := time.NewTicker(c.config.MonitorInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			logger.Infof(ctx, "监控超时或被取消")
			return ctx.Err()
		case <-ticker.C:
			// 检查节点状态
			currentNodes, err := c.getClusterNodes(ctx)
			if err != nil {
				logger.Warnf(ctx, "获取节点列表失败: %v", err)
				continue
			}

			// 检查是否有新节点
			c.updateNodeRecords(currentNodes)

			// 检查是否所有目标节点都已就绪
			readyCount, totalNewNodes := c.getReadinessStats()
			logger.Infof(ctx, "节点就绪状态: %d/%d (%.1f%%)",
				readyCount, totalNewNodes, float64(readyCount)/float64(totalNewNodes)*100)

			// 如果所有新节点都已就绪，结束监控
			if totalNewNodes >= c.config.TargetNodeCount && readyCount == totalNewNodes {
				logger.Infof(ctx, "所有 %d 个新节点已就绪，监控完成", totalNewNodes)
				return nil
			}
		}
	}
}

// updateNodeRecords 更新节点记录
func (c *massNodeNetworkReadiness) updateNodeRecords(currentNodes []corev1.Node) {
	c.nodeRecordsMutex.Lock()
	defer c.nodeRecordsMutex.Unlock()

	now := time.Now()

	for _, node := range currentNodes {
		// 跳过初始节点
		if _, isInitial := c.initialNodes[node.Name]; isInitial {
			continue
		}

		// 检查是否是新节点
		record, exists := c.nodeRecords[node.Name]
		if !exists {
			// 新发现的节点
			record = &NodeReadinessRecord{
				NodeName:    node.Name,
				FirstSeenAt: now,
				IsReady:     false,
			}
			c.nodeRecords[node.Name] = record
		}

		// 检查节点是否就绪
		if !record.IsReady && c.isNodeReady(&node) {
			record.IsReady = true
			record.ReadyTime = now
			record.Duration = now.Sub(c.startTime)
			logger.Infof(context.Background(), "节点 %s 已就绪，耗时: %v", node.Name, record.Duration)
		}
	}
}

// getReadinessStats 获取就绪统计
func (c *massNodeNetworkReadiness) getReadinessStats() (readyCount, totalCount int) {
	c.nodeRecordsMutex.RLock()
	defer c.nodeRecordsMutex.RUnlock()

	totalCount = len(c.nodeRecords)
	for _, record := range c.nodeRecords {
		if record.IsReady {
			readyCount++
		}
	}

	return readyCount, totalCount
}

// generateStatisticsReport 生成统计报告
func (c *massNodeNetworkReadiness) generateStatisticsReport(ctx context.Context) {
	c.nodeRecordsMutex.RLock()
	defer c.nodeRecordsMutex.RUnlock()

	var readyDurations []time.Duration
	var readyNodes []string
	var notReadyNodes []string

	for _, record := range c.nodeRecords {
		if record.IsReady {
			readyDurations = append(readyDurations, record.Duration)
			readyNodes = append(readyNodes, record.NodeName)
		} else {
			notReadyNodes = append(notReadyNodes, record.NodeName)
		}
	}

	// 排序持续时间
	sort.Slice(readyDurations, func(i, j int) bool {
		return readyDurations[i] < readyDurations[j]
	})

	totalNodes := len(c.nodeRecords)
	readyCount := len(readyDurations)

	logger.Infof(ctx, "=== 大规模节点网络就绪分位时间统计 ===")
	logger.Infof(ctx, "目标节点数: %d", c.config.TargetNodeCount)
	logger.Infof(ctx, "实际发现节点数: %d", totalNodes)
	logger.Infof(ctx, "就绪节点数: %d", readyCount)
	logger.Infof(ctx, "就绪成功率: %.2f%%", float64(readyCount)/float64(totalNodes)*100)

	if len(readyDurations) > 0 {
		stats := c.calculatePercentiles(readyDurations)
		logger.Infof(ctx, "最快就绪时间: %v", stats.Min)
		logger.Infof(ctx, "P50 (中位数) 就绪时间: %v", stats.P50)
		logger.Infof(ctx, "P90 就绪时间: %v", stats.P90)
		logger.Infof(ctx, "P95 就绪时间: %v", stats.P95)
		logger.Infof(ctx, "P99 就绪时间: %v", stats.P99)
		logger.Infof(ctx, "最慢就绪时间: %v", stats.Max)
		logger.Infof(ctx, "平均就绪时间: %v", stats.Mean)

		// 输出部分节点详情
		logger.Infof(ctx, "--- 就绪时间详情 (前10个节点) ---")
		for i, nodeName := range readyNodes {
			if i >= 10 {
				logger.Infof(ctx, "... 还有 %d 个节点未显示", len(readyNodes)-10)
				break
			}
			if record, exists := c.nodeRecords[nodeName]; exists {
				logger.Infof(ctx, "节点 %s: %v", nodeName, record.Duration)
			}
		}
	}

	if len(notReadyNodes) > 0 {
		logger.Infof(ctx, "--- 未就绪节点 (前10个) ---")
		for i, nodeName := range notReadyNodes {
			if i >= 10 {
				logger.Infof(ctx, "... 还有 %d 个未就绪节点未显示", len(notReadyNodes)-10)
				break
			}
			logger.Infof(ctx, "未就绪节点: %s", nodeName)
		}
	}

	logger.Infof(ctx, "=====================================")
}

// calculatePercentiles 计算分位数
func (c *massNodeNetworkReadiness) calculatePercentiles(durations []time.Duration) PercentileStats {
	if len(durations) == 0 {
		return PercentileStats{}
	}

	n := len(durations)

	// 计算分位数
	p50 := durations[n*50/100]
	p90 := durations[n*90/100]
	p95 := durations[n*95/100]
	p99 := durations[n*99/100]

	// 计算平均值
	var total time.Duration
	for _, d := range durations {
		total += d
	}
	mean := total / time.Duration(n)

	return PercentileStats{
		P50:         p50,
		P90:         p90,
		P95:         p95,
		P99:         p99,
		Min:         durations[0],
		Max:         durations[n-1],
		Mean:        mean,
		TotalNodes:  n,
		ReadyNodes:  n,
		SuccessRate: 100.0,
	}
}

// getClusterNodes 获取集群节点列表
func (c *massNodeNetworkReadiness) getClusterNodes(ctx context.Context) ([]corev1.Node, error) {
	nodeList, err := c.base.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, err
	}

	return nodeList.Items, nil
}

// isNodeReady 检查节点是否就绪
func (c *massNodeNetworkReadiness) isNodeReady(node *corev1.Node) bool {
	for _, condition := range node.Status.Conditions {
		if condition.Type == corev1.NodeNetworkUnavailable {
			return condition.Status == corev1.ConditionFalse
		}
	}
	return false
}

func (c *massNodeNetworkReadiness) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理大规模测试环境")

	// 如果创建了新的节点组，需要删除它
	if c.createdNodeGroup && c.instanceGroup != "" {
		logger.Infof(ctx, "删除测试创建的节点组: %s", c.instanceGroup)

		// 首先将副本数设置为0
		err := c.scaleInstanceGroup(ctx, c.instanceGroup, 0)
		if err != nil {
			logger.Errorf(ctx, "缩容节点组到0失败: %v", err)
		} else {
			// 等待节点删除完成
			logger.Infof(ctx, "等待节点组节点删除完成")
			time.Sleep(5 * time.Minute) // 大规模节点删除需要更长时间
		}

		// 删除节点组
		_, err = c.base.CCEClient.DeleteInstanceGroup(ctx, c.base.ClusterID, c.instanceGroup, true, nil)
		if err != nil {
			logger.Errorf(ctx, "删除节点组失败: %v", err)
			return err
		}

		logger.Infof(ctx, "节点组删除请求已发送")
	} else if c.instanceGroup != "" && c.originalReplicas >= 0 {
		// 如果使用的是现有节点组，恢复原始副本数
		logger.Infof(ctx, "恢复节点组 %s 到原始副本数: %d", c.instanceGroup, c.originalReplicas)
		err := c.scaleInstanceGroup(ctx, c.instanceGroup, c.originalReplicas)
		if err != nil {
			logger.Errorf(ctx, "恢复节点组副本数失败: %v", err)
			return err
		}
	}

	return nil
}

func (c *massNodeNetworkReadiness) Continue(ctx context.Context) bool {
	return true
}

func (c *massNodeNetworkReadiness) ConfigFormat() string {
	return `{
	"targetNodeCount": 200,
	"timeoutMinutes": 30,
	"monitorInterval": "10s"
}`
}
