/*
Copyright 2024 Baidu Inc.

本文件包含了针对CCE集群新建网卡场景下单容器扩容耗时的自动化测试用例。

用例主要验证以下内容：
1. 基于真实的NRS（NetworkResourceSet）信息获取节点网络资源状态
2. 计算当前ENI的IP使用情况，精确定位ENI创建的临界点
3. 通过创建Pod消耗现有ENI的IP，直到接近新ENI创建的阈值
4. 在关键时刻创建测试Pod，真实测量ENI创建过程中的Pod启动耗时

测试流程：
1. 获取节点的NRS信息，了解ENI配置（通常每个ENI有40个IP）
2. 获取节点当前ENI列表和IP使用状态
3. 计算需要消耗多少IP才能触发新ENI创建
4. 创建临时Pod精确消耗IP，直到接近ENI创建阈值
5. 创建测试Deployment，触发ENI创建过程
6. 监控ENI创建过程和Pod启动时间
7. 输出真实的ENI创建耗时统计

该测试基于真实的网络资源状态，避免假设，能够准确模拟ENI创建场景。
*/

package network

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
)

const (
	// SingleContainerScalingWithNewENICaseName - case 名字
	SingleContainerScalingWithNewENICaseName cases.CaseName = "SingleContainerScalingWithNewENI"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), SingleContainerScalingWithNewENICaseName, NewSingleContainerScalingWithNewENI)
}

// NewENIScalingConfig 新建ENI扩容测试配置
type NewENIScalingConfig struct {
	Image          string            `json:"image"`          // 容器镜像，默认nginx
	CPURequest     string            `json:"cpuRequest"`     // CPU请求，默认100m
	MemoryRequest  string            `json:"memoryRequest"`  // 内存请求，默认128Mi
	CPULimit       string            `json:"cpuLimit"`       // CPU限制，默认500m
	MemoryLimit    string            `json:"memoryLimit"`    // 内存限制，默认512Mi
	TimeoutMinutes int               `json:"timeoutMinutes"` // 超时时间，默认15分钟
	NodeSelector   map[string]string `json:"nodeSelector"`   // 节点选择器
	ExtraLabels    map[string]string `json:"extraLabels"`    // 额外的标签
	SafetyMargin   int               `json:"safetyMargin"`   // 安全边距IP数量，默认2个
}

// ENICreationTimestamps ENI创建和Pod各阶段时间戳
type ENICreationTimestamps struct {
	ScalingStartTime time.Time `json:"scalingStartTime"`
	ENICreationStart time.Time `json:"eniCreationStart"`
	ENICreationEnd   time.Time `json:"eniCreationEnd"`
	PodCreatedTime   time.Time `json:"podCreatedTime"`
	IPAssignedTime   time.Time `json:"ipAssignedTime"`
	PodRunningTime   time.Time `json:"podRunningTime"`
	PodReadyTime     time.Time `json:"podReadyTime"`
}

// NodeNetworkInfo 节点网络信息
type NodeNetworkInfo struct {
	NRS          *types.NetworkResourceSet `json:"nrs"`
	CurrentENIs  []types.ENI               `json:"currentENIs"`
	UsedIPs      int                       `json:"usedIPs"`
	MaxIPsPerENI int                       `json:"maxIPsPerENI"`
	AvailableIPs int                       `json:"availableIPs"`
	IPsToConsume int                       `json:"ipsToConsume"`
	NeedNewENI   bool                      `json:"needNewENI"`
}

type singleContainerScalingWithNewENI struct {
	base           *cases.BaseClient
	config         NewENIScalingConfig
	deploymentName string
	selectedNode   string
	timestamps     ENICreationTimestamps
	resources      []cases.Resource
	preExhaustPods []string // 预消耗IP的Pod列表
	networkInfo    NodeNetworkInfo
}

// NewSingleContainerScalingWithNewENI - 测试案例
func NewSingleContainerScalingWithNewENI(ctx context.Context) cases.Interface {
	return &singleContainerScalingWithNewENI{}
}

func (c *singleContainerScalingWithNewENI) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base is nil")
	}

	c.base = base

	// 解析配置，如果没有配置则使用默认值
	if len(config) > 0 {
		if err := json.Unmarshal(config, &c.config); err != nil {
			return fmt.Errorf("解析配置失败: %v", err)
		}
	}

	// 设置默认值
	if c.config.Image == "" {
		c.config.Image = "registry.baidubce.com/cce/nginx-alpine-go:latest"
	}
	if c.config.CPURequest == "" {
		c.config.CPURequest = "100m"
	}
	if c.config.MemoryRequest == "" {
		c.config.MemoryRequest = "128Mi"
	}
	if c.config.CPULimit == "" {
		c.config.CPULimit = "500m"
	}
	if c.config.MemoryLimit == "" {
		c.config.MemoryLimit = "512Mi"
	}
	if c.config.TimeoutMinutes <= 0 {
		c.config.TimeoutMinutes = 15 // ENI创建需要更长时间
	}
	if c.config.SafetyMargin <= 0 {
		c.config.SafetyMargin = 2 // 保留2个IP作为安全边距
	}

	logger.Infof(ctx, "新建ENI扩容测试配置: 镜像=%s, 超时时间=%d分钟, 安全边距=%d",
		c.config.Image, c.config.TimeoutMinutes, c.config.SafetyMargin)

	return nil
}

func (c *singleContainerScalingWithNewENI) Name() cases.CaseName {
	return SingleContainerScalingWithNewENICaseName
}

func (c *singleContainerScalingWithNewENI) Desc() string {
	return "测试新建网卡场景下的单容器扩容耗时（临时创建ENI）"
}

func (c *singleContainerScalingWithNewENI) Check(ctx context.Context) ([]cases.Resource, error) {
	logger.Infof(ctx, "开始执行新建网卡场景下的单容器扩容耗时测试")

	// 1. 选择目标节点
	selectedNode, err := c.selectTargetNode(ctx)
	if err != nil {
		return nil, fmt.Errorf("选择目标节点失败: %v", err)
	}
	c.selectedNode = selectedNode
	logger.Infof(ctx, "选择节点 %s 作为测试目标", selectedNode)

	// 2. 获取节点网络信息（基于真实的NRS数据）
	err = c.analyzeNodeNetworkInfo(ctx)
	if err != nil {
		return nil, fmt.Errorf("分析节点网络信息失败: %v", err)
	}

	// 3. 检查是否需要消耗IP来触发ENI创建
	if !c.networkInfo.NeedNewENI {
		logger.Infof(ctx, "当前节点已经需要创建新ENI，无需预消耗IP")
	} else {
		// 精确消耗IP，直到接近ENI创建临界点
		err = c.consumeIPsToTriggerENICreation(ctx)
		if err != nil {
			return nil, fmt.Errorf("消耗IP到ENI创建临界点失败: %v", err)
		}
	}

	// 4. 创建目标测试Deployment（副本数为0）
	deploymentName, err := c.createTestDeployment(ctx)
	if err != nil {
		return nil, fmt.Errorf("创建测试Deployment失败: %v", err)
	}
	c.deploymentName = deploymentName

	// 5. 记录扩容开始时间
	c.timestamps.ScalingStartTime = time.Now()
	logger.Infof(ctx, "扩容开始时间: %v (预期将触发ENI创建)", c.timestamps.ScalingStartTime.Format("2006-01-02 15:04:05.000"))

	// 6. 执行扩容操作（0 -> 1），触发ENI创建
	err = c.scaleDeployment(ctx, 1)
	if err != nil {
		return nil, fmt.Errorf("扩容Deployment失败: %v", err)
	}

	// 7. 监控ENI创建和Pod状态变化
	err = c.monitorENICreationAndPodLifecycle(ctx)
	if err != nil {
		return nil, fmt.Errorf("监控ENI创建和Pod生命周期失败: %v", err)
	}

	// 8. 计算并输出时间统计
	c.generateStatisticsReport(ctx)

	return c.resources, nil
}

// selectTargetNode 选择目标节点
func (c *singleContainerScalingWithNewENI) selectTargetNode(ctx context.Context) (string, error) {
	nodes, err := c.base.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return "", fmt.Errorf("获取节点列表失败: %v", err)
	}

	if len(nodes.Items) == 0 {
		return "", fmt.Errorf("集群中没有可用节点")
	}

	// 选择第一个Ready节点作为目标
	for _, node := range nodes.Items {
		if c.isNodeReady(&node) && !c.isNodeMaster(&node) {
			logger.Infof(ctx, "选择节点 %s 作为新建ENI测试目标", node.Name)
			return node.Name, nil
		}
	}

	return "", fmt.Errorf("没有找到可用的Ready节点")
}

// analyzeNodeNetworkInfo 分析节点网络信息
func (c *singleContainerScalingWithNewENI) analyzeNodeNetworkInfo(ctx context.Context) error {
	// 获取节点的NRS信息
	nrs, err := c.base.KubeClient.GetNrs(ctx, c.selectedNode, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取节点 %s 的NRS失败: %v", c.selectedNode, err)
	}
	c.networkInfo.NRS = nrs

	logger.Infof(ctx, "=== 节点 %s 网络资源分析 ===", c.selectedNode)
	logger.Infof(ctx, "NRS名称: %s", nrs.Name)
	logger.Infof(ctx, "实例ID: %s", nrs.Spec.InstanceId)
	logger.Infof(ctx, "每个ENI最大IP数: %d", nrs.Spec.Eni.MaxIPsPerENI)
	logger.Infof(ctx, "最大ENI数量: %d", nrs.Spec.Eni.MaxAllocateENI)

	c.networkInfo.MaxIPsPerENI = nrs.Spec.Eni.MaxIPsPerENI

	// 获取当前已使用的IP数量
	c.networkInfo.UsedIPs = len(nrs.Status.Ipam.Used)
	logger.Infof(ctx, "当前已使用IP数量: %d", c.networkInfo.UsedIPs)

	// 获取当前ENI列表
	eniList, err := c.base.KubeClient.ListENI(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取ENI列表失败: %v", err)
	}

	// 过滤出属于该节点的ENI
	var nodeENIs []types.ENI
	for _, eni := range eniList.Items {
		if eni.Spec.NodeName == c.selectedNode {
			nodeENIs = append(nodeENIs, eni)
		}
	}
	c.networkInfo.CurrentENIs = nodeENIs

	logger.Infof(ctx, "当前节点ENI数量: %d", len(nodeENIs))
	for i, eni := range nodeENIs {
		logger.Infof(ctx, "  ENI %d: %s (IP数量: %d)", i+1, eni.Name, len(eni.Spec.PrivateIPSet))
	}

	// 计算当前可用IP数量
	totalENIIPs := len(nodeENIs) * c.networkInfo.MaxIPsPerENI
	eniOccupiedIPs := len(nodeENIs) // 每个ENI自身占用1个IP
	c.networkInfo.AvailableIPs = totalENIIPs - eniOccupiedIPs - c.networkInfo.UsedIPs

	logger.Infof(ctx, "总ENI可用IP: %d", totalENIIPs)
	logger.Infof(ctx, "ENI自身占用IP: %d", eniOccupiedIPs)
	logger.Infof(ctx, "当前可用IP数量: %d", c.networkInfo.AvailableIPs)

	// 判断是否需要消耗IP来触发ENI创建
	// 如果可用IP数量 <= 安全边距，则当前已经会触发ENI创建
	if c.networkInfo.AvailableIPs <= c.config.SafetyMargin {
		c.networkInfo.NeedNewENI = false
		c.networkInfo.IPsToConsume = 0
		logger.Infof(ctx, "🎯 当前可用IP数量已经很少 (%d)，下次创建Pod将触发ENI创建", c.networkInfo.AvailableIPs)
	} else {
		c.networkInfo.NeedNewENI = true
		// 需要消耗的IP数量 = 当前可用IP - 安全边距 - 1（为测试Pod预留）
		c.networkInfo.IPsToConsume = c.networkInfo.AvailableIPs - c.config.SafetyMargin - 1
		logger.Infof(ctx, "🔧 需要消耗 %d 个IP来触发ENI创建", c.networkInfo.IPsToConsume)
	}

	logger.Infof(ctx, "===============================")
	return nil
}

// consumeIPsToTriggerENICreation 精确消耗IP到ENI创建临界点
func (c *singleContainerScalingWithNewENI) consumeIPsToTriggerENICreation(ctx context.Context) error {
	if c.networkInfo.IPsToConsume <= 0 {
		return nil
	}

	logger.Infof(ctx, "开始精确消耗 %d 个IP到ENI创建临界点", c.networkInfo.IPsToConsume)

	// 构建节点选择器
	nodeSelector := map[string]string{
		"kubernetes.io/hostname": c.selectedNode,
	}

	for i := 0; i < c.networkInfo.IPsToConsume; i++ {
		podName := fmt.Sprintf("ip-consumer-%d-%d", time.Now().Unix(), i)

		pod := &corev1.Pod{
			ObjectMeta: metav1.ObjectMeta{
				Name:      podName,
				Namespace: "default",
				Labels: map[string]string{
					"test":     "new-eni-scaling",
					"purpose":  "ip-consumer",
					"scenario": "new-eni",
				},
			},
			Spec: corev1.PodSpec{
				NodeSelector: nodeSelector,
				Containers: []corev1.Container{
					{
						Name:  "nginx",
						Image: c.config.Image,
						Resources: corev1.ResourceRequirements{
							Requests: corev1.ResourceList{
								corev1.ResourceCPU:    resource.MustParse("50m"),
								corev1.ResourceMemory: resource.MustParse("64Mi"),
							},
							Limits: corev1.ResourceList{
								corev1.ResourceCPU:    resource.MustParse("100m"),
								corev1.ResourceMemory: resource.MustParse("128Mi"),
							},
						},
					},
				},
				RestartPolicy: corev1.RestartPolicyAlways,
			},
		}

		createdPod, err := c.base.K8SClient.CoreV1().Pods("default").Create(ctx, pod, metav1.CreateOptions{})
		if err != nil {
			logger.Warnf(ctx, "创建IP消耗Pod %s 失败: %v", podName, err)
			continue
		}

		c.preExhaustPods = append(c.preExhaustPods, createdPod.Name)
		logger.Infof(ctx, "✅ 创建IP消耗Pod %s (%d/%d)", podName, i+1, c.networkInfo.IPsToConsume)

		// 添加到资源列表以便后续清理
		c.resources = append(c.resources, cases.Resource{
			CaseName: c.Name(),
			Type:     cases.ResourceTypeCCEInstance,
			ID:       fmt.Sprintf("default/%s", podName),
		})

		// 短暂等待，确保IP被分配
		time.Sleep(2 * time.Second)
	}

	// 等待所有Pod启动并分配IP
	logger.Infof(ctx, "等待所有IP消耗Pod启动并分配IP...")
	time.Sleep(20 * time.Second)

	// 验证IP消耗情况
	err := c.verifyIPConsumption(ctx)
	if err != nil {
		return fmt.Errorf("验证IP消耗情况失败: %v", err)
	}

	logger.Infof(ctx, "🎯 IP消耗完成，下一个Pod将触发ENI创建")
	return nil
}

// verifyIPConsumption 验证IP消耗情况
func (c *singleContainerScalingWithNewENI) verifyIPConsumption(ctx context.Context) error {
	// 重新获取NRS信息
	nrs, err := c.base.KubeClient.GetNrs(ctx, c.selectedNode, &kube.GetOptions{})
	if err != nil {
		return fmt.Errorf("重新获取NRS失败: %v", err)
	}

	newUsedIPs := len(nrs.Status.Ipam.Used)
	consumedIPs := newUsedIPs - c.networkInfo.UsedIPs

	logger.Infof(ctx, "=== IP消耗验证 ===")
	logger.Infof(ctx, "原始已使用IP: %d", c.networkInfo.UsedIPs)
	logger.Infof(ctx, "当前已使用IP: %d", newUsedIPs)
	logger.Infof(ctx, "实际消耗IP: %d", consumedIPs)
	logger.Infof(ctx, "预期消耗IP: %d", c.networkInfo.IPsToConsume)

	if consumedIPs != c.networkInfo.IPsToConsume {
		logger.Warnf(ctx, "实际消耗IP数量与预期不符，可能影响测试结果")
	}

	// 计算剩余可用IP
	remainingAvailableIPs := c.networkInfo.AvailableIPs - consumedIPs
	logger.Infof(ctx, "剩余可用IP: %d", remainingAvailableIPs)

	if remainingAvailableIPs <= c.config.SafetyMargin {
		logger.Infof(ctx, "✅ 成功达到ENI创建临界点")
	} else {
		logger.Warnf(ctx, "⚠️ 可能未达到ENI创建临界点，剩余IP仍较多")
	}

	logger.Infof(ctx, "==================")
	return nil
}

// createTestDeployment 创建测试用的Deployment
func (c *singleContainerScalingWithNewENI) createTestDeployment(ctx context.Context) (string, error) {
	deploymentName := fmt.Sprintf("test-new-eni-scaling-%d", time.Now().Unix())

	// 构建节点选择器
	nodeSelector := map[string]string{
		"kubernetes.io/hostname": c.selectedNode,
	}
	// 添加用户自定义的节点选择器
	for k, v := range c.config.NodeSelector {
		nodeSelector[k] = v
	}

	// 构建标签
	labels := map[string]string{
		"app":      "test-new-eni-scaling",
		"test":     "single-container-scaling",
		"scenario": "new-eni",
	}
	for k, v := range c.config.ExtraLabels {
		labels[k] = v
	}

	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      deploymentName,
			Namespace: "default",
			Labels:    labels,
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: func(i int32) *int32 { return &i }(0), // 初始副本数为0
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": "test-new-eni-scaling",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app":      "test-new-eni-scaling",
						"scenario": "new-eni",
					},
				},
				Spec: corev1.PodSpec{
					NodeSelector: nodeSelector,
					Containers: []corev1.Container{
						{
							Name:  "nginx",
							Image: c.config.Image,
							Ports: []corev1.ContainerPort{
								{
									ContainerPort: 80,
									Protocol:      corev1.ProtocolTCP,
								},
							},
							Resources: corev1.ResourceRequirements{
								Requests: corev1.ResourceList{
									corev1.ResourceCPU:    resource.MustParse(c.config.CPURequest),
									corev1.ResourceMemory: resource.MustParse(c.config.MemoryRequest),
								},
								Limits: corev1.ResourceList{
									corev1.ResourceCPU:    resource.MustParse(c.config.CPULimit),
									corev1.ResourceMemory: resource.MustParse(c.config.MemoryLimit),
								},
							},
						},
					},
					RestartPolicy: corev1.RestartPolicyAlways,
				},
			},
		},
	}

	createdDeployment, err := c.base.K8SClient.AppsV1().Deployments("default").Create(ctx, deployment, metav1.CreateOptions{})
	if err != nil {
		return "", fmt.Errorf("创建Deployment失败: %v", err)
	}

	logger.Infof(ctx, "成功创建Deployment %s，初始副本数: 0", deploymentName)

	// 将创建的Deployment添加到资源列表
	c.resources = append(c.resources, cases.Resource{
		CaseName: c.Name(),
		Type:     cases.ResourceTypeCCEInstance,
		ID:       fmt.Sprintf("default/%s", deploymentName),
	})

	return createdDeployment.Name, nil
}

// scaleDeployment 扩容Deployment
func (c *singleContainerScalingWithNewENI) scaleDeployment(ctx context.Context, replicas int) error {
	_, err := c.base.AppClient.ScaleAppResource(ctx, c.base.ClusterID, "deployment", "default", c.deploymentName, fmt.Sprintf("%d", replicas), nil)
	if err != nil {
		return fmt.Errorf("更新Deployment副本数失败: %v", err)
	}

	logger.Infof(ctx, "成功将Deployment %s 扩容到 %d 个副本", c.deploymentName, replicas)
	return nil
}

// monitorENICreationAndPodLifecycle 监控ENI创建和Pod生命周期
func (c *singleContainerScalingWithNewENI) monitorENICreationAndPodLifecycle(ctx context.Context) error {
	timeout := time.Duration(c.config.TimeoutMinutes) * time.Minute
	monitorCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	logger.Infof(ctx, "🔍 开始监控ENI创建和Pod生命周期，超时时间: %v", timeout)

	// 记录初始ENI数量
	initialENICount := len(c.networkInfo.CurrentENIs)
	logger.Infof(ctx, "初始ENI数量: %d", initialENICount)

	// 等待Pod被创建
	var targetPod *corev1.Pod
	err := wait.PollImmediate(1*time.Second, timeout, func() (bool, error) {
		pods, err := c.base.K8SClient.CoreV1().Pods("default").List(monitorCtx, metav1.ListOptions{
			LabelSelector: "app=test-new-eni-scaling",
		})
		if err != nil {
			logger.Warnf(monitorCtx, "获取Pod列表失败: %v", err)
			return false, nil
		}

		for _, pod := range pods.Items {
			if pod.Spec.NodeName == c.selectedNode {
				targetPod = &pod
				c.timestamps.PodCreatedTime = time.Now()
				logger.Infof(monitorCtx, "📦 Pod %s 已创建，调度到节点 %s", pod.Name, pod.Spec.NodeName)
				return true, nil
			}
		}
		return false, nil
	})

	if err != nil {
		return fmt.Errorf("等待Pod创建超时: %v", err)
	}

	// 监控ENI创建和Pod状态变化
	eniCreationDetected := false
	var newENIDetected bool
	return wait.PollImmediate(3*time.Second, timeout, func() (bool, error) {
		pod, err := c.base.K8SClient.CoreV1().Pods("default").Get(monitorCtx, targetPod.Name, metav1.GetOptions{})
		if err != nil {
			logger.Warnf(monitorCtx, "获取Pod状态失败: %v", err)
			return false, nil
		}

		// 检查是否有新ENI被创建
		if !newENIDetected {
			currentENIList, err := c.base.KubeClient.ListENI(ctx, &kube.ListOptions{})
			if err == nil {
				var currentNodeENIs []types.ENI
				for _, eni := range currentENIList.Items {
					if eni.Spec.NodeName == c.selectedNode {
						currentNodeENIs = append(currentNodeENIs, eni)
					}
				}

				if len(currentNodeENIs) > initialENICount {
					newENIDetected = true
					c.timestamps.ENICreationEnd = time.Now()
					logger.Infof(monitorCtx, "🔥 检测到新ENI创建完成！ENI数量: %d -> %d", initialENICount, len(currentNodeENIs))
				}
			}
		}

		// 监控Pod事件，检测ENI创建开始
		if !eniCreationDetected {
			events, err := c.base.K8SClient.CoreV1().Events("default").List(monitorCtx, metav1.ListOptions{
				FieldSelector: fmt.Sprintf("involvedObject.name=%s", pod.Name),
			})
			if err == nil {
				for _, event := range events.Items {
					if event.Type == "Warning" &&
						(event.Reason == "FailedCreatePodSandBox" ||
							event.Reason == "NetworkNotReady" ||
							event.Reason == "FailedMount") {
						if !eniCreationDetected {
							c.timestamps.ENICreationStart = event.FirstTimestamp.Time
							eniCreationDetected = true
							logger.Infof(monitorCtx, "🐌 检测到ENI创建开始: %s", event.Message)
						}
					}
				}
			}
		}

		// 检查IP分配
		if c.timestamps.IPAssignedTime.IsZero() && pod.Status.PodIP != "" {
			c.timestamps.IPAssignedTime = time.Now()
			if c.timestamps.ENICreationEnd.IsZero() {
				c.timestamps.ENICreationEnd = time.Now() // 备用ENI创建完成时间
			}
			logger.Infof(monitorCtx, "🌐 Pod %s IP已分配: %s 🐌 (通过新建ENI)", pod.Name, pod.Status.PodIP)
		}

		// 检查Pod Running状态
		if c.timestamps.PodRunningTime.IsZero() && pod.Status.Phase == corev1.PodRunning {
			c.timestamps.PodRunningTime = time.Now()
			logger.Infof(monitorCtx, "🏃 Pod %s 已进入Running状态", pod.Name)
		}

		// 检查Pod Ready状态
		if c.isPodReady(pod) {
			c.timestamps.PodReadyTime = time.Now()
			logger.Infof(monitorCtx, "✅ Pod %s 已就绪", pod.Name)
			return true, nil
		}

		logger.Infof(monitorCtx, "📊 Pod %s 状态: %s, IP: %s, Ready: %v",
			pod.Name, pod.Status.Phase, pod.Status.PodIP, c.isPodReady(pod))
		return false, nil
	})
}

// generateStatisticsReport 生成统计报告
func (c *singleContainerScalingWithNewENI) generateStatisticsReport(ctx context.Context) {
	// 计算各阶段耗时
	var creationDuration, eniCreationDuration, ipAssignDuration, startupDuration, readinessDuration, totalDuration time.Duration

	if !c.timestamps.PodCreatedTime.IsZero() {
		creationDuration = c.timestamps.PodCreatedTime.Sub(c.timestamps.ScalingStartTime)
	}

	if !c.timestamps.ENICreationStart.IsZero() && !c.timestamps.ENICreationEnd.IsZero() {
		eniCreationDuration = c.timestamps.ENICreationEnd.Sub(c.timestamps.ENICreationStart)
	}

	if !c.timestamps.IPAssignedTime.IsZero() {
		ipAssignDuration = c.timestamps.IPAssignedTime.Sub(c.timestamps.ScalingStartTime)
	}

	if !c.timestamps.PodRunningTime.IsZero() {
		startupDuration = c.timestamps.PodRunningTime.Sub(c.timestamps.ScalingStartTime)
	}

	if !c.timestamps.PodReadyTime.IsZero() {
		readinessDuration = c.timestamps.PodReadyTime.Sub(c.timestamps.PodRunningTime)
		totalDuration = c.timestamps.PodReadyTime.Sub(c.timestamps.ScalingStartTime)
	}

	// 输出详细统计报告
	logger.Infof(ctx, "=== 新建网卡场景单容器扩容时间统计 ===")
	logger.Infof(ctx, "扩容开始时间: %v", c.timestamps.ScalingStartTime.Format("2006-01-02 15:04:05.000"))
	if !c.timestamps.PodCreatedTime.IsZero() {
		logger.Infof(ctx, "Pod创建时间: %v", c.timestamps.PodCreatedTime.Format("2006-01-02 15:04:05.000"))
	}
	if !c.timestamps.ENICreationStart.IsZero() {
		logger.Infof(ctx, "ENI创建开始时间: %v", c.timestamps.ENICreationStart.Format("2006-01-02 15:04:05.000"))
	}
	if !c.timestamps.ENICreationEnd.IsZero() {
		logger.Infof(ctx, "ENI创建完成时间: %v", c.timestamps.ENICreationEnd.Format("2006-01-02 15:04:05.000"))
	}
	if !c.timestamps.IPAssignedTime.IsZero() {
		logger.Infof(ctx, "IP分配完成时间: %v 🐌 (新建ENI)", c.timestamps.IPAssignedTime.Format("2006-01-02 15:04:05.000"))
	}
	if !c.timestamps.PodRunningTime.IsZero() {
		logger.Infof(ctx, "Pod Running时间: %v", c.timestamps.PodRunningTime.Format("2006-01-02 15:04:05.000"))
	}
	if !c.timestamps.PodReadyTime.IsZero() {
		logger.Infof(ctx, "Pod Ready时间: %v", c.timestamps.PodReadyTime.Format("2006-01-02 15:04:05.000"))
	}

	logger.Infof(ctx, "--- 各阶段耗时分析 ---")
	if creationDuration > 0 {
		logger.Infof(ctx, "Pod创建耗时: %v (%.3f秒)", creationDuration, creationDuration.Seconds())
	}
	if eniCreationDuration > 0 {
		logger.Infof(ctx, "ENI创建耗时: %v (%.3f秒) 🐌 (新建ENI)", eniCreationDuration, eniCreationDuration.Seconds())
	}
	if ipAssignDuration > 0 {
		logger.Infof(ctx, "IP分配耗时: %v (%.3f秒) 🐌 (新建ENI)", ipAssignDuration, ipAssignDuration.Seconds())
	}
	if startupDuration > 0 {
		logger.Infof(ctx, "Pod启动耗时: %v (%.3f秒)", startupDuration, startupDuration.Seconds())
	}
	if readinessDuration > 0 {
		logger.Infof(ctx, "就绪检查耗时: %v (%.3f秒)", readinessDuration, readinessDuration.Seconds())
	}
	if totalDuration > 0 {
		logger.Infof(ctx, "总扩容耗时: %v (%.3f秒)", totalDuration, totalDuration.Seconds())
	}

	// 输出网络资源信息
	logger.Infof(ctx, "--- 网络资源信息 ---")
	logger.Infof(ctx, "测试节点: %s", c.selectedNode)
	logger.Infof(ctx, "每个ENI最大IP数: %d", c.networkInfo.MaxIPsPerENI)
	logger.Infof(ctx, "初始ENI数量: %d", len(c.networkInfo.CurrentENIs))
	logger.Infof(ctx, "IP消耗Pod数量: %d", len(c.preExhaustPods))
	logger.Infof(ctx, "消耗的IP数量: %d", c.networkInfo.IPsToConsume)
	logger.Infof(ctx, "=================================")
}

// 辅助函数
func (c *singleContainerScalingWithNewENI) isNodeReady(node *corev1.Node) bool {
	for _, condition := range node.Status.Conditions {
		if condition.Type == corev1.NodeNetworkUnavailable {
			return condition.Status == corev1.ConditionFalse
		}
	}
	return false
}

func (c *singleContainerScalingWithNewENI) isNodeMaster(node *corev1.Node) bool {
	_, hasMasterLabel := node.Labels["node-role.kubernetes.io/master"]
	_, hasControlPlaneLabel := node.Labels["node-role.kubernetes.io/control-plane"]
	return hasMasterLabel || hasControlPlaneLabel
}

func (c *singleContainerScalingWithNewENI) isPodReady(pod *corev1.Pod) bool {
	for _, condition := range pod.Status.Conditions {
		if condition.Type == corev1.PodReady {
			return condition.Status == corev1.ConditionTrue
		}
	}
	return false
}

// Clean 清理测试资源
func (c *singleContainerScalingWithNewENI) Clean(ctx context.Context) error {
	logger.Infof(ctx, "开始清理新建ENI扩容测试资源")

	var errors []error

	// 删除Deployment
	if c.deploymentName != "" {
		err := c.base.K8SClient.AppsV1().Deployments("default").Delete(ctx, c.deploymentName, metav1.DeleteOptions{})
		if err != nil {
			logger.Errorf(ctx, "删除Deployment %s 失败: %v", c.deploymentName, err)
			errors = append(errors, err)
		} else {
			logger.Infof(ctx, "成功删除Deployment %s", c.deploymentName)
		}
	}

	// 删除IP消耗Pod
	if len(c.preExhaustPods) > 0 {
		logger.Infof(ctx, "开始清理 %d 个IP消耗Pod", len(c.preExhaustPods))
		for _, podName := range c.preExhaustPods {
			err := c.base.K8SClient.CoreV1().Pods("default").Delete(ctx, podName, metav1.DeleteOptions{})
			if err != nil {
				logger.Warnf(ctx, "删除IP消耗Pod %s 失败: %v", podName, err)
			} else {
				logger.Infof(ctx, "成功删除IP消耗Pod %s", podName)
			}
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("清理过程中发生 %d 个错误", len(errors))
	}

	logger.Infof(ctx, "新建ENI扩容测试资源清理完成")
	return nil
}

func (c *singleContainerScalingWithNewENI) Continue(ctx context.Context) bool {
	return true
}

func (c *singleContainerScalingWithNewENI) ConfigFormat() string {
	return `{
  "image": "registry.baidubce.com/cce/nginx-alpine-go:latest",
  "cpuRequest": "100m",
  "memoryRequest": "128Mi", 
  "cpuLimit": "500m",
  "memoryLimit": "512Mi",
  "timeoutMinutes": 15,
  "nodeSelector": {},
  "extraLabels": {},
  "safetyMargin": 2
}`
}
