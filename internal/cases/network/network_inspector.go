// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/07/19 15:48:00, by <EMAIL>, create
*/
/*
cce-network-inspector 容器网络回归:
1. 使用 service/network/network-inspector;
2. 将 network-inspector 数据库中结果转存到指定 ConfigMap;
3. 通过 ConfigMap 来判断容器网络是否 Ready.
*/

package network

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/addon"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/network/network-inspector/probe"
	netresult "icode.baidu.com/baidu/cce-test/e2e-test/services/network/network-inspector/result"
)

const (
	// NetworkInspectorCaseName - case 名字
	NetworkInspectorCaseName cases.CaseName = "NetworkInspector"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), NetworkInspectorCaseName, NewNetworkInspector)
}

type networkInspector struct {
	base *cases.BaseClient
}

// NewNetworkInspector - 测试案例
func NewNetworkInspector(ctx context.Context) cases.Interface {
	return &networkInspector{}
}

func (c *networkInspector) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	c.base = base

	return nil
}

func (c *networkInspector) Name() cases.CaseName {
	return NetworkInspectorCaseName
}

func (c *networkInspector) Desc() string {
	return "cce-network-inspector 容器网络回归"
}

func (c *networkInspector) Check(ctx context.Context) ([]cases.Resource, error) {
	// 检查 CoreDNS 启动
	if err := resource.WaitPodsRunning(ctx, c.base.K8SClient, "kube-system", map[string]string{
		"k8s-app": "kube-dns",
	}); err != nil {
		logger.Errorf(ctx, "Wait CoreDNS PodsRunning failed: %s", err)
		return nil, err
	}

	// 部署 network-inspector
	if err := c.base.PluginClient.EnsurePluginDeployed(ctx, string(addon.AddonNetworkInspector), nil); err != nil {
		logger.Errorf(ctx, "Deploy cce-network-inspector failed: %s", err)
		return nil, err
	}

	// 通常需要等待 3 min 才能获取完整结果, 考虑容器启动时间
	logger.Infof(ctx, "Wait 3 mins to get network-inspector result")
	time.Sleep(120 * time.Second)

	for i := 0; i < 10; i++ {
		// 从用户集群 ConfigMap 获取结果
		configmap, err := c.base.K8SClient.CoreV1().ConfigMaps(netresult.DefaultNamespace).Get(ctx, netresult.DefaultConfigMapName, metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "Get configmap cce-network/cce-network-result failed: %s", err)
			return nil, err
		}

		logger.Infof(ctx, "Configmap: %s", utils.ToJSON(configmap))

		// 获取结果
		result, ok := configmap.Data[netresult.ResultKey]
		if !ok {
			logger.Warnf(ctx, "Congimap %s result is empty, retry", utils.ToJSON(result))
			time.Sleep(60 * time.Second)
			continue
		}

		logger.Infof(ctx, "network-inspector result: %s", result)

		var results []*netresult.Result
		if err := json.Unmarshal([]byte(result), &results); err != nil {
			logger.Warnf(ctx, "json.Unmarshal failed: %s", err)
			return nil, err
		}

		// 检查结果
		if err := c.checkNetworkInspectorResult(ctx, results); err != nil {
			logger.Warnf(ctx, "checkNetworkInspectorResult failed, retry: %s", err)
			time.Sleep(60 * time.Second)
			continue
		}

		return nil, nil
	}

	return nil, errors.New("checkNetworkInspectorResult failed")
}

func (c *networkInspector) Clean(ctx context.Context) error {
	// 删除cce-network的命名空间
	err := c.base.K8SClient.CoreV1().Namespaces().Delete(ctx, "cce-network", metav1.DeleteOptions{})
	if err != nil {
		return fmt.Errorf("Delete namespace cce-network failed: %v ", err.Error())
	}

	return nil
}

func (c *networkInspector) Continue(ctx context.Context) bool {
	return true
}

func (c *networkInspector) ConfigFormat() string {
	return ""
}

func (c *networkInspector) checkNetworkInspectorResult(ctx context.Context, results []*netresult.Result) error {
	if results == nil {
		return errors.New("results is nil")
	}

	for _, r := range results {
		// TODO: 后续再考虑处理公网场景
		if r.DestinationType == probe.DestinationTypeExternalURL {
			continue
		}

		if !r.IsSuccess {
			return fmt.Errorf("network-inspector result src=%s dest=%s not success", r.SourceType, r.DestinationType)
		}
	}

	return nil
}
