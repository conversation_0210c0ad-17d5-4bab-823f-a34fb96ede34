/* cni_v2_fixed_ip.go */
/*
modification history
--------------------
2023/6/28, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
场景：使用指定子网策略进行工作负载的ip分配校验(v2网络架构)，相关文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/Jk_2Ea1IJe/aiZAXYYI0j/mUM4T_bwZT995u
1、指定子网动态分配IP：指定子网，由系统自动从该子网中选择IP分配
2、指定多个子网动态分配IP：指定多个子网，由系统自动从该两个子网中选择IP进行分配
3、手动分配固定Pod IP：指定具体的IP段，而不是由系统动态分配
4、指定子网手动分配IP：指定具体的IP段，开启ipReuse
5、namespace级多子网动态IP分配策略：在场景1的情况下，不selector具体pod标签，则整个命名空间下生效
用例 测试流程：
1、创建psts
2、创建相关负载
3、校验负载IP分配情况
4、删除负载
5、校验IP回收情况
用例实现方法：
*由于校验的流程一致，5种场景只是pstt这个资源的配置不同，因此采用对该资源的模版渲染来实现
*用户指定子网以及测试场景的序号即可执行该用例，如
"config": {
	"fixed_ip_cases": [1, 2, 5],
	"extra_subnet_id": ["sbn-xxx1", "sbn-xx2"],
    "exclusive_subnet_id": "sbn-xxx3",
    "ipv4": [
        {
            "start": "172.22.18.xx",
            "end": "172.22.18.xx"
        }
    ]
}
*/

package network

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"text/template"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/tools/clientcmd"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	common "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// CNIV2FixedIPCaseName - case 名字
	CNIV2FixedIPCaseName cases.CaseName = "CNIV2FixedIP"

	WorkloadYaml string = `apiVersion: apps/v1
kind: WORKLOADTYPE
metadata:
  name: fixed-ip-workload
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: fixedIP
  template:
    metadata:
      labels:
        app: fixedIP
    spec:
      containers:
      - name: nginx
        image: hub.baidubce.com/cce/nginx-alpine-go:latest`

	// PSTS模版
	pstsTepl string = `apiVersion: cce.baidubce.com/v2
kind: PodSubnetTopologySpread
metadata:
    name: {{.Name}}
    namespace: default
spec:
    priority: 0
    subnets:
        {{- range $_, $v := $.SubnetID }}
        {{ $v }}: 
            {{- if $.IPV4 }}
            - family: "4"
              range:
              {{- range $_, $ip :=$.IPV4 }}
              - start: {{$ip.Start}}
                end: {{$ip.End}}
              {{- end }}
            {{- else}} []
            {{- end }}
            {{- if $.IPV6 }}
            - family: "6"
              range:
              {{- range $_, $ip := $.IPV6 }}
              - start: {{$ip.Start}}
                end: {{$ip.End}}
              {{- end }}
            {{- end }}
        {{- end }}
    strategy:
        type: {{$.StrategyType}}
        releaseStrategy: {{$.ReleaseStrategy}}
        {{- if $.EnableReuseIPAddress }}
        enableReuseIPAddress: {{$.EnableReuseIPAddress}}
        {{- end }}
    {{- if .MatchLabels }}
    selector:
        matchLabels:
            {{- range $k, $v := $.MatchLabels }}
            {{ $k }}: {{ $v }}
            {{- end }}
    {{- end }}
`
)

type cniV2FixedIP struct {
	base       *cases.BaseClient
	config     cniV2FixedIPConfig
	pstsClient dynamic.NamespaceableResourceInterface
	cepClient  dynamic.NamespaceableResourceInterface
}

type cniV2FixedIPConfig struct {
	FixedIPCases      []int           `json:"fixed_ip_cases"`
	ExtraSubnetID     []string        `json:"extra_subnet_id"`
	ExclusiveSubnetID string          `json:"exclusive_subnet_id"`
	IPV4              []CustomIPRange `json:"ipv4"`
	IPV6              []CustomIPRange `json:"ipv6"`
}

type CustomIPRange struct {
	Start string `json:"start"`
	End   string `json:"end"`
}

type V2SubnetIPStrategy struct {
	Description string

	// Name strategy name
	Name string

	SubnetID []string

	// StrategyType Elastic、Fixed、Manual
	StrategyType string

	// ReleaseStrategy TTL、Never
	ReleaseStrategy string

	// EnableReuseIPAddress
	EnableReuseIPAddress bool

	MatchLabels map[string]string
	IPV4        []CustomIPRange
	IPV6        []CustomIPRange
}

var v2FixIPCases []*V2SubnetIPStrategy

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CNIV2FixedIPCaseName, NewCNIV2FixedIP)
}

// NewCNIV2FixedIP - 测试案例
func NewCNIV2FixedIP(ctx context.Context) cases.Interface {
	return &cniV2FixedIP{}
}

func (c *cniV2FixedIP) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}

	logger.WithValues("case", "CNIV2FixedIP").Infof(ctx, "json raw config: %s", string(config))
	if err := json.Unmarshal(config, &c.config); err != nil {
		return err
	}
	logger.WithValues("case", "CNIV2FixedIP").Infof(ctx, "config: %s", utils.ToJSON(c.config))

	c.base = base
	pstsClient, err := NewPSTSClient(ctx, []byte(c.base.KubeConfig))
	if err != nil {
		return fmt.Errorf("new psts client failed: %v", err.Error())
	}
	c.pstsClient = pstsClient

	cepClient, err := NewCEPClient(ctx, []byte(c.base.KubeConfig))
	if err != nil {
		return fmt.Errorf("new cce endpoint client failed: %v", err.Error())
	}
	c.cepClient = cepClient

	// 5种场景套餐,其中套餐4和套餐5需要后续新建指定子网，并指定子网IP
	v2FixIPCases = []*V2SubnetIPStrategy{
		{
			Description: "1、指定子网动态分配IP", Name: "fix-ip-test1", SubnetID: c.config.ExtraSubnetID[:1],
			StrategyType: "Elastic", ReleaseStrategy: "TTL", MatchLabels: map[string]string{"app": "fixedIP"},
		},
		{
			Description: "2、指定多个子网动态分配IP", Name: "fix-ip-test2", SubnetID: c.config.ExtraSubnetID,
			StrategyType: "Elastic", ReleaseStrategy: "TTL", MatchLabels: map[string]string{"app": "fixedIP"},
		},
		{
			Description: "3、手动分配固定Pod IP", Name: "fix-ip-test3", SubnetID: []string{c.config.ExclusiveSubnetID},
			EnableReuseIPAddress: true, IPV4: c.config.IPV4, IPV6: c.config.IPV6, StrategyType: "Fixed",
			ReleaseStrategy: "Never", MatchLabels: map[string]string{"app": "fixedIP"},
		},
		{
			Description: "4、指定子网手动分配IP", Name: "fix-ip-test4", SubnetID: []string{c.config.ExclusiveSubnetID},
			EnableReuseIPAddress: true, IPV4: c.config.IPV4, IPV6: c.config.IPV6,
			StrategyType: "Elastic", ReleaseStrategy: "TTL", MatchLabels: map[string]string{"app": "fixedIP"},
		},
		{
			Description: "5、namespace级多子网动态IP分配策略", Name: "fix-ip-test5", SubnetID: c.config.ExtraSubnetID[:1],
			StrategyType: "Elastic", ReleaseStrategy: "TTL", IPV4: c.config.IPV4, IPV6: c.config.IPV6,
		},
	}
	return nil
}

func (c *cniV2FixedIP) Name() cases.CaseName {
	return CNIV2FixedIPCaseName
}

func (c *cniV2FixedIP) Desc() string {
	return "使用指定子网策略进行工作负载的ip分配校验(v2网络架构)"
}

func (c *cniV2FixedIP) Check(ctx context.Context) ([]cases.Resource, error) {
	var err error
	// 解析模版
	tmpl, err := template.New("v2FixedIPTest").Parse(pstsTepl)
	if err != nil {
		logger.Errorf(ctx, "parse template failed: %v", err.Error())
		return nil, fmt.Errorf("parse template failed: %v", err.Error())
	}

	for _, caseIndex := range c.config.FixedIPCases {
		logger.Infof(ctx, "%s case check start", v2FixIPCases[caseIndex-1].Description)

		var pstsYaml bytes.Buffer
		err = tmpl.Execute(&pstsYaml, v2FixIPCases[caseIndex-1])
		if err != nil {
			logger.Errorf(ctx, "template execute failed: %v", err.Error())
			return nil, fmt.Errorf("template execute failed: %v", err.Error())
		}

		// 部署策略，yaml通用部署
		if _, err := c.base.AppClient.PostAppResource(ctx, c.base.ClusterID,
			v2FixIPCases[caseIndex-1].Name, pstsYaml.String(), "default", nil); err != nil {
			logger.Errorf(ctx, "Deploy PSTS default/%s failed: %s", v2FixIPCases[caseIndex-1].Name, err)
			return nil, err
		}

		// 等待PSTS创建, https://console.cloud.baidu-int.com/devops/icafe/issue/CCE-12806/show?source=copy-shortcut
		time.Sleep(10 * time.Second)

		// 部署工作负载
		workloadYaml := WorkloadYaml
		workloadName := "fixed-ip-workload" + strconv.Itoa(caseIndex)
		// 判断部署deployment还是statefulSet
		if v2FixIPCases[caseIndex-1].EnableReuseIPAddress {
			workloadYaml = strings.ReplaceAll(workloadYaml, "WORKLOADTYPE", "StatefulSet")
		} else {
			workloadYaml = strings.ReplaceAll(workloadYaml, "WORKLOADTYPE", "Deployment")
		}
		workloadYaml = strings.ReplaceAll(workloadYaml, "fixed-ip-workload", workloadName)

		if _, err := c.base.AppClient.PostAppResource(ctx, c.base.ClusterID,
			workloadName, workloadYaml, "default", nil); err != nil {
			logger.Errorf(ctx, "Deploy workload for %s failed: %s", v2FixIPCases[caseIndex-1].Description, err)
			return nil, err
		}

		// 检验ip分配成功
		if v2FixIPCases[caseIndex-1].EnableReuseIPAddress {
			var statefulSet common.K8SStatefulSet
			if err = statefulSet.NewK8SStatefulSet(ctx, c.base, "default", workloadName); err != nil {
				return nil, err
			}
			statefulSet.SetWantedReplicas(1)
			if err := common.WaitForResourceReady(ctx, &statefulSet); err != nil {
				return nil, err
			}

			if err = c.base.K8SClient.AppsV1().StatefulSets("default").Delete(ctx, workloadName, metav1.DeleteOptions{}); err != nil {
				logger.Errorf(ctx, "delete statefulSet %s failed: %v", workloadName, err.Error())
				return nil, err
			}
		} else {
			var deployment common.K8SDeployment
			if err = deployment.NewK8SDeployment(ctx, c.base, "default", workloadName); err != nil {
				return nil, err
			}
			deployment.SetWantedReplicas(1)
			if err = common.WaitForResourceReady(ctx, &deployment); err != nil {
				return nil, err
			}
			pods, err := deployment.GetPods(ctx)
			if err != nil {
				logger.Errorf(ctx, "get pods of deployment failed: %v", err.Error())
				return nil, err
			}

			time.Sleep(5 * time.Second)

			if err = c.base.K8SClient.AppsV1().Deployments("default").Delete(ctx, workloadName, metav1.DeleteOptions{}); err != nil {
				logger.Errorf(ctx, "delete deployment %s failed: %v", workloadName, err.Error())
				return nil, err
			}

			// 校验cceEndpoint 也被清理掉了
			deleted, err := c.WaitCEPDeleted(ctx, "default", pods.Items[0].Name)
			if err != nil {
				logger.Errorf(ctx, "wait cce endpoint %s deleted failed: %v", pods.Items[0].Name, err.Error())
			}
			if !deleted {
				logger.Errorf(ctx, "cce endpoint % is not deleted", pods.Items[0].Name)
				return nil, err
			}
		}

		// 清理指定子网策略
		if err = c.pstsClient.Namespace("default").Delete(ctx, v2FixIPCases[caseIndex-1].Name, metav1.DeleteOptions{}); err != nil {
			logger.Errorf(ctx, "delete psts failed: %v", err.Error())
			return nil, err
		}
	}

	return nil, err
}

func (c *cniV2FixedIP) Clean(ctx context.Context) error {
	return nil
}

func (c *cniV2FixedIP) Continue(ctx context.Context) bool {
	return true
}

func (c *cniV2FixedIP) ConfigFormat() string {
	return ""
}

// NewPSTSClient 自定义资源psts的client
func NewPSTSClient(ctx context.Context, configBytes []byte) (dynamic.NamespaceableResourceInterface, error) {
	config, err := clientcmd.RESTConfigFromKubeConfig(configBytes)
	if err != nil {
		logger.Errorf(ctx, "RESTConfigFromKubeConfig failed: %s", err)
		return nil, err
	}

	// 获取 Dynamic 客户端
	dynamicClient := dynamic.NewForConfigOrDie(config)

	// 指定自定义资源的 Group 和 Version
	groupVersion := schema.GroupVersion{
		Group:   "cce.baidubce.com",
		Version: "v2",
	}

	// 获取自定义资源的 Dynamic 客户端
	return dynamicClient.Resource(
		schema.GroupVersionResource{
			Group:    groupVersion.Group,
			Version:  groupVersion.Version,
			Resource: "podsubnettopologyspreads",
		},
	), nil
}

// NewCEPClient 自定义资源cceEndpoint的client
func NewCEPClient(ctx context.Context, configBytes []byte) (dynamic.NamespaceableResourceInterface, error) {
	config, err := clientcmd.RESTConfigFromKubeConfig(configBytes)
	if err != nil {
		logger.Errorf(ctx, "RESTConfigFromKubeConfig failed: %s", err)
		return nil, err
	}

	// 获取 Dynamic 客户端
	dynamicClient := dynamic.NewForConfigOrDie(config)

	// 指定自定义资源的 Group 和 Version
	groupVersion := schema.GroupVersion{
		Group:   "cce.baidubce.com",
		Version: "v2",
	}

	// 获取自定义资源的 Dynamic 客户端
	return dynamicClient.Resource(
		schema.GroupVersionResource{
			Group:    groupVersion.Group,
			Version:  groupVersion.Version,
			Resource: "cceendpoints",
		},
	), nil
}

// WaitCEPDeleted 等待cep被删除
func (c *cniV2FixedIP) WaitCEPDeleted(ctx context.Context, namespace, name string) (bool, error) {
	timeoutCtx, cancelFn := context.WithTimeout(ctx, time.Minute*20)
	timeout := true

	wait.UntilWithContext(timeoutCtx, func(_ context.Context) {
		_, err := c.cepClient.Namespace(namespace).Get(ctx, name, metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "get cep %s failed: %v", name, err.Error())
			if strings.Contains(err.Error(), "not found") {
				cancelFn()
				timeout = false
			}
			return
		}
	}, time.Second*5)

	if timeout {
		err := fmt.Errorf("wait cep %s deleted timeout", name)
		logger.Errorf(ctx, "%v", err)
		return false, err
	}

	return true, nil
}
