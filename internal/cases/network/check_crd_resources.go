// Copyright 2024 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2024/04/09 19:00:00, by <EMAIL>, create
*/

/*
check-crd-resources 测试集群中自定义CRD资源:
1. 检查指定的CRD是否正确注册
2. 检查是否能从集群中获取CRD资源的对应CR
3. 检查本地能否正确读出CR资源并展示关键字段
*/

package network

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	// CheckCRDResourcesCaseName - case 名字
	CheckCRDResourcesCaseName cases.CaseName = "CheckCRDResources"
)

func init() {
	// 注册 CaseName
	cases.AddCase(context.TODO(), CheckCRDResourcesCaseName, NewCheckCRDResources)
}

type checkCRDResources struct {
	base *cases.BaseClient
}

// NewCheckCRDResources - 测试案例
func NewCheckCRDResources(ctx context.Context) cases.Interface {
	return &checkCRDResources{}
}

func (c *checkCRDResources) Init(ctx context.Context, config []byte,
	base *cases.BaseClient) error {
	if base == nil {
		return fmt.Errorf("base is nil")
	}

	c.base = base
	return nil
}

func (c *checkCRDResources) Name() cases.CaseName {
	return CheckCRDResourcesCaseName
}

func (c *checkCRDResources) Desc() string {
	return "测试集群中自定义CRD资源的注册与CR资源的获取"
}

func (c *checkCRDResources) Check(ctx context.Context) ([]cases.Resource, error) {
	// 检查所有网络相关CRD是否注册成功
	logger.Infof(ctx, "开始检查CRD注册情况...")

	// 测试 ENI CRD
	logger.Infof(ctx, "检查 ENI CRD...")
	if err := c.checkENICR(ctx); err != nil {
		logger.Warnf(ctx, "检查 ENI CR 失败: %v", err)
	}

	// 测试 NRS CRD
	logger.Infof(ctx, "检查 NetworkResourceSet CRD...")
	if err := c.checkNetworkResourceSetCR(ctx); err != nil {
		logger.Warnf(ctx, "检查 NetworkResourceSet CR 失败: %v", err)
	}

	// 测试 CCEEndpoint CRD
	logger.Infof(ctx, "检查 CCEEndpoint CRD...")
	if err := c.checkCCEEndpointCR(ctx); err != nil {
		logger.Warnf(ctx, "检查 CCEEndpoint CR 失败: %v", err)
	}

	// 测试 ClusterPodSubnetTopologySpread CRD
	logger.Infof(ctx, "检查 ClusterPodSubnetTopologySpread CRD...")
	if err := c.checkClusterPodSubnetTopologySpreadCR(ctx); err != nil {
		logger.Warnf(ctx, "检查 ClusterPodSubnetTopologySpread CR 失败: %v", err)
	}

	// 测试 NetResourceConfigSet CRD
	logger.Infof(ctx, "检查 NetResourceConfigSet CRD...")
	if err := c.checkNetResourceConfigSetCR(ctx); err != nil {
		logger.Warnf(ctx, "检查 NetResourceConfigSet CR 失败: %v", err)
	}

	// 测试 PodSubnetTopologySpread CRD
	logger.Infof(ctx, "检查 PodSubnetTopologySpread CRD...")
	if err := c.checkPodSubnetTopologySpreadCR(ctx); err != nil {
		logger.Warnf(ctx, "检查 PodSubnetTopologySpread CR 失败: %v", err)
	}

	// 测试 Subnet CRD
	logger.Infof(ctx, "检查 Subnet CRD...")
	if err := c.checkSubnetCR(ctx); err != nil {
		logger.Warnf(ctx, "检查 Subnet CR 失败: %v", err)
	}

	logger.Infof(ctx, "CRD资源检查完成")
	return nil, nil
}

func (c *checkCRDResources) Clean(ctx context.Context) error {
	// 不需要清理任何资源
	return nil
}

func (c *checkCRDResources) Continue(ctx context.Context) bool {
	return true
}

func (c *checkCRDResources) ConfigFormat() string {
	return ""
}

func (c *checkCRDResources) checkENICR(ctx context.Context) error {
	eniList, err := c.base.KubeClient.ListENI(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取ENI CR列表失败: %v", err)
	}

	logger.Infof(ctx, "发现 %d 个 ENI 资源", len(eniList.Items))
	for i, eni := range eniList.Items {
		if i >= 5 && len(eniList.Items) > 5 {
			logger.Infof(ctx, "还有 %d 个ENI资源未显示...", len(eniList.Items)-5)
			break
		}
		logger.Infof(ctx, "ENI: %s", eni.Name)
		logger.Infof(ctx, "  节点名称: %s", eni.Spec.NodeName)
		logger.Infof(ctx, "  MAC地址: %s", eni.Spec.MacAddress)
		logger.Infof(ctx, "  VPCID: %s", eni.Spec.VpcID)
		logger.Infof(ctx, "  子网ID: %s", eni.Spec.SubnetID)
		logger.Infof(ctx, "  CCE状态: %s", eni.Status.CCEStatus)
		logger.Infof(ctx, "  VPC状态: %s", eni.Status.VPCStatus)
		logger.Infof(ctx, "  接口名称: %s", eni.Status.InterfaceName)

		if len(eni.Spec.PrivateIPSet) > 0 {
			logger.Infof(ctx, "  私有IP数量: %d", len(eni.Spec.PrivateIPSet))
			for j, ip := range eni.Spec.PrivateIPSet {
				if j >= 3 && len(eni.Spec.PrivateIPSet) > 3 {
					logger.Infof(ctx, "    还有 %d 个私有IP未显示...", len(eni.Spec.PrivateIPSet)-3)
					break
				}
				logger.Infof(ctx, "    IP地址: %s (主IP: %v)", ip.PrivateIPAddress, ip.Primary)
			}
		}
		logger.Infof(ctx, "--------------------")
	}

	return nil
}

func (c *checkCRDResources) checkNetworkResourceSetCR(ctx context.Context) error {
	nrsList, err := c.base.KubeClient.ListNrs(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取NetworkResourceSet CR列表失败: %v", err)
	}

	logger.Infof(ctx, "发现 %d 个 NetworkResourceSet 资源", len(nrsList.Items))
	for i, nrs := range nrsList.Items {
		if i >= 5 && len(nrsList.Items) > 5 {
			logger.Infof(ctx, "还有 %d 个NetworkResourceSet资源未显示...", len(nrsList.Items)-5)
			break
		}
		logger.Infof(ctx, "NetworkResourceSet: %s", nrs.Name)
		logger.Infof(ctx, "  实例ID: %s", nrs.Spec.InstanceId)
		logger.Infof(ctx, "  ENI配置:")
		logger.Infof(ctx, "    最大IP数量: %d", nrs.Spec.Eni.MaxIPsPerENI)
		logger.Infof(ctx, "    子网ID列表: %v", nrs.Spec.Eni.SubnetIds)
		logger.Infof(ctx, "    安全组: %v", nrs.Spec.Eni.SecurityGroups)

		if len(nrs.Status.Enis) > 0 {
			logger.Infof(ctx, "  ENI状态:")
			count := 0
			for eniID, eniStatus := range nrs.Status.Enis {
				if count >= 3 && len(nrs.Status.Enis) > 3 {
					logger.Infof(ctx, "    还有 %d 个ENI状态未显示...", len(nrs.Status.Enis)-3)
					break
				}
				logger.Infof(ctx, "    ENI ID: %s", eniID)
				logger.Infof(ctx, "      子网ID: %s", eniStatus.SubnetId)
				logger.Infof(ctx, "      CCE状态: %s", eniStatus.CceStatus)
				logger.Infof(ctx, "      VPC状态: %s", eniStatus.VpcStatus)
				count++
			}
		}
		logger.Infof(ctx, "--------------------")
	}

	return nil
}

func (c *checkCRDResources) checkCCEEndpointCR(ctx context.Context) error {
	// 先在kube-system命名空间查找
	endpointList, err := c.base.KubeClient.ListCep(ctx, "kube-system", &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取kube-system命名空间中CCEEndpoint CR列表失败: %v", err)
	}

	totalCount := len(endpointList.Items)
	logger.Infof(ctx, "在kube-system命名空间中发现 %d 个 CCEEndpoint 资源", totalCount)

	// 如果kube-system命名空间没有，尝试在default命名空间查找
	if totalCount == 0 {
		endpointList, err = c.base.KubeClient.ListCep(ctx, "default", &kube.ListOptions{})
		if err != nil {
			return fmt.Errorf("获取default命名空间中CCEEndpoint CR列表失败: %v", err)
		}
		totalCount = len(endpointList.Items)
		logger.Infof(ctx, "在default命名空间中发现 %d 个 CCEEndpoint 资源", totalCount)
	}

	for i, endpoint := range endpointList.Items {
		if i >= 5 && totalCount > 5 {
			logger.Infof(ctx, "还有 %d 个CCEEndpoint资源未显示...", totalCount-5)
			break
		}
		logger.Infof(ctx, "CCEEndpoint: %s (命名空间: %s)", endpoint.Name, endpoint.Namespace)
		logger.Infof(ctx, "  Pod名称: %s", endpoint.Spec.ExternalIdentifiers.K8SPodName)
		logger.Infof(ctx, "  命名空间: %s", endpoint.Spec.ExternalIdentifiers.K8SNamespace)
		logger.Infof(ctx, "  容器ID: %s", endpoint.Spec.ExternalIdentifiers.ContainerId)
		logger.Infof(ctx, "  分配节点: %s", endpoint.Spec.Network.IpAllocation.Node)
		logger.Infof(ctx, "  分配类型: %s", endpoint.Spec.Network.IpAllocation.Type)

		if endpoint.Status.State != "" {
			logger.Infof(ctx, "  状态: %s", endpoint.Status.State)
		}

		if len(endpoint.Status.Networking.Addressing) > 0 {
			logger.Infof(ctx, "  网络地址:")
			for j, addr := range endpoint.Status.Networking.Addressing {
				if j >= 3 && len(endpoint.Status.Networking.Addressing) > 3 {
					logger.Infof(ctx, "    还有 %d 个地址未显示...", len(endpoint.Status.Networking.Addressing)-3)
					break
				}
				logger.Infof(ctx, "    类型: %s, IP: %s", addr.Family, addr.IP)
				if addr.Interface != "" {
					logger.Infof(ctx, "    接口: %s", addr.Interface)
				}
			}
		}
		logger.Infof(ctx, "--------------------")
	}

	return nil
}

func (c *checkCRDResources) checkClusterPodSubnetTopologySpreadCR(ctx context.Context) error {
	cpstsList, err := c.base.KubeClient.ListCpsts(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取ClusterPodSubnetTopologySpread CR列表失败: %v", err)
	}

	logger.Infof(ctx, "发现 %d 个 ClusterPodSubnetTopologySpread 资源", len(cpstsList.Items))
	for i, cpsts := range cpstsList.Items {
		if i >= 5 && len(cpstsList.Items) > 5 {
			logger.Infof(ctx, "还有 %d 个ClusterPodSubnetTopologySpread资源未显示...", len(cpstsList.Items)-5)
			break
		}
		logger.Infof(ctx, "ClusterPodSubnetTopologySpread: %s", cpsts.Name)
		logger.Infof(ctx, "  最大偏差: %d", cpsts.Spec.MaxSkew)
		logger.Infof(ctx, "  多区域拓扑分布类型: %s", cpsts.Spec.WhenUnsatisfiable)
		if cpsts.Spec.NamespaceSelector != nil {
			logger.Infof(ctx, "  命名空间选择器: %v", cpsts.Spec.NamespaceSelector.MatchLabels)
		}
		logger.Infof(ctx, "--------------------")
	}

	return nil
}

func (c *checkCRDResources) checkNetResourceConfigSetCR(ctx context.Context) error {
	nrcsList, err := c.base.KubeClient.ListNrcs(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取NetResourceConfigSet CR列表失败: %v", err)
	}

	logger.Infof(ctx, "发现 %d 个 NetResourceConfigSet 资源", len(nrcsList.Items))
	for i, nrcs := range nrcsList.Items {
		if i >= 5 && len(nrcsList.Items) > 5 {
			logger.Infof(ctx, "还有 %d 个NetResourceConfigSet资源未显示...", len(nrcsList.Items)-5)
			break
		}
		logger.Infof(ctx, "NetResourceConfigSet: %s", nrcs.Name)
		logger.Infof(ctx, "  优先级: %d", nrcs.Spec.Priority)

		// Agent配置
		logger.Infof(ctx, "  Agent配置:")
		if len(nrcs.Spec.AgentConfig.EniSubnetIDs) > 0 {
			logger.Infof(ctx, "    ENI子网ID: %v", nrcs.Spec.AgentConfig.EniSubnetIDs)
		}
		if len(nrcs.Spec.AgentConfig.EniSecurityGroupIds) > 0 {
			logger.Infof(ctx, "    ENI安全组: %v", nrcs.Spec.AgentConfig.EniSecurityGroupIds)
		}
		if nrcs.Spec.AgentConfig.EniUseMode != nil {
			logger.Infof(ctx, "    ENI使用模式: %s", *nrcs.Spec.AgentConfig.EniUseMode)
		}

		// 状态信息
		if len(nrcs.Status.AgentVersion) > 0 {
			logger.Infof(ctx, "  节点代理版本数: %d", len(nrcs.Status.AgentVersion))
		}
		logger.Infof(ctx, "  节点数量: %d", nrcs.Status.NodeCount)

		logger.Infof(ctx, "--------------------")
	}

	return nil
}

func (c *checkCRDResources) checkPodSubnetTopologySpreadCR(ctx context.Context) error {
	pstsList, err := c.base.KubeClient.ListPsts(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取PodSubnetTopologySpread CR列表失败: %v", err)
	}

	logger.Infof(ctx, "发现 %d 个 PodSubnetTopologySpread 资源", len(pstsList.Items))
	for i, psts := range pstsList.Items {
		if i >= 5 && len(pstsList.Items) > 5 {
			logger.Infof(ctx, "还有 %d 个PodSubnetTopologySpread资源未显示...", len(pstsList.Items)-5)
			break
		}
		logger.Infof(ctx, "PodSubnetTopologySpread: %s (命名空间: %s)", psts.Name, psts.Namespace)
		logger.Infof(ctx, "  名称: %s", psts.Spec.Name)
		logger.Infof(ctx, "  最大偏差: %d", psts.Spec.MaxSkew)
		logger.Infof(ctx, "  多区域拓扑分布类型: %s", psts.Spec.WhenUnsatisfiable)

		if psts.Spec.Selector != nil && len(psts.Spec.Selector.MatchLabels) > 0 {
			logger.Infof(ctx, "  标签选择器:")
			for k, v := range psts.Spec.Selector.MatchLabels {
				logger.Infof(ctx, "    %s: %s", k, v)
			}
		}

		if len(psts.Status.AvailableSubnets) > 0 {
			logger.Infof(ctx, "  可用子网数: %d", len(psts.Status.AvailableSubnets))
		}
		logger.Infof(ctx, "  Pod匹配数: %d", psts.Status.PodMatchedCount)
		logger.Infof(ctx, "  Pod影响数: %d", psts.Status.PodAffectedCount)
		logger.Infof(ctx, "--------------------")
	}

	return nil
}

func (c *checkCRDResources) checkSubnetCR(ctx context.Context) error {
	subnetList, err := c.base.KubeClient.ListSubnet(ctx, &kube.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取Subnet CR列表失败: %v", err)
	}

	logger.Infof(ctx, "发现 %d 个 Subnet 资源", len(subnetList.Items))
	for i, subnet := range subnetList.Items {
		if i >= 5 && len(subnetList.Items) > 5 {
			logger.Infof(ctx, "还有 %d 个Subnet资源未显示...", len(subnetList.Items)-5)
			break
		}
		logger.Infof(ctx, "Subnet: %s", subnet.Name)
		logger.Infof(ctx, "  CIDR: %s", subnet.Spec.CIDR)
		logger.Infof(ctx, "  可用区: %s", subnet.Spec.AvailabilityZone)
		logger.Infof(ctx, "  VPCID: %s", subnet.Spec.VPCID)
		logger.Infof(ctx, "  子网ID: %s", subnet.Spec.ID)
		if subnet.Spec.Name != "" {
			logger.Infof(ctx, "  子网名称: %s", subnet.Spec.Name)
		}
		if subnet.Status.AvailableIPNum > 0 {
			logger.Infof(ctx, "  可用IP数量: %d", subnet.Status.AvailableIPNum)
		}
		logger.Infof(ctx, "--------------------")
	}

	return nil
}
