// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/09 20:28:00, by <EMAIL>, create
*/
/*
CheckAccountAndRepo, 检查Helm账号和仓库
*/

package helm

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	CheckAccountAndRepo cases.CaseName = "CheckAccountAndRepo"
)

func init() {
	cases.AddCase(context.TODO(), CheckAccountAndRepo, NewCheckAccountAndRepo)
}

var _ cases.Interface = &checkAccountAndRepo{}

type accountInfo struct {
	UserName string `json:"userName"`
	RepoUrl  string `json:"repoUrl"`
}

type checkAccountAndRepo struct {
	base   *cases.BaseClient
	config accountInfo
}

// NewCheckAccountAndRepo - 检查Helm账号和仓库
func NewCheckAccountAndRepo(ctx context.Context) cases.Interface {
	return &checkAccountAndRepo{}
}

func (c *checkAccountAndRepo) Init(ctx context.Context, config []byte, base *cases.BaseClient) error {
	if base == nil {
		return errors.New("base is nil")
	}
	var addConfig accountInfo
	if err := json.Unmarshal(config, &addConfig); err != nil {
		return fmt.Errorf("json.Unmarshal accountInfo failed: %s", err)
	}

	c.base = base
	c.config = addConfig
	return nil
}

func (c *checkAccountAndRepo) Name() cases.CaseName {
	return CheckAccountAndRepo
}

func (c *checkAccountAndRepo) Desc() string {
	return "检查账号和repo"
}

func (c *checkAccountAndRepo) Check(ctx context.Context) ([]cases.Resource, error) {
	resources := make([]cases.Resource, 0)

	// 检查不存在的用户是否不存在
	isExist, err := c.base.HelmAccountClient.CheckUserNameExist(ctx, "notexist")
	if err != nil {
		logger.Errorf(ctx, "CheckUserNameExist failed: %s", err)
		return resources, err
	}
	if isExist {
		logger.Errorf(ctx, "CheckUserNameExist user 'notexist' exist")
		return resources, err
	}

	// 检查存在的用户是否存在
	isExist, err = c.base.HelmAccountClient.CheckUserNameExist(ctx, c.config.UserName)
	if err != nil {
		logger.Errorf(ctx, "CheckUserNameExist failed: %s", err)
		return resources, err
	}
	if !isExist {
		logger.Errorf(ctx, "CheckUserNameExist user '%s' not exist", c.config.UserName)
		return resources, err
	}

	// 检查repo地址是否正确
	repoUrl, err := c.base.HelmAccountClient.GetPrivateRepoURL(ctx)
	if err != nil {
		logger.Errorf(ctx, "GetPrivateRepoURL failed: %s", err)
		return resources, err
	}
	if repoUrl != c.config.RepoUrl {
		logger.Errorf(ctx, "expectedUrl %s and repoUrl %s are not the same", c.config.RepoUrl, repoUrl)
		return resources, fmt.Errorf("expectedUrl %s and repoUrl %s are not the same", c.config.RepoUrl, repoUrl)
	}
	logger.Infof(ctx, "expectedUrl %s and repoUrl %s are the same.", c.config.RepoUrl, repoUrl)
	return resources, nil
}

func (c *checkAccountAndRepo) Clean(ctx context.Context) error {
	// 不需要回收集群, 已在外部设置集群的清理 DeleteCluster: true
	return nil
}

func (c *checkAccountAndRepo) Continue(ctx context.Context) bool {
	return false
}

func (c *checkAccountAndRepo) ConfigFormat() string {
	return ""
}
