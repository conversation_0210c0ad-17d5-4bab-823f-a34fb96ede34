package resource

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/tools/remotecommand"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

type K8SPod struct {
	baseParams  BaseParams
	namespace   string
	matchLabels map[string]string
	status      string
}

func (k *K8SPod) NewK8SPod(ctx context.Context, baseClient *cases.BaseClient, namespace string, matchLabels map[string]string) error {
	if err := Init(k, baseClient); err != nil {
		logger.Errorf(ctx, "NewK8SPod failed: %s", err)
		return err
	}
	k.baseParams.message = fmt.Sprintf("Timeout to wait for K8S_Pod %s/%s ready", namespace, utils.ToJSON(matchLabels))
	k.namespace = namespace
	k.matchLabels = matchLabels
	return nil
}

func (k *K8SPod) GetBaseParams() *BaseParams {
	return &k.baseParams
}

func (k *K8SPod) SetStatus(status string) {
	k.status = status
	k.baseParams.message = fmt.Sprintf("Timeout to wait for K8S_Pod %s/%s status reach %s", k.namespace, utils.ToJSON(k.matchLabels), status)
}

func (k *K8SPod) SetNamespace(namespace string) {
	k.namespace = namespace
}

func (k *K8SPod) SetMatchLabel(key string, value string) {
	k.matchLabels = make(map[string]string)
	k.matchLabels[key] = value
}

func (k *K8SPod) CheckResource(ctx context.Context) error {
	statuses, err := k.GetPodStatus(ctx)
	if err != nil {
		return err
	}
	for podName, status := range statuses {
		if status != k.status {
			if status == StatusPending {
				events, err := k.baseParams.baseClient.K8SClient.CoreV1().Events(k.namespace).List(ctx, metav1.ListOptions{
					FieldSelector: fmt.Sprintf("involvedObject.name=%s", podName),
				})
				if err != nil {
					return fmt.Errorf("Failed to get events: %v\n", err)
				}
				for _, event := range events.Items {
					logger.Errorf(ctx, "Event: %v %v\n", event.Reason, event.Message)
				}
			}
			return fmt.Errorf("pod %s status: %s not as expected: %s", podName, status, k.status)
		}
	}
	return nil
}

func (k *K8SPod) GetPodName() string {
	pods, _ := k.GetPodList(context.TODO())
	return pods.Items[0].Name
}

func (k *K8SPod) GetPodNameList() []string {
	pods, _ := k.GetPodList(context.TODO())
	nameList := make([]string, len(pods.Items))
	for i, pod := range pods.Items {
		nameList[i] = pod.Name
	}
	return nameList
}

func (k *K8SPod) GetPodList(ctx context.Context) (*v1.PodList, error) {
	// 构建 Label
	labelSelector := metav1.LabelSelector{
		MatchLabels: k.matchLabels,
	}
	labelSelectorStr := metav1.FormatLabelSelector(&labelSelector)

	pods, err := k.baseParams.baseClient.K8SClient.CoreV1().Pods(k.namespace).List(ctx, metav1.ListOptions{
		LabelSelector: labelSelectorStr,
	})
	if err != nil {
		logger.Errorf(ctx, "List Pods failed: %s", err)
		return nil, err
	}
	return pods, nil
}

func (k *K8SPod) GetPodNodeList(ctx context.Context) (map[string]bool, error) {
	pods, err := k.GetPodList(ctx)
	if err != nil {
		return nil, err
	}

	nodeList := make(map[string]bool)
	for _, pod := range pods.Items {
		nodeList[pod.Status.HostIP] = true
	}
	return nodeList, nil
}

func (k *K8SPod) GetPodNodeNameList(ctx context.Context) (map[string]bool, error) {
	pods, err := k.GetPodList(ctx)
	if err != nil {
		return nil, err
	}

	nodeNameList := make(map[string]bool)
	for _, pod := range pods.Items {
		nodeNameList[pod.Spec.NodeName] = true
	}
	return nodeNameList, nil
}

func (k *K8SPod) GetPodStatus(ctx context.Context) (map[string]string, error) {
	pods, err := k.GetPodList(ctx)
	if err != nil {
		return nil, err
	}
	if len(pods.Items) == 0 {
		return nil, errors.New("Pods.Items is empty")
	}
	statuses := make(map[string]string, len(pods.Items))
	for _, pod := range pods.Items {
		statuses[pod.Name] = string(pod.Status.Phase)
	}
	return statuses, nil
}

func (k *K8SPod) GetPodLogs(ctx context.Context, podName string, options *v1.PodLogOptions) (string, error) {
	req := k.baseParams.baseClient.K8SClient.CoreV1().Pods(k.namespace).GetLogs(podName, options)
	podLogs, err := req.Stream(ctx)
	if err != nil {
		return "", err
	}
	defer func(podLogs io.ReadCloser) {
		err := podLogs.Close()
		if err != nil {
		}

	}(podLogs)
	buf := new(bytes.Buffer)
	_, err = io.Copy(buf, podLogs)
	if err != nil {
		return "", err
	}
	return buf.String(), nil
}

func (k *K8SPod) ExecInPod(ctx context.Context, podName string, namespace string, command []string) (string, error) {
	config, err := clientcmd.RESTConfigFromKubeConfig([]byte(k.baseParams.baseClient.KubeConfig))
	if err != nil {
		logger.Errorf(ctx, "new config of cluster with error: %v", err)
		return "", err
	}

	req := k.baseParams.baseClient.K8SClient.CoreV1().RESTClient().Post().Resource("pods").
		Name(podName).Namespace(namespace).SubResource("exec").
		VersionedParams(&v1.PodExecOptions{
			Command: command,
			Stdin:   false,
			Stdout:  true,
			Stderr:  true,
			TTY:     false,
		}, scheme.ParameterCodec)
	exec, err := remotecommand.NewSPDYExecutor(config, "POST", req.URL())
	if err != nil {
		logger.Errorf(ctx, "NewSPDYExecutor with error: %v", err)
		return "", err
	}

	var stdout, stderr bytes.Buffer
	err = exec.Stream(remotecommand.StreamOptions{
		Stdin:  nil,
		Stdout: &stdout,
		Stderr: &stderr,
		Tty:    false,
	})
	if err != nil {
		logger.Errorf(ctx, "%v, stderr: %s", err, stderr)
		return "", fmt.Errorf("%v, stderr: %s", err, stderr)
	}
	return stdout.String(), nil
}
