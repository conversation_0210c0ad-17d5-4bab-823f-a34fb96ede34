// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/03 14:28:00, by <EMAIL>, create
*/

package resource

import (
	"context"
	"fmt"
	"strings"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

type K8SDeployment struct {
	baseParams     BaseParams
	namespace      string
	name           string
	wantedReplicas int
}

func (k *K8SDeployment) NewK8SDeployment(ctx context.Context, baseClient *cases.BaseClient, namespace string, name string) error {
	if err := Init(k, baseClient); err != nil {
		logger.Errorf(ctx, "NewK8SDeployment failed: %s", err)
		return err
	}
	k.baseParams.message = fmt.Sprintf("Timeout to wait for K8S_Deployment %s/%s ready", namespace, name)
	k.namespace = namespace
	k.name = name
	k.wantedReplicas = -1
	return nil
}

// SetWantedReplicas  期望负载数量设置
func (k *K8SDeployment) SetWantedReplicas(replicas int) {
	k.wantedReplicas = replicas
}

func (k *K8SDeployment) GetBaseParams() *BaseParams {
	return &k.baseParams
}

func (k *K8SDeployment) CheckResource(ctx context.Context) error {
	deployment, err := k.baseParams.baseClient.K8SClient.AppsV1().Deployments(k.namespace).Get(ctx, k.name, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "Get deployment %s/%s failed: %s", k.namespace, k.name, err)
		return err
	}

	var wantedReplicas int32
	if k.wantedReplicas > 0 {
		wantedReplicas = int32(k.wantedReplicas)
	} else {
		wantedReplicas = *deployment.Spec.Replicas
	}

	if wantedReplicas != deployment.Status.ReadyReplicas {
		return fmt.Errorf("deployment %s/%s replicas is %d/%d", k.namespace, k.name, deployment.Status.ReadyReplicas, wantedReplicas)
	}
	return nil
}

func (k *K8SDeployment) GetPods(ctx context.Context) (*corev1.PodList, error) {
	// Get the Deployment.
	deploy, err := k.baseParams.baseClient.K8SClient.AppsV1().Deployments(k.namespace).Get(ctx, k.name, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "Get deployment %s/%s failed: %s", k.namespace, k.name, err)
		return nil, err
	}

	// Create a label selector from the Deployment's selector.
	ls := &metav1.LabelSelector{
		MatchLabels: deploy.Spec.Selector.MatchLabels,
	}
	selector, err := metav1.LabelSelectorAsSelector(ls)
	if err != nil {
		logger.Errorf(ctx, "Failed to convert selector:", err)
		return nil, err
	}

	// List Pods using the label selector.
	pods, err := k.baseParams.baseClient.K8SClient.CoreV1().Pods(k.namespace).List(ctx, metav1.ListOptions{
		LabelSelector: selector.String(),
	})
	if err != nil {
		logger.Errorf(ctx, "Failed to list pods:", err)
		return nil, err
	}

	// check the pod owner (maybe some pod use the same label)
	items := make([]corev1.Pod, 0, len(pods.Items))
	for _, pod := range pods.Items {
		for _, ownerRef := range pod.OwnerReferences {
			if ownerRef.Kind == "ReplicaSet" && strings.Contains(ownerRef.Name, k.name) {
				items = append(items, pod)
				break
			}
		}
	}
	pods.Items = items

	return pods, err
}

// 创建一个利用副本数和节点反亲和来强制触发扩容的Deployment，适用CA方面,因此命名 CAdeployent
// 加了污点
func CreateCADeployment(ctx context.Context, c *cases.BaseClient, replicas int32, deploymentName string) (err error) {
	logger.Infof(ctx, "crete deployment with taint ")
	var terminationGracePeriodSeconds int64
	var deployTaintKey = deploymentName + "key"
	var deployTaintValue = deploymentName + "value"

	deployment := appsv1.Deployment{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Deployment",
			APIVersion: "apps/v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      deploymentName,
			Namespace: metav1.NamespaceDefault,
			Labels: map[string]string{
				"app": deploymentName,
			},
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": deploymentName,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": deploymentName,
					},
				},
				Spec: corev1.PodSpec{
					Tolerations: []corev1.Toleration{
						{
							Key:      deployTaintKey,
							Operator: corev1.TolerationOpEqual,
							Value:    deployTaintValue,
							Effect:   corev1.TaintEffectNoSchedule,
						},
					},
					TerminationGracePeriodSeconds: &terminationGracePeriodSeconds,
					Affinity: &corev1.Affinity{
						PodAntiAffinity: &corev1.PodAntiAffinity{
							RequiredDuringSchedulingIgnoredDuringExecution: []corev1.PodAffinityTerm{{
								LabelSelector: &metav1.LabelSelector{
									MatchExpressions: []metav1.LabelSelectorRequirement{{
										Key:      "app",
										Operator: metav1.LabelSelectorOpIn,
										Values:   []string{deploymentName},
									}},
								},
								TopologyKey: "kubernetes.io/hostname", // 利用副本数和节点反亲和来强制触发扩容
							}},
						},
					},
					Containers: []corev1.Container{{
						Name:            deploymentName,
						Image:           "registry.baidubce.com/cce-public/busybox",
						ImagePullPolicy: corev1.PullIfNotPresent,
						Command:         []string{"sleep", "3600"},
					}},
				},
			},
		},
	}
	_, err = c.KubeClient.CreateDeploymentAppsV1(ctx, metav1.NamespaceDefault, &deployment)
	if err != nil {
		err = fmt.Errorf("CreateDeploymentAppsV1 failed, %s", err.Error())
		return
	}
	return
}
