// Copyright 2022 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2022/11/07 , by z<PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
check cross vpc eni status
*/

package resource

import (
	"context"
	"fmt"
	"time"

	"k8s.io/client-go/rest"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	cceiov1alpha1 "icode.baidu.com/baidu/cce-test/e2e-test/services/network/eni-resource-controller/api/v1alpha1"
)

type CrossVpcEni struct {
	restClient rest.Interface
	namespace  string
}

const (
	CrossVpcENIOwnerName     = "cce.io/ownerName"
	CrossVpcENIOwnerInstance = "cce.io/ownerInstance"
)

func NewCrossVpcEni(ctx context.Context, restClient rest.Interface, namespace string) (*CrossVpcEni, error) {
	return &CrossVpcEni{
		restClient: restClient,
		namespace:  namespace,
	}, nil
}

func (c *CrossVpcEni) WaitCrossVpcEniInUsed(ctx context.Context, podName string) error {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()
	timer := time.NewTimer(10 * time.Minute)
	defer timer.Stop()
	for {
		select {
		case <-ticker.C:
			status, _, err := c.GetCrossVpcEniStatusByPodName(ctx, podName)
			if err != nil {
				logger.Errorf(ctx, "failed to get CrossVPCEni of %s/%s: %v", c.namespace, podName, err)
				return err
			}
			if status == cceiov1alpha1.EniStatusInuse {
				logger.Infof(ctx, "the crossVpcEni of pod %s is %s as excepted", podName, status)
				return nil
			}
			logger.Warnf(ctx, "the crossVpcEni is not %s as excepted, the status is %s now, wait",
				cceiov1alpha1.EniStatusInuse, status)

		case <-timer.C:
			logger.Errorf(ctx, "timeout waiting for crossVpcEni of %s inuse", podName)
			return fmt.Errorf("timeout waiting for crossVpcEni of %s inuse", podName)
		}
	}
}

func (c *CrossVpcEni) GetCrossVpcEni(ctx context.Context, name string) (*cceiov1alpha1.CrossVPCEni, error) {
	cvEni := &cceiov1alpha1.CrossVPCEni{}
	err := c.restClient.
		Get().
		// Namespace(c.namespace).
		Name(name).
		Resource(crossVPCEniPurl).
		Do(ctx).
		Into(cvEni)
	if err != nil {
		return nil, err
	}
	return cvEni, err
}

func (c *CrossVpcEni) GetCrossVpcEniStatusByPodName(ctx context.Context,
	name string) (cceiov1alpha1.EniStatus, string, error) {
	cvEniList, err := c.ListCrossVpcEni(ctx)
	if err != nil {
		return "", "", err
	}
	for _, eni := range cvEniList.Items {
		if eni.Labels[CrossVpcENIOwnerName] == name {
			return eni.Status.EniStatus, eni.Status.EniID, nil
		}
	}
	return "", "", nil
}

func (c *CrossVpcEni) ListCrossVpcEni(ctx context.Context) (*cceiov1alpha1.CrossVPCEniList, error) {
	cvEni := &cceiov1alpha1.CrossVPCEniList{}
	err := c.restClient.
		Get().
		// Namespace(c.namespace).
		Resource(crossVPCEniPurl).
		Do(ctx).
		Into(cvEni)
	if err != nil {
		return nil, err
	}
	return cvEni, err
}

var (
	crossVPCEniPurl = "crossvpcenis"
)
