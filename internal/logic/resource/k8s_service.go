// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/03 14:28:00, by <EMAIL>, create
*/

package resource

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccemonitor"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	// annotation
	blbIDAnnotation = "service.beta.kubernetes.io/cce-load-balancer-id"
)

type K8SService struct {
	baseParams            BaseParams
	namespace             string
	name                  string
	externalTrafficPolicy corev1.ServiceExternalTrafficPolicyType
}

func (k *K8SService) NewK8SService(ctx context.Context, baseClient *cases.BaseClient, namespace string, name string) error {
	if err := Init(k, baseClient); err != nil {
		logger.Errorf(ctx, "NewK8SService failed: %s", err)
		return err
	}
	k.baseParams.message = fmt.Sprintf("Timeout to wait for K8S_Service %s/%s ready", namespace, name)
	k.namespace = namespace
	k.name = name
	return nil
}

func (k *K8SService) GetBaseParams() *BaseParams {
	return &k.baseParams
}

func (k *K8SService) SetExternalTrafficPolicy(policy corev1.ServiceExternalTrafficPolicyType) {
	k.externalTrafficPolicy = policy
}

func (k *K8SService) CheckResource(ctx context.Context) error {
	if _, err := k.GetServiceBlB(ctx, k.namespace, k.name); err != nil {
		return err
	}
	if _, err := k.GetServiceEIP(ctx, k.namespace, k.name); err != nil {
		return err
	}
	return nil
}

// CheckInfo return nil if service .metadata.name/.metadata.name/.spec.externalTrafficPolicy is equal to expect
func (k *K8SService) CheckInfo(ctx context.Context) error {
	result, err := k.baseParams.baseClient.K8SClient.CoreV1().Services(k.namespace).Get(ctx, k.name, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "get service %s/%s failed:%v", k.namespace, k.name, err)
		return fmt.Errorf("get service %s/%s failed:%v", k.namespace, k.name, err)
	}

	if result.Name != k.name {
		return fmt.Errorf("service %s/%s name is %s, expect: %s", k.namespace, k.name,
			result.Name, k.name)
	}
	if result.Namespace != k.namespace {
		return fmt.Errorf("service %s/%s namespace is %s, expect: %s", k.namespace, k.name,
			result.Namespace, k.namespace)
	}
	if result.Spec.ExternalTrafficPolicy != k.externalTrafficPolicy {
		return fmt.Errorf("service %s/%s externalTrafficPolicy is %s, expect: %s", k.namespace, k.name,
			result.Spec.ExternalTrafficPolicy, k.externalTrafficPolicy)
	}
	return nil
}

// GetServiceBlB 获取 Service 的 blb
func (k *K8SService) GetServiceBlB(ctx context.Context, namespace string, name string) (string, error) {
	service, err := k.baseParams.baseClient.K8SClient.CoreV1().Services(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "Get Service %s/%s failed: %s", namespace, name, err)
		return "", err
	}
	logger.Infof(ctx, "Get service success: %s", utils.ToJSON(service))
	annotations := service.GetAnnotations()

	// 检查是否有 LB
	blbID, ok := annotations[blbIDAnnotation]
	if !ok {
		logger.Errorf(ctx, "%s is empty", blbIDAnnotation)
		return "", fmt.Errorf("%s is empty", blbIDAnnotation)
	}
	logger.Infof(ctx, "BlBId: %s", blbID)
	return blbID, nil
}

// GetServiceEIP 获取 Service 的 eip
func (k *K8SService) GetServiceEIP(ctx context.Context, namespace string, name string) (string, error) {
	service, err := k.baseParams.baseClient.K8SClient.CoreV1().Services(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "Get Service %s/%s failed: %s", namespace, name, err)
		return "", err
	}
	logger.Infof(ctx, "Get service success: %s", utils.ToJSON(service))

	ingresses := service.Status.LoadBalancer.Ingress
	if len(ingresses) == 0 {
		logger.Errorf(ctx, "%s/%s Eip is empty", namespace, name)
		return "", fmt.Errorf("%s/%s Eip is empty", namespace, name)
	}
	eip := service.Status.LoadBalancer.Ingress[0].IP
	logger.Infof(ctx, "EIP: %s", eip)
	return eip, nil
}

func (k *K8SService) CheckServiceMsg(ctx context.Context, clusterID string, msg string) error {
	timer := time.NewTimer(5 * time.Minute)
	defer timer.Stop()
	ticker := time.NewTicker(20 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-timer.C:
			logger.Errorf(ctx, "timeout waiting msg occur")
			return errors.New("timeout waiting msg occur")
		case <-ticker.C:
			args := &ccemonitor.EventQueryArg{
				ClusterUuid:  clusterID,
				Namespace:    k.namespace,
				ResourceKind: "Service",
				ResourceName: k.name,
			}
			eventList, err := k.baseParams.baseClient.MonitorClient.GetK8sEvent(ctx, args, nil)
			if err != nil {
				logger.Errorf(ctx, "get service event failed: %v", err.Error())
				return fmt.Errorf("get service event failed: %v", err.Error())
			}

			var existed bool
			for _, event := range eventList.Result {
				if strings.Contains(event.Message, msg) {
					existed = true
					logger.Infof(ctx, "service %s event has msg \"%s\", check success", k.name, msg)
					return nil
				}
			}

			if !existed {
				logger.Warnf(ctx, "service % event does not has msg \"%s\" yet, retry", k.name, msg)
				continue
			}
		}
	}
}

func (k *K8SService) GetServiceEvents(ctx context.Context, clusterID string) error {
	args := &ccemonitor.EventQueryArg{
		ClusterUuid:  clusterID,
		Namespace:    k.namespace,
		ResourceKind: "Service",
		ResourceName: k.name,
	}
	eventList, err := k.baseParams.baseClient.MonitorClient.GetK8sEvent(ctx, args, nil)
	if err != nil {
		logger.Errorf(ctx, "get service event failed: %v", err.Error())
		return fmt.Errorf("get service event failed: %v", err.Error())
	}

	for _, event := range eventList.Result {
		logger.Infof(ctx, "Type: %s; Reason: %s; Message: %s", event.Type, event.Reason, event.Message)
	}

	return nil
}
