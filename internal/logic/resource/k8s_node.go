package resource

import (
	"context"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

type K8SNode struct {
	baseParams BaseParams
	clusterID  string
	instanceID string
	status     string
}

func (k *K8SNode) NewK8SNode(ctx context.Context, baseClient *cases.BaseClient, clusterID string, instanceID string) error {
	if err := Init(k, baseClient); err != nil {
		logger.Errorf(ctx, "NewK8SPod failed: %s", err)
		return err
	}
	k.baseParams.message = fmt.Sprintf("Timeout to wait for K8S_Node %s/%s ready", clusterID, instanceID)
	k.clusterID = clusterID
	k.instanceID = instanceID
	return nil
}

func (k *K8SNode) GetBaseParams() *BaseParams {
	return &k.baseParams
}

func (k *K8SNode) SetStatus(status string) {
	k.status = status
	k.baseParams.message = fmt.Sprintf("Timeout to wait for K8S_Node %s/%s status reach", k.clusterID, k.instanceID)
}

func (k *K8SNode) GetNodeStatus(ctx context.Context) (string, error) {
	resp, err := k.baseParams.baseClient.CCEClient.GetInstance(ctx, k.clusterID, k.instanceID, nil)
	if err != nil {
		return "", err
	}
	if resp.Instance == nil {
		return "", errors.New("instance in response of CCEClient.GetInstance is nil")
	}
	return string(resp.Instance.Status.InstancePhase), nil
}

func (k *K8SNode) CheckResource(ctx context.Context) error {
	status, err := k.GetNodeStatus(ctx)
	if err != nil {
		return err
	}
	if status != k.status {
		return fmt.Errorf("node %s status: %s not as expected: %s", k.instanceID, status, k.status)
	}

	return nil
}
