/* helm_plugin.go */
/*
modification history
--------------------
2022/7/19, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
*/

package resource

import (
	"context"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/helm"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

type Plugin struct {
	baseParams BaseParams
	clusterID  string
	pluginName string
}

func NewPlugin(ctx context.Context, baseClient *cases.BaseClient, pluginName string) (*Plugin, error) {
	i := &Plugin{}
	if err := Init(i, baseClient); err != nil {
		logger.Errorf(ctx, "NewPlugin failed: %s", err)
		return nil, err
	}
	i.clusterID = baseClient.ClusterID
	i.pluginName = pluginName
	i.baseParams.message = fmt.Sprintf("Timeout to wait for plugin %s/%s ready", i.clusterID, pluginName)
	return i, nil
}

// InstallNginxIngressPlugin 安装nginx-ingress组件
func (i *Plugin) InstallNginxIngressPlugin(ctx context.Context, ingressClass, instancegroupID string, k8sVersion ccetypes.K8SVersion) error {
	pluginChart, err := i.baseParams.baseClient.HelmClient.GetPublicRepoChartDetail(ctx, i.pluginName)
	if err != nil {
		logger.Errorf(ctx, "get plugin %s failed: %v", i.pluginName, err.Error())
		return err
	}

	var pluginYaml string
	if ok, _ := k8sVersion.IsAfter(ccetypes.K8S_1_20_8); ok {
		pluginYaml, err = i.baseParams.baseClient.HelmClient.GetPublicRepoChartYaml(ctx, i.pluginName, pluginChart.History[0].Version)
		if err != nil {
			logger.Errorf(ctx, "get plugin yaml failed: %v", err.Error())
			return err
		}
	} else {
		pluginYaml, err = i.baseParams.baseClient.HelmClient.GetPublicRepoChartYaml(ctx, i.pluginName, pluginChart.History[1].Version)
		if err != nil {
			logger.Errorf(ctx, "get plugin yaml failed: %v", err.Error())
			return err
		}
	}

	// 替换参数
	pluginYaml = strings.Replace(pluginYaml, "ingressClass: cce-nginx", "ingressClass: "+ingressClass, 1)                            // 替换默认ingress class
	pluginYaml = strings.Replace(pluginYaml, "# fullnameOverride:", "fullnameOverride: "+ingressClass+"-ngx-control", 1)             // 覆盖命名，防止过长
	pluginYaml = strings.Replace(pluginYaml, "#   instance-group-id: cce-ig-fwvdbr35", "    instance-group-id: "+instancegroupID, 1) // 指定节点组
	pluginYaml = strings.Replace(pluginYaml, "cpu: 120m", "cpu: 500m", 1)                                                            // 修改组件limit资源
	pluginYaml = strings.Replace(pluginYaml, "memory: 100Mi", "memory: 500Mi", 1)

	request := new(helm.ReleaseInstallRequest)
	request.ChartName = i.pluginName
	request.Name = ingressClass + "-ngx-control"
	request.IsPublic = true
	request.Values = pluginYaml

	if ok, _ := k8sVersion.IsAfter(ccetypes.K8S_1_20_8); ok {
		request.ChartVersion = pluginChart.History[0].Version
	} else {
		request.ChartVersion = pluginChart.History[1].Version
	}

	err = i.baseParams.baseClient.HelmClient.InstallRelease(ctx, i.clusterID, "kube-system", request)
	if err != nil {
		logger.Errorf(ctx, "plugin install failed: %v", err.Error())
		return err
	}

	logger.Infof(ctx, "nginx-ingress plugin install success")

	return nil
}

func (i *Plugin) IsReleaseExist(ctx context.Context, releaseName string) (bool, error) {
	helmReleaseList, err := i.baseParams.baseClient.HelmClient.GetUserReleaseList(ctx, i.clusterID, "kube-system", "1", "100", releaseName)
	if err != nil {
		logger.Errorf(ctx, "list helm release by keyword %s failed: %v", releaseName, err.Error())
		return false, fmt.Errorf("list helm release by keyword %s failed: %v", releaseName, err.Error())
	}

	logger.Infof(ctx, "list helm release by keyword %s: %s", releaseName, helmReleaseList)

	if helmReleaseList.Total != 0 {
		return true, nil
	}

	return false, nil
}

// InstallCCELogOperatorPlugin 安装cce-log-operator插件
func (i *Plugin) InstallCCELogOperatorPlugin(ctx context.Context) error {
	pluginList, err := i.baseParams.baseClient.HelmClient.GetPublicRepoChartList(ctx, "1", "100", i.pluginName)
	if err != nil {
		logger.Errorf(ctx, "get plugin %s failed: %v", i.pluginName, err.Error())
		return err
	}

	pluginYaml, err := i.baseParams.baseClient.HelmClient.GetPublicRepoChartYaml(ctx, i.pluginName, pluginList.List[0].Version)
	if err != nil {
		logger.Errorf(ctx, "get plugin yaml failed: %v", err.Error())
		return err
	}

	request := new(helm.ReleaseInstallRequest)
	request.ChartName = i.pluginName
	request.ChartVersion = pluginList.List[0].Version
	request.Name = i.pluginName
	request.IsPublic = true
	request.Values = pluginYaml

	err = i.baseParams.baseClient.HelmClient.InstallRelease(ctx, i.clusterID, "kube-system", request)
	if err != nil {
		logger.Errorf(ctx, "plugin install failed: %v", err.Error())
		return err
	}

	logger.Infof(ctx, "cce-log-operator plugin install success")

	return nil
}

func (i *Plugin) UninstallPlugin(ctx context.Context, namespace, name string) error {
	return i.baseParams.baseClient.HelmClient.DeleteRelease(ctx, i.clusterID, namespace, name)
}

func (i *Plugin) CheckResource(ctx context.Context) error {
	return nil
}

func (i *Plugin) GetBaseParams() *BaseParams {
	return &i.baseParams
}
