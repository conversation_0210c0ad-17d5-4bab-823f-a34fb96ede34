// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/03 14:28:00, by <EMAIL>, create
*/

package resource

import (
	"context"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	eipsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

type EIPBandwidth struct {
	baseParams BaseParams
	eip        string
	bandwidth  string
}

func (k *EIPBandwidth) NewEIPBandwidth(ctx context.Context, baseClient *cases.BaseClient, eip string, bandwidth string) error {
	if err := Init(k, baseClient); err != nil {
		logger.Errorf(ctx, "NewEIPBandwidth failed: %s", err)
		return err
	}
	k.baseParams.message = fmt.Sprintf("Timeout to wait for EIP %s bandwidth reach %s", eip, bandwidth)
	k.eip = eip
	k.bandwidth = bandwidth
	return nil
}

func (k *EIPBandwidth) GetBaseParams() *BaseParams {
	return &k.baseParams
}

func (k *EIPBandwidth) SetBandwidth(bandwidth string) {
	k.bandwidth = bandwidth
	k.baseParams.message = fmt.Sprintf("Timeout to wait for EIP %s bandwidth reach %s", k.eip, bandwidth)
}

func (k *EIPBandwidth) CheckResource(ctx context.Context) error {
	bandwidth, err := k.GetEIPBandwidth(ctx, k.eip)
	if err != nil {
		return err
	}
	if bandwidth != k.bandwidth {
		return fmt.Errorf("EIP bandwidth: %s not as expected: %s", bandwidth, k.bandwidth)
	}
	return nil
}

// 获取 eip 的 bandwidth
func (k *EIPBandwidth) GetEIPBandwidth(ctx context.Context, eip string) (string, error) {
	args := &eipsdk.GetEIPsArgs{
		EIP: eip,
	}
	eipList, err := k.baseParams.baseClient.EIPClient.GetEIPs(ctx, args, nil)
	if err != nil {
		logger.Errorf(ctx, "%s EIP GetEIPs failed: %s", eip, err)
		return "", err
	}
	logger.Infof(ctx, "%s EIP GetEIPs success: %s", eip, utils.ToJSON(eipList))

	// 检查eip是否存在
	if len(eipList) == 0 {
		logger.Errorf(ctx, "%s EIP is not exist", eip)
		return "", fmt.Errorf("%s EIP is not exist", eip)
	}
	bandwidth := strconv.Itoa(eipList[0].BandwidthInMbps)
	logger.Infof(ctx, "%s EIP bandwidth is %s", eip, bandwidth)
	return bandwidth, nil
}
