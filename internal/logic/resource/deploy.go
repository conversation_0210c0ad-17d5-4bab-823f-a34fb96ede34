package resource

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"

	v1 "k8s.io/api/core/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/kubectl"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	nginxDeploymentYaml_K8s_1_16 = `
apiVersion: apps/v1
kind: Deployment
metadata:
  name: [DEPLOYMENT_NAME]
  labels:
    app: [DEPLOYMENT_NAME]
spec:
  replicas: [REPLICAS]
  minReadySeconds: 0
  strategy: 
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app: [DEPLOYMENT_NAME]
  template:
    metadata:
      labels:
        app: [DEPLOYMENT_NAME]
    spec:
      restartPolicy: Always
      containers:
        - name: nginx
          image: hub.baidubce.com/cce/nginx-alpine-go:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 80`

	notReadyParams = `
          readinessProbe:
            exec:
              command:
                - /bin/sh
                - '-c'
                - exit 1
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 5`
)

var KubectlMutex sync.Mutex

// DeployExampleNginxDeployment 部署指定数量的 Pod
func DeployExampleNginxDeployment(ctx context.Context, baseClient *cases.BaseClient, podNum int, appName string) error {
	ymlStr := strings.ReplaceAll(nginxDeploymentYaml_K8s_1_16, "[DEPLOYMENT_NAME]", appName)
	ymlStr = strings.ReplaceAll(ymlStr, "[REPLICAS]", strconv.Itoa(podNum))

	err := DeployYml(ctx, baseClient.KubectlClient, baseClient.KubeConfig, ymlStr)

	if err != nil {
		return err
	}
	if podNum == 0 {
		return nil
	}

	var k8sPod K8SPod
	if err := k8sPod.NewK8SPod(ctx, baseClient, "default", nil); err != nil {
		return err
	}
	k8sPod.SetMatchLabel("app", appName)
	k8sPod.SetStatus(string(v1.PodRunning))

	err = WaitForResourceReady(ctx, &k8sPod)
	if err != nil {
		return err
	}

	return nil
}

// DeployNotReadyNginxDeployment 部署指定数量的not ready Pod
func DeployNotReadyNginxDeployment(ctx context.Context, baseClient *cases.BaseClient, podNum int, appName string) error {
	if podNum == 0 {
		return nil
	}

	deployment, err := baseClient.K8SClient.AppsV1().Deployments("default").Get(ctx, appName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("get deployment %s failed %v", appName, err)
	}
	if deployment != nil {
		err = baseClient.KubeClient.DeleteDeploymentAppsV1(ctx, deployment.Namespace, appName)
		if err != nil {
			return fmt.Errorf("delete deployment %s failed: %v", appName, err)
		}
	}

	ymlStr := strings.ReplaceAll(nginxDeploymentYaml_K8s_1_16, "[DEPLOYMENT_NAME]", appName)
	ymlStr = strings.ReplaceAll(ymlStr, "[REPLICAS]", strconv.Itoa(podNum))
	ymlStr = ymlStr + notReadyParams

	err = DeployYml(ctx, baseClient.KubectlClient, baseClient.KubeConfig, ymlStr)

	if err != nil {
		return err
	}

	var k8sPod K8SPod
	if err := k8sPod.NewK8SPod(ctx, baseClient, "default", nil); err != nil {
		return err
	}
	k8sPod.SetMatchLabel("app", appName)
	k8sPod.SetStatus(string(v1.PodRunning))

	err = WaitForResourceReady(ctx, &k8sPod)
	if err != nil {
		return err
	}

	return nil
}

// DeployYml 多个测试用例同时使用Kubectl会出现 .kube/config.lock: file exists 错误 所以需要对其使用进行加锁
func DeployYml(ctx context.Context, kubectlClient kubectl.Interface, kubeconfig string, strYml string) error {
	KubectlMutex.Lock()
	err := kubectlClient.Apply(ctx, ccetypes.K8S_1_16_8, kubeconfig, strYml)
	KubectlMutex.Unlock()

	if err != nil {
		return err
	}
	return nil
}

// Contains 检测 ips 是否包含 candidateIP
func Contains(ips []string, candidateIP string) bool {
	if ips == nil {
		return false
	}
	for _, ip := range ips {
		if ip == candidateIP {
			return true
		}
	}
	return false
}

// CurlWithIP 对 Service EIP 地址执行 curl 操作，以检测Service的连通性
func CurlWithIP(ipAddr string) error {
	return nil
}
