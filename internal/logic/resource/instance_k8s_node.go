package resource

import (
	"context"
	"errors"
	"fmt"
	"strings"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	cloudproviderapi "k8s.io/cloud-provider/api"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	cloud_provider "icode.baidu.com/baidu/cce-test/e2e-test/services/cluster/cce-cloud-controller-mamager/cloud-provider"
)

type InstancesK8sNodes struct {
	baseParams BaseParams
	clusterID  string
}

// 对于 Node 检查 Spec.ProviderID Status.Addresses uninitialized Taint 与各个 Label
func NewInstancesK8sNodes(ctx context.Context, baseClient *cases.BaseClient, clusterID string) (*InstancesK8sNodes, error) {
	k := &InstancesK8sNodes{}
	if err := Init(k, baseClient); err != nil {
		logger.Errorf(ctx, "NewK8SPod failed: %s", err)
		return nil, err
	}
	k.baseParams.message = fmt.Sprintf("Timeout to wait for Instance_K8s_Nodes %s ready", clusterID)
	k.clusterID = clusterID

	return k, nil
}

func (k *InstancesK8sNodes) GetBaseParams() *BaseParams {
	return &k.baseParams
}

func (k *InstancesK8sNodes) CheckResource(ctx context.Context) error {
	// 1. InstanceCRD 是否全部 Running
	instances, err := k.isAllInstancesRunning(ctx)
	if err != nil {
		logger.Errorf(ctx, "Not All Instance Running: %s", err)
		return err
	}
	logger.Infof(ctx, "All Instance Running")

	// 2. K8s Node 是否全部 Ready
	k8sNodes, err := k.isAllK8sNodeReady(ctx)
	if err != nil {
		logger.Errorf(ctx, "Not All Nodes Ready: %s", err)
		return err
	}
	logger.Infof(ctx, "All Nodes Ready")

	// 3. InstanceCRD 和 K8s Node 数量是否一致
	if len(k8sNodes) != len(instances) {
		logger.Errorf(ctx, "Nodes Instance Number Not Match")
		return errors.New("instances not equal to k8sNodes")
	}
	logger.Infof(ctx, "Nodes Instance Number Match")

	// 4. InstanceCRD 和 K8s Node 是否一一对应 相关 Labels、Addresses 是否一致
	for _, k8sNode := range k8sNodes {
		isFoundMatch := false
		for _, instance := range instances {
			if cloud_provider.IsInstanceAndK8sNodeCorresponding(instance, k8sNode.Name) {
				isFoundMatch = true
				err := isInstanceAndK8sNodeInfoMatch(instance, k8sNode)
				if err != nil {
					return err
				}
				break
			}
		}
		if !isFoundMatch {
			return fmt.Errorf("node %s cannot found mactch instance", k8sNode.Name)
		}
	}
	logger.Infof(ctx, "Nodes Instance Info Match")

	return nil
}

func isInstanceAndK8sNodeInfoMatch(instance *ccev2.Instance, k8sNode v1.Node) error {
	// 1. 检查 ProviderID
	if !strings.Contains(k8sNode.Spec.ProviderID, instance.Status.Machine.InstanceID) {
		return fmt.Errorf("instance %s and k8sNode %s info not match: ProviderID", instance.Spec.CCEInstanceID, k8sNode.Name)
	}

	// 2. 检查 Labels
	nodeDomain, ok := k8sNode.ObjectMeta.Labels[v1.LabelZoneFailureDomain]
	if !ok {
		return fmt.Errorf("k8sNode %s label failure-domain.beta.kubernetes.io/zone not found", k8sNode.Name)
	}
	if nodeDomain != instance.Spec.VPCConfig.AvailableZone {
		return fmt.Errorf("instance %s and k8sNode %s info not match: failure-domain.beta.kubernetes.io/zone", instance.Spec.CCEInstanceID, k8sNode.Name)
	}

	_, ok = k8sNode.ObjectMeta.Labels[v1.LabelZoneRegion]
	if !ok {
		return fmt.Errorf("k8sNode %s label failure-domain.beta.kubernetes.io/region not found", k8sNode.Name)
	}

	_, ok = k8sNode.ObjectMeta.Labels[v1.LabelInstanceType]
	if !ok {
		return fmt.Errorf("k8sNode %s label beta.kubernetes.io/instance-type not found", k8sNode.Name)
	}

	// 3. 检查 Taint
	for _, taint := range k8sNode.Spec.Taints {
		if taint.Key == cloudproviderapi.TaintExternalCloudProvider {
			return fmt.Errorf("k8sNode %s taints still exists", k8sNode.Name)
		}
	}

	// 4. 检查 Status.Addresses
	// 我们在此测试中创建的是IPv4的无EIP节点 因此这里应该是2个 并且地址都是节点的     VPC IP
	if len(k8sNode.Status.Addresses) != 2 {
		return fmt.Errorf("k8sNode %s addresses should be two but not", k8sNode.Name)
	}
	for _, addr := range k8sNode.Status.Addresses {
		if addr.Address != instance.Status.Machine.VPCIP {
			return fmt.Errorf("instance %s and k8sNode %s info not match: Addresses", instance.Spec.CCEInstanceID, k8sNode.Name)
		}
	}

	return nil
}

func (k *InstancesK8sNodes) isAllInstancesRunning(ctx context.Context) ([]*ccev2.Instance, error) {
	params := &ccev2.ListInstancesByPageParams{
		KeywordType: ccev2.InstanceKeywordTypeInstanceName,
		Keyword:     "",
		OrderBy:     ccev2.InstanceOrderByCreatedAt,
		Order:       ccev2.OrderASC,
		PageNo:      1,
		PageSize:    100000,
	}
	resp, err := k.baseParams.baseClient.CCEClient.ListInstancesByPage(ctx, k.clusterID, params, nil)
	if err != nil {
		return nil, err
	}
	if resp == nil || resp.InstancePage == nil {
		return nil, fmt.Errorf("cluster %s listInstancesByPage fail", k.clusterID)
	}

	resultIns := make([]*ccev2.Instance, 0)
	for _, ins := range resp.InstancePage.InstanceList {
		if ins.Spec.ClusterRole == ccetypes.ClusterRoleNode {
			resultIns = append(resultIns, ins)
		}
	}

	for _, instance := range resultIns {
		if instance.Status.InstancePhase != ccetypes.InstancePhaseRunning {
			return nil, fmt.Errorf(
				"instance %s of cluster %s is not in running, actual %s",
				instance.Spec.CCEInstanceID, k.clusterID, instance.Status.InstancePhase)
		}
	}

	return resultIns, nil
}

func (k *InstancesK8sNodes) isAllK8sNodeReady(ctx context.Context) ([]v1.Node, error) {
	resp, err := k.baseParams.baseClient.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, err
	}
	if resp == nil || resp.Items == nil {
		return nil, fmt.Errorf("cluster %s list nodes fail", k.clusterID)
	}

	resultNods := make([]v1.Node, 0)
	for _, node := range resp.Items {
		if _, ok := node.Labels["cluster-role"]; ok && node.Labels["cluster-role"] == "master" {
			continue
		}
		for _, condition := range node.Status.Conditions {
			if condition.Type == v1.NodeReady && condition.Status != v1.ConditionTrue {
				return nil, fmt.Errorf(
					"node %s of cluster %s is not in ready", node.Name, k.baseParams.baseClient.ClusterID)
			}
		}
		resultNods = append(resultNods, node)
	}

	return resultNods, nil
}
