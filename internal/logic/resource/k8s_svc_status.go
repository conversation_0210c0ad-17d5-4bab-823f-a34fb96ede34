// Copyright 2022 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2022/05/19 14:14:00, by <EMAIL>, create
*/

package resource

import (
	"context"
	"fmt"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

type K8SServiceStatus struct {
	baseParams BaseParams
	namespace  string
	name       string
}

func (k *K8SServiceStatus) NewK8SServiceStatus(ctx context.Context, baseClient *cases.BaseClient, namespace, name string) error {
	if err := Init(k, baseClient); err != nil {
		logger.Errorf(ctx, "NewK8SServiceStatus failed: %s", err)
		return err
	}
	k.baseParams.message = fmt.Sprintf("Timeout to wait for K8S_Service %s/%s to deleted", namespace, name)
	k.namespace = namespace
	k.name = name
	return nil
}

func (k *K8SServiceStatus) GetBaseParams() *BaseParams {
	return &k.baseParams
}

// CheckResource return nil if service deleted
func (k *K8SServiceStatus) CheckResource(ctx context.Context) error {
	_, err := k.baseParams.baseClient.K8SClient.CoreV1().Services(k.namespace).Get(ctx, k.name, metav1.GetOptions{})
	if err != nil {
		if strings.Contains(err.Error(), fmt.Sprintf("\"%s\" not found", k.name)) {
			return nil
		}
		logger.Errorf(ctx, "get service %s/%s failed:%v", k.namespace, k.name, err)
		return fmt.Errorf("get service %s/%s failed:%v", k.namespace, k.name, err)
	}

	return fmt.Errorf("service %s/%s still exists", k.namespace, k.name)
}
