// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/03 14:28:00, by <EMAIL>, create
*/

package resource

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	eipsdk "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

type EIPStatus struct {
	baseParams BaseParams
	eip        string
	status     string
}

func (k *EIPStatus) NewEIPStatus(ctx context.Context, baseClient *cases.BaseClient, eip string, status string) error {
	if err := Init(k, baseClient); err != nil {
		logger.Errorf(ctx, "NewEIPStatus failed: %s", err)
		return err
	}
	k.baseParams.message = fmt.Sprintf("Timeout to wait for EIP %s status reach %s", eip, status)
	k.eip = eip
	k.status = status
	return nil
}

func (k *EIPStatus) GetBaseParams() *BaseParams {
	return &k.baseParams
}

func (k *EIPStatus) SetStatus(status string) {
	k.status = status
	k.baseParams.message = fmt.Sprintf("Timeout to wait for EIP %s status reach %s", k.eip, status)
}

func (k *EIPStatus) CheckResource(ctx context.Context) error {
	status, err := k.GetEIPStatus(ctx, k.eip)
	if err != nil {
		if k.status == "deleted" && strings.Contains(err.Error(), "not exist") {
			return nil
		}
		return err
	}
	if status != k.status {
		return fmt.Errorf("EIP status: %s not as expected: %s", status, k.status)
	}
	return nil
}

// 获取 eip 的 状态
func (k *EIPStatus) GetEIPStatus(ctx context.Context, eip string) (string, error) {
	args := &eipsdk.GetEIPsArgs{
		EIP: eip,
	}
	eipList, err := k.baseParams.baseClient.EIPClient.GetEIPs(ctx, args, nil)
	if err != nil {
		logger.Errorf(ctx, "%s EIP GetEIPs failed: %s", eip, err)
		return "", err
	}
	logger.Infof(ctx, "%s EIP GetEIPs success: %s", eip, utils.ToJSON(eipList))

	// 检查eip是否存在
	if len(eipList) == 0 {
		logger.Errorf(ctx, "%s EIP is not exist", eip)
		return "", fmt.Errorf("%s EIP is not exist", eip)
	}
	status := string(eipList[0].Status)
	logger.Infof(ctx, "%s EIP status is %s", eip, status)
	return status, nil
}

// CheckIsInRecycleBin 检查eip是否在回收站中
func (k *EIPStatus) CheckIsInRecycleBin(ctx context.Context) (bool, error) {
	args := &eipsdk.GetEIPsArgs{
		EIP: k.eip,
	}
	eipList, err := k.baseParams.baseClient.EIPClient.GetRecycleEIPs(ctx, args, nil)
	if err != nil {
		logger.Errorf(ctx, "%s EIP GetRecycleEIPs failed: %s", k.eip, err)
		return false, err
	}
	logger.Infof(ctx, "%s EIP GetRecycleEIPs success: %s", k.eip, utils.ToJSON(eipList))

	// 检查eip是否存在
	if len(eipList) == 0 {
		logger.Errorf(ctx, "%s EIP is not exist", k.eip)
		return false, nil
	}
	return true, nil
}

// DeleteEIPInRecycleBin 删除在回收站中的eip
func (k *EIPStatus) DeleteEIPInRecycleBin(ctx context.Context, eip string) (bool, error) {
	// 检查eip是否存在
	if eip == "" {
		return false, errors.New("eip is empty")
	}
	err := k.baseParams.baseClient.EIPClient.DeleteRecycleEIP(ctx, eip, nil)
	if err != nil {
		logger.Errorf(ctx, "Delete EIP in recycle bin failed, error %s", err)
		return false, err
	}
	logger.Infof(ctx, "delete EIP %s in recycle bin success", eip)

	return true, nil
}
