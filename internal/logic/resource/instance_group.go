// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2022/02/10 , by z<PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
the common function of instanceGroup
*/

package resource

import (
	"context"
	"errors"
	"fmt"
	"time"

	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

type InstanceGroup struct {
	baseParams      BaseParams
	clusterID       string
	instanceGroupID string
}

func NewInstanceGroup(ctx context.Context, baseClient *cases.BaseClient, instanceGroupID string, interval, timeout time.Duration) (*InstanceGroup, error) {
	i := &InstanceGroup{
		baseParams: BaseParams{
			checkInterval: interval,
			checkTimeout:  timeout,
			baseClient:    baseClient,
		},
	}
	if err := Init(i, baseClient); err != nil {
		logger.Errorf(ctx, "NewInstanceGroup failed: %s", err)
		return nil, err
	}
	i.clusterID = baseClient.ClusterID
	i.instanceGroupID = instanceGroupID
	i.baseParams.message = fmt.Sprintf("Timeout to wait for InstanceGroup %s/%s ready", i.clusterID, instanceGroupID)
	return i, nil
}

// NewInstanceGroupWithNullConfig 若没有具体的节点组创建配置，则利用集群中的节点配置进行节点组创建
func NewInstanceGroupWithNullConfig(ctx context.Context, baseClient *cases.BaseClient, instanceGroupName string, NotCreateIfExisted bool) (*InstanceGroup, error) {
	i := &InstanceGroup{}
	if err := Init(i, baseClient); err != nil {
		logger.Errorf(ctx, "NewInstanceGroup failed: %s", err)
		return nil, err
	}
	i.clusterID = baseClient.ClusterID

	if NotCreateIfExisted {
		// 如果节点组存在，则不新建
		resp, err := baseClient.CCEClient.ListInstanceGroups(ctx, baseClient.ClusterID, nil, nil)
		if err != nil {
			return nil, fmt.Errorf("list instancegroups failed: %v", err.Error())
		}

		for _, instancegroup := range resp.Page.List {
			if instancegroup.Spec.InstanceGroupName == instanceGroupName {
				i.instanceGroupID = instancegroup.Spec.CCEInstanceGroupID
				break
			}
		}
	}
	if i.instanceGroupID == "" {
		// 根据列表节点进行节点组创建
		instance, err := baseClient.CCEClient.ListInstancesByPage(ctx, baseClient.ClusterID, &ccev2.ListInstancesByPageParams{
			ClusterRole: ccetypes.ClusterRoleNode}, nil)
		if err != nil {
			return nil, fmt.Errorf("list instance by page failed: %v", err.Error())
		}
		if len(instance.InstancePage.InstanceList) == 0 {
			return nil, errors.New("cluster node cnt is zero, can not create instancegroup with node args")
		}

		// 创建节点组
		instancegroup, err := baseClient.CCEClient.CreateInstanceGroup(
			ctx,
			baseClient.ClusterID,
			&ccev2.CreateInstanceGroupRequest{
				InstanceGroupSpec: ccetypes.InstanceGroupSpec{
					InstanceGroupName: instanceGroupName,
					Replicas:          1,
					InstanceTemplate: ccetypes.InstanceTemplate{
						InstanceSpec: ccetypes.InstanceSpec{
							ClusterRole:  ccetypes.ClusterRoleNode,
							MachineType:  ccetypes.MachineTypeBCC,
							InstanceType: instance.InstancePage.InstanceList[0].Spec.InstanceType,
							VPCConfig: ccetypes.VPCConfig{
								VPCSubnetID:     instance.InstancePage.InstanceList[0].Spec.VPCSubnetID,
								SecurityGroupID: instance.InstancePage.InstanceList[0].Spec.SecurityGroupID,
								SecurityGroup: ccetypes.SecurityGroup{
									EnableCCERequiredSecurityGroup: true,
									EnableCCEOptionalSecurityGroup: false,
								},
							},
							InstanceResource: ccetypes.InstanceResource{
								CPU:         instance.InstancePage.InstanceList[0].Spec.InstanceResource.CPU,
								MEM:         instance.InstancePage.InstanceList[0].Spec.InstanceResource.MEM,
								MachineSpec: instance.InstancePage.InstanceList[0].Spec.InstanceResource.MachineSpec,
							},
							ImageID: instance.InstancePage.InstanceList[0].Spec.ImageID,
							InstanceOS: ccetypes.InstanceOS{
								ImageType: "System",
							},
							AdminPassword: "jhkj4%w@oip",
						},
					},
				},
			},
			nil,
		)
		if err != nil {
			return nil, fmt.Errorf("create instancegroup failed: %v", err.Error())
		}

		i.instanceGroupID = instancegroup.InstanceGroupID
	}

	i.baseParams.message = fmt.Sprintf("Timeout to wait for InstanceGroup %s/%s ready", i.clusterID, i.instanceGroupID)
	return i, nil
}

func (i *InstanceGroup) GetInstanceGroupID() string {
	return i.instanceGroupID
}

// CheckResource 检查节点组实例是否ready
func (i *InstanceGroup) CheckResource(ctx context.Context) (err error) {
	defer func() {
		if err != nil {
			logger.Errorf(ctx, err.Error())
		}
	}()
	if i.clusterID == "" {
		err = errors.New("the clusterID is empty")
		return
	}
	if i.instanceGroupID == "" {
		err = errors.New("the instanceGroupID is empty")
		return
	}
	baseParams := i.GetBaseParams()
	cceClient := i.baseParams.baseClient.CCEClient
	cceHostClient := i.baseParams.baseClient.CCEHostClient

	timeoutCtx, cancel := context.WithTimeout(ctx, baseParams.checkTimeout)
	wait.UntilWithContext(timeoutCtx, func(ctx context.Context) {

		getIGRes, getIGErr := cceClient.GetInstanceGroup(ctx, i.clusterID, i.instanceGroupID, nil)
		if getIGErr != nil {
			logger.Errorf(ctx, "get instance group info `%s` failed: %v", i.instanceGroupID, getIGErr)
			return
		}
		// 获取节点组任务
		listTaskRes, listTaskErr := cceHostClient.ListTasks(ctx, ccetypes.TaskTypeInstanceGroupReplicas, ccev2.ListTaskOption{
			TargetID: i.instanceGroupID,
		}, nil)
		if listTaskErr != nil {
			logger.Errorf(ctx, "list instance group tasks failed: %v", listTaskErr)
			return
		}

		isTaskSuccess := true
		// 存在task说明节点组创建的时候replica > 0
		if len(listTaskRes.Page.Items) != 0 {
			for _, task := range listTaskRes.Page.Items {
				taskID := task.ID
				if taskID != "" {
					getTaskRes, getTaskErr := cceHostClient.GetTask(ctx, ccetypes.TaskTypeInstanceGroupReplicas, taskID, nil)
					if getTaskErr != nil {
						logger.Errorf(ctx, "get instance group task `%s` failed: %v", taskID, getTaskErr)
						return
					}
					// 如果任务是Aborted需要提前退出等待，节约时间
					if getTaskRes.Task != nil && getTaskRes.Task.Phase == string(ccetypes.TaskPhaseAborted) {
						err = fmt.Errorf("instance group %v task `%s` is aborted, stop check instance group, error: %v",
							i.instanceGroupID, taskID, getTaskRes.Task.ErrMessage)
						cancel()
						return
					}
					if getTaskRes.Task != nil && getTaskRes.Task.Phase != string(ccetypes.TaskPhaseFinished) {
						logger.Infof(ctx, "task `%s` is doing", taskID)
						isTaskSuccess = false
						break
					}
					isTaskSuccess = true
				}
			}
		}

		// 如果节点组实例数等于ReadyReplicas说明创建成功，退出等待
		if isTaskSuccess && getIGRes.InstanceGroup.Spec.Replicas == getIGRes.InstanceGroup.Status.ReadyReplicas {
			logger.Infof(ctx, "instance group replicas ready")
			cancel()
		}
		// 未完成，继续等待
		logger.Warnf(ctx, "instance group expected replicas: %d, actual: %d", getIGRes.InstanceGroup.Spec.Replicas,
			getIGRes.InstanceGroup.Status.ReadyReplicas)
		return
	}, baseParams.checkInterval)

	if errors.Is(timeoutCtx.Err(), context.DeadlineExceeded) {
		err = fmt.Errorf("check instance group replicas ready timeout for %s", baseParams.checkTimeout.String())
		return
	}
	return
}

func (i *InstanceGroup) GetInstanceListByInstanceGroupID(ctx context.Context) ([]string, error) {
	// 通过节点组ID获取实例ID列表
	resp, err := i.baseParams.baseClient.CCEClient.ListInstancesByInstanceGroupID(ctx, i.clusterID,
		i.instanceGroupID, 0, 0, nil)
	if err != nil {
		logger.Errorf(ctx, "get instance by instanceGroupID %s failed:%v", i.instanceGroupID, err)
		return nil, fmt.Errorf("get instance by instanceGroupID %s failed:%v", i.instanceGroupID, err)
	}
	if len(resp.Page.List) == 0 {
		logger.Errorf(ctx, "no instance in instanceGroupID %s", i.instanceGroupID)
		return nil, fmt.Errorf("no instance in instanceGroupID %s", i.instanceGroupID)
	}
	instanceID := make([]string, 0, len(resp.Page.List))
	for _, instance := range resp.Page.List {
		instanceID = append(instanceID, instance.Spec.CCEInstanceID)
	}
	return instanceID, nil
}

func (i *InstanceGroup) GetMachineListByInstanceGroupID(ctx context.Context) ([]string, error) {
	// 通过节点组ID获取实例ID列表
	resp, err := i.baseParams.baseClient.CCEClient.ListInstancesByInstanceGroupID(ctx, i.clusterID,
		i.instanceGroupID, 0, 0, nil)
	if err != nil {
		logger.Errorf(ctx, "get machine by instanceGroupID %s failed:%v", i.instanceGroupID, err)
		return nil, fmt.Errorf("get machine by instanceGroupID %s failed:%v", i.instanceGroupID, err)
	}
	if len(resp.Page.List) == 0 {
		logger.Errorf(ctx, "no machine in instanceGroupID %s", i.instanceGroupID)
		return nil, fmt.Errorf("no machine in instanceGroupID %s", i.instanceGroupID)
	}
	machineID := make([]string, 0, len(resp.Page.List))
	for _, instance := range resp.Page.List {
		machineID = append(machineID, instance.Status.Machine.InstanceID)
	}
	return machineID, nil
}

func (i *InstanceGroup) CheckInstanceNotExistedInCluster(ctx context.Context, targetInstanceID []string) error {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()
	timer := time.NewTimer(30 * time.Minute)
	defer timer.Stop()
NextLoop:
	for {
		select {
		case <-ticker.C:
			flag := false
			resp, err := i.baseParams.baseClient.CCEClient.ListInstancesByPage(ctx, i.clusterID, nil, nil)
			if err != nil {
				logger.Errorf(ctx, "get instance by clusterID %s failed:%v", i.clusterID, err)
				return fmt.Errorf("get instance by clusterID %s failed:%v", i.clusterID, err)
			}

			existInstances := make(map[string]struct{})
			for _, instance := range resp.InstancePage.InstanceList {
				existInstances[instance.Spec.CCEInstanceID] = struct{}{}
			}

			if len(targetInstanceID) > 0 {
				for _, instanceID := range targetInstanceID {
					if _, found := existInstances[instanceID]; found {
						flag = true
						logger.Errorf(ctx, "instance %s should be remove in cluster %s, but still exist", instanceID, i.clusterID)
						continue NextLoop
					}
				}
			}
			if !flag {
				logger.Infof(ctx, "instance remove out succeed")
				return nil
			}
		case <-timer.C:
			logger.Errorf(ctx, "timeout waiting instance move out finished")
			return errors.New("timeout waiting instance move finished")
		}
	}
}

func (i *InstanceGroup) CheckInstanceNotExistedInInstanceGroup(ctx context.Context, targetInstanceID []string) error {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()
	timer := time.NewTimer(30 * time.Minute)
	defer timer.Stop()
NextLoop:
	for {
		select {
		case <-ticker.C:
			flag := false
			resp, err := i.baseParams.baseClient.CCEClient.ListInstancesByInstanceGroupID(ctx, i.clusterID,
				i.instanceGroupID, 0, 0, nil)
			if err != nil {
				logger.Errorf(ctx, "get instance by instanceGroupID %s failed:%v", i.instanceGroupID, err)
				return fmt.Errorf("get instance by instanceGroupID %s failed:%v", i.instanceGroupID, err)
			}

			existInstances := make(map[string]struct{})
			for _, instance := range resp.Page.List {
				existInstances[instance.Spec.CCEInstanceID] = struct{}{}
			}

			if len(targetInstanceID) > 0 {
				for _, instanceID := range targetInstanceID {
					if _, found := existInstances[instanceID]; found {
						flag = true
						logger.Errorf(ctx, "instance %s should be remove in %s, but still exist", instanceID, i.instanceGroupID)
						continue NextLoop
					}
				}
			}
			if !flag {
				logger.Infof(ctx, "instance remove out succeed")
				return nil
			}
		case <-timer.C:
			logger.Errorf(ctx, "timeout waiting instance move out finished")
			return errors.New("timeout waiting instance move finished")
		}
	}
}

func (i *InstanceGroup) GetBaseParams() *BaseParams {
	return &i.baseParams
}

func (i *InstanceGroup) AreTasksCompleted(ctx context.Context) (err error) {
	if i.clusterID == "" {
		err = errors.New("the clusterID is empty")
		return err
	}
	if i.instanceGroupID == "" {
		err = errors.New("the instanceGroupID is empty")
		return err
	}
	baseParams := i.GetBaseParams()
	cceHostClient := i.baseParams.baseClient.CCEHostClient

	listTaskRes, listTaskErr := cceHostClient.ListTasks(ctx, ccetypes.TaskTypeInstanceGroupReplicas, ccev2.ListTaskOption{
		TargetID: i.instanceGroupID,
	}, nil)
	if listTaskErr != nil {
		err = fmt.Errorf("list task by instanceGroupID %s failed:%v", i.instanceGroupID, listTaskErr)
		return err
	}

	for _, task := range listTaskRes.Page.Items {
		logger.Infof(ctx, "check task %s phase", task.ID)
		waitErr := WaitForFunc(ctx, func(ctx context.Context) error {
			//当前 task 是否已经完成
			taskRes, getErr := cceHostClient.GetTask(ctx, ccetypes.TaskTypeInstanceGroupReplicas, task.ID, nil)
			if getErr != nil {
				return fmt.Errorf("get task by taskID %s failed:%v", task.ID, getErr)
			}

			if taskRes.Task.Phase != string(ccetypes.TaskPhaseFinished) && taskRes.Task.Phase != string(ccetypes.TaskPhaseAborted) {
				return fmt.Errorf("task %s phase is %s, not completed", task.ID, taskRes.Task.Phase)
			}

			logger.Infof(ctx, "task %s phase is %s, completed", task.ID, taskRes.Task.Phase)
			return nil
		}, baseParams.checkInterval, baseParams.checkTimeout)

		if waitErr != nil {
			err = fmt.Errorf("wait task %s completed failed:%v", task.ID, waitErr)
			return err
		}

		logger.Infof(ctx, "task %s phase is completed,continue check next task", task.ID)
	}

	logger.Infof(ctx, "all tasks of instanceGroupID %s are completed", i.instanceGroupID)
	return nil

}
