// Copyright 2022 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2022/11/10 164:24:00, by z<PERSON><PERSON><PERSON>@baidu.com, create
*/

package resource

import (
	"context"
	"errors"
	"fmt"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/serializer"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/network/eni-resource-controller/api/v1alpha1"
)

const (
	ApiServer         = "kube-apiserver"
	Etcd              = "etcd"
	ControllerManager = "kube-controller-manager"
	ExternalAuditor   = "kube-external-auditor"
	KubeScheduler     = "kube-scheduler"
)

var masterPlugins = map[string]int32{
	ApiServer:         3,
	ControllerManager: 3,
	ExternalAuditor:   2,
	KubeScheduler:     3,
}

type MasterComponent struct {
	client    kubernetes.Interface
	namespace string
}

func NewMasterComponent(ctx context.Context, namespace string, client kubernetes.Interface) (*MasterComponent, error) {
	if namespace == "" {
		return nil, errors.New("the namespace is nil")
	}
	return &MasterComponent{
		namespace: namespace,
		client:    client,
	}, nil
}

func (m *MasterComponent) CheckMasterComponentReady(ctx context.Context) (bool, error) {
	// 检查组件api server, controllerManager, externalAuditor, kubeScheduler是否ready
	for key, val := range masterPlugins {
		deployment, err := m.client.AppsV1().Deployments(m.namespace).Get(ctx, key, metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "get master plugin %s failed:%v", key, err)
			return false, fmt.Errorf("get master plugin %s failed:%v", key, err)
		}

		if deployment.Status.ReadyReplicas != val {
			logger.Errorf(ctx, "deployment %s/%s replicas is %d", m.namespace,
				key, deployment.Status.ReadyReplicas)
			return false, fmt.Errorf("deployment %s/%s replicas is %d", m.namespace,
				key, deployment.Status.ReadyReplicas)
		}
	}

	// 检查etcd组件是否ready，etcd由sts创建
	// 检查etcd-0, etcd-1, etcd-2是否ready
	for i := 0; i < 3; i++ {
		name := fmt.Sprintf(Etcd+"-"+m.namespace+"-%d", i)
		etcd, err := m.client.AppsV1().StatefulSets(m.namespace).Get(ctx, name, metav1.GetOptions{})
		if err != nil {
			return false, fmt.Errorf("get etcd %s/%s failed:%v", m.namespace, name, err)
		}
		if etcd.Status.ReadyReplicas != 1 {
			return false, fmt.Errorf("etcd is not ready, the %s replica is %d", name, etcd.Status.ReadyReplicas)
		}
	}
	return true, nil
}

func (m *MasterComponent) CheckMasterComponentHealthy(ctx context.Context, kubeconfig []byte) error {
	restConfig, err := clientcmd.RESTConfigFromKubeConfig(kubeconfig)
	if err != nil {
		logger.Errorf(ctx, "RESTConfigFromKubeConfig failed: %v", err)
		return err
	}
	restConfig.APIPath = "/"
	restConfig.GroupVersion = &v1alpha1.GroupVersion
	restConfig.NegotiatedSerializer = serializer.WithoutConversionCodecFactory{CodecFactory: scheme.Codecs}
	restConfig.UserAgent = rest.DefaultKubernetesUserAgent()
	// 和 controller worker 数量一致
	restConfig.QPS = 400
	restConfig.Burst = 400
	restClient, err := rest.RESTClientFor(restConfig)
	if err != nil {
		logger.Errorf(ctx, "failed create rest client: %v", err)
		return err
	}
	res, err := restClient.Get().RequestURI("/readyz?verbose").Do(ctx).Raw()
	if err != nil {
		logger.Errorf(ctx, "get the current status of the master components failed:%v", err)
		return err
	}
	if strings.Contains(string(res), "etcd ok") && strings.Contains(string(res), "apiservice-openapi-controller ok") {
		return nil
	}
	return fmt.Errorf("the current status of the master components is not healthy:%s", string(res))
}
