package resource

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gopkg.in/yaml.v2"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	DefaultCheckInterval         = time.Duration(2) * time.Second
	DefaultCheckTimeout          = time.Duration(8) * time.Minute
	DefaultWorkflowCheckInterval = time.Duration(10) * time.Second
	DefaultWorkflowCheckTimeout  = time.Duration(5) * time.Minute
)

type Checker interface {
	CheckResource(context.Context) error
	GetBaseParams() *BaseParams
}

type BaseParams struct {
	checkInterval time.Duration // 等待间隔时间
	checkTimeout  time.Duration // 等待逻辑的超时时间

	message    string
	baseClient *cases.BaseClient
}

func WaitForResourceReady(ctx context.Context, checker Checker) (err error) {
	baseParams := checker.GetBaseParams()
	timeoutCtx, cancel := context.WithTimeout(ctx, baseParams.checkTimeout)

	wait.UntilWithContext(timeoutCtx, func(ctx context.Context) {
		checkErr := checker.CheckResource(ctx)
		if checkErr == nil {
			cancel()
			return
		}
		logger.Warnf(ctx, "check resource failed: %v", checkErr)
	}, baseParams.checkInterval)

	if errors.Is(timeoutCtx.Err(), context.DeadlineExceeded) {
		err = fmt.Errorf("check resource timeout for %s: %s", baseParams.checkTimeout.String(), baseParams.message)
		return
	}
	logger.Infof(ctx, "check resource success")
	return
}

func Init(checker Checker, baseClient *cases.BaseClient) (err error) {
	baseParams := checker.GetBaseParams()
	if baseParams.checkInterval == 0 {
		baseParams.checkInterval = DefaultCheckInterval
	}
	if baseParams.checkTimeout == 0 {
		baseParams.checkTimeout = DefaultCheckTimeout
	}
	if baseClient == nil {
		err = errors.New("baseClient is nil")
		return
	}
	baseParams.baseClient = baseClient
	return
}

func WaitForWorkflowPhase(ctx context.Context, cceClient ccev2.Interface, clusterID, workflowID string, targetPhase ccetypes.WorkflowPhase) (err error) {
	timeoutCtx, cancel := context.WithTimeout(ctx, DefaultWorkflowCheckTimeout)

	wait.UntilWithContext(timeoutCtx, func(ctx context.Context) {
		resp, getErr := cceClient.GetWorkflow(ctx, clusterID, workflowID, &bce.SignOption{})
		if getErr != nil {
			logger.Errorf(ctx, "failed to get worflow of %s/%s: %v", clusterID, workflowID, getErr.Error())
			cancel()
			return
		}
		if resp.Workflow.Status.WorkflowPhase == targetPhase {
			logger.Infof(ctx, "the phase of workflow %s is %s as excepted", workflowID, targetPhase)
			cancel()
			return
		}
		logger.Warnf(ctx, "the phase of workflow %s is %s, expect: %s, wait", workflowID, resp.Workflow.Status.WorkflowPhase, targetPhase)
	}, DefaultWorkflowCheckInterval)
	if errors.Is(timeoutCtx.Err(), context.DeadlineExceeded) {
		err = fmt.Errorf("timeout waiting for workflow %s phase to %s", workflowID, targetPhase)
		return
	}
	return
}

func WaitForFunc(ctx context.Context, f func(context.Context) error, interval, timeout time.Duration) (err error) {
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	wait.UntilWithContext(timeoutCtx, func(ctx context.Context) {
		checkErr := f(ctx)
		if checkErr == nil {
			cancel()
			return
		}
		logger.Warnf(ctx, "WaitForFunc failed, function call err: %v", checkErr)
	}, interval)
	if errors.Is(timeoutCtx.Err(), context.DeadlineExceeded) {
		err = fmt.Errorf("WaitForFunc timeout for %s", timeout.String())
		return
	}
	return
}

// WaitPodsRunning - 等待 PodRunning
func WaitPodsRunning(ctx context.Context, k8sClient kubernetes.Interface, namespace string, matchLabels map[string]string) (err error) {
	if k8sClient == nil {
		err = errors.New("k8sClient is nil")
		return
	}

	if namespace == "" {
		err = errors.New("namespace is empty")
		return
	}

	if matchLabels == nil {
		err = errors.New("matchLabels is nil")
		return
	}

	err = WaitForFunc(ctx, func(context.Context) error {
		// 构建 Label
		labelSelector := metav1.LabelSelector{
			MatchLabels: matchLabels,
		}
		labelSelectorStr := metav1.FormatLabelSelector(&labelSelector)
		pods, listErr := k8sClient.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{
			LabelSelector: labelSelectorStr,
		})
		if listErr != nil {
			return fmt.Errorf("list pods failed, retry: %v", listErr)
		}
		// 不存在
		if len(pods.Items) == 0 {
			return errors.New("pods not exists, retry")
		}
		// Pod != Running
		// e.g.
		// containerStatuses:
		// - containerID: docker://18a666d6c54b24332ac4493f55b829176fc734e422938222e99b26fde898d7f4
		//   state:
		// 	   running:
		// 	     startedAt: "2020-08-01T10:07:15Z"
		flag := true
		for _, pod := range pods.Items {
			for _, containerStatus := range pod.Status.ContainerStatuses {
				if containerStatus.State.Running == nil {
					flag = false
					return fmt.Errorf("Pods.Container %s not running, retry", pod.GetName())
				}
			}

			if !flag {
				break
			}
		}

		if !flag {
			return errors.New("pods not running, retry")
		}
		logger.Infof(ctx, "Pods running")
		return nil
	}, time.Second*10, time.Minute*10)

	if err != nil {
		logger.Errorf(ctx, "pods not running")
	}
	return
}

func GetAPIVersionFromYAMLString(yamlStr string) string {
	type K8sObject struct {
		APIVersion string `yaml:"apiVersion"`
	}

	obj := K8sObject{}
	err := yaml.Unmarshal([]byte(yamlStr), &obj)
	if err != nil {
		fmt.Printf("Unmarshal yaml failed: %v\n", err.Error())
		return ""
	}

	// API version is in the format "group/version"
	// For core group (""), the format is "version"
	return obj.APIVersion
}
