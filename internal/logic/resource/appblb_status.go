// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/03 14:28:00, by <EMAIL>, create
*/

package resource

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

type AppBLBStatus struct {
	baseParams BaseParams
	blb        string
	status     string
	BLBName    string
}

func (k *AppBLBStatus) NewAppBLBStatus(ctx context.Context, baseClient *cases.BaseClient, blb string, status string) error {
	if err := Init(k, baseClient); err != nil {
		logger.Errorf(ctx, "NewAppBLBStatus failed: %s", err)
		return err
	}
	k.baseParams.message = fmt.Sprintf("Timeout to wait for AppBLB %s status reach %s", blb, status)
	k.blb = blb
	k.status = status
	return nil
}

func (k *AppBLBStatus) GetBaseParams() *BaseParams {
	return &k.baseParams
}

// SetStatus BLBStatus包括 creating:创建中 available:运行中 updating:更新中 paused:已欠费 unavailable:暂不可用 deleted:已删除
func (k *AppBLBStatus) SetStatus(status string) {
	k.status = status
	k.baseParams.message = fmt.Sprintf("Timeout to wait for AppBLB %s status reach %s", k.blb, status)
}

func (k *AppBLBStatus) CheckResource(ctx context.Context) error {
	status, err := k.GetAppBLBStatus(ctx, k.blb)
	if err != nil {
		if k.status == "deleted" &&
			(strings.Contains(err.Error(), "InstanceNotFound") || strings.Contains(err.Error(), "NoSuchObject")) {
			return nil
		}
		return err
	}
	if status != k.status {
		return fmt.Errorf("BLB status: %s not as expected: %s", status, k.status)
	}
	return nil
}

// 获取 appblb 的 状态
func (k *AppBLBStatus) GetAppBLBStatus(ctx context.Context, blb string) (string, error) {
	blbInfo, err := k.baseParams.baseClient.AppBLBClient.DescribeAppLoadBalancerByID(ctx, blb, nil)
	if err != nil {
		logger.Errorf(ctx, "%s AppBLB DescribeAppLoadBalancerByID failed: %s", blb, err)
		return "", err
	}
	logger.Infof(ctx, "%s AppBLB DescribeAppLoadBalancerByID success: %s", blb, utils.ToJSON(blbInfo))

	// 检查blb是否存在
	if blbInfo == nil {
		logger.Errorf(ctx, "%s AppBLB is not exist", blb)
		return "", fmt.Errorf("%s AppBLB is not exist", blb)
	}
	if k.BLBName == "" {
		k.BLBName = blbInfo.Name
	}
	status := string(blbInfo.Status)
	logger.Infof(ctx, "%s AppBLB status is %s", blb, status)
	return status, nil
}

// GetAppBLBBackends 返回AppBLB后端第1个IP组内的IP列表（这是PodBackend LB Service的实现方法）
func (k *AppBLBStatus) GetAppBLBBackends(ctx context.Context) ([]string, error) {
	// 获取 AppBLB 的 IP Group 列表
	ipGroupsResp, err := k.baseParams.baseClient.AppBLBClient.ListIPGroups(ctx, k.blb, nil, nil)
	if err != nil {
		return nil, err
	}
	if ipGroupsResp == nil || ipGroupsResp.AppIPGroupList == nil {
		return nil, errors.New("IP Group Response is nil or AppIPGroupList is nil")
	}
	if len(ipGroupsResp.AppIPGroupList) == 0 {
		err := errors.New("appBLB has no IP group")
		return nil, err
	}

	// AppBLB 的 IP Group 列表应该只有一个 获取这个 IP Group 下的 IP 列表
	IPGroupID := ipGroupsResp.AppIPGroupList[0].ID
	args := &appblb.ListIPGroupMemberArgs{
		IPGroupID: IPGroupID,
	}
	ipGroupMembersResp, err := k.baseParams.baseClient.AppBLBClient.ListIPGroupMember(ctx, k.blb, args, nil)
	if err != nil {
		return nil, err
	}
	if ipGroupMembersResp == nil || ipGroupMembersResp.MemberList == nil {
		return nil, errors.New("response of AppBLBClient.ListIPGroupMember is nil or response.MemberList is nil")
	}
	if len(ipGroupMembersResp.MemberList) == 0 { // 空列表不返回错误, 可能只是真的没有后端服务
		logger.Warnf(ctx, "appBLB ip group %s has no members", IPGroupID)
	}

	// 将后端 IP 列表返回
	backendPodIPs := make([]string, 0)
	for _, member := range ipGroupMembersResp.MemberList {
		backendPodIPs = append(backendPodIPs, member.IP)
	}

	return backendPodIPs, nil
}

// UpdateAppBLB 更新blb name和description
func (k *AppBLBStatus) UpdateAppBLB(ctx context.Context, name, descption string) error {
	args := &appblb.UpdateAppLoadBalancerArgs{
		Name:        name,
		Desc:        descption,
		AllowDelete: true,
	}

	err := k.baseParams.baseClient.AppBLBClient.UpdateAppLoadBalancer(ctx, k.blb, args, nil)
	if err != nil {
		logger.Errorf(ctx, "blb %s update failed: %v", err.Error())
		return err
	}

	return nil
}

// DeleteAppBLBListener 删除blb指定端口监听
func (k *AppBLBStatus) DeleteAppBLBListener(ctx context.Context, port int) error {
	args := &appblb.DeleteAppListenersArgs{
		PortList: []int{
			port,
		},
	}

	err := k.baseParams.baseClient.AppBLBClient.DeleteAppListeners(ctx, k.blb, args, nil)
	if err != nil {
		logger.Errorf(ctx, "delete blb %s listener failed: %v", k.blb, err.Error())
		return err
	}

	logger.Infof(ctx, "delete blb listener port %d success", port)

	return nil
}

// AddAppHTTPListener 创建blb指定端口http监听
func (k *AppBLBStatus) AddAppHTTPListener(ctx context.Context, port int) error {
	args := &appblb.CreateAppHTTPListenerArgs{
		ListenerPort: port,
		Scheduler:    appblb.SchedulerTypeRoundRobin,
	}

	err := k.baseParams.baseClient.AppBLBClient.CreateAppHTTPListener(ctx, k.blb, args, nil)
	if err != nil {
		logger.Errorf(ctx, "create app http listener failed: %v", err.Error())
		return err
	}

	logger.Infof(ctx, "create app http listener success")

	return nil
}

// CheckAppBLBNameAndDescNotChanged 校验blb的name和desc 1min内没有变更
func (k *AppBLBStatus) CheckAppBLBNameAndDescNotChanged(ctx context.Context, name, descption string) error {
	timer := time.NewTimer(1 * time.Minute)
	defer timer.Stop()
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-timer.C:
			logger.Infof(ctx, "blb name and description is not changed")
			return nil
		case <-ticker.C:
			blbInfo, err := k.baseParams.baseClient.AppBLBClient.DescribeAppLoadBalancerByID(ctx, k.blb, nil)
			if err != nil {
				logger.Errorf(ctx, "%s AppBLB DescribeAppLoadBalancerByID failed: %s", k.blb, err)
				return err
			}
			if blbInfo.Name == name && blbInfo.Desc == descption {
				continue
			}
			return fmt.Errorf("blb %s's name or description changed", k.blb)
		}
	}
}
