/* k8s_statefulset.go */
/*
modification history
--------------------
2022/10/8, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
*/

package resource

import (
	"context"
	"fmt"

	v1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

type K8SStatefulSet struct {
	baseParams     BaseParams
	namespace      string
	name           string
	wantedReplicas int
}

func (k *K8SStatefulSet) NewK8SStatefulSet(ctx context.Context, baseClient *cases.BaseClient, namespace string, name string) error {
	if err := Init(k, baseClient); err != nil {
		logger.Errorf(ctx, "NewK8SStatefulSet failed: %s", err)
		return err
	}
	k.baseParams.message = fmt.Sprintf("Timeout to wait for K8S_StatefulSet %s/%s ready", namespace, name)
	k.namespace = namespace
	k.name = name
	k.wantedReplicas = -1
	return nil
}

// SetWantedReplicas  期望负载数量设置
func (k *K8SStatefulSet) SetWantedReplicas(replicas int) {
	k.wantedReplicas = replicas
}

func (k *K8SStatefulSet) GetBaseParams() *BaseParams {
	return &k.baseParams
}

func (k *K8SStatefulSet) CheckResource(ctx context.Context) error {
	sts, err := k.baseParams.baseClient.K8SClient.AppsV1().StatefulSets(k.namespace).Get(ctx, k.name, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "Get statefulset %s/%s failed: %s", k.namespace, k.name, err)
		return err
	}

	var wantedReplicas int32
	if k.wantedReplicas > 0 {
		wantedReplicas = int32(k.wantedReplicas)
	} else {
		wantedReplicas = *sts.Spec.Replicas
	}

	if wantedReplicas != sts.Status.ReadyReplicas {
		return fmt.Errorf("statefulset %s/%s replicas is %d/%d", k.namespace, k.name, sts.Status.ReadyReplicas, wantedReplicas)
	}
	return nil
}

func (k *K8SStatefulSet) GetPods(ctx context.Context) (*corev1.PodList, error) {
	// Get the statefulset.
	sts, err := k.baseParams.baseClient.K8SClient.AppsV1().StatefulSets(k.namespace).Get(ctx, k.name, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "Get statefulset %s/%s failed: %s", k.namespace, k.name, err)
		return nil, err
	}

	// Create a label selector from the Deployment's selector.
	ls := &metav1.LabelSelector{
		MatchLabels: sts.Spec.Selector.MatchLabels,
	}
	selector, err := metav1.LabelSelectorAsSelector(ls)
	if err != nil {
		logger.Errorf(ctx, "Failed to convert selector:", err)
		return nil, err
	}

	// List Pods using the label selector.
	pods, err := k.baseParams.baseClient.K8SClient.CoreV1().Pods(k.namespace).List(ctx, metav1.ListOptions{
		LabelSelector: selector.String(),
	})
	if err != nil {
		logger.Errorf(ctx, "Failed to list pods:", err)
		return nil, err
	}

	// check the pod owner (maybe some pod use the same label)
	items := make([]corev1.Pod, 0, len(pods.Items))
	for _, pod := range pods.Items {
		for _, ownerRef := range pod.OwnerReferences {
			if ownerRef.Kind == "StatefulSet" && ownerRef.Name == k.name {
				items = append(items, pod)
				break
			}
		}
	}
	pods.Items = items

	return pods, err
}

func (k *K8SStatefulSet) ScaleUpOrDown(ctx context.Context, replicas int) (*v1.StatefulSet, error) {
	// Get the statefulset.
	sts, err := k.baseParams.baseClient.K8SClient.AppsV1().StatefulSets(k.namespace).Get(ctx, k.name, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "Get statefulset %s/%s failed: %s", k.namespace, k.name, err)
		return nil, err
	}
	newReplicaCount := int32(replicas)
	sts.Spec.Replicas = &newReplicaCount
	sts, err = k.baseParams.baseClient.K8SClient.AppsV1().StatefulSets("default").Update(ctx, sts, metav1.UpdateOptions{})
	if err != nil {
		logger.Errorf(ctx, "update statefulset failed: %v", err.Error())
		return nil, err
	}
	k.SetWantedReplicas(replicas)
	return sts, nil
}
