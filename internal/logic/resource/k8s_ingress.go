/* k8s_ingress.go */
/*
modification history
--------------------
2022/7/20, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
*/

package resource

import (
	"context"
	"fmt"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

type K8SIngress struct {
	baseParams BaseParams
	namespace  string
	name       string
	yamlStr    string
}

func (k *K8SIngress) NewK8SIngress(ctx context.Context, baseClient *cases.BaseClient, namespace, name, yamlStr string) error {
	if err := Init(k, baseClient); err != nil {
		logger.Errorf(ctx, "NewK8SIngress failed: %s", err)
		return err
	}
	k.baseParams.message = fmt.Sprintf("Timeout to wait for K8S_Ingress %s/%s ready", namespace, name)
	k.namespace = namespace
	k.name = name
	k.yamlStr = yamlStr
	return nil
}

func (k *K8SIngress) GetBaseParams() *BaseParams {
	return &k.baseParams
}

func (k *K8SIngress) CheckResource(ctx context.Context) error {
	if _, err := k.GetIngressEIP(ctx, k.namespace, k.name); err != nil {
		return err
	}
	return nil
}

// GetIngressEIP 获取 ingress 的 eip
func (k *K8SIngress) GetIngressEIP(ctx context.Context, namespace, name string) (string, error) {
	apiVersion := GetAPIVersionFromYAMLString(k.yamlStr)
	if apiVersion == "networking.k8s.io/v1" {
		ingress, err := k.baseParams.baseClient.K8SClient.
			NetworkingV1().
			Ingresses(namespace).
			Get(ctx, name, metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "Get Ingress %s/%s failed: %s", namespace, name, err)
			return "", err
		}
		logger.Infof(ctx, "Get Ingress success: %s", utils.ToJSON(ingress))

		ingresses := ingress.Status.LoadBalancer.Ingress
		if len(ingresses) == 0 {
			logger.Errorf(ctx, "%s/%s Eip is empty", namespace, name)
			return "", fmt.Errorf("%s/%s Eip is empty", namespace, name)
		}
		eip := ingress.Status.LoadBalancer.Ingress[0].IP
		logger.Infof(ctx, "EIP: %s", eip)
		return eip, nil
	} else {
		ingress, err := k.baseParams.baseClient.K8SClient.
			ExtensionsV1beta1().
			Ingresses(namespace).
			Get(ctx, name, metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "Get Ingress %s/%s failed: %s", namespace, name, err)
			return "", err
		}
		logger.Infof(ctx, "Get Ingress success: %s", utils.ToJSON(ingress))

		ingresses := ingress.Status.LoadBalancer.Ingress
		if len(ingresses) == 0 {
			logger.Errorf(ctx, "%s/%s Eip is empty", namespace, name)
			return "", fmt.Errorf("%s/%s Eip is empty", namespace, name)
		}
		eip := ingress.Status.LoadBalancer.Ingress[0].IP
		logger.Infof(ctx, "EIP: %s", eip)
		return eip, nil
	}
}
