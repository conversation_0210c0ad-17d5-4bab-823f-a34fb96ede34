// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2020/09/03 14:28:00, by <EMAIL>, create
*/

package resource

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

type CDSStatus struct {
	baseParams BaseParams
	cdsID      string
	status     bcc.VolumeStatus
}

func (k *CDSStatus) NewCDSStatus(ctx context.Context, baseClient *cases.BaseClient, cdsID string, status bcc.VolumeStatus) error {
	if err := Init(k, baseClient); err != nil {
		logger.Errorf(ctx, "NewCDSStatus failed: %s", err)
		return err
	}
	k.baseParams.message = fmt.Sprintf("Timeout to wait for CDS %s status reach %s", cdsID, status)
	k.cdsID = cdsID
	k.status = status
	return nil
}

func (k *CDSStatus) GetBaseParams() *BaseParams {
	return &k.baseParams
}

func (k *CDSStatus) SetStatus(status bcc.VolumeStatus) {
	k.status = status
	k.baseParams.message = fmt.Sprintf("Timeout to wait for CDS %s status reach %s", k.cdsID, status)
}

func (k *CDSStatus) CheckResource(ctx context.Context) error {
	status, err := k.GetCDSStatus(ctx, k.cdsID)
	if err != nil {
		return err
	}
	if status != k.status {
		return fmt.Errorf("CDS status: %s not as expected: %s", status, k.status)
	}
	return nil
}

// 获取 cds 的 状态
func (k *CDSStatus) GetCDSStatus(ctx context.Context, cdsID string) (bcc.VolumeStatus, error) {
	cdsInfo, err := k.baseParams.baseClient.BCCClient.DescribeVolume(ctx, cdsID, nil)
	if err != nil {
		logger.Errorf(ctx, "%s CDS DescribeVolume failed: %s", cdsID, err)
		return "", err
	}
	logger.Infof(ctx, "%s CDS DescribeVolume success: %s", cdsID, utils.ToJSON(cdsInfo))
	logger.Infof(ctx, "%s CDS status is %s", cdsID, cdsInfo.Status)
	return cdsInfo.Status, nil
}

// 获取 cds 的 size
func (k *CDSStatus) GetCDSSize(ctx context.Context, cdsID string) (string, error) {
	cdsInfo, err := k.baseParams.baseClient.BCCClient.DescribeVolume(ctx, cdsID, nil)
	if err != nil {
		logger.Errorf(ctx, "%s CDS DescribeVolume failed: %s", cdsID, err)
		return "", err
	}
	logger.Infof(ctx, "%s CDS DescribeVolume success: %s", cdsID, utils.ToJSON(cdsInfo))

	size := strconv.Itoa(cdsInfo.DiskSizeInGB)
	logger.Infof(ctx, "%s CDS size is %s", cdsID, size)
	return size, nil
}

// WaitCDSDeleted 检查 cds 是否删除成功
func (k *CDSStatus) WaitCDSDeleted(ctx context.Context, cdsID string) (bool, error) {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()
	timer := time.NewTimer(2 * time.Minute)
	defer timer.Stop()
	for {
		select {
		case <-ticker.C:
			flag := false
			volumeList, err := k.baseParams.baseClient.BCCClient.ListVolumes(ctx, "", "", nil)
			if err != nil {
				logger.Errorf(ctx, "ListVolumes failed: %s, retry", err)
				continue
			}
			if len(volumeList.Volumes) == 0 {
				return false, nil
			}
			for _, volume := range volumeList.Volumes {
				if volume.ID == cdsID {
					flag = true
					logger.Warnf(ctx, "cds %s is still exist, wait", cdsID)
					break
				}
			}
			if !flag {
				return false, nil
			}
		case <-timer.C:
			logger.Errorf(ctx, "timeout waiting for cds %s deleted", cdsID)
			return true, fmt.Errorf("timeout waiting for cds %s deleted", cdsID)
		}
	}
}
