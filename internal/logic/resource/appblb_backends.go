package resource

import (
	"context"
	"errors"
	"fmt"
	"sort"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

type AppBLBBackends struct {
	baseParams                    BaseParams
	blbID                         string
	clusterID                     string
	isExternalTrafficePolicyLocal bool
	k8sPod                        K8SPod
}

func (k *AppBLBBackends) NewAppBLBBackends(ctx context.Context, baseClient *cases.BaseClient, blbID string, clusterID string,
	isExternalTrafficePolicyLocal bool, k8sPod K8SPod) error {
	if err := Init(k, baseClient); err != nil {
		logger.Errorf(ctx, "NewAppBLBStatus failed: %s", err)
		return err
	}
	k.baseParams.message = fmt.Sprintf("Timeout to wait for AppBLB %s backends comparsion", blbID)
	k.blbID = blbID
	k.clusterID = clusterID
	k.isExternalTrafficePolicyLocal = isExternalTrafficePolicyLocal
	k.k8sPod = k8sPod
	return nil
}

func (k *AppBLBBackends) GetBaseParams() *BaseParams {
	return &k.baseParams
}

func (k *AppBLBBackends) CheckResource(ctx context.Context) error {
	var workers []string
	var backends []string

	if k.isExternalTrafficePolicyLocal {
		// 对于 Service ExternalTrafficPolicy=Local 的情况
		nodeList, err := k.k8sPod.GetPodNodeList(ctx)
		if err != nil {
			return err
		}

		workers = make([]string, 0)
		for key := range nodeList {
			workers = append(workers, key)
		}
	} else {
		// 对于 Service ExternalTrafficPolicy=Cluster 的情况
		var err error
		workers, err = k.getClusterWorkerIPs(ctx, k.clusterID)
		if err != nil {
			return err
		}
	}

	backends, err := k.GetAppBLBBackendsOfServerGroup(ctx)
	if err != nil {
		return err
	}
	if backends == nil {
		return errors.New("appBLBBackends is nil")
	}

	logger.Warnf(ctx, "BLB Backends %v", backends)
	logger.Warnf(ctx, "Worker %v", workers)

	isBackendWorkersEqual := checkListEqual(workers, backends)
	if !isBackendWorkersEqual {
		err = errors.New("blb backends not equal to cluster workers")
		return err
	}
	return nil
}

// GetAppBLBBackends 返回AppBLB后端第1个IP组内的IP列表（这是PodBackend LB Service的实现方法）
func (k *AppBLBBackends) GetAppBLBBackends(ctx context.Context) ([]string, error) {
	// 获取 AppBLB 的 IP Group 列表
	ipGroupsResp, err := k.baseParams.baseClient.AppBLBClient.ListIPGroups(ctx, k.blbID, nil, nil)
	if err != nil {
		return nil, err
	}
	if ipGroupsResp == nil || ipGroupsResp.AppIPGroupList == nil {
		return nil, errors.New("IP Group Response is nil or AppIPGroupList is nil")
	}
	if len(ipGroupsResp.AppIPGroupList) == 0 {
		err := errors.New("appBLB has no IP group")
		return nil, err
	}

	// AppBLB 的 IP Group 列表应该只有一个 获取这个 IP Group 下的 IP 列表
	IPGroupID := ipGroupsResp.AppIPGroupList[0].ID
	args := &appblb.ListIPGroupMemberArgs{
		IPGroupID: IPGroupID,
	}
	ipGroupMembersResp, err := k.baseParams.baseClient.AppBLBClient.ListIPGroupMember(ctx, k.blbID, args, nil)
	if err != nil {
		return nil, err
	}
	if ipGroupMembersResp == nil || ipGroupMembersResp.MemberList == nil {
		return nil, errors.New("response of AppBLBClient.ListIPGroupMember is nil or response.MemberList is nil")
	}
	if len(ipGroupMembersResp.MemberList) == 0 { // 空列表不返回错误, 可能只是真的没有后端服务
		logger.Warnf(ctx, "appBLB ip group %s has no members", IPGroupID)
	}

	// 将后端 IP 列表返回
	backendPodIPs := make([]string, 0)
	for _, member := range ipGroupMembersResp.MemberList {
		backendPodIPs = append(backendPodIPs, member.IP)
	}

	return backendPodIPs, nil
}

// GetAppBLBBackendsOfServerGroup 返回AppBLB后端第1个sg组内的node IP列表（这是NodeBackend LB Service的实现方法）
func (k *AppBLBBackends) GetAppBLBBackendsOfServerGroup(ctx context.Context) ([]string, error) {
	// 获取 AppBLB 的 Server Group 列表
	serverGroupsResp, err := k.baseParams.baseClient.AppBLBClient.DescribeAppServerGroup(ctx, k.blbID, nil, nil)
	if err != nil {
		return nil, err
	}
	if serverGroupsResp == nil || serverGroupsResp.AppServerGroupList == nil {
		return nil, errors.New("server group response is nil or AppServerGroupList is nil")
	}
	if len(serverGroupsResp.AppServerGroupList) == 0 {
		err := errors.New("appBLB has no server group")
		return nil, err
	}

	// AppBLB 的 Server Group 列表应该只有一个 获取这个 Group 下的 node 的IP 列表
	serverGroupID := serverGroupsResp.AppServerGroupList[0].ID
	blbRsResp, err := k.baseParams.baseClient.AppBLBClient.DescribeAppServerGroupRS(ctx, k.blbID, serverGroupID, nil)
	if err != nil {
		return nil, err
	}
	if blbRsResp == nil || blbRsResp.BackendServerList == nil {
		return nil, errors.New("response of DescribeAppServerGroupRS is nil or response.BackendServerList is nil")
	}
	if len(blbRsResp.BackendServerList) == 0 { // 空列表不返回错误, 可能只是真的没有后端服务
		logger.Warnf(ctx, "appBLB server group %s has no backend server", serverGroupID)
	}

	// 将后端 IP 列表返回
	backendServerIPs := make([]string, 0)
	for _, backendServer := range blbRsResp.BackendServerList {
		backendServerIPs = append(backendServerIPs, backendServer.PrivateIP)
	}

	return backendServerIPs, nil
}

func (k *AppBLBBackends) getClusterWorkerIPs(ctx context.Context, clusterID string) ([]string, error) {
	listInstanceArgs := &ccev2.ListInstancesByPageParams{
		PageSize: 10000,
		PageNo:   1,
	}
	resp, err := k.baseParams.baseClient.CCEClient.ListInstancesByPage(ctx, clusterID, listInstanceArgs, nil)
	if err != nil {
		return nil, err
	}
	if resp == nil || resp.InstancePage == nil || resp.InstancePage.InstanceList == nil {
		return nil, errors.New("response of ListInstancesByPage is nil or resp.InstancePage is nil or resp.InstancePage.InstanceList is nil")
	}

	nodesIDs := make([]string, 0)
	for _, nodes := range resp.InstancePage.InstanceList {
		if nodes.Spec.ClusterRole == ccetypes.ClusterRoleNode && (nodes.Status.InstancePhase == ccetypes.InstancePhaseRunning ||
			nodes.Status.InstancePhase == "ready_scheduling_disabled") {
			nodesIDs = append(nodesIDs, nodes.Status.Machine.VPCIP)
		}
	}

	if len(nodesIDs) == 0 {
		logger.Warnf(ctx, "Worker number of cluster %s is 0", clusterID)
	}

	return nodesIDs, nil
}

func checkListEqual(one []string, two []string) bool {
	if one == nil && two == nil {
		return true
	}
	if len(one) != len(two) {
		return false
	}

	// 排序并对比元素是否相同
	sort.Strings(one)
	sort.Strings(two)
	for i, v := range one {
		if v != two[i] {
			return false
		}
	}
	return true
}
