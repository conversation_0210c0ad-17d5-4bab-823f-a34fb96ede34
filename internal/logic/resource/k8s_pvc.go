// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

/*
modification history
--------------------
2021/11/21 14:28:00, by z<PERSON><PERSON><PERSON>@baidu.com, create
*/

package resource

import (
	"context"
	"fmt"
	"time"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

type K8SPVC struct {
	baseParams BaseParams
	namespace  string
	pvcName    string
	status     v1.PersistentVolumeClaimPhase
}

func (k *K8SPVC) CheckResource(ctx context.Context) error {
	status, err := k.GetPVCStatus(ctx, k.pvcName)
	if err != nil {
		return err
	}
	if status != k.status {
		return fmt.Errorf("pvc status: %s not as expected: %s", status, k.status)
	}
	return nil
}

func (k *K8SPVC) SetStatus(status v1.PersistentVolumeClaimPhase) {
	k.status = status
	k.baseParams.message = fmt.Sprintf("Timeout to wait for pvc %s status reach %s", k.pvcName, status)
}

func (k *K8SPVC) GetBaseParams() *BaseParams {
	return &k.baseParams
}

func (k *K8SPVC) NewK8SPVC(ctx context.Context, baseClient *cases.BaseClient, namespace, pvcName string) error {
	if err := Init(k, baseClient); err != nil {
		logger.Errorf(ctx, "NewK8SPod failed: %s", err)
		return err
	}
	k.namespace = namespace
	k.pvcName = pvcName
	return nil
}

// GetPVCStatus 获取 pvc 的 状态
func (k *K8SPVC) GetPVCStatus(ctx context.Context, pvcName string) (v1.PersistentVolumeClaimPhase, error) {
	pvcInfo, err := k.baseParams.baseClient.K8SClient.CoreV1().PersistentVolumeClaims(k.namespace).Get(ctx, k.pvcName, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "get pvc %s failed: %s", pvcName, err)
		return "", err
	}
	logger.Infof(ctx, "get pvc %s success: %s", pvcName, utils.ToJSON(pvcInfo))
	logger.Infof(ctx, "pvc %s status is %s", pvcName, pvcInfo.Status.Phase)
	return pvcInfo.Status.Phase, nil
}

func (k *K8SPVC) WaitPVCreated(ctx context.Context) (*v1.PersistentVolumeClaim, error) {
	ticker := time.NewTicker(3 * time.Second)
	defer ticker.Stop()
	timer := time.NewTimer(5 * time.Minute)
	defer timer.Stop()
	for {
		select {
		case <-ticker.C:
			pvc, err := k.baseParams.baseClient.K8SClient.CoreV1().PersistentVolumeClaims(k.namespace).Get(ctx, k.pvcName, metav1.GetOptions{})
			if err != nil {
				logger.Errorf(ctx, "GetPersistentVolumeClaims %s/%s failed: %s", k.namespace, k.pvcName, err)
				return nil, err
			}
			logger.Infof(ctx, "GetPersistentVolumeClaims %s/%s, status:%s != %s", k.namespace, k.pvcName, pvc.Status.Phase, StatusBound)
			if pvc.Status.Phase == StatusBound {
				logger.Infof(ctx, "%s/%s volume name is:%s", pvc.Spec.VolumeName)
				return pvc, nil
			}
		case <-timer.C:
			logger.Errorf(ctx, "timeout waiting for pvc %s/%s bound", k.namespace, k.pvcName)
			return nil, fmt.Errorf("timeout waiting for pvc %s/%s bound", k.namespace, k.pvcName)
		}
	}
}
