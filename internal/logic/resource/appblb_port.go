package resource

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

type AppBLBPort struct {
	baseParams  BaseParams
	blbID       string
	backendPort int32
}

func (k *AppBLBPort) NewAppBLBPort(ctx context.Context, baseClient *cases.BaseClient, blbID string, port int32) error {
	if err := Init(k, baseClient); err != nil {
		logger.Errorf(ctx, "NewAppBLBStatus failed: %s", err)
		return err
	}
	k.baseParams.message = fmt.Sprintf("Timeout to wait for AppBLB %s port become %d", blbID, port)
	k.blbID = blbID
	k.backendPort = port
	return nil
}

func (k *AppBLBPort) GetBaseParams() *BaseParams {
	return &k.baseParams
}

func (k *AppBLBPort) CheckResource(ctx context.Context) error {
	port, err := k.GetAppBLBPort(ctx)
	if err != nil {
		return err
	}
	if port != k.backendPort {
		return fmt.Errorf("BLB port %d not equal to service nodeport %d", port, k.backendPort)
	}
	return nil
}

func (k *AppBLBPort) SetPort(port int32) {
	k.backendPort = port
	k.baseParams.message = fmt.Sprintf("Timeout to wait for AppBLB %s status become %d", k.blbID, port)
}

func (k *AppBLBPort) GetAppBLBPort(ctx context.Context) (int32, error) {
	blbInfo, err := k.baseParams.baseClient.AppBLBClient.DescribeAppLoadBalancerByID(ctx, k.blbID, nil)
	if err != nil {
		logger.Errorf(ctx, "%s AppBLB DescribeAppLoadBalancerByID failed: %s", k.blbID, err)
		return -1, err
	}
	logger.Infof(ctx, "%s AppBLB DescribeAppLoadBalancerByID success: %s", k.blbID, utils.ToJSON(blbInfo))

	// 检查blb是否存在
	if blbInfo == nil {
		logger.Errorf(ctx, "%s AppBLB is not exist", k.blbID)
		return -1, fmt.Errorf("%s AppBLB is not exist", k.blbID)
	}
	port := blbInfo.ListenerList[0].Port.IntVal
	logger.Infof(ctx, "%s AppBLB port is %s", k.blbID, port)
	return port, nil
}
