package kube

import (
	"bytes"
	"context"
	"fmt"
	"net/http"

	v1 "k8s.io/api/core/v1"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/remotecommand"
)

func (k *Client) ListPod(ctx context.Context, namespace string, options *ListOptions) (*v1.PodList, error) {
	return k.ClientSet.CoreV1().Pods(namespace).List(ctx, options.CreateListOptions())
}

// RemoteExec 对指定container执行命令并返回标准输出，it 为false
func (k *Client) RemoteExec(namespace, podName, containerName string, command []string) (stdout string, err error) {
	req := k.ClientSet.CoreV1().RESTClient().Post().Resource(string(v1.ResourcePods)).
		Namespace(namespace).
		Name(podName).
		SubResource("exec").
		Param("container", containerName).
		VersionedParams(&v1.PodExecOptions{
			Stdin:   false,
			Stdout:  true,
			Stderr:  true,
			TTY:     false,
			Command: command,
		}, scheme.ParameterCodec)
	exec, execErr := remotecommand.NewSPDYExecutor(k.RestConfig, http.MethodPost, req.URL())
	if execErr != nil {
		err = fmt.Errorf("exec cmd failed, %s", execErr.Error())
		return
	}
	var stdoutBuf, stderrBuf bytes.Buffer
	err = exec.Stream(remotecommand.StreamOptions{
		Stdin:  nil,
		Stdout: &stdoutBuf,
		Stderr: &stderrBuf,
	})
	if err != nil {
		err = fmt.Errorf("remote exec failed, err: %v, stderr: %s", err, stderrBuf.String())
		return
	}
	stdout = stdoutBuf.String()
	return
}
