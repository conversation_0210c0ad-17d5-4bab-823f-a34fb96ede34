package kube

import (
	"context"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func (k *Client) GetNode(ctx context.Context, name string, options *GetOptions) (*v1.Node, error) {
	return k.ClientSet.CoreV1().Nodes().Get(ctx, name, options.CreateGetOptions())
}

func (k *Client) ListNode(ctx context.Context, options *ListOptions) (*v1.NodeList, error) {
	return k.ClientSet.CoreV1().Nodes().List(ctx, options.CreateListOptions())
}

func (k *Client) ListNodeWithoutMaster(ctx context.Context, options *ListOptions) (*v1.NodeList, error) {
	noseList, err := k.ClientSet.CoreV1().Nodes().List(ctx, options.CreateListOptions())
	if err != nil {
		return nil, err
	}
	var nodeListWithoutMaster v1.NodeList
	for _, node := range noseList.Items {
		_, hasMasterLabel := node.Labels["node-role.kubernetes.io/master"]
		if !hasMasterLabel {
			nodeListWithoutMaster.Items = append(nodeListWithoutMaster.Items, node)
		}
	}
	return &nodeListWithoutMaster, nil
}

func (k *Client) UpdateNode(ctx context.Context, node *v1.Node) (*v1.Node, error) {
	return k.ClientSet.CoreV1().Nodes().Update(ctx, node, metav1.UpdateOptions{})
}
