package kube

/*
this client is mainly used for container network crds
*/
import (
	"context"
	"fmt"
	"strings"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

type Client struct {
	ClientSet  *kubernetes.Clientset
	RestConfig *rest.Config
	KubeConfig string
}

type ListOptions struct {
	ResourceVersion string
	Name            string
	LabelSelector   map[string]string
	FieldSelector   map[string]string
	ListContinue    string
	Limit           int64
	Watch           bool
}

// CreateListOptions 创建列表选项
func (options *ListOptions) CreateListOptions() (listOptions metav1.ListOptions) {
	if options.ResourceVersion != "" {
		listOptions.ResourceVersion = options.ResourceVersion
	}
	if options.Name != "" {
		fieldSelector := fmt.Sprintf("metadata.name=%s", options.Name)
		listOptions.FieldSelector = fieldSelector
	}
	if len(options.LabelSelector) > 0 {
		labelSelector := metav1.LabelSelector{MatchLabels: options.LabelSelector}
		listOptions.LabelSelector = metav1.FormatLabelSelector(&labelSelector)
	}
	if len(options.FieldSelector) > 0 {
		fieldSelectors := make([]string, 0, len(options.FieldSelector))
		for key, value := range options.FieldSelector {
			fieldSelectors = append(fieldSelectors, fmt.Sprintf("%s=%s", key, value))
		}
		listOptions.FieldSelector = strings.Join(fieldSelectors, ",")
	}
	if options.ListContinue != "" {
		listOptions.Continue = options.ListContinue
	}
	if options.Limit > 0 {
		listOptions.Limit = options.Limit
	}
	listOptions.Watch = options.Watch
	return
}

type GetOptions struct {
	ResourceVersion string
}

// CreateGetOptions 创建获取选项
func (options *GetOptions) CreateGetOptions() (getOptions metav1.GetOptions) {
	if options.ResourceVersion != "" {
		getOptions.ResourceVersion = options.ResourceVersion
	}
	return
}

type CheckOptions struct {
	Interval time.Duration
	Timeout  time.Duration
}

// NewClientByKubeConfig 创建 kube client by kube config
func NewClientByKubeConfig(kubeConfig string, timeout time.Duration, protobuf bool) (kubeClient *Client, err error) {
	var restConfig *rest.Config
	var clientSet *kubernetes.Clientset
	restConfig, err = clientcmd.RESTConfigFromKubeConfig([]byte(kubeConfig))
	if err != nil {
		err = fmt.Errorf("failed to create rest config from kube config: %v", err)
		return
	}
	restConfig.Timeout = time.Second * 30 // 默认超时时间
	if timeout > 0 {
		restConfig.Timeout = timeout
	}
	// 按需开启，提高性能
	if protobuf {
		restConfig.ContentType = "application/vnd.kubernetes.protobuf"
	}
	clientSet, err = kubernetes.NewForConfig(restConfig)
	if err != nil {
		err = fmt.Errorf("failed to create kube client set from rest config: %v", err)
		return
	}
	kubeClient = &Client{
		ClientSet:  clientSet,
		RestConfig: restConfig,
		KubeConfig: kubeConfig,
	}
	return
}

// NewClientByClusterID 创建 kube client by cluster id
func NewClientByClusterID(ctx context.Context, clusterID string, configType models.KubeConfigType, cce ccev2.Interface, timeout time.Duration,
	protobuf bool) (kubeClient *Client, err error) {
	logger.Infof(ctx, "Init kube client, kubeConfigType: %s", configType)

	var kubeConfigRes *ccev2.GetKubeConfigResponse
	kubeConfigRes, err = cce.GetAdminKubeConfig(ctx, clusterID, configType, nil)
	if err != nil {
		err = fmt.Errorf("ccev2.GetAdminKubeConfig failed: %v", err)
		return
	}
	kubeConfig := kubeConfigRes.KubeConfig
	kubeClient, err = NewClientByKubeConfig(kubeConfig, timeout, protobuf)
	return
}

// NewDynamicClient 创建自定义资源的客户端
func (k *Client) NewDynamicClient(group, version, resourceName string) (resource dynamic.NamespaceableResourceInterface, err error) {
	dynamicClient, err := dynamic.NewForConfig(k.RestConfig)
	if err != nil {
		err = fmt.Errorf("dynamic client failed, err: %v", err)
		return
	}
	resource = dynamicClient.Resource(schema.GroupVersionResource{
		Group:    group,
		Version:  version,
		Resource: resourceName,
	})
	return
}
