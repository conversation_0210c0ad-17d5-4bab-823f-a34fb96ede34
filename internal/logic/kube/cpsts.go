package kube

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/dynamic"
)

func (k *Client) CreateCpsts(ctx context.Context, psts *types.ClusterPodSubnetTopologySpread) (*types.ClusterPodSubnetTopologySpread, error) {
	clientSet, err := k.newCpstsClient()
	if err != nil {
		return nil, err
	}

	unstructuredObj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(psts)
	if err != nil {
		return nil, fmt.Errorf("convert cpsts to unstructured failed: %v", err)
	}

	result, err := clientSet.Create(ctx, &unstructured.Unstructured{Object: unstructuredObj}, metav1.CreateOptions{})
	if err != nil {
		return nil, err
	}

	created, err := toCpsts(result)
	if err != nil {
		return nil, err
	}
	return &created, nil
}

func (k *Client) UpdateCpsts(ctx context.Context, psts *types.ClusterPodSubnetTopologySpread) (*types.ClusterPodSubnetTopologySpread, error) {
	clientSet, err := k.newCpstsClient()
	if err != nil {
		return nil, err
	}

	unstructuredObj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(psts)
	if err != nil {
		return nil, fmt.Errorf("convert cpsts to unstructured failed: %v", err)
	}

	result, err := clientSet.Update(ctx, &unstructured.Unstructured{Object: unstructuredObj}, metav1.UpdateOptions{})
	if err != nil {
		return nil, err
	}

	updated, err := toCpsts(result)
	if err != nil {
		return nil, err
	}
	return &updated, nil
}

func (k *Client) newCpstsClient() (resource dynamic.NamespaceableResourceInterface, err error) {
	resource, err = k.NewDynamicClient("cce.baidubce.com", "v2alpha1", "clusterpodsubnettopologyspreads")
	return
}

func (k *Client) GetCpsts(ctx context.Context, name string, options *GetOptions) (*types.ClusterPodSubnetTopologySpread, error) {
	clientSet, err := k.newCpstsClient()
	if err != nil {
		return nil, err
	}
	result, err := clientSet.Get(ctx, name, options.CreateGetOptions())
	if err != nil {
		return nil, err
	}
	cpsts, err := toCpsts(result)
	if err != nil {
		return nil, err
	}
	return &cpsts, nil
}

func (k *Client) ListCpsts(ctx context.Context, options *ListOptions) (*types.ClusterPodSubnetTopologySpreadList, error) {
	clientSet, err := k.newCpstsClient()
	if err != nil {
		return nil, err
	}
	result, err := clientSet.List(ctx, options.CreateListOptions())
	if err != nil {
		return nil, err
	}

	pstsList := types.ClusterPodSubnetTopologySpreadList{
		TypeMeta: metav1.TypeMeta{
			Kind:       result.GetKind(),
			APIVersion: result.GetAPIVersion(),
		},
		ListMeta: metav1.ListMeta{
			ResourceVersion:    result.GetResourceVersion(),
			Continue:           result.GetContinue(),
			RemainingItemCount: result.GetRemainingItemCount(),
		},
	}
	items := make([]types.ClusterPodSubnetTopologySpread, 0, len(result.Items))
	for _, item := range result.Items {
		cpsts, toErr := toCpsts(&item)
		if toErr != nil {
			return nil, toErr
		}
		items = append(items, cpsts)
	}
	pstsList.Items = items
	return &pstsList, nil
}

func toCpsts(result *unstructured.Unstructured) (psts types.ClusterPodSubnetTopologySpread, err error) {
	err = runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), &psts)
	if err != nil {
		err = fmt.Errorf("convert unstructured to nrs failed, err: %v", err)
		return
	}
	return
}
