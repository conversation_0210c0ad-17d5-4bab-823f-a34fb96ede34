package kube

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/dynamic"
)

func (k *Client) CreateNrcs(ctx context.Context, nrcs *types.NetResourceConfigSet) (*types.NetResourceConfigSet, error) {
	clientSet, err := k.newNrcsClient()
	if err != nil {
		return nil, err
	}

	unstructuredObj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(nrcs)
	if err != nil {
		return nil, fmt.Errorf("convert nrcs to unstructured failed: %v", err)
	}

	result, err := clientSet.Create(ctx, &unstructured.Unstructured{Object: unstructuredObj}, metav1.CreateOptions{})
	if err != nil {
		return nil, err
	}

	created, err := toNrcs(result)
	if err != nil {
		return nil, err
	}
	return &created, nil
}

func (k *Client) UpdateNrcs(ctx context.Context, nrcs *types.NetResourceConfigSet) (*types.NetResourceConfigSet, error) {
	clientSet, err := k.newNrcsClient()
	if err != nil {
		return nil, err
	}

	unstructuredObj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(nrcs)
	if err != nil {
		return nil, fmt.Errorf("convert nrcs to unstructured failed: %v", err)
	}

	result, err := clientSet.Update(ctx, &unstructured.Unstructured{Object: unstructuredObj}, metav1.UpdateOptions{})
	if err != nil {
		return nil, err
	}

	updated, err := toNrcs(result)
	if err != nil {
		return nil, err
	}
	return &updated, nil
}

func (k *Client) newNrcsClient() (resource dynamic.NamespaceableResourceInterface, err error) {
	resource, err = k.NewDynamicClient("cce.baidubce.com", "v2alpha1", "netresourceconfigsets")
	return
}

func (k *Client) GetNrcs(ctx context.Context, name string, options *GetOptions) (*types.NetResourceConfigSet, error) {
	clientSet, err := k.newNrcsClient()
	if err != nil {
		return nil, err
	}
	result, err := clientSet.Get(ctx, name, options.CreateGetOptions())
	if err != nil {
		return nil, err
	}
	nrcs, err := toNrcs(result)
	if err != nil {
		return nil, err
	}
	return &nrcs, nil
}

func (k *Client) ListNrcs(ctx context.Context, options *ListOptions) (*types.NetResourceConfigSetList, error) {
	clientSet, err := k.newNrcsClient()
	if err != nil {
		return nil, err
	}
	result, err := clientSet.List(ctx, options.CreateListOptions())
	if err != nil {
		return nil, err
	}

	nrcsList := types.NetResourceConfigSetList{
		TypeMeta: metav1.TypeMeta{
			Kind:       result.GetKind(),
			APIVersion: result.GetAPIVersion(),
		},
		ListMeta: metav1.ListMeta{
			ResourceVersion:    result.GetResourceVersion(),
			Continue:           result.GetContinue(),
			RemainingItemCount: result.GetRemainingItemCount(),
		},
	}
	items := make([]types.NetResourceConfigSet, 0, len(result.Items))
	for _, item := range result.Items {
		nrcs, toErr := toNrcs(&item)
		if toErr != nil {
			return nil, toErr
		}
		items = append(items, nrcs)
	}
	nrcsList.Items = items
	return &nrcsList, nil
}

func toNrcs(result *unstructured.Unstructured) (nrcs types.NetResourceConfigSet, err error) {
	err = runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), &nrcs)
	if err != nil {
		err = fmt.Errorf("convert unstructured to nrcs failed, err: %v", err)
		return
	}
	return
}

func (k *Client) DeleteNrcs(ctx context.Context, name string, options metav1.DeleteOptions) error {
	clientSet, err := k.newNrcsClient()
	if err != nil {
		return err
	}
	err = clientSet.Delete(ctx, name, options)
	if err != nil {
		return err
	}
	return nil
}
