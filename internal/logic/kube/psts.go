package kube

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/dynamic"
)

func (k *Client) CreatePsts(ctx context.Context, psts *types.PodSubnetTopologySpread) (*types.PodSubnetTopologySpread, error) {
	clientSet, err := k.newPstsClient()
	if err != nil {
		return nil, err
	}

	unstructuredObj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(psts)
	if err != nil {
		return nil, fmt.Errorf("convert psts to unstructured failed: %v", err)
	}

	result, err := clientSet.Create(ctx, &unstructured.Unstructured{Object: unstructuredObj}, metav1.CreateOptions{})
	if err != nil {
		return nil, err
	}

	created, err := toPsts(result)
	if err != nil {
		return nil, err
	}
	return &created, nil
}

func (k *Client) UpdatePsts(ctx context.Context, psts *types.PodSubnetTopologySpread) (*types.PodSubnetTopologySpread, error) {
	clientSet, err := k.newPstsClient()
	if err != nil {
		return nil, err
	}

	unstructuredObj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(psts)
	if err != nil {
		return nil, fmt.Errorf("convert psts to unstructured failed: %v", err)
	}

	result, err := clientSet.Update(ctx, &unstructured.Unstructured{Object: unstructuredObj}, metav1.UpdateOptions{})
	if err != nil {
		return nil, err
	}

	updated, err := toPsts(result)
	if err != nil {
		return nil, err
	}
	return &updated, nil
}

func (k *Client) newPstsClient() (resource dynamic.NamespaceableResourceInterface, err error) {
	resource, err = k.NewDynamicClient("cce.baidubce.com", "v2", "podsubnettopologyspreads")
	return
}

func (k *Client) GetPsts(ctx context.Context, name string, options *GetOptions) (*types.PodSubnetTopologySpread, error) {
	clientSet, err := k.newPstsClient()
	if err != nil {
		return nil, err
	}
	result, err := clientSet.Get(ctx, name, options.CreateGetOptions())
	if err != nil {
		return nil, err
	}
	psts, err := toPsts(result)
	if err != nil {
		return nil, err
	}
	return &psts, nil
}

func (k *Client) ListPsts(ctx context.Context, options *ListOptions) (*types.PodSubnetTopologySpreadList, error) {
	clientSet, err := k.newPstsClient()
	if err != nil {
		return nil, err
	}
	result, err := clientSet.List(ctx, options.CreateListOptions())
	if err != nil {
		return nil, err
	}

	pstsList := types.PodSubnetTopologySpreadList{
		TypeMeta: metav1.TypeMeta{
			Kind:       result.GetKind(),
			APIVersion: result.GetAPIVersion(),
		},
		ListMeta: metav1.ListMeta{
			ResourceVersion:    result.GetResourceVersion(),
			Continue:           result.GetContinue(),
			RemainingItemCount: result.GetRemainingItemCount(),
		},
	}
	items := make([]types.PodSubnetTopologySpread, 0, len(result.Items))
	for _, item := range result.Items {
		psts, toErr := toPsts(&item)
		if toErr != nil {
			return nil, toErr
		}
		items = append(items, psts)
	}
	pstsList.Items = items
	return &pstsList, nil
}

func toPsts(result *unstructured.Unstructured) (psts types.PodSubnetTopologySpread, err error) {
	err = runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), &psts)
	if err != nil {
		err = fmt.Errorf("convert unstructured to nrs failed, err: %v", err)
		return
	}
	return
}
