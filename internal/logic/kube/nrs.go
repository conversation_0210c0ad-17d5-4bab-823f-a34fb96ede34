package kube

import (
	"context"
	"fmt"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/dynamic"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

func (k *Client) newNrsClient() (resource dynamic.NamespaceableResourceInterface, err error) {
	resource, err = k.NewDynamicClient("cce.baidubce.com", "v2", "netresourcesets")
	return
}

func (k *Client) GetNrs(ctx context.Context, name string, options *GetOptions) (*types.NetworkResourceSet, error) {
	clientSet, err := k.newNrsClient()
	if err != nil {
		return nil, err
	}
	result, err := clientSet.Get(ctx, name, options.CreateGetOptions())
	if err != nil {
		return nil, err
	}
	nrs, err := toNrs(result)
	if err != nil {
		return nil, err
	}
	return &nrs, nil
}

func (k *Client) ListNrs(ctx context.Context, options *ListOptions) (*types.NetworkResourceSetList, error) {
	clientSet, err := k.newNrsClient()
	if err != nil {
		return nil, err
	}
	result, err := clientSet.List(ctx, options.CreateListOptions())
	if err != nil {
		return nil, err
	}

	nrsList := types.NetworkResourceSetList{
		TypeMeta: metav1.TypeMeta{
			Kind:       result.GetKind(),
			APIVersion: result.GetAPIVersion(),
		},
		ListMeta: metav1.ListMeta{
			ResourceVersion:    result.GetResourceVersion(),
			Continue:           result.GetContinue(),
			RemainingItemCount: result.GetRemainingItemCount(),
		},
	}
	items := make([]types.NetworkResourceSet, 0, len(result.Items))
	for _, item := range result.Items {
		nrs, toErr := toNrs(&item)
		if toErr != nil {
			return nil, toErr
		}
		items = append(items, nrs)
	}
	nrsList.Items = items
	return &nrsList, nil
}

func toNrs(result *unstructured.Unstructured) (nrs types.NetworkResourceSet, err error) {
	err = runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), &nrs)
	if err != nil {
		err = fmt.Errorf("convert unstructured to nrs failed, err: %v", err)
		return
	}
	return
}
