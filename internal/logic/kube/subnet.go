package kube

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/dynamic"
)

func (k *Client) CreateSubnet(ctx context.Context, subnet *types.Subnet) (*types.Subnet, error) {
	clientSet, err := k.newSubnetClient()
	if err != nil {
		return nil, err
	}

	unstructuredObj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(subnet)
	if err != nil {
		return nil, fmt.Errorf("convert Subnet to unstructured failed: %v", err)
	}

	result, err := clientSet.Create(ctx, &unstructured.Unstructured{Object: unstructuredObj}, metav1.CreateOptions{})
	if err != nil {
		return nil, err
	}

	created, err := toSubnet(result)
	if err != nil {
		return nil, err
	}
	return &created, nil
}

func (k *Client) UpdateSubnet(ctx context.Context, subnet *types.Subnet) (*types.Subnet, error) {
	clientSet, err := k.newSubnetClient()
	if err != nil {
		return nil, err
	}

	unstructuredObj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(subnet)
	if err != nil {
		return nil, fmt.Errorf("convert Subnet to unstructured failed: %v", err)
	}

	result, err := clientSet.Update(ctx, &unstructured.Unstructured{Object: unstructuredObj}, metav1.UpdateOptions{})
	if err != nil {
		return nil, err
	}

	updated, err := toSubnet(result)
	if err != nil {
		return nil, err
	}
	return &updated, nil
}

func (k *Client) newSubnetClient() (resource dynamic.NamespaceableResourceInterface, err error) {
	resource, err = k.NewDynamicClient("cce.baidubce.com", "v1", "subnets")
	return
}

func (k *Client) GetSubnet(ctx context.Context, name string, options *GetOptions) (*types.Subnet, error) {
	clientSet, err := k.newSubnetClient()
	if err != nil {
		return nil, err
	}
	result, err := clientSet.Get(ctx, name, options.CreateGetOptions())
	if err != nil {
		return nil, err
	}
	subnet, err := toSubnet(result)
	if err != nil {
		return nil, err
	}
	return &subnet, nil
}

func (k *Client) ListSubnet(ctx context.Context, options *ListOptions) (*types.SubnetList, error) {
	clientSet, err := k.newSubnetClient()
	if err != nil {
		return nil, err
	}
	result, err := clientSet.List(ctx, options.CreateListOptions())
	if err != nil {
		return nil, err
	}

	subnetList := types.SubnetList{
		TypeMeta: metav1.TypeMeta{
			Kind:       result.GetKind(),
			APIVersion: result.GetAPIVersion(),
		},
		ListMeta: metav1.ListMeta{
			ResourceVersion:    result.GetResourceVersion(),
			Continue:           result.GetContinue(),
			RemainingItemCount: result.GetRemainingItemCount(),
		},
	}
	items := make([]types.Subnet, 0, len(result.Items))
	for _, item := range result.Items {
		subnet, toErr := toSubnet(&item)
		if toErr != nil {
			return nil, toErr
		}
		items = append(items, subnet)
	}
	subnetList.Items = items
	return &subnetList, nil
}

func toSubnet(result *unstructured.Unstructured) (subnet types.Subnet, err error) {
	err = runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), &subnet)
	if err != nil {
		err = fmt.Errorf("convert unstructured to Subnet failed, err: %v", err)
		return
	}
	return
}