package kube

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

// GetNetResourceSetList 获取NetResourceSet列表
func GetNetResourceSetList(ctx context.Context, kubeConfigPath string) (*types.NetworkResourceSetList, error) {
	client, err := NewClientByKubeConfig(kubeConfigPath, 30*time.Second, false)
	if err != nil {
		return nil, fmt.Errorf("创建客户端失败: %v", err)
	}

	listOptions := &ListOptions{
		ResourceVersion: "",
		Name:            "",
		LabelSelector:   nil,
		FieldSelector:   nil,
		ListContinue:    "",
		Limit:           0,
		Watch:           false,
	}

	return client.ListNrs(ctx, listOptions)
}
