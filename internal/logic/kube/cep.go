package kube

import (
	"context"
	"fmt"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/dynamic"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

func (k *Client) newCepClient() (resource dynamic.NamespaceableResourceInterface, err error) {
	resource, err = k.NewDynamicClient("cce.baidubce.com", "v2", "cceendpoints")
	return
}

func (k *Client) GetCep(ctx context.Context, namespace, name string, options *GetOptions) (*types.CCEEndpoint, error) {
	clientSet, err := k.newCepClient()
	if err != nil {
		return nil, err
	}
	result, err := clientSet.Namespace(namespace).Get(ctx, name, options.CreateGetOptions())
	if err != nil {
		return nil, err
	}
	cep, err := toCep(result)
	if err != nil {
		return nil, err
	}
	return &cep, nil
}

func (k *Client) ListCep(ctx context.Context, namespace string, options *ListOptions) (*types.CCEEndpointList, error) {
	clientSet, err := k.newCepClient()
	if err != nil {
		return nil, err
	}
	result, err := clientSet.Namespace(namespace).List(ctx, options.CreateListOptions())
	if err != nil {
		return nil, err
	}

	cepList := types.CCEEndpointList{
		TypeMeta: metav1.TypeMeta{
			Kind:       result.GetKind(),
			APIVersion: result.GetAPIVersion(),
		},
		ListMeta: metav1.ListMeta{
			ResourceVersion:    result.GetResourceVersion(),
			Continue:           result.GetContinue(),
			RemainingItemCount: result.GetRemainingItemCount(),
		},
	}
	items := make([]types.CCEEndpoint, 0, len(result.Items))
	for _, item := range result.Items {
		cep, toErr := toCep(&item)
		if toErr != nil {
			return nil, toErr
		}
		items = append(items, cep)
	}
	cepList.Items = items
	return &cepList, nil
}

func toCep(result *unstructured.Unstructured) (cep types.CCEEndpoint, err error) {
	err = runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), &cep)
	if err != nil {
		err = fmt.Errorf("convert unstructured to cep failed, err: %v", err)
		return
	}
	return
}
