package kube

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/dynamic"
)


func (k *Client) newENIClient() (resource dynamic.NamespaceableResourceInterface, err error) {
	resource, err = k.NewDynamicClient("cce.baidubce.com", "v2", "enis")
	return
}

func (k *Client) CreateENI(ctx context.Context, eni *types.ENI) (*types.ENI, error) {
	clientSet, err := k.newENIClient()
	if err != nil {
		return nil, err
	}

	unstructuredObj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(eni)
	if err != nil {
		return nil, fmt.Errorf("convert ENI to unstructured failed: %v", err)
	}

	result, err := clientSet.Create(ctx, &unstructured.Unstructured{Object: unstructuredObj}, metav1.CreateOptions{})
	if err != nil {
		return nil, err
	}

	created, err := toENI(result)
	if err != nil {
		return nil, err
	}
	return &created, nil
}

func (k *Client) UpdateENI(ctx context.Context, eni *types.ENI) (*types.ENI, error) {
	clientSet, err := k.newENIClient()
	if err != nil {
		return nil, err
	}

	unstructuredObj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(eni)
	if err != nil {
		return nil, fmt.Errorf("convert ENI to unstructured failed: %v", err)
	}

	result, err := clientSet.Update(ctx, &unstructured.Unstructured{Object: unstructuredObj}, metav1.UpdateOptions{})
	if err != nil {
		return nil, err
	}

	updated, err := toENI(result)
	if err != nil {
		return nil, err
	}
	return &updated, nil
}

func (k *Client) GetENI(ctx context.Context, name string, options *GetOptions) (*types.ENI, error) {
	clientSet, err := k.newENIClient()
	if err != nil {
		return nil, err
	}
	result, err := clientSet.Get(ctx, name, options.CreateGetOptions())
	if err != nil {
		return nil, err
	}
	eni, err := toENI(result)
	if err != nil {
		return nil, err
	}
	return &eni, nil
}

func (k *Client) ListENI(ctx context.Context, options *ListOptions) (*types.ENIList, error) {
	clientSet, err := k.newENIClient()
	if err != nil {
		return nil, err
	}
	result, err := clientSet.List(ctx, options.CreateListOptions())
	if err != nil {
		return nil, err
	}

	eniList := types.ENIList{
		TypeMeta: metav1.TypeMeta{
			Kind:       result.GetKind(),
			APIVersion: result.GetAPIVersion(),
		},
		ListMeta: metav1.ListMeta{
			ResourceVersion:    result.GetResourceVersion(),
			Continue:           result.GetContinue(),
			RemainingItemCount: result.GetRemainingItemCount(),
		},
	}
	items := make([]types.ENI, 0, len(result.Items))
	for _, item := range result.Items {
		eni, toErr := toENI(&item)
		if toErr != nil {
			return nil, toErr
		}
		items = append(items, eni)
	}
	eniList.Items = items
	return &eniList, nil
}

func toENI(result *unstructured.Unstructured) (eni types.ENI, err error) {
	err = runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), &eni)
	if err != nil {
		err = fmt.Errorf("convert unstructured to ENI failed, err: %v", err)
		return
	}
	return
}
