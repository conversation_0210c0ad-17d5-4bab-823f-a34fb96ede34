package kube

import (
	"context"
	"errors"
	"fmt"

	appsv1 "k8s.io/api/apps/v1"
	autoscalingv1 "k8s.io/api/autoscaling/v1"
	corev1 "k8s.io/api/core/v1"
	kubeerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

func (k *Client) CreateDeploymentAppsV1(ctx context.Context, namespace string, deployment *appsv1.Deployment) (*appsv1.Deployment, error) {
	return k.ClientSet.AppsV1().Deployments(namespace).Create(ctx, deployment, metav1.CreateOptions{})
}

func (k *Client) GetDeploymentAppsV1(ctx context.Context, namespace, name string, options *GetOptions) (*appsv1.Deployment, error) {
	return k.ClientSet.AppsV1().Deployments(namespace).Get(ctx, name, options.CreateGetOptions())
}

func (k *Client) DeleteDeploymentAppsV1(ctx context.Context, namespace, name string) error {
	return k.ClientSet.AppsV1().Deployments(namespace).Delete(ctx, name, metav1.DeleteOptions{})
}

func (k *Client) GetDeploymentScaleAppsV1(ctx context.Context, namespace, name string, options *GetOptions) (*autoscalingv1.Scale, error) {
	return k.ClientSet.AppsV1().Deployments(namespace).GetScale(ctx, name, options.CreateGetOptions())
}

func (k *Client) ScaleDeploymentAppsV1(ctx context.Context, namespace, name string, scale *autoscalingv1.Scale) (*autoscalingv1.Scale, error) {
	return k.ClientSet.AppsV1().Deployments(namespace).UpdateScale(ctx, name, scale, metav1.UpdateOptions{})
}

// EnsureDeploymentDeletedAppsV1 确保指定namespace下的deployment被删除
func (k *Client) EnsureDeploymentDeletedAppsV1(ctx context.Context, namespace, name string, options *CheckOptions) (err error) {
	err = k.DeleteDeploymentAppsV1(ctx, namespace, name)
	if err != nil {
		if kubeerrors.IsNotFound(err) {
			err = nil
			return
		}
		return
	}
	if options != nil {
		timeoutCtx, cancel := context.WithTimeout(ctx, options.Timeout)
		wait.UntilWithContext(timeoutCtx, func(ctx context.Context) {
			_, err = k.GetDeploymentAppsV1(ctx, namespace, name, &GetOptions{})
			if err != nil {
				// 已经删除成功，退出轮询
				if kubeerrors.IsNotFound(err) {
					err = nil
					cancel()
					return
				}
				logger.Warnf(ctx, "get deployment failed, err: %v, next loop", err)
				return
			}
			logger.Warnf(ctx, "deployment still exist, next loop")
		}, options.Interval)
		if errors.Is(timeoutCtx.Err(), context.DeadlineExceeded) {
			err = fmt.Errorf("ensure deployment deleted timeout for %s", options.Timeout.String())
			return
		}
	}
	return
}

func (k *Client) EnsureDeploymentRunningAppsV1(ctx context.Context, namespace, name string, options *CheckOptions) (err error) {
	if options != nil {
		timeoutCtx, cancel := context.WithTimeout(ctx, options.Timeout)
		wait.UntilWithContext(timeoutCtx, func(ctx context.Context) {
			deployment, err := k.GetDeploymentAppsV1(ctx, namespace, name, &GetOptions{})
			if err != nil {
				// 获取失败
				logger.Warnf(ctx, "get deployment failed, err: %v, next loop", err)
				return
			}

			for _, condition := range deployment.Status.Conditions {
				if condition.Type == appsv1.DeploymentAvailable {
					if condition.Status != corev1.ConditionTrue {
						logger.Warnf(ctx, "deployment is not ready, next loop")
						return
					}
				}
			}

			if deployment.Status.ReadyReplicas == deployment.Status.Replicas {
				cancel()
				return
			}

			logger.Warnf(ctx, " deployment ReadyReplicas are not equal to Replicas, next loop")
		}, options.Interval)

		if errors.Is(timeoutCtx.Err(), context.DeadlineExceeded) {
			err = fmt.Errorf("ensure deployment running timeout for %s", options.Timeout.String())
			return
		}
	}

	return
}
