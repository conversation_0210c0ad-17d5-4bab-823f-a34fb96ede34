CreateCluster: false
ClusterID: cce-j12sq38s
DeleteCluster: false
CheckList:
  - name: K8SVersion
  - name: K8SComponentStatus
  - name: ResourceGCTest
#  - name: CheckPSTS
#    config:
#      psts_subnet_id:
#        - sbn-s5tmcib2ge7p
#        - sbn-a79pw0bpge8s
#      fixIP_subnet_id: sbn-qsaneagw64hh
#      ipv4_psts:
#        - start: ************
#          end: ************
#      ipv4_fix:
#        - start: *************
#          end: *************
#  - name: SingleContainerScalingWithNewENI
#    wait:
#      - name: NetworkPreCheck
#  - name: SingleContainerScalingWithReservedIP
#    wait:
#      - name: SingleContainerScalingWithNewENI
#  - name: PodScalingTest
#    wait:
#      - name: SingleContainerScalingWithReservedIP
#  - name: CheckCRDResources
#    wait:
#      - name: PodScalingTest
#  - name: CheckNodeScaleResources
#    wait:
#      - name: CheckCRDResources
#  - name: CheckNrcsNodeScale
#    config:
#      subnetId: sbn-hh5xbi3p92k3
#    wait:
#      - name: CheckNodeScaleResources
#  - name: CheckENIScaleResources
#    wait:
#      - name: CheckNrcsNodeScale
#  - name: BandwidthQoS
#    wait:
#      - name: CheckENIScaleResources
#  - name: CheckCNIV2Config
#    wait:
#      - name: BandwidthQoS
#  - name: CheckNodeInPlaceRestart
#    wait:
#      - name: CheckCNIV2Config
#  - name: CheckNodeMoveOutReinstallMoveIn
#    wait:
#      - name: CheckNodeInPlaceRestart
#  - name: PodToPodCrossNodeNetworkPerformance
#    wait:
#      - name: CheckNodeMoveOutReinstallMoveIn
#  - name: NetworkCIDR
#    wait:
#      - name: PodToPodCrossNodeNetworkPerformance
