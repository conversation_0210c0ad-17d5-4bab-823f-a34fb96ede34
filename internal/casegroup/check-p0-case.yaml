CreateCluster: false
ClusterID: cce-lfb078r8
DeleteCluster: false
CheckList:
  - name: K8SVersion
  - name: K8SComponentStatus
  - name: ClusterNetwork
  - name: CheckAccountAndRepo
    config:
      userName: root
      repoUrl: http://kubernetes-charts.baidubce.com/repo/bcelxy
  - name: K8SNs
  - name: CreateAndDeleteDaemonSet
  - name: CreateAndDeleteDeployment
  - name: CreateAndDeleteJob
  - name: CreateAndDeleteCronJob
  - name: BLSLogRule
  - name: CheckClusterInfo
  - name: CreateLBService
  - name: CreateAndDeleteService
  - name: CreateIngress