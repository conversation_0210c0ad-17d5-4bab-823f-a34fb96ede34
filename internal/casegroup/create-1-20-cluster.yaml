CreateCluster: true
DeleteCluster: true
CreateClusterRequest:
  cluster:
    clusterName: create-1-20-cluster
    description: 1.20集群
    k8sVersion: 1.20.8
    runtimeType: docker
    vpcID: vpc-0b0a9zxvp0y8
    masterConfig:
      masterType: containerizedCustom
      clusterHA: 1
      exposedPublic: true
      clusterBLBVPCSubnetID: sbn-wruwz38nfz7c
    containerNetworkConfig:
      mode: vpc-route-auto-detect
      lbServiceVPCSubnetID: sbn-wruwz38nfz7c
      nodePortRangeMin: 50000
      nodePortRangeMax: 51000
      clusterPodCIDR: *********/16
      clusterIPServiceCIDR: **********/16
      maxPodsPerNode: 64
      kubeProxyMode: ipvs
    k8sCustomConfig:
      enableHostname: true
  masters:
    - instanceSpec:
        machineType: BCC
        instanceType: N5
        vpcConfig:
          vpcSubnetID: sbn-f9pckcxws8ik
          securityGroupID: g-483icp32q42u
          securityGroup:
            enableCCERequiredSecurityGroup: true
            enableCCEOptionalSecurityGroup: false
        instanceResource:
          cpu: 2
          mem: 8
          machineSpec: bcc.g4.c2m8
          rootDiskType: cloud_hp1
          rootDiskSize: 40
          cdsList:
            - diskPath: /data
              storageType: cloud_hp1
              cdsSize: 120
        instanceOS:
          imageType: System
          imageName: 7.2 x86_64 (64bit)
          osType: linux
          osName: CentOS
          osVersion: "7.2"
          osArch: x86_64 (64bit)
        adminPassword: jhkj4%w@oip
      count: 1
  nodes:
    - instanceSpec:
        machineType: BCC
        instanceType: N5
        vpcConfig:
          vpcSubnetID: sbn-f9pckcxws8ik
          securityGroupID: g-483icp32q42u
          securityGroup:
            enableCCERequiredSecurityGroup: true
            enableCCEOptionalSecurityGroup: false
        instanceResource:
          cpu: 2
          mem: 8
          machineSpec: bcc.g4.c2m8
        instanceOS:
          imageType: System
          imageName: 7.2 x86_64 (64bit)
          osType: linux
          osName: CentOS
          osVersion: "7.2"
          osArch: x86_64 (64bit)
        deployCustomConfig:
          postUserScript: >-
            IyEvYmluL2Jhc2gKbm9kZV9uYW1lPSQoaG9zdG5hbWUgLWkpCmVjaG8gJHtub2RlX25hbWV9CnJlc3VsdD0kKGt1YmVjdGwgbGFiZWwgbm9kZSAke25vZGVfbmFtZX0gcG9zdC1rZXk9cG9zdC12YWx1ZSkKZWNobyAke3Jlc3VsdH0K
        adminPassword: jhkj4%w@oip
      count: 1
    - instanceSpec:
        machineType: BCC
        instanceType: N5
        vpcConfig:
          vpcSubnetID: sbn-f9pckcxws8ik
          securityGroupID: g-483icp32q42u
          securityGroup:
            enableCCERequiredSecurityGroup: true
            enableCCEOptionalSecurityGroup: false
        instanceResource:
          cpu: 2
          mem: 8
          machineSpec: bcc.g4.c2m8
        instanceOS:
          imageType: System
          imageName: 7.2 x86_64 (64bit)
          osType: linux
          osName: CentOS
          osVersion: "7.2"
          osArch: x86_64 (64bit)
        deployCustomConfig:
          postUserScript: >-
            IyEvYmluL2Jhc2gKbm9kZV9uYW1lPSQoaG9zdG5hbWUgLWkpCmVjaG8gJHtub2RlX25hbWV9CnJlc3VsdD0kKGt1YmVjdGwgbGFiZWwgbm9kZSAke25vZGVfbmFtZX0gcG9zdC1rZXk9cG9zdC12YWx1ZSkKZWNobyAke3Jlc3VsdH0K
        adminPassword: jhkj4%w@oip
      count: 1
CheckList:
  - name: K8SVersion
  - name: K8SComponentStatus
  - name: CheckClusterInstance
  - name: CCEGatewayToken
  - name: CheckScript
  - name: GetClusterMaster
  - name: CreateAndDeleteJob
  - name: CreateAndDeleteService
  - name: CreateAndDeleteDaemonSet
  - name: CreateAndDeleteDeployment
  - name: CreateAndDeleteCronJob
  - name: CheckPluginsExist
  - name: CreateAndDeleteStatefulSet
PostCheckList:
  - name: CreateLBService
  - name: CreateIngress
