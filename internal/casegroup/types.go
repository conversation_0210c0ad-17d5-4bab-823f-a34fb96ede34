package casegroup

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	EnvJobName = "JOB_NAME"
)

// Config 定义一个 E2E Test 测试集配置
type Config struct {
	CreateCluster            bool                        `json:"CreateCluster"`        // 是否新建集群
	ClusterID                string                      `json:"ClusterID"`            // 不新建提供 ClusterID
	CreateClusterRequest     *ccev2.CreateClusterRequest `json:"CreateClusterRequest"` // 新建提供创建参数
	DeleteCluster            bool                        `json:"DeleteCluster"`        // 完成后是否删除集群
	ReserveInstances         bool                        `json:"ReserveInstances,omitempty"`
	FailedCasesRetainCluster bool                        `json:"FailedCasesRetainCluster,omitempty"`

	// 定义为 [][]*Case 会更好, 但是需要需改已有的 case 配置, 3 层结构基本能覆盖 99% 场景
	PreCheckList []Case `json:"PreCheckList,omitempty"`

	CheckList     []Case `json:"CheckList"`
	PostCheckList []Case `json:"PostCheckList,omitempty"`

	// DeleteCluster 成功后会执行的case组
	PostDeleteCheckList []Case `json:"PostDeleteCheckList,omitempty"`
}

type Case struct {
	Name     cases.CaseName  `json:"name"`
	Config   any             `json:"config"`
	Wait     []DagWait       `json:"wait"`
	WaitChan chan CaseStatus `json:"-"`
}

type DagWait struct {
	Name cases.CaseName `json:"name"`
	// 暂时不考虑前置case的错误情况，如果要处理错误可以在这里增加字段
}

type CaseStatus struct {
	Name    cases.CaseName `json:"name"`
	Success bool           `json:"success"`
}

// CheckConfig 检查配置是否合法
func (c *Config) CheckConfig(ctx context.Context, isDag bool) error {
	if c.CreateCluster && c.CreateClusterRequest == nil {
		return errors.New("want to create a cluster, but no config.CreateClusterRequest provided")
	}

	if !c.CreateCluster && c.ClusterID == "" {
		return errors.New("want to use a existed cluster, but no config.ClusterID provided")
	}

	if c.CheckList == nil || len(c.CheckList) == 0 {
		return errors.New("config.CheckList is nil or empty")
	}

	if isDag {
		if len(c.PreCheckList) > 0 || len(c.PostCheckList) > 0 {
			return errors.New("in dag mode, config.PreCheckList and config.PostCheckList is not allowed")
		}
		caseNames := make([]string, 0, len(c.CheckList))
		for _, check := range c.CheckList {
			caseNames = append(caseNames, string(check.Name))
		}
		// 检查wait的依赖必须是CheckList中定义过的
		for _, check := range c.CheckList {
			for _, w := range check.Wait {
				if !utils.ContainItem(caseNames, string(w.Name)) {
					return errors.New("wait case should in config.CheckList, wait case `" + string(w.Name) + "` not exist")
				}
			}
		}
	}

	return nil
}
