package executor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/conf"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/casegroup"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/inspect"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/retrypolicy"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

var (
	CaseErr = errors.New("case error") // 特殊的err类型，用于判断是否是case失败产生的错误，不会导致panic
)

// Result - 测试结果
type Result struct {
	BatchID     string
	Region      string
	FailedList  []models.TestResult
	SuccessList []models.TestResult
}

// Executor 实现 CCE Cluster 回归核心流程
type Executor struct {
	ClusterID string // 集群ID

	batchID     string         // 唯一标识本次运行case
	clusterName string         // 集群名称
	JobName     string         // 任务名称
	CaseNames   []string       // 本次运行case列表，优先级 > 配置文件
	job         models.TestJob // 本地运行的job

	cceClient    ccev2.Interface
	bccClient    bcc.Interface
	appblbClient appblb.Interface
	qaDB         *models.Client
	baseClient   *cases.BaseClient // 基础资源客户端

	resources      []cases.Resource // 运行过程中创建资源
	supportedCases cases.CaseList   // 所有支持 Case 的构造函数
	config         *conf.Config
	caseGroup      *casegroup.Config

	successList []models.TestResult
	failedList  []models.TestResult
	isDag       bool // 是否是dag模式

}

func NewExecutor(ctx context.Context, config *conf.Config, caseGroupConfig *casegroup.Config, isDag bool) (executor *Executor, err error) {
	caseList := cases.CCECaseList(ctx)

	// 初始化 cceClient
	cceClient := ccev2.NewClient(&bce.Config{
		Credentials: &bce.Credentials{
			AccessKeyID:     config.AccessKey,
			SecretAccessKey: config.AccessKeySecret,
		},
		Endpoint:    config.Endpoint.CCEV2Endpoint,
		Checksum:    true,
		Region:      config.Region,
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(ctx, 0, 0),
	})
	cceClient.SetInspect(config.EnableInspect)

	// 初始化 bccClient
	bccClient := bcc.NewClient(&bce.Config{
		Credentials: &bce.Credentials{
			AccessKeyID:     config.AccessKey,
			SecretAccessKey: config.AccessKeySecret,
		},
		Endpoint:    config.Endpoint.BCCEndpoint,
		Checksum:    true,
		Region:      config.Region,
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(ctx, 0, 0),
	})
	// 初始化 appblbClient
	appblbCient := appblb.NewClient(&bce.Config{
		Credentials: &bce.Credentials{
			AccessKeyID:     config.AccessKey,
			SecretAccessKey: config.AccessKeySecret,
		},
		Endpoint:    config.Endpoint.AppBLBEndpoint,
		Checksum:    true,
		Region:      config.Region,
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(ctx, 0, 0),
	})

	// 初始化qaDB client
	qaDB, newDBErr := models.NewClient(ctx, config.QAMySQLEndpoint)
	if newDBErr != nil {
		err = fmt.Errorf("internalmodels.NewClient failed: %w", newDBErr)
		return
	}

	executor = &Executor{
		batchID:        config.BatchID,
		cceClient:      cceClient,
		bccClient:      bccClient,
		appblbClient:   appblbCient,
		qaDB:           qaDB,
		supportedCases: caseList,
		config:         config,
		caseGroup:      caseGroupConfig,
		resources:      make([]cases.Resource, 0),
		isDag:          isDag,
	}
	return
}

func (e *Executor) Setup() (err error) {
	// 初始化测试脚本任务
	testJob := models.TestJob{
		BatchID: e.batchID,
		Region:  e.config.Region,
		Name:    e.JobName,
	}
	err = e.qaDB.CreateTestJob(&testJob)
	if err != nil {
		err = fmt.Errorf("create test job failed: %w", err)
		return
	}
	e.job = testJob
	return
}

func (e *Executor) Teardown() (err error) {
	// 这里需要重新获取下job下的case列表计算数量，放置case执行中途退出导致的统计不全
	testJob, getErr := e.qaDB.GetTestJob(e.batchID, e.config.Region, e.job.ID)
	if getErr != nil {
		err = fmt.Errorf("get test job failed: %v", getErr)
		return
	}
	var successCount, failedCount int
	for _, testResult := range testJob.TestResults {
		if testResult.Success {
			successCount++
		} else {
			failedCount++
		}
	}
	testJob.ClusterID = e.ClusterID
	testJob.ClusterName = e.clusterName
	testJob.Cost = time.Since(testJob.CreatedAt)
	testJob.SuccessCount = successCount
	testJob.FailedCount = failedCount
	testJob.FinishedAt = time.Now()

	err = e.qaDB.UpdateTestJob(testJob)
	if err != nil {
		err = fmt.Errorf("update test job failed: %w", err)
		return
	}
	return
}

// Run - 执行测试
// 所有的err只需要return，外面会统一打印日志
func (e *Executor) Run(ctx context.Context) (err error) {
	logger.Infof(ctx, "start to run case, batchID: %s", e.batchID)
	// 执行任务初始化集群，初始化的任务会成为特殊的case插入最终的结果集用于分析初始化的日志
	err = e.setupCluster(ctx)
	if err != nil {
		return
	}
	clusterID := e.ClusterID
	clusterName := e.clusterName
	logger.Infof(ctx, "run executor setup success, ClusterID: %s, ClusterName: %s", clusterID, clusterName)
	// 追加集群ID到日志上下文
	ctx = context.WithValue(ctx, logger.ClusterID, clusterID)

	e.resources = append(e.resources, cases.Resource{
		Type: cases.ResourceTypeCCECluster,
		ID:   clusterID,
	})

	// 初始化 baseClient
	base, newClientErr := cases.NewBaseClient(ctx, clusterID, e.config)
	if newClientErr != nil {
		err = fmt.Errorf("cases.NewBaseClient failed: %s", newClientErr)
		return
	}
	e.baseClient = base

	// 采集共享内存和缓存信息
	logger.Infof(ctx, "start collect collectShareMemCache")
	err = e.collectShareMemCache(ctx, e.baseClient)
	if err != nil {
		return
	}

	// 执行顺序：defer 2，为了确保日志完整不影响上传另开一个defer，这个defer无论如何都会执行
	defer func() {
		// 将日志文件上传到 bos
		if e.config.UploadJobLog {
			err = e.uploadRunLog(ctx)
			if err != nil {
				return
			}
		}
	}()

	// 执行顺序：defer 1
	defer func() {

		// 收集case执行结果,如果case执行失败，则不删除集群
		if e.caseGroup.FailedCasesRetainCluster {
			logger.Infof(ctx, "caseGroup.FailedCasesRetainCluster is true, if cases failed, will not delete cluster")

			testJob, getErr := e.qaDB.GetTestJob(e.batchID, e.config.Region, e.job.ID)
			if getErr != nil {
				err = fmt.Errorf("get test job failed: %v", getErr)
				return
			}

			for _, testResult := range testJob.TestResults {
				if !testResult.Success {
					logger.Infof(ctx, "case failed, caseName: %s, cluster %s, will not be deleted", testResult.Name, clusterID)
					return
				}
			}
		}

		// 根据case执行结果释放集群
		if e.caseGroup.DeleteCluster {
			err = e.releaseCluster(ctx)
			if err != nil {
				err = fmt.Errorf("releaseCluster failed: %v", err)
				return
			}
			// 删除集群成功后执行 PostDeleteCheckList
			err = e.preparePostDeleteTestResult()
			if err != nil {
				err = fmt.Errorf("preparePostDeleteTestResult failed: %v", err)
				return
			}
			if err = e.execCheckListParallel(ctx, e.baseClient, e.caseGroup.PostDeleteCheckList); err != nil {
				err = fmt.Errorf("[%w] %s", CaseErr, err.Error())
				return
			}
		}
		return
	}()

	// 在db中初始化case数据
	err = e.prepareTestResult()
	if err != nil {
		err = fmt.Errorf("prepareTestResult failed: %v", err)
		return
	}

	logger.Infof(ctx, "pre check start")
	if err = e.execCheckListParallel(ctx, base, e.caseGroup.PreCheckList); err != nil {
		err = fmt.Errorf("[%w] %s", CaseErr, err.Error())
		return
	}
	logger.Infof(ctx, "pre check success")

	logger.Infof(ctx, "check start")
	if e.isDag {
		// 初始化case状态通道
		for idx := range e.caseGroup.CheckList {
			e.caseGroup.CheckList[idx].WaitChan = make(chan casegroup.CaseStatus, len(e.caseGroup.CheckList))
		}
	}
	if err = e.execCheckListParallel(ctx, base, e.caseGroup.CheckList); err != nil {
		err = fmt.Errorf("[%w] %s", CaseErr, err.Error())
		return
	}
	logger.Infof(ctx, "check success")

	logger.Infof(ctx, "post check start")
	if err = e.execCheckListParallel(ctx, base, e.caseGroup.PostCheckList); err != nil {
		err = fmt.Errorf("[%w] %s", CaseErr, err.Error())
		return
	}
	logger.Infof(ctx, "post check success")

	// TODO: 确认 IaaS 资源回收完成, 这里整体去重
	logger.Infof(ctx, "---------------------------Resource-----------------------------")
	for _, res := range e.resources {
		logger.Infof(ctx, "Resource: CaseName=%s type=%s id=%s", res.CaseName, res.Type, res.ID)
	}
	logger.Infof(ctx, "----------------------------------------------------------------")

	return
}

// Result - 统计 Case 运行结果
func (e *Executor) Result(ctx context.Context) (result Result, err error) {
	logger.Infof(ctx, "start to print result")

	// 获取结果
	testResultList, listErr := e.qaDB.ListTestResult(models.ListTestResultConditions{
		JobID: e.job.ID,
	})
	if listErr != nil {
		err = fmt.Errorf("qaDB.ListTestResult failed: %v", listErr)
		return
	}

	resultCount := len(testResultList)
	failedList := make([]models.TestResult, 0, resultCount)
	successList := make([]models.TestResult, 0, resultCount)
	for _, r := range testResultList {
		if !r.Success {
			failedList = append(failedList, r)
		}

		if r.Success {
			successList = append(successList, r)
		}
	}
	e.successList = successList
	e.failedList = failedList

	result = Result{
		BatchID:     e.batchID,
		Region:      e.config.Region,
		FailedList:  failedList,
		SuccessList: successList,
	}

	return
}

func (e *Executor) setupCluster(ctx context.Context) (err error) {
	logger.Infof(ctx, "start executor setup")
	var clusterID, clusterName string
	clusterID, clusterName, err = e.ensureClusterRunning(ctx)
	if err != nil {
		return
	}
	e.ClusterID = clusterID
	e.clusterName = clusterName

	// 集群实例检查
	if !e.config.SkipCheckClusterInstances {
		err = e.ensureClusterInstancesRunning(ctx)
		if err != nil {
			return
		}
	}

	return
}

// execCheckListParallel - 并行执行 case
func (e *Executor) execCheckListParallel(ctx context.Context, base *cases.BaseClient, checkList []casegroup.Case) (err error) {
	checkListCount := len(checkList)

	if checkListCount == 0 {
		logger.Infof(ctx, "check list is empty, skip check, batchID: %s", e.batchID)
		return
	}
	wg := sync.WaitGroup{}
	errsChan := make(chan error, checkListCount)

	for _, c := range checkList {
		wg.Add(1)

		go func(ctx context.Context, c casegroup.Case) {
			var checkErr error
			defer func() {
				// 广播任务状态
				if e.isDag {
					for _, check := range checkList {
						// 自己的不需要被广播
						if check.Name == c.Name {
							continue
						}
						check.WaitChan <- casegroup.CaseStatus{
							Name:    c.Name,
							Success: checkErr == nil,
						}
					}
				}
				wg.Done()
			}()

			// 日志注入case名称
			ctx = context.WithValue(ctx, logger.CaseName, string(c.Name))
			// 在这里过滤case
			if !e.filterCase(c.Name) {
				return
			}
			// 需要等待依赖case完成
			if e.isDag && len(c.Wait) > 0 {
				logger.Warnf(ctx, "should wait: %v", c.Wait)
				waited := make([]cases.CaseName, 0, len(c.Wait))
				func(ctx context.Context) {
					for {
						select {
						case v := <-c.WaitChan:
							logger.Warnf(ctx, "case: `%v` finished, my wait: %v", v.Name, c.Wait)
							for _, w := range c.Wait {
								if w.Name == v.Name {
									waited = append(waited, v.Name)
									break
								}
							}
							logger.Warnf(ctx, "current waited: %v", waited)
							if len(waited) == len(c.Wait) {
								logger.Warnf(ctx, "wait finished, start to run case")
								return
							}
						}
					}
				}(ctx)
			}

			if checkErr = e.check(ctx, c, base); checkErr != nil {
				logger.Errorf(ctx, "check failed: %s", checkErr)
				errsChan <- checkErr
			}
		}(ctx, c)
	}

	wg.Wait()

	if len(errsChan) > 0 {
		err = errors.New("check failed")
		return
	}
	return
}

// check - 执行 CheckList 检查
func (e *Executor) check(ctx context.Context, caseGroupCase casegroup.Case, base *cases.BaseClient) (err error) {
	caseName := caseGroupCase.Name
	caseConfig := caseGroupCase.Config

	var config []byte
	var resources []cases.Resource

	f, ok := e.supportedCases[caseName]
	if !ok {
		err = errors.New("case unsupported")
		return
	}
	c := f(ctx)

	logger.Infof(ctx, "check begin")
	checkStart := time.Now()

	defer func() {
		elapsed := time.Since(checkStart)
		logger.Infof(ctx, "check takes %d", elapsed)

		testResult := models.TestResult{
			JobID:      e.job.ID,
			Name:       string(caseName),
			Cost:       elapsed,
			FinishedAt: time.Now(),
		}

		if err != nil {
			// 记录失败结果
			testResult.ErrMessage = err.Error()
		} else {
			// 记录正常结果
			testResult.Success = true
		}
		if err = e.qaDB.UpdateTestResult(testResult); err != nil {
			err = fmt.Errorf("update test result failed: %v", err)
			return
		}

		if c.Continue(ctx) {
			return
		}
	}()

	// 初始化对象
	config, err = json.Marshal(caseConfig)
	if err != nil {
		err = fmt.Errorf("json.Marshal case config failed: %s", err)
		return
	}

	err = c.Init(ctx, config, base)
	if err != nil {
		err = fmt.Errorf("case init failed: %s", err)
		return
	}

	// 执行检查
	resources, err = c.Check(ctx)
	if len(resources) > 0 {
		e.resources = append(e.resources, resources...)
	}
	if err != nil {
		err = fmt.Errorf("case check failed: %s", err)
		return
	}

	// 环境清理
	err = c.Clean(ctx)
	if err != nil {
		err = fmt.Errorf("case clean failed: %s", err)
		return
	}

	return
}

const (
	CreateClusterLatencyExpect float64 = 600000
)

func (e *Executor) collectClusterData(ctx context.Context, clusterID string, clusterInfo *ccev2.GetClusterResponse, operationType models.OperationType,
	operationSuccess bool) (err error) {
	logger.Infof(ctx, "collect cluster data for cluster %s", clusterID)
	if clusterInfo == nil {
		clusterInfo, err = e.cceClient.GetCluster(ctx, clusterID, nil)
		if err != nil {
			err = fmt.Errorf("get cluster %s failed: %s", clusterID, err)
			return
		}
	}

	// 获取集群创建阶段信息
	var clusterEventSteps *ccev2.GetEventStepsResponse
	if operationType == models.CreateClusterOperation || !operationSuccess {
		clusterEventSteps, err = e.cceClient.GetClusterEventSteps(ctx, clusterID, nil)
		if err != nil {
			err = fmt.Errorf("get cluster %s events failed: %s", clusterID, err)
			return
		}
	}

	// 若为新建集群，则额外采集集群和节点相关背景信息
	if operationType == models.CreateClusterOperation {
		err = e.qaDB.CreateClusterCombo(models.ClusterCombo{
			ClusterID:      clusterID,
			Region:         e.config.Region,
			MasterType:     clusterInfo.Cluster.Spec.MasterConfig.MasterType,
			K8SVersion:     clusterInfo.Cluster.Spec.K8SVersion,
			RuntimeType:    clusterInfo.Cluster.Spec.RuntimeType,
			RuntimeVersion: clusterInfo.Cluster.Spec.RuntimeVersion,
			NetworkMode:    clusterInfo.Cluster.Spec.ContainerNetworkConfig.Mode,
			KubeProxy:      clusterInfo.Cluster.Spec.ContainerNetworkConfig.KubeProxyMode,
		})
		if err != nil {
			return
		}
		var instances *ccev2.ListInstancesResponse
		// 采集node背景数据
		instances, err = e.cceClient.ListInstancesByPage(ctx, clusterID, nil, nil)
		if err != nil {
			err = fmt.Errorf("list cluster %s instances failed: %s", clusterID, err)
			return
		}
		instanceList := instances.InstancePage.InstanceList
		cceNodeComboList := make([]models.NodeCombo, 0, len(instanceList))
		for _, instance := range instanceList {
			creationType := "openapi"
			if instance.Spec.InstanceResource.MachineSpec == "" {
				creationType = "internal"
			}
			instanceSpec := fmt.Sprintf("c%sm%s",
				strconv.Itoa(instance.Spec.InstanceResource.CPU),
				strconv.Itoa(instance.Spec.InstanceResource.MEM),
			)
			cceNodeComboList = append(cceNodeComboList, models.NodeCombo{
				ClusterID:       clusterID,
				Region:          e.config.Region,
				ClusterRole:     instance.Spec.ClusterRole,
				CCEInstanceID:   instance.Spec.CCEInstanceID,
				InstanceUUID:    instance.Status.Machine.InstanceUUID,
				OrderID:         instance.Status.Machine.OrderID,
				AvailableZone:   instance.Spec.AvailableZone,
				MachineType:     instance.Spec.MachineType,
				InstanceType:    instance.Spec.InstanceType,
				InstanceSpec:    instanceSpec,
				CreationType:    creationType,
				InstanceGroupID: instance.Spec.InstanceGroupID,
				OsName:          instance.Spec.InstanceOS.OSName,
				OsVersion:       instance.Spec.InstanceOS.OSVersion,
			})
		}
		err = e.qaDB.CreateNodeCombo(cceNodeComboList)
		if err != nil {
			return
		}
		logger.Infof(ctx, "operationSuccess is %v", operationSuccess)

		// 若创建成功，则采集创建耗时性能信息
		if operationSuccess {
			logger.Infof(ctx, "start collect create cluster perf")
			var start, finish *metav1.Time
			stepCostInfo := make(map[ccev2.StepName]time.Duration, len(clusterEventSteps.Steps))
			for _, step := range clusterEventSteps.Steps {
				stepCostInfo[step.StepName] = time.Duration(step.CostSeconds) * time.Second
				if step.StepName == ccev2.ClusterStepCreateCertificateAuthority {
					start = step.StartTime
				}
				if step.StepName == ccev2.ClusterStepDeployK8SPlugin {
					finish = step.FinishedTime
				}
			}
			if start == nil || finish == nil {
				err = errors.New("create cluster perf failed, start or finish is nil")
				return
			}
			if err = e.qaDB.CreateClusterPerf(models.ClusterPerf{
				Region:                       e.config.Region,
				ClusterID:                    clusterID,
				CreateCost:                   finish.Sub(start.Time),
				CreateClusterLBCost:          stepCostInfo[ccev2.ClusterStepCreateLB],
				CreateClusterEIPCost:         stepCostInfo[ccev2.ClusterStepCreateEIP],
				WaitMasterInfrastructureCost: stepCostInfo[ccev2.ClusterStepWaitMasterInfrastructure],
				WaitAPIServerAccessCost:      stepCostInfo[ccev2.ClusterStepWaitAPIServerAccess],
				DeployK8SPluginCost:          stepCostInfo[ccev2.ClusterStepDeployK8SPlugin],
			}); err != nil {
				return
			}
			// 同时推送巡检数据
			inspectClient := inspect.NewClient(e.config.Region)
			params := inspect.PollingDataParams{
				Case:        string(inspect.CreateClusterCase),
				Region:      e.config.Region,
				PollingType: inspect.DataflowType,
				CaseDesc:    fmt.Sprintf("create %s taking time", clusterID),
				Result:      true,
				ErrorInfo:   "",
				CaseResults: []inspect.CaseResult{
					{
						Name:    inspect.TakingTimeName,
						Desc:    fmt.Sprintf("create %s taking time", clusterID),
						Value:   float64(finish.Sub(start.Time).Milliseconds()),
						Checker: "<",
						Limit:   CreateClusterLatencyExpect,
					},
				},
			}
			_, err = inspectClient.PollingData(&params)
			if err != nil {
				return
			}
		}
	}

	// 采集集群创建失败数据
	var errMsg string
	if !operationSuccess {
		for _, step := range clusterEventSteps.Steps {
			// 由master创建导致的失败，状态在30分钟内持续保持doing状态, 为了采集错误信息
			if step.StepStatus == ccev2.StepStatusFailed || step.StepStatus == ccev2.StepStatusDoing {
				errMsg = string(step.StepName) + " : " + step.ErrMsg
				break
			}
		}
	}

	analysisData := []string{
		e.config.Region,
		string(clusterInfo.Cluster.Spec.MasterConfig.MasterType),
		string(clusterInfo.Cluster.Spec.ContainerNetworkConfig.Mode),
		string(operationType),
		string(models.GetEventOperationResult(operationSuccess)),
	}
	err = e.qaDB.CreateEvent([]models.Event{{
		Region:           e.config.Region,
		ClusterID:        clusterID,
		ClusterName:      clusterInfo.Cluster.Spec.ClusterName,
		OperationType:    operationType,
		OperationSuccess: operationSuccess,
		AnalysisData:     strings.Join(analysisData, "-"),
		AnalysisDig:      errMsg,
	}})
	return
}

func (e *Executor) releaseCluster(ctx context.Context) (err error) {
	logger.Infof(ctx, "DeleteCluster %s", e.ClusterID)

	deleteClusterTime := time.Now()
	// 记录删除集群的case
	testResult := models.TestResult{
		JobID: e.job.ID,
		Name:  CaseDeleteCreatedCluster,
	}
	err = e.qaDB.CreateTestResult([]models.TestResult{testResult})
	if err != nil {
		err = fmt.Errorf("prepare testResult failed: %v", err)
		return
	}

	defer func() {
		if err != nil {
			if errors.Is(err, deleteCreatedClusterErr) {
				testResult.Success = false
				testResult.ErrMessage = err.Error()
				testResult.Cost = time.Since(deleteClusterTime)
				testResult.FinishedAt = time.Now()
				updateTestResultErr := e.qaDB.UpdateTestResult(testResult)
				if updateTestResultErr != nil {
					err = fmt.Errorf("%w, update test results failed: %v", err, updateTestResultErr)
					return
				}
			}
		}
	}()

	instanceResp, listErr := e.cceClient.ListInstancesByPage(ctx, e.ClusterID, &ccev2.ListInstancesByPageParams{
		PageNo:   1,
		PageSize: 1000,
	}, nil)
	if listErr != nil {
		logger.Errorf(ctx, "cceClient.ListInstancesByPage failed: %v", listErr)
		return
	}
	instanceIDs := make([]string, 0, len(instanceResp.InstancePage.InstanceList))
	for _, instance := range instanceResp.InstancePage.InstanceList {
		instanceIDs = append(instanceIDs, instance.Status.Machine.InstanceID)
	}
	clusterInfo, getClusterErr := e.cceClient.GetCluster(ctx, e.ClusterID, nil)
	if getClusterErr != nil {
		logger.Errorf(ctx, "cceClient.GetCluster failed: %v", getClusterErr)
		return
	}
	if _, err = e.cceClient.DeleteCluster(ctx, e.ClusterID, &ccev2.DeleteOptions{
		MoveOut:           e.caseGroup.ReserveInstances,
		DeleteCDSSnapshot: !e.caseGroup.ReserveInstances,
		DeleteResource:    !e.caseGroup.ReserveInstances,
	}, nil); err != nil {
		err = fmt.Errorf("cceClient.DeleteCluster failed: %v", err)
		return
	}
	if err = e.ensureClusterDeleted(ctx); err != nil {
		err = fmt.Errorf("ensureClusterDeleted failed: %v", err)
		collectErr := e.collectClusterData(ctx, e.ClusterID, clusterInfo, models.DeleteClusterOperation, false)
		if collectErr != nil {
			logger.Errorf(ctx, collectErr.Error())
		}
		return
	}
	if err = e.ensureIaasReleased(ctx, instanceIDs); err != nil {
		err = fmt.Errorf("ensureIaasReleased failed: %v", err)
		return
	}
	testResult.Success = true
	testResult.Cost = time.Since(deleteClusterTime)
	testResult.FinishedAt = time.Now()
	updateTestResultErr := e.qaDB.UpdateTestResult(testResult)
	if updateTestResultErr != nil {
		err = fmt.Errorf("update test results failed: %v", updateTestResultErr)
		return
	}

	collectErr := e.collectClusterData(ctx, e.ClusterID, clusterInfo, models.DeleteClusterOperation, true)
	if collectErr != nil {
		logger.Errorf(ctx, collectErr.Error())
	}
	return
}

func (e *Executor) caseListToDB(caseList []casegroup.Case) (err error) {
	testResultList := make([]models.TestResult, 0, len(caseList))
	for _, c := range caseList {
		if !e.filterCase(c.Name) {
			continue
		}
		testResultList = append(testResultList, models.TestResult{
			JobID: e.job.ID,
			Name:  string(c.Name),
		})
	}
	err = e.qaDB.CreateTestResult(testResultList)
	return
}

func (e *Executor) prepareTestResult() (err error) {
	caseGroup := e.caseGroup
	caseCount := len(caseGroup.PreCheckList) + len(caseGroup.CheckList) + len(caseGroup.PostCheckList)
	caseList := make([]casegroup.Case, 0, caseCount)
	caseList = append(caseList, caseGroup.PreCheckList...)
	caseList = append(caseList, caseGroup.CheckList...)
	caseList = append(caseList, caseGroup.PostCheckList...)

	err = e.caseListToDB(caseList)
	return
}

func (e *Executor) preparePostDeleteTestResult() (err error) {
	caseGroup := e.caseGroup
	caseCount := len(caseGroup.PostDeleteCheckList)
	caseList := make([]casegroup.Case, 0, caseCount)
	caseList = append(caseList, caseGroup.PostDeleteCheckList...)

	err = e.caseListToDB(caseList)
	return
}

func (e *Executor) uploadRunLog(ctx context.Context) (err error) {
	localLogName := fmt.Sprintf("%s_%s.log", e.batchID, e.JobName)
	localLogPath := filepath.Join(logger.DefaultLoggerDir, localLogName)
	targetObjectName := filepath.Join(conf.DefaultBosJobLogPrefix, e.config.Region, localLogName)
	etag, putErr := e.baseClient.BOSClient.PutObjectFromFile(e.config.BosBucket, targetObjectName, localLogPath, nil)
	if putErr != nil {
		err = fmt.Errorf("BOSClient.PutObjectFromFile failed: %v", putErr)
		return
	}
	logger.Infof(ctx, "upload run log success, etag:%s, put:%s", etag, localLogPath)
	return
}

func (e *Executor) filterCase(caseName cases.CaseName) (found bool) {
	found = true
	// TODO 其他匹配方式
	// 按照 case 名称精确匹配
	if len(e.CaseNames) > 0 {
		found = utils.StringContains(e.CaseNames, string(caseName))
	}
	return
}

func (e *Executor) collectShareMemCache(ctx context.Context, base *cases.BaseClient) error {
	if base.ClusterSpec.ENIDefaultSecurityGroups != nil && strings.Contains(base.ClusterSpec.ENIDefaultSecurityGroups[0].Name, base.ClusterSpec.ClusterID) {
		base.SetSharedMemCache("eniSecurityGroupId", base.ClusterSpec.ENIDefaultSecurityGroups[0].ID)
	}
	if base.ClusterSpec.MasterDefaultSecurityGroups != nil && strings.Contains(base.ClusterSpec.MasterDefaultSecurityGroups[0].Name, base.ClusterSpec.ClusterID) {
		base.SetSharedMemCache("masterSecurityGroupId", base.ClusterSpec.MasterDefaultSecurityGroups[0].ID)
	}
	if base.ClusterSpec.NodeDefaultSecurityGroups != nil && strings.Contains(base.ClusterSpec.NodeDefaultSecurityGroups[0].Name, base.ClusterSpec.ClusterID) {
		base.SetSharedMemCache("nodeSecurityGroupId", base.ClusterSpec.NodeDefaultSecurityGroups[0].ID)
	}
	return nil
}
