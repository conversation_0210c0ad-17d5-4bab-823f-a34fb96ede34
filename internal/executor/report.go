package executor

import (
	"context"
	"strconv"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

// PrintReport 打印测试报告
/*
+----+-----------------+--------+--------------------+--------------+---------------------+
| NO |    BATCH-ID     | REGION |        NAME        |     COST     |      FINISHED       |
+----+-----------------+--------+--------------------+--------------+---------------------+
| 1  | 20240710_170706 | gz     | K8SVersion         | 467.000416ms | 2024-07-10 17:07:07 |
| 2  | 20240710_170706 | gz     | K8SComponentStatus | 2.062791459s | 2024-07-10 17:07:09 |
+----+-----------------+--------+--------------------+--------------+---------------------+
|                                 TOTAL              | 2.529791875S |                     |
+----+-----------------+--------+--------------------+--------------+---------------------+
*/
func PrintReport(ctx context.Context, result Result) {
	successList := result.SuccessList
	failedList := result.FailedList

	logger.Infof(ctx, "------------------------Success TestCase----------------------------")
	successHeader := []string{"NO", "BATCH-ID", "REGION", "NAME", "COST", "FINISHED"}
	successRows := make([][]string, 0, len(successList))
	successIdx := 1
	var successCostTotal time.Duration
	for _, success := range successList {
		successRows = append(successRows, []string{
			strconv.Itoa(successIdx),
			result.BatchID,
			result.Region,
			success.Name,
			success.Cost.String(),
			utils.GetSafeFormattedTime(success.FinishedAt, time.DateTime),
		})
		successIdx++
		successCostTotal += success.Cost
	}
	successFooter := []string{"", "", "", "TOTAL", successCostTotal.String(), " "}
	successTableString := utils.PrintTable(successHeader, successRows, successFooter)
	logger.Infof(ctx, "\n"+successTableString.String())

	logger.Infof(ctx, "------------------------Failed TestCase----------------------------")
	failedHeader := []string{"NO", "BATCH-ID", "REGION", "NAME", "COST", "MESSAGE", "FINISHED"}
	failedRows := make([][]string, 0, len(failedList))
	failedIdx := 1
	var failedCostTotal time.Duration
	for _, failed := range failedList {
		failedRows = append(failedRows, []string{
			strconv.Itoa(failedIdx),
			result.BatchID,
			result.Region,
			failed.Name,
			failed.Cost.String(),
			failed.ErrMessage,
			utils.GetSafeFormattedTime(failed.FinishedAt, time.DateTime),
		})
		failedIdx++
		failedCostTotal += failed.Cost
	}
	failedFooter := []string{"", "", "", "TOTAL", failedCostTotal.String(), " ", " "}
	failedTableString := utils.PrintTable(failedHeader, failedRows, failedFooter)
	logger.Infof(ctx, "\n"+failedTableString.String())
}
