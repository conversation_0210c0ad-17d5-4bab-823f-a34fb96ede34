package executor

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

var (
	createClusterErr              = errors.New("create cluster failed")
	getCreatedClusterErr          = errors.New("get created cluster failed")
	getCreatedClusterInstancesErr = errors.New("get created cluster instances failed")
	deleteCreatedClusterErr       = errors.New("delete created cluster failed")
)

const (
	checkClusterInterval = time.Second * 10
)

const (
	CaseCreateNewCluster           = "CreateNewCluster"
	CaseGetCreatedCluster          = "GetCreatedCluster"
	CaseGetCreatedClusterInstances = "GetCreatedClusterInstances"
	CaseDeleteCreatedCluster       = "DeleteCreatedCluster"
)

// ensureClusterRunning - 确保集群运行正常，如果创建集群，则等待集群创建完成并运行
func (e *Executor) ensureClusterRunning(ctx context.Context) (clusterID, clusterName string, err error) {
	clusterID = e.caseGroup.ClusterID
	waitClusterRunning := time.Now()

	// 准备特殊的case入库等待完成
	testResults := make([]models.TestResult, 0, 2)
	if e.caseGroup.CreateCluster {
		testResults = append(testResults, models.TestResult{
			JobID: e.job.ID,
			Name:  CaseCreateNewCluster,
		})
	}
	testResults = append(testResults, models.TestResult{
		JobID: e.job.ID,
		Name:  CaseGetCreatedCluster,
	})
	logger.Infof(ctx, "prepare setup test results")
	err = e.qaDB.CreateTestResult(testResults)
	if err != nil {
		err = fmt.Errorf("prepare test results failed: %v", err)
		return
	}
	logger.Infof(ctx, "prepare setup test results success")

	var updateTestResultErr error

	defer func() {
		// 总是计算耗时
		elapsed := time.Since(waitClusterRunning)
		logger.Infof(ctx, "wait cluster running takes %s", elapsed)

		if err != nil {
			toUpdateTestResult := models.TestResult{
				JobID:      e.job.ID,
				Success:    false,
				ErrMessage: err.Error(),
				Cost:       elapsed,
				FinishedAt: time.Now(),
			}
			// 创建集群发生错误, 更新case结果
			if errors.Is(err, createClusterErr) {
				toUpdateTestResult.Name = CaseCreateNewCluster
			}
			// 等待集群运行发生错误, 更新case结果
			if errors.Is(err, getCreatedClusterErr) {
				toUpdateTestResult.Name = CaseGetCreatedCluster
			}
			if toUpdateTestResult.Name != "" {
				updateTestResultErr = e.qaDB.UpdateTestResult(toUpdateTestResult)
				if updateTestResultErr != nil {
					err = fmt.Errorf("%w, update test results failed: %v", err, updateTestResultErr)
				}
			}
		}
	}()

	// 创建集群
	if e.caseGroup.CreateCluster {
		createClusterResp, createErr := e.cceClient.CreateCluster(ctx, e.caseGroup.CreateClusterRequest, nil)
		if createErr != nil {
			err = fmt.Errorf("%w, cceClient.CreateCluster failed: %v", createClusterErr, createErr)
			return
		}
		clusterID = createClusterResp.ClusterID
		logger.Infof(ctx, "cceClient.CreateCluster success, ClusterID: %s", clusterID)
		// 创建集群成功，更新case结果
		updateTestResultErr = e.qaDB.UpdateTestResult(models.TestResult{
			JobID:      e.job.ID,
			Name:       CaseCreateNewCluster,
			Success:    true,
			Cost:       time.Since(waitClusterRunning),
			FinishedAt: time.Now(),
		})
		if updateTestResultErr != nil {
			err = fmt.Errorf("update test results failed: %v", updateTestResultErr)
			return
		}
	}
	if clusterID == "" {
		err = errors.New("caseGroup.ClusterID is empty")
		return
	}
	timeoutCtx, cancel := context.WithTimeout(ctx, time.Duration(e.config.CreateClusterTimeoutSeconds)*time.Second)

	var cluster *ccev2.Cluster
	var operationSuccess bool

	// 集群创建好以后，统计回归集群的背景信息
	defer func() {
		if e.caseGroup.CreateCluster {
			logger.Infof(ctx, "start to collect cluster data")
			collectErr := e.collectClusterData(ctx, clusterID, nil, models.CreateClusterOperation, operationSuccess)
			if collectErr != nil {
				logger.Errorf(ctx, collectErr.Error())
			}
		}
	}()

	// 用于记录集群创建的当前状态
	var lastStatus types.ClusterPhase
	// 创建一个状态集合来表示预期的状态顺序
	statusOrder := map[types.ClusterPhase]int{
		types.ClusterPhasePending:      0,
		types.ClusterPhaseProvisioning: 1,
		types.ClusterPhaseProvisioned:  2,
		types.ClusterPhaseRunning:      3,
	}
	lastOrder := -1
	// 等待集群running
	wait.UntilWithContext(timeoutCtx, func(ctx context.Context) {
		// 查询集群信息
		clusterResp, getClusterErr := e.cceClient.GetCluster(ctx, clusterID, nil)
		// 查询错误，下一次循环
		if getClusterErr != nil {
			logger.Warnf(ctx, "cceClient.GetCluster failed, next try, clusterID: %s, %s", clusterID, getClusterErr.Error())
			return
		}
		// 集群不存在，下一次循环
		if clusterResp == nil || clusterResp.Cluster == nil {
			logger.Warnf(ctx, "cceClient.GetCluster cluster is nil, next try, clusterID: %s", clusterID)
			return
		}
		cluster = clusterResp.Cluster
		clusterName = cluster.Spec.ClusterName

		// 方便测试过程中复跑，提前结束查询
		if cluster.Status.ClusterPhase == types.ClusterPhaseRunning || cluster.Status.ClusterPhase == types.ClusterPhaseUpgrading {
			operationSuccess = true
			logger.Infof(ctx, "cceClient.GetCluster success, clusterID: %s, clusterName: %s, phase: %s", clusterID, clusterName, cluster.Status.ClusterPhase)
			cancel()
			return
		}

		// 集群创建失败，提前结束查询
		if cluster.Status.ClusterPhase == types.ClusterPhaseCreateFailed {
			operationSuccess = false
			logger.Errorf(ctx, "cceClient.GetCluster success, clusterID: %s, clusterName: %s, phase: %s", clusterID, clusterName, cluster.Status.ClusterPhase)
			cancel()
			return
		}
		// 创建中，判断阶段的状态流转
		if cluster.Status.ClusterPhase != lastStatus {
			logger.Infof(ctx, "cluster phase changed: %s", cluster.Status.ClusterPhase)
			lastStatus = cluster.Status.ClusterPhase
			// 检查新的状态是否符合预期的顺序
			order, ok := statusOrder[cluster.Status.ClusterPhase]
			if !ok {
				logger.Errorf(ctx, "Unexpected phase: %s", cluster.Status.ClusterPhase)
				cancel()
				return
			} else if order < lastOrder {
				logger.Errorf(ctx, "Phase out of order: %s", cluster.Status.ClusterPhase)
				cancel()
				return
			} else {
				lastOrder = order
			}
		}
	}, checkClusterInterval)

	if errors.Is(timeoutCtx.Err(), context.DeadlineExceeded) {
		operationSuccess = false
		err = fmt.Errorf("%w, check cluster timeout for %d seconds, clusterPhase != running", getCreatedClusterErr, e.config.CreateClusterTimeoutSeconds)
		return
	}
	// 等待集群运行成功，更新case结果
	updateTestResultErr = e.qaDB.UpdateTestResult(models.TestResult{
		JobID:      e.job.ID,
		Name:       CaseGetCreatedCluster,
		Success:    operationSuccess,
		Cost:       time.Since(waitClusterRunning),
		FinishedAt: time.Now(),
	})
	if updateTestResultErr != nil {
		err = fmt.Errorf("update test results failed: %v", updateTestResultErr)
		return
	}
	return
}

// ensureClusterInstancesRunning 确保集群实例运行
func (e *Executor) ensureClusterInstancesRunning(pCtx context.Context) (err error) {
	logger.Infof(pCtx, "start to ensure cluster instances running")
	waitClusterInstancesRunning := time.Now()
	// 准备特殊的case入库等待完成
	testResult := models.TestResult{
		JobID: e.job.ID,
		Name:  CaseGetCreatedClusterInstances,
	}
	err = e.qaDB.CreateTestResult([]models.TestResult{testResult})
	if err != nil {
		err = fmt.Errorf("prepare `%s` test result failed: %v", CaseGetCreatedClusterInstances, err)
		return
	}

	var updateTestResultErr error
	defer func() {
		// 总是计算耗时
		elapsed := time.Since(waitClusterInstancesRunning)
		logger.Infof(pCtx, "wait cluster instances running takes %s", elapsed)

		if err != nil {
			toUpdateTestResult := models.TestResult{
				JobID:      e.job.ID,
				Name:       CaseGetCreatedClusterInstances,
				Success:    false,
				ErrMessage: err.Error(),
				Cost:       elapsed,
				FinishedAt: time.Now(),
			}

			updateTestResultErr = e.qaDB.UpdateTestResult(toUpdateTestResult)
			if updateTestResultErr != nil {
				err = fmt.Errorf("%w, update test results failed: %v", err, updateTestResultErr)
			}
		}
	}()

	instanceIDList := make([]string, 0, 10)
	timeoutCtx, cancel := context.WithTimeout(pCtx, time.Duration(e.config.CreateClusterTimeoutSeconds)*time.Second)
	wait.UntilWithContext(timeoutCtx, func(ctx context.Context) {
		instanceRes, listErr := e.cceClient.ListInstancesByPage(ctx, e.ClusterID, &ccev2.ListInstancesByPageParams{
			PageSize:    1000,
			ClusterRole: types.ClusterRoleNode,
		}, nil)
		if listErr != nil {
			logger.Warnf(ctx, "cceClient.ListInstancesByPage failed: %v", listErr)
			return
		}
		for _, instance := range instanceRes.InstancePage.InstanceList {
			if instance.Status.InstancePhase != types.InstancePhaseRunning {
				logger.Warnf(ctx, "instance `%s` is not running", instance.Spec.CCEInstanceID)
				return
			}
			instanceIDList = append(instanceIDList, instance.Spec.CCEInstanceID)
		}
		cancel()
	}, checkClusterInterval)
	if errors.Is(timeoutCtx.Err(), context.DeadlineExceeded) {
		err = fmt.Errorf("%w, check cluster instances timeout for %d seconds, instances is not all running", getCreatedClusterInstancesErr,
			e.config.CreateClusterTimeoutSeconds)
		return
	}
	logger.Infof(pCtx, "all instances are running, %s", strings.Join(instanceIDList, ","))
	// 等待集群实例运行成功，更新case结果
	updateTestResultErr = e.qaDB.UpdateTestResult(models.TestResult{
		JobID:      e.job.ID,
		Name:       CaseGetCreatedClusterInstances,
		Success:    true,
		Cost:       time.Since(waitClusterInstancesRunning),
		FinishedAt: time.Now(),
	})
	if updateTestResultErr != nil {
		err = fmt.Errorf("update test results failed: %v", updateTestResultErr)
		return
	}
	return
}

// ensureClusterDeleted 确保集群被删除
func (e *Executor) ensureClusterDeleted(pCtx context.Context) (err error) {
	timeoutCtx, cancel := context.WithTimeout(pCtx, time.Duration(e.config.CreateClusterTimeoutSeconds)*time.Second)

	wait.UntilWithContext(timeoutCtx, func(ctx context.Context) {
		// 查询集群信息
		clusterResp, getClusterErr := e.cceClient.GetCluster(ctx, e.ClusterID, nil)
		// 查询错误，下一次循环
		if getClusterErr != nil {
			// 由于错误没有错误类型，只能从字符串来判断特殊错误信息
			if strings.Contains(getClusterErr.Error(), fmt.Sprintf("belongs to account [%s] not exists", e.config.UserID)) {
				cancel()
				return
			}
			logger.Warnf(ctx, "cceClient.GetCluster failed, next try, clusterID: %s, %s", e.ClusterID, getClusterErr.Error())
			return
		}
		if clusterResp == nil || clusterResp.Cluster == nil {
			logger.Infof(ctx, "the cluster is deleted")
			cancel()
			return
		}

		deletedCluster := clusterResp.Cluster
		if deletedCluster.Status.ClusterPhase == types.ClusterPhaseDeleting ||
			deletedCluster.Status.ClusterPhase == types.ClusterPhaseDeleted {
			logger.Warnf(ctx, "the cluster is %v", deletedCluster.Status.ClusterPhase)
			return
		}
		return
	}, checkClusterInterval)

	if errors.Is(timeoutCtx.Err(), context.DeadlineExceeded) {
		err = fmt.Errorf("check cluster timeout for %d seconds", e.config.CreateClusterTimeoutSeconds)
		return
	}

	return
}

// ensureIaasReleased 确保IAAS资源被释放
func (e *Executor) ensureIaasReleased(ctx context.Context, instanceIDs []string) (err error) {
	// 检查instance是否删除成功
	if len(instanceIDs) == 0 {
		logger.Infof(ctx, "the instances are released")
		return
	}
	for _, id := range instanceIDs {
		_, descErr := e.bccClient.DescribeInstance(ctx, id, nil)
		if descErr != nil {
			if strings.Contains(descErr.Error(), "The specified object is not found or resource do not exist") {
				logger.Infof(ctx, "the bcc %s is not existed", id)
				continue
			}
			err = fmt.Errorf("bccClient.DescribeInstance %s failed: %v", id, descErr)
			return
		}
		err = fmt.Errorf("the bcc %s is still existed", id)
		return
	}
	// 检查master blb是否释放成功
	name := fmt.Sprintf("CCE/ClusterBLB/%s", e.ClusterID)
	masterBLB, descErr := e.appblbClient.DescribeAppLoadBalancersByName(ctx, name, nil)
	if descErr != nil {
		err = fmt.Errorf("appblbClient.DescribeAppLoadBalancersByName %s failed: %v", name, descErr)
		return
	}
	if len(masterBLB) > 0 {
		err = fmt.Errorf("the master blb is still existed, %v", utils.ToJSON(masterBLB))
		return
	}
	return
}
