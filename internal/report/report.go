package report

import (
	"bufio"
	"bytes"
	"context"
	"errors"
	"fmt"
	"html/template"
	"os"
	"path/filepath"
	"regexp"
	"sync"
	"time"

	bossdk "github.com/baidubce/bce-sdk-go/services/bos"
	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/cce-test/e2e-test/conf"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

const (
	reportWaitInterval = time.Second * 5
)

var (
	defaultTempLogDir = filepath.Join("temp", conf.DefaultBosJobLogPrefix)
)

type Reporter struct {
	batchID   string
	region    string
	qaDB      *models.Client
	bosClient *bossdk.Client
	config    *conf.Config
}

type ClusterInfo struct {
	ClusterID   string
	ClusterName string
	Region      string
}

type JobCase struct {
	Cost        string
	ClusterInfo ClusterInfo
	CaseLogMap  map[string][]string
	TestResults []models.TestResult
}

type CaseResult struct {
	Name       string
	Cost       string
	Success    bool
	ErrMessage string
	Logs       []string
}

type RenderHTMLData struct {
	ReportTime     string
	SuccessTotal   int
	FailedTotal    int
	JobCost        map[string]string
	JobCluster     map[string]ClusterInfo
	CaseResultList map[string][]CaseResult
}

func NewReporter(ctx context.Context, config *conf.Config) (reporter *Reporter, err error) {
	// 初始化qaDB client
	qaDB, newDBErr := models.NewClient(ctx, config.QAMySQLEndpoint)
	if newDBErr != nil {
		err = fmt.Errorf("models.NewClient failed: %v", newDBErr)
		return
	}

	reporter = &Reporter{
		batchID: config.BatchID,
		region:  config.Region,
		qaDB:    qaDB,
		config:  config,
	}
	return
}

func (r *Reporter) Build(ctx context.Context) (err error) {
	// 创建临时目录，用于存放从bos下载的日志
	err = os.MkdirAll(defaultTempLogDir, 0755)
	if err != nil {
		err = fmt.Errorf("os.MkdirAll %s failed: %v", defaultTempLogDir, err)
		return
	}
	// 初始化 bosClient（使用sdk）
	bosClient, newBosErr := bossdk.NewClient(r.config.AccessKey, r.config.AccessKeySecret, r.config.Endpoint.BOSEndpoint)
	if newBosErr != nil {
		err = fmt.Errorf("bossdk.NewClient failed: %v", newBosErr)
		return
	}
	r.bosClient = bosClient

	testJobList, ensureErr := r.ensureJobAllFinished(ctx)
	if ensureErr != nil {
		// 这里等待全部任务结束失败超时不退出，而是继续执行后续步骤，超时的case会显示为timeout
		logger.Warnf(ctx, ensureErr.Error())
	}
	if len(testJobList) == 0 {
		err = errors.New("no test job found")
		return
	}
	logger.Infof(ctx, "all jobs finished")

	// 收集日志，不需要返回错误，错误会导致最终缺少整个报告，错误类型都是日志找不到
	logger.Infof(ctx, "start to collectCaseLog")
	caseListGroupByJob := r.collectCaseLog(ctx, testJobList)
	logger.Infof(ctx, "collectCaseLog finish")

	var renderHTMLData RenderHTMLData
	jobCost := make(map[string]string)
	caseResultList := make(map[string][]CaseResult)
	jobClusterInfo := make(map[string]ClusterInfo)

	var successTotal, failedTotal int
	for jobName, jobCase := range caseListGroupByJob {
		if _, ok := caseResultList[jobName]; !ok {
			caseResultList[jobName] = make([]CaseResult, 0, len(jobCase.TestResults))
		}
		caseLogMap := jobCase.CaseLogMap

		for _, testResult := range jobCase.TestResults {
			caseResult := CaseResult{
				Name:       testResult.Name,
				Cost:       testResult.Cost.String(),
				Success:    testResult.Success,
				ErrMessage: testResult.ErrMessage,
				Logs:       caseLogMap[testResult.Name],
			}
			// 如果case超时则把错误信息覆盖为timeout
			if utils.IsDateZero(testResult.FinishedAt) {
				caseResult.ErrMessage = fmt.Sprintf("case timeout for %d seconds", r.config.ReportWaitTimeoutSeconds)
			}
			caseResultList[jobName] = append(caseResultList[jobName], caseResult)
			if testResult.Success {
				successTotal++
			} else {
				failedTotal++
			}
		}
		jobCost[jobName] = jobCase.Cost
		jobClusterInfo[jobName] = jobCase.ClusterInfo
	}
	renderHTMLData.JobCost = jobCost
	renderHTMLData.JobCluster = jobClusterInfo
	renderHTMLData.ReportTime = time.Now().Format("2006-01-02 15:04:05")
	renderHTMLData.SuccessTotal = successTotal
	renderHTMLData.FailedTotal = failedTotal
	renderHTMLData.CaseResultList = caseResultList

	logger.Infof(ctx, "start to build report html")
	html, buildErr := r.buildHTML(renderHTMLData)
	if buildErr != nil {
		err = fmt.Errorf("buildHTML failed: %v", buildErr)
		return
	}
	err = os.WriteFile("report.html", html, 0755)
	if err != nil {
		err = fmt.Errorf("os.WriteFile report failed: %v", err)
		return
	}
	logger.Infof(ctx, "build report html success")
	// 存在失败case需要设置错误信息，确保退出码是 1
	if failedTotal > 0 {
		err = fmt.Errorf("failed case count is %d", failedTotal)
	}
	return
}

func (r *Reporter) buildHTML(tplData RenderHTMLData) (output []byte, err error) {
	htmlTemplate, readErr := os.ReadFile(filepath.Join("templates", "report-template.html"))
	if readErr != nil {
		err = fmt.Errorf("os.ReadFile failed: %v", readErr)
		return
	}
	tpl := template.New("report")
	var tplInstance *template.Template
	tplInstance, err = tpl.Parse(string(htmlTemplate))
	if err != nil {
		err = fmt.Errorf("parse template failed, %v", err)
		return
	}
	buf := bytes.NewBuffer(nil)
	err = tplInstance.Execute(buf, tplData)
	if err != nil {
		err = fmt.Errorf("render template failed, %w", err)
		return
	}
	output = buf.Bytes()
	return
}

func (r *Reporter) collectCaseLog(ctx context.Context, testJobList []models.TestJob) (caseListGroupByJob map[string]JobCase) {
	caseListGroupByJob = make(map[string]JobCase)

	wg := sync.WaitGroup{}

	// 按job的粒度去收集日志
	for _, testJob := range testJobList {
		caseLogMap := make(map[string][]string)
		jobCase := JobCase{
			Cost: testJob.Cost.String(),
			ClusterInfo: ClusterInfo{
				ClusterID:   testJob.ClusterID,
				ClusterName: testJob.ClusterName,
				Region:      testJob.Region,
			},
			TestResults: testJob.TestResults,
		}
		caseListGroupByJob[testJob.Name] = jobCase
		// 暂时只收集错误的日志报告，如果没有失败的case，则直接跳过
		if testJob.FailedCount == 0 {
			continue
		}
		failedCaseList := make([]string, 0, len(testJob.TestResults))
		for _, testResult := range testJob.TestResults {
			if !testResult.Success {
				failedCaseList = append(failedCaseList, testResult.Name)
				// 初始化 case 名称与日志的映射
				if _, ok := caseLogMap[testResult.Name]; !ok {
					caseLogMap[testResult.Name] = make([]string, 0, 100)
				}
			}
		}
		wg.Add(1)
		go func(jobName string, jobCase JobCase) {
			defer wg.Done()

			// 从bos下载日志
			tempLogName := fmt.Sprintf("%s_%s.log", r.batchID, jobName)
			tempLogPath := filepath.Join(defaultTempLogDir, tempLogName)
			bosLogObjectName := filepath.Join(conf.DefaultBosJobLogPrefix, r.config.Region, tempLogName)
			getBosErr := r.bosClient.BasicGetObjectToFile(r.config.BosBucket, bosLogObjectName, tempLogPath)
			if getBosErr != nil {
				// 如果无法下载到日志文件直接退出协程
				logger.Errorf(ctx, "get log file `%s` from bos failed: %v", bosLogObjectName, getBosErr)
				return
			}

			f, openErr := os.Open(tempLogPath)
			if openErr != nil {
				logger.Errorf(ctx, "open log file `%s` failed: %v", tempLogPath, openErr)
				return
			}
			defer f.Close()

			// 解析日志
			// 单测和效果在 test/report_test.go 的 TestScanReport
			scanner := bufio.NewScanner(f)
			// 中间变量，用于标识失败CaseName的行开始，直到结束或换名字
			var failedCase string
			for scanner.Scan() {
				line := scanner.Text()
				// [CaseName: xxx]
				reg := regexp.MustCompile(`\[CaseName:\s{1}(\w+)\]`)
				matched := reg.FindStringSubmatch(line)

				// 如果行内有CaseName标记，则取出该标记的case
				var matchedCase string
				if len(matched) > 1 {
					matchedCase = matched[1]
				}

				if matchedCase == "" && failedCase != "" {
					// 普通行，但仍然是失败case的日志，虽然没有CaseName标记，也需要记录该行日志
					caseLogMap[failedCase] = append(caseLogMap[failedCase], line)
					continue
				} else if utils.ContainItem(failedCaseList, matchedCase) {
					// 是case行，且为失败case，给中间变量赋值代表错误行开始了
					failedCase = matchedCase
				} else {
					// 存在CaseName标记，但不是失败case，重置中间变量代表前一个错误case日志结束
					failedCase = ""
				}
				// 不是失败case的日志行跳过
				if failedCase == "" {
					continue
				}

				if _, ok := caseLogMap[failedCase]; !ok {
					caseLogMap[failedCase] = make([]string, 0, 100)
				}
				caseLogMap[failedCase] = append(caseLogMap[failedCase], line)
			}
			jobCase.CaseLogMap = caseLogMap
			caseListGroupByJob[jobName] = jobCase
		}(testJob.Name, jobCase)
	}

	wg.Wait()
	return
}

// ensureJobAllFinished 确保所有的job都执行完毕
func (r *Reporter) ensureJobAllFinished(ctx context.Context) (testJobList []models.TestJob, err error) {
	logger.Infof(ctx, "start to wait all jobs finish")
	timeoutCtx, cancel := context.WithTimeout(ctx, time.Duration(r.config.ReportWaitTimeoutSeconds)*time.Second)

	wait.UntilWithContext(timeoutCtx, func(ctx context.Context) {
		testJobs, listErr := r.qaDB.ListTestJob(models.ListTestJobConditions{
			BatchID: r.batchID,
			Region:  r.region,
		})
		if listErr != nil {
			logger.Warnf(ctx, "models.ListTestJob failed: %v", listErr)
			return
		}
		if len(testJobs) == 0 {
			logger.Warnf(ctx, "models.ListTestJob is empty, wait next loop")
			return
		}
		testJobList = testJobs

		hasUnFinished := false
		for _, testJob := range testJobs {
			if utils.IsDateZero(testJob.FinishedAt) {
				hasUnFinished = true
				// 存在没有完成的任务就遍历哪些case没有完成
				for _, testResult := range testJob.TestResults {
					if utils.IsDateZero(testResult.FinishedAt) {
						logger.Warnf(ctx, "case `%s/%s` is not finished yet", testJob.Name, testResult.Name)
					}
				}
			}
		}
		// 存在没有完成的任务就进行下一轮的检测
		if hasUnFinished {
			return
		}
		cancel()
	}, reportWaitInterval)
	if errors.Is(timeoutCtx.Err(), context.DeadlineExceeded) {
		err = fmt.Errorf("ensureJobAllFinished timeout for %d seconds", r.config.ReportWaitTimeoutSeconds)
		return
	}
	return
}
