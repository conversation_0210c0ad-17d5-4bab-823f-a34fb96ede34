/* utils.go */
/*
modification history
--------------------
2023/7/11, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
*/

package main

import (
	"bufio"
	"bytes"
	"context"
	"errors"
	"fmt"
	"net"
	"os"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/api/storage/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/remotecommand"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
)

var (
	pfsAddonName = "cce-csi-pfs-plugin"

	namespace        = "default"
	pvcName          = "csi-pvc-pfs"
	storageClassName = "pfs-sc"

	provisioner = "csi-clusterfileplugin"
	parentDir   = "/"
	podName     = "test-pvc-pod"
	mountPath   = "/pfs-volume"

	pfsAddon = `pfs:
  configEndpoint: PFS-ENDPOINT
  parentDir: /
nodes:
  - kubeletRootPath: /home/<USER>/kubelet
    kubeletRootPathAffinity: true
  - kubeletRootPath: /data/kubelet
    kubeletRootPathAffinity: true
  - kubeletRootPath: /var/lib/kubelet
    kubeletRootPathAffinity: true`
)

// GetConfig 获取用户信息
func GetConfig() *Config {
	var ak, sk string

	// 引导用户输入AK/SK
	reader := bufio.NewReader(os.Stdin)
	fmt.Print("Enter AccessKey: ")
	ak, _ = reader.ReadString('\n')
	fmt.Print("Enter SecretKey: ")
	sk, _ = reader.ReadString('\n')

	ak = strings.TrimSpace(ak)
	sk = strings.TrimSpace(sk)

	fmt.Print("Enter Region (bjtest/bj/su/bd/fwh/gz/hkg/nj): ")
	region, _ := reader.ReadString('\n')
	fmt.Print("Enter kubeConfigType public(公网访问)、vpc、internal(B区访问), default is public: ")
	kubeConfigType, _ := reader.ReadString('\n')
	fmt.Print("Enter PFS addon version, default is latest: ")
	addonVersion, _ := reader.ReadString('\n')
	fmt.Print("Enter ClusterID: ")
	clusterID, _ := reader.ReadString('\n')
	fmt.Print("Enter PFS Endpoint: ")
	endpoint, _ := reader.ReadString('\n')

	// 去除输入字符串的尾随换行符
	clusterID = strings.TrimSpace(clusterID)
	region = strings.TrimSpace(region)
	endpoint = strings.TrimSpace(endpoint)
	addonVersion = strings.TrimSpace(addonVersion)
	kubeConfigType = strings.TrimSpace(kubeConfigType)

	if kubeConfigType == "" {
		kubeConfigType = "public"
	}

	return &Config{
		AccessKeyID:     ak,
		SecretAccessKey: sk,
		Region:          region,
		ClusterID:       clusterID,
		Endpoint:        endpoint,
		KubeConfigType:  kubeConfigType,
		AddonVersion:    addonVersion,
	}
}

// CheckCIDRs 检查endpoint是否
func CheckCIDRs(endpoint, vpcCidr string) error {
	_, ipNet, _ := net.ParseCIDR(vpcCidr)
	pfsEndpoint := net.ParseIP(endpoint)
	if ipNet.Contains(pfsEndpoint) {
		fmt.Print("Check success\n")
	} else {
		return errors.New("Check failed\n")
	}
	return nil
}

// InstallAddon 安装PFS组件
func InstallAddon(ctx context.Context, client ccev2.Interface, config *Config) error {
	resp, _ := client.ListAddOns(ctx, config.ClusterID, &ccev2.ListParams{TargetAddons: pfsAddonName}, nil)
	if resp.Items[0].Instance == nil {
		pfsAddon = strings.ReplaceAll(pfsAddon, "PFS-ENDPOINT", config.Endpoint)
		_, err := client.InstallAddon(ctx, config.ClusterID, &ccev2.InstallParams{
			Name:    pfsAddonName,
			Params:  pfsAddon,
			Version: config.AddonVersion,
		}, nil,
		)
		if err != nil {
			return fmt.Errorf("Install addon failed: %v ", err.Error())
		}
		fmt.Print("Install addon success\n")
	}
	return nil
}

// DeloyStorageClass 部署StorageClass
func DeployStorageClass(ctx context.Context, client kubernetes.Interface, endpoint string) error {
	storageClassConfig := &v1.StorageClass{
		ObjectMeta: metav1.ObjectMeta{
			Name:      storageClassName,
			Namespace: namespace,
		},
		Provisioner:          provisioner,
		AllowVolumeExpansion: func(rebuild bool) *bool { return &rebuild }(true),
		Parameters: map[string]string{
			"clusterIP":   endpoint,
			"clusterPort": "8888",
			"parentDir":   parentDir,
		},
	}

	storageClass, err := client.StorageV1().StorageClasses().Create(ctx, storageClassConfig, metav1.CreateOptions{})

	if err != nil {
		return fmt.Errorf("Create pvc failed: %v ", err.Error())
	}

	fmt.Printf("storageclass %s status is created\n", storageClass.Name)
	return nil
}

// DeployPVC 部署pvc并等待其绑定pv
func DeployPVC(ctx context.Context, client kubernetes.Interface) error {
	pvcConfig := &corev1.PersistentVolumeClaim{
		ObjectMeta: metav1.ObjectMeta{
			Name:      pvcName,
			Namespace: namespace,
		},
		Spec: corev1.PersistentVolumeClaimSpec{
			AccessModes: []corev1.PersistentVolumeAccessMode{
				corev1.ReadWriteMany,
			},
			StorageClassName: &storageClassName,
			Resources: corev1.ResourceRequirements{
				Requests: corev1.ResourceList{
					corev1.ResourceStorage: resource.MustParse("50Gi"),
				},
			},
		},
	}

	_, err := client.CoreV1().PersistentVolumeClaims(namespace).Create(ctx, pvcConfig, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("Create pvc failed: %v ", err.Error())
	}

	// 等待pvc 为绑定状态，超时时间为5分钟
	timeoutCtx, cancelFn := context.WithTimeout(ctx, time.Minute*5)
	timeout := true

	wait.UntilWithContext(timeoutCtx, func(_ context.Context) {
		pvc, err := client.CoreV1().PersistentVolumeClaims(namespace).Get(ctx, pvcName, metav1.GetOptions{})
		if err != nil {
			fmt.Printf("Get pvc failed: %v ", err.Error())
			return
		}

		if pvc.Status.Phase == corev1.ClaimBound {
			cancelFn()
			timeout = false
		}
		fmt.Printf("pvc %s now status is %s , wait 30 second..\n", pvcName, pvc.Status.Phase)
	}, time.Second*30)

	if timeout {
		return fmt.Errorf("Wait pvc %s bound  timeout", pvcName)
	}

	fmt.Printf("pvc %s is bound\n", pvcName)
	return nil
}

// DeployPodWithPFS 部署挂载了pvc的容器，并等待容器running
func DeployPodWithPFS(ctx context.Context, client kubernetes.Interface) error {
	podConfig := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      podName,
			Namespace: namespace,
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name:  podName,
					Image: "hub.baidubce.com/cce/nginx-alpine-go:latest",
					VolumeMounts: []corev1.VolumeMount{
						{
							Name:      pvcName,
							MountPath: mountPath,
						},
					},
				},
			},
			Volumes: []corev1.Volume{
				{
					Name: pvcName,
					VolumeSource: corev1.VolumeSource{
						PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
							ClaimName: pvcName,
						},
					},
				},
			},
		},
	}

	_, err := client.CoreV1().Pods(namespace).Create(context.TODO(), podConfig, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("Create pod failed: %v ", err.Error())
	}

	// 等待pod running，超时时间为3分钟
	timeoutCtx, cancelFn := context.WithTimeout(ctx, time.Minute*3)
	timeout := true

	wait.UntilWithContext(timeoutCtx, func(_ context.Context) {
		pod, err := client.CoreV1().Pods(namespace).Get(ctx, podName, metav1.GetOptions{})
		if err != nil {
			fmt.Printf("Get pod failed: %v ", err.Error())
			return
		}

		if pod.Status.Phase == corev1.PodRunning {
			cancelFn()
			timeout = false
		}
		fmt.Printf("pod %s now status is %s , wait 10 second..\n", podName, pod.Status.Phase)
	}, time.Second*10)

	if timeout {
		return fmt.Errorf("Wait pod %s running  timeout", pvcName)
	}

	fmt.Printf("pod %s status is running\n", pvcName)
	return nil
}

// CheckVolume 检验容器侧读存储路径读写功能
func CheckVolume(client *Client) error {
	// 向挂点注入大量文件，校验挂载点文件数
	numbersFileCmd := []string{
		"sh",
		"-c",
		"for i in $(seq 10000);do mkdir -p /pfs-volume/pkg$i/{bin,conf,log};done",
	}
	if _, err := ExecInPod(client, numbersFileCmd); err != nil {
		return fmt.Errorf("Exec Cmd %s failed: %v ", numbersFileCmd, err.Error())
	}

	checkNumbersFileCmd := []string{"sh", "-c", "ls /pfs-volume | wc -l"}
	if result, err := ExecInPod(client, checkNumbersFileCmd); err != nil || result <= "10000" {
		fmt.Printf("%s size want >10000, got %s", mountPath, result)
		return fmt.Errorf("Exec Cmd %s failed: %v ", checkNumbersFileCmd, err.Error())
	}

	fmt.Print("Check success\n")
	return nil
}

func ExecInPod(client *Client, command []string) (string, error) {
	req := client.K8SClient.CoreV1().RESTClient().Post().Resource("pods").
		Name(podName).Namespace(namespace).SubResource("exec").
		VersionedParams(&corev1.PodExecOptions{
			Command: command,
			Stdin:   false,
			Stdout:  true,
			Stderr:  true,
			TTY:     false,
		}, scheme.ParameterCodec)
	exec, err := remotecommand.NewSPDYExecutor(client.KubeConfig, "POST", req.URL())
	if err != nil {
		return "", fmt.Errorf("New SPDYExecutor failed: %s ", err.Error())
	}

	var stdout, stderr bytes.Buffer
	err = exec.Stream(remotecommand.StreamOptions{
		Stdin:  nil,
		Stdout: &stdout,
		Stderr: &stderr,
		Tty:    false,
	})
	if err != nil {
		return "", fmt.Errorf("Exec failed: %v ", err.Error())
	}
	return stdout.String(), nil
}

// CleanResource 清理容器、pvc、pv、插件资源
func CleanResource(ctx context.Context, client kubernetes.Interface, cceClient ccev2.Interface, clusterID string) error {
	// 删除容器
	if err := client.CoreV1().Pods(namespace).Delete(ctx, podName, metav1.DeleteOptions{}); err != nil {
		return fmt.Errorf("Delete pod %s failed: %v ", podName, err.Error())
	}
	fmt.Printf("Delete pod %s success\n", podName)

	// 删除pvc
	pvc, _ := client.CoreV1().PersistentVolumeClaims(namespace).Get(ctx, pvcName, metav1.GetOptions{})
	pvName := pvc.Spec.VolumeName
	if err := client.CoreV1().PersistentVolumeClaims(namespace).Delete(ctx, pvcName, metav1.DeleteOptions{}); err != nil {
		return fmt.Errorf("Delete pvc %s failed: %v ", pvcName, err.Error())
	}
	fmt.Printf("Delete pvc %s success\n", pvcName)

	// 删除storageClass

	if err := client.StorageV1().StorageClasses().Delete(ctx, storageClassName, metav1.DeleteOptions{}); err != nil {
		return fmt.Errorf("Delete storageclass %s failed: %v ", storageClassName, err.Error())
	}
	fmt.Printf("Delete storageclass %s success\n", storageClassName)

	// 删除pv
	if err := client.CoreV1().PersistentVolumes().Delete(ctx, pvName, metav1.DeleteOptions{}); err != nil {
		return fmt.Errorf("Delete pv %s failed: %v ", pvName, err.Error())
	}
	fmt.Printf("Delete pv %s success\n", pvName)

	// 卸载组件
	if _, err := cceClient.UninstallAddon(ctx, clusterID, &ccev2.UninstallParams{Name: pfsAddonName}, nil); err != nil {
		return fmt.Errorf("Uninstall addon %s failed: %v ", pfsAddonName, err.Error())
	}
	fmt.Printf("Uninstall addon %s success\n", pfsAddonName)

	return nil
}
