/* main.go */
/*
modification history
--------------------
2023/7/10, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
*/

package main

import (
	"context"
	"fmt"
)

func main() {
	var ctx = context.TODO()

	config := GetConfig()
	client, err := NewClient(ctx, config)

	cluster, err := client.CCEClient.GetCluster(ctx, config.ClusterID, nil)
	if err != nil {
		fmt.Printf("Get cluster failed: %v", err.Error())
	}

	// 校验pfs是否和集群同vpc
	fmt.Print("\n1、Check pfs endpoint if belongs cluster's vpc\n")
	if err = CheckCIDRs(config.Endpoint, cluster.Cluster.Spec.VPCCIDR); err != nil {
		fmt.Printf("%v", err.Error())
		return
	}

	// 安装组件并校验组件状态
	fmt.Print("\n2、Install PFS plugin\n")
	if err = InstallAddon(ctx, client.CCEClient, config); err != nil {
		fmt.Printf("%v", err.Error())
		return
	}

	fmt.Print("\n2.5、Deploy stroageclass\n")
	if err = DeployStorageClass(ctx, client.K8SClient, config.Endpoint); err != nil {
		fmt.Printf("%v", err.Error())
		return
	}

	// 部署pvc等待其状态为bounding
	fmt.Print("\n3、Deploy pfs pvc and wait it to be bounded（Dynamic mount）\n")
	if err = DeployPVC(ctx, client.K8SClient); err != nil {
		fmt.Printf("%v", err.Error())
		return
	}

	// 部署挂载pfs的负载容器
	fmt.Print("\n4、Deploy pod with pfs volume and wait for it to run\n")
	if err = DeployPodWithPFS(ctx, client.K8SClient); err != nil {
		fmt.Printf("%v", err.Error())
		return
	}

	// 校验挂载路径读写正常
	fmt.Print("\n5、Check the read and write of the mount path in the container\n")
	if err = CheckVolume(client); err != nil {
		fmt.Printf("%v", err.Error())
		return
	}

	// 清理资源，组件卸载
	fmt.Print("\n6、Clean resource and uninstall addon\n")
	if err = CleanResource(ctx, client.K8SClient, client.CCEClient, config.ClusterID); err != nil {
		fmt.Printf("%v", err.Error())
		return
	}

	fmt.Print("\nPFS e2e test finished\n")
}
