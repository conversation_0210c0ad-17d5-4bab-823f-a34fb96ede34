/* client.go */
/*
modification history
--------------------
2023/7/11, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
*/

package main

import (
	"context"
	"fmt"
	"time"

	"k8s.io/client-go/kubernetes"
	restclient "k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
)

var Endpoints = map[string]string{
	"bj":     "cce.bj.baidubce.com/api/cce/service/v2",
	"gz":     "cce.gz.baidubce.com/api/cce/service/v2",
	"su":     "cce.su.baidubce.com/api/cce/service/v2",
	"nj":     "cce.nj.baidubce.com/api/cce/service/v2",
	"hkg":    "cce.hkg.baidubce.com/api/cce/service/v2",
	"fwh":    "cce.fwh.baidubce.com/api/cce/service/v2",
	"bd":     "cce.bd.baidubce.com/api/cce/service/v2",
	"bjtest": "*************:8793/api/cce/service/v2",
}

type Client struct {
	ClusterID  string
	KubeConfig *restclient.Config

	CCEClient ccev2.Interface
	K8SClient kubernetes.Interface
}

type MountConfig struct {
	pvcName          string
	podName          string
	volName          string
	storageClassName string
	staticPvName     string
	volumeHandle     string
	pfsl2ID          string
	mountPath        string
}

type StaticPVConfig struct {
	staticPvName string
	volumeHandle string
}

type Config struct {
	AccessKeyID     string
	SecretAccessKey string
	Region          string
	ClusterID       string
	PfsVPC          string
	KubeConfigType  string
	AddonVersion    string
}

func NewClient(ctx context.Context, config *Config) (*Client, error) {
	cceClient := ccev2.NewClient(&bce.Config{
		Credentials: &bce.Credentials{AccessKeyID: config.AccessKeyID, SecretAccessKey: config.SecretAccessKey},
		Endpoint:    Endpoints[config.Region],
	})
	cceClient.SetDebug(false)

	// 通过cceClient获取集群信息初始化K8SClient
	response, err := cceClient.GetAdminKubeConfig(ctx, config.ClusterID, models.KubeConfigType(config.KubeConfigType), nil)
	if err != nil {
		return nil, fmt.Errorf("Get cluster %s kubeconfig failed: %v ", config.ClusterID, err.Error())
	}
	kubeConfig, err := clientcmd.RESTConfigFromKubeConfig([]byte(response.KubeConfig))
	if err != nil {
		return nil, fmt.Errorf("RESTConfigFromKubeConfig failed: %s", err)
	}
	kubeConfig.Timeout = 30 * time.Second
	kubeConfig.ContentType = "application/vnd.kubernetes.protobuf"

	k8sClient, err := kubernetes.NewForConfig(kubeConfig)
	if err != nil {
		return nil, fmt.Errorf("kubernetes.NewForConfig failed: %s", err)
	}

	return &Client{
		ClusterID:  config.ClusterID,
		CCEClient:  cceClient,
		K8SClient:  k8sClient,
		KubeConfig: kubeConfig,
	}, nil
}
