/* utils.go */
/*
modification history
--------------------
2024/10/31, by shuzhou, create
*/
/*
DESCRIPTION
*/

package main

import (
	"bufio"
	"bytes"
	"context"
	"errors"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/api/storage/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/remotecommand"

	logic "icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/resource"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

var (
	pfsAddonName = "cce-csi-pfsl2-plugin"
	//挂载服务名
	mountPoint  = "mt-dq3uOK"
	provisioner = "spectrumscale.csi.ibm.com"

	namespace = "default"

	// dynamicMount
	DynamicMounts = []MountConfig{
		{
			pvcName:          "dynamic-pvc-1",
			podName:          "test-dynamic-pvc-pod-1",
			volName:          "dynamic-pfs-pvc",
			storageClassName: "pfsl2-sc-1",
			pfsl2ID:          "pfs-qnL8Jh",
			mountPath:        "/dynamic-pfs-volume-1",
		},
		{
			pvcName:          "dynamic-pvc-2",
			podName:          "test-dynamic-pvc-pod-2",
			volName:          "dynamic-pfs-pvc-2",
			storageClassName: "pfsl2-sc-2",
			pfsl2ID:          "pfs-VG0IEY",
			mountPath:        "/dynamic-pfs-volume-2",
		}}

	// staticMount
	StaticMounts = []MountConfig{
		{
			pvcName:      "static-pvc-1",
			podName:      "test-static-pvc-pod-1",
			volName:      "static-pfs-pvc-1",
			staticPvName: "static-pv-1",
			volumeHandle: "mt-dq3uOK;pfs-qnL8Jh;/pfs/pfs-qnL8Jh",
			pfsl2ID:      "pfs-qnL8Jh",
			mountPath:    "/static-pfs-volume-1",
		},
		{
			pvcName:      "static-pvc-2",
			podName:      "test-static-pvc-pod-2",
			volName:      "static-pfs-pvc-2",
			staticPvName: "static-pv-2",
			volumeHandle: "mt-dq3uOK;pfs-VG0IEY;/pfs/pfs-VG0IEY",
			pfsl2ID:      "pfs-VG0IEY",
			mountPath:    "/static-pfs-volume-2",
		}}

	CheckPFsl2Times = 9

	// //pfsl2 helm 安装参数
	pfsAddon = `storage:
  pfsId: pfs-qnL8Jh
  mountTargetId: mt-dq3uOK
  region: bj`
)

// GetConfig 获取用户信息
func GetConfig() *Config {
	var ak, sk string

	// 引导用户输入AK/SK
	reader := bufio.NewReader(os.Stdin)
	fmt.Print("Enter AccessKey: ")
	ak, _ = reader.ReadString('\n')
	fmt.Print("Enter SecretKey: ")
	sk, _ = reader.ReadString('\n')

	ak = strings.TrimSpace(ak)
	sk = strings.TrimSpace(sk)

	fmt.Println("PFSl2 service is mt-dq3uOK/mt-dq3uOK")
	fmt.Println("PFSl2 VPCID: vpc-xxht933t76fq (L5000)")
	fmt.Println("Regin is bjtest")
	fmt.Println("kubeConfigType is internal(B区访问)")
	fmt.Print("Enter PFS addon version, default is latest: ")
	addonVersion, _ := reader.ReadString('\n')
	fmt.Print("Enter ClusterID: ")
	clusterID, _ := reader.ReadString('\n')

	// 去除输入字符串的尾随换行符
	clusterID = strings.TrimSpace(clusterID)
	addonVersion = strings.TrimSpace(addonVersion)

	return &Config{
		AccessKeyID:     ak,
		SecretAccessKey: sk,
		Region:          "bjtest",
		ClusterID:       clusterID,
		PfsVPC:          "vpc-xxht933t76fq",
		KubeConfigType:  "internal",
		AddonVersion:    addonVersion,
	}
}

// Check cluster and pfsl2 VPC
func CheckVPC(pfsVpc, vpcID string) error {
	if pfsVpc != vpcID {
		return errors.New("Check failed, VPC is different! ")
	}
	fmt.Println("Check success, VPC is same! ")
	return nil
}

// InstallAddon 安装PFSL2组件
func (c *Client) InstallAddon(ctx context.Context, config *Config) error {
	resp, err := c.CCEClient.ListAddOns(ctx, config.ClusterID, &ccev2.ListParams{TargetAddons: pfsAddonName}, nil)
	if err != nil {
		return fmt.Errorf("ListAddOns failed: %v", err)
	}

	if len(resp.Items) == 0 {
		return errors.New("ListAddOns failed: no addons found")
	}

	if resp.Items[0].Instance != nil {
		fmt.Println("Instance is already installed!")
		return nil
	}

	_, err = c.CCEClient.InstallAddon(ctx, config.ClusterID, &ccev2.InstallParams{
		Name:    pfsAddonName,
		Params:  pfsAddon,
		Version: config.AddonVersion,
	}, nil)
	if err != nil {
		return fmt.Errorf("Install pfsl2 addon failed: %v ", err)
	}
	fmt.Println("Install pfsl2 addon success")
	return nil
}

// 卸载组件
func (c *Client) UninstallAddon(ctx context.Context, clusterID string) error {
	if _, err := c.CCEClient.UninstallAddon(ctx, clusterID, &ccev2.UninstallParams{Name: pfsAddonName}, nil); err != nil {
		return fmt.Errorf("Uninstall pfsl2 addon %s failed: %v ", pfsAddonName, err)
	}

	fmt.Printf("Uninstall pfsl2 addon %s success ", pfsAddonName)
	return nil
}

// Dynamic Mount
// DeloyStorageClass 部署StorageClass

func (c *Client) DeployStorageClass(ctx context.Context, mountConfigs []MountConfig) error {

	if len(mountConfigs) == 0 {
		return errors.New("mountConfigs is empty")
	}

	for _, mountConfig := range mountConfigs {
		storageClassConfig := &v1.StorageClass{
			ObjectMeta: metav1.ObjectMeta{
				Name:      mountConfig.storageClassName,
				Namespace: namespace,
			},
			Provisioner: provisioner,
			Parameters: map[string]string{
				"mountTargetId": mountPoint,
				"pfsId":         mountConfig.pfsl2ID,
			},
			ReclaimPolicy: func(rebuild corev1.PersistentVolumeReclaimPolicy) *corev1.PersistentVolumeReclaimPolicy {
				return &rebuild
			}(corev1.PersistentVolumeReclaimDelete),
			VolumeBindingMode: func(rebuild v1.VolumeBindingMode) *v1.VolumeBindingMode {
				return &rebuild
			}(v1.VolumeBindingImmediate),
		}

		storageClass, err := c.K8SClient.StorageV1().StorageClasses().Create(ctx, storageClassConfig, metav1.CreateOptions{})
		if err != nil {
			return fmt.Errorf("Create storageclss failed: %v ", err)
		}
		fmt.Printf("storageclass %s status is created\n", storageClass.Name)

	}

	return nil
}

// DeployDynamicPVC 部署pvc并等待其绑定pv ----- dynamicPvcName  -
func (c *Client) DeployDynamicPVC(ctx context.Context, mountConfigs []MountConfig) error {
	if len(mountConfigs) == 0 {
		return errors.New("mountConfigs is empty")
	}

	for _, mountConfig := range mountConfigs {
		pvcConfig := &corev1.PersistentVolumeClaim{
			ObjectMeta: metav1.ObjectMeta{
				Name:      mountConfig.pvcName,
				Namespace: namespace,
			},
			Spec: corev1.PersistentVolumeClaimSpec{
				AccessModes: []corev1.PersistentVolumeAccessMode{
					corev1.ReadWriteMany,
				},
				StorageClassName: &mountConfig.storageClassName,
				Resources: corev1.ResourceRequirements{
					Requests: corev1.ResourceList{
						corev1.ResourceStorage: resource.MustParse("50Gi"),
					},
				},
			},
		}
		_, err := c.K8SClient.CoreV1().PersistentVolumeClaims(namespace).Create(ctx, pvcConfig, metav1.CreateOptions{})
		if err != nil {
			return fmt.Errorf("Create pvc failed: %v ", err)
		}
	}

	if err := c.ConcurrencyCheckPvcBound(ctx, mountConfigs); err != nil {
		return fmt.Errorf("Dynamic pvc bound failed: %v ", err)
	}
	LogInfo("Dynamic pvc bound success")

	return nil
}

// DeployPV 部署PV---static mount
func (c *Client) DeployPV(ctx context.Context, mountConfigs []MountConfig) error {
	if len(mountConfigs) == 0 {
		return errors.New("mountConfigs are empty")
	}

	for _, mountConfig := range mountConfigs {
		pvConfig := &corev1.PersistentVolume{
			ObjectMeta: metav1.ObjectMeta{
				Name:      mountConfig.staticPvName,
				Namespace: namespace,
			},
			Spec: corev1.PersistentVolumeSpec{
				AccessModes: []corev1.PersistentVolumeAccessMode{
					corev1.ReadWriteMany,
				},
				Capacity: map[corev1.ResourceName]resource.Quantity{
					corev1.ResourceStorage: resource.MustParse("50Gi"),
				},
				PersistentVolumeSource: corev1.PersistentVolumeSource{
					CSI: &corev1.CSIPersistentVolumeSource{
						Driver:       "spectrumscale.csi.ibm.com",
						VolumeHandle: mountConfig.volumeHandle,
					},
				},
			},
		}

		_, err := c.K8SClient.CoreV1().PersistentVolumes().Create(ctx, pvConfig, metav1.CreateOptions{})
		if err != nil {
			return fmt.Errorf("Create pv failed: %v ", err)
		}

		fmt.Printf("pv %s is created\n", pvConfig.Name)
	}

	return nil
}

// DeployPVC 部署pvc并等待其绑定pv   ------ staticPvcName
func (c *Client) DeployStaticPVC(ctx context.Context, mountConfigs []MountConfig) error {

	if len(mountConfigs) == 0 {
		return errors.New("staticPvConfigs is empty")
	}

	for _, mountConfig := range mountConfigs {
		pvcConfig := &corev1.PersistentVolumeClaim{
			ObjectMeta: metav1.ObjectMeta{
				Name:      mountConfig.pvcName,
				Namespace: namespace,
			},
			Spec: corev1.PersistentVolumeClaimSpec{
				VolumeName: mountConfig.staticPvName,
				AccessModes: []corev1.PersistentVolumeAccessMode{
					corev1.ReadWriteMany,
				},
				Resources: corev1.ResourceRequirements{
					Requests: corev1.ResourceList{
						corev1.ResourceStorage: resource.MustParse("50Gi"),
					},
				},
			},
		}

		_, err := c.K8SClient.CoreV1().PersistentVolumeClaims(namespace).Create(ctx, pvcConfig, metav1.CreateOptions{})
		if err != nil {
			return fmt.Errorf("Create pvc failed: %v ", err)
		}
	}

	if err := c.ConcurrencyCheckPvcBound(ctx, mountConfigs); err != nil {
		return fmt.Errorf("Static pvc bound failed: %v ", err)
	}
	LogInfo("Static pvc bound success")

	return nil
}

// 等待绑定PVC
func (c *Client) WaitBoundPVC(ctx context.Context, mountConfig MountConfig) error {
	// 等待pvc 为绑定状态，超时时间为7分钟
	timeoutCtx, cancelFn := context.WithTimeout(ctx, time.Minute*7)
	wait.UntilWithContext(timeoutCtx, func(_ context.Context) {
		pvc, err := c.K8SClient.CoreV1().PersistentVolumeClaims(namespace).Get(ctx, mountConfig.pvcName, metav1.GetOptions{})
		if err != nil {
			fmt.Printf("Get pvc failed: %v ", err)
			return
		}

		if pvc.Status.Phase == corev1.ClaimBound {
			cancelFn()
			return
		}
		fmt.Printf("pvc %s now status is %s , wait 30 second..\n", mountConfig.pvcName, pvc.Status.Phase)
	}, time.Second*30)

	if errors.Is(timeoutCtx.Err(), context.DeadlineExceeded) {
		return fmt.Errorf("Wait pvc %s bound timeout ", mountConfig.pvcName)
	}

	fmt.Printf("pvc %s is bound\n", mountConfig.pvcName)
	return nil
}

func (c *Client) ConcurrencyCheckPvcBound(ctx context.Context, mountConfigs []MountConfig) error {
	var wg sync.WaitGroup
	var errChan = make(chan error, len(mountConfigs))
	var errs []error

	for _, mountConfig := range mountConfigs {
		wg.Add(1)
		go func(mc MountConfig) {
			defer wg.Done()
			if err := c.WaitBoundPVC(ctx, mc); err != nil {
				errChan <- err
			}
		}(mountConfig)
	}

	go func() {
		wg.Wait()
		close(errChan)
	}()

	for err := range errChan {
		if err != nil {
			errs = append(errs, err)
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("errors occurred %v", fmt.Sprintf("%d errors", len(errs)))
	}

	return nil

}

// DeployPodWithPFS 部署挂载了pvc的容器，并等待容器running ---done
func (c *Client) DeployPodWithPFS(ctx context.Context, mountConfigs []MountConfig) ([]string, error) {

	if len(mountConfigs) == 0 {
		return nil, errors.New("mountConfigs are empty")
	}

	var podNames []string
	var volumeMounts []corev1.VolumeMount
	var volumes []corev1.Volume

	if len(mountConfigs) == 1 {

		volumeMounts = append(volumeMounts, corev1.VolumeMount{
			Name:      mountConfigs[0].volName,
			MountPath: mountConfigs[0].mountPath,
		})

		volumes = append(volumes, corev1.Volume{
			Name: mountConfigs[0].volName,
			VolumeSource: corev1.VolumeSource{
				PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
					ClaimName: mountConfigs[0].pvcName,
				},
			},
		})

		if err := c.DeployPodWithMount(mountConfigs[0].podName, volumeMounts, volumes); err != nil {
			return nil, fmt.Errorf("Deploy pod mount failed: %v ", err)
		}

		fmt.Printf("deploy pod %v success\n", mountConfigs[0].podName)
		podNames = append(podNames, mountConfigs[0].podName)
		fmt.Printf("podNames: %v", podNames)

	} else {
		for _, mountConfig := range mountConfigs {
			singleVolumeMount := corev1.VolumeMount{
				Name:      mountConfig.volName,
				MountPath: mountConfig.mountPath,
			}
			singleVolume := corev1.Volume{
				Name: mountConfig.volName,
				VolumeSource: corev1.VolumeSource{
					PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
						ClaimName: mountConfig.pvcName,
					},
				},
			}
			if err := c.DeployPodWithMount(mountConfig.podName, []corev1.VolumeMount{singleVolumeMount}, []corev1.Volume{singleVolume}); err != nil {
				return nil, fmt.Errorf("Deploy pod mount failed: %v ", err)
			}

			fmt.Printf("deploy pod %v success\n", mountConfig.podName)

			volumeMounts = append(volumeMounts, singleVolumeMount)
			volumes = append(volumes, singleVolume)
			podNames = append(podNames, mountConfig.podName)
		}
		LogInfo("deploy pod with single mount success and deploy pod with multi mount")

		podName := strings.Join(podNames, "-")
		if err := c.DeployPodWithMount(podName, volumeMounts, volumes); err != nil {
			return nil, fmt.Errorf("Deploy pod  with multi mount failed: %v ", err)
		}
		fmt.Printf("deploy pod with multi mount %v success\n", podName)
		podNames = append(podNames, podName)
		fmt.Printf("podNames: %v", podNames)
	}

	LogInfo("begin to wait pod running")
	if err := c.ConcurrencyCheckPodRunning(ctx, podNames); err != nil {
		return nil, fmt.Errorf("Wait pods %v running failed: %v ", podNames, err)
	}
	LogInfo("wait pod running success")

	return podNames, nil
}

func (c *Client) DeployPodWithMount(podName string, volumeMounts []corev1.VolumeMount, volumes []corev1.Volume) error {

	podConfig := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      podName,
			Namespace: namespace,
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name:         podName,
					Image:        "hub.baidubce.com/cce/nginx-alpine-go:latest",
					VolumeMounts: volumeMounts,
				},
			},
			Volumes: volumes,
		},
	}

	_, err := c.K8SClient.CoreV1().Pods(namespace).Create(context.TODO(), podConfig, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("Create pod %v failed: %v ", podName, err)
	}

	return nil
}

func (c *Client) WaitPodRunning(ctx context.Context, podName string) error {

	// 等待pod running，超时时间为3分钟
	timeoutCtx, cancelFn := context.WithTimeout(ctx, time.Minute*3)

	wait.UntilWithContext(timeoutCtx, func(_ context.Context) {
		pod, err := c.K8SClient.CoreV1().Pods(namespace).Get(ctx, podName, metav1.GetOptions{})
		if err != nil {
			fmt.Printf("Get pod failed: %v ", err)
			return
		}

		if pod.Status.Phase == corev1.PodRunning {
			cancelFn()
			return
		}
		fmt.Printf("pod %s now status is %s , wait 10 second..\n", podName, pod.Status.Phase)
	}, time.Second*10)

	if errors.Is(timeoutCtx.Err(), context.DeadlineExceeded) {
		return fmt.Errorf("Wait pod %s running  timeout", podName)
	}

	fmt.Printf("pod %s status is running\n", podName)
	return nil

}

func (c *Client) ConcurrencyCheckPodRunning(ctx context.Context, podNames []string) error {
	var wg sync.WaitGroup
	var errChan = make(chan error, len(podNames))
	var errs []error

	for _, podName := range podNames {
		wg.Add(1)
		go func(name string) {
			defer wg.Done()
			if err := c.WaitPodRunning(ctx, name); err != nil {
				errChan <- err
			}
		}(podName)
	}

	go func() {
		wg.Wait()
		close(errChan)
	}()

	for err := range errChan {
		if err != nil {
			errs = append(errs, err)
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("errors occurred %v", fmt.Sprintf("%d errors", len(errs)))
	}

	return nil

}

func (c *Client) CheckPodsVolume(podsName []string) error {

	if len(podsName) == 0 {
		return errors.New("pods name is empty")
	}

	var wg sync.WaitGroup
	var errChan = make(chan error, len(podsName))
	var errs []error

	for _, podName := range podsName {
		wg.Add(1)

		go func(pN string) {
			defer wg.Done()
			if err := c.CheckVolume(pN); err != nil {
				errChan <- err
			}
		}(podName)

	}

	go func() {
		wg.Wait()
		close(errChan)
	}()

	for err := range errChan {
		if err != nil {
			errs = append(errs, err)
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("errors occurred %v", fmt.Sprintf("%d errors", len(errs)))
	}

	fmt.Printf("Check pods %v success", podsName)
	return nil

}

// CheckVolume 检验容器侧读存储路径读写功能
func (c *Client) CheckVolume(podName string) error {

	pod, err := c.K8SClient.CoreV1().Pods(namespace).Get(context.TODO(), podName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("Get pod %v failed: %v ", podName, err)
	}

	var volumeMounts []string

	for _, volumeMount := range pod.Spec.Containers[0].VolumeMounts {
		//跳过kube-api-access挂点
		if strings.Contains(volumeMount.Name, "kube-api-access") {
			continue
		}
		volumeMounts = append(volumeMounts, volumeMount.MountPath)
	}

	//对每个挂载点注入向挂点注入大量文件，校验挂载点文件数
	var wg sync.WaitGroup
	var errChan = make(chan error, len(volumeMounts))
	var errs []error

	for _, volumeMount := range volumeMounts {
		wg.Add(1)
		go func(vM string) {
			defer wg.Done()

			numbersFileCmd := []string{
				"sh",
				"-c",
				"for i in $(seq 10000);do mkdir -p {/MountPath}/pkg$i/{bin,conf,log};done",
			}
			strings.Replace(numbersFileCmd[2], "{/MountPath}", vM, -1)
			if _, err = c.ExecInPod(numbersFileCmd, podName); err != nil {
				err = fmt.Errorf("Exec Cmd %s failed: %v ", numbersFileCmd, err.Error())
				errChan <- err
			}

			checkNumbersFileCmd := []string{"sh", "-c", "ls {/MountPath} | wc -l"}
			strings.Replace(checkNumbersFileCmd[2], "{/MountPath}", vM, -1)
			if result, execErr := c.ExecInPod(checkNumbersFileCmd, podName); execErr != nil || result <= "10000" {
				err = fmt.Errorf("%s size want >10000, got %s, err: %v", vM, result, execErr.Error())
				errChan <- err
			}

		}(volumeMount)
	}

	go func() {
		wg.Wait()
		close(errChan)
	}()

	for err = range errChan {
		if err != nil {
			errs = append(errs, err)
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("errors occurred %v", fmt.Sprintf("%d errors", len(errs)))
	}

	fmt.Printf("Check %v volume success\n", podName)
	return nil
}

func (c *Client) ExecInPod(command []string, podName string) (string, error) {
	req := c.K8SClient.CoreV1().RESTClient().Post().Resource("pods").
		Name(podName).Namespace(namespace).SubResource("exec").
		VersionedParams(&corev1.PodExecOptions{
			Command: command,
			Stdin:   false,
			Stdout:  true,
			Stderr:  true,
			TTY:     false,
		}, scheme.ParameterCodec)
	exec, err := remotecommand.NewSPDYExecutor(c.KubeConfig, "POST", req.URL())
	if err != nil {
		return "", fmt.Errorf("New SPDYExecutor failed: %s ", err)
	}

	var stdout, stderr bytes.Buffer
	err = exec.Stream(remotecommand.StreamOptions{
		Stdin:  nil,
		Stdout: &stdout,
		Stderr: &stderr,
		Tty:    false,
	})
	if err != nil {
		return "", fmt.Errorf("Exec failed: %v ", err)
	}
	return stdout.String(), nil
}

// CleanDynamicAndStaticResource
func (c *Client) CleanResource(ctx context.Context, dynamicPodsNames []string, staticPodsNames []string) error {
	if err := c.CleanDynamicResource(ctx, DynamicMounts, dynamicPodsNames); err != nil {
		return fmt.Errorf("CleanStaticResource failed: %v", err)
	}

	if err := c.CleanStaticResource(ctx, StaticMounts, staticPodsNames); err != nil {
		return fmt.Errorf("CleanStaticResource failed: %v", err)
	}

	return nil
}

// CleanDynamicResource 清理容器、pvc、pv
func (c *Client) CleanDynamicResource(ctx context.Context, dynamicMounts []MountConfig, podNames []string) (er error) {

	if len(dynamicMounts) == 0 {
		return errors.New("dynamic mount is empty")
	}

	if len(podNames) == 0 {
		return errors.New("pod names is empty")
	}

	fmt.Println("Delete dynamic Resource")

	// 删除pod
	for _, podName := range podNames {
		fmt.Println("Delete dynamic pod")
		if err := c.K8SClient.CoreV1().Pods(namespace).Delete(ctx, podName, metav1.DeleteOptions{}); err != nil {
			return fmt.Errorf("Delete pod %s failed: %v ", podName, err)
		}
		fmt.Printf("Delete pod %s success\n", podName)
	}

	for _, mountConfig := range dynamicMounts {
		// 删除PVC
		fmt.Println("Delete dynamic PVC")
		dynamicPvc, _ := c.K8SClient.CoreV1().PersistentVolumeClaims(namespace).Get(ctx, mountConfig.pvcName, metav1.GetOptions{})
		dynamicPvName := dynamicPvc.Spec.VolumeName
		if err := c.K8SClient.CoreV1().PersistentVolumeClaims(namespace).Delete(ctx, mountConfig.pvcName, metav1.DeleteOptions{}); err != nil {
			return fmt.Errorf("Delete pvc %s failed: %v ", mountConfig.pvcName, err)
		}
		fmt.Printf("Delete pvc %s success\n", mountConfig.pvcName)

		// 删除storageClass
		if err := c.K8SClient.StorageV1().StorageClasses().Delete(ctx, mountConfig.storageClassName, metav1.DeleteOptions{}); err != nil {
			return fmt.Errorf("Delete storageclass %s failed: %v ", mountConfig.storageClassName, err)
		}
		fmt.Printf("Delete storageclass %s success\n", mountConfig.storageClassName)

		// 删除pv
		fmt.Println("Delete dynamic PV")
		if err := c.K8SClient.CoreV1().PersistentVolumes().Delete(ctx, dynamicPvName, metav1.DeleteOptions{}); err != nil {
			return fmt.Errorf("Delete pv %s failed: %v ", dynamicPvName, err)
		}
		fmt.Printf("Delete pv %s success\n", dynamicPvName)
	}

	return nil
}

// CleanStaticResource 清理容器、pvc、pv
func (c *Client) CleanStaticResource(ctx context.Context, staticMounts []MountConfig, podNames []string) (err error) {
	if len(staticMounts) == 0 {
		return errors.New("static mounts are empty")
	}

	if len(podNames) == 0 {
		return errors.New("pod names is empty")
	}

	fmt.Println("Delete dynamic Resource")

	// 删除pod
	for _, podName := range podNames {
		fmt.Println("Delete static pod")
		if err := c.K8SClient.CoreV1().Pods(namespace).Delete(ctx, podName, metav1.DeleteOptions{}); err != nil {
			return fmt.Errorf("Delete pod %s failed: %v ", podName, err)
		}
		fmt.Printf("Delete pod %s success\n", podName)
	}

	for _, mountConfig := range staticMounts {
		// 删除pvc
		fmt.Println("Delete static PVC")
		staticPvc, _ := c.K8SClient.CoreV1().PersistentVolumeClaims(namespace).Get(ctx, mountConfig.pvcName, metav1.GetOptions{})
		staticPvName := staticPvc.Spec.VolumeName
		if err := c.K8SClient.CoreV1().PersistentVolumeClaims(namespace).Delete(ctx, mountConfig.pvcName, metav1.DeleteOptions{}); err != nil {
			return fmt.Errorf("Delete pvc %s failed: %v ", mountConfig.pvcName, err)
		}
		fmt.Printf("Delete pvc %s success\n", mountConfig.pvcName)

		// 删除PV
		fmt.Println("Delete static PV")
		if err := c.K8SClient.CoreV1().PersistentVolumes().Delete(ctx, staticPvName, metav1.DeleteOptions{}); err != nil {
			return fmt.Errorf("Delete pv %s failed: %v ", staticPvName, err)
		}
		fmt.Printf("Delete pv %s success\n", staticPvName)

	}

	return nil
}

// check csinode
func (c *Client) CheckCsiNodeAndPfsl2Label(ctx context.Context) error {
	// 检查csinode状态
	// 超时时间为2分钟
	masterNodeIPs := map[string]struct{}{}

	clusterInfo, getErr := c.CCEClient.GetCluster(ctx, c.ClusterID, nil)
	if getErr != nil {
		return fmt.Errorf("get cluster info failed: %v", getErr)
	}

	if clusterInfo.Cluster.Spec.MasterConfig.MasterType != ccetypes.MasterTypeManagedPro {
		masterNodes, listErr := c.K8SClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{
			LabelSelector: "cluster-role=master"})
		if listErr != nil {
			return fmt.Errorf("list master nodes failed: %v", listErr)
		}

		for _, node := range masterNodes.Items {
			masterNodeIPs[node.Status.Addresses[0].Address] = struct{}{}
		}
	}

	fmt.Printf("master node ips: %v\n", masterNodeIPs)

	waitErr := logic.WaitForFunc(ctx, func(ctx context.Context) error {
		csiNode, err := c.K8SClient.StorageV1().CSINodes().List(ctx, metav1.ListOptions{})
		if err != nil {
			return fmt.Errorf("List CSINode failed: %v ", err)
		}
		if len(csiNode.Items) == 0 {
			return errors.New("CSINode not found")
		}

		// 过滤掉master节点
		var workNodes []v1.CSINode
		for _, node := range csiNode.Items {
			if _, ok := masterNodeIPs[node.OwnerReferences[0].Name]; ok {
				continue
			}
			workNodes = append(workNodes, node)
		}

		found := true
		for _, node := range workNodes {

			if len(node.Spec.Drivers) == 0 {
				found = false
				break
			}

			isFound := false

			for _, driver := range node.Spec.Drivers {

				if driver.Name == "spectrumscale.csi.ibm.com" {
					isFound = true
					break
				}
				isFound = false
			}

			if !isFound {
				found = false
				break
			}

			found = true
		}
		// 打印driver的信息
		if found {
			for _, node := range workNodes {
				fmt.Printf("csinode.dirver name: %v\n", node.Spec.Drivers)
			}
			fmt.Println("csinodes are ready")
			return nil
		}
		return errors.New("wait 10 second.. ")
	}, 10*time.Second, 2*time.Minute)

	if waitErr != nil {
		return errors.New("Wait csinode driver timeout ")
	}

	return nil
}

func LogInfo(a ...any) {
	fmt.Printf("\n%v\n", a)
}
