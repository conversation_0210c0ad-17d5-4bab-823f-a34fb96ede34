/* main.go */
/*
modification history
--------------------
2024/10/31, by shuzhou, create
*/
/*
DESCRIPTION
验证安装卸载10次pfsl2组件，
安装中，通过csinode是否挂载上了
第10次安装时，再检验动态挂载和静态挂载
最后删除

*/

package main

import (
	"context"
	"fmt"
	"log"
)

func main() {
	var ctx = context.TODO()

	config := GetConfig()
	client, err := NewClient(ctx, config)
	if err != nil {
		log.Fatalf("Failed to create client: %v", err)
	}

	cluster, err := client.CCEClient.GetCluster(ctx, config.ClusterID, nil)
	if err != nil {
		log.Fatalf("Failed to get cluster: %v", err)
	}

	// 校验pfs是否和集群同vpc
	LogInfo("Check pfsl2 VPC and cluster's VPC")
	if err = CheckVPC(config.PfsVPC, cluster.Cluster.Spec.VPCID); err != nil {
		log.Fatalf("VPC check failed: %v", err)
	}

	// 先进行pfsl2的安装删除
	LogInfo("install and uninstall pfsl2 and check csi node ", CheckPFsl2Times, " times")
	for i := 1; i <= CheckPFsl2Times; i++ {
		LogInfo("Check time, NO: ", i)
		LogInfo("Install PFS plugin")

		if err = client.InstallAddon(ctx, config); err != nil {
			log.Fatalf("pfsl2 install failed: %v", err)
		}

		if err = client.CheckCsiNode(ctx); err != nil {
			log.Fatalf("check csi node failed: %v", err)
		}

		if err = client.UninstallAddon(ctx, config.ClusterID); err != nil {
			log.Fatalf("pfsl2 uninstall failed: %v", err)
		}

		LogInfo("NO: ", i, " install and uninstall pfsl2, check succeeded.")
	}

	// 安装组件并校验组件状态
	LogInfo("NO: ", CheckPFsl2Times+1, " install and uninstall begin, dynamic and static mount check also ")
	LogInfo("1、 Install PFS plugin")
	if err = client.InstallAddon(ctx, config); err != nil {
		log.Fatalf("pfsl2 install failed: %v", err)
	}

	fmt.Println("2、Check Csinode ")
	if err = client.CheckCsiNode(ctx); err != nil {
		log.Fatalf("check csi node failed: %v", err)
	}

	LogInfo("2、Check Dynamic mount")
	LogInfo("2、1 Deploy stroageclass")
	if err = client.DeployStorageClass(ctx); err != nil {
		log.Fatalf("pfsl2 deploy storageclass failed: %v", err)
	}

	// 部署pvc等待其状态为bound
	LogInfo("2、2 Deploy pfs pvc and wait it to be bounded（Dynamic mount）")
	if err = client.DeployDynamicPVC(ctx, DynamicMount); err != nil {
		log.Fatalf("pfsl2 deploy dynamic pvc failed: %v", err)
	}
	// 部署挂载pfs的负载容器
	LogInfo("2、3 Deploy pod with pfs volume and wait for it to run")
	if err = client.DeployPodWithPFS(ctx, DynamicMount); err != nil {
		log.Fatalf("deploy pod with pfsl2 dynamic mount failed: %v", err)
	}

	// 校验挂载路径读写正常
	LogInfo("2、4 Check the read and write of the mount path in the container")
	if err = client.CheckVolume(DynamicMount.podName); err != nil {
		log.Fatalf("pfsl2 dynamic mount check volume failed: %v", err)
	}

	LogInfo("3、Check Static mount")
	LogInfo("3、1 Deploy pv")
	if err = client.DeployPV(ctx); err != nil {
		log.Fatalf("pfsl2 static mount deploy pv failed: %v", err)
	}

	// 部署pvc等待其状态为bound
	LogInfo("3、2 Deploy pfs pvc and wait it to be bounded（Static mount)")
	if err = client.DeployStaticPVC(ctx, StaticMount); err != nil {
		log.Fatalf("pfsl2 static mount deploy PVC failed: %v", err)
	}

	// 部署挂载pfs的负载容器
	LogInfo("3、3 Deploy pod with pfs volume and wait for it to run")
	if err = client.DeployPodWithPFS(ctx, StaticMount); err != nil {
		log.Fatalf("deploy pod with pfsl2 static mount failed", err)
	}

	// 校验挂载路径读写正常
	LogInfo("3、4 Check the read and write of the mount path in the container")
	if err = client.CheckVolume(StaticMount.podName); err != nil {
		log.Fatalf("pfsl2 static mount check volume failed: %v", err)
	}

	// 清理资源
	LogInfo("4、Clean resource")
	if err = client.CleanResource(ctx); err != nil {
		log.Fatalf("pfsl2 clean resource failed: %v", err)
	}

	// 卸载组件
	LogInfo("5、uninstallAddon")
	if err = client.UninstallAddon(ctx, config.ClusterID); err != nil {
		log.Fatalf("pfsl2 uninstallAddon failed: %v", err)
	}

	LogInfo("PFS e2e test finished")
}
