package models

import "time"

// StoragePod cds挂载的容器创建信息内容
type StoragePod struct {
	ID         int64         `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Region     string        `gorm:"column:region;size:10;index:idx_region_cluster_id" json:"region"`
	ClusterID  string        `gorm:"column:cluster_id;size:12;index:idx_region_cluster_id" json:"clusterID"`
	Name       string        `gorm:"column:name;size:50" json:"name"`      // 容器名称
	Model      string        `gorm:"column:model;size:20" json:"model"`    // 挂载方式（cds/bos/cfs）
	Success    bool          `gorm:"column:success" json:"success"`        // Success pod是否创建成功
	ErrMsg     string        `gorm:"column:err_msg" json:"errMsg"`         // ErrMsg 失败原因
	AttachCost time.Duration `gorm:"column:attach_cost" json:"attachCost"` // AttachCost cds挂载耗时
	CreatedAt  time.Time     `gorm:"column:created_at;autoCreateTime" json:"createdAt"`
	UpdatedAt  time.Time     `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"`
}

// TableName database tableName
func (s *StoragePod) TableName() string {
	return CCEStoragePodTableName
}

func (c *Client) CreateStoragePod(storagePodList []StoragePod) (err error) {
	err = c.db.Create(&storagePodList).Error
	return
}
