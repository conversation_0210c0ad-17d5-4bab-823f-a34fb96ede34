package models

import (
	"errors"
	"time"

	"gorm.io/gorm"
)

// TestJob - 定义回归测试任务
type TestJob struct {
	ID           int64         `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	BatchID      string        `gorm:"column:batch_id;size:15" json:"batchID"` // 标识当前测试批次
	Region       string        `gorm:"column:region;size:10" json:"region"`
	ClusterID    string        `gorm:"column:cluster_id;size:12" json:"clusterID"`
	ClusterName  string        `gorm:"column:cluster_name;size:128" json:"clusterName"`
	Name         string        `gorm:"column:name;size:128" json:"name"`         // Case名称
	SuccessCount int           `gorm:"column:success_count" json:"successCount"` // Case成功数量
	FailedCount  int           `gorm:"column:failed_count" json:"failedCount"`   // Case失败数量
	Cost         time.Duration `gorm:"column:cost" json:"cost"`                  // Case耗时
	TestResults  []TestResult  `gorm:"foreignKey:JobID" json:"testResults"`
	FinishedAt   time.Time     `gorm:"column:finished_at;default: '1970-01-01 08:00:00'" json:"finishedAt"`
	CreatedAt    time.Time     `gorm:"column:created_at;autoCreateTime" json:"createdAt"`
	UpdatedAt    time.Time     `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"`
}

func (t *TestJob) TableName() string {
	return CCETestJobTableName
}

func (c *Client) CreateTestJob(testJob *TestJob) (err error) {
	err = c.db.Create(&testJob).Error
	return
}

func (c *Client) UpdateTestJob(testJob TestJob) (err error) {
	err = c.db.
		Where("batch_id", testJob.BatchID).
		Where("region", testJob.Region).
		Updates(&testJob).Error
	return
}

func (c *Client) GetTestJob(batchID, region string, jobID int64) (testJob TestJob, err error) {
	err = c.db.
		Where("batch_id", batchID).
		Where("region", region).
		Where("id", jobID).
		Preload("TestResults").
		First(&testJob).Error
	return
}

func (c *Client) GetTestJobByName(name string) (testJob TestJob, exists bool, err error) {
	err = c.db.
		Where("name", name).
		Preload("TestResults").
		First(&testJob).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
		}
		return
	}
	exists = true
	return
}

type ListTestJobConditions struct {
	BatchID string
	Region  string
	Name    string
	Limit   int
}

func (c *Client) ListTestJob(conditions ListTestJobConditions) (testJobs []TestJob, err error) {
	db := c.db
	if len(conditions.BatchID) > 0 {
		db = db.Where("batch_id", conditions.BatchID)
	}
	if len(conditions.Region) > 0 {
		db = db.Where("region", conditions.Region)
	}
	if len(conditions.Name) > 0 {
		db = db.Where("name", conditions.Name)
	}
	if conditions.Limit > 0 {
		db = db.Limit(conditions.Limit)
	}
	err = db.Preload("TestResults", func(_db *gorm.DB) *gorm.DB {
		// 优化预加载排序
		return _db.Order("id ASC")
	}).Find(&testJobs).Error
	return
}
