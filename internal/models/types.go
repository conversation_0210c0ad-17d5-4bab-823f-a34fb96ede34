package models

import (
	"context"
	"fmt"
	"strings"
)

const (
	CCETestJobTableName      = "cce_test_job"
	CCETestResultTableName   = "cce_test_result"
	CCEEventTableName        = "cce_event"
	CCEClusterComboTableName = "cce_cluster_combo"
	CCENodeComboTableName    = "cce_node_combo"
	CCEClusterPerfTableName  = "cce_cluster_perf"
	CCEStoragePodTableName   = "cce_storage_pod"
	CCECNIPodTableName       = "cce_cni_pod"
	CCELBServiceTableName    = "cce_lb_service"
)

// KubeConfigType - kube config 类型
type KubeConfigType string

const (
	// KubeConfigTypeInternal 使用 BLB FloatingIP
	KubeConfigTypeInternal KubeConfigType = "internal"

	// KubeConfigTypeVPC 使用 BLB VPCIP
	KubeConfigTypeVPC KubeConfigType = "vpc"

	// KubeConfigTypePublic 使用 BLB EIP
	KubeConfigTypePublic KubeConfigType = "public"
)

// ServiceInstanceClusterRole t_instance 角色
type ServiceInstanceClusterRole string

const (
	// ServiceInstanceClusterRoleMaster 角色 master
	ServiceInstanceClusterRoleMaster ServiceInstanceClusterRole = "master"

	// ServiceInstanceClusterRoleSlave 角色 slave
	ServiceInstanceClusterRoleSlave ServiceInstanceClusterRole = "slave"
)

// ServiceInstanceStatus t_instance 状态
// http://icode.baidu.com/repos/baidu/bce-api/api-service-cce/blob/master:bce-service-cce-dao/src/main/java/com/baidu/bce/cce/dao/model/InstanceStepStatus.java
type ServiceInstanceStatus string

// NEED_PURCHASE
// BIDDING
// VM_CREATING
// VM_REBUILDING
// VM_CREATE_FAILED
// RUNNING
// DEPLOYING
// DEPLOY_FAILED,
// READY
// DESTROYING
// DESTROY_FAILED
// DESTROYED
// EIP_DELETING
// EIP_CREATE_FAILED
// VM_DELETING
// DELETED
// VM_REBUILD_FAILED
// ERROR
// DELETE_FAILED
// EXPIRED
// EXPIRE_DELETED
const (
	ServiceInstanceStatusNeedPurchase    ServiceInstanceStatus = "NEED_PURCHASE"
	ServiceInstanceStatusBidding         ServiceInstanceStatus = "BIDDING"
	ServiceInstanceStatusVMCreating      ServiceInstanceStatus = "VM_CREATING"
	ServiceInstanceStatusVMRebuilding    ServiceInstanceStatus = "VM_REBUILDING"
	ServiceInstanceStatusVMCreateFailed  ServiceInstanceStatus = "VM_CREATE_FAILED"
	ServiceInstanceStatusRunning         ServiceInstanceStatus = "RUNNING"
	ServiceInstanceStatusDeploying       ServiceInstanceStatus = "DEPLOYING"
	ServiceInstanceStatusDeployFailed    ServiceInstanceStatus = "DEPLOY_FAILED"
	ServiceInstanceStatusReady           ServiceInstanceStatus = "READY"
	ServiceInstanceStatusDestroying      ServiceInstanceStatus = "DESTROYING"
	ServiceInstanceStatusDestroyFailed   ServiceInstanceStatus = "DESTROY_FAILED"
	ServiceInstanceStatusDestroyed       ServiceInstanceStatus = "DESTROYED"
	ServiceInstanceStatusEIPDeleting     ServiceInstanceStatus = "EIP_DELETING"
	ServiceInstanceStatusEIPCreateFailed ServiceInstanceStatus = "EIP_CREATE_FAILED"
	ServiceInstanceStatusVMDeleting      ServiceInstanceStatus = "VM_DELETING"
	ServiceInstanceStatusDeleted         ServiceInstanceStatus = "DELETED"
	ServiceInstanceStatusVMRebuildFailed ServiceInstanceStatus = "VM_REBUILD_FAILED"
	ServiceInstanceStatusError           ServiceInstanceStatus = "ERROR"
	ServiceInstanceStatusDeleteFailed    ServiceInstanceStatus = "DELETE_FAILED"
	ServiceInstanceStatusExpired         ServiceInstanceStatus = "EXPIRED"
	ServiceInstanceStatusExpireDeleted   ServiceInstanceStatus = "EXPIRE_DELETED"
	ServiceInstanceStatusUnknown         ServiceInstanceStatus = "UNKNOWN"
)

// CheckKubeConfigType - 检查 KubeConfigType 是否合法
func CheckKubeConfigType(ctx context.Context, kubeConfigType string) error {
	if kubeConfigType != string(KubeConfigTypePublic) &&
		kubeConfigType != string(KubeConfigTypeInternal) &&
		kubeConfigType != string(KubeConfigTypeVPC) {
		return fmt.Errorf("KubeConfigType %s not valid", kubeConfigType)
	}

	return nil
}

func IsBAEndpoint(floatingIP string) bool {
	return strings.Contains(floatingIP, ":")
}

// Interface - models 接口定义
type Interface interface {
	Save(ctx context.Context, statistics *Statistics) error
	CreateStatistics(ctx context.Context, statistics *Statistics) error
}

// Statistics - 统计信息结构体
type Statistics struct {
	ClusterID             string
	InstanceID            string
	UserID                string
	Region                string
	StepName              string
	Success               bool
	Message               string
	CostSeconds           int64
	ExecCount             int
	ClusterRole           string
	MachineType           string
	InstanceOperationType InstanceOperationType
}

// InstanceOperationType - 实例操作类型
type InstanceOperationType string

const (
	// InstanceOperationTypeUnknown - 未知操作
	InstanceOperationTypeUnknown InstanceOperationType = "unknown"

	// InstanceOperationTypeCreate - 创建操作
	InstanceOperationTypeCreate InstanceOperationType = "create"

	// InstanceOperationTypeReinstall - 重装操作
	InstanceOperationTypeReinstall InstanceOperationType = "reinstall"

	// InstanceOperationTypeMoveIn - 迁入操作
	InstanceOperationTypeMoveIn InstanceOperationType = "move_in"

	// InstanceOperationTypeMoveOut - 迁出操作
	InstanceOperationTypeMoveOut InstanceOperationType = "move_out"

	// InstanceOperationTypeDelete - 删除操作
	InstanceOperationTypeDelete InstanceOperationType = "delete"
)
