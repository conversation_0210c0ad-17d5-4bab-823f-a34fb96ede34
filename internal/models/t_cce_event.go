package models

import "time"

type OperationType string

type OperationResult string

const (
	CreateClusterOperation OperationType = "create-cluster"
	DeleteClusterOperation OperationType = "delete-cluster"

	ScaleUpNodeOperation OperationType = "scale-up-node"
	ShrinkNodeOperation  OperationType = "shrink-node"

	OperationResultSuccess OperationResult = "success"
	OperationResultFailed  OperationResult = "failed"
)

// Event - CCE事件的采集
type Event struct {
	ID               int64         `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Region           string        `gorm:"column:region;size:10;index:idx_region_cluster_id" json:"region"`
	ClusterID        string        `gorm:"column:cluster_id;size:12;index:idx_region_cluster_id" json:"clusterID"`
	ClusterName      string        `gorm:"column:cluster_name;size:128" json:"clusterName"`
	OperationType    OperationType `gorm:"column:operation_type;size:20" json:"operationType"` // 事件的操作类型
	OperationSuccess bool          `gorm:"column:operation_success" json:"operationSuccess"`   // 操作结果
	AnalysisData     string        `gorm:"column:analysis_data;size:256" json:"analysisData"`  // 信息聚合
	AnalysisDig      string        `gorm:"column:analysis_dig" json:"analysisDig"`             // 失败原因分析（先将错误信息记录下来，后序再分析原因）
	CreatedAt        time.Time     `gorm:"column:created_at;autoCreateTime" json:"createdAt"`
	UpdatedAt        time.Time     `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"`
}

func (t *Event) TableName() string {
	return CCEEventTableName
}

func (c *Client) CreateEvent(eventList []Event) (err error) {
	err = c.db.Create(&eventList).Error
	return
}

func GetEventOperationResult(success bool) OperationResult {
	if success {
		return OperationResultSuccess
	}
	return OperationResultFailed
}
