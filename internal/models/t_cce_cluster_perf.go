package models

import "time"

// ClusterPerf 集群控制面性能信息内容
type ClusterPerf struct {
	ID                           int64         `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Region                       string        `gorm:"column:region;size:10;index:idx_region_cluster_id" json:"region"`
	ClusterID                    string        `gorm:"column:cluster_id;size:12;index:idx_region_cluster_id" json:"clusterID"`
	CreateCost                   time.Duration `gorm:"column:create_cost" json:"createCost"`                                       // create cost
	CreateClusterLBCost          time.Duration `gorm:"column:create_cluster_lb_cost" json:"createClusterLBCost"`                   // CreateClusterLB 创建BLB耗时
	CreateClusterEIPCost         time.Duration `gorm:"column:create_cluster_eip_cost" json:"createClusterEIPCost"`                 // CreateClusterEIP 创建EIP耗时
	WaitMasterInfrastructureCost time.Duration `gorm:"column:wait_master_infrastructure_cost" json:"waitMasterInfrastructureCost"` // 触发master创建
	WaitAPIServerAccessCost      time.Duration `gorm:"column:wait_api_server_access_cost" json:"waitApiServerAccessCost"`          // WaitAPIServerAccess 连通API耗时
	DeployK8SPluginCost          time.Duration `gorm:"column:deploy_k8s_plugin_cost" json:"deployK8SPluginCost"`                   // DeployK8SPlugin 部署插件耗时
	CreatedAt                    time.Time     `gorm:"column:created_at;autoCreateTime" json:"createdAt"`
	UpdatedAt                    time.Time     `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"`
}

// TableName database tableName
func (c *ClusterPerf) TableName() string {
	return CCEClusterPerfTableName
}

func (c *Client) CreateClusterPerf(clusterPerf ClusterPerf) (err error) {
	err = c.db.Create(&clusterPerf).Error
	return
}
