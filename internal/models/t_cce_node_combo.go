package models

import (
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bccimage"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

// NodeCombo 节点背景信息内容
type NodeCombo struct {
	ID              int64                `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Region          string               `gorm:"column:region;size:10;index:idx_region_cluster_id" json:"region"`
	ClusterID       string               `gorm:"column:cluster_id;size:12;index:idx_region_cluster_id" json:"clusterID"`
	ClusterRole     ccetypes.ClusterRole `gorm:"column:cluster_role" json:"clusterRole"`          // 节点类型 （master/node）
	CCEInstanceID   string               `gorm:"column:cce_instance_id" json:"cceInstanceID"`     // 节点名
	InstanceUUID    string               `gorm:"column:instance_uuid" json:"instanceUUID"`        // 虚机ID
	OrderID         string               `gorm:"column:order_id" json:"orderID"`                  // 虚机订单号
	AvailableZone   string               `gorm:"column:available_zone" json:"availableZone"`      // 可用区
	MachineType     ccetypes.MachineType `gorm:"column:machine_type" json:"machineType"`          // 节点虚机类型（BCC/BBC）
	InstanceType    bcc.InstanceType     `gorm:"column:instance_type" json:"instanceType"`        // 实例类型（N3/N4）
	InstanceSpec    string               `gorm:"column:instance_spec" json:"instanceSpec"`        // 实例规格 （cpu核数-mem（G））
	CreationType    string               `gorm:"column:creation_type" json:"creationType"`        // 创建方式 （internal/openapi）
	InstanceGroupID string               `gorm:"column:instance_group_id" json:"instanceGroupID"` // 所属节点组
	OsName          bccimage.OSName      `gorm:"column:os_name" json:"osName"`                    // 操作系统
	OsVersion       string               `gorm:"column:os_version" json:"osVersion"`              // 系统版本
	CreatedAt       time.Time            `gorm:"column:created_at;autoCreateTime" json:"createdAt"`
	UpdatedAt       time.Time            `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"`
}

// TableName database tableName
func (c *NodeCombo) TableName() string {
	return CCENodeComboTableName
}

func (c *Client) CreateNodeCombo(nodeComboList []NodeCombo) (err error) {
	err = c.db.Create(&nodeComboList).Error
	return
}
