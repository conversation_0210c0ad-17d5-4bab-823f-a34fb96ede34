package models

import "time"

type ServiceOperationType string

const (
	CreateServiceOperation ServiceOperationType = "create-lb-service"
	DeleteServiceOperation ServiceOperationType = "delete-lb-service"
)

// LBService cds挂载的容器创建信息内容
type LBService struct {
	ID            int64                `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Region        string               `gorm:"column:region;size:10;index:idx_region_cluster_id" json:"region"`
	ClusterID     string               `gorm:"column:cluster_id;size:12;index:idx_region_cluster_id" json:"clusterID"`
	OperationType ServiceOperationType `gorm:"column:operation_type" json:"operationType"` // 事件的操作类型
	Success       bool                 `gorm:"column:success" json:"success"`              // 操作结果
	Cost          time.Duration        `gorm:"column:cost" json:"cost"`                    // 端到端耗时
	CreatedAt     time.Time            `gorm:"column:created_at;autoCreateTime" json:"createdAt"`
	UpdatedAt     time.Time            `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"`
}

// TableName database tableName
func (s *LBService) TableName() string {
	return CCELBServiceTableName
}

func (c *Client) CreateLBService(lbService LBService) (err error) {
	err = c.db.Create(&lbService).Error
	return
}
