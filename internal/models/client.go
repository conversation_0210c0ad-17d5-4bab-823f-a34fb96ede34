package models

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

type Client struct {
	db *gorm.DB
}

var (
	mysqlConnMap = map[string]*gorm.DB{}
)

// NewClient - 初始化 Client
func NewClient(ctx context.Context, mysqlConn string) (client *Client, err error) {
	if mysqlConn == "" {
		return nil, errors.New("mysqlConn is empty")
	}

	var db *gorm.DB

	// 复用已有连接
	if existedDB, ok := mysqlConnMap[mysqlConn]; ok {
		if existedDB == nil {
			err = errors.New("DB in mysqlConnMap is nil")
			return
		}
		db = existedDB
	} else {
		db, err = gorm.Open(mysql.Open(mysqlConn), &gorm.Config{})
		if err != nil {
			err = fmt.Errorf("open DB failed: %v", err)
			return
		}

		mysqlConnMap[mysqlConn] = db
	}

	// Init DB
	sqlDB, err := db.DB()
	if err != nil {
		err = fmt.Errorf("init DB failed: %v", err)
		return
	}
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetConnMaxLifetime(0)

	client = &Client{
		db: db,
	}

	return
}

// Save - 保存统计信息
func (c *Client) Save(ctx context.Context, statistics *Statistics) error {
	// 这里应该实现保存统计信息到数据库的逻辑
	// 目前返回 nil 以确保编译通过
	return nil
}

// CreateStatistics - 创建统计信息
func (c *Client) CreateStatistics(ctx context.Context, statistics *Statistics) error {
	// 这里应该实现创建统计信息到数据库的逻辑
	// 目前返回 nil 以确保编译通过
	return nil
}
