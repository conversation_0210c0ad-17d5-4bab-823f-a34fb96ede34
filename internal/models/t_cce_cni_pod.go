package models

import "time"

// CNIPod vpc-cni网络模式下的容器创建信息内容
type CNIPod struct {
	ID        int64         `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Region    string        `gorm:"column:region;size:10;index:idx_region_cluster_id" json:"region"`
	ClusterID string        `gorm:"column:cluster_id;size:12;index:idx_region_cluster_id" json:"clusterID"`
	IPEnough  bool          `gorm:"column:ipEnough" json:"ipEnough"` // IPEnough 创建pod时的辅助IP是否充足
	Success   bool          `gorm:"column:success" json:"success"`   // Success pod是否创建成功
	ErrMsg    string        `gorm:"column:err_msg" json:"errMsg"`    // ErrMsg 失败原因
	Cost      time.Duration `gorm:"column:cost" json:"cost"`         // 创建耗时
	CreatedAt time.Time     `gorm:"column:created_at;autoCreateTime" json:"createdAt"`
	UpdatedAt time.Time     `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"`
}

// TableName database tableName
func (s *CNIPod) TableName() string {
	return CCECNIPodTableName
}

func (c *Client) CreateCNIPod(cniPod CNIPod) (err error) {
	err = c.db.Create(&cniPod).Error
	return
}
