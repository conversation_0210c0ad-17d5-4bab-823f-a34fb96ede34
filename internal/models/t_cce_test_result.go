package models

import (
	"time"
)

// TestResult - 定义回归测试 Case 结果
type TestResult struct {
	ID         int64         `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	JobID      int64         `gorm:"column:job_id" json:"jobID"`                     // 任务ID
	Name       string        `gorm:"column:name;size:128" json:"name"`               // Case名称
	Success    bool          `gorm:"column:success" json:"success"`                  // Case是否成功
	ErrMessage string        `gorm:"column:err_message;size:1024" json:"errMessage"` // Case失败消息
	Cost       time.Duration `gorm:"column:cost" json:"cost"`                        // Case耗时
	FinishedAt time.Time     `gorm:"column:finished_at;default: '1970-01-01 08:00:00'" json:"finishedAt"`
	CreatedAt  time.Time     `gorm:"column:created_at;autoCreateTime" json:"createdAt"`
	UpdatedAt  time.Time     `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"`
}

func (t *TestResult) TableName() string {
	return CCETestResultTableName
}

type ListTestResultConditions struct {
	JobID int64
	Name  string
	Limit int
}

func (c *Client) CreateTestResult(testResultList []TestResult) (err error) {
	if len(testResultList) == 0 {
		return
	}
	err = c.db.Create(&testResultList).Error
	return
}

func (c *Client) UpdateTestResult(testResult TestResult) (err error) {
	err = c.db.
		Where("job_id", testResult.JobID).
		Where("name", testResult.Name).
		Updates(&testResult).Error
	return
}

func (c *Client) ListTestResult(conditions ListTestResultConditions) (testResults []TestResult, err error) {
	db := c.db
	if conditions.JobID > 0 {
		db = db.Where("job_id", conditions.JobID)
	}
	if len(conditions.Name) > 0 {
		db = db.Where("name", conditions.Name)
	}
	if conditions.Limit > 0 {
		db = db.Limit(conditions.Limit)
	}
	err = db.Order("id ASC").Find(&testResults).Error
	return
}
