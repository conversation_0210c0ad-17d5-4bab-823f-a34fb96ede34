package models

import (
	"time"

	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

// ClusterCombo 集群背景信息内容
type ClusterCombo struct {
	ID             int64                         `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Region         string                        `gorm:"column:region;size:10;index:idx_region_cluster_id" json:"region"`
	ClusterID      string                        `gorm:"column:cluster_id;size:12;index:idx_region_cluster_id" json:"clusterID"`
	MasterType     ccetypes.MasterType           `gorm:"column:master_type;size:20" json:"masterType"`         // 集群类型 （独立/托管/serverless）
	K8SVersion     ccetypes.K8SVersion           `gorm:"column:k8s_version;size:20" json:"k8sVersion"`         // k8s版本（1.16.8/1.18.9/1.20.8）
	RuntimeType    ccetypes.RuntimeType          `gorm:"column:runtime_type;size:20" json:"runtimeType"`       // 集群运行时（docker/containerd）
	RuntimeVersion string                        `gorm:"column:runtime_version;size:20" json:"runtimeVersion"` // 运行时版本
	NetworkMode    ccetypes.ContainerNetworkMode `gorm:"column:network_mode;size:128" json:"networkMode"`      // 网络模式
	KubeProxy      ccetypes.KubeProxyMode        `gorm:"column:kube_proxy;size:20" json:"kubeProxy"`           // kube proxy
	CreatedAt      time.Time                     `gorm:"column:created_at;autoCreateTime" json:"createdAt"`
	UpdatedAt      time.Time                     `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"`
}

// TableName database tableName
func (c *ClusterCombo) TableName() string {
	return CCEClusterComboTableName
}

func (c *Client) CreateClusterCombo(clusterCombo ClusterCombo) (err error) {
	err = c.db.Create(&clusterCombo).Error
	return
}
