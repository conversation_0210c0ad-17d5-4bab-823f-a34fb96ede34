package baidubci

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/virtual-kubelet/virtual-kubelet/log"
	v1 "k8s.io/api/core/v1"
	runtimeapi "k8s.io/cri-api/pkg/apis/runtime/v1"
	kubecontainer "k8s.io/kubernetes/pkg/kubelet/container"
	"k8s.io/kubernetes/pkg/kubelet/util/format"
	"k8s.io/utils/pointer"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
)

type podDNSType int

const (
	podDNSCluster podDNSType = iota
	podDNSHost
	podDNSNone
)

const (
	ClusterDomain = "cluster.local"

	PodDNSConfigLabelKey = "vk.bci.baidu.com/dns-config"
)

var (
	defaultDNSOptions    = []string{"ndots:5"}
	defaultPodDNSOptions = []v1.PodDNSConfigOption{
		{
			Name:  "ndots",
			Value: pointer.String("5"),
		},
	}
)

func injectDNSConfig(ctx context.Context, cfg *bci.PodConfig, pod *v1.Pod, clusterDNSServers []string, nodeDNSConfig *v1.PodDNSConfig) error {
	DNSConfig, podDNSConfig, err := getPodDNS(ctx, pod, clusterDNSServers, nodeDNSConfig)
	if err != nil {
		return err
	}

	if len(DNSConfig.Options) == 0 && len(DNSConfig.Searches) == 0 && len(DNSConfig.Servers) == 0 {
		// 不需要设置DNS配置
		return nil
	}

	DNSConfigBytes, err := json.Marshal(DNSConfig)
	if err != nil {
		return err
	}

	cfg.Labels = append(cfg.Labels, bci.PodLabel{
		LabelKey:   PodDNSConfigLabelKey,
		LabelValue: string(DNSConfigBytes),
	})
	cfg.DNSConfig = podDNSConfig

	return nil
}

func getPodDNSType(pod *v1.Pod) (podDNSType, error) {
	DNSPolicy := pod.Spec.DNSPolicy
	switch DNSPolicy {
	case v1.DNSNone:
		return podDNSNone, nil
	case v1.DNSClusterFirstWithHostNet:
		return podDNSCluster, nil
	case v1.DNSClusterFirst:
		if !kubecontainer.IsHostNetworkPod(pod) {
			return podDNSCluster, nil
		}
		// Fallback to DNSDefault for pod on hostnetowrk.
		fallthrough
	case v1.DNSDefault:
		return podDNSHost, nil
	}
	// This should not happen as kube-apiserver should have rejected
	// invalid DNSPolicy.
	return podDNSCluster, fmt.Errorf(fmt.Sprintf("invalid DNSPolicy=%v", DNSPolicy))
}

// getPodDNS returns DNS settings for the pod.
func getPodDNS(ctx context.Context, pod *v1.Pod, clusterDNSServers []string, nodeDNSConfig *v1.PodDNSConfig) (*runtimeapi.DNSConfig, *v1.PodDNSConfig, error) {
	bciDNSConfig := &runtimeapi.DNSConfig{}
	podDNSConfig := &v1.PodDNSConfig{}
	DNSType, err := getPodDNSType(pod)
	if err != nil {
		log.G(ctx).Errorf("Failed to get DNS type for pod %q: %v. Falling back to DNSClusterFirst policy.", format.Pod(pod), err)
		DNSType = podDNSCluster
	}
	switch DNSType {
	case podDNSNone:
		// DNSNone should use empty DNS settings as the base.
	case podDNSCluster:
		if len(clusterDNSServers) != 0 {
			// For a pod with DNSClusterFirst policy, the cluster DNS server is
			// the only nameserver configured for the pod. The cluster DNS server
			// itself will forward queries to other nameservers that is configured
			// to use, in case the cluster DNS server cannot resolve the DNS query
			// itself.
			podDNSConfig.Nameservers = []string{}
			bciDNSConfig.Servers = []string{}
			for _, ip := range clusterDNSServers {
				bciDNSConfig.Servers = append(bciDNSConfig.Servers, ip)
				podDNSConfig.Nameservers = append(podDNSConfig.Nameservers, ip)
			}

			bciDNSConfig.Searches = generateSearchesForDNSClusterFirst(pod)
			bciDNSConfig.Options = defaultDNSOptions

			podDNSConfig.Searches = generateSearchesForDNSClusterFirst(pod)
			podDNSConfig.Options = defaultPodDNSOptions

			break
		}
		// clusterDNS is not known. Pod with ClusterDNSFirst Policy cannot be created.
		nodeErrorMsg := fmt.Sprintf("kubelet does not have ClusterDNS IP configured and cannot create Pod using %q policy. "+
			"Falling back to %q policy.", v1.DNSClusterFirst, v1.DNSDefault)
		log.G(ctx).Warnf("setup %s DNS config failed, err: %v", v1.DNSClusterFirst, nodeErrorMsg)
		// Fallback to DNSDefault.
		// fallthrough
	case podDNSHost:
		if nodeDNSConfig != nil {
			podDNSConfig.Nameservers = []string{}
			bciDNSConfig.Servers = []string{}

			bciDNSConfig.Servers = append(bciDNSConfig.Servers, nodeDNSConfig.Nameservers...)
			podDNSConfig.Nameservers = append(podDNSConfig.Nameservers, nodeDNSConfig.Nameservers...)

			bciDNSConfig.Searches = nodeDNSConfig.Searches
			bciDNSConfig.Options = mergeDNSOptions(nil, nodeDNSConfig.Options)

			podDNSConfig.Searches = nodeDNSConfig.Searches
			podDNSConfig.Options = nodeDNSConfig.Options

			break
		}
	}

	if pod.Spec.DNSConfig != nil {
		bciDNSConfig = appendDNSConfig(bciDNSConfig, pod.Spec.DNSConfig)
		podDNSConfig = appendPodDNSConfig(podDNSConfig, pod.Spec.DNSConfig)
	}
	return bciDNSConfig, podDNSConfig, nil
}

func generateSearchesForDNSClusterFirst(pod *v1.Pod) []string {
	nsSvcDomain := fmt.Sprintf("%s.svc.%s", pod.Namespace, ClusterDomain)
	svcDomain := fmt.Sprintf("svc.%s", ClusterDomain)
	clusterSearch := []string{nsSvcDomain, svcDomain, ClusterDomain}

	return clusterSearch
}

// appendDNSConfig appends DNS servers, search paths and options given by
// PodDNSConfig to the existing DNS config. Duplicated entries will be merged.
// This assumes existingDNSConfig and DNSConfig are not nil.
func appendDNSConfig(existingDNSConfig *runtimeapi.DNSConfig, DNSConfig *v1.PodDNSConfig) *runtimeapi.DNSConfig {
	existingDNSConfig.Servers = omitDuplicates(append(existingDNSConfig.Servers, DNSConfig.Nameservers...))
	existingDNSConfig.Searches = omitDuplicates(append(existingDNSConfig.Searches, DNSConfig.Searches...))
	existingDNSConfig.Options = mergeDNSOptions(existingDNSConfig.Options, DNSConfig.Options)
	return existingDNSConfig
}

func appendPodDNSConfig(existingDNSConfig *v1.PodDNSConfig, DNSConfig *v1.PodDNSConfig) *v1.PodDNSConfig {
	existingDNSConfig.Nameservers = omitDuplicates(append(existingDNSConfig.Nameservers, DNSConfig.Nameservers...))
	existingDNSConfig.Searches = omitDuplicates(append(existingDNSConfig.Searches, DNSConfig.Searches...))
	existingDNSConfig.Options = mergePodDNSConfig(existingDNSConfig.Options, DNSConfig.Options)
	return existingDNSConfig
}

func omitDuplicates(strs []string) []string {
	uniqueStrs := make(map[string]bool)

	var ret []string
	for _, str := range strs {
		if !uniqueStrs[str] {
			ret = append(ret, str)
			uniqueStrs[str] = true
		}
	}
	return ret
}

// mergeDNSOptions merges DNS options. If duplicated, entries given by PodDNSConfigOption will
// overwrite the existing ones.
func mergeDNSOptions(existingDNSConfigOptions []string, DNSConfigOptions []v1.PodDNSConfigOption) []string {
	optionsMap := make(map[string]string)
	for _, op := range existingDNSConfigOptions {
		if index := strings.Index(op, ":"); index != -1 {
			optionsMap[op[:index]] = op[index+1:]
		} else {
			optionsMap[op] = ""
		}
	}
	for _, op := range DNSConfigOptions {
		if op.Value != nil {
			optionsMap[op.Name] = *op.Value
		} else {
			optionsMap[op.Name] = ""
		}
	}
	// Reconvert DNS options into a string array.
	options := []string{}
	for opName, opValue := range optionsMap {
		op := opName
		if opValue != "" {
			op = op + ":" + opValue
		}
		options = append(options, op)
	}
	return options
}

func mergePodDNSConfig(existingConfig []v1.PodDNSConfigOption, podDNSConfig []v1.PodDNSConfigOption) []v1.PodDNSConfigOption {
	optionsMap := make(map[string]*string)
	for _, opt := range existingConfig {
		if opt.Value == nil {
			continue
		}
		optionsMap[opt.Name] = opt.Value
	}
	for _, opt := range podDNSConfig {
		if opt.Value == nil {
			continue
		}
		optionsMap[opt.Name] = opt.Value
	}
	opts := []v1.PodDNSConfigOption{}
	for optName, optValue := range optionsMap {
		opts = append(opts, v1.PodDNSConfigOption{Name: optName, Value: optValue})
	}
	return opts
}
