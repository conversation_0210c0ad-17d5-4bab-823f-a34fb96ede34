package baidubci

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"

	"code.cloudfoundry.org/clock"
	"github.com/virtual-kubelet/virtual-kubelet/log"
	"github.com/virtual-kubelet/virtual-kubelet/trace"
	corev1 "k8s.io/api/core/v1"
	k8serror "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/client-go/util/workqueue"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
)

const (
	// maxRetries is the number of times we try to process a given key before permanently forgetting it.
	maxRetries = 20

	rescueRequestKey ctxKeyType = "rescueRequestKey"
)

type queueHandler func(ctx context.Context, key string) error

type ctxKeyType string

//go:generate mockgen -destination ./mock_pod_rescuer.go -package baidu_bci -self_package icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci PodRescuer

// PodRescuer rescues pods whose bci order failed.
type PodRescuer interface {
	Run(ctx context.Context)
	RescuePod(ctx context.Context, namespace, name, uid string)
	IsRescuing(ctx context.Context, namespace, name, uid string) bool
	NotifyBCICreated(ctx context.Context, namespace, name, uid string)
}

type podRescuer struct {
	podsToRescueQ workqueue.RateLimitingInterface

	sync.Mutex
	// rescuing stores all pods on rescuing.
	// Negative value means the pod is in rescuing and cannot be interrupted.
	// Positive value means the pod creation request is returned successfully at the timestamp.
	rescuing map[string]int64
	// workers count
	workers int

	provider *BCIProvider

	// time source
	clock clock.Clock
	// maxRescuingTime is the max latency between pod creation request and the corresponding pod appearance in pod cache. Unit is second.
	maxRescuingTime int64
}

func NewPodRescuer(ctx context.Context, workers int, provider *BCIProvider) PodRescuer {
	return &podRescuer{
		podsToRescueQ:   workqueue.NewNamedRateLimitingQueue(workqueue.DefaultControllerRateLimiter(), "podsToRescueQ"),
		rescuing:        make(map[string]int64),
		workers:         workers,
		provider:        provider,
		clock:           clock.NewClock(),
		maxRescuingTime: 60,
	}
}

func (ps *podRescuer) RescuePod(ctx context.Context, namespace, name, uid string) {
	ps.podsToRescueQ.AddRateLimited(ps.generateKey(namespace, name, uid))
}

func (*podRescuer) generateKey(namespace, name, uid string) string {
	return fmt.Sprintf("%s/%s/%s", namespace, name, uid)
}

func (*podRescuer) splitMetaNamespaceKey(key string) (string, string, string, error) {
	parts := strings.Split(key, "/")
	if len(parts) != 3 {
		return "", "", "", fmt.Errorf("invalid pod rescuer key %s", key)
	}
	return parts[0], parts[1], parts[2], nil
}

func (ps *podRescuer) Run(ctx context.Context) {
	var wg sync.WaitGroup
	for id := 0; id < ps.workers; id++ {
		wg.Add(1)
		workerID := strconv.Itoa(id)
		go func() {
			defer wg.Done()
			for ps.processNextWorkItem(ctx, workerID, ps.podsToRescueQ) {
			}
		}()
	}

	log.G(ctx).Info("started pod rescuer workers")
	<-ctx.Done()
	log.G(ctx).Info("shutting down workers")
	ps.podsToRescueQ.ShutDown()
	wg.Wait()
}

func (ps *podRescuer) IsRescuing(ctx context.Context, namespace, name, uid string) bool {
	if ps.isRescueContext(ctx) {
		// rescue context is always allowed to continue
		return false
	}
	key := ps.generateKey(namespace, name, uid)
	ps.Lock()
	defer ps.Unlock()
	v, ok := ps.rescuing[key]
	if v > 0 && ps.clock.Now().Unix()-v > ps.maxRescuingTime {
		log.G(ctx).Warnf("max rescuing time exceeded for pod %s/%s/%s", namespace, name, uid)
		delete(ps.rescuing, key)
		return false
	}
	return ok
}

func (ps *podRescuer) NotifyBCICreated(ctx context.Context, namespace, name, uid string) {
	ps.removeRescuing(ps.generateKey(namespace, name, uid))
}

func (*podRescuer) isRescueContext(ctx context.Context) bool {
	if v := ctx.Value(rescueRequestKey); v != nil {
		if value, ok := v.(bool); ok {
			return value
		}
	}
	return false
}

func (*podRescuer) withRescueContext(ctx context.Context) context.Context {
	return context.WithValue(ctx, rescueRequestKey, true)
}

func (ps *podRescuer) addRescuing(key string) error {
	ps.Lock()
	defer ps.Unlock()
	if v, ok := ps.rescuing[key]; ok {
		if v < 0 || ps.clock.Now().Unix()-v <= ps.maxRescuingTime {
			return fmt.Errorf("%s is being rescued at %d", key, v)
		}
	}
	delete(ps.rescuing, key)
	ps.rescuing[key] = -1
	return nil
}

func (ps *podRescuer) updateRescuing(key string) {
	ps.Lock()
	defer ps.Unlock()
	ps.rescuing[key] = ps.clock.Now().Unix()
}

func (ps *podRescuer) removeRescuing(key string) {
	ps.Lock()
	defer ps.Unlock()
	delete(ps.rescuing, key)
}

func (ps *podRescuer) rescuePodHandler(ctx context.Context, key string) error {
	namespace, podName, uid, err := ps.splitMetaNamespaceKey(key)
	if err != nil {
		// Log the error as a warning, but do not requeue the key as it is invalid.
		log.G(ctx).WithError(err).Warnf("ignore invalid pod rescuer key: %q", key)
		return nil
	}
	ctx, span := trace.StartSpan(ctx, "bci.RescuePod")
	defer span.End()
	ctx = span.WithFields(ctx, log.Fields{
		"podUID":       uid,
		"podName":      podName,
		"podNamespace": namespace,
	})

	if err := ps.addRescuing(key); err != nil {
		log.G(ctx).WithError(err).Error("addRescuing failed")
		return err
	}
	waitForCreated := false
	defer func() {
		// if corresponding bci pod is created within this handler,
		// pod should be removed from rescuing pods set after a cooling-off period,
		// so no explicit immediate removeRescuing call is needed.
		if !waitForCreated {
			ps.removeRescuing(key)
		}
	}()

	log.G(ctx).Info("start to rescue pod")

	// find if corresponding bci pod has been created
	allBCIPods, err := ps.provider.searchBCIPodByPodName(ctx, ps.provider.getPodFullName(namespace, podName))
	if err != nil {
		return err
	}
	for _, bpod := range allBCIPods {
		if getBCILabelValue(bpod, ClusterIDLabelKey) == ps.provider.clusterID &&
			getBCILabelValue(bpod, NodeNameLabelKey) == ps.provider.nodeName &&
			getBCILabelValue(bpod, UIDLabelKey) == uid &&
			getBCILabelValue(bpod, NamespaceLabelKey) == namespace &&
			getBCILabelValue(bpod, PodNameLabelKey) == podName {
			if bpod.Status == bci.PodStatusPending {
				msg := fmt.Sprintf("corresponding bci pod %s is found with status=%s, retry later",
					bpod.PodID, bpod.Status)
				log.G(ctx).Info(msg)
				return fmt.Errorf(msg)
			}
			log.G(ctx).Infof("corresponding bci pod %s is found with status=%s, skip saving it",
				bpod.PodID, bpod.Status)
			return nil
		}
	}

	// Corresponding bci pod is not found, check the phase of corresponding k8s pod.
	k8sPod, err := ps.provider.resourceManager.GetPod(podName, namespace)
	if err != nil {
		if k8serror.IsNotFound(err) {
			log.G(ctx).WithError(err).Info("pod has been deleted, skip rescuing it")
			return nil
		}
		log.G(ctx).WithError(err).Error("fail to fetch k8s pod")
		return err
	}
	if gotUID := string(k8sPod.GetUID()); gotUID != uid {
		log.G(ctx).Warnf("pod is found with a different uid (%s (old) != %s (got)), regard it as deleted and skip rescuing it", uid, gotUID)
		return nil
	}
	if k8sPod.Status.Phase != corev1.PodPending {
		log.G(ctx).Infof("pod is found with phase=%s, skip rescuing it", k8sPod.Status.Phase)
		return nil
	}
	if k8sPod.GetDeletionTimestamp() != nil {
		log.G(ctx).Info("pod is found on terminating, skip rescuing it")
	}

	// TODO: event

	// Start to create corresponding bci pod.
	podForProvider := k8sPod.DeepCopy()
	ctx = ps.withRescueContext(ctx)
	// To ensure a different client token is used for rescue request, a pod id must be present.
	if v, ok := podForProvider.GetAnnotations()[PodIDAnnotationKey]; !ok || v == "" {
		annotations := podForProvider.GetAnnotations()
		if annotations == nil {
			annotations = make(map[string]string)
		}
		annotations[PodIDAnnotationKey] = fmt.Sprintf("p-%d", ps.clock.Now().Unix())
		podForProvider.SetAnnotations(annotations)
	}
	if err := ps.provider.CreatePod(ctx, podForProvider); err != nil {
		log.G(ctx).WithError(err).Error("fail to create pod in bci")
		return err
	}
	ps.updateRescuing(key)
	// set waitForCreated to ensure any pod created by us can be found in bci list afterwards.
	waitForCreated = true
	// append history order annotation
	newAnnotations := podForProvider.Annotations
	if newAnnotations == nil {
		newAnnotations = make(map[string]string)
	}
	podID, orderID := newAnnotations[PodIDAnnotationKey], newAnnotations[OrderIDAnnotationKey]
	if orderID != "" && podID != "" {
		log.G(ctx).Infof("pod has been rescued successfully with orderID=%s podID=%s", orderID, podID)
		if lastOrder := k8sPod.GetAnnotations()[OrderIDAnnotationKey]; lastOrder != "" {
			history := newAnnotations[HistoryOrderIDsAnnotationKey]
			if history != "" {
				lastOrder = "," + lastOrder
			}
			newAnnotations[HistoryOrderIDsAnnotationKey] = history + lastOrder
		}
		k8sPod.SetAnnotations(newAnnotations)
	}
	return nil
}

// processNextWorkItem will read a single work item off the work queue and attempt to process it,by calling the syncHandler.
func (ps *podRescuer) processNextWorkItem(ctx context.Context, workerID string, q workqueue.RateLimitingInterface) bool {

	// We create a span only after popping from the queue so that we can get an adequate picture of how long it took to process the item.
	ctx, span := trace.StartSpan(ctx, "processNextWorkItem")
	defer span.End()

	// Add the ID of the current worker as an attribute to the current span.
	ctx = span.WithField(ctx, "workerId", workerID)
	return handleQueueItem(ctx, q, ps.rescuePodHandler)
}

func handleQueueItem(ctx context.Context, q workqueue.RateLimitingInterface, handler queueHandler) bool {
	ctx, span := trace.StartSpan(ctx, "handleQueueItem")
	defer span.End()

	obj, shutdown := q.Get()
	if shutdown {
		return false
	}

	log.G(ctx).Debug("Got queue object")

	err := func(obj interface{}) error {
		defer log.G(ctx).Debug("Processed queue item")
		// We call Done here so the work queue knows we have finished rescuing this item.
		// We also must remember to call Forget if we do not want this work item being re-queued.
		// For example, we do not call Forget if a transient error occurs.
		// Instead, the item is put back on the work queue and attempted again after a back-off period.
		defer q.Done(obj)
		var key string
		var ok bool
		// We expect strings to come off the work queue.
		// These are of the form namespace/name.
		// We do this as the delayed nature of the work queue means the items in the informer cache may actually be more
		//up to date that when the item was initially put onto the workqueue.
		if key, ok = obj.(string); !ok {
			// As the item in the work queue is actually invalid, we call Forget here else we'd go into a loop of attempting to process a work item that is invalid.
			q.Forget(obj)
			log.G(ctx).Warnf("expected string in work queue item but got %#v", obj)
			return nil
		}

		// Add the current key as an attribute to the current span.
		ctx = span.WithField(ctx, "key", key)
		// Run the syncHandler, passing it the namespace/name string of the Pod resource to be synced.
		if err := handler(ctx, key); err != nil {
			if q.NumRequeues(key) < maxRetries {
				// Put the item back on the work queue to handle any transient errors.
				log.G(ctx).WithError(err).Warnf("requeuing %q due to failed sync", key)
				q.AddRateLimited(key)
				return nil
			}
			// We've exceeded the maximum retries, so we must forget the key.
			q.Forget(key)
			return fmt.Errorf("forgetting %q due to maximum retries reached: %w", key, err)
		}
		// Finally, if no error occurs we Forget this item so it does not get queued again until another change happens.
		q.Forget(obj)
		return nil
	}(obj)

	if err != nil {
		// We've actually hit an error, so we set the span's status based on the error.
		span.SetStatus(err)
		log.G(ctx).Error(err)
		return true
	}

	return true
}
