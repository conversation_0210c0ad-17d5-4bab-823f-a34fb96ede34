#!/bin/bash

set -eu

echo "execute local_build.sh ..."

MODE=${1:-NORMAL}

# 生成动态的镜像标签
IMAGE_REPOSITORY="registry.baidubce.com/cce-plugin-dev/bci-virtual-kubelet"
if [ "$MODE" == "BSC" ]; then
  IMAGE_TAG="bci-bsc-$(date +"%Y%m%d-%H%M%S")"
else
  IMAGE_TAG="bci-$(date +"%Y%m%d-%H%M%S")"
fi
#IMAGE_TAG="bci-$(date +"%Y%m%d-%H%M%S")"
GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -o ./virtual-kubelet  -ldflags '-extldflags "-static"' ./cmd/virtual-kubelet

echo "build image ${IMAGE_REPOSITORY}:${IMAGE_TAG} ..."
if [ "$MODE" == "BSC" ]; then
  # 构建BSC镜像，使用Dockerfile.bsc
  docker build -f Dockerfile.bsc -t ${IMAGE_REPOSITORY}:${IMAGE_TAG} .
else
  # 构建普通镜像
  docker build -t ${IMAGE_REPOSITORY}:${IMAGE_TAG} .
fi
echo "build image ${IMAGE_REPOSITORY}:${IMAGE_TAG} ok"

echo "push image ${IMAGE_REPOSITORY}:${IMAGE_TAG} ..."
docker push ${IMAGE_REPOSITORY}:${IMAGE_TAG}
echo "push image ${IMAGE_REPOSITORY}:${IMAGE_TAG} ok"

echo "execute local_build.sh ok"
