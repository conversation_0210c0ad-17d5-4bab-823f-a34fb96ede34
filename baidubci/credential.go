package baidubci

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/patrickmn/go-cache"
	"github.com/virtual-kubelet/virtual-kubelet/log"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/util"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/auth"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

const secretVolume = "/var/run/secrets/cce/cce-plugin-token"

type tokenSource string

const (
	tokenSourceVolume = "volume"
	tokenSourceSecret = "secret"
)

var (
	// STSTokenCache is used to cache STS tokens for different users
	// the cache has an expiration time of 5 minutes and purges expired items every 10 minutes
	STSTokenCache = cache.New(5*time.Minute, 10*time.Minute)

	cceAuthClient = func() auth.Interface {
		endpoint := "127.0.0.1:8400"
		if v := os.Getenv("CCE_AUTH_ENDPOINT"); v != "" {
			endpoint = v
		}
		c := auth.NewClient(&bce.Config{
			// Actually no auth is required for auth service, but if both credential and signOption
			// are nil, the request will be rejected by the SendRequest method and nerver be sent.
			// So a fake credential with empty ak/sk is used here.
			Credentials: bce.NewCredentials("", ""),
			Endpoint:    endpoint,
			Checksum:    true,
			Timeout:     30 * time.Second,
		})
		c.SetDebug(true)
		return c
	}()
)

func (p *BCIProvider) getSignOption(ctx context.Context) *bce.SignOption {
	switch p.multiTenantMode {
	case util.MultiTenantModeBSC:
		return p.getSignOptionBSC(ctx)
	case util.MultiTenantModeCCE:
		return p.getSignOptionCCEMeta(ctx)
	}
	return p.getSignOptionDefault(ctx)
}

func (p *BCIProvider) getSignOptionDefault(ctx context.Context) *bce.SignOption {
	if p.cceGatewayHelper == nil {
		return nil
	}
	var opt *bce.SignOption
	var err error

	switch p.cceGatewayTokenSrc {
	case tokenSourceVolume:
		opt, err = p.cceGatewayHelper.NewSignOptionFromVolume(ctx, secretVolume)
	case tokenSourceSecret:
		opt, err = p.cceGatewayHelper.NewSignOptionFromSecret(ctx,
			func(ns, name string) (*corev1.Secret, error) {
				return p.resourceManager.GetSecret(name, ns)
			})

	default:
		// Determin p.cceGatewayTokenSrc at first time.
		// Try generating token from volume once, or fallback to secret mode.
		if opt, err = p.cceGatewayHelper.NewSignOptionFromVolume(ctx, secretVolume); err == nil {
			p.cceGatewayTokenSrc = tokenSourceVolume
		} else {
			log.G(ctx).WithError(err).Infof("cannot generate sign option from volume, fetch secret directly")
			opt, err = p.cceGatewayHelper.NewSignOptionFromSecret(ctx,
				func(ns, name string) (*corev1.Secret, error) {
					secret, err := p.resourceManager.GetSecret(name, ns)
					if err != nil || secret == nil {
						client := p.resourceManager.GetRawClient()
						secret, err = client.CoreV1().Secrets(ns).Get(ctx, name, metav1.GetOptions{})
						if err != nil || secret == nil {
							log.G(ctx).Errorf("fail to GetSecret namespace %s secret %s with err=%v", ns, name, err)
							err = fmt.Errorf("fail to GetSecret namespace %s secret %s with err=%w", ns, name, err)
							return secret, err
						}
					}
					return secret, nil
				})
			if err == nil {
				p.cceGatewayTokenSrc = tokenSourceSecret
			}
		}
	}
	if err != nil {
		log.G(ctx).WithError(err).Error("fail to get sign option")
	}
	return opt
}

func (p *BCIProvider) getSignOptionBSC(ctx context.Context) *bce.SignOption {
	userUUID := util.GetUserUUID(ctx)
	if userUUID == "" {
		log.G(ctx).Error("userUUID not found in context for BSC mode")
		return nil
	}
	var token *bce.SessionTokenResponse

	// try getting token from cache first
	if v, found := STSTokenCache.Get(userUUID); found {
		if cachedToken, ok := v.(*bce.SessionTokenResponse); ok {
			token = cachedToken
		}
	}

	if token == nil {
		// get sts credential
		if p.stsClient == nil {
			log.G(ctx).Error("stsClient in provider is nil for BSC mode")
			return nil
		}
		sessionTokenResponse, err := p.stsClient.GetSessionToken(ctx, userUUID)
		if err != nil {
			log.G(ctx).WithField("userUUID", userUUID).WithError(err).Error("fail to get sts credential for BSC mode")
			return nil
		}

		STSTokenCache.SetDefault(userUUID, sessionTokenResponse)
		token = sessionTokenResponse
	}

	return p.newSTSSignOption(ctx, token)
}

func (p *BCIProvider) newSTSSignOption(ctx context.Context, token *bce.SessionTokenResponse) *bce.SignOption {
	if token == nil {
		log.G(ctx).Error("sts token is nil")
		return nil
	}

	// no charge application
	if p.chargeApplication == "" || p.chargeAccessKey == "" || p.chargeSecretKey == "" {
		return &bce.SignOption{
			Credentials: bce.NewCredentials(token.AccessKeyID, token.SecretAccessKey),
			Headers:     map[string]string{"x-bce-security-token": token.SessionToken},
		}
	}

	return &bce.SignOption{
		CustomSignFunc: func(ctx context.Context, req *bce.Request) {
			if req.Header.Get("content-type") == "" {
				req.Header.Set("content-type", "application/json")
			}
			// req may have contained auth headers on bce retry, which should not be in headers to sign
			req.Header.Del("authorization")
			req.Header.Del("x-bce-security-token")
			req.Header.Del("charge-authorization")
			req.Header.Del("charge-application")

			// generate charge auth header
			var chargeAuthHeader string
			// clone request to reserve original request
			clonedReq := (*bce.Request)(req.Raw().Clone(ctx))
			cred := bce.NewCredentials(p.chargeAccessKey, p.chargeSecretKey)
			opt := &bce.SignOption{}
			opt.AddHeader("content-type", "application/json")
			bce.GenerateAuthorization(*cred, *clonedReq, opt)
			chargeAuthHeader = clonedReq.Header.Get("authorization")

			// set resource auth header
			cred = bce.NewCredentials(token.AccessKeyID, token.SecretAccessKey)
			opt.AddHeader("x-bce-security-token", token.SessionToken)
			bce.GenerateAuthorization(*cred, *req, opt)

			if p.chargeApplication != "" && chargeAuthHeader != "" {
				req.Header.Set("charge-authorization", chargeAuthHeader)
				req.Header.Set("charge-application", p.chargeApplication)
			}
		},
	}

}

func (p *BCIProvider) getSignOptionCCEMeta(ctx context.Context) *bce.SignOption {
	userUUID := util.GetUserUUID(ctx)
	if userUUID == "" {
		log.G(ctx).Error("userUUID not found in context")
		return nil
	}
	return &bce.SignOption{
		CustomSignFunc: p.cceAuthFunc,
	}
}

// cceAuthFunc uses bce-api-auth to sign a request.
func (p *BCIProvider) cceAuthFunc(ctx context.Context, req *bce.Request) {
	userUUID := util.GetUserUUID(ctx)
	if userUUID == "" {
		log.G(ctx).Error("userUUID not found in context")
		return
	}

	if req.Header.Get("content-type") == "" {
		req.Header.Set("content-type", "application/json")
	}

	// req may have contained auth headers on bce retry, which should not be in headers to sign
	req.Header.Del("authorization")
	req.Header.Del("x-bce-security-token")
	req.Header.Del("charge-authorization")
	req.Header.Del("charge-application")

	var chargeAuthHeader string
	if p.chargeApplication != "" && p.chargeAccessKey != "" && p.chargeSecretKey != "" {
		// generate charge auth header
		cred := bce.NewCredentials(p.chargeAccessKey, p.chargeSecretKey)
		opt := &bce.SignOption{}
		opt.AddHeader("content-type", "application/json")
		bce.GenerateAuthorization(*cred, *req, opt)
		chargeAuthHeader = req.Header.Get("authorization")
		req.Header.Del("authorization")
	}

	args, err := auth.NewGenAuthorizationArgs(ctx, req.Raw(), userUUID)
	if err != nil {
		log.G(ctx).WithError(err).Error("fail to NewGenAuthorizationArgs")
		return
	}
	signature, err := cceAuthClient.GenAuthorization(ctx, args, nil)
	if err != nil {
		log.G(ctx).WithError(err).Error("fail to GenAuthorization")
		return
	}

	req.Header.Set("authorization", signature.Result.Authorization)
	req.Header.Set("x-bce-security-token", signature.Result.SessionToken)
	if p.chargeApplication != "" && chargeAuthHeader != "" {
		req.Header.Set("charge-authorization", chargeAuthHeader)
		req.Header.Set("charge-application", p.chargeApplication)
	}
}
