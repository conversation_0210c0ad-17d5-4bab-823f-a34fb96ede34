package baidubci

import (
	"testing"

	"github.com/google/go-cmp/cmp"
)

func Test_wsEncode(t *testing.T) {
	raw := []byte{}
	encoded := wsEncode(raw, stdinMsgType)
	if len(encoded) != 1 {
		t.<PERSON><PERSON><PERSON>("expected length: %d, actual: %d", 1, len(encoded))
	} else if encoded[0] != byte(stdinMsgType) {
		t.<PERSON>("expected value: %x, acutal: %x", byte(stdinMsgType), encoded[0])
	}
}

func Test_wsDecode(t *testing.T) {
	cases := []struct {
		name            string
		input           []byte
		expectedOutput1 []byte
		expectedOutput2 wsMsgType
		expectedOutput3 error
	}{
		{
			name:            "test decode stdin msg",
			input:           []byte{byte(stdinMsgType)},
			expectedOutput1: []byte{},
			expectedOutput2: stdinMsgType,
			expectedOutput3: nil,
		},
		{
			name:            "test decode stdout msg",
			input:           []byte{byte(stdoutMsgType)},
			expectedOutput1: []byte{},
			expectedOutput2: stdoutMsgType,
			expectedOutput3: nil,
		},
		{
			name:            "test decode stderr msg",
			input:           []byte{byte(stderrMsgType)},
			expectedOutput1: []byte{},
			expectedOutput2: stderrMsgType,
			expectedOutput3: nil,
		},
		{
			name:            "test decode out of band msg",
			input:           []byte{byte(outOfBandMsgType)},
			expectedOutput1: []byte{},
			expectedOutput2: outOfBandMsgType,
			expectedOutput3: nil,
		},
		{
			name:            "test decode resize msg",
			input:           []byte{byte(resizeMsgType)},
			expectedOutput1: []byte{},
			expectedOutput2: resizeMsgType,
			expectedOutput3: nil,
		},
		{
			name:            "test invalid msg length",
			input:           []byte{},
			expectedOutput3: errInvalidMsgLength,
		},
		{
			name:            "test invalid msg type",
			input:           []byte{byte(invalidMsgType)},
			expectedOutput3: errInvalidMsgType,
		},
	}

	for _, c := range cases {
		decoded, msgType, err := wsDecode(c.input)
		if c.expectedOutput3 != nil {
			if c.expectedOutput3 != err {
				t.Errorf("name: %s, expected err: %v, actual: %v", c.name, c.expectedOutput3, err)
			}
		} else {
			if c.expectedOutput3 != err {
				t.Errorf("name: %s, expected err: %v, actual: %v", c.name, c.expectedOutput3, err)
			}
			if c.expectedOutput2 != msgType {
				t.Errorf("name: %s, expected msg type: %v, actual: %v", c.name, c.expectedOutput2, msgType)
			}
			if !cmp.Equal(c.expectedOutput1, decoded) {
				t.Errorf("name: %s, expected decoded msg: %v, actual: %v", c.name, c.expectedOutput1, decoded)
			}
		}
	}
}

func Test_getTokenFromWSSURL(t *testing.T) {
	cases := []struct {
		name           string
		input          string
		expectedOutput string
		expectedError  bool
	}{
		{
			name:           "valid wss url with token",
			input:          "wss://xxx.com?token=test",
			expectedOutput: "test",
			expectedError:  false,
		},
		{
			name:          "invalid wss url",
			input:         "x://x:xxxxxx",
			expectedError: true,
		},
		{
			name:          "valid wss url with no token",
			input:         "wss://xxx.com",
			expectedError: false,
		},
	}

	for _, c := range cases {
		token, err := getTokenFromWSSURL(c.input)
		if c.expectedError {
			if err == nil {
				t.Errorf("name: %s, expected err, actual: %v", c.name, err)
			}
		} else {
			if err != nil {
				t.Errorf("name: %s, expected no err, actual: %v", c.name, err)
			}
			if c.expectedOutput != token {
				t.Errorf("name: %s, expected token: %s, actual: %s", c.name, c.expectedOutput, token)
			}
		}
	}
}
