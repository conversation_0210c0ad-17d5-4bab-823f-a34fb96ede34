package types

import v1 "k8s.io/api/core/v1"

// bci-profile configmap in kube-system namespace, and name is bci-profile
// one example:
/*
apiVersion: v1
data:
  enableReserveFailedPod: "true"
kind: ConfigMap
metadata:
  name: bci-profile
  namespace: kube-system
*/
const (
	BciProfileConfigMapName           = "bci-profile"
	SystemNamespace                   = "kube-system"
	DefaultEnableReserveFailedPod     = false
	DefaultDisableAutoMatchImageCache = false // 默认禁用自动匹配镜像缓存
	DefaultEnableForceInjectDNSConfig = false // 默认不强制注入自定义DNS配置
	DefaultEnableAutoInjectKubeProxy  = false // 默认不自动注入kube-proxyd到pod中
)

type BCIProfileConfig struct {
	Region                     string           `json:"region"`                     // 地域，用于区分不同地域的pod
	ClusterID                  string           `json:"clusterID"`                  // 集群id
	Subnets                    []SubnetInfo     `json:"subnets"`                    // 子网信息
	SecurityGroupID            string           `json:"securityGroupID"`            // 安全组id，用于区分不同安全组的pod
	EnableReserveFailedPod     bool             `json:"enableReserveFailedPod"`     // 是否开启保留已失败的pod
	EnableAutoInjectKubeProxy  bool             `json:"enableAutoInjectKubeProxy"`  // 是否自动注入kube-proxyd到pod中
	EnableForceInjectDNSConfig bool             `json:"enableForceInjectDNSConfig"` // 是否强制注入自定义DNS配置，如果为true，则强制注入自定义DNS配置
	DisableAutoMatchImageCache bool             `json:"disableAutoMatchImageCache"` // 是否禁用自动匹配镜像缓存，如果为true，则会创建并且使用自动匹配镜像缓存
	DNSConfig                  *v1.PodDNSConfig `json:"dnsConfig"`                  // 自定义DNS配置
}

type SubnetInfo struct {
	SubnetID    string `json:"subnetID"`
	LogicalZone string `json:"logicalZone"`
}
