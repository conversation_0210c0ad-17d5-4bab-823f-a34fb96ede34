package baidubci

import (
	"context"
	"net/http"
	"os"

	"github.com/virtual-kubelet/virtual-kubelet/log"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/webhooks"
	admissionv1 "k8s.io/api/admissionregistration/v1"
	corev1 "k8s.io/api/core/v1"
	k8serror "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtimev1 "k8s.io/apimachinery/pkg/runtime"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/healthz"
	sigsLog "sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	k8swebhook "sigs.k8s.io/controller-runtime/pkg/webhook"
)

const (
	MutatingWebhookConfiguration           = "virtual-kubelet-webhook"
	BCIProfileVadatingWebhookConfiguration = "bci-profile-validator"
	WebhookName                            = "bci-virtual-kubelet-webhook.kube-system.svc"
	WebhookServiceName                     = "bci-virtual-kubelet-webhook"
	VKWebhookPath                          = "/vk-mutate-pod"
	BCIProfileVadatingWebhookPath          = "/bci-profile-validator"
	WebhookCACertPath                      = "/etc/webhook-certs/ca.crt"
	WebhookCertName                        = "tls.crt"
	WebhookKeyName                         = "tls.key"
	MutatingWebhookURL                     = "https://bci-virtual-kubelet-webhook:443/vk-mutate-pod"
	BCIProfileValidatingWebhookURL         = "https://bci-virtual-kubelet-webhook:443/bci-profile-validator"
	VirtualKubeletAppLabel                 = "bci-virtual-kubelet"
)

var (
	scheme = runtimev1.NewScheme()
)

type WebhookController struct {
	provider *BCIProvider
}

func init() {
	corev1.AddToScheme(scheme) //nolint:errcheck
}

func NewWebhookController(provider *BCIProvider) *WebhookController {
	return &WebhookController{
		provider: provider,
	}
}

func (wc *WebhookController) Run(ctx context.Context) {
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	sigsLog.SetLogger(zap.New(zap.UseDevMode(true)))

	logger := sigsLog.Log.WithName("webhook-controller")
	logger.Info("Starting webhook controller")

	webhookOptions := k8swebhook.Options{}
	webhookOptions.WebhookMux = http.NewServeMux()
	webhookOptions.CertName = WebhookCertName
	webhookOptions.KeyName = WebhookKeyName
	webhookOptions.CertDir = wc.provider.webhookServerRunOptions.CertDir
	webhookOptions.Port = wc.provider.webhookServerRunOptions.Port
	mgr, err := ctrl.NewManager(ctrl.GetConfigOrDie(), manager.Options{
		Scheme:                 scheme,
		HealthProbeBindAddress: wc.provider.webhookServerRunOptions.ProbeAddr,
		WebhookServer:          k8swebhook.NewServer(webhookOptions),
	})
	if err != nil {
		log.G(ctx).Errorf("failed to start manager: %v", err)
		return
	}
	wc.setWebhookController(ctx)
	if wc.provider.webhookServerRunOptions.WebhookServer {
		log.G(ctx).Info("Setup webhook server")
		if err = webhooks.SetupWithManager(wc.provider.webhookServerRunOptions, mgr); err != nil {
			log.G(ctx).Errorf("failed to setup webhook: %v", err)
			return
		}
	}
	if err := mgr.AddHealthzCheck("healthz", healthz.Ping); err != nil {
		log.G(ctx).Errorf("failed to add healthz check: %v", err)
		return
	}
	if err := mgr.AddReadyzCheck("readyz", healthz.Ping); err != nil {
		log.G(ctx).Errorf("unable to set up ready check: %v", err)
		return
	}
	if err = mgr.Start(ctrl.SetupSignalHandler()); err != nil {
		log.G(ctx).Errorf("failed to start manager: %v", err)
		return
	}

	<-ctx.Done()
	return
}

func (wc *WebhookController) setWebhookController(ctx context.Context) {
	wc.setWebhookConfig(ctx)
	log.G(ctx).Infof("created mutating webhook configuration: %s success", MutatingWebhookConfiguration)
	wc.setBCIProfileValidateWebhookConfig(ctx)
	log.G(ctx).Infof("created validating webhook configuration: %s success", BCIProfileVadatingWebhookConfiguration)
}

func (wc *WebhookController) setWebhookConfig(ctx context.Context) {
	// first delete mutating webhook configuration
	wc.deleteMutatingWebhookConfig(ctx, MutatingWebhookConfiguration)
	// check if mutating webhook configuration exists
	_, err := wc.provider.resourceManager.GetRawClient().AdmissionregistrationV1().MutatingWebhookConfigurations().Get(ctx,
		MutatingWebhookConfiguration, metav1.GetOptions{})
	if err != nil {
		if k8serror.IsNotFound(err) {
			// create mutating webhook configuration
			// 1. read CA certificate file
			caCertPath := WebhookCACertPath
			caCert, err := os.ReadFile(caCertPath)
			if err != nil {
				log.G(ctx).Errorf("failed to read CA certificate file: %v", err)
				return
			}
			// 2. init mutating webhook configuration
			clientConfig := admissionv1.WebhookClientConfig{
				CABundle: caCert,
			}
			if wc.provider.webhookServerRunOptions.IsVKManaged {
				clientConfig.URL = strPtr(MutatingWebhookURL)
			} else {
				service := &admissionv1.ServiceReference{
					Namespace: metav1.NamespaceSystem,
					Name:      WebhookServiceName,
					Path:      strPtr(VKWebhookPath),
				}
				clientConfig.Service = service
			}
			mutatingWebhookConfig := &admissionv1.MutatingWebhookConfiguration{
				TypeMeta: metav1.TypeMeta{
					Kind:       "MutatingWebhookConfiguration",
					APIVersion: admissionv1.SchemeGroupVersion.String(),
				},
				ObjectMeta: metav1.ObjectMeta{
					Name:      MutatingWebhookConfiguration,
					Namespace: metav1.NamespaceSystem,
				},
				Webhooks: []admissionv1.MutatingWebhook{
					{
						Name:         WebhookName,
						ClientConfig: clientConfig,
						Rules: []admissionv1.RuleWithOperations{{
							Operations: []admissionv1.OperationType{admissionv1.Create, admissionv1.Update},
							Rule: admissionv1.Rule{
								APIGroups:   []string{"*"},
								APIVersions: []string{"v1"},
								Resources:   []string{"pods"},
							},
						},
						},
						AdmissionReviewVersions: []string{"v1", "v1beta1"},
						FailurePolicy:           failurePolicyPtr(admissionv1.Ignore),
						SideEffects:             sideEffectsPtr(admissionv1.SideEffectClassNone),
						ObjectSelector: &metav1.LabelSelector{
							MatchExpressions: []metav1.LabelSelectorRequirement{
								{
									Key:      "app",
									Operator: metav1.LabelSelectorOpNotIn,
									Values:   []string{VirtualKubeletAppLabel},
								},
							},
						},
					},
				},
			}
			// 3. create mutating webhook configuration
			_, err = wc.provider.resourceManager.GetRawClient().AdmissionregistrationV1().MutatingWebhookConfigurations().Create(ctx,
				mutatingWebhookConfig, metav1.CreateOptions{})
			if err != nil {
				log.G(ctx).Errorf("failed to create mutating webhook configuration %s, err %v", MutatingWebhookConfiguration, err)
				return
			}
			log.G(ctx).Infof("created mutating webhook configuration: %s", MutatingWebhookConfiguration)
		} else {
			log.G(ctx).Errorf("failed to get mutating webhook configuration %s err %v", MutatingWebhookConfiguration, err)
		}
	}
}

// add bci profile validating webhook configuration
func (wc *WebhookController) setBCIProfileValidateWebhookConfig(ctx context.Context) {
	// first delete validating webhook configuration
	wc.deleteValidatingWebhookConfig(ctx, BCIProfileVadatingWebhookConfiguration)
	// check if validating webhook configuration exists
	_, err := wc.provider.resourceManager.GetRawClient().AdmissionregistrationV1().ValidatingWebhookConfigurations().Get(ctx,
		BCIProfileVadatingWebhookConfiguration, metav1.GetOptions{})
	if err != nil {
		if k8serror.IsNotFound(err) {
			// create validating webhook configuration
			// 1. read CA certificate file
			caCertPath := WebhookCACertPath
			caCert, err := os.ReadFile(caCertPath)
			if err != nil {
				log.G(ctx).Errorf("failed to read CA certificate file: %v", err)
				return
			}
			// 2. init validating webhook configuration
			clientConfig := admissionv1.WebhookClientConfig{
				CABundle: caCert,
			}
			if wc.provider.webhookServerRunOptions.IsVKManaged {
				clientConfig.URL = strPtr(BCIProfileValidatingWebhookURL)
			} else {
				service := &admissionv1.ServiceReference{
					Namespace: metav1.NamespaceSystem,
					Name:      WebhookServiceName,
					Path:      strPtr(BCIProfileVadatingWebhookPath),
				}
				clientConfig.Service = service
			}

			validatingWebhookConfig := &admissionv1.ValidatingWebhookConfiguration{
				TypeMeta: metav1.TypeMeta{
					Kind:       "ValidatingWebhookConfiguration",
					APIVersion: admissionv1.SchemeGroupVersion.String(),
				},
				ObjectMeta: metav1.ObjectMeta{
					Name:      BCIProfileVadatingWebhookConfiguration,
					Namespace: metav1.NamespaceSystem,
				},
				Webhooks: []admissionv1.ValidatingWebhook{
					{
						Name:         WebhookName,
						ClientConfig: clientConfig,
						Rules: []admissionv1.RuleWithOperations{{
							Operations: []admissionv1.OperationType{admissionv1.Create, admissionv1.Update},
							Rule: admissionv1.Rule{
								APIGroups:   []string{"*"},
								APIVersions: []string{"v1"},
								Resources:   []string{"configmaps"},
							},
						},
						},
						AdmissionReviewVersions: []string{"v1", "v1beta1"},
						FailurePolicy:           failurePolicyPtr(admissionv1.Ignore),
						SideEffects:             sideEffectsPtr(admissionv1.SideEffectClassNone),
						ObjectSelector: &metav1.LabelSelector{
							MatchExpressions: []metav1.LabelSelectorRequirement{
								{
									Key:      "app",
									Operator: metav1.LabelSelectorOpNotIn,
									Values:   []string{VirtualKubeletAppLabel},
								},
							},
						},
					},
				},
			}
			// 3. create validating webhook configuration
			_, err = wc.provider.resourceManager.GetRawClient().AdmissionregistrationV1().ValidatingWebhookConfigurations().Create(ctx,
				validatingWebhookConfig, metav1.CreateOptions{})
			if err != nil {
				log.G(ctx).Errorf("failed to create mutating webhook configuration %s, err %v", BCIProfileVadatingWebhookConfiguration, err)
				return
			}
			log.G(ctx).Infof("created validating webhook configuration: %s", BCIProfileVadatingWebhookConfiguration)
		} else {
			log.G(ctx).Errorf("failed to get validating webhook %s configuration: %v", BCIProfileVadatingWebhookConfiguration, err)
		}
	}
}

func (wc *WebhookController) deleteMutatingWebhookConfig(ctx context.Context, name string) {
	err := wc.provider.resourceManager.GetRawClient().AdmissionregistrationV1().MutatingWebhookConfigurations().Delete(ctx,
		name, metav1.DeleteOptions{})
	if err != nil {
		log.G(ctx).Errorf("failed to delete mutating webhook configuration %s, err %v", name, err)
	}
}

func (wc *WebhookController) deleteValidatingWebhookConfig(ctx context.Context, name string) {
	err := wc.provider.resourceManager.GetRawClient().AdmissionregistrationV1().ValidatingWebhookConfigurations().Delete(ctx,
		name, metav1.DeleteOptions{})
	if err != nil {
		log.G(ctx).Errorf("failed to delete validating webhook configuration %s, err %v", name, err)
	}
}

func strPtr(s string) *string {
	return &s
}
func failurePolicyPtr(policy admissionv1.FailurePolicyType) *admissionv1.FailurePolicyType {
	return &policy
}

func sideEffectsPtr(sideEffects admissionv1.SideEffectClass) *admissionv1.SideEffectClass {
	return &sideEffects
}
