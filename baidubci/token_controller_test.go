package baidubci

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/util/workqueue"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/manager"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/token"
	bciv1 "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
	bciv2 "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci/v2"
)

func TestTransferToken(t *testing.T) {
	tc := &tokenController{
		tokenPriorityQueueSet: token.NewPriorityQueueSet(),
		tokenQueue:            workqueue.NewNamedRateLimitingQueue(workqueue.DefaultControllerRateLimiter(), "serviceaccounttoken"),
	}
	tc.tokenPriorityQueueSet.Push("podNs/podName/volumeName/tokenName/10/podUid", 1)

	err := tc.transferToken(context.Background())
	if err != nil {
		t.Fatal(err)
	}
}

func TestSyncToken(t *testing.T) {
	tc := &tokenController{
		tokenPriorityQueueSet: token.NewPriorityQueueSet(),
		tokenQueue:            workqueue.NewNamedRateLimitingQueue(workqueue.DefaultControllerRateLimiter(), "serviceaccounttoken"),
	}
	ctx, cancel := context.WithCancel(context.TODO())
	defer cancel()
	type fields struct {
		ctrl            *gomock.Controller
		tokenController *tokenController
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "start sync pod tokens",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				rm := manager.NewMockResourceManager(ctrl)
				expSecond := int64(600)
				rm.EXPECT().GetPods().Return([]*v1.Pod{
					{
						ObjectMeta: metav1.ObjectMeta{
							Name:      "pod-0",
							Namespace: "default",
							UID:       types.UID("test-pod-uid"),
							Annotations: map[string]string{
								PodIDAnnotationKey: "p-as7xdei8",
							},
							//ClusterName: "test",
						},
						Spec: v1.PodSpec{
							ServiceAccountName: "default",
							Volumes: []v1.Volume{
								{
									Name: "token-vol",
									VolumeSource: v1.VolumeSource{
										Projected: &v1.ProjectedVolumeSource{
											Sources: []v1.VolumeProjection{
												{
													ServiceAccountToken: &v1.ServiceAccountTokenProjection{
														ExpirationSeconds: &expSecond,
													},
												},
											},
										},
									},
								},
							},
						},
						Status: v1.PodStatus{
							Phase: v1.PodRunning,
						},
					},
				}).AnyTimes()
				rm.EXPECT().GetRawClient().Return(nil)
				p := &BCIProvider{
					resourceManager: rm,
				}
				tc.provider = p
				tc.tokenManager = token.NewManager(p.resourceManager.GetRawClient())
				return fields{
					ctrl:            ctrl,
					tokenController: tc,
				}
			}(),
		},
	}
	for _, tt := range tests {
		if tt.fields.ctrl != nil {
			defer tt.fields.ctrl.Finish()
		}
		t.Run(tt.name, func(t *testing.T) {
			tt.fields.tokenController.syncToken(ctx)
		})
	}

}

func TestGetAllServiceAccountToken(t *testing.T) {
	tc := &tokenController{
		tokenPriorityQueueSet: token.NewPriorityQueueSet(),
		tokenQueue:            workqueue.NewNamedRateLimitingQueue(workqueue.DefaultControllerRateLimiter(), "serviceaccounttoken"),
		reqTokens:             make(chan struct{}, 10),
	}
	ctx, cancel := context.WithCancel(context.TODO())
	defer cancel()
	type fields struct {
		ctrl            *gomock.Controller
		tokenController *tokenController
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "start sync pod tokens",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bciV2Client := bciv2.NewMockClient(ctrl)
				pod := &bciv1.Pod{
					Extras: map[string]string{"default/pod-0/token-vol/default/600/test-pod-uid": "1"},
				}
				bciPod := &bciv1.DescribePodResponse{
					Pod: pod,
				}
				bciV2Client.EXPECT().DescribePod(gomock.Any(), gomock.Any(), gomock.Any()).Return(bciPod, nil).AnyTimes()
				rm := manager.NewMockResourceManager(ctrl)
				expSecond := int64(600)
				rm.EXPECT().GetPods().Return([]*v1.Pod{
					{
						ObjectMeta: metav1.ObjectMeta{
							Name:      "pod-0",
							Namespace: "default",
							UID:       types.UID("test-pod-uid"),
							Annotations: map[string]string{
								PodIDAnnotationKey: "p-as7xdei8",
							},
							//ClusterName: "test",
						},
						Spec: v1.PodSpec{
							ServiceAccountName: "default",
							Volumes: []v1.Volume{
								{
									Name: "token-vol",
									VolumeSource: v1.VolumeSource{
										Projected: &v1.ProjectedVolumeSource{
											Sources: []v1.VolumeProjection{
												{
													ServiceAccountToken: &v1.ServiceAccountTokenProjection{
														ExpirationSeconds: &expSecond,
													},
												},
											},
										},
									},
								},
							},
						},
						Status: v1.PodStatus{
							Phase: v1.PodRunning,
						},
					},
				}).AnyTimes()
				rm.EXPECT().GetRawClient().Return(nil)
				p := &BCIProvider{
					resourceManager: rm,
					createV2:        true,
					bciV2Client:     bciV2Client,
				}
				tc.provider = p
				tc.tokenManager = token.NewManager(p.resourceManager.GetRawClient())
				return fields{
					ctrl:            ctrl,
					tokenController: tc,
				}
			}(),
		},
	}
	for _, tt := range tests {
		if tt.fields.ctrl != nil {
			defer tt.fields.ctrl.Finish()
		}
		t.Run(tt.name, func(t *testing.T) {
			tt.fields.tokenController.getAllServiceAccountToken(ctx)
		})
	}
}
