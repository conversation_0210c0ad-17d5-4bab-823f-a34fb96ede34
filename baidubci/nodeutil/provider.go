package nodeutil

import (
	"context"
	"io"

	dto "github.com/prometheus/client_model/go"
	"github.com/virtual-kubelet/virtual-kubelet/node"
	"github.com/virtual-kubelet/virtual-kubelet/node/api"
	"github.com/virtual-kubelet/virtual-kubelet/node/api/statsv1alpha1"
	appsv1 "k8s.io/api/apps/v1"
	v1 "k8s.io/api/core/v1"
	corev1listers "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/tools/record"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
)

// Provider contains the methods required to implement a virtual-kubelet provider.
//
// Errors produced by these methods should implement an interface from
// github.com/virtual-kubelet/virtual-kubelet/errdefs package in order for the
// core logic to be able to understand the type of failure
type Provider interface {
	node.PodLifecycleHandler
	// GetContainerLogs retrieves the logs of a container by name from the provider.
	GetContainerLogs(ctx context.Context, namespace, podName, containerName string, opts api.ContainerLogOpts) (io.ReadCloser, error)

	// RunInContainer executes a command in a container in the pod, copying data
	// between in/out/err and the container's stdin/stdout/stderr.
	RunInContainer(ctx context.Context, namespace, podName, containerName string, cmd []string, attach api.AttachIO) error

	// AttachToContainer attaches to the executing process of a container in the pod, copying data
	// between in/out/err and the container's stdin/stdout/stderr.
	AttachToContainer(ctx context.Context, namespace, podName, containerName string, attach api.AttachIO) error

	// GetStatsSummary gets the stats for the node, including running pods
	GetStatsSummary(context.Context) (*statsv1alpha1.Summary, error)

	// GetMetricsResource gets the metrics for the node, including running pods
	GetMetricsResource(context.Context) ([]*dto.MetricFamily, error)

	// PortForward forwards a local port to a port on the pod
	PortForward(ctx context.Context, namespace, pod string, port int32, stream io.ReadWriteCloser) error

	UpdateConfigMap(ctx context.Context, pod *v1.Pod, configMap *v1.ConfigMap) error

	UpdateDsContainers(ctx context.Context, injectContainers *bci.InjectDsContainersRequest, pod *v1.Pod) error
	GetPodDetail(ctx context.Context, namespace, name string) (*bci.DescribePodResponse, error)
	GetDsVolumes(ctx context.Context, ds *appsv1.DaemonSet) (*bci.Volumes, error)
}

type ProviderConfig struct {
	Pods          corev1listers.PodLister
	ConfigMaps    corev1listers.ConfigMapLister
	Secrets       corev1listers.SecretLister
	Services      corev1listers.ServiceLister
	PV            corev1listers.PersistentVolumeLister
	PVC           corev1listers.PersistentVolumeClaimLister
	EventRecorder record.EventRecorder
	SystemPods    corev1listers.PodLister

	Node *v1.Node
}

// NewProviderFunc is used from NewNodeFromClient to bootstrap a provider using the client/listers/etc created there.
// If a nil node provider is returned a default one will be used.
type NewProviderFunc func(ProviderConfig) (Provider, node.NodeProvider, error)

type PodCAdvisorMetricsProvider interface {
	GetCAdvisorMetrics(context.Context) (io.ReadCloser, error)
}
