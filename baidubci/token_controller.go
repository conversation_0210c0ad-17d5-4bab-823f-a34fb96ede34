package baidubci

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"code.cloudfoundry.org/clock"
	"github.com/virtual-kubelet/virtual-kubelet/log"
	authenticationv1 "k8s.io/api/authentication/v1"
	v1 "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/util/workqueue"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/token"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/util"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
)

const (
	DefaultSyncTokenInterval     = 5 * time.Second
	DefaultRefreshTokenInterval  = 1 * time.Second
	DefaultTransferTokenInterval = 5 * time.Second
	DefaultRefreshTokenWorkers   = 25

	AddTokenType    = "add"
	DeleteTokenType = "delete"
)

type TokenController interface {
	Start(ctx context.Context) error
	GetServiceAccountToken(ctx context.Context, pod *v1.Pod, serviceAccountToken *v1.ServiceAccountTokenProjection,
		tokenKey string) (*authenticationv1.TokenRequest, error)
	NotifyToken(ctx context.Context, tokenKey string, podUID types.UID, priority int64, op string)
}

type tokenController struct {
	syncTokenInterval     time.Duration // 遍历集群pod，同步serviceaccounttoken时间间隔
	refreshTokenInterval  time.Duration // 刷新token时间间隔
	transferTokenInterval time.Duration // 将PriorityQueue中即将要过期的token转移到tokenQueue的时间间隔
	tokenPriorityQueueSet *token.PriorityQueueSet
	tokenQueue            workqueue.RateLimitingInterface
	provider              *BCIProvider
	tokenManager          *token.Manager
	clock                 clock.Clock
	refreshTokenWorkers   int // 更新tokenQueue中token worker数
	reqTokens             chan struct{}
	refreshTokenMap       sync.Map
}

func NewTokenController(p *BCIProvider, maxReqInFlight int) TokenController {
	client := p.resourceManager.GetRawClient()
	return &tokenController{
		syncTokenInterval:     DefaultSyncTokenInterval,
		refreshTokenInterval:  DefaultRefreshTokenInterval,
		transferTokenInterval: DefaultTransferTokenInterval,
		tokenPriorityQueueSet: token.NewPriorityQueueSet(),
		tokenQueue:            workqueue.NewNamedRateLimitingQueue(workqueue.DefaultControllerRateLimiter(), "serviceaccounttoken"),
		provider:              p,
		tokenManager:          token.NewManager(client),
		clock:                 clock.NewClock(),
		refreshTokenWorkers:   DefaultRefreshTokenWorkers,
		reqTokens:             make(chan struct{}, maxReqInFlight),
		refreshTokenMap:       sync.Map{},
	}
}

func (tc *tokenController) Start(ctx context.Context) error {
	// 初始化所有pod serviceAccountToke到tokenPriorityQueueSet
	tc.getAllServiceAccountToken(ctx)

	// 定期同步pod serviceAccountToken到tokenPriorityQueueSet
	go tc.startSyncToken(ctx)

	// 定期将tokenPriorityQueueSet中即将过期的token写入tokenQueue
	go tc.transferTokenHandler(ctx)

	// 定期刷新token
	tc.startRefreshToken(ctx)

	return nil
}

// startRefreshToken： 更新即将过期的token
func (tc *tokenController) startRefreshToken(ctx context.Context) {
	log.G(ctx).Info("start tokenController transfer token")
	for i := 0; i < tc.refreshTokenWorkers; i++ {
		go wait.UntilWithContext(ctx, tc.refreshWorker, DefaultRefreshTokenInterval)
	}
}

func (tc *tokenController) refreshWorker(ctx context.Context) {
	for tc.processNextWorkItem(ctx) {
	}

}

func (tc *tokenController) processNextWorkItem(ctx context.Context) bool {
	tokenKey, quit := tc.tokenQueue.Get()
	if quit {
		return false
	}
	defer tc.tokenQueue.Done(tokenKey)

	err := tc.refreshToken(ctx, tokenKey.(string))
	if err == nil {
		log.G(ctx).Infof("refresh token %s success", tokenKey.(string))
		tc.tokenQueue.Forget(tokenKey)
		return true
	}
	tc.tokenQueue.AddRateLimited(tokenKey)
	return true
}

func (tc *tokenController) transferTokenHandler(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			log.G(ctx).Info("tokenController sync loop exits")
			return
		default:
			err := tc.transferToken(ctx)
			if err != nil {
				log.G(ctx).Error(err)
			}
		}
	}
}

func (tc *tokenController) transferToken(ctx context.Context) error {
	if tc.tokenPriorityQueueSet.Len() == 0 {
		time.Sleep(DefaultTransferTokenInterval)
		return nil
	}
	tokenPop := tc.tokenPriorityQueueSet.Pop()
	if tokenPop == nil {
		time.Sleep(DefaultTransferTokenInterval)
		return nil
	}
	tokenItem, ok := tokenPop.(*token.Item)
	if ok {
		// 1. 解析token过期时间
		tokenStr, _ := (tokenItem.Value).(string)
		tokenSlice := strings.Split(tokenStr, "/")
		if len(tokenSlice) < 6 {
			return fmt.Errorf("PriorityQueueSet token %v key is invalid", tokenStr)
		}
		// Load the time zone location
		loc, err := time.LoadLocation("Asia/Shanghai") // Replace with the appropriate time zone
		if err != nil {
			log.G(ctx).Errorf("error loading time zone tokenKey %v err %v", tokenStr, err)
			return fmt.Errorf("error loading time zone err %w", err)
		}
		expTime := time.Unix(tokenItem.Priority, 0).In(loc)
		expSeconds, err := strconv.ParseInt(tokenSlice[4], 10, 64)
		// log.G(ctx).Infof("transfer token expTime %v, expSeconds %v, priority %v", expTime, expSeconds, tokenItem.Priority)
		if err != nil {
			log.G(ctx).Errorf("parse token %v expirationSeconds fail err %v", tokenStr, err)
			return fmt.Errorf("parse token %v expirationSeconds fail err %w", tokenStr, err)
		}
		// 2. 判断token是否快要过期
		expire := token.RefreshToken(expTime, expSeconds)
		if expire {
			// 将即将过期的token写入普通队列
			log.G(ctx).Infof("transfer token expire %v, expTime %v", tokenStr, expTime)
			tc.tokenQueue.Add(tokenStr)
		} else {
			// 未过期 TODO 暂不处理
		}
	}
	return nil
}

func (tc *tokenController) refreshToken(ctx context.Context, tokenKey string) error {
	tokenSlice := strings.Split(tokenKey, "/")
	if len(tokenSlice) < 6 {
		log.G(ctx).Errorf("PriorityQueueSet token %v key is invalid", tokenKey)
		return fmt.Errorf("token %v key is invalid", tokenKey)
	}
	podNamespace := tokenSlice[0]
	podName := tokenSlice[1]
	volumeName := tokenSlice[2]
	log.G(ctx).Infof("refreshToken: start refresh token %s", tokenKey)
	// 1. get k8s pod
	pod, err := tc.provider.resourceManager.GetPod(podName, podNamespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			tc.tokenManager.DeleteServiceAccountTokenByTokenKey(tokenKey)
			return nil
		}
		log.G(ctx).Errorf("refreshToken: get pod from k8s fail, so skip to refresh toke %s, err %v", tokenKey, err)
		return err
	}
	// 2. 若pod被删除，则跳过
	if pod.DeletionTimestamp != nil {
		tc.tokenManager.DeleteServiceAccountToken(pod.UID)
		log.G(ctx).Errorf("refreshToken: pod is deleting, so skip to refresh token %s", tokenKey)
		return nil
	}

	// 3. 查看pod-id是否存在
	podID := pod.GetAnnotations()[PodIDAnnotationKey]
	if podID == "" {
		log.G(ctx).Errorf("refreshToken: pod uid is empty, so skip to refresh token %s", tokenKey)
		return fmt.Errorf("pod uid is empty, token %s", tokenKey)
	}

	if _, exist := tc.refreshTokenMap.Load(tokenKey); exist {
		log.G(ctx).Info("refreshToken: tokenKey exists, so skip to refresh token %s", tokenKey)
		return nil
	}

	// 4. 查看serviceAccountToken volume信息
	tc.refreshTokenMap.Store(tokenKey, struct{}{})
	defer tc.refreshTokenMap.Delete(tokenKey)
	var (
		volumeConfigFile = new(bci.VolumeConfigFile)
		extras           = make(map[string]string)
	)
	for _, volume := range pod.Spec.Volumes {
		if volume.Projected != nil && volume.Name == volumeName {
			volumeConfigFile.DefaultMode = volume.Projected.DefaultMode
			volumeConfigFile.Name = volume.Name
			for _, ps := range volume.Projected.Sources {
				if ps.ServiceAccountToken != nil {
					key := KeyFunc(pod, ps.ServiceAccountToken, volume.Name)
					if key != tokenKey {
						continue
					}
					if !tc.tokenManager.IsServiceAccountTokenExpired(tokenKey) {
						continue
					}
					configFiles, extra, err := tc.provider.ParseServiceAccountToken(ctx, pod, ps.ServiceAccountToken, volume.Name)
					if err != nil {
						log.G(ctx).Errorf("refresh token %v fail err %v", tokenKey, err)
						return err
					}
					volumeConfigFile.ConfigFiles = append(volumeConfigFile.ConfigFiles, configFiles...)
					if len(extra) > 0 {
						for k, v := range extra {
							extras[k] = v
						}
					}
				}
			}
		}
	}

	// 5. 请求bci更新token对应的configmap
	err = tc.provider.chooseBCIClient(nil).UpdateServiceAccountToken(ctx, podID, volumeConfigFile, extras, tc.provider.getSignOption(ctx))
	if err != nil {
		log.G(ctx).Errorf("refresh token %v fail err %v", tokenKey, err)
		return err
	}

	return nil
}

// startSyncToken: 定期同步集群pod中serviceaccounttoken
func (tc *tokenController) startSyncToken(ctx context.Context) {
	ticker := tc.clock.NewTicker(tc.syncTokenInterval)
	defer ticker.Stop()

	log.G(ctx).Info("start tokenController sync loop")
	for {
		select {
		case <-ctx.Done():
			log.G(ctx).Info("tokenController sync loop exits")
			return
		case <-ticker.C():
		}
		tc.syncToken(ctx)
	}
}

func (tc *tokenController) syncToken(ctx context.Context) {
	// 1. get all pods
	pods := tc.provider.resourceManager.GetPods()

	// 2. get pod serviceAccountToken
	for _, p := range pods {
		if p.Status.Phase != v1.PodRunning {
			continue
		}
		podID := p.GetAnnotations()[PodIDAnnotationKey]
		// 跳过podId为空的pod
		if podID == "" {
			continue
		}
		tokenName := p.Spec.ServiceAccountName
		if tokenName != "" {
			// 3. get projected volume
			for _, v := range p.Spec.Volumes {
				if v.Projected != nil {
					for _, s := range v.Projected.Sources {
						if s.ServiceAccountToken != nil {
							// 4. get serviceAccountToken expiration timestamp
							key := KeyFunc(p, s.ServiceAccountToken, v.Name)
							tr := tc.tokenManager.GetServiceAccountTokenFromCache(key)
							// 5. set serviceAccountToken key and expiration timestamp in tokenPriorityQueueSet
							var priority int64
							if tr != nil {
								priority = tr.Status.ExpirationTimestamp.Unix()
							}
							tc.tokenPriorityQueueSet.Push(key, priority)
						}
					}
				}
			}
		}
	}
}

// GetServiceAccountToken: 获取serviceAccountToken
func (tc *tokenController) GetServiceAccountToken(ctx context.Context, pod *v1.Pod, serviceAccountToken *v1.ServiceAccountTokenProjection,
	tokenKey string) (*authenticationv1.TokenRequest, error) {
	var auds []string
	if len(serviceAccountToken.Audience) != 0 {
		auds = []string{serviceAccountToken.Audience}
	}

	tr, err := tc.tokenManager.GetServiceAccountToken(pod.Namespace, pod.Spec.ServiceAccountName, tokenKey, &authenticationv1.TokenRequest{
		Spec: authenticationv1.TokenRequestSpec{
			Audiences:         auds,
			ExpirationSeconds: serviceAccountToken.ExpirationSeconds,
			BoundObjectRef: &authenticationv1.BoundObjectReference{
				APIVersion: "v1",
				Kind:       "Pod",
				Name:       pod.Name,
				UID:        pod.UID,
			},
		},
	})
	if err != nil {
		log.G(ctx).Errorf("Fail to GetServiceAccountToken pod %s serviceAccountToken %s err %v", pod.Name, pod.Spec.ServiceAccountName, err)
		return nil, err
	}
	return tr, nil
}

// getAllServiceAccountToken： 获取所有pod中指定的serviceAccountToken，并写入tokenPriorityQueueSet
func (tc *tokenController) getAllServiceAccountToken(ctx context.Context) {
	log.G(ctx).Infof("start to get all serviceAccountToken")
	// 1. 获取vk上托管的所有pod
	pods := tc.provider.resourceManager.GetPods()

	// 2. 获取bci pod详情
	var wg sync.WaitGroup
	for _, pod := range pods {
		podID := pod.GetAnnotations()[PodIDAnnotationKey]
		// 跳过podID为空的pod
		if podID == "" {
			continue
		}
		if pod.Status.Phase != v1.PodRunning {
			continue
		}
		wg.Add(1)
		go func(pod *v1.Pod) {
			defer wg.Done()
			tc.reqTokens <- struct{}{}
			// 从 namespace 中提取正确的 userUUID，用于 BSC 模式的鉴权
			correctUserUUID := extractUserUUIDFromNamespace(pod.Namespace)
			var describeCtx context.Context
			if correctUserUUID != "" {
				// 创建包含正确 userUUID 的 context
				describeCtx = util.WithUserUUID(ctx, correctUserUUID)
			} else {
				describeCtx = ctx
			}
			podDetail, err := tc.provider.chooseBCIClient(nil).DescribePod(describeCtx, podID, tc.provider.getSignOption(describeCtx))
			<-tc.reqTokens
			if err != nil {
				log.G(ctx).Errorf("get pod %s/%s/%s detail from bci fail err %v", pod.Namespace, pod.Name, podID, err)
				return
			}

			// 3. 拼接pod serviceaccounttoken key
			tokenName := pod.Spec.ServiceAccountName
			if tokenName != "" {
				for _, v := range pod.Spec.Volumes {
					if v.Projected != nil {
						for _, s := range v.Projected.Sources {
							if s.ServiceAccountToken != nil {
								key := KeyFunc(pod, s.ServiceAccountToken, v.Name)
								// 4. 从bci pod extras中解析token expiration timestamp
								if value, ok := podDetail.Pod.Extras[key]; ok {
									parsedTime, err := strconv.ParseInt(value, 10, 64)
									if err != nil {
										log.G(ctx).Errorf("parse time from bci pod extras fail key %s err %v", key, err)
										return
									}
									// 5. token写入优先级队列
									tc.tokenPriorityQueueSet.Push(key, parsedTime)

									// 6. token写入tokenCache
									tc.tokenManager.SetServiceAccountToken(key, parsedTime)
								}
							}
						}
					}
				}
			}
		}(pod)
	}
	wg.Wait()
}

func KeyFunc(pod *v1.Pod, s *v1.ServiceAccountTokenProjection, volumeName string) string {
	key := fmt.Sprintf("%s/%s/%s/%s/%v/%s", pod.Namespace, pod.Name, volumeName, pod.Spec.ServiceAccountName, *s.ExpirationSeconds, pod.UID)
	if s.Audience != "" {
		key = fmt.Sprintf("%s/%s", key, s.Audience)
	}
	return key
}

// NotifyToken :
// 1. pod创建：往优先级队列tokenPriorityQueueSet中添加token
// 2. pod删除: 删除tokenCache中的token
func (tc *tokenController) NotifyToken(ctx context.Context, tokenKey string, podUID types.UID, priority int64, opType string) {
	log.G(ctx).Infof("Notify token %s, opType %s", tokenKey, opType)
	switch opType {
	case AddTokenType:
		tc.tokenPriorityQueueSet.Push(tokenKey, priority)
	case DeleteTokenType:
		tc.tokenManager.DeleteServiceAccountToken(podUID)
	}
}
