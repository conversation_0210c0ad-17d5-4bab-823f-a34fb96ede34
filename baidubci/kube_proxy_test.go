package baidubci

import (
	"context"
	"fmt"
	"path/filepath"
	"testing"

	"github.com/google/go-cmp/cmp"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
)

func TestInjectKUBEProxy(t *testing.T) {
	testCases := []struct {
		name                  string
		cfg                   *bci.PodConfig
		randToken             string
		healthzPort           int64
		annotations           map[string]interface{}
		v2                    bool
		expectedContainerName string
		expectedAnnotations   map[string]interface{}
		expectedErr           bool
		expectedPodConfig     *bci.PodConfig
	}{
		{
			name: "inject succeed",
			cfg: &bci.PodConfig{
				Name:       "test",
				Volumes:    &bci.Volumes{},
				Containers: []bci.Container{},
			},
			randToken:             "test",
			healthzPort:           10256,
			annotations:           map[string]interface{}{},
			expectedContainerName: KUBEProxyContainerNamePrefix + "test",
			expectedAnnotations: map[string]interface{}{
				PodIPAnnotationKey: &PodIPAnnotationValueType{"kube-proxy-test": {"MY_IP"}},
			},
			expectedErr: false,
			expectedPodConfig: &bci.PodConfig{
				Name: "test",
				Volumes: &bci.Volumes{
					NFS:      nil,
					EmptyDir: nil,
					ConfigFile: []bci.VolumeConfigFile{
						{
							Name: KUBEProxyConfigVolumeNamePrefix + "test",
							ConfigFiles: []bci.ConfigFile{
								{
									Path: filepath.Base(KUBEProxyConfigPath),
									File: kubeProxyConfigContentBase64,
								},
							},
						},
					},
				},
				Containers: []bci.Container{
					{
						Name:               KUBEProxyContainerNamePrefix + "test",
						ContainerImageInfo: getContainerImageInfo(KUBEProxyImage),
						MemoryInGB:         KUBEProxyMemory,
						CPUInCore:          KUBEProxyCPU,
						WorkingDir:         "",
						ImagePullPolicy:    bci.PullAlways,
						Commands:           []string{"/bin/sh"},
						Args: []string{
							"-c",
							fmt.Sprintf("iptables-ensurer && kube-proxy --bind-address=$%s --cluster-cidr=%s --proxy-mode=iptables "+
								"--masquerade-all=false --hostname-override=$%s --kubeconfig=%s --master=%s --healthz-bind-address=127.0.0.1 "+
								"--healthz-port=%d --logtostderr=true --v=6",
								KUBEProxyPodIPEnvName, clusterCIDR, KUBEProxyPodIPEnvName, KUBEProxyConfigPath, apiServerEndpoint, 10256),
						},
						VolumeMounts: []bci.VolumeMount{
							{
								Name:      KUBEProxyConfigVolumeNamePrefix + "test",
								MountPath: filepath.Dir(KUBEProxyConfigPath),
							},
						},
						Envs: []bci.Env{
							{
								Key:   KUBEProxyPodIPEnvName,
								Value: "127.0.0.1",
							},
						},
					},
				},
				ImageRegistrySecret: nil,
			},
		},
		{
			name: "inject v2 succeed",
			cfg: &bci.PodConfig{
				Name:       "test",
				Volumes:    &bci.Volumes{},
				Containers: []bci.Container{},
			},
			randToken:             "test",
			healthzPort:           10256,
			annotations:           map[string]interface{}{},
			v2:                    true,
			expectedContainerName: KUBEProxyContainerNamePrefix + "test",
			expectedAnnotations: map[string]interface{}{
				PodIPAnnotationKey: &PodIPAnnotationValueType{"kube-proxy-test": {"MY_IP"}},
			},
			expectedErr: false,
			expectedPodConfig: &bci.PodConfig{
				Name: "test",
				Volumes: &bci.Volumes{
					NFS:      nil,
					EmptyDir: nil,
					ConfigFile: []bci.VolumeConfigFile{
						{
							Name: KUBEProxyConfigVolumeNamePrefix + "test",
							ConfigFiles: []bci.ConfigFile{
								{
									Path: filepath.Base(KUBEProxyConfigPath),
									File: kubeProxyConfigContentBase64,
								},
							},
						},
					},
				},
				Containers: []bci.Container{
					{
						Name:               KUBEProxyContainerNamePrefix + "test",
						ContainerImageInfo: getContainerImageInfo(KUBEProxyImageV2),
						MemoryInGB:         KUBEProxyMemory,
						CPUInCore:          KUBEProxyCPU,
						WorkingDir:         "",
						ImagePullPolicy:    bci.PullAlways,
						Commands:           []string{"/bin/sh"},
						Args: []string{
							"-c",
							fmt.Sprintf("iptables-ensurer && kube-proxy --bind-address=$%s --cluster-cidr=%s --proxy-mode=iptables "+
								"--masquerade-all=false --hostname-override=$%s --kubeconfig=%s --master=%s --healthz-bind-address=127.0.0.1 "+
								"--healthz-port=%d --logtostderr=true --v=6 --conntrack-tcp-timeout-established=0s --conntrack-tcp-timeout-close-wait=0s "+
								"--conntrack-max-per-core=0",
								KUBEProxyPodIPEnvName, clusterCIDR, KUBEProxyPodIPEnvName, KUBEProxyConfigPath, apiServerEndpoint, 10256),
						},
						VolumeMounts: []bci.VolumeMount{
							{
								Name:      KUBEProxyConfigVolumeNamePrefix + "test",
								MountPath: filepath.Dir(KUBEProxyConfigPath),
							},
						},
						Envs: []bci.Env{
							{
								Key:   KUBEProxyPodIPEnvName,
								Value: "127.0.0.1",
							},
						},
					},
				},
				ImageRegistrySecret: nil,
			},
		},
		{
			name: "invalid port",
			cfg: &bci.PodConfig{
				Name:       "test",
				Volumes:    &bci.Volumes{},
				Containers: []bci.Container{},
			},
			healthzPort: -1,
			expectedErr: true,
			expectedPodConfig: &bci.PodConfig{
				Name:       "test",
				Volumes:    &bci.Volumes{},
				Containers: []bci.Container{},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			name, annotations, err := injectKUBEProxy(context.Background(), tc.cfg, tc.randToken, tc.healthzPort, tc.annotations, tc.v2, false, false)
			if (err == nil && tc.expectedErr) || (err != nil && !tc.expectedErr) {
				t.Errorf("expected err: %v, actual: %v", tc.expectedErr, err)
				return
			}
			if tc.expectedContainerName != name {
				t.Errorf("expected container name: %s, actual: %s", tc.expectedContainerName, name)
			}

			if !cmp.Equal(annotations, tc.expectedAnnotations) {
				t.Errorf("diff between actually and expected annotations: %s", cmp.Diff(annotations, tc.expectedAnnotations))
			}
			if !cmp.Equal(tc.cfg, tc.expectedPodConfig) {
				t.Errorf("diff between actually and expected podConfig: %s", cmp.Diff(tc.cfg, tc.expectedPodConfig))
			}

		})
	}
}

func Test_getKubeProxyHealthzPort(t *testing.T) {
	type args struct {
		annotations map[string]string
	}
	tests := []struct {
		name string
		args args
		want int64
	}{
		// All test cases.
		{
			name: "no annotation found",
			args: args{},
			want: 10256,
		},
		{
			name: "valid annotation found",
			args: args{
				annotations: map[string]string{
					KUBEProxyHealthzPortAnnotationKey: "10333",
				},
			},
			want: 10333,
		},
		{
			name: "invalid annotation found",
			args: args{
				annotations: map[string]string{
					KUBEProxyHealthzPortAnnotationKey: "103333",
				},
			},
			want: 10256,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getKubeProxyHealthzPort(tt.args.annotations); got != tt.want {
				t.Errorf("getKubeProxyHealthzPort() = %v, want %v", got, tt.want)
			}
		})
	}
}
