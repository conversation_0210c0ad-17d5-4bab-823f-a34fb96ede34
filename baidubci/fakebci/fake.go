package fakebci

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"code.cloudfoundry.org/clock"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
	bciv2 "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci/v2"
	bceutil "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/util"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"
)

var (
	_ bci.Client   = (*BCIClientFake)(nil)
	_ bciv2.Client = (*BCIClientFake)(nil)

	podRunningCost       = time.Second * 30
	containerRunningCost = time.Second * 25

	podCreateAPICost   = time.Second * 2
	podListAPICost     = time.Millisecond * 600
	podDescribeAPICost = time.Millisecond * 800
	podDeleteAPICost   = time.Millisecond * 800

	orderFailedPercent = 0
)

func init() {
	if v := os.Getenv("FAKE_BCI_ORDER_FAILED_PERCENT"); v != "" {
		if i, err := strconv.Atoi(v); err == nil && i >= 0 && i <= 100 {
			orderFailedPercent = i
		}
	}
}

type bciPod struct {
	createdAt      time.Time
	ready          bool
	orderFailed    bool
	orderEvaluated bool
	pod            *bci.DescribePodResponse
}

func (p *bciPod) evaluateOrder(ctx context.Context, now time.Time) {
	if p.orderEvaluated {
		return
	}

	if now.After(p.createdAt.Add(podRunningCost / 2)) {
		p.orderEvaluated = true
		if rand.Intn(100) < orderFailedPercent {
			logger.Infof(ctx, "[Fake BCI] order %s has failed for pod %s name=%s",
				p.pod.OrderID, p.pod.PodID, p.pod.Name)
			p.orderFailed = true
		}
	}
}

func (p *bciPod) checkReady(ctx context.Context, now time.Time) *bciPod {
	if p == nil || p.orderFailed {
		return nil
	}
	if p.ready {
		return p
	}

	p.evaluateOrder(ctx, now)
	if p.orderFailed {
		return nil
	}

	if !now.After(p.createdAt.Add(podRunningCost)) {
		return p
	}

	p.ready = true
	p.pod.Pod.Status = bci.PodStatusRunning
	p.pod.UpdatedTime = now
	for i := range p.pod.Containers {
		p.pod.Containers[i].Status = &bci.ContainerStatus{
			CurrentState: &bci.ContainerState{
				State:              bci.ContainerStateStringRunning,
				ContainerStartTime: p.createdAt.Add(containerRunningCost),
			},
		}
	}
	return p
}

// ipPool try to allocate ip ranged in 10.0.0.0-**************
type ipPool struct {
	sync.Mutex
	first, second, third, fourth int
}

// Next returns the next ip in ip pool.
func (p *ipPool) Next() string {
	p.Lock()
	defer p.Unlock()
	if p.fourth != 10 {
		p.fourth = 10
	}
	if p.first < 256 {
		p.first++
		return fmt.Sprintf("%d.%d.%d.%d", p.fourth, p.third, p.second, p.first-1)
	}
	p.first = 0
	if p.second < 256 {
		p.second += 1
		return fmt.Sprintf("%d.%d.%d.%d", p.fourth, p.third, p.second-1, p.first)
	}
	p.second = 0
	if p.third < 256 {
		p.third++
		return fmt.Sprintf("%d.%d.%d.%d", p.fourth, p.third-1, p.second, p.first)
	}

	// go back to 10.0.0.0 if exhausted, this could rarely happen.
	return fmt.Sprintf("%d.%d.%d.%d", p.fourth, p.third, p.second, p.first)
}

// BCIClientFake implements bci.Client by managing bci pod entries in memory.
// Typically for testutils focusing on vk itself.
type BCIClientFake struct {
	lock        sync.Mutex
	pods        map[string]*bciPod
	nameToPodID map[string]string
	clock       clock.Clock
	userID      string
	region      string
	ipPool      *ipPool
}

func NewBCIClientFake() *BCIClientFake {
	return &BCIClientFake{
		pods:        make(map[string]*bciPod),
		nameToPodID: make(map[string]string),
		clock:       clock.NewClock(),
		userID:      "eca97e148cb74e9683d7b7240829d1ff",
		region:      "bj",
		ipPool:      new(ipPool),
	}
}

func (*BCIClientFake) SetDebug(bool) {}

func (*BCIClientFake) GetUserVersion(context.Context, *bce.SignOption) (*bci.UserVersionResponse, error) {
	return &bci.UserVersionResponse{
		AccountID: "00dc1b52d8354d9193536e4dd2c41ae6",
		IsV2:      false,
	}, nil
}

func (*BCIClientFake) ListPodsMetrics(context.Context, []string, *bce.SignOption) (*bci.ListPodsMetricsResponse, error) {
	return nil, errors.New("not implemented")
}

func (f *BCIClientFake) CreatePod(ctx context.Context, cfg *bci.PodConfig, eipCfg *bci.EIPConfig, opt *bce.SignOption) (*bci.CreatePodResponse, error) {
	podUUID := bceutil.GetRequestID()
	orderID := strings.ReplaceAll(bceutil.GetRequestID(), "-", "")

	<-time.After(podCreateAPICost)
	f.lock.Lock()
	defer f.lock.Unlock()

	now := f.clock.Now()
	podID := bceutil.GenerateBCEShortID("p")
	for {
		if _, ok := f.pods[podID]; !ok {
			break
		}
		podID = bceutil.GenerateBCEShortID("p")
	}

	resp, err := PodConfigToPod(cfg, f.region, podID, podUUID, orderID, f.ipPool.Next(), f.userID, now)
	if err != nil {
		return nil, err
	}
	f.pods[podID] = &bciPod{
		pod:       resp,
		createdAt: now,
	}
	f.nameToPodID[cfg.Name] = podID
	logger.Infof(ctx, "[Fake BCI] pod %s name=%s order=%s created", podID, cfg.Name, orderID)

	return &bci.CreatePodResponse{
		OrderID: orderID,
		PodIDs:  []string{podID},
	}, nil

}

// ListPods only support exact keyword list option, which is sufficient for vk.
func (f *BCIClientFake) ListPods(ctx context.Context, opt *bci.ListOption, signOpt *bce.SignOption) (*bci.ListPodsResponse, error) {
	res := &bci.ListPodsResponse{
		Result:      []*bci.Pod{},
		OrderBy:     "name",
		Order:       "desc",
		MaxKeys:     bci.DefaultMaxKeys,
		IsTruncated: false,
	}

	defer func() {
		logger.Infof(ctx, "[Fake BCI] %d pods listed", len(res.Result))
	}()

	now := f.clock.Now()
	orderHasFailed := func(p *bciPod) bool {
		if p.checkReady(ctx, now) == nil {
			// order has failed
			delete(f.pods, p.pod.PodID)
			delete(f.nameToPodID, p.pod.Name)
			return true
		}
		return false
	}

	<-time.After(podListAPICost)
	f.lock.Lock()
	defer f.lock.Unlock()

	if opt == nil || opt.Keyword == nil {
		for _, v := range f.pods {
			if !orderHasFailed(v) {
				res.Result = append(res.Result, v.pod.Pod)
			}
		}
		return res, nil
	}
	switch opt.Keyword.KeywordType {
	case bci.KeywordTypeCCEID:
		for _, v := range f.pods {
			if v.pod.CCEID != opt.Keyword.Keyword {
				continue
			}
			if !orderHasFailed(v) {
				res.Result = append(res.Result, v.pod.Pod)
			}
		}
		return res, nil
	case bci.KeywordTypePodName:
		if podID, ok := f.nameToPodID[opt.Keyword.Keyword]; ok {
			if v, ok := f.pods[podID]; ok {
				if !orderHasFailed(v) {
					res.Result = append(res.Result, v.pod.Pod)
				}
			}
		}
	case bci.KeywordTypePodID:
		if v, ok := f.pods[opt.Keyword.Keyword]; ok {
			if !orderHasFailed(v) {
				res.Result = append(res.Result, v.pod.Pod)
			}
		}
	}

	return res, nil
}

func (f *BCIClientFake) ListPodsForLight(ctx context.Context, opt *bci.ListOption, signOpt *bce.SignOption) (*bci.ListPodsResponse, error) {
	res := &bci.ListPodsResponse{
		Result:      []*bci.Pod{},
		OrderBy:     "name",
		Order:       "desc",
		MaxKeys:     bci.DefaultMaxKeys,
		IsTruncated: false,
	}

	defer func() {
		logger.Infof(ctx, "[Fake BCI] %d pods listed", len(res.Result))
	}()

	now := f.clock.Now()
	orderHasFailed := func(p *bciPod) bool {
		if p.checkReady(ctx, now) == nil {
			// order has failed
			delete(f.pods, p.pod.PodID)
			delete(f.nameToPodID, p.pod.Name)
			return true
		}
		return false
	}

	<-time.After(podListAPICost)
	f.lock.Lock()
	defer f.lock.Unlock()

	if opt == nil || opt.Keyword == nil {
		for _, v := range f.pods {
			if !orderHasFailed(v) {
				res.Result = append(res.Result, v.pod.Pod)
			}
		}
		return res, nil
	}
	switch opt.Keyword.KeywordType {
	case bci.KeywordTypeCCEID:
		for _, v := range f.pods {
			if v.pod.CCEID != opt.Keyword.Keyword {
				continue
			}
			if !orderHasFailed(v) {
				res.Result = append(res.Result, v.pod.Pod)
			}
		}
		return res, nil
	case bci.KeywordTypePodName:
		if podID, ok := f.nameToPodID[opt.Keyword.Keyword]; ok {
			if v, ok := f.pods[podID]; ok {
				if !orderHasFailed(v) {
					res.Result = append(res.Result, v.pod.Pod)
				}
			}
		}
	case bci.KeywordTypePodID:
		if v, ok := f.pods[opt.Keyword.Keyword]; ok {
			if !orderHasFailed(v) {
				res.Result = append(res.Result, v.pod.Pod)
			}
		}
	}

	return res, nil
}

func (f *BCIClientFake) DeletePod(ctx context.Context, args *bci.DeletePodArgs, opt *bce.SignOption) error {
	<-time.After(podDeleteAPICost)
	f.lock.Lock()
	defer f.lock.Unlock()

	for _, arg := range args.DeletePods {
		v, ok := f.pods[arg.PodID]
		if !ok {
			return nil
		}
		v = v.checkReady(ctx, f.clock.Now())
		if v != nil && !v.ready {
			return fmt.Errorf("pod %s is not ready, cannot delete it", arg.PodID)
		}
		delete(f.nameToPodID, v.pod.Name)
		delete(f.pods, arg.PodID)
		logger.Infof(ctx, "[Fake BCI] pod %s name=%s deleted", arg.PodID, v.pod.Name)
	}
	return nil
}

func (f *BCIClientFake) DescribePod(ctx context.Context, podID string, opt *bce.SignOption) (*bci.DescribePodResponse, error) {
	<-time.After(podDescribeAPICost)
	f.lock.Lock()
	defer f.lock.Unlock()
	if v, ok := f.pods[podID]; ok {
		logger.Infof(ctx, "[Fake BCI] pod %s name=%s described", podID, v.pod.Name)
		if v.checkReady(ctx, f.clock.Now()) != nil {
			return v.pod, nil
		}
		delete(f.nameToPodID, v.pod.Name)
		delete(f.pods, v.pod.PodID)
	}
	return nil, fmt.Errorf("pod %s not found", podID)
}

func (f *BCIClientFake) DescribeBatchPodForLight(ctx context.Context, podIds []string, opt *bce.SignOption) ([]*bci.DescribePodResponse, error) {
	<-time.After(podDescribeAPICost)
	f.lock.Lock()
	defer f.lock.Unlock()
	if v, ok := f.pods[podIds[0]]; ok {
		logger.Infof(ctx, "[Fake BCI] pod %s name=%s described", podIds[0], v.pod.Name)
		if v.checkReady(ctx, f.clock.Now()) != nil {
			res := make([]*bci.DescribePodResponse, 0)
			res = append(res, v.pod)
			return res, nil
		}
		delete(f.nameToPodID, v.pod.Name)
		delete(f.pods, v.pod.PodID)
	}
	return nil, fmt.Errorf("pod %s not found", podIds[0])
}

func (f *BCIClientFake) DescribePodWithDeleted(ctx context.Context, podID string, opt *bce.SignOption) (*bci.DescribePodResponse, error) {
	<-time.After(podDescribeAPICost)
	f.lock.Lock()
	defer f.lock.Unlock()
	if v, ok := f.pods[podID]; ok {
		logger.Infof(ctx, "[Fake BCI] pod %s name=%s described", podID, v.pod.Name)
		if v.checkReady(ctx, f.clock.Now()) != nil {
			return v.pod, nil
		}
		delete(f.nameToPodID, v.pod.Name)
		delete(f.pods, v.pod.PodID)
	}
	return nil, fmt.Errorf("pod %s not found", podID)
}

func (f *BCIClientFake) GetContainerLog(ctx context.Context, podID, containerName string, logOpts *bci.LogOptions, opt *bce.SignOption) ([]byte, error) {
	return nil, errors.New("not implemented")
}

func (f *BCIClientFake) GetPodQuota(ctx context.Context, opt *bce.SignOption) (*bci.PodQuotaResponse, error) {
	return &bci.PodQuotaResponse{
		PodTotal:        10000,
		PodCreated:      10000,
		VolumeRatio:     10000,
		NfsRatio:        10000,
		EmptyDirRatio:   10000,
		ConfigFileRatio: 10000,
		EnvRatio:        10000,
		PortRatio:       10000,
	}, nil
}

func (f *BCIClientFake) LaunchExecWSSUrl(ctx context.Context, args *bci.LaunchExecWSSUrlArgs, opt *bce.SignOption) (string, error) {
	return "", errors.New("not implemented")
}

func (f *BCIClientFake) CreatePullTask(ctx context.Context, args *bci.PullTaskArgs, opt *bce.SignOption) (*bci.CreatePullTaskResponse, error) {
	return nil, errors.New("not implemented")
}

func (f *BCIClientFake) QueryPullTask(ctx context.Context, taskID string, opt *bce.SignOption) (*bci.QueryPullTaskResponse, error) {
	return nil, errors.New("not implemented")
}

func PodConfigToPod(cfg *bci.PodConfig, region, podID, podUUID, orderID, ip, userID string, createdAt time.Time) (*bci.DescribePodResponse, error) {
	if cfg == nil {
		return nil, errors.New("pod config cannot be nil")
	}
	containers := make([]bci.Container, 0, len(cfg.Containers))
	var vCPU, vMEM float64
	for _, c := range cfg.Containers {
		c.CreatedTime = createdAt
		c.Status = new(bci.ContainerStatus)
		containers = append(containers, c)
		vCPU += c.CPUInCore
		vMEM += c.MemoryInGB
	}

	pod := &bci.Pod{
		Name:          cfg.Name,
		PodID:         podID,
		PodUUID:       podUUID,
		Status:        bci.PodStatusPending, // default status
		VCPU:          vCPU,
		MemoryInGB:    vMEM,
		CCEID:         cfg.CCEID,
		InternalIP:    ip,
		RestartPolicy: cfg.RestartPolicy,
		OrderID:       orderID,
		CreatedTime:   createdAt,
		UpdatedTime:   createdAt,
		Region:        region,
		UserID:        userID,
		Labels:        cfg.Labels,
		Volumes:       cfg.Volumes,
	}

	// network related fields in DescribePodResponse are omitted as vk does not care about them.

	return &bci.DescribePodResponse{
		Pod:        pod,
		Containers: containers,
		Subnet: &bci.Subnet{
			SubnetID:   cfg.SubnetID,
			SubnetUUID: cfg.SubnetUUID,
		},
	}, nil
}

func (f *BCIClientFake) ReportPods(ctx context.Context, clusters map[string][]*bci.ClusterInfo, version string, signOpt *bce.SignOption) error {
	return nil
}

func (f *BCIClientFake) UpdateServiceAccountToken(ctx context.Context, podID string, configFile *bci.VolumeConfigFile, extras map[string]string,
	signOpt *bce.SignOption) error {
	return nil
}

func (f *BCIClientFake) UpdateConfigMap(ctx context.Context, podID string, configFile *bci.VolumeConfigFile, signOpt *bce.SignOption) error {
	return nil
}

func (*BCIClientFake) ListPodsSummary(context.Context, []string, *bce.SignOption) (*bci.ListPodsMetricsResponse, error) {
	return nil, nil
}

func (f *BCIClientFake) UpdateDsContainers(ctx context.Context, dsContainers *bci.InjectDsContainersRequest, clientToken string, opt *bce.SignOption) error {
	return nil
}
