package baidubci

import (
	"context"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"net/http"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/hashicorp/go-multierror"
	"github.com/virtual-kubelet/virtual-kubelet/errdefs"
	"github.com/virtual-kubelet/virtual-kubelet/log"
	"github.com/virtual-kubelet/virtual-kubelet/trace"
	appsv1 "k8s.io/api/apps/v1"
	v1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/intstr"
	internalresource "k8s.io/kubernetes/pkg/api/v1/resource"
	"k8s.io/kubernetes/pkg/fieldpath"

	logoperator "icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/logoperator"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
	bcerror "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/error"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/utils"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/webhooks/options"
)

// User visible annotations.
const (
	BCICreateEIPAnnotationKey              string = "bci.virtual-kubelet.io/bci-create-eip"
	BCICreateEIPBandwidthAnnotationKey     string = "bci.virtual-kubelet.io/bci-create-eip-bandwidth"
	BCICreateEIPRouteTypeAnnotationKey     string = "bci.virtual-kubelet.io/bci-create-eip-route-type"
	BCICreateEIPBillingMethodAnnotationKey string = "bci.virtual-kubelet.io/bci-create-eip-paymethod"
	BCIEIPAddressAnnotationKey             string = "bci.virtual-kubelet.io/bci-eip-ip" // user specified eip

	// Properties of EIP bound to bci currently.
	BCIBoundEIPIDAnnotationKey        string = "bci.virtual-kubelet.io/bci-bound-eip-id"
	BCIBoundEIPAddressAnnotationKey   string = "bci.virtual-kubelet.io/bci-bound-eip-ip"
	BCIBoundEIPBandwidthAnnotationKey string = "bci.virtual-kubelet.io/bci-bound-eip-bandwidth"
	BCIBoundEIPRouteTypeAnnotationKey string = "bci.virtual-kubelet.io/bci-bound-eip-route-type"
	BCIBoundEIPPayMethodAnnotationKey string = "bci.virtual-kubelet.io/bci-bound-eip-paymethod"

	BCILogicalZoneAnnotationKey     string = "bci.virtual-kubelet.io/bci-logical-zone"
	BCIVPCIDAnnotationKey           string = "bci.virtual-kubelet.io/bci-vpc-id"
	BCIVPCUUIDAnnotationKey         string = "bci.virtual-kubelet.io/bci-vpc-uuid"
	BCISubnetIDAnnotationKey        string = "bci.virtual-kubelet.io/bci-subnet-id"
	BCISecurityGroupIDAnnotationKey string = "bci.virtual-kubelet.io/bci-security-group-id"
	BCISubnetIDsAnnotationKey       string = "bci.virtual-kubelet.io/bci-subnet-ids"
	BCIApplicationAnnotationKey     string = "bci.virtual-kubelet.io/bci-application"

	BCIEnableLogPushAnnotatonKey           string = "bci.virtual-kubelet.io/bci-enable-log-push"
	BCIEnableAutoLogPushPhasesAnnotatonKey string = "bci.virtual-kubelet.io/bci-enable-auto-log-push-phases"

	BCIBidModelAnnotatonKey string = "bci.virtual-kubelet.io/bci-bid-model"
	BCIBidPriceAnnotatonKey string = "bci.virtual-kubelet.io/bci-bid-price"

	BCIGPUTypeAnnotatonKey string = "bci.virtual-kubelet.io/bci-gpu-type"

	BCICorePatternAnnotationKey string = "bci.virtual-kubelet.io/core-pattern"

	BCICPUTypeAnnotationKey       string = "bci.virtual-kubelet.io/bci-cpu-type"
	BCIDisableAutoMatchImageCache string = "bci.virtual-kubelet.io/disable-auto-match-image-cache"

	BCIDelayReleaseDurationMinuteAnnotationKey string = "bci.virtual-kubelet.io/pod-delay-release-duration-minute"
	BCIdelayReleaseSucceeded                   string = "bci.virtual-kubelet.io/pod-delay-release-succeeded"
	BCITidalPodAnnotationKey                   string = "bci.virtual-kubelet.io/bci-tidal-pod"
	BCIScheduleInPfsPoolAnnotationKey          string = "bci.virtual-kubelet.io/schedule-in-pfs-pool"
	BCIResourceTagAnnotationKey                string = "bci.virtual-kubelet.io/resource-tag"
	BCIMaxPendingMinuteAnnotationKey           string = "bci.virtual-kubelet.io/bci-max-pending-minute"
	BCIResourceIgnoreContainersAnnotationKey   string = "bci.virtual-kubelet.io/bci-resource-ignore-containers"
	BCIIgnoreExitCodeContainersAnnotationKey   string = "bci.virtual-kubelet.io/bci-ignore-exit-code-containers"
	BCIIgnoreNotReadyContainersAnnotationKey   string = "bci.virtual-kubelet.io/bci-ignore-not-ready-containers"
	BCINTPServerAnnotationKey                  string = "bci.virtual-kubelet.io/bci-ntp-server"
	BCIFailStrategyAnnotationKey               string = "bci.virtual-kubelet.io/bci-fail-strategy"

	// IPv6 support annotation
	BCIEnableIPv6AnnotationKey string = "bci.baidu.com/bci-enable-ipv6"

	CommonSecurityGroupPrefix     string = "g-"
	EnterpriseSecurityGroupPrefix string = "esg-"
)

// BCI API annotations.
const (
	PostStartHookAnnotationKey string = "bci.virtual-kubelet.io/container-post-start-hook"
	PreStopHookAnnotationKey   string = "bci.virtual-kubelet.io/container-pre-stop-hook"

	ContainerTerminationParamAnnotatonKey string = "bci.virtual-kubelet.io/container-termination-param"
)

// AuthConfig is the secret returned from an ImageRegistryCredential
type AuthConfig struct {
	Username      string `json:"username,omitempty"`
	Password      string `json:"password,omitempty"`
	Auth          string `json:"auth,omitempty"`
	Email         string `json:"email,omitempty"`
	ServerAddress string `json:"serveraddress,omitempty"`
	IdentityToken string `json:"identitytoken,omitempty"`
	RegistryToken string `json:"registrytoken,omitempty"`
}

func (p *BCIProvider) setupWebhookOptions() error {
	p.webhookServerRunOptions = options.NewWebhookServerRunOptions()
	if wsp := os.Getenv("WEBHOOK_SERVER_PORT"); wsp != "" {
		port, err := strconv.Atoi(wsp)
		if err != nil {
			return fmt.Errorf("illegal webhook server port: %w", err)
		}
		p.webhookServerRunOptions.Port = port
	}
	if isVkManaged := os.Getenv("VIRTUAL_KUBELET_MANAGED"); isVkManaged == "true" {
		p.webhookServerRunOptions.IsVKManaged = true
	}
	return nil
}

func (p *BCIProvider) setupNetworkProfile() error {
	if v := os.Getenv("BCI_VPC_ID"); v != "" {
		p.vpcID = v
	}
	if v := os.Getenv("BCI_VPC_UUID"); v != "" {
		p.vpcUUID = v
	}

	if v := os.Getenv("BCI_SECURITY_GROUP_ID"); v != "" {
		securityGroupID, err := parseSecurityGroup(v)
		if err != nil {
			return err
		}
		p.securityGroupID = securityGroupID
	} else {
		return errors.New("Security group id cannot be empty please set BCI_SECURITY_GROUP_ID\n")
	}

	// subnet and zone options
	if v := os.Getenv("BCI_SUBNET_OPTIONS"); v != "" {
		options := make(map[string]*SubnetOption)
		if err := json.Unmarshal([]byte(v), &options); err != nil {
			return fmt.Errorf("illegal subnet options: %w", err)
		}
		p.subnetOptions = options
		err := p.checkSubnetOptions()
		if err != nil {
			log.G(context.Background()).Errorf("check subnet options error: %w", err)
		}

	} else {
		// translate legacy subnet options to new options
		options, err := p.buildSubnetOptionsFromEnv()
		if err != nil {
			return fmt.Errorf("illegal subnet options: %w", err)
		}

		p.subnetOptions = options
		err = p.checkSubnetOptions()
		if err != nil {
			log.G(context.Background()).Errorf("check subnet options error: %w", err)
		}

	}
	// for later subnet selection
	rand.Seed(time.Now().UnixNano())

	return nil
}

func parseSecurityGroup(securityGroupID string) (string, error) {
	securityGroupID = strings.TrimRight(securityGroupID, ",")
	securityGroupIDs := strings.Split(securityGroupID, ",")
	// 检查是否有空的安全组ID
	for _, id := range securityGroupIDs {
		if id == "" {
			return "", errors.New("Security group id cannot be empty\n")
		}
	}
	if len(securityGroupIDs) == 1 {
		return securityGroupID, nil
	}
	// 多于5个安全组的情况，最多支持5个安全组，则取前5个
	if len(securityGroupIDs) > 5 {
		securityGroupIDs = securityGroupIDs[:5]
	}
	// 多个安全组需要判断安全组类型是否相同
	if !checkSecurityGroup(securityGroupIDs) {
		return "", errors.New("Multiple security groups must be of the same type， " +
			"common security group or enterprise security group\n")
	}
	// 重新拼接安全组ID
	securityGroupID = ""
	securityGroupID = strings.Join(securityGroupIDs, ",")

	return securityGroupID, nil
}

func checkSecurityGroup(securityGroupIDs []string) bool {
	// 判断第一个 ID 的前缀
	var prefix string
	if strings.HasPrefix(securityGroupIDs[0], CommonSecurityGroupPrefix) {
		prefix = CommonSecurityGroupPrefix
	} else if strings.HasPrefix(securityGroupIDs[0], EnterpriseSecurityGroupPrefix) {
		prefix = EnterpriseSecurityGroupPrefix
	} else {
		return false // 如果第一个元素都不符合，直接返回 false
	}

	// 遍历数组，检查所有 ID 是否都匹配相同的前缀
	for _, id := range securityGroupIDs {
		if !strings.HasPrefix(id, prefix) {
			return false
		}
	}
	return true
}

func (p *BCIProvider) buildSubnetOptionsFromEnv() (map[string]*SubnetOption, error) {
	options := make(map[string]*SubnetOption)
	logicalZones := strings.Split(os.Getenv("BCI_LOGICAL_ZONE"), ",")
	subnetIDs := strings.Split(os.Getenv("BCI_SUBNET_ID"), ",")
	if len(subnetIDs) != len(logicalZones) {
		return nil, fmt.Errorf("logical zone and subnet must be equal in size: %d != %d", len(logicalZones), len(subnetIDs))
	}
	for index, subnetID := range subnetIDs {
		if subnetID == "" {
			continue
		}
		if _, ok := options[subnetID]; ok {
			options[subnetID].Weight = options[subnetID].Weight + 1
			continue
		}
		options[subnetID] = &SubnetOption{
			Weight:      1,
			LogicalZone: logicalZones[index],
		}
	}
	return options, nil
}

func (p *BCIProvider) checkSubnetOptions() error {
	if len(p.subnetOptions) == 0 {
		return errors.New("subnet options cannot be empty")
	}
	for subnetID, opt := range p.subnetOptions {
		if subnetID == "" || opt.Weight <= 0 {
			return errors.New("subnetID and weight must be set in subnet option")
		}
	}
	return nil
}

func (p *BCIProvider) setupCapacity() error {
	p.cpu = "1000"
	p.memory = "4Ti"
	p.pods = "1000"
	p.ips = "1000"
	p.enis = "1000"
	p.ephemeralStorage = "40Ti"

	if cpuQuota := os.Getenv("BCI_QUOTA_CPU"); cpuQuota != "" {
		p.cpu = cpuQuota
	}

	if memoryQuota := os.Getenv("BCI_QUOTA_MEMORY"); memoryQuota != "" {
		p.memory = memoryQuota
	}

	if podsQuota := os.Getenv("BCI_QUOTA_PODS"); podsQuota != "" {
		p.pods = podsQuota
	}

	if ipsQuota := os.Getenv("BCI_QUOTA_IPS"); ipsQuota != "" {
		p.ips = ipsQuota
	}

	if enisQuota := os.Getenv("BCI_QUOTA_ENIS"); enisQuota != "" {
		p.enis = enisQuota
	}

	if ephemeralStorageQuota := os.Getenv("BCI_QUOTA_EPHEMERAL_STORAGE"); ephemeralStorageQuota != "" {
		p.ephemeralStorage = ephemeralStorageQuota
	}

	return nil
}

// BCIAnnotationParams represents all bci params which can be set in pod annotation
type BCIAnnotationParams struct {
	LogicalZone                string
	VPCID, VPCUUID             string
	SubnetID                   string
	SecurityGroupID            string
	SubnetIDs                  string
	Application                string
	CreateEIP                  bool
	CreateEIPBandwidth         int
	CreateEIPRouteType         string
	CreateEIPBillingMethod     string
	EIPAddress                 string
	EnableLogPush              bool
	EnableAutoLogPushPhases    string
	BidModel                   string
	BidPrice                   float64
	GPUType                    string
	CorePattern                string
	CPUType                    string
	DelayReleaseDurationMinute int
	DelayReleaseSucceeded      bool
	IsTidal                    bool
	DisableAutoMatchImageCache bool
	EnableIPv6                 bool
}

func (params *BCIAnnotationParams) validate() error {
	// id and uuid of the same object must be set simultaneously
	if (params.VPCID == "") != (params.VPCUUID == "") {
		return fmt.Errorf("illegal bci annotation: %s and %s must be set simultaneously",
			BCIVPCIDAnnotationKey, BCIVPCUUIDAnnotationKey)
	}
	if params.CreateEIP && params.CreateEIPBandwidth <= 0 {
		return fmt.Errorf("illegal bci annotation: %s must be greater than 0", BCICreateEIPBandwidthAnnotationKey)
	}

	if params.CreateEIPBillingMethod != "" {
		if params.CreateEIPBillingMethod != string(bci.V2SubProductTypeByTraffic) && params.CreateEIPBillingMethod != string(bci.V2SubProductTypeByBandwidth) {
			return fmt.Errorf("illegal bci annotation: %s is found while only %s and %s are supported",
				BCICreateEIPBillingMethodAnnotationKey, bci.V2SubProductTypeByTraffic, bci.V2SubProductTypeByBandwidth)
		}
	}

	if v := params.EnableAutoLogPushPhases; v != "" {
		for _, phase := range strings.Split(v, ",") {
			if phase != string(v1.PodFailed) && phase != string(v1.PodSucceeded) {
				return fmt.Errorf("illegal bci annotation: phase %s is found while only %s and %s is supported for %s",
					phase, v1.PodFailed, v1.PodSucceeded, BCIEnableAutoLogPushPhasesAnnotatonKey)
			}
		}
	}
	if params.BidModel == bci.BidModelCustom {
		if params.BidPrice <= 0 {
			return errors.New("bid price must be set for custom bid model")
		}
	}
	return nil
}

func parseBCIAnnotations(ctx context.Context, annotations map[string]string) (*BCIAnnotationParams, error) {
	params := &BCIAnnotationParams{
		LogicalZone:             annotations[BCILogicalZoneAnnotationKey],
		VPCID:                   annotations[BCIVPCIDAnnotationKey],
		VPCUUID:                 annotations[BCIVPCUUIDAnnotationKey],
		SubnetID:                annotations[BCISubnetIDAnnotationKey],
		SubnetIDs:               annotations[BCISubnetIDsAnnotationKey],
		Application:             annotations[BCIApplicationAnnotationKey], // empty string is default value
		EnableAutoLogPushPhases: annotations[BCIEnableAutoLogPushPhasesAnnotatonKey],
		CreateEIPRouteType:      annotations[BCICreateEIPRouteTypeAnnotationKey],
		CreateEIPBillingMethod:  annotations[BCICreateEIPBillingMethodAnnotationKey],
		BidModel:                annotations[BCIBidModelAnnotatonKey],
		GPUType:                 annotations[BCIGPUTypeAnnotatonKey],
		CorePattern:             annotations[BCICorePatternAnnotationKey],
		CPUType:                 annotations[BCICPUTypeAnnotationKey],
	}

	if v := annotations[BCISecurityGroupIDAnnotationKey]; v != "" {
		securityGroupID, err := parseSecurityGroup(v)
		if err != nil {
			return nil, err
		}
		params.SecurityGroupID = securityGroupID
	}

	if annotations[BCIEnableLogPushAnnotatonKey] == "true" {
		params.EnableLogPush = true
	}

	if v := annotations[BCICreateEIPAnnotationKey]; v == "true" {
		params.CreateEIP = true
	}
	eipBandwidth := 100 // default to 100Mbps
	if v := annotations[BCICreateEIPBandwidthAnnotationKey]; v != "" {
		if gotBandwidth, err := strconv.Atoi(v); err == nil && gotBandwidth > 0 {
			eipBandwidth = gotBandwidth
		}
	}
	params.CreateEIPBandwidth = eipBandwidth

	if v := annotations[BCIEIPAddressAnnotationKey]; v != "" {
		if params.CreateEIP {
			return nil, fmt.Errorf("eip address cannot be present while %s is true", BCICreateEIPAnnotationKey)
		}
		params.EIPAddress = v
	}

	var bidPrice float64
	if v := annotations[BCIBidPriceAnnotatonKey]; v != "" {
		if gotBidPrice, err := strconv.ParseFloat(v, 64); err == nil && gotBidPrice > 0 {
			bidPrice = gotBidPrice
		}
	}
	params.BidPrice = bidPrice

	if v := annotations[BCIDelayReleaseDurationMinuteAnnotationKey]; v != "" {
		if got, err := strconv.Atoi(v); err == nil && got > 0 {
			params.DelayReleaseDurationMinute = got
		} else {
			return nil, fmt.Errorf("invalid annotation value '%s' for %s",
				v, BCIDelayReleaseDurationMinuteAnnotationKey)
		}
	}
	if v := annotations[BCIdelayReleaseSucceeded]; v == "true" {
		params.DelayReleaseSucceeded = true
	}

	if annotations[BCITidalPodAnnotationKey] == "true" {
		params.IsTidal = true
	}

	if annotations[BCIDisableAutoMatchImageCache] == "true" {
		params.DisableAutoMatchImageCache = true
	} else if annotations[BCIDisableAutoMatchImageCache] == "false" {
		params.DisableAutoMatchImageCache = false
	}

	if annotations[BCIEnableIPv6AnnotationKey] == "true" {
		params.EnableIPv6 = true
	}
	if err := params.validate(); err != nil {
		return nil, err
	}
	log.G(ctx).WithField("method", "parseBCIAnnotations").Debugf("get bci params from annotation: %v", *params)
	return params, nil
}

func getProtocol(pro v1.Protocol) bci.ContainerNetworkProtocol {
	switch pro {
	case v1.ProtocolUDP:
		return bci.ContainerNetworkProtocolUDP
	default:
		return bci.ContainerNetworkProtocolTCP
	}
}

func getPullPolicy(policy v1.PullPolicy) bci.PullPolicy {
	switch policy {
	case v1.PullAlways:
		return bci.PullAlways
	case v1.PullNever:
		return bci.PullNever
	default:
		return bci.PullIfNotPresent
	}
}

func getContainerImageInfo(image string) *bci.ContainerImageInfo {
	info := new(bci.ContainerImageInfo)
	// split image with slash first
	slashParts := strings.Split(image, "/")
	// split last element with colon
	colonParts := strings.Split(slashParts[len(slashParts)-1], ":")

	if len(slashParts) < 2 { // no slash is found
		info.ImageAddress = colonParts[0]
	} else {
		info.ImageAddress = strings.Join(slashParts[0:len(slashParts)-1], "/") + "/" + colonParts[0]
	}

	info.ImageName = colonParts[0]
	if len(colonParts) < 2 { // no explicit image version is found
		info.ImageVersion = "latest"
	} else {
		info.ImageVersion = colonParts[1]
	}

	return info
}

func (p *BCIProvider) selectSubnet(ctx context.Context, pod *v1.Pod) (
	string, string, error) {
	var weightSum int
	var weights = map[int]string{}
	for subnetID, option := range p.subnetOptions {
		if p.podCache != nil {
			// TODO add usage of pod itself
			if usage := p.podCache.GetSubnetUsage(ctx, subnetID); !usage.Less(option.Quota) {
				log.G(ctx).WithFields(log.Fields{
					"subnetID": subnetID,
					"usage":    usage,
				}).Debug("out of subnet resource quota")
				continue
			}
		}

		for i := weightSum; i < weightSum+option.Weight; i++ {
			weights[i] = subnetID
		}
		weightSum += option.Weight
	}
	if len(weights) == 0 {
		return "", "", fmt.Errorf("no subnet available for creation due to all subnets are out of quota: %+v", p.subnetOptions)
	}

	subnetID := weights[rand.Intn(weightSum)]
	zone := p.subnetOptions[subnetID].LogicalZone
	return subnetID, zone, nil
}

// getContainers 将Kubernetes的v1.Container列表转换为BCI平台的bci.Container列表。
//
// 主要功能：
//   - 遍历传入的K8s容器列表（cs），将其各项属性（如镜像、命令、资源、端口、环境变量等）
//     转换为BCI平台所需的bci.Container结构体。
//   - 支持资源（CPU/内存/临时存储/GPU）默认值和优先级处理，兼容部分业务默认资源为0的场景。
//   - 处理端口名到端口号的转换，保证探针（Startup/Liveness/Readiness）端口正确。
//   - 支持日志采集配置（优先环境变量指定，其次自动注入）。
//   - 支持挂载点、生命周期、拉取策略、安全上下文等K8s原生特性。
//
// 参数：
//
//	cs      - K8s原生容器列表（[]v1.Container）
//	gpuType - GPU类型（string），用于BCI容器的GPUType字段
//	pod     - 所属Pod对象指针（*v1.Pod），部分日志采集等场景可能用到
//
// 返回值：
//
//	[]bci.Container - 转换后的BCI容器列表
//	error           - 转换过程中遇到的错误
func (p *BCIProvider) getContainers(cs []v1.Container, gpuType string, pod *v1.Pod, logTaskIdx *int) ([]bci.Container, error) {
	containers := make([]bci.Container, 0, len(cs))
	for _, v1container := range cs {
		c := bci.Container{
			Name:               v1container.Name,
			ContainerImageInfo: getContainerImageInfo(v1container.Image),
			WorkingDir:         v1container.WorkingDir,
			Commands:           v1container.Command,
			Args:               v1container.Args,
			ImagePullPolicy:    getPullPolicy(v1container.ImagePullPolicy),
			VolumeMounts:       make([]bci.VolumeMount, 0, len(v1container.VolumeMounts)),
			Ports:              make([]bci.ContainerPort, 0, len(v1container.Ports)),
			Envs:               make([]bci.Env, 0, len(v1container.Env)),
			Stdin:              v1container.Stdin,
			StdinOnce:          v1container.StdinOnce,
			TTY:                v1container.TTY,
			Lifecycle:          v1container.Lifecycle,
			SecurityContext:    v1container.SecurityContext,
		}

		portNames := make(map[string]int32, len(v1container.Ports))
		for _, port := range v1container.Ports {
			c.Ports = append(c.Ports, bci.ContainerPort{
				Port:     port.ContainerPort,
				Protocol: getProtocol(port.Protocol),
				Name:     port.Name,
			})
			portNames[port.Name] = port.ContainerPort
		}

		for _, e := range v1container.Env {
			c.Envs = append(c.Envs, bci.Env{
				// NOTE(yezichao) if value from secret need more concern?
				Key:       e.Name,
				Value:     e.Value,
				ValueFrom: e.ValueFrom,
			})
		}

		// TODO(yezichao) cpu/memory/gpu should have a limited accurancy (e.g. times of 10m/0.1 GB)
		// Default values.
		var defaultCPUInCore float64 = 1.00
		var defaultMemoryInGB float64 = 2.00
		var defaultEphemeralStorageGB = 0.00
		var defaultGPUCount float64 = 0.00
		// 作业帮BCI Pod未写具体资源需求时默认cpu/memory为0；且只看limits
		if p.enableDefaultZeroResource {
			defaultCPUInCore = 0.00
			defaultMemoryInGB = 0.00
		}
		var resourceList = []v1.ResourceList{v1container.Resources.Limits}
		if !p.enableDefaultZeroResource {
			resourceList = append(resourceList, v1container.Resources.Requests)
		}

		// Find largest resource values if exists.
		for _, rList := range resourceList {
			if quantity, ok := rList[v1.ResourceCPU]; ok && !quantity.IsZero() {
				valueInCore := float64(quantity.MilliValue()/10.00) / 100.00
				if valueInCore > c.CPUInCore {
					c.CPUInCore = valueInCore
				}
			}

			if quantity, ok := rList[v1.ResourceMemory]; ok && !quantity.IsZero() {
				valueInGB := float64(quantity.Value()/1024.00/1024.00) / 1024.00
				if valueInGB > c.MemoryInGB {
					c.MemoryInGB = valueInGB
				}
			}

			if quantity, ok := rList[v1.ResourceEphemeralStorage]; ok && !quantity.IsZero() {
				valueInGB := float64(quantity.Value()/1024.00/1024.00) / 1024.00
				if valueInGB > c.EphemeralStorageGB {
					c.EphemeralStorageGB = valueInGB
				}
			}

			if quantity, ok := rList[ResourceGPU]; ok && !quantity.IsZero() {
				value := float64(quantity.MilliValue()/10.00) / 100.00
				if value > c.GPUCount {
					c.GPUCount = value
				}
			}
		}

		// Assign defaults if not specified.
		if c.CPUInCore == 0.00 {
			c.CPUInCore = defaultCPUInCore
		}
		if c.MemoryInGB == 0.00 {
			c.MemoryInGB = defaultMemoryInGB
		}
		if c.EphemeralStorageGB == 0.00 {
			c.EphemeralStorageGB = defaultEphemeralStorageGB
		}
		if c.GPUCount == 0.00 {
			c.GPUCount = defaultGPUCount
		}

		// TODO: if gpuType can be empty for non-zero gpuCount？
		c.GPUType = gpuType

		var err error
		if c.StartupProbe, err = translatePortName(v1container.StartupProbe, portNames); err != nil {
			return nil, err
		}
		if c.LivenessProbe, err = translatePortName(v1container.LivenessProbe, portNames); err != nil {
			return nil, err
		}
		if c.ReadinessProbe, err = translatePortName(v1container.ReadinessProbe, portNames); err != nil {
			return nil, err
		}

		// 如果通过环境变量指定了日志采集方式，则使用环境变量指定的日志采集方式
		if c.LogCollections, err = getLogCollections(v1container); err != nil {
			return nil, err
		}
		// 如果没有通过环境变量指定日志采集，尝试使用log operator自动注入逻辑
		if len(c.LogCollections) == 0 {
			log.G(context.Background()).WithField("method", "getLogOperatorCollections").Debugf("get log operator collections")
			c.LogCollections, err = getLogOperatorCollections(&v1container, pod, logTaskIdx, p.resourceManager.GetLogConfigController())
			if err != nil {
				return nil, err
			}

		}
		for _, v := range v1container.VolumeMounts {
			c.VolumeMounts = append(c.VolumeMounts, bci.VolumeMount{
				Name:        v.Name,
				MountPath:   v.MountPath,
				ReadOnly:    v.ReadOnly,
				SubPath:     v.SubPath,
				SubPathExpr: v.SubPathExpr,
			})
		}

		containers = append(containers, c)
	}

	return containers, nil
}

func getLogOperatorCollections(container *v1.Container, pod *v1.Pod,
	logTaskIdx *int, logConfigController *logoperator.LogConfigController) (collections []*bci.LogCollection, err error) {
	logConfigs := logConfigController.ListValidLogConfigs()
	hasStdoutTask := false
	for _, lc := range logConfigs {
		if lc.Spec.SrcConfig.MatchLabels != nil {
			matchLabel := true
			for _, label := range lc.Spec.SrcConfig.MatchLabels {
				switch label.Key {
				case "io.kubernetes.container.name":
					if label.Value != container.Name {
						matchLabel = false
					}
				case "io.kubernetes.pod.namespace":
					if label.Value != pod.Namespace {
						matchLabel = false
					}
				}
			}
			if !matchLabel {
				continue
			}
		}
		collection := &bci.LogCollection{
			Name:    "bci-" + lc.Name,
			SrcType: string(lc.Spec.SrcConfig.LogType),
			Index:   *logTaskIdx,
			SrcConfig: bci.SrcConfig{
				SrcDir:         lc.Spec.SrcConfig.SrcDir,
				MatchedPattern: lc.Spec.SrcConfig.MatchPattern,
				TTL:            lc.Spec.SrcConfig.TTL,
			},
			DestConfig: bci.DestConfig{
				DestType:  string(lc.Spec.DstConfig.DstType),
				LogStore:  lc.Spec.DstConfig.LogStore,
				RateLimit: lc.Spec.DstConfig.RateLimit,
			},
		}
		*logTaskIdx++
		switch collection.SrcType {
		case "stdout":
			// 如果srcType为stdout，则将srcDir设置为stdout
			collection.SrcConfig.SrcDir = "stdout"
			hasStdoutTask = true
			// 添加volumeMount信息
			// volumeMounts:
			// - mountPath: /stdout
			//   name: sidecar-stdout
			container.VolumeMounts = append(container.VolumeMounts, v1.VolumeMount{
				MountPath: "/stdout",
				Name:      "sidecar-stdout",
			})
		case "internal":
			// collection.SrcConfig.SrcDir 是从logConfig中读取的目录信息，不需要修改
			// 设置 volumeMount信息
			// volumeMounts:
			// - mountPath: /var/log/sidecar-internal
			//   name: sidecar-internal
			container.VolumeMounts = append(container.VolumeMounts, v1.VolumeMount{
				MountPath: collection.SrcConfig.SrcDir,
				Name:      container.Name + "-" + collection.Name,
			})
			// 设置 volume信息
			// volumes:
			// - name: emptydir1
			//   emptyDir: {}
			pod.Spec.Volumes = append(pod.Spec.Volumes, v1.Volume{
				Name: container.Name + "-" + collection.Name,
				VolumeSource: v1.VolumeSource{
					EmptyDir: &v1.EmptyDirVolumeSource{},
				},
			})
		}
		if collection.SrcConfig.MatchedPattern == "" {
			collection.SrcConfig.MatchedPattern = ".*"
		}
		collections = append(collections, collection)
	}
	// 这只pod的volume信息和容器的volumeMount信息
	if hasStdoutTask {
		// 如果pod中没有sidecar-stdout的volume，则添加
		hasStdoutVolume := false
		for _, v := range pod.Spec.Volumes {
			if v.Name == "sidecar-stdout" {
				hasStdoutVolume = true
				break
			}
		}
		if !hasStdoutVolume {
			// volumes:
			// - name: sidecar-stdout
			//   flexVolume:
			//     driver: k8s/sidecar-stdout
			pod.Spec.Volumes = append(pod.Spec.Volumes, v1.Volume{
				Name: "sidecar-stdout",
				VolumeSource: v1.VolumeSource{
					FlexVolume: &v1.FlexVolumeSource{
						Driver: "k8s/sidecar-stdout",
					},
				},
			})
		}
	}
	return collections, nil
}

// translatePortName ensures port representation in probe is port num (translate port name to port num if necessary).
func translatePortName(probe *v1.Probe, portNames map[string]int32) (*v1.Probe, error) {
	if probe == nil {
		return nil, nil
	}
	ret := probe.DeepCopy()
	// getNumPort ensures port is represented as port num.
	getNumPort := func(port intstr.IntOrString) (intstr.IntOrString, bool) {
		if port.Type == intstr.Int {
			return port, true
		}
		if portNum, ok := portNames[port.StrVal]; ok {
			return intstr.FromInt(int(portNum)), true
		}
		return port, false
	}

	var ok bool
	if v := probe.ProbeHandler.HTTPGet; v != nil {
		ret.ProbeHandler.HTTPGet.Port, ok = getNumPort(v.Port)
		if !ok {
			return nil, fmt.Errorf("unknown port name %s in probe", v.Port.StrVal)
		}
	}
	if v := probe.ProbeHandler.TCPSocket; v != nil {
		ret.ProbeHandler.TCPSocket.Port, ok = getNumPort(v.Port)
		if !ok {
			return nil, fmt.Errorf("unknown port name %s in probe", v.Port.StrVal)
		}
	}

	return ret, nil
}

func getLifecycleAnnotations(pod *v1.Pod) map[string]map[string]*v1.LifecycleHandler {
	postStarts := make(map[string]*v1.LifecycleHandler)
	preStops := make(map[string]*v1.LifecycleHandler)

	for _, c := range append(pod.Spec.InitContainers, pod.Spec.Containers...) {
		if lc := c.Lifecycle; lc != nil {
			if lc.PostStart != nil {
				postStarts[c.Name] = lc.PostStart
			}
			if lc.PreStop != nil {
				preStops[c.Name] = lc.PreStop
			}
		}
	}

	return map[string]map[string]*v1.LifecycleHandler{
		PostStartHookAnnotationKey: postStarts,
		PreStopHookAnnotationKey:   preStops,
	}
}

type TerminationParam struct {
	TerminationMessagePath   string                      `json:"terminationMessagePath"`
	TerminationMessagePolicy v1.TerminationMessagePolicy `json:"terminationMessagePolicy"`
	ContainerType            bci.ContainerType           `json:"containerType"`
}

func getTerminationParamAnnotations(pod *v1.Pod) map[string]map[string]*TerminationParam {
	result := make(map[string]*TerminationParam)
	for cType, containers := range map[bci.ContainerType][]v1.Container{
		bci.ContainerTypeInit:     pod.Spec.InitContainers,
		bci.ContainerTypeWorkload: pod.Spec.Containers,
	} {
		for _, c := range containers {
			result[c.Name] = &TerminationParam{
				TerminationMessagePath:   c.TerminationMessagePath,
				TerminationMessagePolicy: c.TerminationMessagePolicy,
				ContainerType:            cType,
			}
		}
	}
	return map[string]map[string]*TerminationParam{
		ContainerTerminationParamAnnotatonKey: result,
	}
}

func (p *BCIProvider) getNetworkPrivilegedContainers(pod *v1.Pod) []string {
	var containers []string
	for _, container := range pod.Spec.Containers {
		if v := container.SecurityContext; v != nil && v.Capabilities != nil {
			for _, capability := range v.Capabilities.Add {
				if capability == "NET_ADMIN" {
					containers = append(containers, container.Name)
					break
				}
			}
		}
	}

	return containers
}

func (p *BCIProvider) getImageRegistrySecrets(pod *v1.Pod) ([]bci.ImageRegistrySecret, error) {
	irs := make([]bci.ImageRegistrySecret, 0, len(pod.Spec.ImagePullSecrets))
	for _, ref := range pod.Spec.ImagePullSecrets {
		secret, err := p.resourceManager.GetSecret(ref.Name, pod.Namespace)
		if err != nil {
			return irs, err
		}
		if secret == nil {
			return nil, errors.New("error getting image pull secret")
		}

		switch secret.Type {
		case v1.SecretTypeDockercfg:
			irs, err = readDockerCfgSecret(secret, irs)
		case v1.SecretTypeDockerConfigJson:
			irs, err = readDockerConfigJSONSecret(secret, irs)
		default:
			return nil, errors.New("image pull secret type is not one of kubernetes.io/dockercfg or kubernetes.io/dockerconfigjson")
		}

		if err != nil {
			return irs, err
		}
	}
	return irs, nil
}

func readDockerCfgSecret(secret *v1.Secret, irs []bci.ImageRegistrySecret) ([]bci.ImageRegistrySecret, error) {
	var err error
	var authConfigs map[string]AuthConfig
	repoData, ok := secret.Data[string(v1.DockerConfigKey)]

	if !ok {
		return irs, errors.New("no dockercfg present in secret")
	}

	err = json.Unmarshal(repoData, &authConfigs)
	if err != nil {
		return irs, err
	}

	for server := range authConfigs {
		cred, err := makeRegistryCredential(server, authConfigs[server])
		if err != nil {
			return irs, err
		}

		irs = append(irs, *cred)
	}

	return irs, err
}

func readDockerConfigJSONSecret(secret *v1.Secret, irs []bci.ImageRegistrySecret) ([]bci.ImageRegistrySecret, error) {
	var err error
	repoData, ok := secret.Data[string(v1.DockerConfigJsonKey)]

	if !ok {
		return irs, errors.New("no dockerconfigjson present in secret")
	}

	var authConfigs map[string]map[string]AuthConfig

	err = json.Unmarshal(repoData, &authConfigs)
	if err != nil {
		return irs, err
	}

	auths, ok := authConfigs["auths"]
	if !ok {
		return irs, errors.New("malformed dockerconfigjson in secret")
	}

	for server := range auths {
		cred, err := makeRegistryCredential(server, auths[server])
		if err != nil {
			return irs, err
		}

		irs = append(irs, *cred)
	}

	return irs, err
}

func makeRegistryCredential(server string, authConfig AuthConfig) (*bci.ImageRegistrySecret, error) {
	username := authConfig.Username
	password := authConfig.Password

	if username == "" {
		if authConfig.Auth == "" {
			return nil, fmt.Errorf("no username present in auth config for server: %s", server)
		}

		decoded, err := base64.StdEncoding.DecodeString(authConfig.Auth)
		if err != nil {
			return nil, fmt.Errorf("error decoding the auth for server: %s Error: %w", server, err)
		}

		parts := strings.Split(string(decoded), ":")
		if len(parts) != 2 {
			return nil, fmt.Errorf("malformed auth for server: %s", server)
		}

		username = parts[0]
		password = parts[1]
	}

	cred := bci.ImageRegistrySecret{
		Server:   server,
		UserName: username,
		Password: password,
	}

	return &cred, nil
}

// adjustBCISecretsBasedOnImages appends namespace to secret server if match image is found.
// That is, with image "hub.baidubce.com/cce/nginx" the secret should be adjusted as following:
//
//	{
//		"server":   "https://hub.baidubce.com", => "https://hub.baidubce.com/cce",
//		"userName": "user1",
//		"password": "password1",
//	}
//
// Assumes only one secret for each registry.
func adjustBCISecretsBasedOnImages(secrets []bci.ImageRegistrySecret, containers []bci.Container) (
	ret []bci.ImageRegistrySecret) {
	if len(secrets) == 0 {
		return
	}
	// NOTE: leave only one secret for each server, later one precedents if multiple present.
	// This is a compromise for the current bci implementation that only one secret for each image will be selected.
	secretInfos := make(map[string]bci.ImageRegistrySecret)
	for _, secret := range secrets {
		server := secret.Server
		// remove scheme if exists
		if subs := strings.Split(server, "://"); len(subs) > 1 {
			server = subs[1]
		}
		secretInfos[server] = secret
	}

	// containerInfos holds all distinct `[server/]namespace` strings for all containers
	containerInfos := make(map[string]struct{})
	for _, c := range containers {
		index := strings.LastIndex(c.ImageAddress, c.ImageName)
		if index == 0 {
			// default docker registry image without namespace
			continue
		}
		// extract only `[server/]namespace` into key
		containerInfos[c.ImageAddress[0:index-1]] = struct{}{}
	}

	for ci := range containerInfos {
		if secret, ok := secretInfos[ci]; ok {
			// server field already contains namespace in the original secret, just use it
			ret = append(ret, secret)
			continue
		}
		serverAndNamespace := strings.Split(ci, "/")
		if len(serverAndNamespace) < 2 {
			// default docker registry image with namespace, fill the server field with empty
			serverAndNamespace = append(serverAndNamespace, serverAndNamespace[0])
			serverAndNamespace[0] = ""
		}
		if secret, ok := secretInfos[serverAndNamespace[0]]; ok {
			server := secret.Server
			// add slash if server is not empty
			if server != "" {
				server += "/"
			}
			ret = append(ret, bci.ImageRegistrySecret{
				Server:   server + serverAndNamespace[1],
				UserName: secret.UserName,
				Password: secret.Password,
			})
		}
	}
	return
}

// mapToItems handles the key path mapping stored in items (if exists) in configMap/secret volume
func mapToItems(pathFile map[string]string, items []v1.KeyToPath, optional bool) (
	configFiles []bci.ConfigFile, err error) {
	if len(items) > 0 {
		configFiles = make([]bci.ConfigFile, 0, len(items))
		// set projection between key and path
		keyToPath := make(map[string]string, len(items))
		for _, item := range items {
			if _, ok := pathFile[item.Key]; !ok && !optional {
				err = fmt.Errorf("key %s not found in non-optional volume", item.Key)
				return
			}
			keyToPath[item.Key] = item.Path
		}
		for k, v := range pathFile {
			if path, ok := keyToPath[k]; ok {
				configFiles = append(configFiles, bci.ConfigFile{
					Path: path,
					File: v,
				})
			}
		}
	} else {
		configFiles = make([]bci.ConfigFile, 0, len(pathFile))
		for k, v := range pathFile {
			configFiles = append(configFiles, bci.ConfigFile{
				Path: k,
				File: v,
			})
		}
	}
	return
}

func (p *BCIProvider) getAffinity(ctx context.Context, pod *v1.Pod) (*v1.Affinity, error) {
	ctx, span := trace.StartSpan(ctx, "getAffinity")
	defer span.End()
	var affinity v1.Affinity
	if pod.Annotations != nil {
		affinityStr, ok := pod.Annotations["bci.virtual-kubelet.io/affinity"]
		if ok && affinityStr != "" {
			err := json.Unmarshal([]byte(affinityStr), &affinity)
			if err != nil {
				log.G(ctx).Errorf("unmarshal affinity in annotation error: %+v, pod: %s, affinity: %s", err, pod.GetName(), affinityStr)
				return &affinity, err
			}
			return &affinity, nil
		}
	}
	return pod.Spec.Affinity, nil
}

func (p *BCIProvider) GetDsVolumes(ctx context.Context, ds *appsv1.DaemonSet) (*bci.Volumes, error) {
	bciVolumes := new(bci.Volumes)
	// single volume translation process
	tranlateVolume := func(v v1.Volume, volumes *bci.Volumes) error {
		// Handle the case for the EmptyDir.
		if v.EmptyDir != nil {
			bciEmptyDir := bci.VolumeEmptyDir{
				Name:     v.Name,
				DsVolume: true,
			}
			var sizeLimitInGiB float64
			if v.EmptyDir.SizeLimit != nil {
				quantity := v.EmptyDir.SizeLimit
				if !quantity.IsZero() {
					sizeLimitInGiB = float64(quantity.Value()/1024.00/1024.00) / 1024.00
				}
				bciEmptyDir.SizeLimitGiB = sizeLimitInGiB
			}

			if v.EmptyDir.Medium != "" {
				bciEmptyDir.Medium = bci.StorageMedium(v.EmptyDir.Medium)
			}

			volumes.EmptyDir = append(volumes.EmptyDir, bciEmptyDir)
			return nil
		}

		// Handle the case for ConfigMap volume.
		if v.ConfigMap != nil {
			configMapInfo := &ConfigMapInfo{
				VolumeName:    v.Name,
				ConfigMapName: v.ConfigMap.Name,
				Namespace:     ds.Namespace,
				Items:         v.ConfigMap.Items,
				Optional:      v.ConfigMap.Optional,
			}
			configFiles, err := p.parseConfigMapVolume(ctx, configMapInfo)
			if err != nil {
				return err
			}

			if len(configFiles) != 0 {
				volumes.ConfigFile = append(volumes.ConfigFile, bci.VolumeConfigFile{
					Name:        v.Name,
					ConfigFiles: configFiles,
					DefaultMode: v.ConfigMap.DefaultMode,
					DsVolume:    true,
				})
			}
			return nil
		}

		// Handle the case for the HostPath.
		if v.HostPath != nil {
			volumes.HostPath = append(volumes.HostPath, bci.VolumeHostPath{
				Name:     v.Name,
				Path:     v.HostPath.Path,
				Type:     v.HostPath.Type,
				DsVolume: true,
			})
			return nil
		}

		// If we've made it this far we have found a volume type that isn't supported
		return fmt.Errorf("unsupported volume type ds %s volume %s ", ds.Name, v.Name)
	}

	for _, v := range ds.Spec.Template.Spec.Volumes {
		if err := tranlateVolume(v, bciVolumes); err != nil {
			return nil, err
		}
	}
	return bciVolumes, nil
}

func (p *BCIProvider) getVolumes(ctx context.Context, pod *v1.Pod) (*bci.Volumes, error) {
	ctx, span := trace.StartSpan(ctx, "getVolumes")
	defer span.End()

	bciVolumes := new(bci.Volumes)
	type failedVolume struct {
		v   v1.Volume
		err error
	}
	failedVolumes := make(chan failedVolume, len(pod.Spec.Volumes))
	tokenExtras := make(map[string]string)

	// single volume translatation process
	tranlateVolume := func(v v1.Volume, volumes *bci.Volumes) error {
		// Handle the case for the EmptyDir.
		if v.EmptyDir != nil {
			bciEmptyDir := bci.VolumeEmptyDir{
				Name: v.Name,
			}
			var sizeLimitInGiB float64
			if v.EmptyDir.SizeLimit != nil {
				quantity := v.EmptyDir.SizeLimit
				if !quantity.IsZero() {
					sizeLimitInGiB = float64(quantity.Value()/1024.00/1024.00) / 1024.00
				}
				bciEmptyDir.SizeLimitGiB = sizeLimitInGiB
			}

			if v.EmptyDir.Medium != "" {
				bciEmptyDir.Medium = bci.StorageMedium(v.EmptyDir.Medium)
			}

			volumes.EmptyDir = append(volumes.EmptyDir, bciEmptyDir)
			return nil
		}

		// Handle the case for NFS.
		if v.NFS != nil {
			volumes.NFS = append(volumes.NFS, bci.VolumeNFS{
				Name:     v.Name,
				Server:   v.NFS.Server,
				Path:     v.NFS.Path,
				ReadOnly: v.NFS.ReadOnly,
			})
			return nil
		}

		if v.CephFS != nil {
			secretInfo := &SecretInfo{
				VolumeName: v.Name + "-" + v.CephFS.SecretRef.Name,
				SecretName: v.CephFS.SecretRef.Name,
				Namespace:  pod.Namespace,
			}
			configFiles, err := p.parseSecretVolume(ctx, secretInfo)
			if err != nil {
				return err
			}

			if len(configFiles) != 0 {
				volumes.ConfigFile = append(volumes.ConfigFile, bci.VolumeConfigFile{
					Name:        v.Name + "-" + v.CephFS.SecretRef.Name,
					ConfigFiles: configFiles,
				})

				volumes.CephFS = append(volumes.CephFS, bci.VolumeCephFS{
					Name:      v.Name,
					Monitors:  v.CephFS.Monitors,
					Path:      v.CephFS.Path,
					User:      v.CephFS.User,
					SecretRef: v.CephFS.SecretRef,
					ReadOnly:  v.CephFS.ReadOnly,
				})
			}
			return nil
		}

		// Handle the case for ConfigMap volume.
		if v.ConfigMap != nil {
			configMapInfo := &ConfigMapInfo{
				VolumeName:    v.Name,
				ConfigMapName: v.ConfigMap.Name,
				Namespace:     pod.Namespace,
				Items:         v.ConfigMap.Items,
				Optional:      v.ConfigMap.Optional,
			}
			configFiles, err := p.parseConfigMapVolume(ctx, configMapInfo)
			if err != nil {
				return err
			}

			if len(configFiles) == 0 {
				log.G(ctx).Infof("%s configmap data is empty", v.Name)
			}
			volumes.ConfigFile = append(volumes.ConfigFile, bci.VolumeConfigFile{
				Name:        v.Name,
				ConfigFiles: configFiles,
				DefaultMode: v.ConfigMap.DefaultMode,
			})

			return nil
		}

		// Handle the case for Secret volume.
		if v.Secret != nil {
			secretInfo := &SecretInfo{
				VolumeName: v.Name,
				SecretName: v.Secret.SecretName,
				Namespace:  pod.Namespace,
				Items:      v.Secret.Items,
				Optional:   v.Secret.Optional,
			}
			configFiles, err := p.parseSecretVolume(ctx, secretInfo)
			if err != nil {
				return err
			}

			if len(configFiles) == 0 {
				log.G(ctx).Infof("%s secret data is empty", v.Name)
			}
			volumes.ConfigFile = append(volumes.ConfigFile, bci.VolumeConfigFile{
				Name:        v.Name,
				ConfigFiles: configFiles,
				DefaultMode: v.Secret.DefaultMode,
			})

			return nil
		}

		// Handle the case for PersistentVolumeClaim volume.
		if v.PersistentVolumeClaim != nil {
			pvc, err := p.resourceManager.GetPersistentVolumeClaim(v.PersistentVolumeClaim.ClaimName, pod.Namespace)
			if err != nil || pvc == nil {
				log.G(ctx).Errorf("fail to GetPersistentVolumeClaim %s with err=%v", v.Name, err)
				failedVolumes <- failedVolume{v, fmt.Errorf("get persistentVolumeClaim: %w", err)}
				return nil
			}
			if pvc.Status.Phase != v1.ClaimBound {
				return fmt.Errorf("pod %s volume %s refers to a non-bound pvc %s: %v",
					pod.Name, v.Name, pvc.Name, pvc.Status.Phase)
			}
			pv, err := p.resourceManager.GetPersistentVolume(pvc.Spec.VolumeName)
			if err != nil || pv == nil {
				return fmt.Errorf("fail to get binding PersistentVolume %s for PersistentVolumeClaim %s: %w", pvc.Spec.VolumeName, pvc.Name, err)
			}
			switch {
			case pv.Spec.NFS != nil:
				volumes.NFS = append(volumes.NFS, bci.VolumeNFS{
					Name:     v.Name,
					Server:   pv.Spec.NFS.Server,
					Path:     pv.Spec.NFS.Path,
					ReadOnly: pv.Spec.NFS.ReadOnly || v.PersistentVolumeClaim.ReadOnly,
				})

			case pv.Spec.CSI != nil:
				switch pv.Spec.CSI.Driver {
				case "csi-clusterfileplugin": // PFS PV
					server, path, err := p.parsePFSPV(ctx, pv)
					if err != nil {
						return fmt.Errorf("parse PFS PV %s: %w", pv.GetName(), err)
					}
					volumes.PFS = append(volumes.PFS, bci.VolumePFS{
						Name:   v.Name,
						Server: server,
						Path:   path,
					})
				case "csi-bosplugin": // BOS PV
					volumeBos, err := p.parseBOSPV(ctx, pv)
					if err != nil {
						return fmt.Errorf("parse BOS PV %s: %w", pv.GetName(), err)
					}
					volumes.BOS = append(volumes.BOS, bci.VolumeBOS{
						Name:      v.Name,
						Bucket:    volumeBos.Bucket,
						URL:       volumeBos.URL,
						AccessKey: volumeBos.AccessKey,
						SecretKey: volumeBos.SecretKey,
						OtherOpts: volumeBos.OtherOpts,
						ReadyOnly: volumeBos.ReadyOnly || v.PersistentVolumeClaim.ReadOnly,
					})
				default:
					return fmt.Errorf("pod %s/%s volume %s refers to an unsupported CSI PersistentVolume %s",
						pod.Namespace, pod.Name, v.Name, pv.Name)
				}

			default:
				return fmt.Errorf("pod %s/%s volume %s refers to an unsupported PersistentVolume %s",
					pod.Namespace, pod.Name, v.Name, pv.Name)
			}
			return nil
		}

		if v.DownwardAPI != nil {
			configFiles, err := p.parseDownwardAPI(ctx, pod, v.Name, v.DownwardAPI.Items, v.DownwardAPI.DefaultMode)
			if err != nil {
				return err
			}
			if len(configFiles) > 0 {
				volumes.ConfigFile = append(volumes.ConfigFile, bci.VolumeConfigFile{
					Name:        v.Name,
					ConfigFiles: configFiles,
				})
			}
			return nil
		}

		if v.FlexVolume != nil {
			if v.FlexVolume.Driver != "k8s/sidecar-stdout" {
				return fmt.Errorf("flexVolume driver %s is not supported", v.FlexVolume.Driver)
			}
			volumes.FlexVolume = append(volumes.FlexVolume, bci.VolumeFlexVolume{
				Name:   v.Name,
				Driver: v.FlexVolume.Driver,
			})
			return nil
		}

		if v.HostPath != nil {
			volumes.HostPath = append(volumes.HostPath, bci.VolumeHostPath{
				Name: v.Name,
				Path: v.HostPath.Path,
				Type: v.HostPath.Type,
			})
			return nil
		}

		// projected volume
		if v.Projected != nil && p.enableTokenCache {
			volumeConfigFile := bci.VolumeConfigFile{
				Name:        v.Name,
				DefaultMode: v.Projected.DefaultMode,
				ConfigFiles: make([]bci.ConfigFile, 0),
			}
			for _, ps := range v.Projected.Sources {
				// Secret
				if ps.Secret != nil {
					secretInfo := &SecretInfo{
						VolumeName: v.Name,
						SecretName: ps.Secret.Name,
						Namespace:  pod.Namespace,
						Items:      ps.Secret.Items,
						Optional:   ps.Secret.Optional,
					}
					configFiles, err := p.parseSecretVolume(ctx, secretInfo)
					if err != nil {
						return err
					}

					if len(configFiles) == 0 {
						log.G(ctx).Infof("%s project volume secret data is empty", ps.Secret.Name)
					}
					volumeConfigFile.ConfigFiles = append(volumeConfigFile.ConfigFiles, configFiles...)
				}
				// ConfigMap
				if ps.ConfigMap != nil {
					configMapInfo := &ConfigMapInfo{
						VolumeName:    v.Name,
						ConfigMapName: ps.ConfigMap.Name,
						Namespace:     pod.Namespace,
						Items:         ps.ConfigMap.Items,
						Optional:      ps.ConfigMap.Optional,
					}
					configFiles, err := p.parseConfigMapVolume(ctx, configMapInfo)
					if err != nil {
						return err
					}

					if len(configFiles) == 0 {
						log.G(ctx).Infof("%s project volume configmap data is empty", ps.ConfigMap.Name)
					}
					volumeConfigFile.ConfigFiles = append(volumeConfigFile.ConfigFiles, configFiles...)
				}
				// DownwardAPI
				if ps.DownwardAPI != nil {
					configFiles, err := p.parseDownwardAPI(ctx, pod, v.Name, ps.DownwardAPI.Items, v.Projected.DefaultMode)
					if err != nil {
						return err
					}
					if len(configFiles) > 0 {
						volumeConfigFile.ConfigFiles = append(volumeConfigFile.ConfigFiles, configFiles...)
					}
				}
				// ServiceAccountToken
				if ps.ServiceAccountToken != nil {
					configFiles, extra, err := p.ParseServiceAccountToken(ctx, pod, ps.ServiceAccountToken, v.Name)
					if err != nil {
						return err
					}
					if len(configFiles) > 0 {
						volumeConfigFile.ConfigFiles = append(volumeConfigFile.ConfigFiles, configFiles...)
					}
					if len(extra) > 0 {
						for k, val := range extra {
							tokenExtras[k] = val
						}
					}
				}
			}
			if len(volumeConfigFile.ConfigFiles) == 0 {
				log.G(ctx).Infof("%s Volumes.Projected.Sources is empty", v.Name)
			}
			volumes.ConfigFile = append(volumes.ConfigFile, volumeConfigFile)
			volumes.TokenExtras = tokenExtras
			return nil
		}

		// If we've made it this far we have found a volume type that isn't supported
		return fmt.Errorf("pod %s requires volume %s which is of an unsupported type", pod.Name, v.Name)
	}

	for _, v := range pod.Spec.Volumes {
		if err := tranlateVolume(v, bciVolumes); err != nil {
			return nil, err
		}
	}

	// limited retry on failed volumes
	if failedCnt := len(failedVolumes); failedCnt > 0 {
		maxRetry := 3
		for i := 0; i < maxRetry && failedCnt > 0; i++ {
			<-time.After(time.Second)
			log.G(ctx).Infof("%d.retry for %d failed volumes", i, failedCnt)
			for i := 0; i < failedCnt; i++ {
				if err := tranlateVolume((<-failedVolumes).v, bciVolumes); err != nil {
					return nil, err
				}
			}
			// update failedCnt to latest after each retry
			failedCnt = len(failedVolumes)
		}
		if failedCnt > 0 {
			failedVolumesList := make([]string, 0, failedCnt)
			for i := 0; i < failedCnt; i++ {
				failed := <-failedVolumes
				failedVolumesList = append(failedVolumesList, fmt.Sprintf("%s: %s", failed.v.Name, failed.err.Error()))
			}
			return nil, fmt.Errorf("fail to fetch related resource for volumes: [%s]", strings.Join(failedVolumesList, ", "))
		}
	}

	return bciVolumes, nil
}

// ConfigMapInfo Extract configmap info from volume.ConfigMap or projected.ConfigMap
type ConfigMapInfo struct {
	VolumeName    string
	ConfigMapName string
	Namespace     string
	Items         []v1.KeyToPath
	Optional      *bool
}

// parseConfigMapVolume parses configmap volume config.
func (p *BCIProvider) parseConfigMapVolume(ctx context.Context, configMapInfo *ConfigMapInfo) (configFiles []bci.ConfigFile, err error) {
	ctx, span := trace.StartSpan(ctx, "parseConfigMapVolume")
	defer span.End()

	configMap, err := p.resourceManager.GetConfigMap(configMapInfo.ConfigMapName, configMapInfo.Namespace)
	if err != nil || configMap == nil {
		client := p.resourceManager.GetRawClient()
		configMap, err = client.CoreV1().ConfigMaps(configMapInfo.Namespace).Get(ctx, configMapInfo.ConfigMapName, metav1.GetOptions{})
		if err != nil || configMap == nil {
			log.G(ctx).Errorf("fail to GetConfigMap volume %s configmap %s with err=%v", configMapInfo.VolumeName, configMapInfo.ConfigMapName, err)
			err = fmt.Errorf("fail to GetConfigMap volume %s configmap %s with err=%w", configMapInfo.VolumeName, configMapInfo.ConfigMapName, err)
			return
		}
	}
	return p.parseConfigMap2ConfigFiles(ctx, configMapInfo, configMap)
}

func (p *BCIProvider) parseConfigMap2ConfigFiles(ctx context.Context, configMapInfo *ConfigMapInfo,
	configMap *v1.ConfigMap) (configFiles []bci.ConfigFile, err error) {
	configMapKV := make(map[string]string)

	for k, v := range configMap.Data {
		configMapKV[k] = base64.StdEncoding.EncodeToString([]byte(v))
	}

	for k, v := range configMap.BinaryData {
		configMapKV[k] = base64.StdEncoding.EncodeToString(v)
	}

	optional := (configMapInfo.Optional != nil && *configMapInfo.Optional)
	configFiles, err = mapToItems(configMapKV, configMapInfo.Items, optional)
	if err != nil {
		log.G(ctx).Errorf("fail to map configMap volume %s configmap %s to configFiles: %w", configMapInfo.VolumeName, configMapInfo.ConfigMapName, err)
		err = fmt.Errorf("fail to map configMap volume %s configmap %s to configFiles: %w", configMapInfo.VolumeName, configMapInfo.ConfigMapName, err)
		return
	}
	return
}

// SecretInfo Extract secret info from volume.Secret or projected.Secret
type SecretInfo struct {
	VolumeName string
	SecretName string
	Namespace  string
	Items      []v1.KeyToPath
	Optional   *bool
}

// parseSecretVolume parses secret volume config.
func (p *BCIProvider) parseSecretVolume(ctx context.Context, secretInfo *SecretInfo) (configFiles []bci.ConfigFile, err error) {
	ctx, span := trace.StartSpan(ctx, "parseSecretVolume")
	defer span.End()

	secret, err := p.resourceManager.GetSecret(secretInfo.SecretName, secretInfo.Namespace)
	if err != nil || secret == nil {
		client := p.resourceManager.GetRawClient()
		secret, err = client.CoreV1().Secrets(secretInfo.Namespace).Get(ctx, secretInfo.SecretName, metav1.GetOptions{})
		if err != nil || secret == nil {
			log.G(ctx).Errorf("fail to GetSecret volume %s secret %s with err=%v", secretInfo.VolumeName, secretInfo.SecretName, err)
			err = fmt.Errorf("fail to GetSecret volume %s secret %s with err=%w", secretInfo.VolumeName, secretInfo.SecretName, err)
			return
		}
	}

	secretKV := make(map[string]string)
	for k, v := range secret.Data {
		secretKV[k] = base64.StdEncoding.EncodeToString(v)
	}

	for k, v := range secret.StringData {
		secretKV[k] = base64.StdEncoding.EncodeToString([]byte(v))
	}

	optional := (secretInfo.Optional != nil && !*secretInfo.Optional)
	configFiles, err = mapToItems(secretKV, secretInfo.Items, optional)
	if err != nil {
		log.G(ctx).Errorf("fail to map secret volume %s secret %s to items: %w", secretInfo.VolumeName, secretInfo.SecretName, err)
		err = fmt.Errorf("fail to map secret volume %s secret %s to items: %w", secretInfo.VolumeName, secretInfo.SecretName, err)
		return
	}
	return
}

func (p *BCIProvider) parseDownwardAPI(ctx context.Context, pod *v1.Pod, vName string, items []v1.DownwardAPIVolumeFile,
	mode *int32) (configFiles []bci.ConfigFile, err error) {
	ctx, span := trace.StartSpan(ctx, "parseDownwardAPI")
	defer span.End()

	files, err := collectData(ctx, items, pod, mode)
	if err != nil {
		log.G(ctx).Errorf("fail to collector data for volume %s: %w", vName, err)
		err = fmt.Errorf("fail to collector data for volume %s: %w", vName, err)
		return
	}

	for path, value := range files {
		configFiles = append(configFiles, bci.ConfigFile{
			Path: path,
			File: base64.StdEncoding.EncodeToString(value.Data),
		})
	}
	return
}

func (p *BCIProvider) ParseServiceAccountToken(ctx context.Context, pod *v1.Pod, serviceAccountToken *v1.ServiceAccountTokenProjection,
	volumeName string) (configFiles []bci.ConfigFile, extra map[string]string, err error) {
	ctx, span := trace.StartSpan(ctx, "parseServiceAccountToken")
	defer span.End()

	key := KeyFunc(pod, serviceAccountToken, volumeName)
	tr, err := p.tokenController.GetServiceAccountToken(ctx, pod, serviceAccountToken, key)
	if err != nil {
		log.G(ctx).Errorf("Fail to GetServiceAccountToken pod %s serviceAccountToken %s err %v", pod.Name, pod.Spec.ServiceAccountName, err)
		err = fmt.Errorf("fail to GetServiceAccountToken pod %s serviceAccountToken %s err %w", pod.Name, pod.Spec.ServiceAccountName, err)
		return
	}

	configFiles = append(configFiles, bci.ConfigFile{
		Path: serviceAccountToken.Path,
		File: base64.StdEncoding.EncodeToString([]byte(tr.Status.Token)),
	})

	// key: podNamespace/podName/volumeName/tokenName/ExpirationSeconds/podUID
	// value: tr.Status.ExpirationTimestamp
	expirationTimestampStr := strconv.FormatInt(tr.Status.ExpirationTimestamp.Unix(), 10)
	extra = make(map[string]string)
	extra[key] = expirationTimestampStr
	return
}

func (p *BCIProvider) parseBOSPV(ctx context.Context, pv *v1.PersistentVolume) (volumeBos *bci.VolumeBOS, err error) {
	ctx, span := trace.StartSpan(ctx, "parseBOSPV")
	defer span.End()
	if pv.Spec.CSI.VolumeHandle == "" {
		return nil, errors.New("volumeHandle cannot be empty")
	}
	// volumeHandle corresponds to the bucketName of BOS
	volumeBos = new(bci.VolumeBOS)
	volumeBos.Bucket = pv.Spec.CSI.VolumeHandle
	volumeBos.URL = fmt.Sprintf("%s.bcebos.com", p.region)
	volumeBos.ReadyOnly = pv.Spec.CSI.ReadOnly
	// get ak, sk
	if pv.Spec.CSI.NodePublishSecretRef != nil {
		secretRef, err := p.resourceManager.GetSecret(pv.Spec.CSI.NodePublishSecretRef.Name, pv.Spec.CSI.NodePublishSecretRef.Namespace)
		if err != nil {
			return nil, fmt.Errorf("fail to get secret reference of bos err %w", err)
		}
		secretKV := make(map[string]string)
		for k, v := range secretRef.Data {
			secretKV[k] = string(v)
		}
		volumeBos.AccessKey = secretKV["ak"]
		volumeBos.SecretKey = secretKV["sk"]
	}
	if len(pv.Spec.MountOptions) > 0 {
		for _, mo := range pv.Spec.MountOptions {
			volumeBos.OtherOpts = fmt.Sprintf("%v %v", volumeBos.OtherOpts, mo)
		}
	}

	return volumeBos, nil
}

// parsePFSPV parses pfs server and path from PV spec and PFS CSI config.
func (p *BCIProvider) parsePFSPV(ctx context.Context, pv *v1.PersistentVolume) (server, path string, err error) {
	ctx, span := trace.StartSpan(ctx, "parsePFSPV")
	defer span.End()

	if pv.Spec.CSI.VolumeHandle == "" {
		return server, path, errors.New("volumeHandle cannot be empty")
	}

	var parentDir, pfsCluster string

	getPath := func() error {
		if pv.Spec.CSI.VolumeHandle == "data-id" {
			// Static PFS PV uses parentDir as base.
			subPath := pv.Spec.CSI.VolumeAttributes["path"]
			if subPath == "" {
				return errors.New("path cannot be empty for static PV")
			}
			if parentDir != "" {
				path = filepath.Join(parentDir, subPath)
			}
		} else {
			// Dynamic provisioned PFS PV uses /{parentDir}/{pfsCluster} as base.
			if parentDir != "" && pfsCluster != "" {
				path = filepath.Join(parentDir, pfsCluster, pv.Spec.CSI.VolumeHandle)
			}
		}
		return nil
	}

	getServerFromScript := func(in string) (server string) {
		prefix := "curl -o /etc/cluster/client.conf "
		if start := strings.Index(in, prefix); start >= 0 {
			in = in[start+len(prefix):]            // http://*************[:port]/xx/xx ...
			in = strings.TrimPrefix(in, "http://") // *************[:port]/xx/xx ...
			if end := strings.Index(in, "/"); end > 0 {
				// *************[:port]
				server = in[:end]
				server = server[:strings.Index(server, ":")]
			}
		}
		return
	}

	// Find server for legacy pfs csi plugin.
	toolConfigMap, err := p.resourceManager.GetConfigMap("client-conf-tools", "kube-system")
	if err != nil {
		log.G(ctx).WithError(err).Warn("get pfs configmap failed")
		if !k8serrors.IsNotFound(err) {
			return server, path, fmt.Errorf("get pfs configmap: %w", err)
		}
	}
	log.G(ctx).Debug("try to get pfs config from configmap kube-system/client-conf-tools")

	if toolConfigMap != nil {
		if v, ok := toolConfigMap.GetAnnotations()["csi-clusterfileplugin/parent-dir"]; ok && v != "" {
			parentDir = v
		}
		if v, ok := toolConfigMap.GetAnnotations()["csi-clusterfileplugin/cluster"]; ok && v != "" {
			pfsCluster = v
		}
		if v, ok := toolConfigMap.GetAnnotations()["csi-clusterfileplugin/config-endpoint"]; ok && v != "" {
			ipPort := strings.TrimPrefix(v, "http://")
			if end := strings.Index(ipPort, ":"); end > 0 {
				server = ipPort[0:end]
			} else {
				server = ipPort
			}
		} else {
			if script, ok := toolConfigMap.Data["tools.sh"]; ok && script != "" {
				server = getServerFromScript(script)
			}
		}
	}
	if err = getPath(); err != nil {
		return
	}

	if server != "" && path != "" {
		return
	}

	// Find server and path from pfs csi provisioner pod.
	selector, err := labels.Parse("app in (cce-csi-pfs-plugin-provisioner,cce-roce-csi-pfs-plugin-provisioner)")
	if err != nil {
		return server, path, fmt.Errorf("parse label selector for pfs provisioner: %w", err)
	}
	provisionerPods, err := p.resourceManager.ListSystemPods(selector)
	if err != nil {
		return server, path, fmt.Errorf("get pfs provisioner pod: %w", err)
	}
	if len(provisionerPods) == 0 {
		return server, path, errors.New("pfs provisioner pod not found")
	}
	provisionerPod := provisionerPods[0]
	log.G(ctx).Debugf("try to get pfs config from pod %s/%s", provisionerPod.GetNamespace(), provisionerPod.GetName())
	if server == "" {
		for _, c := range provisionerPod.Spec.InitContainers {
			if c.Name != "cluster-conf" {
				continue
			}

			if len(c.Command) < 3 {
				continue
			}
			server = getServerFromScript(c.Command[2])
		}
	}
	for _, c := range provisionerPod.Spec.Containers {
		if c.Name != "cluster" {
			continue
		}
		for _, env := range c.Env {
			if env.Name == "PARENT_DIR" {
				parentDir = env.Value
			}
			if env.Name == "CLUSTER" {
				pfsCluster = env.Value
			}
		}
	}

	if err = getPath(); err != nil {
		return
	}
	if server != "" && path != "" {
		return
	}

	log.G(ctx).Errorf("parse pfs server and path failed: got server=%s, got path=%s", server, path)
	return server, path, errors.New("cannot get pfs server and path")
}

func getRestartPolicy(policy v1.RestartPolicy) bci.PodRestartPolicy {
	switch policy {
	case v1.RestartPolicyNever:
		return bci.RestartPolicyNever
	case v1.RestartPolicyOnFailure:
		return bci.RestartPolicyOnFailure
	default:
		return bci.RestartPolicyAlways
	}
}

// reasonAndMessageDelimiter is used to split Reason and Message within v1.ContainerState.Terminated
// to recognize the corresponding fields in DetailStatus in nova container status.
// It should be the same with the one defined in containermanager.
const reasonAndMessageDelimiter = ";;"

// DetailPodStatus is meant to carry the pod status detail within a container status
// as nova and console do not recognize these fields.
// The struct should be the same with the one defined in containermanager.
type DetailPodStatus struct {
	Phase      v1.PodPhase       `json:"phase,omitempty"`
	Conditions []v1.PodCondition `json:"conditions,omitempty"`
	Message    string            `json:"message,omitempty"`
	Reason     string            `json:"reason,omitempty"`

	ContainerDetailStatus string `json:"container_detail_status,omitempty"`
}

// bciPodDetailToPod 将bci.DescribePodResponse转换为*v1.Pod，包括容器状态、条件等信息。
// 返回值err表示转换过程中出现的错误，如果没有错误则err为nil。
func bciPodDetailToPod(ctx context.Context, bpod *bci.DescribePodResponse) (*v1.Pod, error) {
	var podCreationTimestamp metav1.Time
	if v := getBCILabelValue(bpod.Pod, CreationTimestampLabelKey); v != "" {
		unix, err := strconv.ParseInt(v, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("fail to parse pod CreationTimestamp %s: %w", v, err)
		}
		t := time.Unix(unix, 0)
		podCreationTimestamp = metav1.NewTime(t)
	}

	hidedContainersMap := make(map[string]struct{})
	if v := getBCILabelValue(bpod.Pod, HidedContainersLabelKey); v != "" {
		var hidedContainers []string
		if err := json.Unmarshal([]byte(v), &hidedContainers); err != nil {
			return nil, fmt.Errorf("failed to parse hided containers %s: %w", v, err)
		}
		for _, name := range hidedContainers {
			hidedContainersMap[name] = struct{}{}
		}
	}

	containers := make([]v1.Container, 0, len(bpod.Containers))
	var containerStatuses []v1.ContainerStatus
	var initContainerStatuses []v1.ContainerStatus
	containerStatusFound := false
	zoneNoResourceSpecification := false
	var detailPodStatus *DetailPodStatus
	for _, c := range bpod.Containers {
		if _, found := hidedContainersMap[c.Name]; found {
			continue
		}
		container := v1.Container{
			Name:    c.Name,
			Image:   bciContainerImageInfoToImage(c.ContainerImageInfo),
			Command: c.Commands,
			Args:    c.Args,
			Resources: v1.ResourceRequirements{
				Requests: v1.ResourceList{
					v1.ResourceCPU:    resource.MustParse(fmt.Sprintf("%.2f", c.CPUInCore)),
					v1.ResourceMemory: resource.MustParse(fmt.Sprintf("%.1fGi", c.MemoryInGB)),
				},
			},
		}

		containers = append(containers, container)

		containerStatus, podStatus, found, noResource := getContainerStatus(ctx, bpod, c)
		if podStatus != nil {
			detailPodStatus = podStatus
		}
		if found {
			containerStatusFound = true
		}
		if noResource {
			zoneNoResourceSpecification = true
		}

		// Add to containerStatuses
		switch c.ContainerType {
		case bci.ContainerTypeInit:
			initContainerStatuses = append(initContainerStatuses, containerStatus)
		case bci.ContainerTypeWorkload:
			containerStatuses = append(containerStatuses, containerStatus)
		default:
			containerStatuses = append(containerStatuses, containerStatus)
		}
	}

	podStartTime := metav1.NewTime(time.Time(bpod.CreatedTime))

	podPhase := bciPodStatusToPodPhase(bpod.Status, containerStatusFound, zoneNoResourceSpecification)
	var message, reason string
	if detailPodStatus != nil {
		podPhase = detailPodStatus.Phase
		message = detailPodStatus.Message
		reason = detailPodStatus.Reason
	}
	podConditions := bciPodStatusToConditions(bpod, podPhase)
	// TODO(yezichao): read conditions from detailPodStatus when available

	if len(bpod.Conditions) > 0 {
		podConditions = bpod.Conditions
	}

	// For pod terminated due to host crash, we do not respct non-terminated pod/container status.
	podHasTerminated := (podPhase == v1.PodSucceeded || podPhase == v1.PodFailed)
	if bpod.Status == bci.PodStatusCrashed && !podHasTerminated {
		podPhase = v1.PodFailed
		reason = "Crashed"
		message = "Host crashed"
		crashedAt := metav1.NewTime(time.Time(bpod.UpdatedTime))
		for i, cs := range containerStatuses {
			if cs.State.Terminated != nil {
				// terminated container is not affected
				continue
			}
			cs.Ready = false
			startedAt := crashedAt
			if cs.State.Running != nil {
				startedAt = cs.State.Running.StartedAt
			}
			cs.State = v1.ContainerState{
				Terminated: &v1.ContainerStateTerminated{
					ExitCode:   137, // treated crashed container as kill -9
					Reason:     "Crashed",
					Message:    "Host crashed",
					StartedAt:  startedAt,
					FinishedAt: crashedAt,
				},
			}
			containerStatuses[i] = cs
		}
	}

	// Set pod phase for recycled bidding instance.
	if bpod.Status == bci.PodStatusRecycled {
		podPhase = v1.PodSucceeded
		for _, cs := range containerStatuses {
			if cs.State.Terminated != nil && cs.State.Terminated.ExitCode != 0 {
				podPhase = v1.PodFailed
				break
			}
		}
	}

	// Setup Pod IPs for IPv6 support
	var podIPs []v1.PodIP
	if bpod.InternalIP != "" {
		podIPs = append(podIPs, v1.PodIP{IP: bpod.InternalIP})
	}

	// check if pod has IPv6 IP
	if bpod.Pod.InternalIPv6 != "" {
		podIPs = append(podIPs, v1.PodIP{IP: bpod.Pod.InternalIPv6})
	}

	p := v1.Pod{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Pod",
			APIVersion: "v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      getBCILabelValue(bpod.Pod, PodNameLabelKey),
			Namespace: getBCILabelValue(bpod.Pod, NamespaceLabelKey),
			// ClusterName:       bpod.CCEID,
			UID:               types.UID(getBCILabelValue(bpod.Pod, UIDLabelKey)),
			CreationTimestamp: podCreationTimestamp,
		},
		Spec: v1.PodSpec{
			NodeName:   getBCILabelValue(bpod.Pod, NodeNameLabelKey),
			Volumes:    []v1.Volume{}, // TODO should volumes and volumeMounts be set?
			Containers: containers,
		},
		Status: v1.PodStatus{
			Phase:                 podPhase,
			Conditions:            podConditions,
			Message:               message,
			Reason:                reason,
			HostIP:                "",
			PodIP:                 bpod.InternalIP,
			PodIPs:                podIPs,
			StartTime:             &podStartTime,
			ContainerStatuses:     containerStatuses,
			InitContainerStatuses: initContainerStatuses,
		},
	}

	return &p, nil
}

// getContainerStatus get container status and the embedded pod status if present.
// Whether remote status can be found (either current or previous status can be found) is also returned.
func getContainerStatus(ctx context.Context, bpod *bci.DescribePodResponse, c bci.Container) (v1.ContainerStatus, *DetailPodStatus, bool, bool) {
	ready := false
	containerStatusFound := false
	var curr, pre *bci.ContainerState
	var started *bool
	var detailPodStatus *DetailPodStatus
	if cs := c.Status; cs != nil {
		curr = cs.CurrentState
		pre = cs.PreviousState
		if curr != nil {
			containerStatusFound = true
			ready = (bciStateToPodPhase(cs.CurrentState.State) == v1.PodRunning)
			if strings.HasPrefix(curr.DetailStatus, "{") {
				// may be a json string of DetailStatus, try to unmarshal it
				detailPodStatus = &DetailPodStatus{}
				if err := json.Unmarshal([]byte(curr.DetailStatus), detailPodStatus); err == nil {
					curr.DetailStatus = detailPodStatus.ContainerDetailStatus
				}
			}
		}
		if pre != nil {
			containerStatusFound = true
		}

		if cs.Ready != nil {
			ready = *cs.Ready
		}
		started = cs.Started
	}

	podPhase := bciPodStatusToPodPhase(bpod.Status, containerStatusFound, false)
	containerState, noResource := bciContainerStateToContainerState(curr, podPhase)
	containerStatus := v1.ContainerStatus{
		Name:         c.Name,
		State:        containerState,
		Ready:        ready,
		Started:      started,
		RestartCount: c.Status.RestartCount,
		Image:        bciContainerImageInfoToImage(c.ContainerImageInfo),
		ImageID:      c.ImageID,
		ContainerID:  getContainerID(c.ContainerUUID, c.Name),
	}
	if pre != nil && isContainerStateTerminated(pre.State) {
		previousContainerState, _ := bciContainerStateToContainerState(c.Status.PreviousState, podPhase)
		containerStatus.LastTerminationState = previousContainerState
	}

	if curr == nil && pre == nil && bpod.Status == bci.PodStatusBidding {
		// Pod is in bidding, set waiting reason to bidding.
		containerStatus.State = v1.ContainerState{
			Waiting: &v1.ContainerStateWaiting{
				Reason: string(bci.PodStatusBidding),
			},
		}
	}

	return containerStatus, detailPodStatus, containerStatusFound, noResource
}

// bciContainerStateToContainerState 将bci.ContainerState转换为v1.ContainerState，并返回一个bool值表示是否没有资源（noResource）。如果容器状态不可用，则返回默认等待状态和false。
// 参数：
//
//	cs *bci.ContainerState - 需要转换的bci.ContainerState指针，如果为nil，则返回默认等待状态和false。
//	podStatus v1.PodPhase - 包含pod状态的v1.PodPhase类型，如果pod状态是成功或失败，则假定容器处于等待状态。
//
// 返回值：
//
//	v1.ContainerState - 转换后的v1.ContainerState结构体，包括运行中、终止和等待三种状态。
//	 bool - 一个布尔值，表示是否没有资源（noResource），如果容器状态不可用，则返回默认等待状态和false。
func bciContainerStateToContainerState(cs *bci.ContainerState, podStatus v1.PodPhase) (v1.ContainerState, bool) {
	defaultWaitingStatus := v1.ContainerState{
		Waiting: &v1.ContainerStateWaiting{},
	}
	if cs == nil {
		// state not available and pod state is not the final state(Succeeded or Failed), assume in waiting
		if podStatus != v1.PodSucceeded && podStatus != v1.PodFailed {
			defaultWaitingStatus.Waiting.Reason = string(bci.ContainerStateStringCreating)
		}
		return defaultWaitingStatus, false
	}

	startTime := metav1.NewTime(time.Time(cs.ContainerStartTime))

	// Handle the case where the container is running.
	if cs.State == bci.ContainerStateStringRunning {
		return v1.ContainerState{
			Running: &v1.ContainerStateRunning{
				StartedAt: startTime,
			},
		}, false
	}

	reason := cs.DetailStatus
	message := ""
	if i := strings.Index(cs.DetailStatus, reasonAndMessageDelimiter); i > 0 {
		message = cs.DetailStatus[i+2:]
		reason = cs.DetailStatus[:i]
	}

	// Handle the case where the container is terminated.
	if isContainerStateTerminated(cs.State) {
		return v1.ContainerState{
			Terminated: &v1.ContainerStateTerminated{
				ExitCode:   cs.ExitCode,
				Reason:     reason,
				Message:    message,
				StartedAt:  startTime,
				FinishedAt: metav1.NewTime(time.Time(cs.ContainerFinishTime)),
			},
		}, false
	}

	// Handle the case where the container is pending.
	// Pod state is not the final state(Succeeded or Failed)
	// Which should be all other bci states.
	noResource := false
	if reason == "" {
		if podStatus != v1.PodSucceeded && podStatus != v1.PodFailed {
			reason = string(bci.ContainerStateStringCreating)
		}
	} else if reason == "ZoneNoResourceSpecification" {
		noResource = true
	}

	return v1.ContainerState{
		Waiting: &v1.ContainerStateWaiting{
			Reason:  reason,
			Message: message,
		},
	}, noResource
}

func isContainerStateTerminated(cs bci.ContainerStateString) bool {
	return cs == bci.ContainerStateStringSucceeded || cs == bci.ContainerStateStringFailed
}

func bciStateToPodPhase(state bci.ContainerStateString) v1.PodPhase {
	switch state {
	case bci.ContainerStateStringPending, bci.ContainerStateStringCreating:
		return v1.PodPending
	case bci.ContainerStateStringRunning:
		return v1.PodRunning
	case bci.ContainerStateStringFailed:
		return v1.PodFailed
	case bci.ContainerStateStringSucceeded:
		return v1.PodSucceeded
	}
	return v1.PodUnknown
}

func bciContainerImageInfoToImage(info *bci.ContainerImageInfo) string {
	return fmt.Sprintf("%s:%s", info.ImageAddress, info.ImageVersion)
}

func getContainerID(containerID, containerName string) string {
	if containerID == "" {
		return ""
	}

	containerResourceID := fmt.Sprintf("%s/containers/%s", containerID, containerName)

	h := sha256.New()
	h.Write([]byte(strings.ToUpper(containerResourceID)))
	hashBytes := h.Sum(nil)
	return fmt.Sprintf("bci://%s", hex.EncodeToString(hashBytes))
}

// bciPodStatusToPodPhase 将bci.PodStatus转换为v1.PodPhase，containerStatusFound表示容器状态是否已经找到，zoneNoResourceSpecification表示区域没有资源指定。
// 返回值v1.PodPhase包括：Pending、Running、Succeeded、Failed和Unknown。
func bciPodStatusToPodPhase(status bci.PodStatus, containerStatusFound bool,
	zoneNoResourceSpecification bool) v1.PodPhase {
	switch status {
	case bci.PodStatusPending, bci.PodStatusBidding:
		return v1.PodPending
	case bci.PodStatusRunning:
		if containerStatusFound {
			return v1.PodRunning
		}
		return v1.PodPending
	case bci.PodStatusSucceeded:
		return v1.PodSucceeded
	case bci.PodStatusFailed:
		if zoneNoResourceSpecification {
			return v1.PodPending
		}
		return v1.PodFailed
	}
	return v1.PodUnknown
}

// bciPodStatusToConditions calculates pod conditions base on pod status, podPhase and container statuses.
// Only a basic implemetation when no conditions from BCI can be found.
func bciPodStatusToConditions(bpod *bci.DescribePodResponse, podPhase v1.PodPhase) []v1.PodCondition {
	all := map[v1.PodConditionType]v1.ConditionStatus{
		v1.PodReady:        v1.ConditionFalse,
		v1.ContainersReady: v1.ConditionFalse,
		v1.PodInitialized:  v1.ConditionFalse,
		v1.PodScheduled:    v1.ConditionTrue, // always be true
	}
	transitiontime := metav1.NewTime(bpod.CreatedTime)
	withInitContainer := false
	allContainersRunning := true
	for _, c := range bpod.Containers {
		if c.ContainerType == bci.ContainerTypeInit {
			withInitContainer = true
			continue
		}
		if c.Status != nil && c.Status.CurrentState != nil && c.Status.CurrentState.State == bci.ContainerStateStringRunning {
			continue
		}
		allContainersRunning = false
	}
	if !withInitContainer {
		all[v1.PodInitialized] = v1.ConditionTrue
	}

	switch bpod.Status {
	case bci.PodStatusSucceeded, bci.PodStatusFailed, bci.PodStatusRecycled, bci.PodStatusCrashed:
		all[v1.PodInitialized] = v1.ConditionTrue

	case bci.PodStatusRunning:
		if podPhase == v1.PodRunning {
			all[v1.PodReady] = v1.ConditionTrue
			if allContainersRunning {
				all[v1.ContainersReady] = v1.ConditionTrue
			}
			break
		}
		fallthrough

	case bci.PodStatusPending:
	}

	return []v1.PodCondition{
		{
			Type:               v1.PodInitialized,
			Status:             all[v1.PodInitialized],
			LastTransitionTime: transitiontime,
		},
		{
			Type:               v1.PodReady,
			Status:             all[v1.PodReady],
			LastTransitionTime: transitiontime,
		},
		{
			Type:               v1.ContainersReady,
			Status:             all[v1.ContainersReady],
			LastTransitionTime: transitiontime,
		},
		{
			Type:               v1.PodScheduled,
			Status:             all[v1.PodScheduled],
			LastTransitionTime: transitiontime,
		},
	}
}

func getBCILabelValue(bpod *bci.Pod, key string) string {
	for _, label := range bpod.Labels {
		if label.LabelKey == key {
			return label.LabelValue
		}
	}
	return ""
}

func wrapError(err error) error {
	if err == nil {
		return nil
	}

	e, ok := err.(*bcerror.Error)
	if !ok {
		return err
	}

	switch e.StatusCode {
	case http.StatusNotFound:
		return errdefs.AsNotFound(err)
	default:
		return err
	}
}

// GetResourceFieldRef implements ResourceFieldRefProvider in vk lib.
func (p *BCIProvider) GetResourceFieldRef(pod *v1.Pod, fs *v1.ResourceFieldSelector) (string, error) {
	fs = fs.DeepCopy()
	pod = pod.DeepCopy()
	return extractResourceValueByContainerName(fs, pod, fs.ContainerName)
}

// For downwardAPI volume.
type FileProjection struct {
	Data []byte
	Mode int32
}

// collectData collects requested downwardAPI in data map.
// Map's key is the requested name of file to dump
// Map's value is the (sorted) content of the field to be dumped in the file.
func collectData(ctx context.Context, items []v1.DownwardAPIVolumeFile, pod *v1.Pod, defaultMode *int32) (map[string]FileProjection, error) {
	if defaultMode == nil {
		var i int32 = 0644
		defaultMode = &i
	}

	var errs error
	data := make(map[string]FileProjection)
	for _, fileInfo := range items {
		var fileProjection FileProjection
		fPath := filepath.Clean(fileInfo.Path)
		if fileInfo.Mode != nil {
			fileProjection.Mode = *fileInfo.Mode
		} else {
			fileProjection.Mode = *defaultMode
		}
		if fileInfo.FieldRef != nil {
			if values, err := fieldpath.ExtractFieldPathAsString(pod, fileInfo.FieldRef.FieldPath); err != nil {
				log.G(ctx).WithError(err).Errorf("unable to extract field %s", fileInfo.FieldRef.FieldPath)
				errs = multierror.Append(errs, err)
			} else {
				fileProjection.Data = []byte(values)
			}
		} else if fileInfo.ResourceFieldRef != nil {
			containerName := fileInfo.ResourceFieldRef.ContainerName
			if values, err := extractResourceValueByContainerName(fileInfo.ResourceFieldRef, pod, containerName); err != nil {
				log.G(ctx).WithError(err).Errorf("unable to extract field %s", fileInfo.ResourceFieldRef.Resource)
				errs = multierror.Append(errs, err)
			} else {
				fileProjection.Data = []byte(values)
			}
		}

		data[fPath] = fileProjection
	}
	return data, errs
}

// extractResourceValueByContainerName extracts the value of a resource
// by providing container name
func extractResourceValueByContainerName(fs *v1.ResourceFieldSelector, pod *v1.Pod, containerName string) (string, error) {
	realContainer, err := findContainerInPod(pod, containerName)
	if err != nil {
		return "", err
	}
	container := realContainer.DeepCopy()

	// Use default resource requests if undefined.
	if container.Resources.Requests == nil {
		container.Resources.Requests = make(v1.ResourceList)
	}
	defaultResources := v1.ResourceList{
		v1.ResourceCPU:    resource.MustParse("1"),
		v1.ResourceMemory: resource.MustParse("2Gi"),
	}
	for resource, v := range defaultResources {
		if quantity, exists := container.Resources.Requests[resource]; !exists || quantity.IsZero() {
			container.Resources.Requests[resource] = v
		}
	}

	// For bci container resource limits always equal to resource requests.
	container.Resources.Limits = container.Resources.Requests

	return internalresource.ExtractContainerResourceValue(fs, container)
}

// findContainerInPod finds a container by its name in the provided pod
func findContainerInPod(pod *v1.Pod, containerName string) (*v1.Container, error) {
	for _, container := range pod.Spec.Containers {
		if container.Name == containerName {
			return &container, nil
		}
	}
	for _, container := range pod.Spec.InitContainers {
		if container.Name == containerName {
			return &container, nil
		}
	}
	return nil, fmt.Errorf("container %s not found", containerName)
}

// getLogCollections generates log collection configs from envs of container.
func getLogCollections(c v1.Container) ([]*bci.LogCollection, error) {
	all := make(map[int]*bci.LogCollection)
	for _, env := range c.Env {
		if !strings.HasPrefix(env.Name, "bls_task_") {
			continue
		}
		// ctxErrorf wraps error string with env context.
		ctxErrorf := func(format string, a ...any) error {
			return fmt.Errorf(fmt.Sprintf("illegal bls env %s for container %s: ", env.Name, c.Name)+format, a...)
		}
		// ${internal,stdout}_${index}_${property}
		parts := strings.Split(strings.TrimPrefix(env.Name, "bls_task_"), "_")
		if len(parts) != 3 {
			return nil, ctxErrorf("invalid env name")
		}
		logType := parts[0]
		indexStr := parts[1]
		property := parts[2]
		if logType != "internal" && logType != "stdout" {
			return nil, ctxErrorf("unknown log type '%s'", logType)
		}

		index, err := strconv.Atoi(indexStr)
		if err != nil {
			return nil, ctxErrorf("invalid index '%s': %w", indexStr, err)
		}
		if index <= 0 {
			return nil, ctxErrorf("invalid index '%s': must be greater than zero", indexStr)
		}
		if _, ok := all[index]; !ok {
			all[index] = &bci.LogCollection{
				SrcType: logType,
				Index:   index,
				DestConfig: bci.DestConfig{
					DestType: "BLS",
				},
			}
		}
		if all[index].SrcType != logType {
			return nil, ctxErrorf("multiple log types found for index %d: %s != %s", index, all[index].SrcType, logType)
		}

		switch property {
		case "name":
			all[index].Name = env.Value
		case "logStore":
			all[index].DestConfig.LogStore = env.Value
		case "ttl":
			if ttl, err := strconv.Atoi(env.Value); err != nil || ttl <= 0 {
				if err == nil {
					err = errors.New("must be greater than zero")
				}
				return nil, ctxErrorf("invalid ttl value %s: %w", env.Value, err)
			} else {
				all[index].SrcConfig.TTL = ttl
			}
		case "rateLimit":
			if rl, err := strconv.Atoi(env.Value); err != nil {
				return nil, ctxErrorf("invalid rateLimit value %s: %w", env.Value, err)
			} else {
				all[index].DestConfig.RateLimit = rl
			}
		case "matchedPattern":
			all[index].SrcConfig.MatchedPattern = env.Value
		case "srcDir":
			all[index].SrcConfig.SrcDir = env.Value

		default:
			return nil, ctxErrorf("unknown property %s", property)
		}

	}

	var collections []*bci.LogCollection
	for k, v := range all {
		if err := utils.Valid(v); err != nil {
			return nil, fmt.Errorf("invalid bls config index=%d for container %s: %w", k, c.Name, err)
		}
		collections = append(collections, v)
	}
	// Sort for test assertion.
	sort.Slice(collections, func(i, j int) bool {
		return collections[i].Index < collections[j].Index
	})

	return collections, nil
}

func AppendDsVolume(bciVolumes *bci.Volumes, dsVolumes *bci.Volumes) {
	if bciVolumes == nil {
		bciVolumes = new(bci.Volumes)
	}

	if dsVolumes != nil {
		if len(dsVolumes.EmptyDir) > 0 {
			bciVolumes.EmptyDir = append(bciVolumes.EmptyDir, dsVolumes.EmptyDir...)
		}
		if len(dsVolumes.ConfigFile) > 0 {
			bciVolumes.ConfigFile = append(bciVolumes.ConfigFile, dsVolumes.ConfigFile...)
		}
		if len(dsVolumes.HostPath) > 0 {
			bciVolumes.HostPath = append(bciVolumes.HostPath, dsVolumes.HostPath...)
		}
	}
	return
}

// equalMaps 比较两个 map 是否相等，支持任意类型的值
func equalMapString(a, b map[string]string) bool {
	if len(a) != len(b) {
		return false
	}

	for key, valueA := range a {
		valueB, exists := b[key]
		if !exists || valueA != valueB {
			return false
		}
	}

	return true
}

func equalMapBytes(a, b map[string][]byte) bool {
	if len(a) != len(b) {
		return false
	}

	for key, valueA := range a {
		valueB, exists := b[key]
		if !exists || string(valueA) != string(valueB) {
			return false
		}
	}

	return true
}
