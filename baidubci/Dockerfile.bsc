FROM registry.baidubce.com/cce-plugin-dev/sts-generate:dev

COPY docker/logrotate.cron /logrotate/logrotate.cron
COPY docker/logrotate.conf /logrotate/logrotate.conf
COPY docker/entrypoint.sh /usr/bin/entrypoint.sh
COPY stsgenerator.jar /stsgenerator.jar
COPY virtual-kubelet /usr/bin/virtual-kubelet
COPY config/cert  /etc/webhook-certs
RUN chmod +x /usr/bin/entrypoint.sh \
    && chmod +x /logrotate/logrotate.cron \
    && echo "*       *       *       *       *       /logrotate/logrotate.cron" >> /etc/crontabs/root \
    && mkdir -p /home/<USER>
ENTRYPOINT ["/usr/bin/entrypoint.sh" ]
CMD [ "--help" ]%