package baidubci

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"testing"
	"time"

	"code.cloudfoundry.org/clock"
	"code.cloudfoundry.org/clock/fakeclock"
	"github.com/golang/mock/gomock"
	"github.com/sirupsen/logrus"
	"github.com/virtual-kubelet/virtual-kubelet/log"
	logruslogger "github.com/virtual-kubelet/virtual-kubelet/log/logrus"
	"github.com/virtual-kubelet/virtual-kubelet/trace"
	"github.com/virtual-kubelet/virtual-kubelet/trace/opencensus"
	"gotest.tools/assert"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/manager"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/userstorage"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
	bciv2 "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci/v2"
)

func init() {
	logger := logrus.StandardLogger()
	log.L = logruslogger.FromLogrus(logrus.NewEntry(logger))
	trace.T = opencensus.Adapter{}
}

func generateBCIPod(name, cceID, podID string, labels []bci.PodLabel, createdAt time.Time) *bci.DescribePodResponse {
	return &bci.DescribePodResponse{
		Pod: &bci.Pod{
			Name:        name,
			PodID:       podID,
			VCPU:        1,
			MemoryInGB:  2,
			Labels:      labels,
			CCEID:       cceID,
			CreatedTime: createdAt,
			UpdatedTime: createdAt,
		},
		Containers: []bci.Container{
			{
				Name:               "container01",
				ContainerImageInfo: getContainerImageInfo("nginx"),
				CPUInCore:          1,
				MemoryInGB:         2,
				Status: &bci.ContainerStatus{
					CurrentState: &bci.ContainerState{
						State:              bci.ContainerStateStringRunning,
						ContainerStartTime: createdAt,
					},
				},
			},
		},
	}
}

func podMapToList(input map[string]*bci.DescribePodResponse) (output []*bci.Pod) {
	for _, p := range input {
		output = append(output, p.Pod)
	}
	return
}

func bpodsToV1Pods(input map[string]*bci.DescribePodResponse) (output []*v1.Pod) {
	for _, p := range input {
		v1p, err := bciPodDetailToPod(context.TODO(), p)
		if err != nil {
			panic(err)
		}
		output = append(output, v1p)
	}
	return
}

func TestTicker(t *testing.T) {
	now := time.Now()
	t.Log("now is", now)
	testClock := fakeclock.NewFakeClock(now)
	ticker := testClock.NewTicker(time.Second)
	defer ticker.Stop()
	timer := testClock.NewTimer(time.Second * 2)
	defer timer.Stop()

	done := make(chan struct{}, 20)

	go func() {
		for i := 0; i < 10; i++ {
			t.Log("wait ticker")
			now := <-ticker.C()
			t.Log("ticker:", now)
			done <- struct{}{}
		}
	}()
	go func() {
		t.Log("wait timer")
		now := <-timer.C()
		t.Log("timer:", now)
		done <- struct{}{}
	}()

	for i := 0; i < 10; i++ {
		t.Logf("i=%d", i)
		testClock.IncrementBySeconds(1)
		<-time.After(time.Millisecond)
	}

	for i := 0; i < 11; i++ {
		<-done
	}
}

func TestPodCacheForSingleUser(t *testing.T) {
	testClock := fakeclock.NewFakeClock(time.Now())
	clusterID := "cce-unittest"
	userStorage, _ := userstorage.NewEmptyStorage()

	provider := &BCIProvider{
		nodeName:    "bci-virtual-kubelet",
		userStorage: userStorage,
		clusterID:   clusterID,
	}
	podNamespace := "default"
	bpods := make(map[string]*bci.DescribePodResponse)
	podIDs := make(map[string]string)
	now := testClock.Now()
	for i := 0; i < 5; i++ {
		podName := fmt.Sprintf("pod-%d", i)
		podID := fmt.Sprintf("pod-%dpadding", i)
		labels := []bci.PodLabel{
			{LabelKey: UIDLabelKey, LabelValue: podName + "-uid"},
			{LabelKey: PodNameLabelKey, LabelValue: podName},
			{LabelKey: NamespaceLabelKey, LabelValue: podNamespace},
			{LabelKey: NodeNameLabelKey, LabelValue: provider.nodeName},
			{LabelKey: ClusterIDLabelKey, LabelValue: provider.clusterID},
			{LabelKey: CreationTimestampLabelKey, LabelValue: strconv.FormatInt(now.Unix(), 10)},
		}
		bpods[podID] = generateBCIPod(podNamespace+"-"+podName, clusterID, podID, labels, now)
		podIDs[podNamespace+"/"+podName] = podID
	}

	pc := &podCache{
		clock:               testClock,
		syncInterval:        time.Second,
		regularSyncInterval: time.Minute,
		pods:                make(map[string]*cachedPod),
		podIDs:              make(map[string]string),
		creatingPods:        make(map[string]*creatingPod),
		reqTokens:           make(chan struct{}, 128),
		provider:            provider,
		syncEnd:             make(chan struct{}, 1),
		notify:              func(*v1.Pod) {},
		subnetLongToShort:   make(map[string]string),
		subnetUsages:        make(map[string]*BCIResources),
		toNotFoundTimeout:   30 * time.Minute,
	}

	wantListOption := bci.NewMarkerListOption(clusterID, "", "", "", bci.DefaultMaxKeys, nil, bci.NewListKeyword(bci.KeywordTypeCCEID, clusterID))

	// var podID, podName, podUID string
	// var updatedPodNameAndUIDs [][]string
	// var wantPods []*v1.Pod

	ctx, cancel := context.WithCancel(context.TODO())
	defer cancel()

	type fields struct {
		ctrl *gomock.Controller

		setupProvider func(*BCIProvider)

		pre    func(*testing.T)
		assert func(*testing.T)
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
		{
			name: "start pod cache and then get all pods",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				return fields{
					ctrl: ctrl,
					setupProvider: func(p *BCIProvider) {
						bciClient := bci.NewMockClient(ctrl)
						bciV2Client := bciv2.NewMockClient(ctrl)
						rm := manager.NewMockResourceManager(ctrl)

						bciV2Client.EXPECT().ListPods(gomock.Any(), wantListOption, nil).Return(&bci.ListPodsResponse{
							MaxKeys:     bci.DefaultMaxKeys,
							IsTruncated: false,
							Result:      podMapToList(bpods),
						}, nil)
						for id, p := range bpods {
							bciClient.EXPECT().DescribePod(gomock.Any(), id, nil).Return(p, nil)
						}

						p.bciClient = bciClient
						p.bciV2Client = bciV2Client
						p.resourceManager = rm
					},
					pre: func(*testing.T) {
						err := pc.Start(ctx)
						if err != nil {
							t.Fatalf("podCache start fail err : %v", err)
						}
					},
					assert: func(t *testing.T) {
						v1Pods, err := pc.GetPods(ctx)
						assert.NilError(t, err, "podCache.GetPods")
						wantPods := bpodsToV1Pods(bpods)
						sort.Slice(v1Pods, func(i, j int) bool { return v1Pods[i].Name < v1Pods[j].Name })
						sort.Slice(wantPods, func(i, j int) bool { return wantPods[i].Name < wantPods[j].Name })
						assert.DeepEqual(t, v1Pods, wantPods)
						pod0 := v1Pods[0]
						gotPod0, err := pc.getUIDPod(ctx, pod0.GetNamespace(), pod0.GetName(), string(pod0.GetUID()))
						assert.NilError(t, err, "podCache.GetPod 0")
						assert.DeepEqual(t, gotPod0, pod0)
					},
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			if tt.fields.setupProvider != nil {
				tt.fields.setupProvider(pc.provider)
			}
			if tt.fields.pre != nil {
				tt.fields.pre(t)
			}

			tt.fields.assert(t)
		})
	}
}

func TestJitter(t *testing.T) {
	base := 5 * time.Minute
	for i := 0; i < 10; i++ {
		got := Jitter(base, 0.2)
		assert.Assert(t, got >= base && got <= 6*time.Minute)
	}

	got := Jitter(base, 0)
	assert.Equal(t, got, base)
}

func Test_podCache_toNotFoundChecker(t *testing.T) {
	type fields struct {
		ctrl *gomock.Controller

		pods              map[string]*cachedPod
		provider          *BCIProvider
		notify            func(*v1.Pod)
		toNotFoundTimeout time.Duration
		setupProvider     func(*BCIProvider)
	}
	ctx, cancel := context.WithCancel(context.TODO())
	defer cancel()

	testClock := fakeclock.NewFakeClock(time.Now())
	ns := "default"
	clusterID := "cce-xxxxxxxx"
	defaultPC := &podCache{
		clock: testClock,
	}

	tests := []struct {
		name   string
		fields fields
	}{
		// All test cases.
		{
			name: "pending pod and time out is not reached yet",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				rm := manager.NewMockResourceManager(ctrl)
				rm.EXPECT().GetPods().Return([]*v1.Pod{
					newTestV1Pod(ns, "test-0", "test-uid-0", "bci-vk", clusterID, testClock.Now().Add(-10*time.Minute).Unix(), bci.PodStatusPending),
					newTestV1Pod(ns, "test-1", "test-uid-1", "bci-vk", clusterID, testClock.Now().Add(-time.Minute).Unix(), bci.PodStatusPending),
				})

				return fields{
					ctrl: ctrl,
					provider: &BCIProvider{
						resourceManager: rm,
						nodeName:        "bci-vk",
					},
					toNotFoundTimeout: 30 * time.Minute,
				}
			}(),
		},
		{
			name: "pending pod and found in pod cache",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				rm := manager.NewMockResourceManager(ctrl)
				rm.EXPECT().GetPods().Return([]*v1.Pod{
					newTestV1Pod(ns, "test-0", "test-uid-0", "bci-vk", clusterID, testClock.Now().Add(-40*time.Minute).Unix(), bci.PodStatusPending),
					newTestV1Pod(ns, "test-1", "test-uid-1", "bci-vk", clusterID, testClock.Now().Add(-time.Minute).Unix(), bci.PodStatusPending),
				})
				pods := map[string]*cachedPod{
					defaultPC.getCacheKey(ns, "test-0", "test-uid-0"): {
						v1Pod: newTestV1Pod(ns, "test-0", "test-uid-0", "bci-vk", clusterID, testClock.Now().Add(-40*time.Minute).Unix(), bci.PodStatusPending),
					},
				}

				return fields{
					ctrl: ctrl,
					provider: &BCIProvider{
						resourceManager: rm,
						nodeName:        "bci-vk",
						clusterID:       clusterID,
					},
					pods:              pods,
					toNotFoundTimeout: 30 * time.Minute,
				}
			}(),
		},
		{
			name: "set pod status to not found",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				testPod := newTestV1Pod(ns, "test-0", "test-uid-0", "bci-vk", clusterID, testClock.Now().Add(-40*time.Minute).Unix(), bci.PodStatusPending)

				annotations := make(map[string]string)
				annotations["bci.virtual-kubelet.io/pod-id"] = "test-0"
				testPod.Annotations = annotations
				rm := manager.NewMockResourceManager(ctrl)
				rm.EXPECT().GetPods().Return([]*v1.Pod{
					testPod,
					newTestV1Pod(ns, "test-1", "test-uid-1", "bci-vk", clusterID, testClock.Now().Add(-time.Minute).Unix(), bci.PodStatusPending),
				})

				return fields{
					ctrl: ctrl,
					provider: &BCIProvider{
						resourceManager: rm,
						nodeName:        "bci-vk",
						clusterID:       clusterID,
						createV2:        true,
					},
					setupProvider: func(p *BCIProvider) {
						bciClient := bci.NewMockClient(ctrl)
						bciV2Client := bciv2.NewMockClient(ctrl)

						bciV2Client.EXPECT().DescribePod(gomock.Any(), "test-0", nil).Return(&bci.DescribePodResponse{}, nil)
						p.bciClient = bciClient
						p.bciV2Client = bciV2Client
						p.resourceManager = rm
					},
					notify: func(got *v1.Pod) {
						assert.DeepEqual(t, got, defaultPC.toNotFound(ctx, testPod))
					},
					toNotFoundTimeout: 30 * time.Minute,
				}
			}(),
		},
		{
			name: "set pod status to max retries exceeded",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				testPod := newTestV1Pod(ns, "test-0", "test-uid-0", "bci-vk", clusterID, testClock.Now().Add(-40*time.Minute).Unix(), bci.PodStatusPending)
				testPod.Status.Reason = podStatusReasonProviderFailed
				testPod.Status.Message = "some error occurs"

				rm := manager.NewMockResourceManager(ctrl)
				rm.EXPECT().GetPods().Return([]*v1.Pod{
					testPod,
					newTestV1Pod(ns, "test-1", "test-uid-1", "bci-vk", clusterID, testClock.Now().Add(-time.Minute).Unix(), bci.PodStatusPending),
				})

				return fields{
					ctrl: ctrl,
					provider: &BCIProvider{
						resourceManager: rm,
						nodeName:        "bci-vk",
					},
					notify: func(got *v1.Pod) {
						assert.DeepEqual(t, got, defaultPC.toMaxRetriesExceeded(ctx, testPod))
					},
					toNotFoundTimeout: 30 * time.Minute,
				}
			}(),
		},
		{
			name: "set pod status to not found",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				testPod := newTestV1Pod(ns, "test-0", "test-uid-0", "bci-vk", clusterID, testClock.Now().Add(-40*time.Minute).Unix(), bci.PodStatusPending)

				rm := manager.NewMockResourceManager(ctrl)
				rm.EXPECT().GetPods().Return([]*v1.Pod{
					testPod,
					newTestV1Pod(ns, "test-1", "test-uid-1", "bci-vk", clusterID, testClock.Now().Add(-time.Minute).Unix(), bci.PodStatusPending),
				})

				bpods := make(map[string]*bci.DescribePodResponse)
				return fields{
					ctrl: ctrl,
					provider: &BCIProvider{
						resourceManager: rm,
						nodeName:        "bci-vk",
						clusterID:       clusterID,
						createV2:        true,
					},
					setupProvider: func(p *BCIProvider) {
						bciClient := bci.NewMockClient(ctrl)
						bciV2Client := bciv2.NewMockClient(ctrl)

						bciV2Client.EXPECT().ListPods(gomock.Any(), gomock.Any(), nil).Return(&bci.ListPodsResponse{
							MaxKeys:     bci.DefaultMaxKeys,
							IsTruncated: false,
							Result:      podMapToList(bpods),
						}, nil)

						p.bciClient = bciClient
						p.bciV2Client = bciV2Client
						p.resourceManager = rm
					},
					notify: func(got *v1.Pod) {
						assert.DeepEqual(t, got, defaultPC.toNotFound(ctx, testPod))
					},
					toNotFoundTimeout: 30 * time.Minute,
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			pc := &podCache{
				clock:             testClock,
				pods:              tt.fields.pods,
				notify:            tt.fields.notify,
				provider:          tt.fields.provider,
				toNotFoundTimeout: tt.fields.toNotFoundTimeout,
			}
			if tt.fields.setupProvider != nil {
				tt.fields.setupProvider(pc.provider)
			}
			pc.toNotFoundChecker(ctx)
		})
	}
}

func Test_podCache_toNotFound(t *testing.T) {
	testClock := fakeclock.NewFakeClock(time.Now())
	testPod := &v1.Pod{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Pod",
			APIVersion: "v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:              "test-pod",
			Namespace:         "default",
			UID:               types.UID("test-uid"),
			CreationTimestamp: metav1.NewTime(testClock.Now()),
			//ClusterName:       "cce-xxxxxxxx",
		},
		Spec: v1.PodSpec{
			NodeName: "bci-vk",
			Volumes:  []v1.Volume{},
			Containers: []v1.Container{
				{
					Name:  "container-0",
					Image: "nginx:latest",
				},
			},
		},
		Status: v1.PodStatus{
			Phase: v1.PodPending,
			ContainerStatuses: []v1.ContainerStatus{
				{
					Name: "container-0",
					State: v1.ContainerState{
						Waiting: &v1.ContainerStateWaiting{
							Reason: "Creating",
						},
					},
				},
			},
		},
	}
	type fields struct {
		clock clock.Clock
	}
	type args struct {
		ctx    context.Context
		k8sPod *v1.Pod
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *v1.Pod
	}{
		// All test cases.
		{
			name: "pending pod case",
			fields: fields{
				clock: testClock,
			},
			args: args{
				ctx:    context.TODO(),
				k8sPod: testPod.DeepCopy(),
			},
			want: func() *v1.Pod {
				pod := testPod.DeepCopy()
				pod.Status.Phase = v1.PodFailed
				pod.Status.Reason = podStatusReasonNotFound
				pod.Status.Message = podStatusMessageNotFound
				pod.Status.ContainerStatuses[0] = v1.ContainerStatus{
					Name: "container-0",
					State: v1.ContainerState{
						Terminated: &v1.ContainerStateTerminated{
							ExitCode:   containerStatusExitCodeNotFound,
							Reason:     containerStatusReasonNotFound,
							Message:    containerStatusMessageNotFound,
							FinishedAt: metav1.NewTime(testClock.Now()),
						},
					},
				}

				return pod
			}(),
		},
		{
			name: "pending pod and bci is never seen case",
			fields: fields{
				clock: testClock,
			},
			args: args{
				ctx: context.TODO(),
				k8sPod: func() *v1.Pod {
					pod := testPod.DeepCopy()
					pod.Status.ContainerStatuses[0].State = v1.ContainerState{}
					return pod
				}(),
			},
			want: func() *v1.Pod {
				pod := testPod.DeepCopy()
				pod.Status.Phase = v1.PodFailed
				pod.Status.Reason = podStatusReasonNotFound
				pod.Status.Message = podStatusMessageNotFound
				pod.Status.ContainerStatuses[0].State = v1.ContainerState{}
				return pod
			}(),
		},
		{
			name: "running pod case",
			fields: fields{
				clock: testClock,
			},
			args: args{
				ctx: context.TODO(),
				k8sPod: func() *v1.Pod {
					pod := testPod.DeepCopy()
					pod.Status = v1.PodStatus{
						Phase: v1.PodRunning,
						ContainerStatuses: []v1.ContainerStatus{
							{
								Name: "container-0",
								State: v1.ContainerState{
									Running: &v1.ContainerStateRunning{
										StartedAt: metav1.NewTime(testClock.Now().Add(-15 * time.Minute)),
									},
								},
								Ready:       true,
								ContainerID: "test-container-id",
							},
						},
					}
					return pod
				}(),
			},
			want: func() *v1.Pod {
				pod := testPod.DeepCopy()
				pod.Status = v1.PodStatus{
					Phase:   v1.PodFailed,
					Message: podStatusMessageNotFound,
					Reason:  podStatusReasonNotFound,
					ContainerStatuses: []v1.ContainerStatus{
						{
							Name: "container-0",
							State: v1.ContainerState{
								Terminated: &v1.ContainerStateTerminated{
									ExitCode:    containerStatusExitCodeNotFound,
									Reason:      containerStatusReasonNotFound,
									Message:     containerStatusMessageNotFound,
									FinishedAt:  metav1.NewTime(testClock.Now()),
									StartedAt:   metav1.NewTime(testClock.Now().Add(-15 * time.Minute)),
									ContainerID: "test-container-id",
								},
							},
							ContainerID: "test-container-id",
						},
					},
				}
				return pod
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pc := &podCache{
				clock: tt.fields.clock,
			}
			got := pc.toNotFound(tt.args.ctx, tt.args.k8sPod)
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func Test_ensureTolerations(t *testing.T) {
	type args struct {
		tolerations []v1.Toleration
	}
	tests := []struct {
		name string
		args args
		want []v1.Toleration
	}{
		// TODO: Add test cases.
		{
			name: "not exist case",
			args: args{
				tolerations: []v1.Toleration{
					{
						Effect:   v1.TaintEffectNoSchedule,
						Key:      "virtual-kubelet.io/provider",
						Operator: v1.TolerationOpEqual,
						Value:    "baidu",
					},
					{
						Key:               v1.TaintNodeNotReady,
						Operator:          v1.TolerationOpExists,
						Effect:            v1.TaintEffectNoExecute,
						TolerationSeconds: func() *int64 { var i int64 = 300; return &i }(),
					},
					{
						Key:               v1.TaintNodeUnreachable,
						Operator:          v1.TolerationOpExists,
						Effect:            v1.TaintEffectNoExecute,
						TolerationSeconds: func() *int64 { var i int64 = 300; return &i }(),
					},
				},
			},
			want: []v1.Toleration{
				{
					Effect:   v1.TaintEffectNoSchedule,
					Key:      "virtual-kubelet.io/provider",
					Operator: v1.TolerationOpEqual,
					Value:    "baidu",
				},
				{
					Key:               v1.TaintNodeNotReady,
					Operator:          v1.TolerationOpExists,
					Effect:            v1.TaintEffectNoExecute,
					TolerationSeconds: func() *int64 { var i int64 = 300; return &i }(),
				},
				{
					Key:               v1.TaintNodeUnreachable,
					Operator:          v1.TolerationOpExists,
					Effect:            v1.TaintEffectNoExecute,
					TolerationSeconds: func() *int64 { var i int64 = 300; return &i }(),
				},
				TolerationForNodeNotReady,
				TolerationForNodeUnreachable,
			},
		},
		{
			name: "already exist case",
			args: args{
				tolerations: []v1.Toleration{
					{
						Effect:   v1.TaintEffectNoSchedule,
						Key:      "virtual-kubelet.io/provider",
						Operator: v1.TolerationOpEqual,
						Value:    "baidu",
					},
					{
						Key:               v1.TaintNodeNotReady,
						Operator:          v1.TolerationOpExists,
						Effect:            v1.TaintEffectNoExecute,
						TolerationSeconds: func() *int64 { var i int64 = 300; return &i }(),
					},
					{
						Key:               v1.TaintNodeUnreachable,
						Operator:          v1.TolerationOpExists,
						Effect:            v1.TaintEffectNoExecute,
						TolerationSeconds: func() *int64 { var i int64 = 300; return &i }(),
					},
					TolerationForNodeNotReady,
					TolerationForNodeUnreachable,
				},
			},
			want: []v1.Toleration{
				{
					Effect:   v1.TaintEffectNoSchedule,
					Key:      "virtual-kubelet.io/provider",
					Operator: v1.TolerationOpEqual,
					Value:    "baidu",
				},
				{
					Key:               v1.TaintNodeNotReady,
					Operator:          v1.TolerationOpExists,
					Effect:            v1.TaintEffectNoExecute,
					TolerationSeconds: func() *int64 { var i int64 = 300; return &i }(),
				},
				{
					Key:               v1.TaintNodeUnreachable,
					Operator:          v1.TolerationOpExists,
					Effect:            v1.TaintEffectNoExecute,
					TolerationSeconds: func() *int64 { var i int64 = 300; return &i }(),
				},
				TolerationForNodeNotReady,
				TolerationForNodeUnreachable,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ensureTolerations(tt.args.tolerations)
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func Test_ensureBCIAnnotations(t *testing.T) {
	type args struct {
		old  map[string]string
		bpod *bci.DescribePodResponse
	}
	tests := []struct {
		name string
		args args
		want map[string]string
	}{
		// All test cases.
		{
			name: "pending pod case",
			args: args{
				old: map[string]string{
					BCILogicalZoneAnnotationKey:     "zoneC",
					BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
					BCISubnetIDAnnotationKey:        "sbn-366vk0zh4v3d",
					BCIVPCIDAnnotationKey:           "",
					BCIVPCUUIDAnnotationKey:         "",
					OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
					PodIDAnnotationKey:              "p-mb6v9ipr",
				},
				bpod: &bci.DescribePodResponse{
					Pod: &bci.Pod{
						PodID:   "p-mb6v9ipr",
						PodUUID: "p-mb6v9ipr",
						OrderID: "1d48567396484052b87acd457366d37c",
						Status:  bci.PodStatusPending,
					},
				},
			},
			want: map[string]string{
				BCILogicalZoneAnnotationKey:     "zoneC",
				BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
				BCISubnetIDAnnotationKey:        "sbn-366vk0zh4v3d",
				BCIVPCIDAnnotationKey:           "",
				BCIVPCUUIDAnnotationKey:         "",
				OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
				PodIDAnnotationKey:              "p-mb6v9ipr",
			},
		},
		{
			name: "running pod case",
			args: args{
				old: map[string]string{
					BCILogicalZoneAnnotationKey:     "zoneC",
					BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
					BCISubnetIDAnnotationKey:        "sbn-366vk0zh4v3d",
					BCIVPCIDAnnotationKey:           "",
					BCIVPCUUIDAnnotationKey:         "",
					OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
					PodIDAnnotationKey:              "p-mb6v9ipr",
				},
				bpod: &bci.DescribePodResponse{
					Pod: &bci.Pod{
						PodID:   "p-mb6v9ipr",
						PodUUID: "67589edc-360c-4949-bf02-d176ad490bdc",
						OrderID: "1d48567396484052b87acd457366d37c",
						Status:  bci.PodStatusPending,
					},
				},
			},
			want: map[string]string{
				BCILogicalZoneAnnotationKey:     "zoneC",
				BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
				BCISubnetIDAnnotationKey:        "sbn-366vk0zh4v3d",
				BCIVPCIDAnnotationKey:           "",
				BCIVPCUUIDAnnotationKey:         "",
				OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
				PodIDAnnotationKey:              "p-mb6v9ipr",
				PodUUIDAnnotationKey:            "67589edc-360c-4949-bf02-d176ad490bdc",
			},
		},
		{
			name: "bind eip to pod case",
			args: args{
				old: map[string]string{
					BCILogicalZoneAnnotationKey:     "zoneC",
					BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
					BCISubnetIDAnnotationKey:        "sbn-366vk0zh4v3d",
					BCIVPCIDAnnotationKey:           "",
					BCIVPCUUIDAnnotationKey:         "",
					OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
					PodIDAnnotationKey:              "p-mb6v9ipr",
				},
				bpod: &bci.DescribePodResponse{
					Pod: &bci.Pod{
						PodID:   "p-mb6v9ipr",
						PodUUID: "67589edc-360c-4949-bf02-d176ad490bdc",
						OrderID: "1d48567396484052b87acd457366d37c",
						Status:  bci.PodStatusPending,
						BoundEIPConfig: bci.BoundEIPConfig{
							EIPID:           "ip-fe76a3e0",
							PublicIP:        "**************",
							BandwidthInMbps: 100,
							EIPRouteType:    "BGP",
							EIPPayMethod:    "ByTraffic",
						},
					},
				},
			},
			want: map[string]string{
				BCILogicalZoneAnnotationKey:       "zoneC",
				BCISecurityGroupIDAnnotationKey:   "g-nuk8yx6p4mmt",
				BCISubnetIDAnnotationKey:          "sbn-366vk0zh4v3d",
				BCIVPCIDAnnotationKey:             "",
				BCIVPCUUIDAnnotationKey:           "",
				OrderIDAnnotationKey:              "1d48567396484052b87acd457366d37c",
				PodIDAnnotationKey:                "p-mb6v9ipr",
				PodUUIDAnnotationKey:              "67589edc-360c-4949-bf02-d176ad490bdc",
				BCIBoundEIPIDAnnotationKey:        "ip-fe76a3e0",
				BCIBoundEIPAddressAnnotationKey:   "**************",
				BCIBoundEIPBandwidthAnnotationKey: "100",
				BCIBoundEIPRouteTypeAnnotationKey: "BGP",
				BCIBoundEIPPayMethodAnnotationKey: "ByTraffic",
			},
		},
		{
			name: "unbind eip to pod case",
			args: args{
				old: map[string]string{
					BCILogicalZoneAnnotationKey:       "zoneC",
					BCISecurityGroupIDAnnotationKey:   "g-nuk8yx6p4mmt",
					BCISubnetIDAnnotationKey:          "sbn-366vk0zh4v3d",
					BCIVPCIDAnnotationKey:             "",
					BCIVPCUUIDAnnotationKey:           "",
					OrderIDAnnotationKey:              "1d48567396484052b87acd457366d37c",
					PodIDAnnotationKey:                "p-mb6v9ipr",
					PodUUIDAnnotationKey:              "67589edc-360c-4949-bf02-d176ad490bdc",
					BCIBoundEIPIDAnnotationKey:        "ip-fe76a3e0",
					BCIBoundEIPAddressAnnotationKey:   "**************",
					BCIBoundEIPBandwidthAnnotationKey: "100",
					BCIBoundEIPRouteTypeAnnotationKey: "BGP",
					BCIBoundEIPPayMethodAnnotationKey: "ByTraffic",
				},
				bpod: &bci.DescribePodResponse{
					Pod: &bci.Pod{
						PodID:   "p-mb6v9ipr",
						PodUUID: "67589edc-360c-4949-bf02-d176ad490bdc",
						OrderID: "1d48567396484052b87acd457366d37c",
						Status:  bci.PodStatusPending,
					},
				},
			},
			want: map[string]string{
				BCILogicalZoneAnnotationKey:     "zoneC",
				BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
				BCISubnetIDAnnotationKey:        "sbn-366vk0zh4v3d",
				BCIVPCIDAnnotationKey:           "",
				BCIVPCUUIDAnnotationKey:         "",
				OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
				PodIDAnnotationKey:              "p-mb6v9ipr",
				PodUUIDAnnotationKey:            "67589edc-360c-4949-bf02-d176ad490bdc",
			},
		},
		{
			name: "nil subnet case",
			args: args{
				old: map[string]string{
					BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
					BCIVPCIDAnnotationKey:           "",
					BCIVPCUUIDAnnotationKey:         "",
					OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
					PodIDAnnotationKey:              "p-mb6v9ipr",
					PodUUIDAnnotationKey:            "67589edc-360c-4949-bf02-d176ad490bdc",
				},
				bpod: &bci.DescribePodResponse{
					Pod: &bci.Pod{
						PodID:   "p-mb6v9ipr",
						PodUUID: "67589edc-360c-4949-bf02-d176ad490bdc",
						OrderID: "1d48567396484052b87acd457366d37c",
						Status:  bci.PodStatusPending,
					},
				},
			},
			want: map[string]string{
				BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
				BCIVPCIDAnnotationKey:           "",
				BCIVPCUUIDAnnotationKey:         "",
				OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
				PodIDAnnotationKey:              "p-mb6v9ipr",
				PodUUIDAnnotationKey:            "67589edc-360c-4949-bf02-d176ad490bdc",
			},
		},
		{
			name: "new subnet case",
			args: args{
				old: map[string]string{
					BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
					BCIVPCIDAnnotationKey:           "",
					BCIVPCUUIDAnnotationKey:         "",
					OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
					PodIDAnnotationKey:              "p-mb6v9ipr",
					PodUUIDAnnotationKey:            "67589edc-360c-4949-bf02-d176ad490bdc",
				},
				bpod: &bci.DescribePodResponse{
					Pod: &bci.Pod{
						PodID:   "p-mb6v9ipr",
						PodUUID: "67589edc-360c-4949-bf02-d176ad490bdc",
						OrderID: "1d48567396484052b87acd457366d37c",
						Status:  bci.PodStatusPending,
					},
					Subnet: &bci.Subnet{
						Name:       "some",
						SubnetID:   "f3a97589-d037-49b9-8dd3-25468fd21dd9",
						ShortID:    "sbn-366vk0zh4v3d",
						SubnetUUID: "f3a97589-d037-49b9-8dd3-25468fd21dd9",
					},
				},
			},
			want: map[string]string{
				BCISubnetIDAnnotationKey:        "sbn-366vk0zh4v3d",
				BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
				BCIVPCIDAnnotationKey:           "",
				BCIVPCUUIDAnnotationKey:         "",
				OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
				PodIDAnnotationKey:              "p-mb6v9ipr",
				PodUUIDAnnotationKey:            "67589edc-360c-4949-bf02-d176ad490bdc",
			},
		},
		{
			name: "custom annotations exists case",
			args: args{
				old: map[string]string{
					BCILogicalZoneAnnotationKey:     "zoneC",
					BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
					BCISubnetIDAnnotationKey:        "sbn-366vk0zh4v3d",
					BCIVPCIDAnnotationKey:           "",
					BCIVPCUUIDAnnotationKey:         "",
					OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
					PodIDAnnotationKey:              "p-mb6v9ipr",
					"some-key":                      "some-value",
				},
				bpod: &bci.DescribePodResponse{
					Pod: &bci.Pod{
						PodID:   "p-mb6v9ipr",
						PodUUID: "67589edc-360c-4949-bf02-d176ad490bdc",
						OrderID: "1d48567396484052b87acd457366d37c",
						Status:  bci.PodStatusPending,
					},
				},
			},
			want: map[string]string{
				BCILogicalZoneAnnotationKey:     "zoneC",
				BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
				BCISubnetIDAnnotationKey:        "sbn-366vk0zh4v3d",
				BCIVPCIDAnnotationKey:           "",
				BCIVPCUUIDAnnotationKey:         "",
				OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
				PodIDAnnotationKey:              "p-mb6v9ipr",
				PodUUIDAnnotationKey:            "67589edc-360c-4949-bf02-d176ad490bdc",
				"some-key":                      "some-value",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ensureBCIAnnotations(tt.args.old, tt.args.bpod)
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func Test_podCache_toMaxRetriesExceeded(t *testing.T) {
	testClock := fakeclock.NewFakeClock(time.Now())
	errMsg := `Error Message: "get security group failed, securityGroupId g-cxcxcxcx", Error Code: "BadRequest", ` +
		`Status Code: 400, Request Id: "07db2f17-0d7a-414d-a48a-79aba7f07bd3"`
	testPod := &v1.Pod{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Pod",
			APIVersion: "v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:              "test-pod",
			Namespace:         "default",
			UID:               types.UID("test-uid"),
			CreationTimestamp: metav1.NewTime(testClock.Now()),
			//ClusterName:       "cce-xxxxxxxx",
		},
		Spec: v1.PodSpec{
			NodeName: "bci-vk",
			Volumes:  []v1.Volume{},
			Containers: []v1.Container{
				{
					Name:  "container-0",
					Image: "nginx:latest",
				},
			},
		},
		Status: v1.PodStatus{
			Phase:   v1.PodPending,
			Reason:  podStatusReasonProviderFailed,
			Message: errMsg,
		},
	}
	type fields struct {
		clock clock.Clock
	}
	type args struct {
		ctx    context.Context
		k8sPod *v1.Pod
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *v1.Pod
	}{
		// All test cases.
		{
			name: "pending pod case",
			fields: fields{
				clock: testClock,
			},
			args: args{
				ctx:    context.TODO(),
				k8sPod: testPod.DeepCopy(),
			},
			want: func() *v1.Pod {
				pod := testPod.DeepCopy()
				pod.Status.Phase = v1.PodFailed
				pod.Status.Reason = podStatusReasonMaxRetriesExceeded
				pod.Status.Message = errMsg

				return pod
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pc := &podCache{
				clock: tt.fields.clock,
			}
			got := pc.toMaxRetriesExceeded(tt.args.ctx, tt.args.k8sPod)
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func Test_GetPodDetail(t *testing.T) {
	type fields struct {
		ctrl      *gomock.Controller
		provider  *BCIProvider
		podDetail *bci.DescribePodResponse
	}
	pod := &v1.Pod{}
	pod.Name = "test-pod"
	pod.Namespace = "default"
	pod.UID = "test-uid"
	bciPodDetail := &bci.DescribePodResponse{
		Pod: &bci.Pod{
			Name:  "test-pod",
			PodID: "p-test",
		},
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "cann't find k8s pod",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				rm := manager.NewMockResourceManager(ctrl)
				rm.EXPECT().GetPod(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("k8s pod not found"))
				provider := &BCIProvider{
					resourceManager: rm,
				}
				return fields{
					ctrl:     ctrl,
					provider: provider,
				}
			}(),
			wantErr: true,
		},
		{
			name: "cann't find pod from podCache",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				rm := manager.NewMockResourceManager(ctrl)
				rm.EXPECT().GetPod(gomock.Any(), gomock.Any()).Return(pod, nil)
				provider := &BCIProvider{
					resourceManager: rm,
				}
				return fields{
					ctrl:     ctrl,
					provider: provider,
				}
			}(),
			wantErr: true,
		},
		{
			name: "normal case",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				rm := manager.NewMockResourceManager(ctrl)
				rm.EXPECT().GetPod(gomock.Any(), gomock.Any()).Return(pod, nil)
				provider := &BCIProvider{
					resourceManager: rm,
				}
				return fields{
					ctrl:      ctrl,
					provider:  provider,
					podDetail: bciPodDetail,
				}
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			pc := &podCache{
				provider: tt.fields.provider,
			}
			if tt.fields.podDetail != nil {
				cached := &cachedPod{
					podDetail: tt.fields.podDetail,
				}
				pc.pods = map[string]*cachedPod{
					"default/test-pod/test-uid": cached,
				}
			}
			_, err := pc.GetPodDetail(context.TODO(), "default", "test-pod")
			if (err != nil) != tt.wantErr {
				t.Errorf("BCIProvider.getDsInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestExtractUserUUIDFromNamespace(t *testing.T) {
	tests := []struct {
		name      string
		namespace string
		expected  string
	}{
		{
			name:      "BSC namespace with userUUID",
			namespace: "ns-de4a30cb55d9455ebfdce571c01fc293",
			expected:  "de4a30cb55d9455ebfdce571c01fc293",
		},
		{
			name:      "Regular namespace",
			namespace: "default",
			expected:  "",
		},
		{
			name:      "Empty namespace",
			namespace: "",
			expected:  "",
		},
		{
			name:      "Namespace with ns- prefix but no userUUID",
			namespace: "ns-",
			expected:  "",
		},
		{
			name:      "Another BSC namespace",
			namespace: "ns-a15fdd9dd5154845b32f7c74ae155ae3",
			expected:  "a15fdd9dd5154845b32f7c74ae155ae3",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := extractUserUUIDFromNamespace(tt.namespace)
			if result != tt.expected {
				t.Errorf("extractUserUUIDFromNamespace(%s) = %s, expected %s", tt.namespace, result, tt.expected)
			}
		})
	}
}
