package main

import (
	"context"
	"runtime"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/virtual-kubelet/virtual-kubelet/log"
	logruslogger "github.com/virtual-kubelet/virtual-kubelet/log/logrus"
	"github.com/virtual-kubelet/virtual-kubelet/trace"
	"github.com/virtual-kubelet/virtual-kubelet/trace/opencensus"

	bci "icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci"
	cli "icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli"
	logruscli "icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/logrus"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/opts"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/provider"
)

var (
	buildVersion = "N/A"
	buildTime    = "N/A"
	k8sVersion   = "v1.29.1" // This should follow the version of k8s.io/kubernetes we are importing
)

func main() {
	defer func() {
		log.G(context.Background()).Info("============================================================")
		if r := recover(); r != nil {
			buf := make([]byte, 1<<16)
			stackSize := runtime.Stack(buf, true)
			log.G(context.Background()).Errorf("Panic happened: %v\n", r)
			log.G(context.Background()).Errorf("Stack trace:\n%s", buf[:stackSize])
			time.Sleep(time.Second * 5)
		}
	}()

	ctx := cli.ContextWithCancelOnSignal(context.Background())

	logger := logrus.StandardLogger()
	log.L = logruslogger.FromLogrus(logrus.NewEntry(logger))
	logConfig := &logruscli.Config{LogLevel: "info"}
	trace.T = opencensus.Adapter{}

	o, err := opts.FromEnv(ctx)
	if err != nil {
		log.G(ctx).Fatal(err)
	}
	o.Provider = "baidu"
	o.Version = strings.Join([]string{k8sVersion, "vk-baidubci", buildVersion}, "-")

	node, err := cli.New(ctx,
		cli.WithBaseOpts(o),
		cli.WithCLIVersion(buildVersion, buildTime),
		cli.WithProvider("baidu", func(cfg provider.InitConfig) (provider.Provider, error) {
			return bci.NewBCIProvider(ctx, cfg)
		}),
		cli.WithPersistentFlags(logConfig.FlagSet()),
		cli.WithPersistentPreRunCallback(func() error {
			return logruscli.Configure(logConfig, logger)
		}),
	)

	if err != nil {
		log.G(ctx).Fatal(err)
	}

	if err := node.Run(); err != nil {
		log.G(ctx).Fatal(err)
	}
}
