#!/bin/sh

iptables-ensurer && nohup kube-proxy --bind-address=$MY_IP --cluster-cidr= --proxy-mode=iptables --masquerade-all=false --hostname-override=$MY_IP --kubeconfig=/conf/kube-proxy.conf --master= --healthz-bind-address=127.0.0.1 --healthz-port=10256 --logtostderr=true --v=6 --conntrack-tcp-timeout-established=0s --conntrack-tcp-timeout-close-wait=0s --conntrack-max-per-core=0 > /home/<USER>/kube-proxy-init.log 2>&1 &

successcount=0
retrycount=0
while true
do
  if [ $retrycount -eq 30 ]
    then
        echo "sidecar check failed: bci internal component kubeProxy init failed" >> /dev/termination-log
    exit 1
  else
    retrycount=`expr $retrycount + 1`
  fi
  if [ -z `which curl` ]
  then
    exit 0
  fi
  code=`curl ${HealthAddress}/healthz -I -m 10 -o /dev/null -s -w %{http_code}`
  if [ "$code" -eq 200 ]
  then
    if [ $successcount -ge 3 ]
    then
      echo "kubeproxy sidecar health check ok" >> /dev/termination-log
      if iptables -t nat -L | grep -q "KUBE-SERVICES"; then
        echo "KUBE-SERVICES entries found" >> /dev/termination-log
        exit 0
      else
        echo "KUBE-SERVICES entries not found" >> /dev/termination-log
        successcount=`expr $successcount + 1`
        sleep 1
      fi
    else
      successcount=`expr $successcount + 1`
      sleep 1
     fi
  else
    sleep 1
    echo "kubeproxy sidecar check code $code" >> /dev/termination-log
  fi
done
