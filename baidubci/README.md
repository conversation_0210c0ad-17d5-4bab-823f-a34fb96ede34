# 项目名称
简要说明

## 快速开始
如何构建、安装、运行

目录：cd baidu/bci2/virtual-kubelet/baidu-bci

编译二进制：
方式一：GOOS=linux GOARCH=amd64 CGO_ENABLED=0 make compile
方式二：
GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go mod tidy
GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -o ./virtual-kubelet.bin  -ldflags '-extldflags "-static"' ./cmd/virtual-kubelet

镜像：
docker build --platform=linux/amd64 --tag registry.baidubce.com/cce-plugin-dev/bci-virtual-kubelet:bciv2 .

## 本地镜像构建（用于自测）
sh ./local_build.sh
local_build.sh会自动构建镜像并推送到registry.baidubce.com/cce-plugin-dev/bci-virtual-kubelet仓库
镜像tag命名方式:"bci-$(date +"%Y%m%d-%H%M%S")"

## 正式镜像发布
走流水线

## 测试
如何执行自动化测试

## 如何贡献
贡献patch流程、质量要求

## 讨论
百度Hi讨论群：XXXX

## 链接
[百度golang代码库组织和引用指南](http://wiki.baidu.com/pages/viewpage.action?pageId=515622823)