// nolint
package v1

import (
	"reflect"
	"testing"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// TestDeepCopyInto 是用于测试 DeepCopyInto
// generated by Comate
func TestDeepCopyInto(t *testing.T) {
	testCases := []struct {
		name     string
		src      *Agent
		expected *Agent
	}{
		{
			name: "All fields set",
			src: &Agent{
				HostID:                  "host1",
				HostName:                "hostname1",
				IP:                      "***********",
				IsAvailableForNewConfig: true,
				Status:                  "active",
				UpdatedTime:             "2023-01-01T00:00:00Z",
			},
			expected: &Agent{
				HostID:                  "host1",
				HostName:                "hostname1",
				IP:                      "***********",
				IsAvailableForNewConfig: true,
				Status:                  "active",
				UpdatedTime:             "2023-01-01T00:00:00Z",
			},
		},
		{
			name: "Some fields set",
			src: &Agent{
				HostID:                  "host2",
				IP:                      "***********",
				IsAvailableForNewConfig: false,
				Status:                  "inactive",
				UpdatedTime:             "2023-01-02T00:00:00Z",
			},
			expected: &Agent{
				HostID:                  "host2",
				IP:                      "***********",
				IsAvailableForNewConfig: false,
				Status:                  "inactive",
				UpdatedTime:             "2023-01-02T00:00:00Z",
			},
		},
		{
			name: "No fields set",
			src:  &Agent{},
			expected: &Agent{
				HostID:                  "",
				HostName:                "",
				IP:                      "",
				IsAvailableForNewConfig: false,
				Status:                  "",
				UpdatedTime:             "",
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			out := &Agent{}
			tc.src.DeepCopyInto(out)
			if !reflect.DeepEqual(out, tc.expected) {
				t.Errorf("DeepCopyInto did not produce the expected result. Expected: %v, Got: %v", tc.expected, out)
			}
		})
	}
}

// TestDeepCopyAgent 是用于测试 DeepCopyAgent
// generated by Comate
func TestDeepCopyAgent(t *testing.T) {
	testCases := []struct {
		name string
		in   *Agent
	}{
		{
			name: "empty",
			in:   &Agent{},
		},
		{
			name: "with data",
			in: &Agent{
				HostID:      "host-1",
				HostName:    "host-1",
				IP:          "***********",
				Status:      "available",
				UpdatedTime: "2023-01-01T00:00:00Z",
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			out := tc.in.DeepCopy()
			if !reflect.DeepEqual(out, tc.in) {
				t.Errorf("DeepCopy did not produce a deep copy. Expected %v, got %v", tc.in, out)
			}
		})
	}
}

// TestDeepCopyDstConfig 是用于测试 DeepCopyDstConfig
// generated by Comate
func TestDeepCopyDstConfig(t *testing.T) {
	testCases := []struct {
		name string
		in   *DstConfig
	}{
		{
			name: "empty",
			in:   &DstConfig{},
		},
		{
			name: "with data",
			in: &DstConfig{
				LogStore:        "logstore-1",
				AccountID:       "account-1",
				Retention:       30,
				RateLimit:       10,
				BESClusterID:    "cluster-1",
				BESUser:         "user-1",
				BESPasswd:       "passwd-1",
				BESIndexPrefix:  "prefix-1",
				BESIndexRolling: "day",
				BESIsPwChange:   true,
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			out := tc.in.DeepCopy()
			if !reflect.DeepEqual(out, tc.in) {
				t.Errorf("DeepCopy did not produce a deep copy. Expected %v, got %v", tc.in, out)
			}
		})
	}
}

// TestDeepCopyKVPair 是用于测试 DeepCopyKVPair
// generated by Comate
func TestDeepCopyKVPair(t *testing.T) {
	testCases := []struct {
		name string
		in   *KVPair
	}{
		{
			name: "empty",
			in:   &KVPair{},
		},
		{
			name: "with data",
			in:   &KVPair{Key: "key-1", Value: "value-1"},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			out := tc.in.DeepCopy()
			if !reflect.DeepEqual(out, tc.in) {
				t.Errorf("DeepCopy did not produce a deep copy. Expected %v, got %v", tc.in, out)
			}
		})
	}
}

// TestDeepCopyLogConfigSpec 是用于测试 DeepCopyLogConfigSpec
// generated by Comate
func TestDeepCopyLogConfigSpec(t *testing.T) {
	testCases := []struct {
		name string
		in   *LogConfigSpec
	}{
		{
			name: "empty",
			in:   &LogConfigSpec{},
		},
		{
			name: "with data",
			in: &LogConfigSpec{
				SrcConfig: SrcConfig{
					SrcType: "journald",
					TTL:     3600,
				},
				DstConfig: DstConfig{
					LogStore: "logstore-1",
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			out := tc.in.DeepCopy()
			if !reflect.DeepEqual(out, tc.in) {
				t.Errorf("DeepCopy did not produce a deep copy. Expected %v, got %v", tc.in, out)
			}
		})
	}
}

// TestDeepCopyLogConfigList 是用于测试 DeepCopyLogConfigList
// generated by Comate
func TestDeepCopyLogConfigList(t *testing.T) {
	testCases := []struct {
		name string
		in   *LogConfigList
	}{
		{
			name: "empty",
			in:   &LogConfigList{},
		},
		{
			name: "with data",
			in: &LogConfigList{
				Items: []LogConfig{
					{
						Spec: LogConfigSpec{
							SrcConfig: SrcConfig{
								SrcType: "journald",
								TTL:     3600,
							},
							DstConfig: DstConfig{
								LogStore: "logstore-1",
							},
						},
					},
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			out := tc.in.DeepCopy()
			if !reflect.DeepEqual(out, tc.in) {
				t.Errorf("DeepCopy did not produce a deep copy. Expected %v, got %v", tc.in, out)
			}
		})
	}
}

// TestDeepCopyProcessConfig 是用于测试 DeepCopyProcessConfig
// generated by Comate
func TestDeepCopyProcessConfig(t *testing.T) {
	testCases := []struct {
		name string
		in   *ProcessConfig
	}{
		{
			name: "empty",
			in:   &ProcessConfig{},
		},
		{
			name: "with data",
			in: &ProcessConfig{
				Regex:            "regex-1",
				Separator:        "separator-1",
				Quote:            "quote-1",
				SampleLog:        "sampleLog-1",
				Keys:             "keys-1",
				DataType:         "string",
				DiscardOnFailure: true,
				KeepOriginal:     true,
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			out := tc.in.DeepCopy()
			if !reflect.DeepEqual(out, tc.in) {
				t.Errorf("DeepCopy did not produce a deep copy. Expected %v, got %v", tc.in, out)
			}
		})
	}
}

// TestDeepCopySrcConfig 是用于测试 DeepCopySrcConfig
// generated by Comate
func TestDeepCopySrcConfig(t *testing.T) {
	testCases := []struct {
		name string
		in   *SrcConfig
	}{
		{
			name: "empty",
			in:   &SrcConfig{},
		},
		{
			name: "with data",
			in: &SrcConfig{
				SrcType: "journald",
				TTL:     3600,
				MetaEnv: []string{"env-1", "env-2"},
				ProcessConfig: ProcessConfig{
					Regex:            "regex-1",
					Separator:        "separator-1",
					Quote:            "quote-1",
					SampleLog:        "sampleLog-1",
					Keys:             "keys-1",
					DataType:         "string",
					DiscardOnFailure: true,
					KeepOriginal:     true,
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			out := tc.in.DeepCopy()
			if !reflect.DeepEqual(out, tc.in) {
				t.Errorf("DeepCopy did not produce a deep copy. Expected %v, got %v", tc.in, out)
			}
		})
	}
}

// TestDeepCopyObject 是用于测试 DeepCopyObject
// generated by Comate
func TestDeepCopyObject(t *testing.T) {
	testLogConfig := &LogConfig{
		TypeMeta: metav1.TypeMeta{
			APIVersion: "v1",
			Kind:       "LogConfig",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: "test-log-config",
		},
		Spec: LogConfigSpec{
			SrcConfig: SrcConfig{
				SrcType: "journald",
			},
			DstConfig: DstConfig{
				DstType: "BOS",
			},
		},
		Status: LogConfigStatus{
			Phase:   "Running",
			Ready:   true,
			Message: "LogConfig is running",
			LastProbeTime: metav1.Time{
				Time: time.Now(),
			},
			LastTransitionTime: metav1.Time{
				Time: time.Now(),
			},
			TaskID: "task-123",
			Hosts: []Agent{
				{
					HostID:                  "host-1",
					HostName:                "host-1",
					IP:                      "***********",
					IsAvailableForNewConfig: true,
					Status:                  "Running",
					UpdatedTime:             "2023-01-01T00:00:00Z",
				},
			},
			LastApplyHostHash: "hash-123",
			LastApplyHash:     "hash-456",
			LastApplySpecHash: "spec-hash-789",
		},
	}

	deepCopiedLogConfig := testLogConfig.DeepCopyObject()

	if deepCopiedLogConfig == nil {
		t.Fatal("DeepCopyObject returned nil")
	}

	if deepCopiedLogConfig.(*LogConfig).TypeMeta.APIVersion != "v1" {
		t.Errorf("Expected APIVersion to be 'v1', got %s", deepCopiedLogConfig.(*LogConfig).TypeMeta.APIVersion)
	}

	if deepCopiedLogConfig.(*LogConfig).TypeMeta.Kind != "LogConfig" {
		t.Errorf("Expected Kind to be 'LogConfig', got %s", deepCopiedLogConfig.(*LogConfig).TypeMeta.Kind)
	}

	if deepCopiedLogConfig.(*LogConfig).ObjectMeta.Name != "test-log-config" {
		t.Errorf("Expected Name to be 'test-log-config', got %s", deepCopiedLogConfig.(*LogConfig).ObjectMeta.Name)
	}

	if deepCopiedLogConfig.(*LogConfig).Spec.SrcConfig.SrcType != "journald" {
		t.Errorf("Expected SrcType to be 'journald', got %s", deepCopiedLogConfig.(*LogConfig).Spec.SrcConfig.SrcType)
	}

	if deepCopiedLogConfig.(*LogConfig).Spec.DstConfig.DstType != "BOS" {
		t.Errorf("Expected DstType to be 'BOS', got %s", deepCopiedLogConfig.(*LogConfig).Spec.DstConfig.DstType)
	}

	if deepCopiedLogConfig.(*LogConfig).Status.Phase != "Running" {
		t.Errorf("Expected Phase to be 'Running', got %s", deepCopiedLogConfig.(*LogConfig).Status.Phase)
	}

	if !deepCopiedLogConfig.(*LogConfig).Status.Ready {
		t.Errorf("Expected Ready to be true, got %t", deepCopiedLogConfig.(*LogConfig).Status.Ready)
	}

	if deepCopiedLogConfig.(*LogConfig).Status.Message != "LogConfig is running" {
		t.Errorf("Expected Message to be 'LogConfig is running', got %s", deepCopiedLogConfig.(*LogConfig).Status.Message)
	}

	if deepCopiedLogConfig.(*LogConfig).Status.TaskID != "task-123" {
		t.Errorf("Expected TaskID to be 'task-123', got %s", deepCopiedLogConfig.(*LogConfig).Status.TaskID)
	}

	if len(deepCopiedLogConfig.(*LogConfig).Status.Hosts) != 1 {
		t.Errorf("Expected Hosts to have 1 element, got %d", len(deepCopiedLogConfig.(*LogConfig).Status.Hosts))
	}

	if deepCopiedLogConfig.(*LogConfig).Status.Hosts[0].HostID != "host-1" {
		t.Errorf("Expected HostID to be 'host-1', got %s", deepCopiedLogConfig.(*LogConfig).Status.Hosts[0].HostID)
	}

	if deepCopiedLogConfig.(*LogConfig).Status.LastApplyHostHash != "hash-123" {
		t.Errorf("Expected LastApplyHostHash to be 'hash-123', got %s", deepCopiedLogConfig.(*LogConfig).Status.LastApplyHostHash)
	}

	if deepCopiedLogConfig.(*LogConfig).Status.LastApplyHash != "hash-456" {
		t.Errorf("Expected LastApplyHash to be 'hash-456', got %s", deepCopiedLogConfig.(*LogConfig).Status.LastApplyHash)
	}

	if deepCopiedLogConfig.(*LogConfig).Status.LastApplySpecHash != "spec-hash-789" {
		t.Errorf("Expected LastApplySpecHash to be 'spec-hash-789', got %s", deepCopiedLogConfig.(*LogConfig).Status.LastApplySpecHash)
	}
}

// TestDeepCopyLogConfigStatus 是用于测试 DeepCopyLogConfigStatus
// generated by Comate
func TestDeepCopyLogConfigStatus(t *testing.T) {
	testCases := []struct {
		name string
		in   *LogConfigStatus
	}{
		{
			name: "empty",
			in:   &LogConfigStatus{},
		},
		{
			name: "with data",
			in: &LogConfigStatus{
				Phase:              LogConfigPhase("logTime"),
				Ready:              true,
				Message:            "test message",
				LastProbeTime:      metav1.NewTime(time.Now()),
				LastTransitionTime: metav1.NewTime(time.Now()),
				TaskID:             "task-123",
				Hosts: []Agent{
					{
						HostID:      "host-1",
						HostName:    "host-1",
						IP:          "***********",
						Status:      "available",
						UpdatedTime: "2023-01-01T00:00:00Z",
					},
				},
				LastApplyHostHash: "hash-1",
				LastApplyHash:     "hash-2",
				LastApplySpecHash: "hash-3",
			},
		},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			out := tc.in.DeepCopy()
			if !reflect.DeepEqual(out, tc.in) {
				t.Errorf("DeepCopy did not produce a deep copy. Expected %v, got %v", tc.in, out)
			}
		})
	}
}

// TestDeepCopyObject1 是用于测试 DeepCopyObject
// generated by Comate
func TestDeepCopyObject1(t *testing.T) {
	testLogConfigList := &LogConfigList{
		TypeMeta: metav1.TypeMeta{
			Kind:       "LogConfigList",
			APIVersion: "v1",
		},
		ListMeta: metav1.ListMeta{
			ResourceVersion: "1",
		},
		Items: []LogConfig{
			{
				ObjectMeta: metav1.ObjectMeta{
					Name: "test-logconfig",
				},
				Spec: LogConfigSpec{
					SrcConfig: SrcConfig{
						SrcType: "journald",
					},
					DstConfig: DstConfig{
						DstType: "BOS",
					},
				},
				Status: LogConfigStatus{
					Phase: "Running",
					Ready: true,
				},
			},
		},
	}

	copiedLogConfigList := testLogConfigList.DeepCopyObject()

	if copiedLogConfigList == nil {
		t.Fatal("DeepCopyObject returned nil")
	}

	if copiedLogConfigList.(*LogConfigList).TypeMeta.Kind != "LogConfigList" {
		t.Errorf("Expected Kind to be 'LogConfigList', got %v", copiedLogConfigList.(*LogConfigList).TypeMeta.Kind)
	}

	if copiedLogConfigList.(*LogConfigList).TypeMeta.APIVersion != "v1" {
		t.Errorf("Expected APIVersion to be 'v1', got %v", copiedLogConfigList.(*LogConfigList).TypeMeta.APIVersion)
	}

	if copiedLogConfigList.(*LogConfigList).ListMeta.ResourceVersion != "1" {
		t.Errorf("Expected ResourceVersion to be '1', got %v", copiedLogConfigList.(*LogConfigList).ListMeta.ResourceVersion)
	}

	if len(copiedLogConfigList.(*LogConfigList).Items) != 1 {
		t.Errorf("Expected 1 item in Items, got %v", len(copiedLogConfigList.(*LogConfigList).Items))
	}

	if copiedLogConfigList.(*LogConfigList).Items[0].Spec.SrcConfig.SrcType != "journald" {
		t.Errorf("Expected SrcType to be 'journald', got %v", copiedLogConfigList.(*LogConfigList).Items[0].Spec.SrcConfig.SrcType)
	}

	if copiedLogConfigList.(*LogConfigList).Items[0].Spec.DstConfig.DstType != "BOS" {
		t.Errorf("Expected DstType to be 'BOS', got %v", copiedLogConfigList.(*LogConfigList).Items[0].Spec.DstConfig.DstType)
	}

	if copiedLogConfigList.(*LogConfigList).Items[0].Status.Phase != "Running" {
		t.Errorf("Expected Phase to be 'Running', got %v", copiedLogConfigList.(*LogConfigList).Items[0].Status.Phase)
	}

	if !copiedLogConfigList.(*LogConfigList).Items[0].Status.Ready {
		t.Errorf("Expected Ready to be true, got %v", copiedLogConfigList.(*LogConfigList).Items[0].Status.Ready)
	}
}
