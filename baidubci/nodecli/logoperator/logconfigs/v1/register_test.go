// nolint
package v1

import (
	"testing"

	"k8s.io/apimachinery/pkg/runtime/schema"
)

// TestKind 是用于测试 Kind
// generated by Comate
func TestKind(t *testing.T) {
	testCases := []struct {
		name     string
		kind     string
		expected schema.GroupKind
	}{
		{
			name:     "Test Kind with valid kind",
			kind:     "Service",
			expected: schema.GroupKind{Group: GroupName, Kind: "Service"},
		},
		{
			name:     "Test Kind with invalid kind",
			kind:     "InvalidKind",
			expected: schema.GroupKind{Group: GroupName, Kind: "InvalidKind"},
		},
		{
			name:     "Test Kind with empty kind",
			kind:     "",
			expected: schema.GroupKind{Group: GroupName, Kind: ""},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := Kind(tc.kind)
			if result != tc.expected {
				t.Errorf("Expected %v, but got %v", tc.expected, result)
			}
		})
	}
}

// TestResource 是用于测试 Resource
// generated by Comate
func TestResource(t *testing.T) {
	testCases := []struct {
		name     string
		resource string
		expected schema.GroupResource
	}{
		{
			name:     "Valid Resource",
			resource: "pods",
			expected: schema.GroupResource{
				Group:    GroupName,
				Resource: "pods",
			},
		},
		{
			name:     "Empty Resource",
			resource: "",
			expected: schema.GroupResource{
				Group:    GroupName,
				Resource: "",
			},
		},
		{
			name:     "Special Characters",
			resource: "@!#$%^&*()",
			expected: schema.GroupResource{
				Group:    GroupName,
				Resource: "@!#$%^&*()",
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := Resource(tc.resource)
			if result.Group != tc.expected.Group || result.Resource != tc.expected.Resource {
				t.Errorf("Expected %v, got %v", tc.expected, result)
			}
		})
	}
}
