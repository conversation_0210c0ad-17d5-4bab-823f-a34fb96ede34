// nolint
package logoperator

import (
	"fmt"
	"reflect"
	"sync"
	"testing"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/dynamic/fake"

	logconfigv1 "icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/logoperator/logconfigs/v1"
)

// TestIsValidLogConfig 是用于测试 IsValidLogConfig
// generated by Comate
func TestIsValidLogConfig(t *testing.T) {
	testCases := []struct {
		name      string
		logConfig *logconfigv1.LogConfig
		expected  bool
	}{
		{
			name: "ValidConfig",
			logConfig: &logconfigv1.LogConfig{
				Spec: logconfigv1.LogConfigSpec{
					SrcConfig: logconfigv1.SrcConfig{
						SrcType: "container",
						LogType: "stdout",
					},
					DstConfig: logconfigv1.DstConfig{
						DstType:  "BLS",
						LogStore: "test-store",
					},
				},
				Status: logconfigv1.LogConfigStatus{
					Phase:  "Succeeded",
					TaskID: "test-task-id",
				},
			},
			expected: true,
		},
		{
			name: "InvalidSrcType",
			logConfig: &logconfigv1.LogConfig{
				Spec: logconfigv1.LogConfigSpec{
					SrcConfig: logconfigv1.SrcConfig{
						SrcType: "non-container",
						LogType: "stdout",
					},
					DstConfig: logconfigv1.DstConfig{
						DstType:  "BLS",
						LogStore: "test-store",
					},
				},
				Status: logconfigv1.LogConfigStatus{
					Phase:  "Succeeded",
					TaskID: "test-task-id",
				},
			},
			expected: false,
		},
		{
			name: "InvalidLogType",
			logConfig: &logconfigv1.LogConfig{
				Spec: logconfigv1.LogConfigSpec{
					SrcConfig: logconfigv1.SrcConfig{
						SrcType: "container",
						LogType: "invalid",
					},
					DstConfig: logconfigv1.DstConfig{
						DstType:  "BLS",
						LogStore: "test-store",
					},
				},
				Status: logconfigv1.LogConfigStatus{
					Phase:  "Succeeded",
					TaskID: "test-task-id",
				},
			},
			expected: false,
		},
		{
			name: "InvalidDstType",
			logConfig: &logconfigv1.LogConfig{
				Spec: logconfigv1.LogConfigSpec{
					SrcConfig: logconfigv1.SrcConfig{
						SrcType: "container",
						LogType: "stdout",
					},
					DstConfig: logconfigv1.DstConfig{
						DstType:  "non-BLS",
						LogStore: "test-store",
					},
				},
				Status: logconfigv1.LogConfigStatus{
					Phase:  "Succeeded",
					TaskID: "test-task-id",
				},
			},
			expected: false,
		},
		{
			name: "EmptyLogStore",
			logConfig: &logconfigv1.LogConfig{
				Spec: logconfigv1.LogConfigSpec{
					SrcConfig: logconfigv1.SrcConfig{
						SrcType: "container",
						LogType: "stdout",
					},
					DstConfig: logconfigv1.DstConfig{
						DstType:  "BLS",
						LogStore: "",
					},
				},
				Status: logconfigv1.LogConfigStatus{
					Phase:  "Succeeded",
					TaskID: "test-task-id",
				},
			},
			expected: false,
		},
		{
			name: "InvalidPhase",
			logConfig: &logconfigv1.LogConfig{
				Spec: logconfigv1.LogConfigSpec{
					SrcConfig: logconfigv1.SrcConfig{
						SrcType: "container",
						LogType: "stdout",
					},
					DstConfig: logconfigv1.DstConfig{
						DstType:  "BLS",
						LogStore: "test-store",
					},
				},
				Status: logconfigv1.LogConfigStatus{
					Phase:  "Failed",
					TaskID: "test-task-id",
				},
			},
			expected: false,
		},
		{
			name: "EmptyTaskID",
			logConfig: &logconfigv1.LogConfig{
				Spec: logconfigv1.LogConfigSpec{
					SrcConfig: logconfigv1.SrcConfig{
						SrcType: "container",
						LogType: "stdout",
					},
					DstConfig: logconfigv1.DstConfig{
						DstType:  "BLS",
						LogStore: "test-store",
					},
				},
				Status: logconfigv1.LogConfigStatus{
					Phase:  "Succeeded",
					TaskID: "",
				},
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := isValidLogConfig(tc.logConfig)
			if result != tc.expected {
				t.Errorf("Expected %v, but got %v", tc.expected, result)
			}
		})
	}
}

// TestLogConfigController_Stop 是用于测试 LogConfigController_Stop
// generated by Comate
func TestLogConfigController_Stop(t *testing.T) {
	// 创建一个 LogConfigController 实例
	controller := &LogConfigController{
		LogConfigs: make(map[string]*logconfigv1.LogConfig),
		stopCh:     make(chan struct{}),
	}

	// 启动一个 goroutine 来检测 stopCh 是否被关闭
	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		// 等待 stopCh 被关闭
		<-controller.stopCh
	}()

	// 调用 Stop 方法
	controller.Stop()

	// 等待 goroutine 结束
	wg.Wait()
}

// TestNewLogConfigController 是用于测试 NewLogConfigController
// generated by Comate
func TestNewLogConfigController(t *testing.T) {
	scheme := runtime.NewScheme()
	logconfigv1.AddToScheme(scheme)
	dynamicClient := fake.NewSimpleDynamicClient(scheme)
	controller := NewLogConfigController(dynamicClient)
	if controller == nil {
		t.Fatal("NewLogConfigController should not return nil")
	}
	if controller.stopCh == nil {
		t.Fatal("stopCh should not be nil")
	}
	if len(controller.LogConfigs) != 0 {
		t.Fatalf("LogConfigs should be empty, but got: %v", controller.LogConfigs)
	}
	close(controller.stopCh)
}

// TestLogConfigController_handleDelete 是用于测试 LogConfigController_handleDelete
// generated by Comate
func TestLogConfigController_handleDelete(t *testing.T) {
	// 创建一个 LogConfigController 实例
	controller := &LogConfigController{
		LogConfigs: make(map[string]*logconfigv1.LogConfig),
		stopCh:     make(chan struct{}),
	}

	// 添加一个 LogConfig 到控制器
	namespace := "default"
	name := "test-logconfig"
	key := fmt.Sprintf("%s/%s", namespace, name)
	controller.LogConfigs[key] = &logconfigv1.LogConfig{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: namespace,
			Name:      name,
		},
	}

	// 调用 handleDelete 方法
	controller.handleDelete(controller.LogConfigs[key])

	// 验证 LogConfig 是否被删除
	controller.mutex.Lock()
	defer controller.mutex.Unlock()
	if _, ok := controller.LogConfigs[key]; ok {
		t.Errorf("LogConfig with key %s should have been deleted", key)
	}
}

// TestLogConfigController_ListValidLogConfigs 是用于测试 LogConfigController_ListValidLogConfigs
// generated by Comate
func TestLogConfigController_ListValidLogConfigs(t *testing.T) {
	controller := &LogConfigController{
		LogConfigs: make(map[string]*logconfigv1.LogConfig),
		stopCh:     make(chan struct{}),
	}
	config1 := &logconfigv1.LogConfig{
		ObjectMeta: metav1.ObjectMeta{
			Name: "config1",
		},
	}
	config2 := &logconfigv1.LogConfig{
		ObjectMeta: metav1.ObjectMeta{
			Name: "config2",
		},
	}
	controller.LogConfigs["namespace1/config1"] = config1
	controller.LogConfigs["namespace2/config2"] = config2
	result := controller.ListValidLogConfigs()
	expected := []*logconfigv1.LogConfig{config1, config2}
	if !reflect.DeepEqual(result, expected) {
		t.Errorf("Expected %v, got %v", expected, result)
	}
}
