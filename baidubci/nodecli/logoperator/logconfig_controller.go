package logoperator

import (
	"context"
	"fmt"
	"sync"

	logconfigv1 "icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/logoperator/logconfigs/v1"

	"github.com/virtual-kubelet/virtual-kubelet/log"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/dynamic"
	dynamicinformer "k8s.io/client-go/dynamic/dynamicinformer"
	"k8s.io/client-go/tools/cache"
)

// LogConfigController 负责监听和缓存 kube-system 下有效的 LogConfig CRD
// 仅缓存满足 BLS/phase=Succeeded/taskID不为空/srcType=container 的 CRD
// 提供查询接口供 pod 注入逻辑调用

type LogConfigController struct {
	mutex      sync.RWMutex
	LogConfigs map[string]*logconfigv1.LogConfig // key: namespace/name
	stopCh     chan struct{}
}

func NewLogConfigController(dynamicClient dynamic.Interface) *LogConfigController {
	c := &LogConfigController{
		LogConfigs: make(map[string]*logconfigv1.LogConfig),
		stopCh:     make(chan struct{}),
	}
	factory := dynamicinformer.NewFilteredDynamicSharedInformerFactory(
		dynamicClient,
		0,
		// 所有namespace
		"",
		nil,
	)
	go c.run(factory)
	return c
}

func (c *LogConfigController) run(factory dynamicinformer.DynamicSharedInformerFactory) {
	gvr := logconfigv1.SchemeGroupVersion.WithResource("logconfigs")
	informer := factory.ForResource(gvr).Informer()

	_, err := informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    c.handleAddOrUpdate,
		UpdateFunc: func(oldObj, newObj interface{}) { c.handleAddOrUpdate(newObj) },
		DeleteFunc: c.handleDelete,
	})
	if err != nil {
		log.G(context.Background()).Errorf("add event handler error: %+v", err)
		return
	}
	factory.Start(c.stopCh)
	factory.WaitForCacheSync(c.stopCh)
	<-c.stopCh
}

func (c *LogConfigController) handleAddOrUpdate(obj interface{}) {
	if _, ok := obj.(metav1.Object); !ok {
		log.G(context.Background()).Errorf("obj is not metav1.Object")
		return
	}
	// 反序列化为 LogConfig
	logConfig := &logconfigv1.LogConfig{}
	err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.(*unstructured.Unstructured).Object, logConfig)
	if err != nil {
		log.G(context.Background()).Errorf("unstructured to logConfig error: %+v", err)
		return
	}
	if !isValidLogConfig(logConfig) {
		log.G(context.Background()).Infof("logConfig %s is not valid", logConfig.Name)
		return
	}
	key := fmt.Sprintf("%s/%s", logConfig.Namespace, logConfig.Name)
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.LogConfigs[key] = logConfig
}

func (c *LogConfigController) handleDelete(obj interface{}) {
	var namespace, name string
	if metaObj, ok := obj.(metav1.Object); ok {
		namespace = metaObj.GetNamespace()
		name = metaObj.GetName()
	} else {
		return
	}
	key := fmt.Sprintf("%s/%s", namespace, name)
	c.mutex.Lock()
	defer c.mutex.Unlock()
	delete(c.LogConfigs, key)
}

// isValidLogConfig 判断 CRD 是否满足采集条件
func isValidLogConfig(lc *logconfigv1.LogConfig) bool {
	if lc.Spec.SrcConfig.SrcType != "container" {
		log.G(context.Background()).Infof("logConfig %s is not container", lc.Name)
		return false
	}
	if lc.Spec.SrcConfig.LogType != "stdout" && lc.Spec.SrcConfig.LogType != "internal" {
		log.G(context.Background()).Infof("logConfig %s is not stdout or internal", lc.Name)
		return false
	}
	if lc.Spec.DstConfig.DstType != "BLS" {
		log.G(context.Background()).Infof("logConfig %s is not BLS", lc.Name)
		return false
	}
	if lc.Spec.DstConfig.LogStore == "" {
		log.G(context.Background()).Infof("logConfig %s logStore is empty", lc.Name)
		return false
	}
	if lc.Status.Phase != "Succeeded" {
		log.G(context.Background()).Infof("logConfig %s is not Succeeded", lc.Name)
		return false
	}
	if lc.Status.TaskID == "" {
		log.G(context.Background()).Infof("logConfig %s TaskID is empty", lc.Name)
		return false
	}
	return true

}

// ListValidLogConfigs 返回所有有效的 LogConfig
func (c *LogConfigController) ListValidLogConfigs() []*logconfigv1.LogConfig {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	var result []*logconfigv1.LogConfig
	for _, v := range c.LogConfigs {
		result = append(result, v)
	}
	return result
}

// Stop 停止 controller
func (c *LogConfigController) Stop() {
	close(c.stopCh)
}
