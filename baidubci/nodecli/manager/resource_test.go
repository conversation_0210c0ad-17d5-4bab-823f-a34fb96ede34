package manager

import (
	logoperator "icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/logoperator"

	"testing"

	"k8s.io/apimachinery/pkg/runtime"
	dynamicfake "k8s.io/client-go/dynamic/fake"
	"k8s.io/client-go/kubernetes/fake"
	admissionv1listers "k8s.io/client-go/listers/admissionregistration/v1"
	appsv1listers "k8s.io/client-go/listers/apps/v1"
	corev1listers "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/record"
)

// TestNewResourceManager 是用于测试 NewResourceManager
// generated by Comate
func TestNewResourceManager(t *testing.T) {
	// Create fake listers and clients
	indexer := cache.NewIndexer(cache.MetaNamespaceKeyFunc, cache.Indexers{})
	podLister := corev1listers.NewPodLister(indexer)
	secretLister := corev1listers.NewSecretLister(indexer)
	configMapLister := corev1listers.NewConfigMapLister(indexer)
	serviceLister := corev1listers.NewServiceLister(indexer)
	persistentVolumeLister := corev1listers.NewPersistentVolumeLister(indexer)
	persistentVolumeClaimLister := corev1listers.NewPersistentVolumeClaimLister(indexer)
	systemPodLister := corev1listers.NewPodLister(indexer)
	mutatingWebhookConfigurationLister := admissionv1listers.NewMutatingWebhookConfigurationLister(indexer)
	dsLister := appsv1listers.NewDaemonSetLister(indexer)
	eventRecorder := record.NewFakeRecorder(100)
	client := fake.NewSimpleClientset()
	dynamicClient := dynamicfake.NewSimpleDynamicClient(runtime.NewScheme())
	logConfigController := logoperator.NewLogConfigController(dynamicClient)
	resourceManager, err := NewResourceManager(
		podLister,
		secretLister,
		configMapLister,
		serviceLister,
		persistentVolumeLister,
		persistentVolumeClaimLister,
		systemPodLister,
		mutatingWebhookConfigurationLister,
		dsLister,
		eventRecorder,
		client,
		logConfigController,
	)
	if err != nil {
		t.Errorf("NewResourceManager returned an error: %v", err)
	}
	if resourceManager == nil {
		t.Errorf("NewResourceManager returned a nil resourceManager")
	}
	if resourceManager.podLister != podLister {
		t.Errorf("podLister not set correctly")
	}
	if resourceManager.secretLister != secretLister {
		t.Errorf("secretLister not set correctly")
	}
	if resourceManager.configMapLister != configMapLister {
		t.Errorf("configMapLister not set correctly")
	}
	if resourceManager.serviceLister != serviceLister {
		t.Errorf("serviceLister not set correctly")
	}
	if resourceManager.persistentVolumeLister != persistentVolumeLister {
		t.Errorf("persistentVolumeLister not set correctly")
	}
	if resourceManager.persistentVolumeClaimLister != persistentVolumeClaimLister {
		t.Errorf("persistentVolumeClaimLister not set correctly")
	}
	if resourceManager.systemPodLister != systemPodLister {
		t.Errorf("systemPodLister not set correctly")
	}
	if resourceManager.mutatingWebhookConfigurationLister != mutatingWebhookConfigurationLister {
		t.Errorf("mutatingWebhookConfigurationLister not set correctly")
	}
	if resourceManager.eventRecorder != eventRecorder {
		t.Errorf("eventRecorder not set correctly")
	}
	if resourceManager.client != client {
		t.Errorf("client not set correctly")
	}
}
