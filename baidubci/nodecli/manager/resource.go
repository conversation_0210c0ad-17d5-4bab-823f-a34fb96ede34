package manager

import (
	logoperator "icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/logoperator"

	"github.com/virtual-kubelet/virtual-kubelet/log"
	admissionv1 "k8s.io/api/admissionregistration/v1"
	appsv1 "k8s.io/api/apps/v1"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/kubernetes"
	admissionv1listers "k8s.io/client-go/listers/admissionregistration/v1"
	appsv1listers "k8s.io/client-go/listers/apps/v1"
	corev1listers "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/tools/record"
)

//go:generate mockgen -destination ./mock_resource.go -package manager -self_package icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/manager
// icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/manager ResourceManager

// ResourceManager acts as a passthrough to a cache (lister) for pods assigned to the current node.
// It is also a passthrough to a cache (lister) for Kubernetes secrets, config maps, pvcs, pvs and eventRecorder.
type ResourceManager interface {
	GetPods() []*v1.Pod
	GetPod(name, namespace string) (*v1.Pod, error)
	GetConfigMap(name, namespace string) (*v1.ConfigMap, error)
	GetSecret(name, namespace string) (*v1.Secret, error)
	ListServices() ([]*v1.Service, error)
	GetService(name, namespace string) (*v1.Service, error)
	GetPersistentVolume(name string) (*v1.PersistentVolume, error)
	GetPersistentVolumeClaim(name, namespace string) (*v1.PersistentVolumeClaim, error)
	ListSystemPods(selector labels.Selector) ([]*v1.Pod, error)
	GetEventRecorder() record.EventRecorder
	GetRawClient() kubernetes.Interface
	GetMutatingWebhookConfiguration(name string) (*admissionv1.MutatingWebhookConfiguration, error)
	GetDaemonsets() []*appsv1.DaemonSet
	GetDaemonset(name, namespace string) (*appsv1.DaemonSet, error)
	GetLogConfigController() *logoperator.LogConfigController
}

type resourceManager struct {
	podLister       corev1listers.PodLister
	secretLister    corev1listers.SecretLister
	configMapLister corev1listers.ConfigMapLister
	serviceLister   corev1listers.ServiceLister

	persistentVolumeLister      corev1listers.PersistentVolumeLister
	persistentVolumeClaimLister corev1listers.PersistentVolumeClaimLister
	systemPodLister             corev1listers.PodLister

	mutatingWebhookConfigurationLister admissionv1listers.MutatingWebhookConfigurationLister

	dsLister appsv1listers.DaemonSetLister

	eventRecorder record.EventRecorder

	client kubernetes.Interface

	logConfigController *logoperator.LogConfigController
}

// NewResourceManager returns a ResourceManager with the internal maps initialized.
func NewResourceManager(
	podLister corev1listers.PodLister,
	secretLister corev1listers.SecretLister,
	configMapLister corev1listers.ConfigMapLister,
	serviceLister corev1listers.ServiceLister,
	persistentVolumeLister corev1listers.PersistentVolumeLister,
	persistentVolumeClaimLister corev1listers.PersistentVolumeClaimLister,
	systemPodLister corev1listers.PodLister,
	mutatingWebhookConfigurationLister admissionv1listers.MutatingWebhookConfigurationLister,
	dsLister appsv1listers.DaemonSetLister,
	eventRecorder record.EventRecorder,
	client kubernetes.Interface,
	logConfigController *logoperator.LogConfigController,
) (*resourceManager, error) {
	rm := resourceManager{
		podLister:                          podLister,
		secretLister:                       secretLister,
		configMapLister:                    configMapLister,
		serviceLister:                      serviceLister,
		persistentVolumeLister:             persistentVolumeLister,
		persistentVolumeClaimLister:        persistentVolumeClaimLister,
		systemPodLister:                    systemPodLister,
		mutatingWebhookConfigurationLister: mutatingWebhookConfigurationLister,
		dsLister:                           dsLister,
		eventRecorder:                      eventRecorder,
		client:                             client,
		logConfigController:                logConfigController,
	}
	return &rm, nil
}

// GetPods returns a list of all known pods assigned to this virtual node.
func (rm *resourceManager) GetPods() []*v1.Pod {
	l, err := rm.podLister.List(labels.Everything())
	if err == nil {
		return l
	}
	log.L.Errorf("failed to fetch pods from lister: %v", err)
	return make([]*v1.Pod, 0)
}

// GetPod retrieves the specified pod from Kubernetes.
func (rm *resourceManager) GetPod(name, namespace string) (*v1.Pod, error) {
	return rm.podLister.Pods(namespace).Get(name)
}

// GetConfigMap retrieves the specified config map from the cache.
func (rm *resourceManager) GetConfigMap(name, namespace string) (*v1.ConfigMap, error) {
	return rm.configMapLister.ConfigMaps(namespace).Get(name)
}

// GetSecret retrieves the specified secret from Kubernetes.
func (rm *resourceManager) GetSecret(name, namespace string) (*v1.Secret, error) {
	return rm.secretLister.Secrets(namespace).Get(name)
}

// ListServices retrieves the list of services from Kubernetes.
func (rm *resourceManager) ListServices() ([]*v1.Service, error) {
	return rm.serviceLister.List(labels.Everything())
}

// GetService retrieves the specified service from Kubernetes.
func (rm *resourceManager) GetService(name, namespace string) (*v1.Service, error) {
	return rm.serviceLister.Services(namespace).Get(name)
}

// GetPersistentVolume retrieves the specified PersistentVolume from Kubernetes.
func (rm *resourceManager) GetPersistentVolume(name string) (*v1.PersistentVolume, error) {
	return rm.persistentVolumeLister.Get(name)
}

// GetPersistentVolumeClaim retrieves the specified PersistentVolumeClaim from Kubernetes.
func (rm *resourceManager) GetPersistentVolumeClaim(name, namespace string) (*v1.PersistentVolumeClaim, error) {
	return rm.persistentVolumeClaimLister.PersistentVolumeClaims(namespace).Get(name)
}

// ListSystemPods list pods within kube-system namespace.
func (rm *resourceManager) ListSystemPods(selector labels.Selector) ([]*v1.Pod, error) {
	return rm.systemPodLister.List(selector)
}

// GetEventRecorder get event recorder.
func (rm *resourceManager) GetEventRecorder() record.EventRecorder {
	return rm.eventRecorder
}

// GetRawClient gets the raw kubernetes client.
func (rm *resourceManager) GetRawClient() kubernetes.Interface {
	return rm.client
}

func (rm *resourceManager) GetMutatingWebhookConfiguration(name string) (*admissionv1.MutatingWebhookConfiguration, error) {
	return rm.mutatingWebhookConfigurationLister.Get(name)
}

// GetDaemonsets  returns all daemonsets assigned to this virtual node.
func (rm *resourceManager) GetDaemonsets() []*appsv1.DaemonSet {
	l, err := rm.dsLister.List(labels.Everything())
	if err == nil {
		return l
	}
	log.L.Errorf("failed to fetch pods from lister: %v", err)
	return make([]*appsv1.DaemonSet, 0)
}

// GetDaemonset retrieves the specified daemonset from Kubernetes.
func (rm *resourceManager) GetDaemonset(name, namespace string) (*appsv1.DaemonSet, error) {
	return rm.dsLister.DaemonSets(namespace).Get(name)
}

// GetLogConfigController returns the LogConfigController instance.
func (rm *resourceManager) GetLogConfigController() *logoperator.LogConfigController {
	return rm.logConfigController
}
