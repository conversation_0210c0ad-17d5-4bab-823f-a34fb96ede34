package logrus

import (
	"github.com/spf13/pflag"
)

// Config is used to configure a logrus logger from CLI flags.
type Config struct {
	LogLevel string
}

// FlagSet creates a new flag set based on the current config
func (c *Config) FlagSet() *pflag.FlagSet {
	flags := pflag.NewFlagSet("logrus", pflag.ContinueOnError)
	flags.StringVar(&c.<PERSON>g<PERSON>, "log-level", c.<PERSON>, `set the log level, e.g. "debug", "info", "warn", "error"`)
	return flags
}
