package logrus

import (
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"github.com/virtual-kubelet/virtual-kubelet/errdefs"
)

// Configure sets up the logrus logger
func Configure(c *Config, logger *logrus.Logger) error {
	if c.LogLevel != "" {
		lvl, err := logrus.ParseLevel(c.<PERSON>g<PERSON>)
		if err != nil {
			return errdefs.AsInvalidInput(errors.Wrap(err, "error parsing log level"))
		}

		if logger == nil {
			logger = logrus.StandardLogger()
		}
		logger.SetLevel(lvl)
	}

	return nil
}
