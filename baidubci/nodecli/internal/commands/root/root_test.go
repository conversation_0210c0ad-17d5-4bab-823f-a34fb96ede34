package root

import (
	"os"
	"testing"

	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/opts"
)

func Test_newClient(t *testing.T) {
	f, err := os.CreateTemp("", "kubeconfig*")
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(f.Name())

	if err := os.WriteFile(f.Name(), []byte(testKubeConfig), 0644); err != nil {
		t.Fatal(err)
	}

	type args struct {
		c *opts.Opts
	}
	tests := []struct {
		name    string
		args    args
		want    *kubernetes.Clientset
		want1   *kubernetes.Clientset
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			args: args{
				c: &opts.Opts{
					KubeConfigPath: f.Name(),
					KubeAPIQPS:     0,
					KubeAPIBurst:   0,
					EventRecordQPS: 0,
					EventBurst:     0,
				},
			},
			wantErr: true,
		},
		{
			name: "negative kube api qps",
			args: args{
				c: &opts.Opts{
					KubeConfigPath: f.Name(),
					KubeAPIQPS:     -1,
					KubeAPIBurst:   0,
					EventRecordQPS: 0,
					EventBurst:     0,
				},
			},
			wantErr: true,
		},
		{
			name: "negative event qps",
			args: args{
				c: &opts.Opts{
					KubeConfigPath: f.Name(),
					KubeAPIQPS:     0,
					KubeAPIBurst:   0,
					EventRecordQPS: -1,
					EventBurst:     0,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, _, _, err := newClient(tt.args.c)
			if (err != nil) != tt.wantErr {
				t.Errorf("newClient() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

const testKubeConfig = `apiVersion: v1
clusters:
- cluster:
    certificate-authority-data: 
LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURuakNDQW9hZ0F3SUJBZ0lVSDlQRVJPMUUwRnZqMnFINU1mb0c4bWhDY2FJd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1p6RUx
NQWtHQTFVRUJoTUNRMDR4RURBT0JnTlZCQWdUQjBKbGFVcHBibWN4RURBT0JnTlZCQWNUQjBKbAphVXBwYm1jeERqQU1C
Z05WQkFvVEJXcHdZV0Z6TVJRd0VnWURWUVFMRXd0amJHOTFaRzVoZEdsMlpURU9NQXdHCkExVUVBeE1GYW5CaFlYTXdI
aGNOTWpJeE1qQTVNRGd4T1RBd1doY05Nekl4TWpBMk1EZ3hPVEF3V2pCbk1Rc3cKQ1FZRFZRUUdFd0pEVGpFUU1BNEdBM
VVFQ0JNSFFtVnBTbWx1WnpFUU1BNEdBMVVFQnhNSFFtVnBTbWx1WnpFTwpNQXdHQTFVRUNoTUZhbkJoWVhNeEZEQVNCZ0
5WQkFzVEMyTnNiM1ZrYm1GMGFYWmxNUTR3REFZRFZRUURFd1ZxCmNHRmhjekNDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUF
EZ2dFUEFEQ0NBUW9DZ2dFQkFNdWhRSnh3em9Vai9ZK3gKdXlvQlFIYy9HM1BQV0V3ZUdtTHRmYjVwSUxoeEhqb0hYZHVB
WW4wTnRDNEhuakRjWUFnd0lXWHBSR2VQWVozRwpVS0plMitVdVFDZGtWdnYrNkNRQ0tNKyt2dHdBK0c5MlBaYm40dXlla
HA0eHNzVGRGb1pOajZ0UFBzZGJrTlpPCm92NmFJMzNZUnBIRGQzOWdOa28yeENsNkFSeDJuam8vWHRrWnFKSEs0ZW9lZm
dPdE9oczhJSnBOZ1JIeEdIWGoKWURkUkk0M3pobEFBU0hRbWJPeXpFSFB1VGJVV25vVWUyVk9LVFBtMmNjU3R3bkd3QVV
saEQ4VDJEamliMVoyWQppR2JKekVQK0J3TWNiOHpUYXpnUVNPNStiaW5wZ0NjWkEvcFArWE1IMWV2eGtDd1J6UmdLYzZR
YVRKdmRLYTlLCkVUbFFJQU1DQXdFQUFhTkNNRUF3RGdZRFZSMFBBUUgvQkFRREFnRUdNQThHQTFVZEV3RUIvd1FGTUFN
QkFmOHcKSFFZRFZSME9CQllFRkdqdVlSTlFWazdIOHloeGVJbDluSHBjV3BBL01BMEdDU3FHU0liM0RRRUJDd1VBQTRJ
QgpBUUJsMUgvWmZrSlBObW5sbDlmRlpiQUVXS3SZVEkxUTJRY2xZQlpEQS84em4waklrdDVsanYyWUo2SjR4TmlOCmxj
YUk0NWpSUmdqYSsrc1c4NzFNSGVrMnRyblFKMzdjU3Vxd0VZa1FVejQvK0RNNGg4Yk8vckFWZVNYUkxIQUgKUk4rZ2Jne
GtBZjVwbDMzS0x3eWRUeDBkbG1LSCtLU3ZBTjlBUGkzV21ZWWN3aXNWcG5Kb2FGTkoyOTdrWDd2ZQp6bWJibnI3QWVaZ
k96NnQ2S3JhWk8xemN5MVNqWnUwOVhVa1NrUWNINUhoUDlPWFVlZXo0VERRYTRhaFJDVkJwCkVUVWR3T1pVZVY2SlpOVF
hvYWJLOHppR1NHSVUrRVBaNk95Z0w4SVBiTnpsWnNuclh4Z2ZQRy9oemtKdHpkRDUKYlErOEdEVWJPb2hucmdhRVppYT
ErQWhYCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
    server: https://116.13.125.242:6443
  name: kubernetes
contexts:
- context:
    cluster: kubernetes
    user: eca97e148cb74e9683d7b7240829d1ff
  name: eca97e148cb74e9683d7b7240829d1ff@kubernetes
current-context: eca97e148cb74e9683d7b7240829d1ff@kubernetes
kind: Config
preferences: {}
users:
- name: eca97e148cb74e9683d7b7240829d1ff
  user:
    client-certificate-data: 
LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUVRekNDQXl1Z0F3SUJBZ0lVVjRpU1VXMHN2emlVZDVQSzBpRm9kNVV4TVRJd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1p6
RUxNQWtHQTFVRUJoTUNRMDR4RURBT0JnTlZCQWdUQjBKbGFVcHBibWN4RURBT0JnTlZCQWNUQjBKbAphVXBwYm1jeERq
QU1CZ05WQkFvVEJXcHdZV0Z6TVJRd0VnWURWUVFMRXd0amJHOTFaRzVoZEdsMlpURU9NQXdHCkExVUVBeE1GYW5CaFlY
TXdIaGNOTWpJeE1qQTVNRGd5TURBd1doY05Nekl4TWpBMk1EZ3lNREF3V2pDQm5URUwKTUFrR0ExVUVCaE1DUTA0eEVE
QU9CZ05WQkFnVEIwSmxhVXBwYm1jeEVEQU9CZ05WQkFjVEIwSmxhVXBwYm1jeApLVEFuQmdOVkJBb1RJR1ZqWVRrM1pU
RTBPR05pTnpSbE9UWTRNMlEzWWpjeU5EQTRNamxrTVdabU1SUXdFZ1lEClZRUUxFd3RqYkc5MVpHNWhkR2wyWlRFcE1D
Y0dBMVVFQXhNZ1pXTmhPVGRsTVRRNFkySTNOR1U1TmpnelpEZGkKTnpJME1EZ3lPV1F4Wm1Zd2dnRWlNQTBHQ1NxR1N
JYjNEUUVCQVFVQUE0SUJEd0F3Z2dFS0FvSUJBUUNkeTBySwp3VG84UmRma1pVWnRCV0lYVUNvVmZBTkU2TC9uV1F3VT
RNUHZJT0JaMXF0bjRtN0dxbkhZRitBWkhDUTY1R2FYCktJU2xIZlBvTzkzRnFnMndlNXMyZ1gweWl3M2FOalNWWE00Y
khHRVZoSnpXZzBMN3RTRkFZOUw0WkxkK1k0d0QKUGZYT1U1SjQ2bURFQzY4MmVvM3EzVnZVbFdkakxoODRlQ29rSnl3
cU1aMEV2T09rakF4aW53dVJKZHgwOW91RwoydXFHdCs0WFcwU056N0l2MWlQNkdxTDRmNC9iMXpKZUM4RVpaZVViOWJ
FQk9nMzgrOWdUWm9UdDJsMUs4ZG9rCkNmelphOUNKUXYvdmJ6ZGZwaDdkT3hpeTVHMFN1UnVEOTljdGpaeXBPa1V4Rj
NpU1RZT2txMFZLVmJBRDQwTG0KNkpWU1NveVAwQTJ0YUJlakFnTUJBQUdqZ2E4d2dhd3dEZ1lEVlIwUEFRSC9CQVFEQ
WdXZ01CMEdBMVVkSlFRVwpNQlFHQ0NzR0FRVUZCd01CQmdnckJnRUZCUWNEQWpBTUJnTlZIUk1CQWY4RUFqQUFNQjBH
QTFVZERnUVdCQlNjCkM1cXV1bFVsSVQ3M2hLN3EycVJZT054STVqQWZCZ05WSFNNRUdEQVdnQlJvN21FVFVGWk94L01v
Y1hpSmZaeDYKWEZxUVB6QXRCZ05WSFJFRUpqQWtod1JrUXBYdmh3VEFxQUFIaHdScURYM3lod1RBcUFvR2h3UmtVSmx
5aHdRSwovd0FCTUEwR0NTcUdTSWIzRFFFQkN3VUFBNElCQVFCZ1gwMzdXT0I5QVNvcWJURkUrVTdMUU5ZRWpSZ3p4WnZ
BCmxCN0VrcnI4WnZPZXFNdW5kTDFNYklZUXVjLy9CQXNXczExaU9TWUdtL25BNFQzQ3lhRnVROGF3Q3hTYmVpY3gKa21
5cW84RVMvZFR6TFJ4YzloZEVza1Z0QVJZS3Y5djlIZ0RHOC9IbHpBZVd0Y05wRzlsMW1LQi8zdDJ2dDd3MApseGNmWV
ZmSXQ0Ry9ydjZJVitiM1ArMlptQ3Y4dElaaldnenZmTkxmYUhQNHQ5S1ZlVmxuS2RWUEQ3eDVvbGdvCjNORW5rczhiaD
BaZkxtN1JrMHpKQWxUQXlQZitHUmhLWStZU1hjay93TkRrQWlqTGdlNTQvNmh5MFE5UjZCdCsKRVpEVEhNakZ6amQyK
2lMcDRHK2h5czZKckNRWVN0QWhVRERVRmdONmwyWjRRNERWMEdBYwotLS0tLUVOR
    client-key-data: 
LS0tLS1CRUdJTiBSU0EgUFJJVkFURSBLRVktLS0tLQpNSUlFb3dJQkFBS0NBUUVBbmN0S3lzRTZQRVhYNUdWR2JRVmlGMUFxRlh3RFJPaS81MWtNRk9ERDd5RGdXZGFyClorSnV4cXB4MkJm
Z0dSd2tPdVJtbHlpRXBSM3o2RHZkeGFvTnNIdWJOb0Y5TW9zTjJqWTBsVnpPR3h4aEZZU2MKMW9OQys3VWhRR1BTK0dTM2
ZtT01BejMxemxPU2VPcGd4QXV2Tm5xTjZ0MWIxSlZuWXk0Zk9IZ3FKQ2NzS2pHZApCTHpqcEl3TVlwOExrU1hjZFBhTGh0
cnFocmZ1RjF0RWpjK3lMOVlqK2hxaStIK1AyOWN5WGd2QkdXWGxHL1d4CkFUb04vUHZZRTJhRTdkcGRTdkhhSkFuODJXdl
FpVUwvNzI4M1g2WWUzVHNZc3VSdEVya2JnL2ZYTFkyY3FUcEYKTVJkNGtrMkRwS3RGU2xXd0ErTkM1dWlWVWtxTWo5QU5y
V2dYb3dJREFRQUJBb0lCQUZQb29OTUV3WlQvaHZoNwpLbUlhOUxoMDlvd0tNQ3Jhb2dIQ0JueXJzVVZkOUMrUnlnQXFGc0l
xTFBPK2JucXJ6bEJLMklzVkRzUkhFMGF4Clh4TVhoTVFPVnRFN05ucEVHNCtvTjNvZHFiUWMrVHRhRTVTMnBFMzVXYjlGMW
hBa2JyY1U5SVZjMDNOdVdYdFgKRnpIcERMa3Bub0lBU0M3WmErci93Ylpqbi9VczBiTzN4bzVUaU1jUnhVR2NMeGlkRyt1Z
lM3cUNUdDlWYlRNSQpJQlo4MGlNT2JjK1FNSHJtUTNWeVpGZERwc3Zmd1d3WFVwK1c1eFFLWFNMVmYySW5hclpCT1lwQm1x
MkFvSjV2ClBDVmk3TmpWK3FjZUZrZUk0OFJackREcVNIdFRqaHh6Si9Db09HRjlKMzErZW56ZlptU3Z0bm0vOStMRU56cTUK
UVFWaVN0RUNnWUVBekgvcHlVS01NODR1VmswNDkwMEtTeVFOb0RMRXpzOTA0SExsNm1OazhNelUrdC9JTXRtTApRN0NCZW9
MaENMNWtORUJrMmd4OER1S1dZdGlwRWtUM05NZm94U0pBVDhIK2VtbUhyTXhVQmdzUDRkZ2tKUXhwClZodjU1S252TjhYU2c
2dUVTWktxb3ZYMlhzQWgyQUhEQkxjWHZEZmlLa2w4a0xFNXQ2Y21GVThDZ1lFQXhZaEcKQktMMGo1MUpFVGhBVFZKMXRnS1
lVNFd6RER2T2grTW04ekF2SWZDK2l2TFJCUm1LMXN6OHBSeThPWU1qeTRuagoyelc1NzgxakxBNkxFYXN6NVlWWHJ1dFliV
XJ3MHlRZ0pnUE9MWTV6Wk0rcWNjdHI0NXVhMGNGQ29Sajc1RFJQClhBSDFTK1QyemZWSkVEdG8vcUUyT09HYTBlbnYzTkhD
QzVQbmEyMENnWUJEVlo3WjZQS1hJRDE3cHlPcHNoRkEKbXBkdDl5bLhXeXpGdHE5amxPU29zUmt6bmJtaW5LVFJYZTNVTkF
INnlnM2VjcEdDaG5mV3d0NzNueFZVY2FiZwpGeTdhc2lOdzM1UjJKQWRMYTJBRE9XTTZYdHZQNHgxUUFCZ3hKWThoMHg0MH
dxWGxQOGkxcmIwNGg1VGROZjlMCnJ5bStqTHdJWm5nQWdJcGJvVXJWMndLQmdENGZiSDNhemZVNHJ4a1dTc3JHaXpTUnZse
E9LUkJJNHR4RHFTVkUKV3g1ZWUxNXhuU0R3ZG9TMWRTTnNWUm4xVDFXYzZTY3d6MmhHZk5FSFJkOXBxMm1jdFFYVENEMWZH
endjR1d3NwpPQ1dGcm03ZkRLaTdKQlF5Qk0vQTZ2YUZSL3J1SktNOUZwc215Q1BmNTNtT1phZm5CWW1BRWxQQU1lcU5vV3h
JCnVMaEZBb0dCQUkvRFYybnRmcFk1M09FeVFibmZwQlAvUEdDdGY5QVNETWd0cnJ6bEQ1UUY5QlBZa0FCZEhzZGoKVEpkQn
dOSkNGOENwZ2gxQ29kbU95RTZSalYxR1Z5R0VXNXJBc1l1SHNiVXlnN2swRXUyZm9VRjhwUU5DeTdvUgpjSjlkVHNlSzda
UmRTWWIrSjhEdXdYY2pZNW5jTXNTZ3FIT01JbTNJRzdqc1FBa1ljZ2NVCi0tLS0tRU5EIFJTQSBQUklWQVRFIEtFWS0tLS
0tCg==`
