package root

import (
	"context"
	"os"
	"strings"

	"github.com/virtual-kubelet/virtual-kubelet/errdefs"
	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/opts"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/provider"
)

const osLabel = "beta.kubernetes.io/os"

// NodeFromProvider builds a kubernetes node object from a providers
// This is a temporary solution until node stuff actually split off from the providers interface itself.
func NodeFromProvider(ctx context.Context, name string, taint *v1.Taint, p provider.Provider, version string) *v1.Node {
	taints := make([]v1.Taint, 0)

	if taint != nil {
		taints = append(taints, *taint)
	}

	node := &v1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: name,
			Labels: map[string]string{
				"type":                   "virtual-kubelet",
				"kubernetes.io/role":     "agent",
				"kubernetes.io/hostname": name,
			},
		},
		Spec: v1.NodeSpec{
			Taints: taints,
		},
		Status: v1.NodeStatus{
			NodeInfo: v1.NodeSystemInfo{
				Architecture:   "amd64",
				KubeletVersion: version,
			},
		},
	}

	nodeLabels := ""
	if v := os.Getenv("VNODE_LABELS"); v != "" {
		nodeLabels = v
	}
	if nodeLabels != "" {
		for _, label := range strings.Split(nodeLabels, ",") {
			parts := strings.SplitN(label, "=", 2)
			if len(parts) != 2 || parts[0] == "" || parts[1] == "" {
				continue
			}
			node.ObjectMeta.Labels[parts[0]] = parts[1]
		}
	}

	p.ConfigureNode(ctx, node)
	if _, ok := node.ObjectMeta.Labels[osLabel]; !ok {
		node.ObjectMeta.Labels[osLabel] = strings.ToLower(node.Status.NodeInfo.OperatingSystem)
	}
	return node
}

// getTaint creates a taint using the provided key/value.
// Taint effect is read from the environment
// The taint key/value may be overwritten by the environment.
func getTaint(o *opts.Opts) (*corev1.Taint, error) {
	if o.TaintValue == "" {
		o.TaintValue = o.Provider
	}

	var effect corev1.TaintEffect
	switch o.TaintEffect {
	case "NoSchedule":
		effect = corev1.TaintEffectNoSchedule
	case "NoExecute":
		effect = corev1.TaintEffectNoExecute
	case "PreferNoSchedule":
		effect = corev1.TaintEffectPreferNoSchedule
	default:
		return nil, errdefs.InvalidInputf("taint effect %q is not supported", o.TaintEffect)
	}

	return &corev1.Taint{
		Key:    o.TaintKey,
		Value:  o.TaintValue,
		Effect: effect,
	}, nil
}
