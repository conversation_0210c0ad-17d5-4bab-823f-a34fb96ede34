package root

import (
	"testing"

	fakeauthenticationclient "k8s.io/client-go/kubernetes/typed/authentication/v1/fake"
	kubeletconfig "k8s.io/kubernetes/pkg/kubelet/apis/config"
)

// TestBuildAuthz_Webhook_NoClient 是用于测试 BuildAuthz_Webhook_NoClient
// generated by Comate
func TestBuildAuthz_Webhook_NoClient(t *testing.T) {
	authz := kubeletconfig.KubeletAuthorization{
		Mode: kubeletconfig.KubeletAuthorizationModeWebhook,
	}
	_, err := BuildAuthz(nil, authz)
	if err == nil {
		t.<PERSON>rrorf("Expected an error, got nil")
	}
	if err.Error() != "no client provided, cannot use webhook authorization" {
		t.Errorf("Expected error 'no client provided, cannot use webhook authorization', got %v", err)
	}
}

// TestBuildAuthn_NoClient 是用于测试 BuildAuthn_NoClient
// generated by Comate
func TestBuildAuthn_NoClient(t *testing.T) {
	authn := kubeletconfig.KubeletAuthentication{
		Webhook: kubeletconfig.KubeletWebhookAuthentication{
			Enabled: true,
		},
	}
	_, _, err := BuildAuthn(nil, authn)
	if err == nil {
		t.Fatal("Expected error, but got nil")
	}
	if err.Error() != "no client provided, cannot use webhook authentication" {
		t.Fatalf("Expected error 'no client provided, cannot use webhook authentication', but got: %v", err)
	}
}

// TestBuildAuthn_Anonymous 是用于测试 BuildAuthn_Anonymous
// generated by Comate
func TestBuildAuthn_Anonymous(t *testing.T) {
	client := &fakeauthenticationclient.FakeAuthenticationV1{}
	authn := kubeletconfig.KubeletAuthentication{
		Anonymous: kubeletconfig.KubeletAnonymousAuthentication{
			Enabled: true,
		},
	}
	authenticator, _, err := BuildAuthn(client, authn)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	if authenticator == nil {
		t.Fatal("Expected authenticator, but got nil")
	}
}

// TestBuildAuthn_InvalidConfig 是用于测试 BuildAuthn_InvalidConfig
// generated by Comate
func TestBuildAuthn_InvalidConfig(t *testing.T) {
	client := &fakeauthenticationclient.FakeAuthenticationV1{}
	authn := kubeletconfig.KubeletAuthentication{
		Anonymous: kubeletconfig.KubeletAnonymousAuthentication{
			Enabled: true,
		},
		Webhook: kubeletconfig.KubeletWebhookAuthentication{
			Enabled: true,
		},
		X509: kubeletconfig.KubeletX509Authentication{
			ClientCAFile: "non-existent-file.crt",
		},
	}
	_, _, err := BuildAuthn(client, authn)
	if err == nil {
		t.Fatal("Expected error, but got nil")
	}
}
