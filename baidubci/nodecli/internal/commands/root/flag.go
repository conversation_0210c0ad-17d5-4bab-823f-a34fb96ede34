package root

import (
	"flag"
	"os"

	"github.com/spf13/pflag"
	"k8s.io/klog"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/opts"
)

func installFlags(flags *pflag.FlagSet, c *opts.Opts) {
	flags.StringVar(&c.<PERSON>, "kubeconfig", c.<PERSON>, "kube config file to use for connecting to the Kubernetes API server")
	flags.StringVar(&c.<PERSON>, "namespace", c.KubeNamespace, "kubernetes namespace (default is 'all')")
	flags.StringVar(&c.<PERSON>, "cluster-domain", c.<PERSON>, "kubernetes cluster-domain (default is 'cluster.local')")
	flags.StringVar(&c.<PERSON>, "nodename", c.<PERSON>de<PERSON>, "kubernetes node name")
	flags.StringVar(&c.OperatingSystem, "os", c.OperatingSystem, "Operating System (Linux/Windows)")
	flags.StringVar(&c.<PERSON>vide<PERSON>, "providers", c.Provide<PERSON>, "cloud providers")
	flags.StringVar(&c.<PERSON>vide<PERSON>onfig<PERSON>, "providers-config", c.<PERSON>viderConfigPath, "cloud providers configuration file")
	flags.StringVar(&c.MetricsAddr, "metrics-addr", c.MetricsAddr, "address to listen for metrics/stats requests")

	flags.StringVar(&c.TaintKey, "taint", c.TaintKey, "Set node taint key")
	flags.BoolVar(&c.DisableTaint, "disable-taint", c.DisableTaint, "disable the virtual-kubelet node taint")
	_ = flags.MarkDeprecated("taint", "Taint key should now be configured using the VK_TAINT_KEY environment variable")

	flags.IntVar(&c.PodSyncWorkers, "pod-sync-workers", c.PodSyncWorkers, `set the number of pod synchronization workers`)
	flags.BoolVar(&c.EnableNodeLease, "enable-node-lease", c.EnableNodeLease, `use node leases (1.13) for node heartbeats`)

	flags.BoolVar(&c.EnableLeaderElection, "enable-leader-election", c.EnableLeaderElection, "Enables leader election. If leader election is enabled, "+
		"additional RBAC rules are required.")
	flags.StringVar(&c.LeaderElectionNamespace, "leader-election-namespace", c.LeaderElectionNamespace, "Namespace where the leader election resource lives."+
		" Defaults to kube-system if not set.")

	flags.DurationVar(&c.InformerResyncPeriod, "full-resync-period", c.InformerResyncPeriod, "how often to perform a full resync of pods between kubernetes "+
		"and the providers")
	flags.DurationVar(&c.StartupTimeout, "startup-timeout", c.StartupTimeout, "How long to wait for the virtual-kubelet to start")

	flags.Int32Var(&c.KubeAPIQPS, "kube-api-qps", c.KubeAPIQPS, "QPS to use while talking with kubernetes apiserver. The number must be >= 0. If 0 will use "+
		"DefaultQPS: 500. Doesn't cover events whose rate limiting is controlled by a different set of flags")
	flags.Int32Var(&c.KubeAPIBurst, "kube-api-burst", c.KubeAPIBurst, "Burst to use while talking with kubernetes apiserver. The number must be >= 0. If 0 "+
		"will use DefaultBurst: 500. Doesn't cover events whose rate limiting is controlled by a different set of flags")
	flags.Int32Var(&c.EventRecordQPS, "event-qps", c.EventRecordQPS, "QPS to limit event creations. The number must be >= 0. If 0 will use DefaultQPS: 50.")
	flags.Int32Var(&c.EventBurst, "event-burst", c.EventBurst,
		"Maximum size of a bursty event records, temporarily allows event records to burst to this number, while still not exceeding event-qps. "+
			"The number must be >= 0. If 0 will use DefaultBurst: 50.")
	flags.StringVar(&c.DNSConfigPath, "dns-config", c.DNSConfigPath, "path to a file containing the DNS configuration")

	flagset := flag.NewFlagSet("klog", flag.PanicOnError)
	klog.InitFlags(flagset)
	flagset.VisitAll(func(f *flag.Flag) {
		f.Name = "klog." + f.Name
		flags.AddGoFlag(f)
	})
}

func getEnv(key, defaultValue string) string {
	value, found := os.LookupEnv(key)
	if found {
		return value
	}
	return defaultValue
}
