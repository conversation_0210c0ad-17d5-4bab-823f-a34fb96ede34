// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/provider (interfaces: Provider)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	io "io"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	io_prometheus_client "github.com/prometheus/client_model/go"
	api "github.com/virtual-kubelet/virtual-kubelet/node/api"
	statsv1alpha1 "github.com/virtual-kubelet/virtual-kubelet/node/api/statsv1alpha1"
	bci "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
	v1 "k8s.io/api/apps/v1"
	v10 "k8s.io/api/core/v1"
)

// MockProvider is a mock of Provider interface.
type MockProvider struct {
	ctrl     *gomock.Controller
	recorder *MockProviderMockRecorder
}

// MockProviderMockRecorder is the mock recorder for MockProvider.
type MockProviderMockRecorder struct {
	mock *MockProvider
}

// NewMockProvider creates a new mock instance.
func NewMockProvider(ctrl *gomock.Controller) *MockProvider {
	mock := &MockProvider{ctrl: ctrl}
	mock.recorder = &MockProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProvider) EXPECT() *MockProviderMockRecorder {
	return m.recorder
}

// AttachToContainer mocks base method.
func (m *MockProvider) AttachToContainer(arg0 context.Context, arg1, arg2, arg3 string, arg4 api.AttachIO) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AttachToContainer", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// AttachToContainer indicates an expected call of AttachToContainer.
func (mr *MockProviderMockRecorder) AttachToContainer(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AttachToContainer", reflect.TypeOf((*MockProvider)(nil).AttachToContainer), arg0, arg1, arg2, arg3, arg4)
}

// ConfigureNode mocks base method.
func (m *MockProvider) ConfigureNode(arg0 context.Context, arg1 *v10.Node) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ConfigureNode", arg0, arg1)
}

// ConfigureNode indicates an expected call of ConfigureNode.
func (mr *MockProviderMockRecorder) ConfigureNode(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfigureNode", reflect.TypeOf((*MockProvider)(nil).ConfigureNode), arg0, arg1)
}

// CreatePod mocks base method.
func (m *MockProvider) CreatePod(arg0 context.Context, arg1 *v10.Pod) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePod", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreatePod indicates an expected call of CreatePod.
func (mr *MockProviderMockRecorder) CreatePod(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePod", reflect.TypeOf((*MockProvider)(nil).CreatePod), arg0, arg1)
}

// DeletePod mocks base method.
func (m *MockProvider) DeletePod(arg0 context.Context, arg1 *v10.Pod) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePod", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePod indicates an expected call of DeletePod.
func (mr *MockProviderMockRecorder) DeletePod(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePod", reflect.TypeOf((*MockProvider)(nil).DeletePod), arg0, arg1)
}

// GetContainerLogs mocks base method.
func (m *MockProvider) GetContainerLogs(arg0 context.Context, arg1, arg2, arg3 string, arg4 api.ContainerLogOpts) (io.ReadCloser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContainerLogs", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(io.ReadCloser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContainerLogs indicates an expected call of GetContainerLogs.
func (mr *MockProviderMockRecorder) GetContainerLogs(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContainerLogs", reflect.TypeOf((*MockProvider)(nil).GetContainerLogs), arg0, arg1, arg2, arg3, arg4)
}

// GetDsVolumes mocks base method.
func (m *MockProvider) GetDsVolumes(arg0 context.Context, arg1 *v1.DaemonSet) (*bci.Volumes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDsVolumes", arg0, arg1)
	ret0, _ := ret[0].(*bci.Volumes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDsVolumes indicates an expected call of GetDsVolumes.
func (mr *MockProviderMockRecorder) GetDsVolumes(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDsVolumes", reflect.TypeOf((*MockProvider)(nil).GetDsVolumes), arg0, arg1)
}

// GetMetricsResource mocks base method.
func (m *MockProvider) GetMetricsResource(arg0 context.Context) ([]*io_prometheus_client.MetricFamily, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMetricsResource", arg0)
	ret0, _ := ret[0].([]*io_prometheus_client.MetricFamily)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMetricsResource indicates an expected call of GetMetricsResource.
func (mr *MockProviderMockRecorder) GetMetricsResource(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMetricsResource", reflect.TypeOf((*MockProvider)(nil).GetMetricsResource), arg0)
}

// GetPod mocks base method.
func (m *MockProvider) GetPod(arg0 context.Context, arg1, arg2 string) (*v10.Pod, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPod", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v10.Pod)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPod indicates an expected call of GetPod.
func (mr *MockProviderMockRecorder) GetPod(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPod", reflect.TypeOf((*MockProvider)(nil).GetPod), arg0, arg1, arg2)
}

// GetPodDetail mocks base method.
func (m *MockProvider) GetPodDetail(arg0 context.Context, arg1, arg2 string) (*bci.DescribePodResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPodDetail", arg0, arg1, arg2)
	ret0, _ := ret[0].(*bci.DescribePodResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPodDetail indicates an expected call of GetPodDetail.
func (mr *MockProviderMockRecorder) GetPodDetail(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPodDetail", reflect.TypeOf((*MockProvider)(nil).GetPodDetail), arg0, arg1, arg2)
}

// GetPodStatus mocks base method.
func (m *MockProvider) GetPodStatus(arg0 context.Context, arg1, arg2 string) (*v10.PodStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPodStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v10.PodStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPodStatus indicates an expected call of GetPodStatus.
func (mr *MockProviderMockRecorder) GetPodStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPodStatus", reflect.TypeOf((*MockProvider)(nil).GetPodStatus), arg0, arg1, arg2)
}

// GetPods mocks base method.
func (m *MockProvider) GetPods(arg0 context.Context) ([]*v10.Pod, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPods", arg0)
	ret0, _ := ret[0].([]*v10.Pod)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPods indicates an expected call of GetPods.
func (mr *MockProviderMockRecorder) GetPods(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPods", reflect.TypeOf((*MockProvider)(nil).GetPods), arg0)
}

// GetStatsSummary mocks base method.
func (m *MockProvider) GetStatsSummary(arg0 context.Context) (*statsv1alpha1.Summary, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStatsSummary", arg0)
	ret0, _ := ret[0].(*statsv1alpha1.Summary)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStatsSummary indicates an expected call of GetStatsSummary.
func (mr *MockProviderMockRecorder) GetStatsSummary(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStatsSummary", reflect.TypeOf((*MockProvider)(nil).GetStatsSummary), arg0)
}

// PortForward mocks base method.
func (m *MockProvider) PortForward(arg0 context.Context, arg1, arg2 string, arg3 int32, arg4 io.ReadWriteCloser) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PortForward", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// PortForward indicates an expected call of PortForward.
func (mr *MockProviderMockRecorder) PortForward(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PortForward", reflect.TypeOf((*MockProvider)(nil).PortForward), arg0, arg1, arg2, arg3, arg4)
}

// RunInContainer mocks base method.
func (m *MockProvider) RunInContainer(arg0 context.Context, arg1, arg2, arg3 string, arg4 []string, arg5 api.AttachIO) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RunInContainer", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// RunInContainer indicates an expected call of RunInContainer.
func (mr *MockProviderMockRecorder) RunInContainer(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunInContainer", reflect.TypeOf((*MockProvider)(nil).RunInContainer), arg0, arg1, arg2, arg3, arg4, arg5)
}

// UpdateConfigMap mocks base method.
func (m *MockProvider) UpdateConfigMap(arg0 context.Context, arg1 *v10.Pod, arg2 *v10.ConfigMap) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateConfigMap", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateConfigMap indicates an expected call of UpdateConfigMap.
func (mr *MockProviderMockRecorder) UpdateConfigMap(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateConfigMap", reflect.TypeOf((*MockProvider)(nil).UpdateConfigMap), arg0, arg1, arg2)
}

// UpdateDsContainers mocks base method.
func (m *MockProvider) UpdateDsContainers(arg0 context.Context, arg1 *bci.InjectDsContainersRequest, arg2 *v10.Pod) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDsContainers", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDsContainers indicates an expected call of UpdateDsContainers.
func (mr *MockProviderMockRecorder) UpdateDsContainers(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDsContainers", reflect.TypeOf((*MockProvider)(nil).UpdateDsContainers), arg0, arg1, arg2)
}

// UpdatePod mocks base method.
func (m *MockProvider) UpdatePod(arg0 context.Context, arg1 *v10.Pod) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePod", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePod indicates an expected call of UpdatePod.
func (mr *MockProviderMockRecorder) UpdatePod(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePod", reflect.TypeOf((*MockProvider)(nil).UpdatePod), arg0, arg1)
}
