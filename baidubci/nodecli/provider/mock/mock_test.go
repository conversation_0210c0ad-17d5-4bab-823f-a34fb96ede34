package mock

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	io_prometheus_client "github.com/prometheus/client_model/go"
	statsv1alpha1 "github.com/virtual-kubelet/virtual-kubelet/node/api/statsv1alpha1"
)

// TestGetStatsSummary_Success 是用于测试 GetStatsSummary_Success
// generated by Comate
func TestGetStatsSummary_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockProvider := NewMockProvider(ctrl)
	ctx := context.Background()

	expectedSummary := &statsv1alpha1.Summary{
		// 填充Summary的字段
	}

	mockProvider.EXPECT().GetStatsSummary(ctx).Return(expectedSummary, nil)

	summary, err := mockProvider.GetStatsSummary(ctx)
	if err != nil {
		t.<PERSON>rrorf("GetStatsSummary() returned an error: %v", err)
	}

	if summary != expectedSummary {
		t.<PERSON><PERSON>("GetStatsSummary() returned unexpected summary: got %v, want %v", summary, expectedSummary)
	}
}

// TestMockProvider_GetMetricsResource 是用于测试 MockProvider_GetMetricsResource
// generated by Comate
func TestMockProvider_GetMetricsResource(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockProvider := NewMockProvider(ctrl)
	ctx := context.Background()
	expectedName := "metric_name"
	expectedHelp := "metric_help"
	expectedType := io_prometheus_client.MetricType_COUNTER
	expectedMetrics := []*io_prometheus_client.MetricFamily{
		{
			Name: &expectedName,
			Help: &expectedHelp,
			Type: &expectedType,
		},
	}
	mockProvider.EXPECT().GetMetricsResource(ctx).Return(expectedMetrics, nil)
	metrics, err := mockProvider.GetMetricsResource(ctx)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
	if len(metrics) != 1 {
		t.Errorf("Expected 1 metric, got %d", len(metrics))
	}
	if metrics[0].Name == nil || *metrics[0].Name != expectedName {
		t.Errorf("Expected metric name to be %s, got %s", expectedName, *metrics[0].Name)
	}
	if metrics[0].Help == nil || *metrics[0].Help != expectedHelp {
		t.Errorf("Expected metric help to be %s, got %s", expectedHelp, *metrics[0].Help)
	}
	if metrics[0].Type == nil || *metrics[0].Type != expectedType {
		t.Errorf("Expected metric type to be %s, got %s", expectedType, *metrics[0].Type)
	}
}
