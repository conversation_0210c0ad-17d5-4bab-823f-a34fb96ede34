package provider

import (
	"context"
	v1 "k8s.io/api/core/v1"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodeutil"
)

// Provider contains the methods required to implement a virtual-kubelet providers.
//
// Errors produced by these methods should implement an interface from
// github.com/virtual-kubelet/virtual-kubelet/errdefs package in order for the
// core logic to be able to understand the type of failure.
//
//go:generate mockgen -destination ./mock/mock.go -package mock -self_package icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/provider icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/provider Provider
type Provider interface {
	nodeutil.Provider
	// ConfigureNode enables a providers to configure the node object that
	// will be used for Kubernetes.
	ConfigureNode(context.Context, *v1.Node)
}
