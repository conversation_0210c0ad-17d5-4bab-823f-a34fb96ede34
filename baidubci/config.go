package baidubci

import (
	"context"
	"fmt"
	"io"
	"os"
	"strings"

	"github.com/BurntSushi/toml"
	"github.com/virtual-kubelet/virtual-kubelet/log"
	v1 "k8s.io/api/core/v1"
	"sigs.k8s.io/yaml"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/provider"
)

type providerConfig struct {
	Region          string
	LogicalZone     string
	VPCID           string
	VPCUUID         string
	SubnetID        string
	SubnetUUID      string
	SecurityGroupID string
	ClusterID       string
	CPU             string
	Memory          string
	Pods            string
	OperatingSystem string
}

func (p *BCIProvider) loadConfig(r io.Reader) error {
	var config providerConfig
	if _, err := toml.DecodeReader(r, &config); err != nil {
		return err
	}
	p.region = config.Region
	if p.region == "" {
		p.region = "bj"
	}

	// Default to 20 mcpu
	p.cpu = "20"
	if config.CPU != "" {
		p.cpu = config.CPU
	}
	// Default to 100Gi
	p.memory = "100Gi"
	if config.Memory != "" {
		p.memory = config.Memory
	}
	// Default to 20 pods
	p.pods = "20"
	if config.Pods != "" {
		p.pods = config.Pods
	}

	// Default to Linux if the operating system was not defined in the config.
	if config.OperatingSystem == "" {
		config.OperatingSystem = provider.OperatingSystemLinux
	} else {
		// Validate operating system from config.
		ok := provider.ValidOperatingSystems[config.OperatingSystem]
		if !ok {
			return fmt.Errorf("%q is not a valid operating system, try one of the following instead: %s",
				config.OperatingSystem, strings.Join(provider.ValidOperatingSystems.Names(), " | "))
		}
	}

	p.operatingSystem = config.OperatingSystem
	return nil
}

func (p *BCIProvider) loadDNSConfigFromPath(path string) error {
	if path == "" {
		return nil
	}
	f, err := os.Open(path)
	if err != nil {
		if os.IsNotExist(err) {
			log.G(context.Background()).Warnf("dns config file %s not found, do nothing", path)
		} else {
			return err
		}
	}
	defer func() {
		if f != nil {
			f.Close()
		}
	}()

	if f == nil {
		return nil
	}

	if err := p.loadDNSConfig(f); err != nil {
		log.G(context.Background()).Warnf("load dns config err: %v", err)
		return err
	}
	return nil
}

func (p *BCIProvider) loadDNSConfig(r io.Reader) error {
	if r == nil {
		return nil
	}

	b, err := io.ReadAll(r)
	if err != nil {
		return err
	}
	if err := p.setDNSConfig(b); err != nil {
		return err
	}

	return nil
}

func (p *BCIProvider) setDNSConfig(configData []byte) error {
	var dnsConfig v1.PodDNSConfig
	if err := yaml.Unmarshal(configData, &dnsConfig); err != nil {
		log.G(context.Background()).WithField("dnsconfig content", string(configData)).Errorf("Failed to parse DNS config: %v", err)
		return err
	}

	if len(dnsConfig.Nameservers) != 0 || len(dnsConfig.Searches) != 0 || len(dnsConfig.Options) != 0 {
		p.nodeDNSConfig = &dnsConfig
	}
	return nil
}
