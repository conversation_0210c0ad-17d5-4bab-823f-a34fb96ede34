package baidubci

import (
	"context"
	"errors"

	corev1 "k8s.io/api/core/v1"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	coreinformers "k8s.io/client-go/informers/core/v1"
	clientset "k8s.io/client-go/kubernetes"
	corev1client "k8s.io/client-go/kubernetes/typed/core/v1"
	corelisters "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/tools/cache"
	"k8s.io/klog"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/manager"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/provider"
)

type ConfigMapController struct {
	provider provider.Provider

	configmapInformer coreinformers.ConfigMapInformer
	configmapLister   corelisters.ConfigMapLister
	configmapClient   corev1client.ConfigMapsGetter

	podInformer coreinformers.PodInformer
	podLister   corelisters.PodLister
	podClient   corev1client.PodsGetter

	resourceManager manager.ResourceManager
}

func NewConfigMapController(provider provider.Provider, client clientset.Interface, configmapInformer coreinformers.ConfigMapInformer,
	podInformer coreinformers.PodInformer, rm manager.ResourceManager) (*ConfigMapController, error) {
	if configmapInformer == nil {
		return nil, errors.New("ConfigmapInformer can not be nil")
	}
	if podInformer == nil {
		return nil, errors.New("podInformer can not be nil")
	}
	if provider == nil {
		return nil, errors.New("provider can not be nil")
	}
	if client == nil {
		return nil, errors.New("client can not be nil")
	}
	if client.AppsV1() == nil || client.CoreV1() == nil {
		return nil, errors.New("configmap client or pod client can not be nil")
	}
	cmc := &ConfigMapController{
		provider:          provider,
		configmapClient:   client.CoreV1(),
		configmapInformer: configmapInformer,
		podClient:         client.CoreV1(),
		podInformer:       podInformer,
		resourceManager:   rm,
	}

	cmc.configmapLister = configmapInformer.Lister()
	cmc.podLister = podInformer.Lister()
	return cmc, nil
}

func (cmc *ConfigMapController) Run(ctx context.Context) error {
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	// Wait for the caches to be synced *before* starting to do work.
	if ok := cache.WaitForCacheSync(ctx.Done(), cmc.podInformer.Informer().HasSynced); !ok {
		return errors.New("pod informer cache is not synced")
	}
	// Wait for the caches to be synced *before* starting to do work.
	if ok := cache.WaitForCacheSync(ctx.Done(), cmc.configmapInformer.Informer().HasSynced); !ok {
		return errors.New("configmap informer cache is not synced")
	}

	var configmapEventHandler cache.ResourceEventHandler = cache.ResourceEventHandlerFuncs{
		UpdateFunc: cmc.updateConfigMap,
	}
	cmc.configmapInformer.Informer().AddEventHandler(configmapEventHandler)
	defer utilruntime.HandleCrash()
	defer klog.Infof("ConfigMapController: shutting ConfigMapController")

	<-ctx.Done()
	return nil
}

func (cmc *ConfigMapController) updateConfigMap(oldObj, newObj any) {
	oldConfigMap := oldObj.(*corev1.ConfigMap)
	newConfigMap := newObj.(*corev1.ConfigMap)
	if !isConfigMapChange(oldConfigMap, newConfigMap) {
		klog.Infof("ConfigMapController: configmap does not change,configmap name: %s, namespace: %s", newConfigMap.Name, newConfigMap.Namespace)
		return
	}
	klog.Infof("ConfigMapController: configmap change, configmap name: %s, namespace: %s", newConfigMap.Name, newConfigMap.Namespace)

	// TODO: daemonset need to update configmap too
	pods := cmc.resourceManager.GetPods()
	if len(pods) == 0 {
		klog.Infof("ConfigMapController: no bci pod found, skip")
		return
	}
	for _, pod := range pods {
		podID, ok := pod.GetAnnotations()[PodIDAnnotationKey]
		if !ok {
			klog.Infof("ConfigMapController: pod has no bci pod id annotation, skip")
			continue
		}

		// find configmap in pod
		klog.Infof("ConfigMapController: UpdateConfigMap podId: %s, namespace:%s, configmap name:%s",
			podID, pod.Namespace, newConfigMap.Name)

		for _, volume := range pod.Spec.Volumes {
			if volume.ConfigMap != nil && volume.ConfigMap.Name == newConfigMap.Name && pod.Namespace == newConfigMap.Namespace {
				ctx := context.Background()
				// 1. 请求bci更新token对应的configmap
				klog.Infof("ConfigMapController: UpdateConfigMap podID: %s, namespace:%s, configmap name:%s",
					podID, pod.Namespace, volume.ConfigMap.Name)
				err := cmc.provider.UpdateConfigMap(ctx, pod, newConfigMap)
				if err != nil {
					klog.Errorf("ConfigMapController: UpdateConfigMap podID: %s, namespace:%s, configmap name:%s,error: %v",
						podID, pod.Namespace, volume.ConfigMap.Name, err)
				}
			}
		}
	}

}

func isConfigMapChange(oldConfigMap, newConfigMap *corev1.ConfigMap) bool {
	configDataEqual := equalMapString(oldConfigMap.Data, newConfigMap.Data)
	if !configDataEqual {
		return true
	}

	configBinaryDataEqual := equalMapBytes(oldConfigMap.BinaryData, newConfigMap.BinaryData)
	return !configBinaryDataEqual
}
