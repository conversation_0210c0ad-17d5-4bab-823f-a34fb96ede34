package baidubci

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strings"
	"testing"
	"time"

	"code.cloudfoundry.org/clock"
	"code.cloudfoundry.org/clock/fakeclock"
	"github.com/golang/mock/gomock"
	uuid "github.com/satori/go.uuid"
	"github.com/virtual-kubelet/virtual-kubelet/node/api"
	"gotest.tools/assert"
	appsv1 "k8s.io/api/apps/v1"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/mock"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/manager"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/stsclient"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/userstorage"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/util"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
	bciv2 "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci/v2"
)

func TestBCIProvider_launchExecWSSURL(t *testing.T) {
	expectedURL := "wss://xxx.com"

	type fields struct {
		ctrl        *gomock.Controller
		bciClient   bci.Client
		bciV2Client bciv2.Client
		clusterID   string
		nodeName    string
	}
	type args struct {
		ctx           context.Context
		namespace     string
		podName       string
		containerName string
		cmd           []string
		attach        api.AttachIO
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		want1   *bci.Pod
		wantErr bool
	}{
		// TODO: Add testutils cases.
		{
			name: "success case",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bciClient := bci.NewMockClient(ctrl)
				bciV2Client := bciv2.NewMockClient(ctrl)
				bciV2Client.EXPECT().ListPods(gomock.Any(), gomock.Any(), gomock.Any()).Return(&bci.ListPodsResponse{Result: []*bci.Pod{
					{
						Name:  "default-testutils-pod",
						PodID: "p-qwer1234",
						Labels: []bci.PodLabel{
							{
								LabelKey:   PodNameLabelKey,
								LabelValue: "testutils-pod",
							},
							{
								LabelKey:   NamespaceLabelKey,
								LabelValue: "default",
							},
							{
								LabelKey:   ClusterIDLabelKey,
								LabelValue: "c-asdf1234",
							},
							{
								LabelKey:   NodeNameLabelKey,
								LabelValue: "bci-virtual-kubelet",
							},
						},
					},
				}}, nil)
				bciClient.EXPECT().LaunchExecWSSUrl(gomock.Any(), &bci.LaunchExecWSSUrlArgs{
					PodID:         "p-qwer1234",
					ContainerName: "container-0",
					TTY:           true,
					Stdin:         true,
					Command:       []string{"sh"},
				}, gomock.Any()).Return(expectedURL, nil)

				return fields{
					ctrl:        ctrl,
					bciClient:   bciClient,
					bciV2Client: bciV2Client,
					clusterID:   "c-asdf1234",
					nodeName:    "bci-virtual-kubelet",
				}
			}(),
			args: args{
				ctx:           context.Background(),
				namespace:     "default",
				podName:       "testutils-pod",
				containerName: "container-0",
				cmd:           []string{"sh"},
				attach:        &mock.MockAttachIO{},
			},
			want: expectedURL,
		},
		{
			name:   "nil attachIO case",
			fields: fields{},
			args: args{
				ctx:           context.Background(),
				namespace:     "default",
				podName:       "testutils-pod",
				containerName: "container-0",
				cmd:           []string{"sh"},
				attach:        nil,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			p := &BCIProvider{
				bciClient:   tt.fields.bciClient,
				bciV2Client: tt.fields.bciV2Client,
				nodeName:    tt.fields.nodeName,
				clusterID:   tt.fields.clusterID,
			}
			got, _, err := p.launchExecWSSURL(tt.args.ctx, tt.args.namespace, tt.args.podName, tt.args.containerName, tt.args.cmd, tt.args.attach)
			if (err != nil) != tt.wantErr {
				t.Errorf("BCIProvider.launchExecWSSURL() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func TestBCIProvider_attachToWSS(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, canl := context.WithTimeout(context.Background(), time.Second)
	defer canl()

	attachIO := &mock.MockAttachIO{}
	conn := &mock.MockWebsocketConn{}

	p := BCIProvider{
		clock: clock.NewClock(),
	}
	if err := p.attachToWSS(ctx, &bci.Pod{}, conn, attachIO); err == nil {
		t.Errorf("expected running until context timeout, actual: %v", err)
	}
}

func TestBCIProvider_getPodFullName(t *testing.T) {
	p := BCIProvider{}
	expected := "namespace-pod"
	if fullName := p.getPodFullName("namespace", "pod"); fullName != expected {
		t.Errorf("expected: %s, actual: %s", expected, fullName)
	}
}

func TestBCIProvider_getCapacity(t *testing.T) {
	p := BCIProvider{
		cpu:              "20",
		memory:           "100Gi",
		pods:             "0",
		ips:              "0",
		enis:             "0",
		ephemeralStorage: "400Gi",
	}
	res := p.getCapacity(context.TODO())
	if !res.Pods().IsZero() {
		t.Errorf("expected pod count is 0")
	}
}

func TestBCIProvider_searchBCIPodByPodName(t *testing.T) {
	testPodName := "default-testutils-pod"
	type fields struct {
		ctrl        *gomock.Controller
		bciV2Client bciv2.Client
	}
	type args struct {
		ctx     context.Context
		podName string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*bci.Pod
		wantErr bool
	}{
		// All testutils cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bciV2Client := bciv2.NewMockClient(ctrl)
				bciV2Client.EXPECT().ListPods(gomock.Any(), bci.NewMarkerListOption("", "", "", "", bci.DefaultMaxKeys, nil,
					bci.NewListKeyword(bci.KeywordTypePodName, testPodName)), nil).Return(&bci.ListPodsResponse{
					Result: []*bci.Pod{
						{
							Name: testPodName,
						},
					},
					IsTruncated: false,
					MaxKeys:     bci.DefaultMaxKeys,
				}, nil)

				return fields{
					ctrl:        ctrl,
					bciV2Client: bciV2Client,
				}
			}(),
			args: args{
				ctx:     context.TODO(),
				podName: testPodName,
			},
			want: []*bci.Pod{
				{
					Name: testPodName,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			p := &BCIProvider{
				bciV2Client: tt.fields.bciV2Client,
			}
			got, err := p.searchBCIPodByPodName(tt.args.ctx, tt.args.podName)
			if (err != nil) != tt.wantErr {
				t.Errorf("BCIProvider.searchBCIPodByPodName() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func TestBCIProvider_listMyBCIPods(t *testing.T) {
	testCCEID := "cce-testcce0"
	testNodeName := "bci-vk"
	type fields struct {
		ctrl        *gomock.Controller
		bciV2Client bciv2.Client
		clusterID   string
		nodeName    string
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*bci.Pod
		wantErr bool
	}{
		// All testutils cases.
		{
			name: "finish in one request",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bciV2Client := bciv2.NewMockClient(ctrl)

				bciV2Client.EXPECT().ListPods(gomock.Any(), bci.NewMarkerListOption(testCCEID, "", "", "", bci.DefaultMaxKeys, nil,
					bci.NewListKeyword(bci.KeywordTypeCCEID, testCCEID)), nil).Return(&bci.ListPodsResponse{
					Result: []*bci.Pod{{
						CCEID: testCCEID,
						Labels: []bci.PodLabel{
							{
								LabelKey:   NodeNameLabelKey,
								LabelValue: testNodeName,
							},
						},
					}},
				}, nil)

				return fields{
					ctrl:        ctrl,
					bciV2Client: bciV2Client,
					clusterID:   testCCEID,
					nodeName:    testNodeName,
				}
			}(),
			args: args{
				ctx: context.Background(),
			},
			want: []*bci.Pod{{
				CCEID: testCCEID,
				Labels: []bci.PodLabel{
					{
						LabelKey:   NodeNameLabelKey,
						LabelValue: testNodeName,
					},
				},
			}},
		},
		{
			name: "finish in two requests",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bciV2Client := bciv2.NewMockClient(ctrl)

				var result []*bci.Pod
				for i := int64(0); i < bci.DefaultMaxKeys; i++ {
					result = append(result, &bci.Pod{
						CCEID: testCCEID,
						Labels: []bci.PodLabel{
							{
								LabelKey:   NodeNameLabelKey,
								LabelValue: testNodeName,
							},
						},
					})
				}
				breakMarker := "p-mymarker"
				bciV2Client.EXPECT().ListPods(gomock.Any(), bci.NewMarkerListOption(testCCEID, "", "", "", bci.DefaultMaxKeys, nil,
					bci.NewListKeyword(bci.KeywordTypeCCEID, testCCEID)), nil).Return(&bci.ListPodsResponse{
					Result:      result,
					IsTruncated: true,
					NextMarker:  "p-mymarker",
					MaxKeys:     bci.DefaultMaxKeys,
				}, nil)
				bciV2Client.EXPECT().ListPods(gomock.Any(), bci.NewMarkerListOption(testCCEID, breakMarker, "", "", bci.DefaultMaxKeys, nil,
					bci.NewListKeyword(bci.KeywordTypeCCEID, testCCEID)), nil).Return(&bci.ListPodsResponse{
					Result:      result,
					IsTruncated: false,
					MaxKeys:     bci.DefaultMaxKeys,
				}, nil)

				return fields{
					ctrl:        ctrl,
					bciV2Client: bciV2Client,
					clusterID:   testCCEID,
					nodeName:    testNodeName,
				}
			}(),
			args: args{
				ctx: context.Background(),
			},
			want: func() []*bci.Pod {
				var result []*bci.Pod
				for i := int64(0); i < 2*bci.DefaultMaxKeys; i++ {
					result = append(result, &bci.Pod{
						CCEID: testCCEID,
						Labels: []bci.PodLabel{
							{
								LabelKey:   NodeNameLabelKey,
								LabelValue: testNodeName,
							},
						},
					})
				}
				return result
			}(),
		},
		{
			name: "error case",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bciV2Client := bciv2.NewMockClient(ctrl)

				bciV2Client.EXPECT().ListPods(gomock.Any(), bci.NewMarkerListOption(testCCEID, "", "", "", bci.DefaultMaxKeys, nil,
					bci.NewListKeyword(bci.KeywordTypeCCEID, testCCEID)), nil).Return(nil, fmt.Errorf("list error"))

				return fields{
					ctrl:        ctrl,
					bciV2Client: bciV2Client,
					clusterID:   testCCEID,
					nodeName:    testNodeName,
				}
			}(),
			args:    args{},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			p := &BCIProvider{
				bciV2Client: tt.fields.bciV2Client,
				clusterID:   tt.fields.clusterID,
				nodeName:    tt.fields.nodeName,
			}
			got, err := p.listMyBCIPods(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("BCIProvider.listMyBCIPods() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func TestBCIProvider_ConfigureNode(t *testing.T) {
	p := BCIProvider{
		cpu:              "20",
		memory:           "100Gi",
		pods:             "0",
		ips:              "0",
		enis:             "0",
		ephemeralStorage: "400Gi",
	}
	p.ConfigureNode(context.TODO(), &v1.Node{ObjectMeta: metav1.ObjectMeta{Labels: map[string]string{}}})
}

// ContextUserUUIDMatcher implements gomock.Matcher, judging if a specific userUUID is within context
type ContextUserUUIDMatcher struct {
	wantUserUUID string
}

func NewContextUserUUIDMatcher(userUUID string) gomock.Matcher {
	return &ContextUserUUIDMatcher{
		wantUserUUID: userUUID,
	}
}

func (m *ContextUserUUIDMatcher) String() string {
	return "UserUUID in context"
}

func (m *ContextUserUUIDMatcher) Matches(x interface{}) bool {
	if ctx, ok := x.(context.Context); ok {
		if util.GetUserUUID(ctx) == m.wantUserUUID {
			return true
		}
	}
	return false
}

func TestBCIProviderGetPods(t *testing.T) {
	CCEID1 := "c-asdf1234"
	nodeName1 := "bci-virtual-kubelet-0"
	CCEID2 := "c-s239r2mj"
	nodeName2 := "bci-virtual-kubelet-1"

	user1UUID := strings.Replace(uuid.NewV4().String(), "-", "", -1)
	user2UUID := strings.Replace(uuid.NewV4().String(), "-", "", -1)

	user1PodList := []*bci.Pod{
		{
			Name:    "user1-testutils-pod-1",
			PodID:   "p-qwer1234",
			PodUUID: uuid.NewV4().String(),
			CCEID:   CCEID1,
			Labels: []bci.PodLabel{
				{
					LabelKey:   NodeNameLabelKey,
					LabelValue: nodeName1,
				},
			},
		},
		{
			Name:    "user1-testutils-pod-2",
			PodID:   "p-x7ew6rfx",
			PodUUID: uuid.NewV4().String(),
			CCEID:   CCEID1,
			Labels: []bci.PodLabel{
				{
					LabelKey:   NodeNameLabelKey,
					LabelValue: nodeName2,
				},
			},
		},
		{
			Name:    "user1-testutils-pod-3",
			PodID:   "p-dwfqr345",
			CCEID:   CCEID2,
			PodUUID: uuid.NewV4().String(),
			Labels: []bci.PodLabel{
				{
					LabelKey:   NodeNameLabelKey,
					LabelValue: nodeName1,
				},
			},
		},
	}
	user2PodList := []*bci.Pod{
		{
			Name:    "user2-testutils-pod-1",
			PodID:   "p-76tguihk",
			CCEID:   CCEID2,
			PodUUID: uuid.NewV4().String(),
			Labels: []bci.PodLabel{
				{
					LabelKey:   NodeNameLabelKey,
					LabelValue: nodeName2,
				},
			},
		},
		{
			Name:    "user2-testutils-pod-2",
			PodID:   "p-h8bjhkj6",
			PodUUID: uuid.NewV4().String(),
			CCEID:   CCEID1,
			Labels: []bci.PodLabel{
				{
					LabelKey:   NodeNameLabelKey,
					LabelValue: nodeName1,
				},
			},
		},
		{
			Name:    "user2-testutils-pod-3",
			PodID:   "p-yhuik6lk",
			PodUUID: uuid.NewV4().String(),
			CCEID:   CCEID1,
			Labels: []bci.PodLabel{
				{
					LabelKey:   NodeNameLabelKey,
					LabelValue: nodeName1,
				},
			},
		},
	}

	type args struct {
		ctrl        *gomock.Controller
		bciClient   bci.Client
		bciV2Client bciv2.Client
		userStorage userstorage.Storage
		stsClient   stsclient.Client
		nodeName    string
		clusterID   string
		mode        util.MultiTenantMode

		ctx context.Context

		want    []*v1.Pod
		wantErr bool
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "single tenant case",
			args: func() args {
				nodeName := nodeName1
				clusterID := CCEID2
				ctrl := gomock.NewController(t)
				ctx := context.TODO()

				// mock bci.Client
				bciClient := bci.NewMockClient(ctrl)
				bciV2Client := bciv2.NewMockClient(ctrl)
				bciV2Client.EXPECT().ListPods(NewContextUserUUIDMatcher(""), bci.NewMarkerListOption(clusterID, "", "", "",
					bci.DefaultMaxKeys, nil, bci.NewListKeyword(bci.KeywordTypeCCEID, clusterID)), gomock.Any()).
					Return(&bci.ListPodsResponse{
						Result:     user1PodList,
						TotalCount: int64(len(user1PodList)),
					}, nil)
				for _, pod := range user1PodList {
					if getBCILabelValue(pod, NodeNameLabelKey) == nodeName && pod.CCEID == clusterID {
						bciClient.EXPECT().DescribePod(NewContextUserUUIDMatcher(""), pod.PodID,
							gomock.Any()).Return(&bci.DescribePodResponse{Pod: pod}, nil)
					}
				}

				// mock userstorage.Storage
				userStorage := userstorage.NewMockStorage(ctrl)
				userStorage.EXPECT().GetAllUsers(gomock.Any(), nodeName).Return(nil, nil)

				want := make([]*v1.Pod, 0)
				for _, pod := range user1PodList {
					if getBCILabelValue(pod, NodeNameLabelKey) == nodeName && pod.CCEID == clusterID {
						v1Pod, err := bciPodDetailToPod(ctx, &bci.DescribePodResponse{
							Pod: pod,
						})
						if err != nil {
							t.Fatalf("bciPodDetailToPod: %v", err)
						}
						want = append(want, v1Pod)
					}
				}

				return args{
					ctrl:        ctrl,
					bciClient:   bciClient,
					bciV2Client: bciV2Client,
					userStorage: userStorage,
					nodeName:    nodeName,
					clusterID:   clusterID,
					ctx:         ctx,
					want:        want,
				}
			}(),
		},
		{
			name: "multiple tenant case",
			args: func() args {
				nodeName := nodeName1
				clusterID := CCEID1
				mode := util.MultiTenantModeBSC
				ctrl := gomock.NewController(t)
				ctx := context.TODO()

				// mock bci.Client
				bciClient := bci.NewMockClient(ctrl)
				bciV2Client := bciv2.NewMockClient(ctrl)
				bciV2Client.EXPECT().ListPods(NewContextUserUUIDMatcher(user1UUID), gomock.Any(), gomock.Any()).Return(&bci.ListPodsResponse{
					Result:     user1PodList,
					TotalCount: int64(len(user1PodList)),
				}, nil)
				bciV2Client.EXPECT().ListPods(NewContextUserUUIDMatcher(user2UUID), gomock.Any(), gomock.Any()).Return(&bci.ListPodsResponse{
					Result:     user2PodList,
					TotalCount: int64(len(user2PodList)),
				}, nil)
				for _, pod := range user1PodList {
					if getBCILabelValue(pod, NodeNameLabelKey) == nodeName && pod.CCEID == clusterID {
						bciClient.EXPECT().DescribePod(NewContextUserUUIDMatcher(user1UUID), pod.PodID, gomock.Any()).Return(&bci.DescribePodResponse{Pod: pod}, nil)
					}
				}
				for _, pod := range user2PodList {
					if getBCILabelValue(pod, NodeNameLabelKey) == nodeName && pod.CCEID == clusterID {
						bciClient.EXPECT().DescribePod(NewContextUserUUIDMatcher(user2UUID), pod.PodID, gomock.Any()).Return(&bci.DescribePodResponse{Pod: pod}, nil)
					}
				}

				// mock stsclient.Client
				stsClient := stsclient.NewMockClient(ctrl)
				// GetSessionToken token should be called only once for each user because of cache
				stsClient.EXPECT().GetSessionToken(gomock.Any(), gomock.Eq(user1UUID)).Return(&bce.SessionTokenResponse{}, nil)
				stsClient.EXPECT().GetSessionToken(gomock.Any(), gomock.Eq(user2UUID)).Return(&bce.SessionTokenResponse{}, nil)

				// mock userstorage.Storage
				userStorage := userstorage.NewMockStorage(ctrl)
				userStorage.EXPECT().GetAllUsers(gomock.Any(), nodeName).Return([]string{user1UUID, user2UUID}, nil)

				// want
				want := make([]*v1.Pod, 0)
				for _, pod := range user1PodList {
					if getBCILabelValue(pod, NodeNameLabelKey) == nodeName && pod.CCEID == clusterID {
						v1Pod, err := bciPodDetailToPod(ctx, &bci.DescribePodResponse{
							Pod: pod,
						})
						if err != nil {
							t.Fatalf("bciPodDetailToPod: %v", err)
						}
						want = append(want, v1Pod)
					}
				}
				for _, pod := range user2PodList {
					if getBCILabelValue(pod, NodeNameLabelKey) == nodeName && pod.CCEID == clusterID {
						v1Pod, err := bciPodDetailToPod(ctx, &bci.DescribePodResponse{
							Pod: pod,
						})
						if err != nil {
							t.Fatalf("bciPodDetailToPod: %v", err)
						}
						want = append(want, v1Pod)
					}
				}

				return args{
					ctrl:        ctrl,
					bciClient:   bciClient,
					bciV2Client: bciV2Client,
					stsClient:   stsClient,
					userStorage: userStorage,
					nodeName:    nodeName,
					clusterID:   clusterID,
					mode:        mode,
					ctx:         ctx,
					want:        want,
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.args.ctrl != nil {
				defer tt.args.ctrl.Finish()
			}
			defer STSTokenCache.Flush()
			p := &BCIProvider{
				nodeName:        tt.args.nodeName,
				clusterID:       tt.args.clusterID,
				multiTenantMode: tt.args.mode,
				bciClient:       tt.args.bciClient,
				bciV2Client:     tt.args.bciV2Client,
				stsClient:       tt.args.stsClient,
				userStorage:     tt.args.userStorage,
			}

			got, err := p.GetPods(tt.args.ctx)
			if (err != nil) != tt.args.wantErr {
				t.Errorf("BCIProvider.listMyBCIPods() error = %v, wantErr %v", err, tt.args.wantErr)
				return
			}

			// sort before comparison
			sort.Slice(got, func(i, j int) bool { return got[i].GetName() < got[j].GetName() })
			sort.Slice(tt.args.want, func(i, j int) bool { return tt.args.want[i].GetName() < tt.args.want[j].GetName() })

			assert.DeepEqual(t, got, tt.args.want)
		})
	}
}

func TestBCIResources(t *testing.T) {
	xJSONBytes := `{"cpu":"250m","memory":"512Mi","pods":2}`
	cpu := resource.MustParse("250m")
	memory := resource.MustParse("512Mi")
	x := BCIResources{}
	err := json.Unmarshal([]byte(xJSONBytes), &x)
	assert.NilError(t, err)
	assert.DeepEqual(t, x, BCIResources{
		CPUQuantity:    &cpu,
		MemoryQuantity: &memory,
		Pods:           2,
		CPUInCore:      0.25,
		MemoryInGB:     0.5,
	})

	yJSONBytes := `{"cpu":"1000","memory":"2000Gi", "pods":2000}`
	cpu = resource.MustParse("1000")
	memory = resource.MustParse("2000Gi")

	y := BCIResources{}
	err = json.Unmarshal([]byte(yJSONBytes), &y)
	assert.NilError(t, err)
	assert.DeepEqual(t, y, BCIResources{
		CPUQuantity:    &cpu,
		MemoryQuantity: &memory,
		Pods:           2000,
		CPUInCore:      1000,
		MemoryInGB:     2000,
	})

	assert.Assert(t, x.Less(y))

	z := &BCIResources{}
	err = json.Unmarshal([]byte(`{}`), z) // unlimited
	assert.NilError(t, err)
	assert.DeepEqual(t, z, &BCIResources{
		CPUQuantity:    nil,
		MemoryQuantity: nil,
		Pods:           0,
		CPUInCore:      0,
		MemoryInGB:     0,
	})

	assert.Assert(t, y.Less(*z))

	z = ResourceAdd(&x, y)
	assert.Assert(t, y.Less(*z))

	z = ResourceAdd(nil, y)
	assert.Assert(t, !y.Less(*z))

	z = ResourceAdd(&x, BCIResources{
		CPUInCore: 2000,
	})
	assert.Assert(t, !y.Less(*z))
}

func TestBCIProvider_DeletePod(t *testing.T) {
	type fields struct {
		ctrl        *gomock.Controller
		bciClient   bci.Client
		bciV2Client bciv2.Client
		cluserID    string
		nodeName    string
	}
	type args struct {
		ctx context.Context
		pod *v1.Pod
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// testutils cases.
		{
			name: "only v1 pods",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bciClient := bci.NewMockClient(ctrl)
				bciV2Client := bciv2.NewMockClient(ctrl)
				clusterID := "cce-xxxxxxxx"
				nodeName := "bci-virtual-kubelet"

				bciV2Client.EXPECT().ListPods(gomock.Any(),
					bci.NewMarkerListOption(clusterID, "", "", "", bci.DefaultMaxKeys, nil,
						bci.NewListKeyword(bci.KeywordTypePodName, "default-testutils-pod")), nil).Return(
					&bci.ListPodsResponse{
						Result: []*bci.Pod{
							{
								Name:    "default-testutils-pod",
								PodID:   "p-76tguihk",
								CCEID:   clusterID,
								PodUUID: uuid.NewV4().String(),
								Labels: []bci.PodLabel{
									{
										LabelKey:   NodeNameLabelKey,
										LabelValue: nodeName,
									},
									{
										LabelKey:   PodNameLabelKey,
										LabelValue: "testutils-pod",
									},
									{
										LabelKey:   NamespaceLabelKey,
										LabelValue: "default",
									},
									{
										LabelKey:   UIDLabelKey,
										LabelValue: "testutils-pod-uid",
									},
									{
										LabelKey:   ClusterIDLabelKey,
										LabelValue: "cce-xxxxxxxx",
									},
								},
							},
							{
								Name:    "default-testutils-pod",
								PodID:   "p-xiuuew2j",
								CCEID:   clusterID,
								PodUUID: uuid.NewV4().String(),
								Labels: []bci.PodLabel{
									{
										LabelKey:   NodeNameLabelKey,
										LabelValue: nodeName,
									},
									{
										LabelKey:   PodNameLabelKey,
										LabelValue: "testutils-pod",
									},
									{
										LabelKey:   NamespaceLabelKey,
										LabelValue: "default",
									},
									{
										LabelKey:   UIDLabelKey,
										LabelValue: "testutils-pod-uid",
									},
									{
										LabelKey:   ClusterIDLabelKey,
										LabelValue: "cce-xxxxxxxx",
									},
								},
							},
						},
						MaxKeys:     bci.DefaultMaxKeys,
						IsTruncated: false,
					},
					nil)

				bciClient.EXPECT().DeletePod(gomock.Any(), bci.NewDeleteMultiplePodsArgs([]*bci.DeletePod{
					{
						PodID: "p-76tguihk",
						CCEID: "cce-xxxxxxxx",
					},
					{
						PodID: "p-xiuuew2j",
						CCEID: "cce-xxxxxxxx",
					},
				}, true), nil)

				return fields{
					ctrl:        ctrl,
					bciClient:   bciClient,
					bciV2Client: bciV2Client,
					cluserID:    clusterID,
					nodeName:    nodeName,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				pod: &v1.Pod{
					ObjectMeta: metav1.ObjectMeta{
						Namespace: "default",
						Name:      "testutils-pod",
						UID:       types.UID("testutils-pod-uid"),
					},
				},
			},
		},
		{
			name: "only v2 pods",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bciClient := bci.NewMockClient(ctrl)
				bciV2Client := bciv2.NewMockClient(ctrl)
				clusterID := "cce-xxxxxxxx"
				nodeName := "bci-virtual-kubelet"

				bciV2Client.EXPECT().ListPods(gomock.Any(),
					bci.NewMarkerListOption(clusterID, "", "", "", bci.DefaultMaxKeys, nil,
						bci.NewListKeyword(bci.KeywordTypePodName, "default-testutils-pod")), nil).Return(
					&bci.ListPodsResponse{
						Result: []*bci.Pod{
							{
								Name:    "default-testutils-pod",
								PodID:   "p-76tguihk",
								CCEID:   clusterID,
								PodUUID: uuid.NewV4().String(),
								V2:      true,
								Labels: []bci.PodLabel{
									{
										LabelKey:   NodeNameLabelKey,
										LabelValue: nodeName,
									},
									{
										LabelKey:   PodNameLabelKey,
										LabelValue: "testutils-pod",
									},
									{
										LabelKey:   NamespaceLabelKey,
										LabelValue: "default",
									},
									{
										LabelKey:   UIDLabelKey,
										LabelValue: "testutils-pod-uid",
									},
									{
										LabelKey:   ClusterIDLabelKey,
										LabelValue: "cce-xxxxxxxx",
									},
								},
							},
							{
								Name:    "default-testutils-pod",
								PodID:   "p-xiuuew2j",
								CCEID:   clusterID,
								PodUUID: uuid.NewV4().String(),
								V2:      true,
								Labels: []bci.PodLabel{
									{
										LabelKey:   NodeNameLabelKey,
										LabelValue: nodeName,
									},
									{
										LabelKey:   PodNameLabelKey,
										LabelValue: "testutils-pod",
									},
									{
										LabelKey:   NamespaceLabelKey,
										LabelValue: "default",
									},
									{
										LabelKey:   UIDLabelKey,
										LabelValue: "testutils-pod-uid",
									},
									{
										LabelKey:   ClusterIDLabelKey,
										LabelValue: "cce-xxxxxxxx",
									},
								},
							},
						},
						MaxKeys:     bci.DefaultMaxKeys,
						IsTruncated: false,
					},
					nil)

				bciV2Client.EXPECT().DeletePod(gomock.Any(), bci.NewDeleteMultiplePodsArgs([]*bci.DeletePod{
					{
						PodID: "p-76tguihk",
						CCEID: "cce-xxxxxxxx",
					},
					{
						PodID: "p-xiuuew2j",
						CCEID: "cce-xxxxxxxx",
					},
				}, true), nil)

				return fields{
					ctrl:        ctrl,
					bciClient:   bciClient,
					bciV2Client: bciV2Client,
					cluserID:    clusterID,
					nodeName:    nodeName,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				pod: &v1.Pod{
					ObjectMeta: metav1.ObjectMeta{
						Namespace: "default",
						Name:      "testutils-pod",
						UID:       types.UID("testutils-pod-uid"),
					},
				},
			},
		},
		{
			name: "v1 and v2 pods",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bciClient := bci.NewMockClient(ctrl)
				bciV2Client := bciv2.NewMockClient(ctrl)
				clusterID := "cce-xxxxxxxx"
				nodeName := "bci-virtual-kubelet"

				bciV2Client.EXPECT().ListPods(gomock.Any(),
					bci.NewMarkerListOption(clusterID, "", "", "", bci.DefaultMaxKeys, nil,
						bci.NewListKeyword(bci.KeywordTypePodName, "default-testutils-pod")), nil).Return(
					&bci.ListPodsResponse{
						Result: []*bci.Pod{
							{
								Name:    "default-testutils-pod",
								PodID:   "p-76tguihk",
								CCEID:   clusterID,
								PodUUID: uuid.NewV4().String(),
								Labels: []bci.PodLabel{
									{
										LabelKey:   NodeNameLabelKey,
										LabelValue: nodeName,
									},
									{
										LabelKey:   PodNameLabelKey,
										LabelValue: "testutils-pod",
									},
									{
										LabelKey:   NamespaceLabelKey,
										LabelValue: "default",
									},
									{
										LabelKey:   UIDLabelKey,
										LabelValue: "testutils-pod-uid",
									},
									{
										LabelKey:   ClusterIDLabelKey,
										LabelValue: "cce-xxxxxxxx",
									},
								},
							},
							{
								Name:    "default-testutils-pod",
								PodID:   "p-xiuuew2j",
								CCEID:   clusterID,
								PodUUID: uuid.NewV4().String(),
								V2:      true,
								Labels: []bci.PodLabel{
									{
										LabelKey:   NodeNameLabelKey,
										LabelValue: nodeName,
									},
									{
										LabelKey:   PodNameLabelKey,
										LabelValue: "testutils-pod",
									},
									{
										LabelKey:   NamespaceLabelKey,
										LabelValue: "default",
									},
									{
										LabelKey:   UIDLabelKey,
										LabelValue: "testutils-pod-uid",
									},
									{
										LabelKey:   ClusterIDLabelKey,
										LabelValue: "cce-xxxxxxxx",
									},
								},
							},
						},
						MaxKeys:     bci.DefaultMaxKeys,
						IsTruncated: false,
					},
					nil)

				bciClient.EXPECT().DeletePod(gomock.Any(), bci.NewDeleteMultiplePodsArgs([]*bci.DeletePod{
					{
						PodID: "p-76tguihk",
						CCEID: "cce-xxxxxxxx",
					},
				}, true), nil)
				bciV2Client.EXPECT().DeletePod(gomock.Any(), bci.NewDeleteMultiplePodsArgs([]*bci.DeletePod{
					{
						PodID: "p-xiuuew2j",
						CCEID: "cce-xxxxxxxx",
					},
				}, true), nil)

				return fields{
					ctrl:        ctrl,
					bciClient:   bciClient,
					bciV2Client: bciV2Client,
					cluserID:    clusterID,
					nodeName:    nodeName,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				pod: &v1.Pod{
					ObjectMeta: metav1.ObjectMeta{
						Namespace: "default",
						Name:      "testutils-pod",
						UID:       types.UID("testutils-pod-uid"),
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			p := &BCIProvider{
				bciClient:   tt.fields.bciClient,
				bciV2Client: tt.fields.bciV2Client,
				clusterID:   tt.fields.cluserID,
				nodeName:    tt.fields.nodeName,
			}
			if err := p.DeletePod(tt.args.ctx, tt.args.pod); (err != nil) != tt.wantErr {
				t.Errorf("BCIProvider.DeletePod() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestBCIProvider_GetPod(t *testing.T) {
	type fields struct {
		ctrl        *gomock.Controller
		bciClient   bci.Client
		bciV2Client bciv2.Client
		cluserID    string
		nodeName    string
	}
	type args struct {
		ctx       context.Context
		namespace string
		name      string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *v1.Pod
		wantErr bool
	}{
		// testutils cases.
		{
			name: "v1 pod",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bciClient := bci.NewMockClient(ctrl)
				bciV2Client := bciv2.NewMockClient(ctrl)
				clusterID := "cce-xxxxxxxx"
				nodeName := "bci-virtual-kubelet"

				bciV2Client.EXPECT().ListPods(gomock.Any(),
					bci.NewMarkerListOption(clusterID, "", "", "", bci.DefaultMaxKeys, nil,
						bci.NewListKeyword(bci.KeywordTypePodName, "default-testutils-pod")), nil).Return(
					&bci.ListPodsResponse{
						Result: []*bci.Pod{
							{
								Name:    "default-testutils-pod",
								PodID:   "p-76tguihk",
								CCEID:   clusterID,
								PodUUID: uuid.NewV4().String(),
								Labels: []bci.PodLabel{
									{
										LabelKey:   NodeNameLabelKey,
										LabelValue: nodeName,
									},
									{
										LabelKey:   PodNameLabelKey,
										LabelValue: "testutils-pod",
									},
									{
										LabelKey:   NamespaceLabelKey,
										LabelValue: "default",
									},
									{
										LabelKey:   UIDLabelKey,
										LabelValue: "testutils-pod-uid",
									},
									{
										LabelKey:   ClusterIDLabelKey,
										LabelValue: "cce-xxxxxxxx",
									},
								},
							},
						},
						MaxKeys:     bci.DefaultMaxKeys,
						IsTruncated: false,
					},
					nil)
				bciClient.EXPECT().DescribePod(gomock.Any(), "p-76tguihk", nil).Return(nil, errors.New("some err"))

				return fields{
					ctrl:        ctrl,
					bciClient:   bciClient,
					bciV2Client: bciV2Client,
					cluserID:    clusterID,
					nodeName:    nodeName,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				namespace: "default",
				name:      "testutils-pod",
			},
			wantErr: true,
		},
		{
			name: "v1 pod",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bciClient := bci.NewMockClient(ctrl)
				bciV2Client := bciv2.NewMockClient(ctrl)
				clusterID := "cce-xxxxxxxx"
				nodeName := "bci-virtual-kubelet"

				bciV2Client.EXPECT().ListPods(gomock.Any(),
					bci.NewMarkerListOption(clusterID, "", "", "", bci.DefaultMaxKeys, nil,
						bci.NewListKeyword(bci.KeywordTypePodName, "default-testutils-pod")), nil).Return(
					&bci.ListPodsResponse{
						Result: []*bci.Pod{
							{
								Name:    "default-testutils-pod",
								PodID:   "p-76tguihk",
								CCEID:   clusterID,
								PodUUID: uuid.NewV4().String(),
								V2:      true,
								Labels: []bci.PodLabel{
									{
										LabelKey:   NodeNameLabelKey,
										LabelValue: nodeName,
									},
									{
										LabelKey:   PodNameLabelKey,
										LabelValue: "testutils-pod",
									},
									{
										LabelKey:   NamespaceLabelKey,
										LabelValue: "default",
									},
									{
										LabelKey:   UIDLabelKey,
										LabelValue: "testutils-pod-uid",
									},
									{
										LabelKey:   ClusterIDLabelKey,
										LabelValue: "cce-xxxxxxxx",
									},
								},
							},
						},
						MaxKeys:     bci.DefaultMaxKeys,
						IsTruncated: false,
					},
					nil)
				bciV2Client.EXPECT().DescribePod(gomock.Any(), "p-76tguihk", nil).Return(nil, errors.New("some err"))

				return fields{
					ctrl:        ctrl,
					bciClient:   bciClient,
					bciV2Client: bciV2Client,
					cluserID:    clusterID,
					nodeName:    nodeName,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				namespace: "default",
				name:      "testutils-pod",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl == nil {
				defer tt.fields.ctrl.Finish()
			}
			p := &BCIProvider{
				bciClient:   tt.fields.bciClient,
				bciV2Client: tt.fields.bciV2Client,
				nodeName:    tt.fields.nodeName,
				clusterID:   tt.fields.cluserID,
			}
			got, err := p.GetPod(tt.args.ctx, tt.args.namespace, tt.args.name)
			if (err != nil) != tt.wantErr {
				t.Errorf("BCIProvider.GetPod() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func TestBCIProvider_getUserVersion(t *testing.T) {
	type fields struct {
		ctrl        *gomock.Controller
		bciV2Client bciv2.Client
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		want1   bool
		wantErr bool
	}{
		// TODO: Add testutils cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				v2c := bciv2.NewMockClient(ctrl)
				v2c.EXPECT().GetUserVersion(context.TODO(), nil).Return(&bci.UserVersionResponse{
					IsV2:      true,
					AccountID: "00dc1b52d8354d9193536e4dd2c41ae6",
				}, nil)

				return fields{
					ctrl:        ctrl,
					bciV2Client: v2c,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			want:  "00dc1b52d8354d9193536e4dd2c41ae6",
			want1: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			p := &BCIProvider{
				bciV2Client: tt.fields.bciV2Client,
			}
			got, got1, err := p.getUserVersion(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("BCIProvider.shouldCreateV2() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("BCIProvider.shouldCreateV2() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("BCIProvider.shouldCreateV2() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestAddPodIPEnvSource(t *testing.T) {
	testPod := &v1.Pod{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Pod",
			APIVersion: "v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:              "pod-0",
			Namespace:         "default",
			UID:               types.UID("xxxx-xx-xx-xxxx"),
			CreationTimestamp: metav1.NewTime(time.Unix(**********, 0)),
		},
		Spec: v1.PodSpec{
			NodeName: "bci-vk",
			Volumes:  []v1.Volume{},
			InitContainers: []v1.Container{
				{
					Name:    "init-container-0",
					Image:   "nginx:latest",
					Command: []string{"sleep", "10"},
					Env: []v1.EnvVar{
						{
							Name:  "init-k1",
							Value: "init-v1",
						},
						{
							Name:  "init-k2",
							Value: "init-v2",
						},
					},
				},
			},
			Containers: []v1.Container{
				{
					Name:  "container-0",
					Image: "nginx:latest",
					Env: []v1.EnvVar{
						{
							Name:  "k1",
							Value: "v1",
						},
						{
							Name:  "k2",
							Value: "v2",
						},
					},
				},
			},
		},
	}
	type args struct {
		ctx context.Context
		pod *v1.Pod
	}
	tests := []struct {
		name   string
		args   args
		assert func(*v1.Pod)
	}{
		// testutils cases.
		{
			name: "normal v2 pod",
			args: args{
				ctx: context.TODO(),
				pod: func() *v1.Pod {
					pod := testPod.DeepCopy()
					pod.Annotations = map[string]string{
						PodIPAnnotationKey: func() string {
							v := *NewPodIPAnnotationValue()
							v["init-container-0"] = []string{"init-k1"}
							v["container-0"] = []string{"k1"}
							output, err := json.Marshal(v)
							if err != nil {
								t.Fatal(err)
							}
							return string(output)
						}(),
					}
					return pod
				}(),
			},
			assert: func(pod *v1.Pod) {
				wantEnvSource := &v1.EnvVarSource{
					FieldRef: &v1.ObjectFieldSelector{
						FieldPath: "status.podIP",
					},
				}
				assert.DeepEqual(t, pod.Spec.InitContainers[0].Env[0].ValueFrom, wantEnvSource)
				assert.DeepEqual(t, pod.Spec.Containers[0].Env[0].ValueFrom, wantEnvSource)
				assert.Assert(t, pod.Spec.InitContainers[0].Env[1].ValueFrom == nil)
				assert.Assert(t, pod.Spec.Containers[0].Env[1].ValueFrom == nil)
			},
		},
		{
			name: "v2 pod with invalid annotaion",
			args: args{
				ctx: context.TODO(),
				pod: func() *v1.Pod {
					pod := testPod.DeepCopy()
					pod.Annotations = map[string]string{
						PodIPAnnotationKey: "some-invalid-json",
					}
					return pod
				}(),
			},
			assert: func(pod *v1.Pod) {
				for _, c := range pod.Spec.InitContainers {
					for _, e := range c.Env {
						assert.Assert(t, e.ValueFrom == nil)
					}
				}
				for _, c := range pod.Spec.Containers {
					for _, e := range c.Env {
						assert.Assert(t, e.ValueFrom == nil)
					}
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.assert(addPodIPEnvSource(tt.args.ctx, tt.args.pod))
		})
	}
}

func Test_GetBCIPodMetadataLabels(t *testing.T) {
	pods := []*v1.Pod{
		{
			ObjectMeta: metav1.ObjectMeta{
				Name: "p1",
				Labels: map[string]string{
					"k1": "v1",
					"k2": "v2",
				},
			},
		},
	}

	for _, pod := range pods {
		bciPodLabels := getBCIPodMetadataLabels(pod)
		for _, labels := range bciPodLabels {
			if labels.LabelValue != pod.GetLabels()[labels.LabelKey] {
				t.Errorf("label key %s not found", labels.LabelKey)
			}
		}
	}
}

func TestBCIProvider_CreatePod(t *testing.T) {
	testClock := fakeclock.NewFakeClock(time.Now())
	testNodeName := "bci-vk"
	testClusterID := "cce-test1234"
	testSubnets := map[string]*SubnetOption{
		"sbn-test1234": {
			Weight:      1,
			LogicalZone: "zoneA",
		},
	}
	type fields struct {
		ctrl          *gomock.Controller
		nodeName      string
		clusterID     string
		bciClient     bci.Client
		bciV2Client   bciv2.Client
		userStorage   userstorage.Storage
		subnetOptions map[string]*SubnetOption
		clock         clock.Clock
	}
	type args struct {
		ctx context.Context
		pod *v1.Pod
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add testutils cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bciClient := bci.NewMockClient(ctrl)
				bciClient.EXPECT().CreatePod(gomock.Any(), gomock.Any(), nil, gomock.Any()).
					Return(&bci.CreatePodResponse{
						OrderID: "testutils-order-id",
						PodIDs:  []string{"testutils-pod-id"},
					}, nil)

				userStorage := userstorage.NewMockStorage(ctrl)
				userStorage.EXPECT().AddUser(gomock.Any(), "", testNodeName)

				return fields{
					ctrl:          ctrl,
					nodeName:      testNodeName,
					clusterID:     testClusterID,
					bciClient:     bciClient,
					userStorage:   userStorage,
					subnetOptions: testSubnets,
					clock:         testClock,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				pod: &v1.Pod{
					ObjectMeta: metav1.ObjectMeta{
						Name:              "testutils-pod",
						Namespace:         "default",
						UID:               types.UID("testutils-pod-uid"),
						CreationTimestamp: metav1.NewTime(testClock.Now()),
						Annotations:       map[string]string{"bci.virtual-kubelet.io/resource-tag": "{}"},
					},
					Spec: v1.PodSpec{
						DNSPolicy: v1.DNSDefault,
						NodeName:  testNodeName,
						Containers: []v1.Container{
							{
								Name:  "container-0",
								Image: "nginx",
							},
						},
					},
				},
			},
		},
		/*
			{
				name: "subnet id with empty zone",
				fields: func() fields {
					ctrl := gomock.NewController(t)
					bciClient := bci.NewMockClient(ctrl)
					bciClient.EXPECT().CreatePod(gomock.Any(), testutils.CondEq(&bci.PodConfig{
						Name:          "default-testutils-pod",
						RestartPolicy: bci.RestartPolicyAlways,
						SubnetID:      "sbn-annotation",
						ClientToken:   "testutils-pod-uid",
						CCEID:         testClusterID,
						ServiceType:   bci.ServiceTypeBCI,
						PurchaseNum:   1,
						ProductType:   bci.ProductTypePostPay,
						Labels: []bci.PodLabel{
							{
								LabelKey:   UIDLabelKey,
								LabelValue: "testutils-pod-uid",
							},
							{
								LabelKey:   PodNameLabelKey,
								LabelValue: "testutils-pod",
							},
							{
								LabelKey:   NamespaceLabelKey,
								LabelValue: "default",
							},
							{
								LabelKey:   NodeNameLabelKey,
								LabelValue: testNodeName,
							},
							{
								LabelKey:   ClusterIDLabelKey,
								LabelValue: testClusterID,
							},
							{
								LabelKey:   CreationTimestampLabelKey,
								LabelValue: fmt.Sprintf("%d", testClock.Now().Unix()),
							},
						},
						Volumes: &bci.Volumes{},
						Containers: []bci.Container{
							{
								Name:               "container-0",
								ContainerImageInfo: getContainerImageInfo("nginx"),
								CPUInCore:          1.00,
								MemoryInGB:         2.00,
								ImagePullPolicy:    bci.PullIfNotPresent,
								VolumeMounts:       []bci.VolumeMount{},
								Ports:              []bci.ContainerPort{},
								Envs:               []bci.Env{},
							},
						},
						Annotations: `{"bci.virtual-kubelet.io/container-termination-param":{"container-0":{"terminationMessagePath":"",` +
							`"terminationMessagePolicy":"","containerType":"workload"}}}`,
					}, nil), nil, gomock.Any()).
						Return(&bci.CreatePodResponse{
							OrderID: "testutils-order-id",
							PodIDs:  []string{"testutils-pod-id"},
						}, nil)

					userStorage := userstorage.NewMockStorage(ctrl)
					userStorage.EXPECT().AddUser(gomock.Any(), "", testNodeName)

					return fields{
						ctrl:          ctrl,
						nodeName:      testNodeName,
						clusterID:     testClusterID,
						bciClient:     bciClient,
						userStorage:   userStorage,
						subnetOptions: testSubnets,
						clock:         testClock,
					}
				}(),
				args: args{
					ctx: context.TODO(),
					pod: &v1.Pod{
						ObjectMeta: metav1.ObjectMeta{
							Name:              "testutils-pod",
							Namespace:         "default",
							UID:               types.UID("testutils-pod-uid"),
							CreationTimestamp: metav1.NewTime(testClock.Now()),
							Annotations: map[string]string{
								BCISubnetIDAnnotationKey: "sbn-annotation",
							},
						},
						Spec: v1.PodSpec{
							DNSPolicy: v1.DNSDefault,
							NodeName:  testNodeName,
							Containers: []v1.Container{
								{
									Name:  "container-0",
									Image: "nginx",
								},
							},
						},
					},
				},
			},
			{
				name: "multiple subnet ids set in pod",
				fields: func() fields {
					ctrl := gomock.NewController(t)
					bciClient := bci.NewMockClient(ctrl)
					bciClient.EXPECT().CreatePod(gomock.Any(), testutils.CondEq(&bci.PodConfig{
						Name:          "default-testutils-pod",
						RestartPolicy: bci.RestartPolicyAlways,
						SubnetIDs:     "sbn-1,sbn-2,sbn-3",
						ClientToken:   "testutils-pod-uid",
						CCEID:         testClusterID,
						ServiceType:   bci.ServiceTypeBCI,
						PurchaseNum:   1,
						ProductType:   bci.ProductTypePostPay,
						Labels: []bci.PodLabel{
							{
								LabelKey:   UIDLabelKey,
								LabelValue: "testutils-pod-uid",
							},
							{
								LabelKey:   PodNameLabelKey,
								LabelValue: "testutils-pod",
							},
							{
								LabelKey:   NamespaceLabelKey,
								LabelValue: "default",
							},
							{
								LabelKey:   NodeNameLabelKey,
								LabelValue: testNodeName,
							},
							{
								LabelKey:   ClusterIDLabelKey,
								LabelValue: testClusterID,
							},
							{
								LabelKey:   CreationTimestampLabelKey,
								LabelValue: fmt.Sprintf("%d", testClock.Now().Unix()),
							},
						},
						Volumes: &bci.Volumes{},
						Containers: []bci.Container{
							{
								Name:               "container-0",
								ContainerImageInfo: getContainerImageInfo("nginx"),
								CPUInCore:          1.00,
								MemoryInGB:         2.00,
								ImagePullPolicy:    bci.PullIfNotPresent,
								VolumeMounts:       []bci.VolumeMount{},
								Ports:              []bci.ContainerPort{},
								Envs:               []bci.Env{},
							},
						},
						Annotations: `{"bci.virtual-kubelet.io/container-termination-param":{"container-0":{"terminationMessagePath":"",` +
							`"terminationMessagePolicy":"","containerType":"workload"}}}`,
					}, nil), nil, gomock.Any()).
						Return(&bci.CreatePodResponse{
							OrderID: "testutils-order-id",
							PodIDs:  []string{"testutils-pod-id"},
						}, nil)

					userStorage := userstorage.NewMockStorage(ctrl)
					userStorage.EXPECT().AddUser(gomock.Any(), "", testNodeName)

					return fields{
						ctrl:          ctrl,
						nodeName:      testNodeName,
						clusterID:     testClusterID,
						bciClient:     bciClient,
						userStorage:   userStorage,
						subnetOptions: testSubnets,
						clock:         testClock,
					}
				}(),
				args: args{
					ctx: context.TODO(),
					pod: &v1.Pod{
						ObjectMeta: metav1.ObjectMeta{
							Name:      "testutils-pod",
							Namespace: "default",
							UID:       types.UID("testutils-pod-uid"),
							Annotations: map[string]string{
								BCISubnetIDsAnnotationKey: "sbn-1,sbn-2,sbn-3",
							},
							CreationTimestamp: metav1.NewTime(testClock.Now()),
						},
						Spec: v1.PodSpec{
							DNSPolicy: v1.DNSDefault,
							NodeName:  testNodeName,
							Containers: []v1.Container{
								{
									Name:  "container-0",
									Image: "nginx",
								},
							},
						},
					},
				},
			},
		*/
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &BCIProvider{
				nodeName:      tt.fields.nodeName,
				clusterID:     tt.fields.clusterID,
				bciClient:     tt.fields.bciClient,
				bciV2Client:   tt.fields.bciV2Client,
				userStorage:   tt.fields.userStorage,
				subnetOptions: tt.fields.subnetOptions,
				clock:         tt.fields.clock,
			}
			if err := p.CreatePod(tt.args.ctx, tt.args.pod); (err != nil) != tt.wantErr {
				t.Errorf("BCIProvider.CreatePod() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_getMaxReqInFlight(t *testing.T) {
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		// TODO: Add testutils cases.
		{
			name: "default",
			args: args{
				ctx: context.TODO(),
			},
			want: 128,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getMaxReqInFlight(tt.args.ctx); got != tt.want {
				t.Errorf("getMaxReqInFlight() = %v, want %v", got, tt.want)
			}
		})
	}
}

type MockEventSinker struct {
	run chan<- struct{}
}

func (mes *MockEventSinker) Run(context.Context) {
	mes.run <- struct{}{}
}

func TestBCIProvider_startAsyncWorks(t *testing.T) {
	run := make(chan struct{}, 10)
	type fields struct {
		ctrl        *gomock.Controller
		podCache    PodCache
		eventSinker EventSinker
	}
	type args struct {
		ctx context.Context
	}

	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// All testutils cases.
		{
			name: "pod cache is enabled",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				podCache := NewMockPodCache(ctrl)
				podCache.EXPECT().Start(gomock.Any())

				return fields{
					ctrl:        ctrl,
					podCache:    podCache,
					eventSinker: &MockEventSinker{run},
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
		},
		{
			name: "pod cache is disabled",
			fields: fields{
				eventSinker: &MockEventSinker{run},
			},
			args: args{
				ctx: context.TODO(),
			},
		},
		{
			name: "pod cache is disabled and mock process exit",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				podCache := NewMockPodCache(ctrl)
				podCache.EXPECT().Start(gomock.Any()).Return(errors.New("mock an initial error of pod cache"))
				return fields{
					ctrl:        ctrl,
					podCache:    podCache,
					eventSinker: &MockEventSinker{run},
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
		},
	}

	// 保存原始的 exitfunc
	originalExitFunc := exitfunc
	// 在测试结束时恢复原始的 exitfunc
	defer func() { exitfunc = originalExitFunc }()
	// 替换 exitfunc 为一个空函数
	exitfunc = func() {}

	for _, tt := range tests {
		if tt.fields.ctrl != nil {
			defer tt.fields.ctrl.Finish()
		}
		t.Run(tt.name, func(t *testing.T) {
			p := &BCIProvider{
				podCache:    tt.fields.podCache,
				eventSinker: tt.fields.eventSinker,
			}
			p.startAsyncWorks(tt.args.ctx)
			<-run
		})
	}
}

func TestBCIProvider_reportPodsToBCI(t *testing.T) {
	type fields struct {
		ctrl            *gomock.Controller
		bciClient       bci.Client
		bciV2Client     bciv2.Client
		createV2        bool
		resourceManager manager.ResourceManager
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "report pods success",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bciClient := bci.NewMockClient(ctrl)
				bciClient.EXPECT().ReportPods(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				bciV2Client := bciv2.NewMockClient(ctrl)
				bciV2Client.EXPECT().ReportPods(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				rm := manager.NewMockResourceManager(ctrl)
				rm.EXPECT().GetPods().Return([]*v1.Pod{
					{
						ObjectMeta: metav1.ObjectMeta{
							Name:      "pod-0",
							Namespace: "default",
							UID:       types.UID("testutils-pod-uid"),
							Annotations: map[string]string{
								PodIDAnnotationKey: "p-as7xdei8",
							},
							//ClusterName: "testutils",
						},
						Status: v1.PodStatus{
							Phase: v1.PodRunning,
						},
					},
				}).AnyTimes()
				return fields{
					ctrl:            ctrl,
					bciClient:       bciClient,
					bciV2Client:     bciV2Client,
					resourceManager: rm,
					createV2:        true,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		if tt.fields.ctrl != nil {
			defer tt.fields.ctrl.Finish()
		}
		t.Run(tt.name, func(t *testing.T) {
			p := &BCIProvider{
				bciClient:       tt.fields.bciClient,
				bciV2Client:     tt.fields.bciV2Client,
				resourceManager: tt.fields.resourceManager,
				createV2:        tt.fields.createV2,
			}
			p.reportPodsToBci(tt.args.ctx)
			tt.args.ctx.Done()
		})
	}
}

func TestBCIProvider_GetPodDetail(t *testing.T) {
	type fields struct {
		ctrl           *gomock.Controller
		bciClient      bci.Client
		bciV2Client    bciv2.Client
		podCache       PodCache
		cluserID       string
		nodeName       string
		enablePodCache bool
	}
	type args struct {
		ctx       context.Context
		namespace string
		name      string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *v1.Pod
		wantErr bool
	}{
		{
			name: "enable podcache",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				podMockCache := NewMockPodCache(ctrl)
				podMockCache.EXPECT().GetPodDetail(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&bci.DescribePodResponse{},
					nil)
				bciClient := bci.NewMockClient(ctrl)
				bciV2Client := bciv2.NewMockClient(ctrl)
				clusterID := "cce-xxxxxxxx"
				nodeName := "bci-virtual-kubelet"

				return fields{
					ctrl:           ctrl,
					bciClient:      bciClient,
					bciV2Client:    bciV2Client,
					cluserID:       clusterID,
					nodeName:       nodeName,
					enablePodCache: true,
					podCache:       podMockCache,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				namespace: "default",
				name:      "test-pod",
			},
			wantErr: false,
		},
		{
			name: "unable podcache",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bciClient := bci.NewMockClient(ctrl)
				bciV2Client := bciv2.NewMockClient(ctrl)
				clusterID := "cce-xxxxxxxx"
				nodeName := "bci-virtual-kubelet"

				bciV2Client.EXPECT().ListPods(gomock.Any(),
					bci.NewMarkerListOption(clusterID, "", "", "", bci.DefaultMaxKeys, nil,
						bci.NewListKeyword(bci.KeywordTypePodName, "default-test-pod")), nil).Return(
					&bci.ListPodsResponse{
						Result: []*bci.Pod{
							{
								Name:    "default-test-pod",
								PodID:   "p-76tguihk",
								CCEID:   clusterID,
								PodUUID: uuid.NewV4().String(),
								Labels: []bci.PodLabel{
									{
										LabelKey:   NodeNameLabelKey,
										LabelValue: nodeName,
									},
									{
										LabelKey:   PodNameLabelKey,
										LabelValue: "test-pod",
									},
									{
										LabelKey:   NamespaceLabelKey,
										LabelValue: "default",
									},
									{
										LabelKey:   UIDLabelKey,
										LabelValue: "test-pod-uid",
									},
									{
										LabelKey:   ClusterIDLabelKey,
										LabelValue: "cce-xxxxxxxx",
									},
								},
							},
						},
						MaxKeys:     bci.DefaultMaxKeys,
						IsTruncated: false,
					},
					nil)
				bciClient.EXPECT().DescribePod(gomock.Any(), "p-76tguihk", nil).Return(nil, nil)
				return fields{
					ctrl:           ctrl,
					bciClient:      bciClient,
					bciV2Client:    bciV2Client,
					cluserID:       clusterID,
					nodeName:       nodeName,
					enablePodCache: false,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				namespace: "default",
				name:      "test-pod",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl == nil {
				defer tt.fields.ctrl.Finish()
			}
			p := &BCIProvider{
				bciClient:      tt.fields.bciClient,
				bciV2Client:    tt.fields.bciV2Client,
				nodeName:       tt.fields.nodeName,
				clusterID:      tt.fields.cluserID,
				enablePodCache: tt.fields.enablePodCache,
				podCache:       tt.fields.podCache,
			}
			_, err := p.GetPodDetail(tt.args.ctx, tt.args.namespace, tt.args.name)
			if (err != nil) != tt.wantErr {
				t.Errorf("BCIProvider.GetPod() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestBCIProvider_UpdateDsContainers(t *testing.T) {
	type fields struct {
		ctrl           *gomock.Controller
		bciClient      bci.Client
		bciV2Client    bciv2.Client
		podCache       PodCache
		cluserID       string
		nodeName       string
		enablePodCache bool
	}
	type args struct {
		ctx              context.Context
		injectContainers *bci.InjectDsContainersRequest
		pod              *v1.Pod
	}
	pod := &v1.Pod{}
	pod.Name = "test-pod"
	pod.Namespace = "default"

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *v1.Pod
		wantErr bool
	}{
		{
			name: "update ds containers",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bciClient := bci.NewMockClient(ctrl)
				bciV2Client := bciv2.NewMockClient(ctrl)
				clusterID := "cce-xxxxxxxx"
				nodeName := "bci-virtual-kubelet"

				bciV2Client.EXPECT().ListPods(gomock.Any(),
					gomock.Any(), nil).Return(
					&bci.ListPodsResponse{
						Result: []*bci.Pod{
							{
								Name:    "default-test-pod",
								PodID:   "p-76tguihk",
								CCEID:   clusterID,
								PodUUID: uuid.NewV4().String(),
								V2:      true,
								Labels: []bci.PodLabel{
									{
										LabelKey:   NodeNameLabelKey,
										LabelValue: nodeName,
									},
									{
										LabelKey:   PodNameLabelKey,
										LabelValue: "test-pod",
									},
									{
										LabelKey:   NamespaceLabelKey,
										LabelValue: "default",
									},
									{
										LabelKey:   UIDLabelKey,
										LabelValue: "test-pod-uid",
									},
									{
										LabelKey:   ClusterIDLabelKey,
										LabelValue: "cce-xxxxxxxx",
									},
								},
								Status: bci.PodStatusRunning,
							},
						},
						MaxKeys:     bci.DefaultMaxKeys,
						IsTruncated: false,
					},
					nil)
				bciV2Client.EXPECT().UpdateDsContainers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				return fields{
					ctrl:           ctrl,
					bciClient:      bciClient,
					bciV2Client:    bciV2Client,
					cluserID:       clusterID,
					nodeName:       nodeName,
					enablePodCache: false,
				}
			}(),
			args: args{
				ctx:              context.TODO(),
				injectContainers: &bci.InjectDsContainersRequest{},
				pod:              pod,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl == nil {
				defer tt.fields.ctrl.Finish()
			}
			p := &BCIProvider{
				bciClient:      tt.fields.bciClient,
				bciV2Client:    tt.fields.bciV2Client,
				nodeName:       tt.fields.nodeName,
				clusterID:      tt.fields.cluserID,
				enablePodCache: tt.fields.enablePodCache,
				podCache:       tt.fields.podCache,
			}
			err := p.UpdateDsContainers(tt.args.ctx, tt.args.injectContainers, tt.args.pod)
			if (err != nil) != tt.wantErr {
				t.Errorf("BCIProvider.GetPod() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}

}

func TestBCIProvider_getDsInfo(t *testing.T) {
	type fields struct {
		ctrl            *gomock.Controller
		resourceManager manager.ResourceManager
	}
	pod := &v1.Pod{}
	pod.Name = "test-pod"
	pod.Namespace = "default"
	pod.Annotations = map[string]string{"bci.virtual-kubelet.io/pod-inject-ds": "test-ds"}
	ds := appsv1.DaemonSet{
		Spec: appsv1.DaemonSetSpec{
			Template: v1.PodTemplateSpec{
				Spec: v1.PodSpec{
					Containers: []v1.Container{
						{
							Name:  "my-ds",
							Image: "my-image",
						},
					},
				},
			},
		},
	}
	ds.Name = "test-ds"
	ds.Namespace = "default"

	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "nomal case",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				rm := manager.NewMockResourceManager(ctrl)
				rm.EXPECT().GetDaemonset(gomock.Any(), gomock.Any()).Return(&ds, nil)
				return fields{
					ctrl:            ctrl,
					resourceManager: rm,
				}
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			p := &BCIProvider{
				resourceManager: tt.fields.resourceManager,
			}
			_, _, err := p.getDsInfo(context.TODO(), pod)
			if (err != nil) != tt.wantErr {
				t.Errorf("BCIProvider.getDsInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
