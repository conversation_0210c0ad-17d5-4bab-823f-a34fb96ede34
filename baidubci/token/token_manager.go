package token

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"strings"
	"sync"
	"time"

	authenticationv1 "k8s.io/api/authentication/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	clientset "k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"
	"k8s.io/utils/clock"
)

const (
	maxTTL    = 24 * time.Hour
	gcPeriod  = time.Minute
	maxJitter = 10 * time.Second
)

// Manager manages service account tokens for pods.
type Manager struct {
	// cacheMutex guards the cache
	cacheMutex sync.RWMutex
	// cache is a map from key to TokenRequest
	cache map[string]*authenticationv1.TokenRequest

	// name: serviceAccountName, namespace: pod.Namespace
	getToken func(name, namespace string, tr *authenticationv1.TokenRequest) (*authenticationv1.TokenRequest, error)

	clock clock.Clock
}

func NewManager(c clientset.Interface) *Manager {
	// check whether the server supports token requests so we can give a more helpful error message
	supported := false
	once := &sync.Once{}
	tokenRequestsSupported := func() bool {
		once.Do(func() {
			resources, err := c.Discovery().ServerResourcesForGroupVersion("v1")
			if err != nil {
				return
			}
			for _, resource := range resources.APIResources {
				if resource.Name == "serviceaccounts/token" {
					supported = true
					return
				}
			}
		})
		return supported
	}

	m := &Manager{
		getToken: func(name, namespace string, tr *authenticationv1.TokenRequest) (*authenticationv1.TokenRequest, error) {
			if c == nil {
				return nil, errors.New("cannot use TokenManager when kubelet is in standalone mode")
			}
			tokenRequest, err := c.CoreV1().ServiceAccounts(namespace).CreateToken(context.TODO(), name, tr, metav1.CreateOptions{})
			if apierrors.IsNotFound(err) && !tokenRequestsSupported() {
				return nil, errors.New("the API server does not have TokenRequest endpoints enabled")
			}
			return tokenRequest, err
		},
		cache: make(map[string]*authenticationv1.TokenRequest),
		clock: clock.RealClock{},
	}
	return m
}

// GetServiceAccountToken gets a service account token for a pod from cache or
// from the TokenRequest API. This process is as follows:
// * Check the cache for the current token request.
// * If the token exists and does not require a refresh, return the current token.
// * Attempt to refresh the token.
// * If the token is refreshed successfully, save it in the cache and return the token.
// * If refresh fails and the old token is still valid, log an error and return the old token.
// * If refresh fails and the old token is no longer valid, return an error
func (m *Manager) GetServiceAccountToken(namespace, name, key string, tr *authenticationv1.TokenRequest) (*authenticationv1.TokenRequest, error) {
	ctr, ok := m.get(key)

	if ok && ctr != nil && !m.requiresRefresh(ctr) {
		return ctr, nil
	}

	tr, err := m.getToken(name, namespace, tr)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch token: %v, err %w", key, err)
	}

	m.set(key, tr)
	return tr, nil
}

// DeleteServiceAccountToken should be invoked when pod got deleted. It simply
// clean token manager cache.
func (m *Manager) DeleteServiceAccountToken(podUID types.UID) {
	m.cacheMutex.Lock()
	defer m.cacheMutex.Unlock()
	for k, tr := range m.cache {
		if tr != nil && (tr.Spec.BoundObjectRef.UID == podUID) {
			delete(m.cache, k)
		}
	}
}

func (m *Manager) DeleteServiceAccountTokenByTokenKey(tokenKey string) {
	if _, ok := m.cache[tokenKey]; ok {
		delete(m.cache, tokenKey)
	}
}

func (m *Manager) GetServiceAccountTokenFromCache(tokenKey string) *authenticationv1.TokenRequest {
	ctr, _ := m.get(tokenKey)
	return ctr
}

func (m *Manager) SetServiceAccountToken(tokenKey string, expireTime int64) {
	// Load the time zone location
	loc, err := time.LoadLocation("Asia/Shanghai") // Replace with the appropriate time zone
	if err != nil {
		// Fallback to UTC if loading the specific timezone fails
		loc = time.UTC
	}
	expTime := time.Unix(expireTime, 0).In(loc)
	metaTime := metav1.NewTime(expTime)
	_, ok := m.get(tokenKey)
	if !ok {
		podName := ""
		podUID := types.UID("")
		tokenSlice := strings.Split(tokenKey, "/")
		if len(tokenSlice) >= 6 {
			podName = tokenSlice[1]
			podUID = types.UID(tokenSlice[5])
		}
		tr := &authenticationv1.TokenRequest{
			Spec: authenticationv1.TokenRequestSpec{
				ExpirationSeconds: &expireTime,
				BoundObjectRef: &authenticationv1.BoundObjectReference{
					APIVersion: "v1",
					Kind:       "pod",
					Name:       podName,
					UID:        podUID,
				},
			},
			Status: authenticationv1.TokenRequestStatus{
				ExpirationTimestamp: metaTime,
			},
		}
		m.set(tokenKey, tr)
	}
	return
}

func (m *Manager) IsServiceAccountTokenExpired(tokenKey string) bool {
	ctr, _ := m.get(tokenKey)
	if ctr != nil {
		return m.requiresRefresh(ctr)
	}
	return false
}

func (m *Manager) cleanup() {
	m.cacheMutex.Lock()
	defer m.cacheMutex.Unlock()
	for k, tr := range m.cache {
		if m.expired(tr) {
			delete(m.cache, k)
		}
	}
}

func (m *Manager) get(key string) (*authenticationv1.TokenRequest, bool) {
	m.cacheMutex.RLock()
	defer m.cacheMutex.RUnlock()
	ctr, ok := m.cache[key]
	return ctr, ok
}

func (m *Manager) set(key string, tr *authenticationv1.TokenRequest) {
	m.cacheMutex.Lock()
	defer m.cacheMutex.Unlock()
	m.cache[key] = tr
}

func (m *Manager) expired(t *authenticationv1.TokenRequest) bool {
	return m.clock.Now().After(t.Status.ExpirationTimestamp.Time)
}

// requiresRefresh returns true if the token is older than 80% of its total
// ttl, or if the token is older than 24 hours.
func (m *Manager) requiresRefresh(tr *authenticationv1.TokenRequest) bool {
	if tr.Spec.ExpirationSeconds == nil {
		cpy := tr.DeepCopy()
		cpy.Status.Token = ""
		klog.ErrorS(nil, "Expiration seconds was nil for token request", "tokenRequest", cpy)
		return false
	}
	now := m.clock.Now()
	exp := tr.Status.ExpirationTimestamp.Time
	iat := exp.Add(-1 * time.Duration(*tr.Spec.ExpirationSeconds) * time.Second)

	jitter := time.Duration(rand.Float64()*maxJitter.Seconds()) * time.Second
	if now.After(iat.Add(maxTTL - jitter)) {
		return true
	}
	// Require a refresh if within 20% of the TTL plus a jitter from the expiration time.
	if now.After(exp.Add(-1*time.Duration((*tr.Spec.ExpirationSeconds*20)/100)*time.Second - jitter)) {
		return true
	}
	return false
}

// keys should be nonconfidential and safe to log
func keyFunc(name, namespace string, tr *authenticationv1.TokenRequest) string {
	var exp int64
	if tr.Spec.ExpirationSeconds != nil {
		exp = *tr.Spec.ExpirationSeconds
	}

	var ref authenticationv1.BoundObjectReference
	var podName string
	if tr.Spec.BoundObjectRef != nil {
		ref = *tr.Spec.BoundObjectRef
		podName = tr.Spec.BoundObjectRef.Name
	}

	// serviceAccountTokenName/podNamespace/podName/token.Audiences/ecp/ref
	return fmt.Sprintf("%v/%v/%v/%#v/%#v/%#v", name, namespace, podName, tr.Spec.Audiences, exp, ref)
}
