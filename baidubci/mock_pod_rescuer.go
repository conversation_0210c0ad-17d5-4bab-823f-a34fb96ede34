// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci (interfaces: PodRescuer)

// Package baidu_bci is a generated GoMock package.
package baidubci

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockPodRescuer is a mock of PodRescuer interface.
type MockPodRescuer struct {
	ctrl     *gomock.Controller
	recorder *MockPodRescuerMockRecorder
}

// MockPodRescuerMockRecorder is the mock recorder for MockPodRescuer.
type MockPodRescuerMockRecorder struct {
	mock *MockPodRescuer
}

// NewMockPodRescuer creates a new mock instance.
func NewMockPodRescuer(ctrl *gomock.Controller) *MockPodRescuer {
	mock := &MockPodRescuer{ctrl: ctrl}
	mock.recorder = &MockPodRescuerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPodRescuer) EXPECT() *MockPodRescuerMockRecorder {
	return m.recorder
}

// IsRescuing mocks base method.
func (m *MockPodRescuer) IsRescuing(arg0 context.Context, arg1, arg2, arg3 string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsRescuing", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsRescuing indicates an expected call of IsRescuing.
func (mr *MockPodRescuerMockRecorder) IsRescuing(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsRescuing", reflect.TypeOf((*MockPodRescuer)(nil).IsRescuing), arg0, arg1, arg2, arg3)
}

// NotifyBCICreated mocks base method.
func (m *MockPodRescuer) NotifyBCICreated(arg0 context.Context, arg1, arg2, arg3 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "NotifyBCICreated", arg0, arg1, arg2, arg3)
}

// NotifyBCICreated indicates an expected call of NotifyBCICreated.
func (mr *MockPodRescuerMockRecorder) NotifyBCICreated(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NotifyBCICreated", reflect.TypeOf((*MockPodRescuer)(nil).NotifyBCICreated), arg0, arg1, arg2, arg3)
}

// RescuePod mocks base method.
func (m *MockPodRescuer) RescuePod(arg0 context.Context, arg1, arg2, arg3 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RescuePod", arg0, arg1, arg2, arg3)
}

// RescuePod indicates an expected call of RescuePod.
func (mr *MockPodRescuerMockRecorder) RescuePod(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RescuePod", reflect.TypeOf((*MockPodRescuer)(nil).RescuePod), arg0, arg1, arg2, arg3)
}

// Run mocks base method.
func (m *MockPodRescuer) Run(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Run", arg0)
}

// Run indicates an expected call of Run.
func (mr *MockPodRescuerMockRecorder) Run(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Run", reflect.TypeOf((*MockPodRescuer)(nil).Run), arg0)
}
