package baidubci

import (
	"context"
	"errors"
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/google/go-cmp/cmp"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	k8serr "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/apimachinery/pkg/util/wait"
	appsinformers "k8s.io/client-go/informers/apps/v1"
	coreinformers "k8s.io/client-go/informers/core/v1"
	clientset "k8s.io/client-go/kubernetes"
	appsv1client "k8s.io/client-go/kubernetes/typed/apps/v1"
	corev1client "k8s.io/client-go/kubernetes/typed/core/v1"
	appslisters "k8s.io/client-go/listers/apps/v1"
	corelisters "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/record"
	"k8s.io/client-go/util/workqueue"
	"k8s.io/klog"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/manager"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/provider"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
)

const (
	DefaultSyncDsWorkers = 20
	DefaultTimeOut       = 10 * time.Minute

	PodInjectDsAnnotationKey = "bci.virtual-kubelet.io/pod-inject-ds" // 需注入ds的bci pod标识，dsNamespace1/dsName1
	DsInjectionAnnotationKey = "bci.virtual-kubelet.io/ds-injection"  // ds pod标识 true or false

	PodConditionType   = "Ready"
	PodConditionStatus = "True"

	DsContainersEventUpdateFailed = "ProviderUpdateDsContainersFailed"

	DsVolumeUnsupportedErr = "Unsupported volume type"
	DsContainersUpdateErr  = "Status Code: 400"

	EnableDsRollingUpgrade = false // ds滚动升级开关
)

type DsInjector struct {
	provider provider.Provider

	dsInformer appsinformers.DaemonSetInformer
	dsLister   appslisters.DaemonSetLister
	dsClient   appsv1client.DaemonSetsGetter

	podInformer coreinformers.PodInformer
	podLister   corelisters.PodLister
	podClient   corev1client.PodsGetter

	resourceManager manager.ResourceManager

	injectDsContainersQueue workqueue.RateLimitingInterface
	workers                 int
	enableDsRollingUpgrade  bool

	syncDsPodQueue workqueue.RateLimitingInterface
}

func NewDsInjector(provider provider.Provider, client clientset.Interface, dsInformer appsinformers.DaemonSetInformer,
	podInformer coreinformers.PodInformer, rm manager.ResourceManager) (*DsInjector, error) {
	if dsInformer == nil {
		return nil, errors.New("daemonSetInformer can not be nil")
	}
	if podInformer == nil {
		return nil, errors.New("podInformer can not be nil")
	}
	if provider == nil {
		return nil, errors.New("provider can not be nil")
	}
	if client == nil {
		return nil, errors.New("client can not be nil")
	}
	if client.AppsV1() == nil || client.CoreV1() == nil {
		return nil, errors.New("daemonset client or pod client can not be nil")
	}
	dsc := &DsInjector{
		provider:               provider,
		dsClient:               client.AppsV1(),
		dsInformer:             dsInformer,
		podClient:              client.CoreV1(),
		podInformer:            podInformer,
		resourceManager:        rm,
		workers:                DefaultSyncDsWorkers,
		enableDsRollingUpgrade: EnableDsRollingUpgrade,
	}

	if v := os.Getenv("ENABLE_DS_ROLLING_UPGRADE"); v != "" {
		enable, err := strconv.ParseBool(v)
		if err != nil {
			return nil, errors.New("get env fail, ENABLE_DS_ROLLING_UPGRADE env is not a bool")
		}
		dsc.enableDsRollingUpgrade = enable
	}

	if v := os.Getenv("SYNC_DAEMONSET_WORKERS"); v != "" {
		workers, err := strconv.Atoi(v)
		if err != nil {
			return nil, errors.New("get env fail, SYNC_DAEMONSET_WORKERS env is not a number")
		}
		dsc.workers = workers
	}

	dsc.dsLister = dsInformer.Lister()
	dsc.podLister = podInformer.Lister()
	dsc.injectDsContainersQueue = workqueue.NewNamedRateLimitingQueue(workqueue.DefaultControllerRateLimiter(), "injectDsContainers")
	dsc.syncDsPodQueue = workqueue.NewNamedRateLimitingQueue(workqueue.DefaultControllerRateLimiter(), "syncDsPod")
	return dsc, nil
}

func (dsc *DsInjector) Run(ctx context.Context) error {
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	// Wait for the caches to be synced *before* starting to do work.
	if ok := cache.WaitForCacheSync(ctx.Done(), dsc.podInformer.Informer().HasSynced); !ok {
		return errors.New("pod informer cache is not synced")
	}
	// Wait for the caches to be synced *before* starting to do work.
	if ok := cache.WaitForCacheSync(ctx.Done(), dsc.dsInformer.Informer().HasSynced); !ok {
		return errors.New("daemon set informer cache is not synced")
	}

	var podEventHandler cache.ResourceEventHandler = cache.ResourceEventHandlerFuncs{
		AddFunc:    dsc.addPod,
		UpdateFunc: dsc.updatePod,
	}
	podEventHandler = cache.FilteringResourceEventHandler{
		FilterFunc: func(obj interface{}) bool {
			p, ok := obj.(*corev1.Pod)
			if !ok {
				return false
			}
			// filter ds pod
			_, ok1 := p.GetAnnotations()[DsInjectionAnnotationKey]
			// filter bci pod
			_, ok2 := p.GetAnnotations()[PodInjectDsAnnotationKey]
			if !ok1 && !ok2 {
				return false
			}
			// only support bci v3 pod
			//bciv3Lable := p.GetLabels()[Bci3LabelKey]
			//if ok2 && bciv3Lable != "true" {
			//	return false
			//}
			restartPolicy := p.Spec.RestartPolicy
			dsNamespace := p.GetNamespace()
			if ok2 && (restartPolicy != corev1.RestartPolicyAlways || dsNamespace == "kube-system") {
				return false
			}
			return true
		},
		Handler: podEventHandler,
	}
	dsc.podInformer.Informer().AddEventHandler(podEventHandler) //nolint:errcheck

	var dsEventHandler cache.ResourceEventHandler = cache.ResourceEventHandlerFuncs{}
	if dsc.enableDsRollingUpgrade {
		dsEventHandler = cache.ResourceEventHandlerFuncs{
			AddFunc:    dsc.addDaemonset,
			UpdateFunc: dsc.updateDaemonset,
			DeleteFunc: dsc.deleteDaemonset,
		}
	}
	dsEventHandler = cache.FilteringResourceEventHandler{
		FilterFunc: func(obj interface{}) bool {
			ds, ok := obj.(*appsv1.DaemonSet)
			if !ok {
				return false
			}
			// filter ds pod
			if _, ok = ds.Spec.Template.GetAnnotations()[DsInjectionAnnotationKey]; !ok {
				return false
			}
			return true
		},
		Handler: dsEventHandler,
	}
	dsc.dsInformer.Informer().AddEventHandler(dsEventHandler) //nolint:errcheck

	defer utilruntime.HandleCrash()
	defer dsc.injectDsContainersQueue.ShutDown()
	defer dsc.syncDsPodQueue.ShutDown()

	// Initialize on reboot
	dsc.InitializePods()

	klog.Infof("DsInjector: starting workers")
	defer klog.Infof("DsInjector: shutting dsInjector finished workers")
	// start workers to update ds container
	for i := 0; i < dsc.workers; i++ {
		go wait.UntilWithContext(ctx, dsc.runWorker, time.Second)
	}

	// start workers to sync ds pod status
	for i := 0; i < dsc.workers; i++ {
		go wait.UntilWithContext(ctx, dsc.syncWork, time.Second)
	}

	<-ctx.Done()
	return nil
}

func (dsc *DsInjector) InitializePods() {
	pods, err := dsc.podLister.List(labels.Everything())
	if err != nil {
		klog.Errorf("DsInjector: initialize pods failed err %v", err)
		return
	}

	for _, pod := range pods {
		if _, ok := pod.GetAnnotations()[DsInjectionAnnotationKey]; ok {
			if key, err := cache.MetaNamespaceKeyFunc(pod); err != nil {
				klog.Errorf("DsInjector: Failed to get key for ds pod %v when initialing err %v", pod.Name, err)
			} else {
				dsc.syncDsPodQueue.Add(key)
			}
		}

		if _, ok := pod.GetAnnotations()[PodInjectDsAnnotationKey]; ok {
			if key, err := cache.MetaNamespaceKeyFunc(pod); err != nil {
				klog.Errorf("DsInjector: Failed to get key for bci pod %v when initialing err %v", pod.Name, err)
			} else {
				dsc.injectDsContainersQueue.Add(key)
			}
		}
	}
}

// syncWork: 更新ds pod status
func (dsc *DsInjector) syncWork(ctx context.Context) {
	for dsc.processNextSyncWorkItem(ctx) {
	}
}

func (dsc *DsInjector) processNextSyncWorkItem(ctx context.Context) bool {
	klog.Infof("DsInjector: start update ds pod status ")
	podKey, quit := dsc.syncDsPodQueue.Get()
	if quit {
		klog.Errorf("DsInjector: syncQueue quit")
		return false
	}
	defer dsc.syncDsPodQueue.Done(podKey)

	// update ds pod status
	err := dsc.updateDsPodStatus(ctx, podKey.(string))
	if err == nil {
		klog.Infof("DsInjector: update ds pod status success ds pod %v", podKey)
		dsc.syncDsPodQueue.Forget(podKey)
		return true
	}

	klog.Errorf("DsInjector: update ds pod status fail ds pod %v", podKey)
	dsc.syncDsPodQueue.AddRateLimited(podKey)
	return true
}

// updateDsPodStatus： 更新ds pod status
func (dsc *DsInjector) updateDsPodStatus(ctx context.Context, key string) error {
	defer func() {
		klog.Infof("DsInjector: Finished update ds pod %v", key)
	}()
	namespace, name, err := cache.SplitMetaNamespaceKey(key)
	if err != nil {
		klog.Errorf("DsInjector: invalid resource key %v err: %v", key, err)
		return err
	}
	// get ds pod
	pod, err := dsc.podLister.Pods(namespace).Get(name)
	if err != nil {
		if k8serr.IsNotFound(err) {
			klog.Infof("DsInjector: ds pod %v is deleted", key)
			return nil
		}
		klog.Errorf("DsInjector: failed to get ds pod %v, err: %v", key, err)
		return err
	}

	if pod.GetDeletionTimestamp() != nil {
		klog.Infof("DsInjector: ds pod %v is deleting", key)
		return nil
	}

	if pod.Status.Phase == corev1.PodFailed {
		klog.Errorf("DsInjector: ds pod %v is failed", key)
		return nil
	}

	// update ds pod condition
	podCondition := corev1.PodCondition{
		Type:   PodConditionType,
		Status: PodConditionStatus,
	}
	pod.Status.Conditions = append(pod.Status.Conditions, podCondition)
	pod.ResourceVersion = "0"
	// update ds pod status
	_, err = dsc.podClient.Pods(pod.GetNamespace()).UpdateStatus(ctx, pod, metav1.UpdateOptions{})
	if err != nil && !k8serr.IsNotFound(err) {
		klog.Errorf("DsInjector: update ds pod status fail ds pod %v, err: %v", key, err)
		return err
	}

	klog.Infof("DsInjector: update ds pod %v status success ", key)
	return nil
}

// runWorker: 为bci pod注入ds containers
func (dsc *DsInjector) runWorker(ctx context.Context) {
	for dsc.processNextWorkItem(ctx) {
	}
}

func (dsc *DsInjector) processNextWorkItem(ctx context.Context) bool {
	podKey, quit := dsc.injectDsContainersQueue.Get()
	if quit {
		return false
	}
	defer dsc.injectDsContainersQueue.Done(podKey)

	err := dsc.injectDsContainers(ctx, podKey.(string))
	if err == nil {
		klog.Infof("inject daemonset containers to pod %v success", podKey)
		dsc.injectDsContainersQueue.Forget(podKey)
		return true
	}
	klog.Errorf("inject daemonset containers to pod %v fail err %v", podKey, err)
	dsc.injectDsContainersQueue.AddRateLimited(podKey)
	return true
}

// injectDsContainers: 为bci pod注入ds containers
func (dsc *DsInjector) injectDsContainers(ctx context.Context, key string) error {
	defer func() {
		klog.Infof("DsInjector: Finished injecting ds container %v", key)
	}()

	namespace, name, err := cache.SplitMetaNamespaceKey(key)
	if err != nil {
		klog.Errorf("DsInjector: invalid resource key %v err: %v", key, err)
		return err
	}

	// Get k8s pod resource with this namespace/name
	k8sPod, err := dsc.podLister.Pods(namespace).Get(name)
	if err != nil {
		if k8serr.IsNotFound(err) {
			klog.Infof("DsInjector: k8s pod %v is deleted", key)
			return nil
		}
		klog.Errorf("DsInjector: failed to get k8s pod %v, err: %v", key, err)
		return err
	}
	if k8sPod.DeletionTimestamp != nil {
		klog.Infof("DsInjector: k8s pod %v is deleting", key)
		return nil
	}

	if k8sPod.Status.Phase == corev1.PodFailed || k8sPod.Status.Phase == corev1.PodSucceeded {
		klog.Infof("DsInjector: skipping to inject ds container into pod %v in phase %v", key, k8sPod.Status.Phase)
		return nil
	}

	if k8sPod.Status.Phase != corev1.PodRunning {
		klog.Infof("DsInjector: skipping to inject ds container into pod %v in phase %v", key, k8sPod.Status.Phase)
		return errors.New("pod is not running")
	}

	//if bciv3Label := k8sPod.GetLabels()[Bci3LabelKey]; bciv3Label != "true" {
	//	klog.Infof("DsInjector: k8s pod %v is not v3 pod", key)
	//	return nil
	//}

	podID := ""
	if v, ok := k8sPod.GetAnnotations()[PodIDAnnotationKey]; ok && v != "" {
		podID = v
	} else {
		klog.Warningf("pod %v id is empty, so skip to inject ds container into it", key)
		return errors.New("pod id is empty")
	}

	// Get provider pod resource with this namespace/name
	providerPod, err := dsc.provider.GetPodDetail(ctx, namespace, name)
	if err != nil {
		klog.Errorf("DsInjector: failed to get provider pod %v, err: %v", key, err)
		return err
	}

	if providerPod == nil {
		klog.Errorf("DsInjector: provider pod %s not found", key)
		return errors.New("provider pod not found")
	}

	if string(providerPod.Status) != string(corev1.PodRunning) {
		klog.Infof("DsInjector: skipping to inject ds container into pod %v in phase %v", key, providerPod.Status)
		return errors.New("provider pod is not running")
	}

	// Start inject Ds containers
	// 1. Get the ds containers that need to be injected
	eventRecord := dsc.resourceManager.GetEventRecorder()
	k8sInjectContainers, bciVolumes, injectErrs := dsc.getInjectContainers(ctx, k8sPod, eventRecord)
	// 2. if an error occurs, the pod key needs to be re-queued
	errStr := ""
	if len(injectErrs) > 0 {
		for _, e := range injectErrs {
			errStr += e.Error() + " ;;"
		}
		return errors.New(errStr)
	}

	// 3. Get the ds containers that actually injected into the provider pod
	providerInjectContainers := make(map[string]bci.Container) // key is container name
	for _, container := range providerPod.Containers {
		if container.ContainerType == DsContainerType {
			providerInjectContainers[container.Name] = container
		}
	}

	// 4. Compare the list of ds containers that need to be injected with the list of ds containers that actually injected,
	// and find out the list of ds containers that do not need to be changed, need to be added, need to be changed and need
	// to be deleted
	injectContainers := &bci.InjectDsContainersRequest{
		Config: &bci.DsConfig{
			ExpectDsContainers: make([]bci.Container, 0),
			ExpectDsVolumes:    new(bci.Volumes),
		},
	}
	injectContainers.PodID = podID

	if !dsc.isInjectContainersChange(providerInjectContainers, k8sInjectContainers) {
		klog.Infof("no need to update ds containers: %v", k8sInjectContainers)
		return nil
	}
	for _, kic := range k8sInjectContainers {
		container := CovertDsContainerToBciContainer(kic)
		injectContainers.Config.ExpectDsContainers = append(injectContainers.Config.ExpectDsContainers, container)
	}
	injectContainers.Config.ExpectDsVolumes = bciVolumes

	// 5. Request bci to update ds containers
	if err = dsc.provider.UpdateDsContainers(ctx, injectContainers, k8sPod); err != nil {
		klog.Errorf("DsInjector: failed to update ds containers for pod %v, err: %v", key, err)
		eventRecord.Event(k8sPod, corev1.EventTypeWarning, DsContainersEventUpdateFailed, err.Error())
		if find := strings.Contains(err.Error(), DsContainersUpdateErr); !find {
			return err
		}
	}

	if dsc.enableDsRollingUpgrade {
		// 6. Rolling upgrade, wait for the k8s pod until all the container status is ready
		startTime := time.Now()
		for {
			// 防止daemonset-controller注入ds卡死，设置10 minutes timeout
			if time.Now().Sub(startTime) > DefaultTimeOut {
				break
			}
			currK8sPod, err := dsc.podLister.Pods(namespace).Get(name)
			if err != nil {
				klog.Errorf("DsInjector: failed to get k8s pod %v, err: %v", key, err)
				break
			}
			// 等待ds容器注入，需要比对currK8sPod.Status.ContainerStatuses中容器个数与k8sPod.Spec.Containers+k8sInjectContainers个数是否相等
			// 其中currK8sPod.Status.ContainerStatuses包含pod.Spec指定的容器和需要注入的ds容器
			// k8sPod.Spec.Containers：用户pod yaml中定义的容器列表
			// k8sInjectContainers：需要注入的ds容器列表
			if len(currK8sPod.Status.ContainerStatuses) != len(k8sPod.Spec.Containers)+len(k8sInjectContainers) {
				klog.Infof("DsInjectords: ds containers haven't started to be injected")
				time.Sleep(1 * time.Second)
				continue
			}
			// ds容器已完成注入，需要判断pod各容器状态是否均为ready
			// 注意：这里不能以pod.Phases是否为Running来判断，由于pod事件同步延迟较大，当pod.Status.ContainerStatuses中存在container
			//ready为false时，pod.Phases仍为Running
			containerNotReady := true
			for _, cs := range currK8sPod.Status.ContainerStatuses {
				if !cs.Ready {
					containerNotReady = false
					break
				}
			}
			// 全部containers ready，表明ds containers已成功注入，则退出循环
			if containerNotReady {
				klog.Infof("DsInjector: ds containers is not ready, k8s pod %v", key)
				break
			}
			time.Sleep(1 * time.Second)
		}
	}
	return nil
}

func (dsc *DsInjector) getInjectContainers(ctx context.Context, k8sPod *corev1.Pod, eventRecord record.EventRecorder) (map[string]corev1.Container,
	*bci.Volumes, []error) {
	k8sInjectContainers := make(map[string]corev1.Container)
	injectErrs := make([]error, 0)
	bciVolumes := new(bci.Volumes)
	k8sInjectContainersVersion := make(map[string]int64, 0) // key: containerName, value: dsResourceVersion
	dsVolumesMap := make(map[string]int64, 0)               // key: volumeName, value: dsResourceVersion
	if v, ok := k8sPod.GetAnnotations()[PodInjectDsAnnotationKey]; ok {
		k8sInjectDs := strings.Split(v, ",")
		for _, ds := range k8sInjectDs {
			ds = strings.TrimSpace(ds)
			dsNs, dsName, _ := cache.SplitMetaNamespaceKey(ds)
			if dsNs == "" {
				dsNs = k8sPod.GetNamespace()
			}

			dsKey := fmt.Sprintf("%s/%s", dsNs, dsName)
			// Get ds
			k8sDs, err := dsc.dsLister.DaemonSets(dsNs).Get(dsName)
			if err != nil {
				if !k8serr.IsNotFound(err) {
					klog.Errorf("DsInjector: failed to get daemonset, ds %v, err: %v", dsKey, err)
					injectErrs = append(injectErrs, errors.New("failed to get daemonset"))
				}
				klog.Infof("DsInjector: daemonset %v is deleted, err %v", dsKey, err)
				continue
			}
			if k8sDs.DeletionTimestamp != nil {
				klog.Infof("DsInjector: skip the deleting daemonset %v", dsKey)
				continue
			}
			if k8sDs.Status.NumberReady != k8sDs.Status.DesiredNumberScheduled {
				injectErrs = append(injectErrs, errors.New("daemonset is not ready"))
				klog.Infof("DsInjector: skip the NotReady daemonset %v", dsKey)
				continue
			}
			if v1, ok1 := k8sDs.Spec.Template.GetAnnotations()[DsInjectionAnnotationKey]; ok1 && (v1 == "false" || v1 == "") {
				klog.Infof("DsInjector: daemonset %v cancels injection so skip it", dsKey)
				continue
			}

			// 判断ds volume是否有重名，若有重名，则过滤该ds
			volumeDuplicated := false
			for _, volume := range k8sDs.Spec.Template.Spec.Volumes {
				resVersion, _ := strconv.ParseInt(k8sDs.ResourceVersion, 10, 64)
				if _, exist := dsVolumesMap[volume.Name]; exist {
					err = fmt.Errorf("ds %s volume %s name is duplicated", dsKey, volume.Name)
					eventRecord.Event(k8sPod, corev1.EventTypeWarning, DsContainersEventUpdateFailed, err.Error())
					volumeDuplicated = true
					break
				}
				dsVolumesMap[volume.Name] = resVersion
			}
			if volumeDuplicated {
				continue
			}

			// get ds volumes
			dsVolumes, err := dsc.provider.GetDsVolumes(ctx, k8sDs)
			klog.Infof("DsInjector: get daemonset volume %v, err %v", dsKey, err)
			if err != nil {
				eventRecord.Event(k8sPod, corev1.EventTypeWarning, DsContainersEventUpdateFailed, err.Error())
				if find := strings.Contains(err.Error(), DsVolumeUnsupportedErr); !find {
					injectErrs = append(injectErrs, errors.New("get daemonset volume fail"))
					klog.Errorf("DsInjector: get daemonset volume fail %v, err %v", dsKey, err)
					continue
				}
				continue
			}
			AppendDsVolume(bciVolumes, dsVolumes)

			// add inject ds containers
			for _, container := range k8sDs.Spec.Template.Spec.Containers {
				// 判断container是否有重名，若有，则选择resourceVersion较小的ds注入
				resVersion, _ := strconv.ParseInt(k8sDs.ResourceVersion, 10, 64)
				if version, exist := k8sInjectContainersVersion[container.Name]; exist {
					// container name duplicated, choose the ds which has the smaller resourceVersion
					if version >= resVersion {
						err = fmt.Errorf("ds %s container %s name is duplicated", dsKey, container.Name)
						eventRecord.Event(k8sPod, corev1.EventTypeWarning, DsContainersEventUpdateFailed, err.Error())
						continue
					}
				}

				k8sInjectContainersVersion[container.Name] = resVersion
				k8sInjectContainers[container.Name] = container
			}
		}
	}
	return k8sInjectContainers, bciVolumes, injectErrs
}

// isInjectContainersChange: 判断需要注入/更新/删除ds containers
func (dsc *DsInjector) isInjectContainersChange(providerInjectContainers map[string]bci.Container, k8sInjectContainers map[string]corev1.Container) bool {
	// 遍历查看是否有需要新增/更新的ds containers
	for kicKey, kic := range k8sInjectContainers {
		pic, ok := providerInjectContainers[kicKey]
		if ok {
			// need update
			if dsc.isContainerChange(kic, pic) {
				return true
			}
		} else {
			// need add
			return true
		}
	}

	// 遍历查看是否有需要删除的ds containers
	for _, pic := range providerInjectContainers {
		_, ok := k8sInjectContainers[pic.Name]
		if !ok {
			return true
		}
	}

	return false
}

// isContainerChange：判断ds container是否改变，比较containerName、imageName、imageAddress、imageVersion、imageID是否变化
func (dsc *DsInjector) isContainerChange(c1 corev1.Container, c2 bci.Container) bool {
	bciC1Container := CovertDsContainerToBciContainer(c1)
	return bciC1Container.Name != c2.Name || bciC1Container.ImageName != c2.ImageName ||
		bciC1Container.ImageAddress != c2.ImageAddress || bciC1Container.ImageVersion != c2.ImageVersion ||
		bciC1Container.ImageID != c2.ImageID
}

func (dsc *DsInjector) addPod(obj interface{}) {
	pod := obj.(*corev1.Pod)
	// add ds pod which has the DsInjectionAnnotationKey annotation
	// vk inject ds container to the bci pod when it is created, so ds-injector skip to handler the bci pod when it is added
	if _, ok := pod.GetAnnotations()[DsInjectionAnnotationKey]; ok {
		if key, err := cache.MetaNamespaceKeyFunc(pod); err != nil {
			klog.Errorf("Failed to get key for pod %v: err: %v\n", pod.Name, err)
		} else {
			dsc.syncDsPodQueue.Add(key)
		}
	}
}

func (dsc *DsInjector) updatePod(oldObj, newObj interface{}) {
	oldPod := oldObj.(*corev1.Pod)
	newPod := newObj.(*corev1.Pod)
	if isPodChange(oldPod, newPod) {
		// bci pod change
		if _, ok := newPod.GetAnnotations()[PodInjectDsAnnotationKey]; ok {
			if key, err := cache.MetaNamespaceKeyFunc(newPod); err != nil {
				klog.Errorf("Failed to get key for pod %v: err: %v\n", newPod.Name, err)
			} else {
				dsc.injectDsContainersQueue.Add(key)
			}
		}
		// ds pod change
		if _, ok := newPod.GetAnnotations()[DsInjectionAnnotationKey]; ok {
			if key, err := cache.MetaNamespaceKeyFunc(newPod); err != nil {
				klog.Errorf("Failed to get key for pod %v: err: %v\n", newPod.Name, err)
			} else {
				dsc.syncDsPodQueue.Add(key)
			}
		}
	}
}

// addDaemonset: 创建ds时，把标记有需要注入该ds的bci pod加入injectDsContainersQueue
func (dsc *DsInjector) addDaemonset(obj interface{}) {
	ds := obj.(*appsv1.DaemonSet)
	klog.Infof("DsInjector: Add daemonset %v", ds)
	dsc.enqueuePod(ds.GetNamespace(), ds.GetName())
}

// updateDaemonset: ds更新时，把标记有需要注入ds的bci pod加入injectDsContainersQueue
func (dsc *DsInjector) updateDaemonset(oldObj, newObj interface{}) {
	oldDs := oldObj.(*appsv1.DaemonSet)
	newDs := newObj.(*appsv1.DaemonSet)
	if isDsChange(oldDs, newDs) {
		klog.Infof("DsInjector: ds is change old: %v, new: %v", oldDs, newDs)
		dsc.enqueuePod(newDs.GetNamespace(), newDs.GetName())
	}
}

// deleteDaemonset: ds删除时，把标记有需要注入ds的bci pod加入injectDsContainersQueue
func (dsc *DsInjector) deleteDaemonset(obj interface{}) {
	ds := obj.(*appsv1.DaemonSet)
	klog.Infof("DsInjector: Delete daemonset %v", ds)
	dsc.enqueuePod(ds.GetNamespace(), ds.GetName())
}

func (dsc *DsInjector) enqueuePod(dsNamespace string, dsName string) {
	// list bci pod
	pods, err := dsc.podLister.List(labels.Everything())
	if err != nil {
		klog.Errorf("DsInjector: Failed to get k8s pod err %v", err)
		return
	}
	for _, p := range pods {
		// bci.virtual-kubelet.io/inject-ds: 'ns1/ds1,ns2/ds1' or bci.virtual-kubelet.io/inject-ds: 'ds1,ds1'
		// if the ds's ns not specified, ds's ns is same with the pod's ns
		// get the bci pod with the PodInjectDsAnnotationKey
		// the bci pod with the empty PodInjectDsAnnotationKey is include by the podInformer.UpdateEventHandler
		if v, ok := p.GetAnnotations()[PodInjectDsAnnotationKey]; ok && v != "" {
			tmpStr := fmt.Sprintf("%s/%s", dsNamespace, dsName)
			exist1 := strings.Contains(v, tmpStr)
			exist2 := strings.Contains(v, dsName)

			if exist1 || (!exist1 && exist2 && dsNamespace == p.GetNamespace()) {
				if key, err := cache.MetaNamespaceKeyFunc(p); err != nil {
					klog.Errorf("DsInjector: Failed to get key for pod %v: %v", p.Name, err)
				} else {
					dsc.injectDsContainersQueue.Add(key)
				}
			}
		}
	}
}

func isDsChange(oldDs, newDs *appsv1.DaemonSet) bool {
	if !dsEqual(oldDs, newDs) {
		return true
	}
	if !deleteGraceTimeEqual(oldDs.DeletionGracePeriodSeconds, newDs.DeletionGracePeriodSeconds) {
		return true
	}
	if !oldDs.DeletionTimestamp.Equal(newDs.DeletionTimestamp) {
		return true
	}
	return false
}

func dsEqual(ds1, ds2 *appsv1.DaemonSet) bool {
	// Daemonset Update Only Permits update of:
	// - `spec.template.spec.containers[*].image`
	// - `Spec.template.objectmeta.annotations`
	// - `spec.template.spec.activeDeadlineSeconds`
	// - `spec.template.objectmeta.labels`
	// - `spec.template.spec.volumes`
	// - `spec.template.spec.tolerations` (only additions to existing tolerations)
	// compare the values of the pods to see if the values actually changed
	return cmp.Equal(ds1.Spec.Template.Spec.Containers, ds2.Spec.Template.Spec.Containers) &&
		cmp.Equal(ds1.Spec.Template.ObjectMeta.Annotations, ds2.Spec.Template.ObjectMeta.Annotations) &&
		cmp.Equal(ds1.Spec.Template.Spec.ActiveDeadlineSeconds, ds2.Spec.Template.Spec.ActiveDeadlineSeconds) &&
		cmp.Equal(ds1.Spec.Template.ObjectMeta.Labels, ds1.Spec.Template.ObjectMeta.Labels) &&
		cmp.Equal(ds1.Spec.Template.Spec.Volumes, ds2.Spec.Template.Spec.Volumes) &&
		cmp.Equal(ds1.Spec.Template.Spec.Tolerations, ds2.Spec.Template.Spec.Tolerations)
}

func isPodChange(oldPod, newPod *corev1.Pod) bool {
	if !podEqual(oldPod, newPod) {
		return true
	}
	if !deleteGraceTimeEqual(oldPod.DeletionGracePeriodSeconds, newPod.DeletionGracePeriodSeconds) {
		return true
	}
	if !oldPod.DeletionTimestamp.Equal(newPod.DeletionTimestamp) {
		return true
	}
	return false
}

func deleteGraceTimeEqual(old, new *int64) bool {
	if old == nil && new == nil {
		return true
	}
	if old != nil && new != nil {
		return *old == *new
	}
	return false
}

func podEqual(pod1, pod2 *corev1.Pod) bool {
	// Pod Update Only Permits update of:
	// - `spec.containers[*].image`
	// - `spec.initContainers[*].image`
	// - `spec.activeDeadlineSeconds`
	// - `spec.tolerations` (only additions to existing tolerations)
	// - `objectmeta.labels`
	// - `objectmeta.annotations`
	// compare the values of the pods to see if the values actually changed
	return cmp.Equal(pod1.Spec.Containers, pod2.Spec.Containers) &&
		cmp.Equal(pod1.Spec.InitContainers, pod2.Spec.InitContainers) &&
		cmp.Equal(pod1.Spec.ActiveDeadlineSeconds, pod2.Spec.ActiveDeadlineSeconds) &&
		cmp.Equal(pod1.Spec.Tolerations, pod2.Spec.Tolerations) &&
		cmp.Equal(pod1.ObjectMeta.Labels, pod2.ObjectMeta.Labels) &&
		cmp.Equal(pod1.ObjectMeta.Annotations, pod2.ObjectMeta.Annotations)
}
