package baidubci

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"sort"
	"sync"
	"time"

	"github.com/hashicorp/go-multierror"
	dto "github.com/prometheus/client_model/go"
	"github.com/prometheus/common/expfmt"
	"github.com/virtual-kubelet/virtual-kubelet/log"
	"github.com/virtual-kubelet/virtual-kubelet/node/api/statsv1alpha1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
)

type family struct {
	typ  dto.MetricType
	help string
}

const (
	ContainerCPUUsageSecondsTotal  = "container_cpu_usage_seconds_total"
	ContainerMemoryWorkingSetBytes = "container_memory_working_set_bytes"
	NanosecondUnit                 = 1000000000
)

var families map[string]family = map[string]family{
	"bci_pods_count": {
		typ:  dto.MetricType_GAUGE,
		help: "BCI pods count on this virtual node",
	},
	"container_cpu_cfs_periods_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Number of elapsed enforcement period intervals.",
	},
	"container_cpu_cfs_throttled_periods_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Number of throttled period intervals.",
	},
	"container_cpu_cfs_throttled_seconds_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Total time duration the container has been throttled.",
	},
	"container_cpu_load_average_10s": {
		typ:  dto.MetricType_GAUGE,
		help: "Value of container cpu load average over the last 10 seconds.",
	},
	"container_cpu_system_seconds_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative system cpu time consumed in seconds.",
	},
	"container_cpu_usage_seconds_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative cpu time consumed in seconds.",
	},
	"container_cpu_user_seconds_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative user cpu time consumed in seconds.",
	},
	"container_file_descriptors": {
		typ:  dto.MetricType_GAUGE,
		help: "Number of open file descriptors for the container.",
	},
	"container_fs_inodes_free": {
		typ:  dto.MetricType_GAUGE,
		help: "Number of available Inodes",
	},
	"container_fs_inodes_total": {
		typ:  dto.MetricType_GAUGE,
		help: "Number of Inodes",
	},
	"container_fs_io_current": {
		typ:  dto.MetricType_GAUGE,
		help: "Number of I/Os currently in progress",
	},
	"container_fs_io_time_seconds_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative count of seconds spent doing I/Os",
	},
	"container_fs_io_time_weighted_seconds_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative weighted I/O time in seconds",
	},
	"container_fs_limit_bytes": {
		typ:  dto.MetricType_GAUGE,
		help: "Number of bytes that can be consumed by the container on this filesystem.",
	},
	"container_fs_read_seconds_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative count of seconds spent reading",
	},
	"container_fs_reads_bytes_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative count of bytes read",
	},
	"container_fs_reads_merged_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative count of reads merged",
	},
	"container_fs_reads_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative count of reads completed",
	},
	"container_fs_sector_reads_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative count of sector reads completed",
	},
	"container_fs_sector_writes_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative count of sector writes completed",
	},
	"container_fs_usage_bytes": {
		typ:  dto.MetricType_GAUGE,
		help: "Number of bytes that are consumed by the container on this filesystem.",
	},
	"pod_filesystem_free_bytes": {
		typ:  dto.MetricType_GAUGE,
		help: "The number of bytes free for pod file system.",
	},
	"pod_filesystem_size_bytes": {
		typ:  dto.MetricType_GAUGE,
		help: "The number of total bytes pod file system can use",
	},
	"container_fs_write_seconds_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative count of seconds spent writing",
	},
	"container_fs_writes_bytes_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative count of bytes written",
	},
	"container_fs_writes_merged_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative count of writes merged",
	},
	"container_fs_writes_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative count of writes completed",
	},
	"container_last_seen": {
		typ:  dto.MetricType_GAUGE,
		help: "Last time a container was seen by the exporter",
	},
	"container_memory_cache": {
		typ:  dto.MetricType_GAUGE,
		help: "Number of bytes of page cache memory.",
	},
	"container_memory_failcnt": {
		typ:  dto.MetricType_COUNTER,
		help: "Number of memory usage hits limits",
	},
	"container_memory_failures_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative count of memory allocation failures.",
	},
	"container_memory_mapped_file": {
		typ:  dto.MetricType_GAUGE,
		help: "Size of memory mapped files in bytes.",
	},
	"container_memory_max_usage_bytes": {
		typ:  dto.MetricType_GAUGE,
		help: "Maximum memory usage recorded in bytes",
	},
	"container_memory_rss": {
		typ:  dto.MetricType_GAUGE,
		help: "Size of RSS in bytes.",
	},
	"container_memory_swap": {
		typ:  dto.MetricType_GAUGE,
		help: "Container swap usage in bytes.",
	},
	"container_memory_usage_bytes": {
		typ:  dto.MetricType_GAUGE,
		help: "Current memory usage in bytes, including all memory regardless of when it was accessed",
	},
	"container_memory_working_set_bytes": {
		typ:  dto.MetricType_GAUGE,
		help: "Current working set in bytes.",
	},
	"container_network_receive_bytes_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative count of bytes received",
	},
	"container_network_receive_errors_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative count of errors encountered while receiving",
	},
	"container_network_receive_packets_dropped_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative count of packets dropped while receiving",
	},
	"container_network_receive_packets_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative count of packets received",
	},
	"container_network_transmit_bytes_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative count of bytes transmitted",
	},
	"container_network_transmit_errors_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative count of errors encountered while transmitting",
	},
	"container_network_transmit_packets_dropped_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative count of packets dropped while transmitting",
	},
	"container_network_transmit_packets_total": {
		typ:  dto.MetricType_COUNTER,
		help: "Cumulative count of packets transmitted",
	},
	"container_processes": {
		typ:  dto.MetricType_GAUGE,
		help: "Number of processes running inside the container.",
	},
	"container_scrape_error": {
		typ:  dto.MetricType_GAUGE,
		help: "1 if there was an error while getting container metrics, 0 otherwise",
	},
	"container_sockets": {
		typ:  dto.MetricType_GAUGE,
		help: "Number of open sockets for the container.",
	},
	"container_spec_cpu_period": {
		typ:  dto.MetricType_GAUGE,
		help: "CPU period of the container.",
	},
	"container_spec_cpu_quota": {
		typ:  dto.MetricType_GAUGE,
		help: "CPU quota of the container.",
	},
	"container_spec_cpu_shares": {
		typ:  dto.MetricType_GAUGE,
		help: "CPU share of the container.",
	},
	"container_spec_memory_limit_bytes": {
		typ:  dto.MetricType_GAUGE,
		help: "Memory limit for the container.",
	},
	"container_spec_memory_reservation_limit_bytes": {
		typ:  dto.MetricType_GAUGE,
		help: "Memory reservation limit for the container.",
	},
	"container_spec_memory_swap_limit_bytes": {
		typ:  dto.MetricType_GAUGE,
		help: "Memory swap limit for the container.",
	},
	"container_start_time_seconds": {
		typ:  dto.MetricType_GAUGE,
		help: "Start time of the container since unix epoch in seconds.",
	},
	"container_tasks_state": {
		typ:  dto.MetricType_GAUGE,
		help: "Number of tasks in given state",
	},
	"container_threads": {
		typ:  dto.MetricType_GAUGE,
		help: "Number of threads running inside the container",
	},
	"container_threads_max": {
		typ:  dto.MetricType_GAUGE,
		help: "Maximum number of threads allowed inside the container, infinity if value is zero",
	},
	"container_ulimits_soft": {
		typ:  dto.MetricType_GAUGE,
		help: "Soft ulimit values for the container root process. Unlimited if -1, except priority and nice",
	},
	// add gpu metrics
	"DCGM_FI_DEV_SM_CLOCK": {
		typ:  dto.MetricType_GAUGE,
		help: "DCGM_FI_DEV_SM_CLOCK SM clock frequency (in MHz)",
	},
	"DCGM_FI_DEV_MEM_CLOCK": {
		typ:  dto.MetricType_GAUGE,
		help: "DCGM_FI_DEV_MEM_CLOCK Memory clock frequency (in MHz)",
	},
	"DCGM_FI_DEV_GPU_TEMP": {
		typ:  dto.MetricType_GAUGE,
		help: "DCGM_FI_DEV_GPU_TEMP GPU temperature (in C)",
	},
	"DCGM_FI_DEV_POWER_USAGE": {
		typ:  dto.MetricType_GAUGE,
		help: "DCGM_FI_DEV_POWER_USAGE Power draw (in W)",
	},
	"DCGM_FI_DEV_TOTAL_ENERGY_CONSUMPTION": {
		typ:  dto.MetricType_COUNTER,
		help: "DCGM_FI_DEV_TOTAL_ENERGY_CONSUMPTION Total energy consumption since boot (in mJ)",
	},
	"DCGM_FI_DEV_PCIE_REPLAY_COUNTER": {
		typ:  dto.MetricType_COUNTER,
		help: "DCGM_FI_DEV_PCIE_REPLAY_COUNTER Total number of PCIe retries",
	},
	"DCGM_FI_DEV_GPU_UTIL": {
		typ:  dto.MetricType_GAUGE,
		help: "DCGM_FI_DEV_GPU_UTIL GPU utilization (in %)",
	},
	"DCGM_FI_DEV_MEM_COPY_UTIL": {
		typ:  dto.MetricType_GAUGE,
		help: "DCGM_FI_DEV_MEM_COPY_UTIL Memory utilization (in %)",
	},
	"DCGM_FI_DEV_ENC_UTIL": {
		typ:  dto.MetricType_GAUGE,
		help: "DCGM_FI_DEV_ENC_UTIL Encoder utilization (in %)",
	},
	"DCGM_FI_DEV_DEC_UTIL": {
		typ:  dto.MetricType_GAUGE,
		help: "DCGM_FI_DEV_DEC_UTIL Decoder utilization (in %)",
	},
	"DCGM_FI_DEV_XID_ERRORS": {
		typ:  dto.MetricType_GAUGE,
		help: "DCGM_FI_DEV_XID_ERRORS Value of the last XID error encountered",
	},
	"DCGM_FI_DEV_FB_FREE": {
		typ:  dto.MetricType_GAUGE,
		help: "DCGM_FI_DEV_FB_FREE Framebuffer memory free (in MiB)",
	},
	"DCGM_FI_DEV_FB_USED": {
		typ:  dto.MetricType_GAUGE,
		help: "DCGM_FI_DEV_FB_USED Framebuffer memory used (in MiB)",
	},
	"DCGM_FI_DEV_NVLINK_BANDWIDTH_TOTAL": {
		typ:  dto.MetricType_COUNTER,
		help: "DCGM_FI_DEV_NVLINK_BANDWIDTH_TOTAL Total number of NVLink bandwidth counters for all lanes",
	},
	"DCGM_FI_DEV_VGPU_LICENSE_STATUS": {
		typ:  dto.MetricType_GAUGE,
		help: "DCGM_FI_DEV_VGPU_LICENSE_STATUS vGPU License status",
	},
	"DCGM_FI_DEV_UNCORRECTABLE_REMAPPED_ROWS": {
		typ:  dto.MetricType_COUNTER,
		help: "DCGM_FI_DEV_UNCORRECTABLE_REMAPPED_ROWS Number of remapped rows for uncorrectable errors",
	},
	"DCGM_FI_DEV_CORRECTABLE_REMAPPED_ROWS": {
		typ:  dto.MetricType_COUNTER,
		help: "DCGM_FI_DEV_CORRECTABLE_REMAPPED_ROWS Number of remapped rows for correctable errors",
	},
	"DCGM_FI_DEV_ROW_REMAP_FAILURE": {
		typ:  dto.MetricType_GAUGE,
		help: "DCGM_FI_DEV_ROW_REMAP_FAILURE Whether remapping of rows has failed",
	},
	"DCGM_FI_PROF_PIPE_TENSOR_ACTIVE": {
		typ:  dto.MetricType_GAUGE,
		help: "DCGM_FI_PROF_PIPE_TENSOR_ACTIVE Ratio of cycles the tensor (HMMA) pipe is active (in %)",
	},
	"DCGM_FI_PROF_GR_ENGINE_ACTIVE": {
		typ:  dto.MetricType_GAUGE,
		help: "DCGM_FI_PROF_GR_ENGINE_ACTIVE Ratio of time the graphics engine is active (in %)",
	},
	"DCGM_FI_PROF_SM_ACTIVE": {
		typ:  dto.MetricType_GAUGE,
		help: "DCGM_FI_PROF_SM_ACTIVE The ratio of cycles an SM has at least 1 warp assigned (in %)",
	},
	"DCGM_FI_PROF_SM_OCCUPANCY": {
		typ:  dto.MetricType_GAUGE,
		help: "DCGM_FI_PROF_SM_OCCUPANCY The ratio of number of warps resident on an SM (in %)",
	},
	"DCGM_FI_PROF_DRAM_ACTIVE": {
		typ:  dto.MetricType_GAUGE,
		help: "DCGM_FI_PROF_DRAM_ACTIVE Ratio of cycles the device memory interface is active sending or receiving data (in %)",
	},
	"DCGM_FI_PROF_PCIE_TX_BYTES": {
		typ:  dto.MetricType_COUNTER,
		help: "DCGM_FI_PROF_PCIE_TX_BYTES The number of bytes of active pcie tx data including both header and payload",
	},
	"DCGM_FI_PROF_PCIE_RX_BYTES": {
		typ:  dto.MetricType_COUNTER,
		help: "DCGM_FI_PROF_PCIE_RX_BYTES The number of bytes of active pcie rx data including both header and payload",
	},
}

func getStringPointer(v string) *string {
	return &v
}

func getMetricTypePointer(v dto.MetricType) *dto.MetricType {
	return &v
}

func getFloat64Pointer(v float64) *float64 {
	return &v
}

func newEmptyFamilies(podsCount int, unixMilli int64) map[string]*dto.MetricFamily {
	ret := make(map[string]*dto.MetricFamily, len(families))
	for name, meta := range families {
		ret[name] = &dto.MetricFamily{
			Name: getStringPointer(name),
			Help: getStringPointer(meta.help),
			Type: getMetricTypePointer(meta.typ),
		}
		// value of bci_pods_count is generated by us.
		if name == "bci_pods_count" {
			ret[name].Metric = []*dto.Metric{
				{
					TimestampMs: &unixMilli,
					Label: []*dto.LabelPair{
						{
							Name:  getStringPointer("version"),
							Value: getStringPointer("v2"),
						},
					},
					Gauge: &dto.Gauge{
						Value: getFloat64Pointer(float64(podsCount)),
					},
				},
			}
		}
	}
	return ret
}

func (p *BCIProvider) GetMetricsResource(ctx context.Context) ([]*dto.MetricFamily, error) {
	if !p.enablePodCache {
		return nil, nil
	}
	all, err := p.podCache.GetPodIDs(ctx)
	if err != nil {
		return nil, err
	}
	result := newEmptyFamilies(len(all), p.clock.Now().UnixMilli())

	podIDs := make([]string, 0, len(all))
	for podID := range all {
		podIDs = append(podIDs, podID)
	}

	log.G(ctx).Infof("start to list metrics for %d pods", len(podIDs))
	var podsMetrics []*bci.PodMetrics
	var wg sync.WaitGroup
	var lock sync.Mutex
	var errs error
	for i := 0; i < len(podIDs); i += bci.MaxPodsPerListPodsMetricsRequest {
		wg.Add(1)
		max := i + bci.MaxPodsPerListPodsMetricsRequest
		if max > len(podIDs) {
			max = len(podIDs)
		}
		go func(ids []string) {
			defer wg.Done()
			if len(ids) > 0 {
				resp, err := p.bciV2Client.ListPodsMetrics(ctx, ids, p.getSignOption(ctx))
				lock.Lock()
				defer lock.Unlock()
				if err != nil {
					errs = multierror.Append(errs, err)
					return
				}
				podsMetrics = append(podsMetrics, resp.Result...)
			}
		}(podIDs[i:max])
	}
	wg.Wait()
	if errs != nil {
		log.G(ctx).WithError(errs).Error("fetch pod metrics error")
		return nil, errs
	}

	idPrefix := "/kubepods/guaranteed/pod"
	for _, podMetrics := range podsMetrics {
		k8sPod, ok := all[podMetrics.PodShortID]
		if !ok {
			log.G(ctx).Warn("ignore unknown podID %s in ListPodsMetrics resp", podMetrics.PodShortID)
			continue
		}
		uid := string(k8sPod.GetUID())
		podName := k8sPod.GetName()
		namespace := k8sPod.GetNamespace()

		for _, m := range podMetrics.Metrics {
			f, ok := result[m.Meta.Name]
			if !ok {
				continue
			}
			var id string
			if m.Meta.ContainerID != "" {
				// eg: /kubepods/burstable/podcee5b9fb-6424-49a5-9257-f7dcc0b23336/4fd6c1531098e2409b2531f21ab993f51398838742903d6db40972b6d8ba7b99
				id = fmt.Sprintf("%s%s/%s", idPrefix, uid, m.Meta.ContainerID)
			} else {
				// eg: /kubepods/burstable/podf69f3180-392a-466d-ab62-e304b197c8df
				id = fmt.Sprintf("%s%s", idPrefix, uid)
			}

			// Label name in dictionary order.
			labelPairs := []*dto.LabelPair{
				{
					Name:  getStringPointer("container"),
					Value: getStringPointer(m.Meta.Container),
				},
				{
					Name:  getStringPointer("id"),
					Value: getStringPointer(id),
				},
				{
					Name:  getStringPointer("image"),
					Value: getStringPointer(m.Meta.Image),
				},
				{
					Name:  getStringPointer("name"),
					Value: getStringPointer(m.Meta.ContainerID),
				},
				{
					Name:  getStringPointer("namespace"),
					Value: getStringPointer(namespace),
				},
				{
					Name:  getStringPointer("pod"),
					Value: getStringPointer(podName),
				},
				// add gpu metrics label
				{
					Name:  getStringPointer("job_pod_name"),
					Value: getStringPointer(podName),
				},
				{
					Name:  getStringPointer("job_pod_namespace"),
					Value: getStringPointer(namespace),
				},
			}
			timestampMs := m.TimeStamp * 1000
			metric := &dto.Metric{
				Label:       labelPairs,
				TimestampMs: &timestampMs,
			}

			switch *(f.Type) {
			case dto.MetricType_COUNTER:
				metric.Counter = &dto.Counter{
					Value: getFloat64Pointer(m.Value),
				}
			case dto.MetricType_GAUGE:
				metric.Gauge = &dto.Gauge{
					Value: getFloat64Pointer(m.Value),
				}
			}

			f.Metric = append(f.Metric, metric)
		}

	}

	mfs := make([]*dto.MetricFamily, 0, len(result))
	for k, f := range result {
		if len(f.GetMetric()) == 0 {
			log.G(ctx).Debugf("ignore empty metric family %s", k)
			continue
		}
		mfs = append(mfs, f)
	}
	return mfs, nil
}

func (p *BCIProvider) GetCAdvisorMetrics(ctx context.Context) (io.ReadCloser, error) {
	if !p.enablePodCache {
		return nil, nil // TODO:
	}

	all, err := p.podCache.GetPodIDs(ctx)
	if err != nil {
		return nil, err
	}
	result := newEmptyFamilies(len(all), p.clock.Now().UnixMilli())

	podIDs := make([]string, 0, len(all))
	for podID := range all {
		podIDs = append(podIDs, podID)
	}

	log.G(ctx).Infof("start to list metrics for %d pods", len(podIDs))
	var podsMetrics []*bci.PodMetrics
	var wg sync.WaitGroup
	var lock sync.Mutex
	var errs error
	for i := 0; i < len(podIDs); i += bci.MaxPodsPerListPodsMetricsRequest {
		wg.Add(1)
		max := i + bci.MaxPodsPerListPodsMetricsRequest
		if max > len(podIDs) {
			max = len(podIDs)
		}
		go func(ids []string) {
			defer wg.Done()
			if len(ids) > 0 {
				resp, err := p.bciV2Client.ListPodsMetrics(ctx, ids, p.getSignOption(ctx))
				lock.Lock()
				defer lock.Unlock()
				if err != nil {
					errs = multierror.Append(errs, err)
					return
				}
				podsMetrics = append(podsMetrics, resp.Result...)
			}
		}(podIDs[i:max])
	}
	wg.Wait()
	if errs != nil {
		log.G(ctx).WithError(errs).Error("fetch pod metrics error")
		return nil, errs
	}

	idPrefix := "/kubepods/guaranteed/pod"
	for _, podMetrics := range podsMetrics {
		k8sPod, ok := all[podMetrics.PodShortID]
		if !ok {
			log.G(ctx).Warn("ignore unknown podID %s in ListPodsMetrics resp", podMetrics.PodShortID)
			continue
		}
		uid := string(k8sPod.GetUID())
		podName := k8sPod.GetName()
		namespace := k8sPod.GetNamespace()

		for _, m := range podMetrics.Metrics {
			f, ok := result[m.Meta.Name]
			if !ok {
				continue
			}
			var id string
			if m.Meta.ContainerID != "" {
				// eg: /kubepods/burstable/podcee5b9fb-6424-49a5-9257-f7dcc0b23336/4fd6c1531098e2409b2531f21ab993f51398838742903d6db40972b6d8ba7b99
				id = fmt.Sprintf("%s%s/%s", idPrefix, uid, m.Meta.ContainerID)
			} else {
				// eg: /kubepods/burstable/podf69f3180-392a-466d-ab62-e304b197c8df
				id = fmt.Sprintf("%s%s", idPrefix, uid)
			}

			// Label name in dictionary order.
			labelPairs := []*dto.LabelPair{
				{
					Name:  getStringPointer("container"),
					Value: getStringPointer(m.Meta.Container),
				},
				{
					Name:  getStringPointer("id"),
					Value: getStringPointer(id),
				},
				{
					Name:  getStringPointer("image"),
					Value: getStringPointer(m.Meta.Image),
				},
				{
					Name:  getStringPointer("name"),
					Value: getStringPointer(m.Meta.ContainerID),
				},
				{
					Name:  getStringPointer("namespace"),
					Value: getStringPointer(namespace),
				},
				{
					Name:  getStringPointer("pod"),
					Value: getStringPointer(podName),
				},
				// add gpu metrics label
				{
					Name:  getStringPointer("job_pod_name"),
					Value: getStringPointer(podName),
				},
				{
					Name:  getStringPointer("job_pod_namespace"),
					Value: getStringPointer(namespace),
				},
			}
			timestampMs := m.TimeStamp * 1000
			metric := &dto.Metric{
				Label:       labelPairs,
				TimestampMs: &timestampMs,
			}

			switch *(f.Type) {
			case dto.MetricType_COUNTER:
				metric.Counter = &dto.Counter{
					Value: getFloat64Pointer(m.Value),
				}
			case dto.MetricType_GAUGE:
				metric.Gauge = &dto.Gauge{
					Value: getFloat64Pointer(m.Value),
				}
			}

			f.Metric = append(f.Metric, metric)
		}

	}

	mfs := make([]*dto.MetricFamily, 0, len(result))
	for k, f := range result {
		if len(f.GetMetric()) == 0 {
			log.G(ctx).Debugf("ignore empty metric family %s", k)
			continue
		}
		mfs = append(mfs, f)
	}

	buf := new(bytes.Buffer)
	ret := io.NopCloser(buf)

	sort.Slice(mfs, func(i, j int) bool { return *(mfs[i].Name) < *(mfs[j].Name) }) // for testutils stablity.
	encoder := expfmt.NewEncoder(buf, expfmt.FmtText)
	for _, mf := range mfs {
		if err := encoder.Encode(mf); err != nil {
			log.G(ctx).WithError(err).Errorf("fail to encode metric family %s", *mf.Name)
			return ret, err
		}
	}

	return ret, nil
}

// GetStatsSummary 获取统计摘要
func (p *BCIProvider) GetStatsSummary(ctx context.Context) (*statsv1alpha1.Summary, error) {
	if !p.enablePodCache {
		return nil, nil
	}
	// get all bci pods
	all, err := p.podCache.GetPodIDs(ctx)
	if err != nil {
		return nil, err
	}

	podIDs := make([]string, 0, len(all))
	for podID := range all {
		podIDs = append(podIDs, podID)
	}

	log.G(ctx).Infof("GetStatsSummary: start to list stats for %d pods", len(podIDs))
	var podStats []statsv1alpha1.PodStats
	var wg sync.WaitGroup
	var lock sync.Mutex
	var errs error

	for i := 0; i < len(podIDs); i += bci.MaxPodsPerListPodsMetricsRequest {
		wg.Add(1)
		max := i + bci.MaxPodsPerListPodsMetricsRequest
		if max > len(podIDs) {
			max = len(podIDs)
		}
		go func(ids []string) {
			defer wg.Done()
			if len(ids) > 0 {
				resp, err := p.bciV2Client.ListPodsSummary(ctx, ids, p.getSignOption(ctx))
				lock.Lock()
				defer lock.Unlock()
				if err != nil {
					errs = multierror.Append(errs, err)
					return
				}
				ps := convertPodMetricsToPodStats(ctx, all, resp.Result)
				podStats = append(podStats, ps...)
			}
		}(podIDs[i:max])
	}
	wg.Wait()
	if errs != nil {
		log.G(ctx).WithError(errs).Error("GetStatsSummary: fetch pod stats error")
		return nil, errs
	}
	sts := &statsv1alpha1.Summary{
		Pods: podStats,
	}

	return sts, nil
}

// convertPodMetricsToPodStats converts pod metrics to pod stats
func convertPodMetricsToPodStats(ctx context.Context, podsMap map[string]*v1.Pod, podMetrics []*bci.PodMetrics) []statsv1alpha1.PodStats {
	podStats := make([]statsv1alpha1.PodStats, 0)
	for _, metric := range podMetrics {
		pod, ok := podsMap[metric.PodShortID]
		if !ok {
			log.G(ctx).Infof("GetStatsSummary: pod %s not found", metric.PodShortID)
			continue
		}
		podStat := statsv1alpha1.PodStats{
			PodRef: statsv1alpha1.PodReference{
				Name:      pod.GetName(),
				Namespace: pod.GetNamespace(),
				UID:       string(pod.GetUID()),
			},
			StartTime: pod.CreationTimestamp,
		}
		for _, container := range pod.Spec.Containers {
			var (
				usageCoreNanoSeconds  uint64
				memoryWorkingSetBytes uint64
				timeStamp             metav1.Time
			)
			for _, cm := range metric.Metrics {
				if cm.Meta.Container == container.Name {
					timeStamp = metav1.NewTime(time.Unix(cm.TimeStamp, 0))
					if cm.Meta.Name == ContainerCPUUsageSecondsTotal {
						usageCoreNanoSeconds = uint64(cm.Value * NanosecondUnit)
					} else if cm.Meta.Name == ContainerMemoryWorkingSetBytes {
						memoryWorkingSetBytes = uint64(cm.Value)
					}
				}
			}

			podStat.Containers = append(podStat.Containers, statsv1alpha1.ContainerStats{
				Name:      container.Name,
				StartTime: pod.CreationTimestamp,

				CPU: &statsv1alpha1.CPUStats{
					UsageCoreNanoSeconds: &usageCoreNanoSeconds,
					Time:                 timeStamp,
				},
				Memory: &statsv1alpha1.MemoryStats{
					WorkingSetBytes: &memoryWorkingSetBytes,
					Time:            timeStamp,
				},
			})
		}
		podStats = append(podStats, podStat)
	}

	return podStats
}
