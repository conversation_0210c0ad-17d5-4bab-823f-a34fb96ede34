package baidubci

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	k8serr "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/kubernetes/fake"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/util/workqueue"
	"k8s.io/kubernetes/pkg/controller"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/manager"
	provider2 "icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/provider/mock"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
)

func newTestDsInjector(ctx context.Context, ctrl *gomock.Controller, initialOnjects ...runtime.Object) *DsInjector {
	clientset := fake.NewSimpleClientset(initialOnjects...)
	informerFactory := informers.NewSharedInformerFactory(clientset, controller.NoResyncPeriodFunc())

	podInformer := informerFactory.Core().V1().Pods()
	dsInformer := informerFactory.Apps().V1().DaemonSets()

	provider := provider2.NewMockProvider(ctrl)

	rm := manager.NewMockResourceManager(ctrl)

	podLister := podInformer.Lister()
	dsLister := dsInformer.Lister()

	go informerFactory.Start(ctx.Done())

	ds := &DsInjector{
		provider:        provider,
		dsInformer:      dsInformer,
		dsLister:        dsLister,
		dsClient:        clientset.AppsV1(),
		podInformer:     podInformer,
		podLister:       podLister,
		podClient:       clientset.CoreV1(),
		resourceManager: rm,
		workers:         5,
	}
	ds.injectDsContainersQueue = workqueue.NewNamedRateLimitingQueue(workqueue.DefaultControllerRateLimiter(), "injectDsContainers")
	ds.syncDsPodQueue = workqueue.NewNamedRateLimitingQueue(workqueue.DefaultControllerRateLimiter(), "syncDsPod")
	return ds
}

func Test_NewDsInjector(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	provider := provider2.NewMockProvider(ctrl)
	clientset := fake.NewSimpleClientset()
	informerFactory := informers.NewSharedInformerFactory(clientset, controller.NoResyncPeriodFunc())

	podInformer := informerFactory.Core().V1().Pods()
	dsInformer := informerFactory.Apps().V1().DaemonSets()

	rm := manager.NewMockResourceManager(ctrl)

	_, err := NewDsInjector(provider, clientset, dsInformer, podInformer, rm)
	if err != nil {
		t.Errorf("run err: %v", err)
	}
	return
}

func Test_Run(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, canl := context.WithTimeout(context.Background(), time.Second)
	defer canl()
	dsInjector := newTestDsInjector(ctx, ctrl)

	err := dsInjector.Run(ctx)
	if err != nil {
		t.Errorf("run err: %v", err)
	}
	return
}

func Test_processNextSyncWorkItem(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, canl := context.WithTimeout(context.Background(), time.Second)
	defer canl()
	dsInjector := newTestDsInjector(ctx, ctrl)
	dsInjector.syncDsPodQueue.Add("default/nginx")
	res := dsInjector.processNextSyncWorkItem(ctx)
	if !res {
		t.Errorf("processNextSyncWorkItem fail")
	}
	return
}

func Test_updateDsPodStatus(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, canl := context.WithTimeout(context.Background(), time.Second)
	defer canl()
	dsInjector := newTestDsInjector(ctx, ctrl)
	key := "default/nginx"
	pod := &corev1.Pod{}
	pod.Name = "nginx"
	pod.Namespace = "default"
	pod.Status.Phase = corev1.PodRunning
	pod, err := dsInjector.podClient.Pods("default").Create(ctx, pod, metav1.CreateOptions{})
	time.Sleep(1 * time.Second)
	err = dsInjector.updateDsPodStatus(ctx, key)
	if err != nil {
		t.Errorf("updateDsPodStatus fail")
	}
	return
}

func Test_processNextWorkItem(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, canl := context.WithTimeout(context.Background(), time.Second)
	defer canl()
	dsInjector := newTestDsInjector(ctx, ctrl)

	dsInjector.injectDsContainersQueue.Add("default/nginx")
	res := dsInjector.processNextWorkItem(ctx)
	if !res {
		t.Errorf("processNextWorkItem faile")
	}
	return
}

func Test_injectDsContainer(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, canl := context.WithTimeout(context.Background(), time.Second)
	defer canl()
	dsInjector := newTestDsInjector(ctx, ctrl)
	provider := provider2.NewMockProvider(ctrl)
	resourceManager := manager.NewMockResourceManager(ctrl)
	key := "default/myapp"
	pod := &corev1.Pod{}
	pod.Name = "myapp"
	pod.Namespace = "default"
	pod.Status.Phase = corev1.PodRunning
	pod.Status.ContainerStatuses = []corev1.ContainerStatus{
		{
			Name:  "nginx",
			Ready: true,
		},
	}
	pod.Annotations = make(map[string]string, 0)
	pod.Annotations[PodIDAnnotationKey] = "p-test"
	pod.Annotations[PodInjectDsAnnotationKey] = "default/nginx"
	pod.Labels = make(map[string]string, 0)
	// pod.Labels["bci3"] = "true"
	bciPodRes := new(bci.DescribePodResponse)
	bciPodRes.Pod = &bci.Pod{
		PodID:  "p-test",
		Name:   pod.Name,
		Status: bci.PodStatusRunning,
	}
	dsInjector.provider = provider
	dsInjector.resourceManager = resourceManager
	daemonset := &appsv1.DaemonSet{}
	daemonset.Name = "nginx"
	daemonset.Namespace = "default"
	daemonset.Status.NumberReady = 1
	daemonset.Status.DesiredNumberScheduled = 1
	daemonset.Annotations = map[string]string{}
	daemonset.Annotations[DsInjectionAnnotationKey] = "true"
	container := corev1.Container{
		Name:  "nginx",
		Image: "busy",
	}
	dsInjector.podClient.Pods("default").Create(ctx, pod, metav1.CreateOptions{}) //nolint:errcheck
	daemonset.Spec.Template.Spec.Containers = append(daemonset.Spec.Template.Spec.Containers, container)
	dsInjector.dsClient.DaemonSets("default").Create(ctx, daemonset, metav1.CreateOptions{}) //nolint:errcheck
	time.Sleep(2 * time.Second)
	if ok := cache.WaitForCacheSync(ctx.Done(), dsInjector.podInformer.Informer().HasSynced); !ok {
		return
	}
	if ok := cache.WaitForCacheSync(ctx.Done(), dsInjector.dsInformer.Informer().HasSynced); !ok {
		return
	}
	_, err := dsInjector.podLister.Pods("default").Get("nginx")
	if k8serr.IsNotFound(err) {
		return
	}
	resourceManager.EXPECT().GetEventRecorder().Return(nil)
	provider.EXPECT().GetPodDetail(gomock.Any(), gomock.Any(), gomock.Any()).Return(bciPodRes, nil)
	provider.EXPECT().GetDsVolumes(gomock.Any(), gomock.Any()).Return(nil, nil)
	provider.EXPECT().UpdateDsContainers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
	err = dsInjector.injectDsContainers(ctx, key)
	if err != nil {
		t.Errorf("injectDsContainer fail err %v", err)
	}
	return
}

func Test_addPod(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, canl := context.WithTimeout(context.Background(), time.Second)
	defer canl()
	dsInjector := newTestDsInjector(ctx, ctrl)

	pod := &corev1.Pod{}
	pod.Name = "myapp"
	pod.Namespace = "default"
	pod.Annotations = map[string]string{}
	pod.Annotations[DsInjectionAnnotationKey] = "true"
	dsInjector.addPod(pod)
}

func Test_updatePod(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, canl := context.WithTimeout(context.Background(), time.Second)
	defer canl()
	dsInjector := newTestDsInjector(ctx, ctrl)

	newPod1 := &corev1.Pod{}
	newPod1.Name = "myapp"
	newPod1.Namespace = "default"
	newPod1.Annotations = map[string]string{}
	newPod1.Annotations[PodInjectDsAnnotationKey] = "default/nginx1"

	oldPod1 := &corev1.Pod{}
	oldPod1.Name = "myapp"
	oldPod1.Namespace = "default"
	oldPod1.Annotations = map[string]string{}
	oldPod1.Annotations[PodInjectDsAnnotationKey] = "default/nginx2"
	dsInjector.updatePod(oldPod1, newPod1)
	newPod2 := &corev1.Pod{}
	newPod2.Name = "nginx"
	newPod2.Namespace = "default"
	newPod2.Annotations = map[string]string{}
	newPod2.Annotations[DsInjectionAnnotationKey] = "true"

	oldPod2 := &corev1.Pod{}
	oldPod2.Name = "nginx"
	oldPod2.Namespace = "default"
	oldPod2.Annotations = map[string]string{}
	oldPod2.Annotations[DsInjectionAnnotationKey] = "false"
	dsInjector.updatePod(oldPod2, newPod2)
}

func Test_addDaemonset(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, canl := context.WithTimeout(context.Background(), time.Second)
	defer canl()
	dsInjector := newTestDsInjector(ctx, ctrl)

	daemonset := &appsv1.DaemonSet{}
	daemonset.Name = "nginx"
	daemonset.Namespace = "default"

	pod := &corev1.Pod{}
	pod.Name = "myapp"
	pod.Namespace = "default"
	pod.Status.Phase = corev1.PodRunning
	pod.Annotations = map[string]string{}
	pod.Annotations[PodInjectDsAnnotationKey] = "default/nginx"
	dsInjector.podClient.Pods("default").Create(ctx, pod, metav1.CreateOptions{}) //nolint:errcheck
	time.Sleep(1 * time.Second)
	dsInjector.addDaemonset(daemonset)
}

func Test_updateDaemonset(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, canl := context.WithTimeout(context.Background(), time.Second)
	defer canl()
	dsInjector := newTestDsInjector(ctx, ctrl)

	oldDs := &appsv1.DaemonSet{}
	oldDs.Name = "nginx"
	oldDs.Namespace = "default"
	oldDs.Annotations = map[string]string{}
	oldDs.Annotations[DsInjectionAnnotationKey] = "true"

	newDs := &appsv1.DaemonSet{}
	newDs.Name = "nginx"
	newDs.Namespace = "default"
	newDs.Annotations = map[string]string{}
	newDs.Annotations[DsInjectionAnnotationKey] = "false"

	pod := &corev1.Pod{}
	pod.Name = "myapp"
	pod.Namespace = "default"
	pod.Status.Phase = corev1.PodRunning
	pod.Annotations = map[string]string{}
	pod.Annotations[PodInjectDsAnnotationKey] = "default/nginx"
	dsInjector.podClient.Pods("default").Create(ctx, pod, metav1.CreateOptions{}) //nolint:errcheck
	time.Sleep(1 * time.Second)

	dsInjector.updateDaemonset(oldDs, newDs)

}

func Test_deleteDaemonset(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, canl := context.WithTimeout(context.Background(), time.Second)
	defer canl()
	dsInjector := newTestDsInjector(ctx, ctrl)

	ds := &appsv1.DaemonSet{}
	ds.Name = "nginx"
	ds.Namespace = "default"
	ds.Annotations = map[string]string{}
	ds.Annotations[DsInjectionAnnotationKey] = "true"

	pod := &corev1.Pod{}
	pod.Name = "myapp"
	pod.Namespace = "default"
	pod.Status.Phase = corev1.PodRunning
	pod.Annotations = map[string]string{}
	pod.Annotations[PodInjectDsAnnotationKey] = "default/nginx"
	dsInjector.podClient.Pods("default").Create(ctx, pod, metav1.CreateOptions{}) //nolint:errcheck
	time.Sleep(1 * time.Second)

	dsInjector.deleteDaemonset(ds)
}
