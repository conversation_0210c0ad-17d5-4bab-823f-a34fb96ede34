package util

import (
	"fmt"

	"github.com/google/go-cmp/cmp"
)

// condEqMatcher defines a matcher that only matches under condition specified by opt
type condEqMatcher struct {
	want interface{}
	opt  cmp.Option
}

func (ce condEqMatcher) String() string {
	return fmt.Sprintf("%v", ce.want)
}

func (ce condEqMatcher) Matches(got interface{}) bool {
	eq := cmp.Equal(got, ce.want, ce.opt)
	if !eq {
		fmt.Println(cmp.Diff(got, ce.want, ce.opt))
	}
	return eq
}

func CondEq(want interface{}, f interface{}) condEqMatcher {
	var opt cmp.Option = nil
	if f != nil {
		opt = cmp.Comparer(f)
	}
	return condEqMatcher{
		want: want,
		opt:  opt,
	}
}
