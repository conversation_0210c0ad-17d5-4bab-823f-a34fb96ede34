package util

import (
	logconfigv1 "icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/logoperator/logconfigs/v1"
	"path"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/wait"
	dynamicfake "k8s.io/client-go/dynamic/fake"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/kubernetes/fake"
	"k8s.io/client-go/kubernetes/scheme"
	corev1client "k8s.io/client-go/kubernetes/typed/core/v1"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/record"

	logoperator "icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/logoperator"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/manager"
)

// FakeResourceManager returns an instance of the resource manager that will return the specified objects when its "GetX" methods are called.
// Objects can be any valid Kubernetes object (corev1.Pod, corev1.ConfigMap, corev1.Secret, ...).
func FakeResourceManager(objects ...runtime.Object) manager.ResourceManager {
	// Create a fake Kubernetes client that will list the specified objects.
	kubeClient := fake.NewSimpleClientset(objects...)
	newScheme := runtime.NewScheme()
	logconfigv1.AddToScheme(newScheme)
	dynamicClient := dynamicfake.NewSimpleDynamicClient(newScheme)
	// Create a shared informer factory from where we can grab informers and listers for pods, configmaps, secrets and services.
	kubeInformerFactory := informers.NewSharedInformerFactory(kubeClient, 30*time.Second)
	// Grab informers for pods, configmaps and secrets.
	pInformer := kubeInformerFactory.Core().V1().Pods()
	mInformer := kubeInformerFactory.Core().V1().ConfigMaps()
	sInformer := kubeInformerFactory.Core().V1().Secrets()
	svcInformer := kubeInformerFactory.Core().V1().Services()
	pvInformer := kubeInformerFactory.Core().V1().PersistentVolumes()
	pvcInformer := kubeInformerFactory.Core().V1().PersistentVolumeClaims()
	scInformer := kubeInformerFactory.Storage().V1().StorageClasses()
	mutatingWebhookConfigurationInformer := kubeInformerFactory.Admissionregistration().V1().MutatingWebhookConfigurations()
	dsInformer := kubeInformerFactory.Apps().V1().DaemonSets()
	// Start all the required informers.
	go pInformer.Informer().Run(wait.NeverStop)
	go mInformer.Informer().Run(wait.NeverStop)
	go sInformer.Informer().Run(wait.NeverStop)
	go svcInformer.Informer().Run(wait.NeverStop)
	go pvInformer.Informer().Run(wait.NeverStop)
	go pvcInformer.Informer().Run(wait.NeverStop)
	go scInformer.Informer().Run(wait.NeverStop)
	go mutatingWebhookConfigurationInformer.Informer().Run(wait.NeverStop)
	go dsInformer.Informer().Run(wait.NeverStop)
	// Wait for the caches to be synced.
	if !cache.WaitForCacheSync(wait.NeverStop, pInformer.Informer().HasSynced, mInformer.Informer().HasSynced, sInformer.Informer().HasSynced,
		svcInformer.Informer().HasSynced, dsInformer.Informer().HasSynced) {
		panic("failed to wait for caches to be synced")
	}
	eb := record.NewBroadcaster()
	eb.StartRecordingToSink(&corev1client.EventSinkImpl{Interface: kubeClient.CoreV1().Events("default")})
	eventRecorder := eb.NewRecorder(scheme.Scheme, corev1.EventSource{Component: path.Join("node-name", "pod-controller")})
	logConfigController := logoperator.NewLogConfigController(dynamicClient)

	// Create a new instance of the resource manager using the listers for pods, configmaps and secrets.
	r, err := manager.NewResourceManager(pInformer.Lister(), sInformer.Lister(), mInformer.Lister(), svcInformer.Lister(), pvInformer.Lister(),
		pvcInformer.Lister(), pInformer.Lister(), mutatingWebhookConfigurationInformer.Lister(), dsInformer.Lister(), eventRecorder, kubeClient, logConfigController)
	if err != nil {
		panic(err)
	}
	return r
}
