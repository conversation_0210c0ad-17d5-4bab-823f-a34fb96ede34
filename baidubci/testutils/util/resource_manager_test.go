package util

import (
	"testing"

	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// TestFakeResourceManager 是用于测试 FakeResourceManager
// generated by Comate
func TestFakeResourceManager(t *testing.T) {
	pod := &corev1.Pod{
		ObjectMeta: v1.ObjectMeta{
			Name:      "test-pod",
			Namespace: "default",
		},
	}
	configMap := &corev1.ConfigMap{
		ObjectMeta: v1.ObjectMeta{
			Name:      "test-configmap",
			Namespace: "default",
		},
	}
	secret := &corev1.Secret{
		ObjectMeta: v1.ObjectMeta{
			Name:      "test-secret",
			Namespace: "default",
		},
	}
	resourceManager := FakeResourceManager(pod, configMap, secret)
	if resourceManager == nil {
		t.Fatal("FakeResourceManager returned nil")
	}
	podResult, err := resourceManager.GetPod("test-pod", "default")
	if err != nil || podResult == nil {
		t.Fatalf("Failed to get pod: %v", err)
	}
	if podResult.Name != "test-pod" || podResult.Namespace != "default" {
		t.Fatalf("Unexpected pod: %v", podResult)
	}
	configMapResult, err := resourceManager.GetConfigMap("test-configmap", "default")
	if err != nil || configMapResult == nil {
		t.Fatalf("Failed to get configmap: %v", err)
	}
	if configMapResult.Name != "test-configmap" || configMapResult.Namespace != "default" {
		t.Fatalf("Unexpected configmap: %v", configMapResult)
	}
	secretResult, err := resourceManager.GetSecret("test-secret", "default")
	if err != nil || secretResult == nil {
		t.Fatalf("Failed to get secret: %v", err)
	}
	if secretResult.Name != "test-secret" || secretResult.Namespace != "default" {
		t.Fatalf("Unexpected secret: %v", secretResult)
	}
	eventRecorder := resourceManager.GetEventRecorder()
	if eventRecorder == nil {
		t.Fatal("Failed to get event recorder")
	}
	client := resourceManager.GetRawClient()
	if client == nil {
		t.Fatal("Failed to get raw client")
	}
}
