package baidubci

import (
	"testing"

	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/types"
)

// TestNewBCIProfileConfig 是用于测试 NewBCIProfileConfig
// generated by Comate
func TestNewBCIProfileConfig(t *testing.T) {
	config := NewBCIProfileConfig()

	if config.EnableReserveFailedPod != types.DefaultEnableReserveFailedPod {
		t.Errorf("Expected EnableReserveFailedPod to be %v, got %v", types.DefaultEnableReserveFailedPod, config.EnableReserveFailedPod)
	}
}

// TestBuildSubnetOptionsFromProfile 是用于测试 BuildSubnetOptionsFromProfile
// generated by Comate
func TestBuildSubnetOptionsFromProfile(t *testing.T) {
	provider := &BCIProvider{}

	subnetInfos := []types.SubnetInfo{
		{
			SubnetID:    "subnet1",
			LogicalZone: "zone1",
		},
		{
			SubnetID:    "subnet2",
			LogicalZone: "zone2",
		},
		{
			SubnetID:    "subnet1",
			LogicalZone: "zone1",
		},
	}

	options, err := provider.buildSubnetOptionsFromProfile(subnetInfos)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(options) != 2 {
		t.Errorf("Expected 2 unique subnet options, got %d", len(options))
	}

	if options["subnet1"].Weight != 2 {
		t.Errorf("Expected weight of subnet1 to be 2, got %d", options["subnet1"].Weight)
	}

	if options["subnet2"].Weight != 1 {
		t.Errorf("Expected weight of subnet2 to be 1, got %d", options["subnet2"].Weight)
	}
}

// TestBuildSubnetOptionsFromProfileEmpty 是用于测试 BuildSubnetOptionsFromProfileEmpty
// generated by Comate
func TestBuildSubnetOptionsFromProfileEmpty(t *testing.T) {
	provider := &BCIProvider{}

	subnetInfos := []types.SubnetInfo{}

	_, err := provider.buildSubnetOptionsFromProfile(subnetInfos)
	if err == nil {
		t.Errorf("Expected error, got nil")
	}

	if err.Error() != "subnet info is empty" {
		t.Errorf("Expected error 'subnet info is empty', got %v", err)
	}
}

// TestBuildSubnetOptionsFromProfileWithEmptySubnetID 是用于测试 BuildSubnetOptionsFromProfileWithEmptySubnetID
// generated by Comate
func TestBuildSubnetOptionsFromProfileWithEmptySubnetID(t *testing.T) {
	provider := &BCIProvider{}

	subnetInfos := []types.SubnetInfo{
		{
			SubnetID:    "",
			LogicalZone: "zone1",
		},
		{
			SubnetID:    "subnet2",
			LogicalZone: "zone2",
		},
	}

	options, err := provider.buildSubnetOptionsFromProfile(subnetInfos)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(options) != 1 {
		t.Errorf("Expected 1 unique subnet options, got %d", len(options))
	}

	if options["subnet2"].Weight != 1 {
		t.Errorf("Expected weight of subnet2 to be 1, got %d", options["subnet2"].Weight)
	}
}

// TestBuildSubnetInfoFromSubnetOptions 是用于测试 BuildSubnetInfoFromSubnetOptions
// generated by Comate
func TestBuildSubnetInfoFromSubnetOptions(t *testing.T) {
	provider := &BCIProvider{
		subnetOptions: map[string]*SubnetOption{
			"subnet1": {
				Weight:      1,
				LogicalZone: "zone1",
				Quota: BCIResources{
					CPUQuantity:    resource.NewQuantity(10, resource.DecimalSI),
					MemoryQuantity: resource.NewQuantity(20, resource.DecimalSI),
					Pods:           30,
				},
			},
			"subnet2": {
				Weight:      2,
				LogicalZone: "zone2",
				Quota: BCIResources{
					CPUQuantity:    resource.NewQuantity(20, resource.DecimalSI),
					MemoryQuantity: resource.NewQuantity(40, resource.DecimalSI),
					Pods:           60,
				},
			},
		},
	}

	subnetInfos := provider.buildSubnetInfoFromSubnetOptions()

	if len(subnetInfos) != 2 {
		t.Errorf("Expected 2 subnet infos, got %d", len(subnetInfos))
	}

	expectedSubnet1 := types.SubnetInfo{
		SubnetID:    "subnet1",
		LogicalZone: "zone1",
	}
	expectedSubnet2 := types.SubnetInfo{
		SubnetID:    "subnet2",
		LogicalZone: "zone2",
	}

	if subnetInfos[0] != expectedSubnet1 {
		t.Errorf("Expected subnet1 info to be %v, got %v", expectedSubnet1, subnetInfos[0])
	}

	if subnetInfos[1] != expectedSubnet2 {
		t.Errorf("Expected subnet2 info to be %v, got %v", expectedSubnet2, subnetInfos[1])
	}
}

// TestSetBCIProfileConfig 是用于测试 SetBCIProfileConfig
// generated by Comate
func TestSetBCIProfileConfig(t *testing.T) {
	provider := &BCIProvider{}
	controller := &BCIProfileController{
		provider: provider,
	}

	config := &types.BCIProfileConfig{
		Subnets: []types.SubnetInfo{
			{
				SubnetID:    "subnet1",
				LogicalZone: "zone1",
			},
			{
				SubnetID:    "subnet2",
				LogicalZone: "zone2",
			},
		},
		SecurityGroupID: "sg-12345678",
		DNSConfig: &v1.PodDNSConfig{
			Nameservers: []string{"*******", "*******"},
		},
	}

	controller.setBCIProfileConfig(config)

	if len(provider.subnetOptions) != 2 {
		t.Errorf("Expected 2 subnet options, got %d", len(provider.subnetOptions))
	}

	if provider.securityGroupID != "sg-12345678" {
		t.Errorf("Expected securityGroupID to be 'sg-12345678', got %s", provider.securityGroupID)
	}

	if provider.nodeDNSConfig == nil || len(provider.nodeDNSConfig.Nameservers) != 2 ||
		provider.nodeDNSConfig.Nameservers[0] != "*******" || provider.nodeDNSConfig.Nameservers[1] != "*******" {
		t.Errorf("Expected DNSConfig to be set, got %+v", provider.nodeDNSConfig)
	}
}
