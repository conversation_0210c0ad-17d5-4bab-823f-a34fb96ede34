package userstorage

import (
	"context"
)

// emptyStorage implements Storage with no op.
type emptyStorage struct{}

func NewEmptyStorage() (Storage, error) {
	return &emptyStorage{}, nil
}

func (*emptyStorage) GetAllUsers(ctx context.Context, nodeName string) ([]string, error) {
	return nil, nil
}

func (*emptyStorage) AddUser(ctx context.Context, userUUID, nodeName string) error {
	return nil
}

func (*emptyStorage) RemoveUser(ctx context.Context, userUUID, nodeName string) error {
	return nil
}
