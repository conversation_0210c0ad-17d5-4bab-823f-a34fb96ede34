package userstorage

import (
	"context"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/util"
)

const (
	fileStorageBaseDir = "/userstorage"
)

//go:generate mockgen -destination ./mock.go -package userstorage -self_package icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/userstorage icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/userstorage Storage
type Storage interface {
	GetAllUsers(ctx context.Context, nodeName string) ([]string, error)
	AddUser(ctx context.Context, userUUID, nodeName string) error
	RemoveUser(ctx context.Context, userUUID, nodeName string) error
}

func NewStorage(mode util.MultiTenantMode, nodeName string) (Storage, error) {
	switch mode {
	case util.MultiTenantModeCCE, util.MultiTenantModeBSC:
		return NewFileStorage(fileStorageBaseDir, nodeName)
	}
	return NewEmptyStorage()
}
