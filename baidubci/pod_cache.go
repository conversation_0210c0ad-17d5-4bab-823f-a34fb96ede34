package baidubci

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"code.cloudfoundry.org/clock"
	"github.com/google/go-cmp/cmp"
	uuid "github.com/satori/go.uuid"
	"github.com/virtual-kubelet/virtual-kubelet/errdefs"
	"github.com/virtual-kubelet/virtual-kubelet/log"
	"github.com/virtual-kubelet/virtual-kubelet/trace"
	v1 "k8s.io/api/core/v1"
	k8serror "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/util"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
)

const (
	// reserve 5 digits in fraction
	timeFormat = "2006-01-02T15:04:05.00000Z07:00"

	// Consts to generate not found pod status. Refer to syncProviderWrapper within vk.
	podStatusReasonNotFound          = "NotFound"
	podStatusMessageNotFound         = "The pod status was not found and may have been deleted from the provider"
	containerStatusReasonNotFound    = "NotFound"
	containerStatusMessageNotFound   = "Container was not found and was likely deleted"
	containerStatusExitCodeNotFound  = -137
	statusTerminatedReason           = "Terminated"
	containerStatusTerminatedMessage = "Container was terminated. The exit code may not reflect the real exit code"

	podStatusReasonProviderFailed = "ProviderFailed"

	podStatusReasonMaxRetriesExceeded = "MaxRetriesExceeded"
)

var (
	TolerationForNodeNotReady = v1.Toleration{
		Key:      v1.TaintNodeNotReady,
		Operator: v1.TolerationOpExists,
		Effect:   v1.TaintEffectNoExecute,
	}

	TolerationForNodeUnreachable = v1.Toleration{
		Key:      v1.TaintNodeUnreachable,
		Operator: v1.TolerationOpExists,
		Effect:   v1.TaintEffectNoExecute,
	}
)

// extractUserUUIDFromNamespace extracts userUUID from namespace.
// For BSC mode, namespace format is "ns-<userUUID>", so we need to extract the userUUID part.
func extractUserUUIDFromNamespace(namespace string) string {
	if strings.HasPrefix(namespace, "ns-") {
		return namespace[3:] // 去掉 "ns-" 前缀
	}
	return ""
}

// Jitter returns a time.Duration between duration and duration + maxFactor *
// duration.
//
// This allows clients to avoid converging on periodic behavior. If maxFactor
// is no more than 0.0, no jitter will performed.
func Jitter(duration time.Duration, maxFactor float64) time.Duration {
	if maxFactor <= 0.0 {
		return duration
	}
	wait := duration + time.Duration(rand.Float64()*maxFactor*float64(duration))
	return wait
}

//go:generate mockgen -destination ./mock_pod_cache.go -package baidubci -self_package icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci PodCache

type PodCache interface {
	Start(ctx context.Context) error
	GetPods(ctx context.Context) ([]*v1.Pod, error)
	GetPod(ctx context.Context, namespace, name string) (*v1.Pod, error)
	GetPodByPodID(ctx context.Context, podID string) (*v1.Pod, error)
	GetPodIDs(ctx context.Context) (map[string]*v1.Pod, error)
	NotifyPodCreating(ctx context.Context, podID string, pod *v1.Pod)
	NotifyPods(ctx context.Context, f func(*v1.Pod))
	GetSubnetUsage(ctx context.Context, subnetID string) BCIResources
	AddSubnetUsage(ctx context.Context, subnetID string, r BCIResources)
	GetPodDetail(ctx context.Context, namespace, name string) (*bci.DescribePodResponse, error)
}

func NewPodCache(ctx context.Context, p *BCIProvider, maxReqInFlight int, syncInterval, regularSyncInterval time.Duration,
	regularSyncJitterFactor float64) PodCache {
	return &podCache{
		clock:                   clock.NewClock(),
		syncInterval:            syncInterval,
		regularSyncInterval:     regularSyncInterval,
		regularSyncJitterFactor: regularSyncJitterFactor,
		pods:                    make(map[string]*cachedPod),
		podIDs:                  make(map[string]string),
		creatingPods:            make(map[string]*creatingPod),
		reqTokens:               make(chan struct{}, maxReqInFlight),
		provider:                p,
		syncEnd:                 make(chan struct{}, 1),
		notify:                  func(*v1.Pod) {}, // default no-op
		subnetLongToShort:       make(map[string]string),
		subnetUsages:            make(map[string]*BCIResources),
		toNotFoundTimeout:       30 * time.Minute,
	}
}

func (pc *podCache) Start(ctx context.Context) error {
	_, err := pc.getAllPods(ctx)
	if err != nil {
		// 日志打印不要使用了 log.Fatal() ，Fatal打印日志信息后会调用 os.Exit(1) 立即终止程序，如需终止程序显式调用
		// log.G(ctx).WithError(err).Fatal("initialize podCache failed")
		log.G(ctx).WithError(err).Error("initialize podCache failed")
		return err
	}
	// start sync
	go pc.startSync(ctx)
	// delete InternalError pod
	go func() {
		time.Sleep(time.Minute * 3)
		wait.UntilWithContext(ctx, pc.deleteInternalErrPod, time.Minute*3)
	}()

	// start toNotFoundChecker
	go func() {
		// Wait 1m to start to avoid re-create/set not-found race when vk restarts.
		<-pc.clock.After(time.Minute)
		wait.UntilWithContext(ctx, pc.toNotFoundChecker, time.Minute)
	}()
	return nil
}

type podCache struct {
	sync.RWMutex

	clock        clock.Clock
	syncInterval time.Duration
	pods         map[string]*cachedPod   // key is <namespace>/<podName>/<uid>
	podIDs       map[string]string       // used for searching by podID, key is podID and value is cacheKey
	creatingPods map[string]*creatingPod // record creating pods to tolerate cache latency, key is podID
	reqTokens    chan struct{}
	provider     *BCIProvider
	lastGC       time.Time

	// Regular sync is triggered for every regularSyncInterval for each pod to avoid
	// data inconsistency due to master-slave sync of bci console database.
	regularSyncInterval     time.Duration
	regularSyncJitterFactor float64

	// for test
	syncEnd chan struct{}

	notify func(*v1.Pod)

	subnetLock        sync.RWMutex
	subnetLongToShort map[string]string
	subnetUsages      map[string]*BCIResources

	toNotFoundTimeout time.Duration
}

type cachedPod struct {
	v1Pod       *v1.Pod
	podID       string
	createdTime time.Time
	updatedTime time.Time
	lastStatus  bci.PodStatus // work around for unreliable updatedTime of bci console :(

	// lastSync is the last time the pod data sync from bci occured.
	// This field is used to invoke regular sync to avoid data inconsistency due to
	// master-slave sync of bci console database.
	lastSync time.Time

	subnetID, subnetUUID string

	// podDetail is the raw detail info fetched from BCI.
	// A non-nil podDetail indicates a notification is needed.
	podDetail *bci.DescribePodResponse
}

type creatingPod struct {
	v1Pod      *v1.Pod
	creatingAt time.Time
	notified   bool
}

func (pc *podCache) getSubnetKey(userUUID, subnetID string) string {
	return userUUID + "/" + subnetID
}

func (pc *podCache) getCacheKey(namespace, name, uid string) string {
	return namespace + "/" + name + "/" + uid
}

func (pc *podCache) splitCacheKey(key string) (ns, name, uid string, err error) {
	parts := strings.Split(key, "/")
	if len(parts) != 3 {
		err = fmt.Errorf("invalid cache key %s", key)
		return
	}
	ns = parts[0]
	name = parts[1]
	uid = parts[2]
	return
}

func (pc *podCache) NotifyPods(ctx context.Context, f func(*v1.Pod)) {
	pc.notify = f
}

func (pc *podCache) getUIDPod(ctx context.Context, namespace, name, uid string) (*v1.Pod, error) {
	log.G(ctx).Infof("getUIDPod %s/%s/%s from podCache starts", namespace, name, uid)
	pc.RLock()
	defer pc.RUnlock()

	key := pc.getCacheKey(namespace, name, uid)
	if pod, ok := pc.pods[key]; ok {
		return pod.v1Pod.DeepCopy(), nil
	}

	log.G(ctx).Infof("pod %s not found in podCache", key)
	return nil, errdefs.AsNotFound(fmt.Errorf("getUIDPod can't find Pod %s", key))
}

func (pc *podCache) GetPod(ctx context.Context, namespace, name string) (*v1.Pod, error) {
	log.G(ctx).Infof("GetPod %s/%s from podCache starts", namespace, name)
	k8sPod, err := pc.provider.resourceManager.GetPod(name, namespace)
	if err != nil {
		log.G(ctx).WithError(err).Errorf("GetPod can't find k8s Pod %s/%s", namespace, name)
		return nil, err
	}
	if _, ok := k8sPod.GetAnnotations()[DsInjectionAnnotationKey]; ok {
		return k8sPod, nil
	}
	return pc.getUIDPod(ctx, namespace, name, string(k8sPod.GetUID()))
}

func (pc *podCache) getUIDPodDetail(ctx context.Context, namespace, name, uid string) (*bci.DescribePodResponse, error) {
	log.G(ctx).Infof("getUIDPodDetail %s/%s/%s from podCache starts", namespace, name, uid)
	pc.RLock()
	defer pc.RUnlock()

	key := pc.getCacheKey(namespace, name, uid)
	if pod, ok := pc.pods[key]; ok {
		return pod.podDetail, nil
	}

	log.G(ctx).Infof("pod %s not found in podCache", key)
	return nil, errdefs.AsNotFound(fmt.Errorf("getUIDPod can't find Pod %s", key))
}

func (pc *podCache) GetPodDetail(ctx context.Context, namespace, name string) (*bci.DescribePodResponse, error) {
	log.G(ctx).Infof("GetPodDetail %s/%s from podCache starts", namespace, name)
	k8sPod, err := pc.provider.resourceManager.GetPod(name, namespace)
	if err != nil {
		log.G(ctx).WithError(err).Errorf("GetPodDetail can't find k8s Pod %s/%s", namespace, name)
		return nil, err
	}
	return pc.getUIDPodDetail(ctx, namespace, name, string(k8sPod.GetUID()))
}

// GetPodByPodID get *v1.Pod by podID.
// If the pod can be found in the cache, the pod obejct built from remote is returned,
// otherwise the object used for creation is returned if exists.
func (pc *podCache) GetPodByPodID(ctx context.Context, podID string) (*v1.Pod, error) {
	ctx, span := trace.StartSpan(ctx, "podCache.GetPodByPodID")
	defer span.End()

	log.G(ctx).Infof("GetPodByPodID %s from podCache starts", podID)
	// podIDs may be cleaned up below so write lock is used here.
	pc.Lock()
	defer pc.Unlock()

	if cacheKey, ok := pc.podIDs[podID]; ok {
		if pod, ok := pc.pods[cacheKey]; ok && pod.podID == podID {
			return pod.v1Pod, nil
		}
		log.G(ctx).Infof("remove invalid podID reference in pod cache: %s => %s", podID, cacheKey)
		delete(pc.podIDs, podID)
	}

	if cp, ok := pc.creatingPods[podID]; ok {
		return cp.v1Pod, nil
	}

	return nil, errdefs.AsNotFound(fmt.Errorf("GetPodByPodID can't find Pod %s", podID))
}

func (pc *podCache) NotifyPodCreating(ctx context.Context, podID string, pod *v1.Pod) {
	pc.Lock()
	defer pc.Unlock()
	pc.creatingPods[podID] = &creatingPod{
		v1Pod:      pod,
		creatingAt: pc.clock.Now(),
	}
}

func (pc *podCache) GetPods(ctx context.Context) ([]*v1.Pod, error) {
	log.G(ctx).Infof("GetPods from podCache starts")

	pc.RLock()
	defer pc.RUnlock()

	all := make([]*v1.Pod, 0, len(pc.pods))
	for _, p := range pc.pods {
		all = append(all, p.v1Pod.DeepCopy())
	}
	return all, nil
}

// GetPodIDs returns map containing all known podIDs with podID as key and pod UID as value.
func (pc *podCache) GetPodIDs(ctx context.Context) (map[string]*v1.Pod, error) {
	log.G(ctx).Infof("GetPodIDs from podCache starts")

	pc.RLock()
	defer pc.RUnlock()

	all := make(map[string]*v1.Pod, len(pc.pods)+len(pc.creatingPods))
	for _, p := range pc.pods {
		all[p.podID] = p.v1Pod.DeepCopy()
	}
	for podID, pod := range pc.creatingPods {
		all[podID] = pod.v1Pod.DeepCopy()
	}

	return all, nil
}

func (pc *podCache) GetSubnetUsage(ctx context.Context, subnetID string) BCIResources {
	subnetKey := pc.getSubnetKey(util.GetUserUUID(ctx), subnetID)
	pc.subnetLock.RLock()
	v, ok := pc.subnetUsages[subnetKey]
	pc.subnetLock.RUnlock()

	log.G(ctx).Debugf("get subnet usage by key %s: %+v", subnetKey, v)
	if !ok {
		return BCIResources{}
	}
	if v == nil {
		v = &BCIResources{}
	}
	return BCIResources{
		CPUInCore:  v.CPUInCore,
		MemoryInGB: v.MemoryInGB,
		Pods:       v.Pods,
	}
}

func (pc *podCache) AddSubnetUsage(ctx context.Context, subnetID string, r BCIResources) {
	subnetKey := pc.getSubnetKey(util.GetUserUUID(ctx), subnetID)
	pc.subnetLock.Lock()
	pc.subnetUsages[subnetKey] = ResourceAdd(pc.subnetUsages[subnetKey], r)
	pc.subnetLock.Unlock()
}

func (pc *podCache) toNotFoundChecker(ctx context.Context) {
	ctx, span := trace.StartSpan(ctx, "podCache.toNotFoundChecker")
	defer span.End()

	ctx = span.WithField(ctx, "traceID", uuid.NewV4().String())

	log.G(ctx).Debug("check starts")

	pods := pc.provider.resourceManager.GetPods()
	for _, pod := range pods {
		// filter daemonset pod
		if _, ok := pod.GetAnnotations()[DsInjectionAnnotationKey]; ok {
			continue
		}
		if pod.Status.Phase == v1.PodFailed || pod.Status.Phase == v1.PodSucceeded {
			continue
		}
		if pod.GetDeletionTimestamp() != nil {
			continue
		}
		if pod.Spec.NodeName != pc.provider.nodeName {
			continue
		}
		if pc.clock.Now().Sub(pod.ObjectMeta.CreationTimestamp.Time) <= pc.toNotFoundTimeout {
			continue
		}

		pc.RLock()
		cached, ok := pc.pods[pc.getCacheKey(pod.GetNamespace(), pod.GetName(), string(pod.GetUID()))]
		pc.RUnlock()

		if ok && pod.GetUID() == cached.v1Pod.GetUID() {
			continue
		}

		logFields := log.Fields{
			"creationTimestamp": pod.ObjectMeta.CreationTimestamp.Time.Format(time.RFC3339),
			"uid":               pod.GetUID(),
			"phase":             pod.Status.Phase,
		}
		if pod.Status.Reason == podStatusReasonProviderFailed {
			// To MaxRetriesExceeded.
			log.G(ctx).WithFields(logFields).Infof("notify Pending pod %s/%s to maxRetriesExceeded", pod.GetNamespace(), pod.GetName())
			pc.notify(pc.toMaxRetriesExceeded(ctx, pod))
		} else {
			// check whether the pod exists in the creatingPods list of the pod cache
			if podID := pod.GetAnnotations()[PodIDAnnotationKey]; podID != "" {
				pc.RLock()
				_, ok = pc.creatingPods[podID]
				pc.RUnlock()
				if ok {
					continue
				}
				// check whether the bci pod with pod-id exists
				// 从 namespace 中提取正确的 userUUID，用于 BSC 模式的鉴权
				correctUserUUID := extractUserUUIDFromNamespace(pod.GetNamespace())
				var describeCtx context.Context
				if correctUserUUID != "" {
					// 创建包含正确 userUUID 的 context
					describeCtx = util.WithUserUUID(ctx, correctUserUUID)
				} else {
					describeCtx = ctx
				}
				_, err := pc.provider.chooseBCIClient(nil).DescribePod(describeCtx, podID, pc.provider.getSignOption(describeCtx))
				if err != nil {
					log.G(ctx).Warnf("toNotFoundChecker: get pod %s%s from bci fail err %v", pod.GetNamespace(), pod.GetName(), err)
				} else {
					// the bci pod exists
					continue
				}
			} else {
				// check whether the bci pod without pod-id exists
				// 从 namespace 中提取正确的 userUUID，用于 BSC 模式的鉴权
				correctUserUUID := extractUserUUIDFromNamespace(pod.GetNamespace())
				var filterCtx context.Context
				if correctUserUUID != "" {
					// 创建包含正确 userUUID 的 context
					filterCtx = util.WithUserUUID(ctx, correctUserUUID)
				} else {
					filterCtx = ctx
				}
				_, err := pc.provider.filterBCIPod(filterCtx, pod.GetNamespace(), pod.GetName())
				if err != nil {
					log.G(ctx).Warnf("toNotFoundChecker: list pod %s%s from bci fail err %v", pod.GetNamespace(), pod.GetName(), err)
				} else {
					// the bci pod exists
					continue
				}
			}

			// To NotFound.
			log.G(ctx).WithFields(logFields).Infof("notify Pending pod %s/%s to not found", pod.GetNamespace(), pod.GetName())
			pc.notify(pc.toNotFound(ctx, pod))
		}

	}

	log.G(ctx).Debug("check ends")
}

// getAllPods gets all pods managed by us from bci.
func (pc *podCache) getAllPods(ctx context.Context) ([]*v1.Pod, error) {
	ctx, span := trace.StartSpan(ctx, "podCache.getAllPods")
	defer span.End()

	allUserUUIDs, err := pc.provider.userStorage.GetAllUsers(ctx, pc.provider.nodeName)
	if err != nil {
		return nil, err
	}
	if allUserUUIDs == nil {
		// 一般情况为单租户，在这里进行赋值
		allUserUUIDs = []string{""}
	}

	// get pod detail in limited parallel
	allUsersPods := make([][]*v1.Pod, len(allUserUUIDs))
	errs := make(chan error, len(allUserUUIDs))
	var wg sync.WaitGroup
	wg.Add(len(allUserUUIDs))
	for i, uuid := range allUserUUIDs {
		go func(index int, userUUID string) {
			defer wg.Done()
			ctx = util.WithUserUUID(span.WithField(ctx, "userUUID", userUUID), userUUID)
			pods, err := pc.getPodsForSingleUser(ctx)
			if err != nil {
				log.G(ctx).WithError(err).Errorf("error list bci pod for user=%s", userUUID)
				errs <- fmt.Errorf("list bci pod for user=%s err: %w", userUUID, err)
				return
			}
			allUsersPods[index] = pods
		}(i, uuid)
	}
	wg.Wait()

	if len(errs) == 0 {
		allPods := make([]*v1.Pod, 0)
		for _, pods := range allUsersPods {
			if len(pods) > 0 {
				allPods = append(allPods, pods...)
			}
		}
		return allPods, nil
	}

	errStr := ""
	failedCnt := len(errs)
	for i := 0; i < failedCnt; i++ {
		errStr += (<-errs).Error() + " ;; "
	}
	log.G(ctx).WithField("failedCnt", failedCnt).Errorf("fail to get pods for all users: %s", errStr)

	return nil, errors.New(errStr)
}

// getPodsForSingleUser gets all pods of single user managed by us from bci.
func (pc *podCache) getPodsForSingleUser(ctx context.Context) ([]*v1.Pod, error) {
	ctx, span := trace.StartSpan(ctx, "podCache.getPodsForSingleUser")
	defer span.End()

	userUUID := util.GetUserUUID(ctx)

	bpods, err := pc.provider.listMyBCIPods(ctx)
	if err != nil {
		return nil, fmt.Errorf("fail to retrieve bci pods list: %w", err)
	}

	// get pod detail in parallel
	pods := make([]*v1.Pod, len(bpods))
	errs := make(chan error, len(bpods))
	subnetUsagesBySubnetUUID := make(map[string]*BCIResources)
	var wg sync.WaitGroup
	for i, bpod := range bpods {
		if bpod.SubnetUUID != "" {
			subnetUsagesBySubnetUUID[bpod.SubnetUUID] = ResourceAdd(subnetUsagesBySubnetUUID[bpod.SubnetUUID], BCIResources{
				CPUInCore:  bpod.VCPU,
				MemoryInGB: bpod.MemoryInGB,
				Pods:       1,
			})
		}

		wg.Add(1)
		go func(index int, bpod *bci.Pod) {
			defer wg.Done()
			pc.reqTokens <- struct{}{}
			podDetail, err := pc.provider.chooseBCIClient(bpod).DescribePod(ctx, bpod.PodID, pc.provider.getSignOption(ctx))
			<-pc.reqTokens
			if err != nil {
				log.G(ctx).WithFields(log.Fields{
					"name": bpod.Name,
					"id":   bpod.PodID,
				}).WithError(err).Error("error getting pod detailed info")
				errs <- fmt.Errorf("describe pod name=%s id=%s err: %w", bpod.Name, bpod.PodID, err)
				return
			}
			p, err := bciPodDetailToPod(ctx, podDetail)
			if err != nil {
				log.G(ctx).WithFields(log.Fields{
					"name": bpod.Name,
					"id":   bpod.PodID,
				}).WithError(err).Error("error converting bci pod to pod")
				errs <- fmt.Errorf("convert pod name=%s id=%s err: %w", bpod.Name, bpod.PodID, err)
				return
			}
			var subnetID, subnetUUID string
			if sbn := podDetail.Subnet; sbn != nil {
				subnetID = sbn.ShortID
				subnetUUID = sbn.SubnetUUID
			} else {
				log.G(ctx).WithFields(log.Fields{
					"podID":   podDetail.PodID,
					"podUUID": podDetail.PodUUID,
				}).Warn("ignore subnet usage statistics due to subnet info is null")
			}
			pods[index] = p

			pc.Lock()
			pc.pods[pc.getCacheKey(p.GetNamespace(), p.GetName(), string(p.GetUID()))] = &cachedPod{
				v1Pod:       p.DeepCopy(),
				podID:       podDetail.PodID,
				createdTime: podDetail.CreatedTime,
				updatedTime: podDetail.UpdatedTime,
				lastStatus:  podDetail.Status,
				lastSync:    pc.clock.Now(),
				subnetID:    subnetID,
				subnetUUID:  subnetUUID,
				podDetail:   podDetail,
			}
			pc.podIDs[podDetail.PodID] = pc.getCacheKey(p.GetNamespace(), p.GetName(), string(p.GetUID()))
			pc.Unlock()

			pc.subnetLock.RLock()
			_, ok := pc.subnetLongToShort[subnetUUID]
			pc.subnetLock.RUnlock()
			if !ok {
				pc.subnetLock.Lock()
				pc.subnetLongToShort[subnetUUID] = subnetID
				pc.subnetLock.Unlock()
			}

		}(i, bpod)
	}
	wg.Wait()

	for uuid, usage := range subnetUsagesBySubnetUUID {
		pc.subnetLock.RLock()
		shortID, ok := pc.subnetLongToShort[uuid]
		pc.subnetLock.RUnlock()
		if !ok {
			// no short id can be found means that no podDetail is known yet, so treat the pod as non-existing.
			log.G(ctx).Warnf("no short id found for subnet uuid %s, skip update usage")
			continue
		}
		subnetKey := pc.getSubnetKey(userUUID, shortID)
		log.G(ctx).WithField("subnet", subnetKey).Infof("update subnet usage to %+v", usage)
		pc.subnetLock.Lock()
		pc.subnetUsages[subnetKey] = usage
		pc.subnetLock.Unlock()
	}

	if len(errs) == 0 {
		return pods, nil
	}

	errStr := ""
	failedCnt := len(errs)
	for i := 0; i < failedCnt; i++ {
		errStr += (<-errs).Error() + " ; "
	}
	log.G(ctx).WithField("failedCnt", failedCnt).Errorf("fail to get all pods in bci: %s", errStr)

	return nil, errors.New(errStr)
}

func (pc *podCache) startSync(ctx context.Context) {
	ticker := pc.clock.NewTicker(pc.syncInterval)
	defer ticker.Stop()

	log.G(ctx).WithFields(log.Fields{
		"interval":            pc.syncInterval.String(),
		"regularSyncInterval": pc.regularSyncInterval.String(),
	}).Info("start podCache syncloop")

	for {
		select {
		case <-ctx.Done():
			log.G(ctx).Info("podCache sync loop exits")
			return
		case <-ticker.C():
			log.G(ctx).Info("podCache sync starts at ", pc.clock.Now().Format(timeFormat))

			allUserUUIDs, err := pc.provider.userStorage.GetAllUsers(ctx, pc.provider.nodeName)
			if err != nil {
				log.G(ctx).WithError(err).Error("fail to sync pod cache: fail to GetAllUsers")
				continue
			}
			if allUserUUIDs == nil {
				// 一般情况为单租户，在这里进行赋值
				allUserUUIDs = []string{""}
			}
			var wg sync.WaitGroup
			for _, userUUID := range allUserUUIDs {
				wg.Add(1)
				go func(userUUID string) {
					defer wg.Done()
					ctx := util.WithUserUUID(ctx, userUUID)
					if err := pc.syncPodsForSingleUser(ctx); err != nil {
						log.G(ctx).WithError(err).WithField("userUUID", userUUID).Error("fail to sync pod cache")
					}
				}(userUUID)
			}
			wg.Wait()

			log.G(ctx).Info("podCache sync ends")
			select {
			case pc.syncEnd <- struct{}{}:
			default:
			}
		}
	}
}

// syncPodsForSingleUser sync pods for single user.
func (pc *podCache) syncPodsForSingleUser(ctx context.Context) error {
	syncTime := time.Now().Format("2006.01.02 15.04.05")
	ctx, span := trace.StartSpan(ctx, "podCache.syncPodsForSingleUser"+"-"+syncTime)
	defer span.End()

	ctx = span.WithField(ctx, "syncID", uuid.NewV4().String())
	log.G(ctx).Info("start sync pods to podCache for single user")

	// 调用轻量级的list接口
	remotePods, err := pc.provider.listBCIPodsForLight(ctx)
	if err != nil {
		log.G(ctx).WithError(err).Error("podCache: fail to listMyBCIPods")
		return err
	}

	var wg sync.WaitGroup
	errs := make(chan error, len(remotePods))
	podID2CacheKey := make(map[string]string, len(remotePods))
	CacheKey2podID := make(map[string]string, len(remotePods))

	// 调用pod describe接口，获取pod详情，更新 cachePod cache
	errs = pc.updatePodCacheUseRemotePod(ctx, remotePods, podID2CacheKey, CacheKey2podID, &wg, errs)

	// Do notify for created pods (set status and annotations) and creating pods (set annotations).
	beginSyncPodStatusTimeStamp := pc.clock.Now()
	pc.Lock()
	// 从缓存中获取pod的状态
	for _, cachePod := range pc.pods {
		if cachePod.podDetail == nil {
			continue
		}
		wg.Add(1)
		go func(cachePod *cachedPod) {
			defer wg.Done()
			cp := cachePod
			cacheKey := podID2CacheKey[cp.podID]
			if notifyStatusUpdateErr := pc.doNotify(ctx, cp, nil); notifyStatusUpdateErr != nil {
				log.G(ctx).WithError(notifyStatusUpdateErr).Errorf("fail to notify cachePod status updated: %s", cacheKey)
			} else {
				// reset podDetail.
				cp.podDetail = nil
			}
		}(cachePod)
	}
	wg.Wait()
	log.G(ctx).Infof("syncPodsForSingleUser: sync cached pod status cost time: %.3f seconds", time.Since(beginSyncPodStatusTimeStamp).Seconds())

	beginSyncCreatingPodTimeStamp := pc.clock.Now()
	for _, podInCreating := range pc.creatingPods {
		if podInCreating.notified {
			continue
		}
		wg.Add(1)
		go func(podInCreating *creatingPod) {
			defer wg.Done()
			cp := podInCreating
			if err := pc.doNotify(ctx, nil, cp); err != nil {
				log.G(ctx).WithError(err).Errorf("fail to notify creating pod %s/%s/%s",
					cp.v1Pod.GetNamespace(), cp.v1Pod.GetName(), cp.v1Pod.GetUID())
			} else {
				cp.notified = true
			}
		}(podInCreating)
	}
	wg.Wait()
	log.G(ctx).Infof("syncPodsForSingleUser: sync creating pod status cost time: %.3f seconds", time.Since(beginSyncCreatingPodTimeStamp).Seconds())
	pc.Unlock()

	// TODO: should we call NotifyPods if bpod is deleted
	pc.Lock()
	removedPods := make([]*cachedPod, 0)
	for cacheKey, cachePod := range pc.pods {
		if _, ok := CacheKey2podID[cacheKey]; !ok {
			// 找出缓存中存在，BCI list接口中不存在的pod
			podNamespace := cachePod.v1Pod.Namespace
			podName := cachePod.v1Pod.Name

			// describe bci cachePod
			if cachePod.podID != "" {
				// 从 namespace 中提取正确的 userUUID，用于 BSC 模式的鉴权
				correctUserUUID := extractUserUUIDFromNamespace(podNamespace)
				var describeCtx context.Context
				if correctUserUUID != "" {
					// 创建包含正确 userUUID 的 context
					describeCtx = util.WithUserUUID(ctx, correctUserUUID)
				} else {
					describeCtx = ctx
				}
				_, err = pc.provider.chooseBCIClient(nil).DescribePod(describeCtx, cachePod.podID, pc.provider.getSignOption(describeCtx))
				if err == nil {
					continue
				}
				log.G(ctx).Warnf("podCache: get cachePod %s/%s podID %s from bci fail err %v", podNamespace, podName, cachePod.podID, err)
			}
			if strings.Contains(err.Error(), "ZoneNoResourceSpecification") {
				continue
			}

			if strings.Contains(err.Error(), "AccessNonOwnedResource") || strings.Contains(err.Error(), "PermissionDeny") {
				// bci cachePod not exist, remove it from cache
				removedPods = append(removedPods, cachePod)
				delete(pc.pods, cacheKey)
				delete(pc.podIDs, cachePod.podID)
				delete(pc.creatingPods, cachePod.podID)
			}
		}
	}
	pc.Unlock()

	// 清理悬空的pod，用户集群存在但BCI中不存在的pod
	removePodsIdentifiers := pc.cleanPodsFromK8S(ctx, removedPods)
	if len(removedPods) > 0 {
		log.G(ctx).WithField("removedPods", strings.Join(removePodsIdentifiers, ",")).Infof(
			"podCache: %d pods removed from podCache", len(removedPods))
	}

	if len(errs) == 0 {
		now := pc.clock.Now()
		// GC creatingPods if necessary.
		if now.Sub(pc.lastGC) > 10*time.Minute {
			expiredPods := make([]string, 0)
			pc.Lock()
			for k, creating := range pc.creatingPods {
				if now.Sub(creating.creatingAt) > 10*time.Minute {
					delete(pc.creatingPods, k)
					expiredPods = append(expiredPods, k)
				}
			}
			pc.lastGC = now
			pc.Unlock()
			log.G(ctx).WithField("pods", strings.Join(expiredPods, ",")).Infof("%d creating pods GCed", len(expiredPods))
		}

		return nil
	}

	errStr := ""
	failedCnt := len(errs)
	for i := 0; i < failedCnt; i++ {
		errStr += (<-errs).Error() + " ;; "
	}
	log.G(ctx).WithField("failedCnt", failedCnt).Errorf("podCache: fail to syncPodsForSingleUser: %s", errStr)

	return errors.New(errStr)
}

func (pc *podCache) updatePodCacheUseRemotePod(ctx context.Context, remotePods []*bci.Pod, podID2CacheKey map[string]string,
	CacheKey2podID map[string]string, wg *sync.WaitGroup, errs chan error) chan error {
	beginBatchDescribeTimeStamp := pc.clock.Now()
	batchSize := 200
	totalNum := len(remotePods)
	round := (totalNum + batchSize - 1) / batchSize
	podIDRound := make([][]string, 0, round)
	log.G(ctx).Infof("remote bci pods num: %v, round: %v, batchSize: %v", totalNum, round, batchSize)
	// 把remotePods分成多个批次，然后调用批量查询pod详情接口
	for i := 0; i < round; i++ {
		start := i * batchSize
		end := start + batchSize
		if end > totalNum {
			end = totalNum
		}
		podIds := make([]string, 0, end-start)
		for j := start; j < end; j++ {
			rp := remotePods[j]
			namespace := getBCILabelValue(rp, NamespaceLabelKey)
			name := getBCILabelValue(rp, PodNameLabelKey)
			uid := getBCILabelValue(rp, UIDLabelKey)
			cacheKey := pc.getCacheKey(namespace, name, uid)
			podID2CacheKey[rp.PodID] = cacheKey
			CacheKey2podID[cacheKey] = rp.PodID

			pc.RLock()
			// vk启动时会更新这个cache，vk运行中创建的pod在这里添加到cache中
			knownPod, exist := pc.pods[cacheKey]
			pc.RUnlock()
			if exist {
				// pod status 是否更新
				podUpdated := knownPod.updatedTime.Before(rp.UpdatedTime) || (knownPod.lastStatus != rp.Status)
				log.G(ctx).Infof("pod %v updated result is %v", rp.PodID, podUpdated)
				if !podUpdated {
					// 是否到达强制更新的时间点
					jitterRegularInterval := Jitter(pc.regularSyncInterval, pc.regularSyncJitterFactor)
					needRegularSync := knownPod.lastSync.Before(beginBatchDescribeTimeStamp.Add(-jitterRegularInterval))
					if !needRegularSync {
						// 如果BCI pod的status没有更新 && 没有到达强制更新的时间间隔，跳过更新缓存
						continue
					} else {
						// 到达了强制更新pod状态的时间间隔
						log.G(ctx).WithFields(log.Fields{
							"cacheKey": cacheKey,
							"podID":    rp.PodID,
							"lastSync": knownPod.lastSync.Format(timeFormat),
						}).Info("trigger regular sync pod status from bci")
					}
				}
			} else {
				// BCI侧新创建的pod，把这个pod记录从rescuer队列中删除
				log.G(ctx).Infof("bci pod for %s/%s/%s is newly created", namespace, name, uid)
				if pc.provider.podRescuer != nil {
					pc.provider.podRescuer.NotifyBCICreated(ctx, namespace, name, uid)
				}
			}
			podIds = append(podIds, rp.PodID)
		}
		if len(podIds) > 0 {
			podIDRound = append(podIDRound, podIds)
		}
	}
	log.G(ctx).Infof("need request newest pod detail list is: %+v", podIDRound)

	// 调用批量查询pod详情接口，并更新pod cache
	for _, podIds := range podIDRound {
		wg.Add(1)
		go func(podIds []string) {
			defer wg.Done()
			// DescribeBatchPodForLight 接口限流
			pc.reqTokens <- struct{}{}
			podDetails, err := pc.provider.chooseBCIClient(nil).DescribeBatchPodForLight(ctx, podIds, pc.provider.getSignOption(ctx))
			<-pc.reqTokens
			if err != nil {
				log.G(ctx).WithError(err).WithField("DescribeBatchPods", podIds).Error("podCache: fail to batch describe pod")
				errs <- fmt.Errorf("describe pod %s err: %w", podIds, err)
				return
			}

			for _, podDetail := range podDetails {
				v1Pod, transferErr := bciPodDetailToPod(ctx, podDetail)
				if transferErr != nil {
					log.G(ctx).WithError(transferErr).Error("podCache: fail to transfer bciPod to v1Pod")
					errs <- fmt.Errorf("translate bci pod to v1Pod transferErr: %w", transferErr)
					continue
				}
				cacheKey := podID2CacheKey[podDetail.PodID]
				log.G(ctx).WithFields(log.Fields{
					"updatedTime": podDetail.UpdatedTime.Format(timeFormat),
					"podID":       podDetail.PodID,
					"podUUID":     podDetail.PodUUID,
					"status":      podDetail.Status,
				}).Infof("podCache receives update for pod %s/%s", podDetail.Name)

				pc.Lock()
				pc.pods[cacheKey] = &cachedPod{
					v1Pod:       v1Pod.DeepCopy(),
					podID:       podDetail.PodID,
					createdTime: podDetail.CreatedTime,
					updatedTime: podDetail.UpdatedTime,
					lastStatus:  podDetail.Status,
					lastSync:    pc.clock.Now(),
					podDetail:   podDetail,
				}
				pc.podIDs[podDetail.PodID] = cacheKey
				delete(pc.creatingPods, podDetail.PodID)
				pc.Unlock()
			}
		}(podIds)
	}
	wg.Wait()
	log.G(ctx).Infof("syncPodsForSingleUser: updatePodCacheUseRemotePod cost time: %.3f seconds", time.Since(beginBatchDescribeTimeStamp).Seconds())
	return errs
}

func (pc *podCache) cleanPodsFromK8S(ctx context.Context, removedPods []*cachedPod) []string {
	// Check if any notification should be triggered for removedPod.
	var removePodsIdentifiers []string
	for _, removePod := range removedPods {
		name, ns, uid := removePod.v1Pod.GetName(), removePod.v1Pod.GetNamespace(), removePod.v1Pod.GetUID()
		identifier := fmt.Sprintf("%s/%s[%s](%s)", ns, name, uid, removePod.podID)
		removePodsIdentifiers = append(removePodsIdentifiers, identifier)
		k8sPod, getPodFromK8SErr := pc.provider.resourceManager.GetPod(name, ns)
		if getPodFromK8SErr != nil && !k8serror.IsNotFound(getPodFromK8SErr) {
			log.G(ctx).WithError(getPodFromK8SErr).Errorf("fail to get k8s cachePod for removedPod %s", removePod)
			continue
		}
		if k8sPod == nil || k8sPod.GetUID() != uid {
			// k8s cachePod has been removed or not the same cachePod
			continue
		}
		if k8sPod.GetDeletionTimestamp() != nil {
			// K8S cachePod is being deleted, notify terminal status of cachePod as the syncWrapper does to fully support grace period.
			// TODO: The terminal status notification should be done here or just after a successful delete api call.
			if !shouldSkipPodStatusUpdate(k8sPod) {
				updated := k8sPod.DeepCopy()
				updated.Status.Phase = v1.PodSucceeded
				now := metav1.NewTime(pc.clock.Now())
				for i, cs := range updated.Status.ContainerStatuses {
					updated.Status.ContainerStatuses[i].State.Terminated = &v1.ContainerStateTerminated{
						Reason:     statusTerminatedReason,
						Message:    containerStatusTerminatedMessage,
						FinishedAt: now,
					}
					if cs.State.Running != nil {
						updated.Status.ContainerStatuses[i].State.Terminated.StartedAt = cs.State.Running.StartedAt
						cs.State.Running = nil
					}
				}
				updated.Status.Reason = statusTerminatedReason

				pc.notify(updated)
				log.G(ctx).WithFields(log.Fields{
					"namespace": k8sPod.GetNamespace(),
					"name":      k8sPod.GetName(),
					"uid":       k8sPod.GetUID(),
				}).Debug("notified cachePod terminal status due to remote cachePod removal")
			}
			continue
		}

		// toNotFound is the flag determining whether cachePod status should be set to not found (Failed).
		toNotFound := false
		// TODO: should we depend on k8s cachePod status or last notified status
		switch k8sPod.Status.Phase {
		case v1.PodPending:
			if pc.clock.Now().Sub(k8sPod.ObjectMeta.CreationTimestamp.Time) <= pc.toNotFoundTimeout {
				if pc.provider.podRescuer != nil {
					// Here we find a pending cachePod to save if time window has not expired and podRescuer is enabled.
					log.G(ctx).Infof("going to save cachePod %s/%s(%s) due to bci %s has disappeared",
						ns, name, string(k8sPod.GetUID()), identifier)
					pc.provider.podRescuer.RescuePod(ctx, ns, name, string(k8sPod.GetUID()))
				}
				// The cachePod won't be set to NotFound until time window has expired and still cannot be found in bci.
				// toNotFoundChecker will do the notification when time window has expired for the cachePod.
			} else {
				toNotFound = true
			}
		case v1.PodRunning:
			toNotFound = true
		case v1.PodSucceeded, v1.PodFailed:
			// Not change the status of terminated pods.
		}
		if toNotFound {
			// Notify the cachePod is not found and set its phase to Failed.
			pc.notify(pc.toNotFound(ctx, k8sPod))
		}
	}
	return removePodsIdentifiers
}

// doNotify does a normal notify for a created/creating bci pod.
// Only one of created or creating may be specified.
func (pc *podCache) doNotify(ctx context.Context, created *cachedPod, creating *creatingPod) error {
	ctx, span := trace.StartSpan(ctx, "podCache.doNotify")
	defer span.End()

	if created == nil && creating == nil {
		return nil
	}
	var v1Pod *v1.Pod
	var syncAt time.Time
	if created != nil {
		v1Pod = created.v1Pod
		syncAt = created.lastSync
	} else {
		v1Pod = creating.v1Pod
		syncAt = creating.creatingAt
	}
	name := v1Pod.GetName()
	namespace := v1Pod.GetNamespace()
	uid := v1Pod.GetUID()

	ctx = span.WithFields(ctx, log.Fields{
		"namespace": namespace,
		"name":      name,
		"uid":       uid,
	})
	log.G(ctx).Debug("start pod notify")

	// Only podStatus from bci is used since bci does not hold the completed pod spec.
	podFromKubernetes, err := pc.provider.resourceManager.GetPod(name, namespace)
	if err != nil {
		log.G(ctx).WithError(err).Error("fail to get pod from kubernetes for notifying")
		if k8serror.IsNotFound(err) {
			// TODO: should we delete remote pod on not found err.
			return nil
		}
		return fmt.Errorf("get pod from kubernetes for notifying err: %w", err)
	}
	if shouldSkipPodStatusUpdate(podFromKubernetes) {
		log.G(ctx).Debug("skipping pod status update for terminated pod")
		return nil
	}
	if podFromKubernetes.GetUID() != uid {
		log.G(ctx).WithFields(log.Fields{
			"uidFromK8S": podFromKubernetes.GetUID(),
			"uidFromBCI": uid,
		}).Warn("skip notify due to uid mismatch for pod between k8s and bci")
		// TODO: this may be a leaked pod, should we handle this?
		return nil
	}
	notifyPod := podFromKubernetes.DeepCopy()
	if notifyPod.Annotations == nil {
		notifyPod.Annotations = make(map[string]string)
	}
	if created != nil {
		created.v1Pod.Status.DeepCopyInto(&notifyPod.Status)
		notifyPod.Annotations = ensureBCIAnnotations(notifyPod.Annotations, created.podDetail)
	} else {
		for k, v := range creating.v1Pod.GetAnnotations() {
			notifyPod.Annotations[k] = v
		}
	}
	// Mutate local cache to avoid unnecessary update event.
	podFromKubernetes.Annotations = notifyPod.Annotations
	// TODO: ensure permanent not-ready/unreachable tolerations.
	// Patch status cannot do the trick.

	// Avoid unnecessary notify (eg. when vk restarts).
	if cmp.Equal(notifyPod, podFromKubernetes) {
		log.G(ctx).Info("k8s pod is up to date, skip notify")
		return nil
	}
	pc.notify(notifyPod)
	log.G(ctx).WithFields(log.Fields{
		"phase":  notifyPod.Status.Phase,
		"syncAt": syncAt,
	}).Info("pod notified")
	return nil
}

func shouldSkipPodStatusUpdate(pod *v1.Pod) bool {
	return pod.Status.Phase == v1.PodSucceeded ||
		pod.Status.Phase == v1.PodFailed
}

func (pc *podCache) toMaxRetriesExceeded(ctx context.Context, k8sPod *v1.Pod) *v1.Pod {
	podStatus := k8sPod.Status.DeepCopy()
	podStatus.Phase = v1.PodFailed
	podStatus.Reason = podStatusReasonMaxRetriesExceeded

	log.G(ctx).WithFields(log.Fields{
		"namespace": k8sPod.GetNamespace(),
		"name":      k8sPod.GetName(),
		"uid":       k8sPod.GetUID(),
	}).Debug("Setting maxRetriesExceeded on pod status")
	notifyPod := k8sPod.DeepCopy()
	podStatus.DeepCopyInto(&notifyPod.Status)
	return notifyPod
}

func (pc *podCache) toNotFound(ctx context.Context, k8sPod *v1.Pod) *v1.Pod {
	podStatus := k8sPod.Status.DeepCopy()
	podStatus.Phase = v1.PodFailed
	podStatus.Reason = podStatusReasonNotFound
	podStatus.Message = podStatusMessageNotFound
	now := metav1.NewTime(pc.clock.Now())
	for i, c := range podStatus.ContainerStatuses {
		if c.State.Terminated != nil {
			// Do not overwrite the existed terminated msg.
			continue
		}
		if c.State.Waiting == nil && c.State.Running == nil && c.State.Terminated == nil {
			// Do not set container status if corresponding bci pod is never seen.
			continue
		}
		podStatus.ContainerStatuses[i].State.Terminated = &v1.ContainerStateTerminated{
			ExitCode:    containerStatusExitCodeNotFound,
			Reason:      containerStatusReasonNotFound,
			Message:     containerStatusMessageNotFound,
			FinishedAt:  now,
			ContainerID: c.ContainerID,
		}
		if running := c.State.Running; running != nil {
			podStatus.ContainerStatuses[i].State.Terminated.StartedAt = running.StartedAt
		}
		podStatus.ContainerStatuses[i].State.Running = nil
		podStatus.ContainerStatuses[i].State.Waiting = nil
		podStatus.ContainerStatuses[i].Ready = false
	}
	log.G(ctx).WithFields(log.Fields{
		"namespace": k8sPod.GetNamespace(),
		"name":      k8sPod.GetName(),
		"uid":       k8sPod.GetUID(),
	}).Debug("Setting pod not found on pod status")
	notifyPod := k8sPod.DeepCopy()
	podStatus.DeepCopyInto(&notifyPod.Status)
	return notifyPod
}

func (pc *podCache) deleteInternalErrPod(ctx context.Context) {
	pods := pc.provider.resourceManager.GetPods()
	InternalErrPods := make([]*v1.Pod, 0)
	for _, p := range pods {
		if time.Since(p.CreationTimestamp.Time) < time.Minute*5 {
			continue
		}
		// 1. Pod is in failed state and belong to BCI.
		if p.Status.Phase == v1.PodFailed && pc.provider.nodeName == p.Spec.NodeName {
			// 2.containerStatuses contains message "bci internal component init failed"
			log.G(ctx).WithFields(log.Fields{
				"namespace": p.Namespace,
				"name":      p.Name,
			}).Infof("deleteInternalErrPod: add pod %s/%s, pod status is: %+v to InternalErrPods", p.Namespace, p.Name, p.Status)
			InternalErrPods = append(InternalErrPods, p)
		}
	}
	if len(InternalErrPods) == 0 {
		return
	}

	// group pods by replicaset
	replicaset2pods := make(map[string][]*v1.Pod)
	for _, p := range InternalErrPods {
		replicasetName := ""
		for _, owner := range p.GetOwnerReferences() {
			if owner.Kind == "ReplicaSet" {
				replicasetName = owner.Name
				break
			}
		}
		if replicasetName == "" {
			log.G(ctx).WithFields(log.Fields{
				"namespace": p.Namespace,
				"name":      p.Name,
			}).Warnf("deleteInternalErrPod: Failed to get replicaset name for pod %s/%s", p.Namespace, p.Name)
			continue
		}
		if _, ok := replicaset2pods[replicasetName]; ok {
			replicaset2pods[replicasetName] = append(replicaset2pods[replicasetName], p)
		} else {
			replicaset2pods[replicasetName] = []*v1.Pod{p}
		}
	}

	for _, podSlice := range replicaset2pods {
		// sort pods by creation time
		sort.SliceStable(podSlice, func(i, j int) bool {
			return podSlice[i].CreationTimestamp.Before(&podSlice[j].CreationTimestamp)
		})
	}

	// delete pods, 1 replicaset reserved 1 pod for problem detection
	for _, podSlice := range replicaset2pods {
		podsLen := len(podSlice)
		// 若设置了保留失败的Pod,则至少保留一个现场以供定位问题
		startIndex := 0
		if pc.provider.bciProfileConfig != nil {
			log.G(ctx).Infof("deleteInternalErrPod: enableReserveFailedPod: %+v", pc.provider.bciProfileConfig.EnableReserveFailedPod)
			if pc.provider.bciProfileConfig.EnableReserveFailedPod {
				startIndex = 1
				log.G(ctx).Infof("deleteInternalErrPod: reserveFailedPod: %+v", podsLen)
				continue
			}
		}
		for i := startIndex; i < podsLen; i++ {
			p := podSlice[i]
			err := pc.provider.resourceManager.GetRawClient().CoreV1().Pods(p.Namespace).Delete(context.Background(), p.Name, metav1.DeleteOptions{})
			if err != nil {
				log.G(ctx).WithFields(log.Fields{
					"namespace": p.Namespace,
					"name":      p.Name,
				}).WithError(err).Warnf("deleteInternalErrPod: Failed to delete pod %s/%s, pod status is: %+v", p.Namespace, p.Name, p.Status)
			} else {
				log.G(ctx).WithFields(log.Fields{
					"namespace": p.Namespace,
					"name":      p.Name,
				}).Infof("deleteInternalErrPod: Successfully deleted pod %s/%s, pod status is: %+v", p.Namespace, p.Name, p.Status)
			}
		}
	}
}

func ensureBCIAnnotations(old map[string]string, bpod *bci.DescribePodResponse) map[string]string {
	if old == nil {
		old = make(map[string]string)
	}

	baseInfos := map[string]string{
		PodIDAnnotationKey:   bpod.PodID,
		OrderIDAnnotationKey: bpod.OrderID,
	}
	for k, v := range baseInfos {
		if v != "" {
			old[k] = v
		}
	}

	if bpod.InternalIPv6 != "" {
		old[PodAllocatedIPv6Annotation] = bpod.InternalIPv6
	}
	if bpod.PodUUID != bpod.PodID {
		old[PodUUIDAnnotationKey] = bpod.PodUUID
	}
	if bpod.Subnet != nil && bpod.Subnet.ShortID != "" {
		old[BCISubnetIDAnnotationKey] = bpod.Subnet.ShortID
	}
	if bpod.LogicalZone != "" {
		old[BCILogicalZoneAnnotationKey] = bpod.LogicalZone
	}

	// EIP can be bound/unbound during pod's life, so we must ensure no annotation is set for empty properties.
	eipInfos := map[string]string{
		BCIBoundEIPIDAnnotationKey:        bpod.EIPID,
		BCIBoundEIPAddressAnnotationKey:   bpod.PublicIP,
		BCIBoundEIPBandwidthAnnotationKey: "", // zero value of bandwidth is "0" but expect empty string, so set afterwards
		BCIBoundEIPRouteTypeAnnotationKey: bpod.EIPRouteType,
		BCIBoundEIPPayMethodAnnotationKey: bpod.EIPPayMethod,
	}
	if bpod.BandwidthInMbps > 0 {
		eipInfos[BCIBoundEIPBandwidthAnnotationKey] = strconv.FormatInt(bpod.BandwidthInMbps, 10)
	}
	for k, v := range eipInfos {
		if v == "" {
			delete(old, k)
		} else {
			old[k] = v
		}
	}

	return old
}

func ensureTolerations(tolerations []v1.Toleration) []v1.Toleration {
	var tolerateNotReady, tolerateUnreachable bool
	for _, toleration := range tolerations {
		if cmp.Equal(toleration, TolerationForNodeNotReady) {
			tolerateNotReady = true
		}
		if cmp.Equal(toleration, TolerationForNodeUnreachable) {
			tolerateUnreachable = true
		}
		if tolerateNotReady && tolerateUnreachable {
			break
		}
	}
	if !tolerateNotReady {
		tolerations = append(tolerations, TolerationForNodeNotReady)
	}
	if !tolerateUnreachable {
		tolerations = append(tolerations, TolerationForNodeUnreachable)
	}
	return tolerations
}
