package stsclient

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"

	"github.com/virtual-kubelet/virtual-kubelet/log"
	"github.com/virtual-kubelet/virtual-kubelet/trace"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

type cmdClient struct {
	scriptPath string
}

func NewCmdClient(scriptPath string) (Client, error) {
	if _, err := os.Stat(scriptPath); err != nil {
		return nil, fmt.Errorf("script %v not found: %w", scriptPath, err)
	}
	return &cmdClient{scriptPath}, nil
}

func (c *cmdClient) GetSessionToken(ctx context.Context, userUUID string) (*bce.SessionTokenResponse, error) {
	ctx, span := trace.StartSpan(ctx, "cmdClient.GetSessionToken")
	defer span.End()

	ctx = span.WithField(ctx, "userUUID", userUUID)

	cmd := exec.CommandContext(ctx, c.scriptPath, userUUID)
	log.G(ctx).Infof(fmt.Sprintf(`start cmd %v`, cmd))

	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf(`fail to exec cmd %v: output is %v, err is %w`, cmd, string(output), err)
	}
	log.G(ctx).Infof(fmt.Sprintf(`successfully exec cmd %v: %v`, cmd, string(output)))

	resp := new(bce.SessionTokenResponse)
	err = json.Unmarshal(output, resp)
	if err != nil {
		return nil, fmt.Errorf("fail to parse output '%s': %w", string(output), err)
	}

	return resp, err
}
