// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/stsclient (interfaces: Client)

// Package stsclient is a generated GoMock package.
package stsclient

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// GetSessionToken mocks base method.
func (m *MockClient) GetSessionToken(arg0 context.Context, arg1 string) (*bce.SessionTokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSessionToken", arg0, arg1)
	ret0, _ := ret[0].(*bce.SessionTokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSessionToken indicates an expected call of GetSessionToken.
func (mr *MockClientMockRecorder) GetSessionToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSessionToken", reflect.TypeOf((*MockClient)(nil).GetSessionToken), arg0, arg1)
}
