package stsclient

import (
	"context"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/util"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

const (
	cmdClientEntrypoint string = "/entrypoint.sh"
)

//go:generate mockgen -destination ./mock.go -package stsclient -self_package icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/stsclient icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/stsclient Client
type Client interface {
	GetSessionToken(ctx context.Context, userUUID string) (*bce.SessionTokenResponse, error)
}

func NewClient(mode util.MultiTenantMode) (Client, error) {
	switch mode {
	case util.MultiTenantModeBSC:
		return NewCmdClient(cmdClientEntrypoint)
	}
	return NewUnimplementedClient()
}
