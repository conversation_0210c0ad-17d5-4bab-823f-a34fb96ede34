package baidubci

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"testing"
	"time"

	"code.cloudfoundry.org/clock/fakeclock"
	"github.com/golang/mock/gomock"
	"gotest.tools/assert"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/record"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/nodecli/manager"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
	bciv2 "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci/v2"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bcm"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bcm/mock"
)

func newBCMEvent(region, eventID, podID string, level bcm.EventLevel, typ bcm.EventType, ts time.Time,
	content string) *bcm.Event {
	return &bcm.Event{
		Region:       "bj",
		EventID:      eventID,
		ResourceID:   podID,
		ResourceType: bcm.ResourceTypeInstance,
		EventLevel:   level,
		EventType:    typ,
		Timestamp:    ts.Format("2006-01-02T15:04:05Z"),
		ServiceName:  "BCE_BCI",
		Content:      content,
	}
}

func newBCMEventContent(t *testing.T, reason, message string) string {
	content := map[string]string{
		"reason":  reason,
		"message": message,
	}
	v, err := json.Marshal(content)
	if err == nil {
		return string(v)
	}
	t.Fatal(err)
	return ""
}

func TestEventSinkerRunWithPodCache(t *testing.T) {
	clockStart, err := time.Parse(time.RFC3339, "2022-10-13T07:00:00Z")
	if err != nil {
		t.Fatal(err)
	}
	clock := fakeclock.NewFakeClock(clockStart)
	er := record.NewFakeRecorder(64)

	provider := &BCIProvider{
		region:         "bj",
		enablePodCache: true,
		nodeName:       "bci-virtual-kubelet-0",
	}
	testPods := []string{
		"p-gjblcq1l",
		"p-bd1b4iwk",
		"p-anrbxjud",
	}
	podTypeMeta := metav1.TypeMeta{
		Kind:       "Pod",
		APIVersion: "v1",
	}
	testV1Pods := []*corev1.Pod{
		{
			TypeMeta: podTypeMeta,
			ObjectMeta: metav1.ObjectMeta{
				Name:      "pod-0",
				Namespace: "default",
				UID:       types.UID("cce92e61-9f3b-49f7-a6a8-cfa2246c11d0"),
			},
		},
		{
			TypeMeta: podTypeMeta,
			ObjectMeta: metav1.ObjectMeta{
				Name:      "pod-1",
				Namespace: "default",
				UID:       types.UID("cce92e61-9f3b-49f7-a6a8-cfa2246c11d1"),
			},
		},
		{
			TypeMeta: podTypeMeta,
			ObjectMeta: metav1.ObjectMeta{
				Name:      "pod-2",
				Namespace: "default",
				UID:       types.UID("cce92e61-9f3b-49f7-a6a8-cfa2246c11d2"),
			},
		},
	}

	accountID := "00dc1b52d8354d9193536e4dd2c41ae6"

	ctrl := gomock.NewController(t)
	rm := manager.NewMockResourceManager(ctrl)
	rm.EXPECT().GetRawClient().Return(nil)
	provider.resourceManager = rm
	es, err := NewEventSinker(context.TODO(), accountID, nil, newDefaultEventSinkerConfig(), er, provider)
	if err != nil {
		t.Fatal(err)
	}
	es.windowOffset = 0
	ctrl.Finish()
	es.clock = clock
	es.listPageSize = 2
	es.notifyCollectDone = make(chan struct{}, 1)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Start to Run, and all test cases below are run serially.
	go es.Run(ctx)
	<-time.After(time.Millisecond)

	type fields struct {
		ctrl      *gomock.Controller
		bcmClient bcm.Interface
		podCache  PodCache
	}
	type args struct {
		after time.Duration
	}
	tests := []struct {
		name   string
		repeat int64
		fields func(es *eventSinker, repeatIndex int64) fields
		args   func(es *eventSinker) args
		assert func(es *eventSinker)
	}{
		// test cases.
		{
			name: "no collection before the first execution of collectMyEvent",
			fields: func(*eventSinker, int64) fields {
				return fields{
					ctrl: gomock.NewController(t),
				}
			},
			args: func(*eventSinker) args {
				return args{
					after: time.Second,
				}
			},
			assert: func(es *eventSinker) {
				assert.Equal(t, len(er.Events), 0)
				select {
				case <-es.notifyCollectDone:
					t.Fatal("collection not expected")
				default:
				}
			},
		},
		{
			name: "1st collection succeeds",
			args: func(es *eventSinker) args {
				return args{
					after: es.collectInterval - time.Second,
				}
			},
			fields: func(es *eventSinker, repeatIndex int64) fields {
				start := clockStart
				ctrl := gomock.NewController(t)
				bcmClient := mock.NewMockInterface(ctrl)
				podCache := NewMockPodCache(ctrl)
				bcmClient.EXPECT().ListEvents(gomock.Any(), &bcm.ListOption{
					PageNo:    1,
					PageSize:  es.listPageSize,
					StartTime: start,
					EndTime:   start.Add(es.collectInterval),
					Scope:     "BCE_BCI",
					Region:    "bj",
					AccountID: accountID,
				}, nil).Return(&bcm.ListEventsResponse{
					Content: []*bcm.Event{
						newBCMEvent("bj", "1.event-0", testPods[0], bcm.EventLevelNotice,
							bcm.EventType("Pulling"), start.Add(time.Second), newBCMEventContent(t, "Pulling", "Pulling")),
						newBCMEvent("bj", "1.event-1", testPods[0], bcm.EventLevelNotice,
							bcm.EventType("Pulled"), start.Add(time.Second), newBCMEventContent(t, "Pulled", "Pulled")),
					},
					PageNo:        1,
					PageSize:      es.listPageSize,
					TotalPages:    1,
					TotalElements: 2,
					Last:          true,
				}, nil)
				podCache.EXPECT().GetPodByPodID(gomock.Any(), testPods[0]).Return(testV1Pods[0], nil).Times(2)
				return fields{
					ctrl:      ctrl,
					bcmClient: bcmClient,
					podCache:  podCache,
				}
			},
			assert: func(es *eventSinker) {
				<-es.notifyCollectDone
				assert.Equal(t, len(er.Events), 2)
				for i := 0; i < 2; i++ {
					t.Logf("1.got event: %s", <-er.Events)
				}
			},
		},
		{
			name: "2nd collection fails in listing last page",
			args: func(es *eventSinker) args {
				return args{
					after: es.collectInterval,
				}
			},
			fields: func(es *eventSinker, repeatIndex int64) fields {
				start := clockStart.Add(es.collectInterval)
				ctrl := gomock.NewController(t)
				bcmClient := mock.NewMockInterface(ctrl)
				podCache := NewMockPodCache(ctrl)
				bcmClient.EXPECT().ListEvents(gomock.Any(), &bcm.ListOption{
					PageNo:    1,
					PageSize:  es.listPageSize,
					StartTime: start,
					EndTime:   start.Add(es.collectInterval),
					Scope:     "BCE_BCI",
					Region:    "bj",
					AccountID: accountID,
				}, nil).Return(&bcm.ListEventsResponse{
					Content: []*bcm.Event{
						newBCMEvent("bj", "2.event-0", testPods[0], bcm.EventLevelNotice,
							bcm.EventType("Pulling"), start.Add(time.Second), newBCMEventContent(t, "Pulling", "Pulling")),
						newBCMEvent("bj", "2.event-1", testPods[0], bcm.EventLevelNotice,
							bcm.EventType("Pulled"), start.Add(time.Second), newBCMEventContent(t, "Pulled", "Pulled")),
					},
					PageNo:        1,
					PageSize:      es.listPageSize,
					TotalPages:    1,
					TotalElements: 3,
					Last:          false,
				}, nil)
				bcmClient.EXPECT().ListEvents(gomock.Any(), &bcm.ListOption{
					PageNo:    2,
					PageSize:  es.listPageSize,
					StartTime: start,
					EndTime:   start.Add(es.collectInterval),
					Scope:     "BCE_BCI",
					Region:    "bj",
					AccountID: accountID,
				}, nil).Return(nil, errors.New("2.error occurs in listing page 2"))
				podCache.EXPECT().GetPodByPodID(gomock.Any(), testPods[0]).Return(testV1Pods[0], nil).Times(2)

				return fields{
					ctrl:      ctrl,
					bcmClient: bcmClient,
					podCache:  podCache,
				}
			},
			assert: func(es *eventSinker) {
				<-es.notifyCollectDone
				assert.Equal(t, len(er.Events), 2)
				for i := 0; i < 2; i++ {
					t.Logf("2.got event: %s", <-er.Events)
				}
			},
		},
		{
			name:   "3rd-12th collections fail to list any page",
			repeat: 10,
			args: func(es *eventSinker) args {
				return args{
					after: time.Second,
				}
			},
			fields: func(es *eventSinker, repeatIndex int64) fields {
				// start is same with second listing.
				start := clockStart.Add(es.collectInterval)
				ctrl := gomock.NewController(t)
				bcmClient := mock.NewMockInterface(ctrl)
				podCache := NewMockPodCache(ctrl)

				bcmClient.EXPECT().ListEvents(gomock.Any(), &bcm.ListOption{
					PageNo:    1,
					PageSize:  es.listPageSize,
					StartTime: start,
					EndTime: /* now */ start.Add(es.collectInterval + time.Second +
						time.Duration(repeatIndex)*time.Second),
					Scope:     "BCE_BCI",
					Region:    "bj",
					AccountID: accountID,
				}, nil).Return(nil, fmt.Errorf("%d.error occurs in listing page 1", 3+repeatIndex))

				return fields{
					ctrl:      ctrl,
					bcmClient: bcmClient,
					podCache:  podCache,
				}
			},
			assert: func(es *eventSinker) {
				<-es.notifyCollectDone
				assert.Equal(t, len(er.Events), 0)
			},
		},
		{
			name:   "13-15th collections fail to list any page and exceed max list window size",
			repeat: 3,
			args: func(es *eventSinker) args {
				return args{
					after: time.Second,
				}
			},
			fields: func(es *eventSinker, repeatIndex int64) fields {
				// start is same with second listing.
				start := clockStart.Add(es.collectInterval)
				ctrl := gomock.NewController(t)
				bcmClient := mock.NewMockInterface(ctrl)
				podCache := NewMockPodCache(ctrl)

				bcmClient.EXPECT().ListEvents(gomock.Any(), &bcm.ListOption{
					PageNo:   1,
					PageSize: es.listPageSize,
					StartTime: /* maxWindowSize is exceeded so start also increments */ start.
						Add(time.Second + time.Duration(repeatIndex)*time.Second),
					EndTime: /* now */ start.Add(es.collectInterval +
						11*time.Second + time.Duration(repeatIndex)*time.Second),
					Scope:     "BCE_BCI",
					Region:    "bj",
					AccountID: accountID,
				}, nil).Return(nil, fmt.Errorf("%d.error occurs in listing page 1", 13+repeatIndex))

				return fields{
					ctrl:      ctrl,
					bcmClient: bcmClient,
					podCache:  podCache,
				}
			},
			assert: func(es *eventSinker) {
				<-es.notifyCollectDone
				assert.Equal(t, len(er.Events), 0)
			},
		},
		{
			name: "16th collection succeeds",
			args: func(es *eventSinker) args {
				return args{
					after: time.Second,
				}
			},
			fields: func(es *eventSinker, repeatIndex int64) fields {
				start := clockStart.Add(es.collectInterval + 4*time.Second)
				ctrl := gomock.NewController(t)
				bcmClient := mock.NewMockInterface(ctrl)
				podCache := NewMockPodCache(ctrl)

				bcmClient.EXPECT().ListEvents(gomock.Any(), &bcm.ListOption{
					PageNo:    1,
					PageSize:  es.listPageSize,
					StartTime: start,
					EndTime:   start.Add(3 * es.collectInterval),
					Scope:     "BCE_BCI",
					Region:    "bj",
					AccountID: accountID,
				}, nil).Return(&bcm.ListEventsResponse{
					Content: []*bcm.Event{
						newBCMEvent("bj", "16.event-0", testPods[0], bcm.EventLevelNotice,
							bcm.EventType("Pulled"), start.Add(5*time.Second), newBCMEventContent(t, "Pulled", "Pulled")),
						newBCMEvent("bj", "16.event-1", testPods[0], bcm.EventLevelNotice,
							bcm.EventType("Pulling"), start.Add(4*time.Second), newBCMEventContent(t, "Pulling", "Pulling")),
						newBCMEvent("bj", "16.event-2", testPods[1], bcm.EventLevelNotice,
							bcm.EventType("Pulled"), start.Add(3*time.Second), newBCMEventContent(t, "Pulled", "Pulled")),
					},
					PageNo:        1,
					PageSize:      es.listPageSize,
					TotalPages:    2,
					TotalElements: 5,
					Last:          false,
				}, nil)
				bcmClient.EXPECT().ListEvents(gomock.Any(), &bcm.ListOption{
					PageNo:    2,
					PageSize:  es.listPageSize,
					StartTime: start,
					EndTime:   start.Add(3 * es.collectInterval),
					Scope:     "BCE_BCI",
					Region:    "bj",
					AccountID: accountID,
				}, nil).Return(&bcm.ListEventsResponse{
					Content: []*bcm.Event{
						newBCMEvent("bj", "16.event-3", testPods[1], bcm.EventLevelNotice,
							bcm.EventType("Pulling"), start.Add(2*time.Second), newBCMEventContent(t, "Pulling", "Pulling")),
						newBCMEvent("bj", "16.event-4", testPods[2], bcm.EventLevelNotice,
							bcm.EventType("Pulled"), start.Add(time.Second), newBCMEventContent(t, "Pulled", "Pulled")),
					},
					PageNo:        2,
					PageSize:      es.listPageSize,
					TotalPages:    2,
					TotalElements: 5,
					Last:          true,
				}, nil)

				podCache.EXPECT().GetPodByPodID(gomock.Any(), testPods[0]).Return(testV1Pods[0], nil).Times(2)
				podCache.EXPECT().GetPodByPodID(gomock.Any(), testPods[1]).Return(testV1Pods[1], nil).Times(2)
				podCache.EXPECT().GetPodByPodID(gomock.Any(), testPods[2]).Return(testV1Pods[0], nil)

				return fields{
					ctrl:      ctrl,
					bcmClient: bcmClient,
					podCache:  podCache,
				}
			},
			assert: func(es *eventSinker) {
				<-es.notifyCollectDone
				assert.Equal(t, len(er.Events), 5)
				for i := 0; i < 5; i++ {
					t.Logf("16.got event: %s", <-er.Events)
				}
			},
		},
		{
			name: "17th collection succeeds with empty event list",
			args: func(es *eventSinker) args {
				return args{
					after: 5 * time.Second,
				}
			},
			fields: func(es *eventSinker, repeatIndex int64) fields {
				start := clockStart.Add(es.collectInterval + 19*time.Second)
				ctrl := gomock.NewController(t)
				bcmClient := mock.NewMockInterface(ctrl)
				podCache := NewMockPodCache(ctrl)

				bcmClient.EXPECT().ListEvents(gomock.Any(), &bcm.ListOption{
					PageNo:    1,
					PageSize:  es.listPageSize,
					StartTime: start,
					EndTime:   start.Add(es.collectInterval),
					Scope:     "BCE_BCI",
					Region:    "bj",
					AccountID: accountID,
				}, nil).Return(&bcm.ListEventsResponse{
					Content:       []*bcm.Event{},
					PageNo:        1,
					PageSize:      es.listPageSize,
					TotalPages:    1,
					TotalElements: 0,
					Last:          true,
				}, nil)

				return fields{
					ctrl:      ctrl,
					bcmClient: bcmClient,
					podCache:  podCache,
				}
			},
			assert: func(es *eventSinker) {
				<-es.notifyCollectDone
				assert.Equal(t, len(er.Events), 0)
			},
		},
		{
			name: "18th collection fails in listing last page",
			args: func(es *eventSinker) args {
				return args{
					after: 5 * time.Second,
				}
			},
			fields: func(es *eventSinker, repeatIndex int64) fields {
				start := clockStart.Add(es.collectInterval + 24*time.Second)
				ctrl := gomock.NewController(t)
				bcmClient := mock.NewMockInterface(ctrl)
				podCache := NewMockPodCache(ctrl)

				bcmClient.EXPECT().ListEvents(gomock.Any(), &bcm.ListOption{
					PageNo:    1,
					PageSize:  es.listPageSize,
					StartTime: start,
					EndTime:   start.Add(es.collectInterval),
					Scope:     "BCE_BCI",
					Region:    "bj",
					AccountID: accountID,
				}, nil).Return(&bcm.ListEventsResponse{
					Content: []*bcm.Event{
						newBCMEvent("bj", "18.event-0", testPods[0], bcm.EventLevelNotice,
							bcm.EventType("Pulled"), start.Add(4*time.Second), newBCMEventContent(t, "Pulled", "Pulled")),
						newBCMEvent("bj", "18.event-1", testPods[0], bcm.EventLevelNotice,
							bcm.EventType("Pulling"), start.Add(3*time.Second), newBCMEventContent(t, "Pulling", "Pulling")),
						newBCMEvent("bj", "18.event-2", testPods[1], bcm.EventLevelNotice,
							bcm.EventType("Pulled"), start.Add(2*time.Second), newBCMEventContent(t, "Pulled", "Pulled")),
					},
					PageNo:        1,
					PageSize:      es.listPageSize,
					TotalPages:    2,
					TotalElements: 4,
					Last:          false,
				}, nil)
				bcmClient.EXPECT().ListEvents(gomock.Any(), &bcm.ListOption{
					PageNo:    2,
					PageSize:  es.listPageSize,
					StartTime: start,
					EndTime:   start.Add(es.collectInterval),
					Scope:     "BCE_BCI",
					Region:    "bj",
					AccountID: accountID,
				}, nil).Return(nil, errors.New("18.error occurs in listing page 1"))
				podCache.EXPECT().GetPodByPodID(gomock.Any(), testPods[0]).Return(testV1Pods[0], nil).Times(2)
				podCache.EXPECT().GetPodByPodID(gomock.Any(), testPods[1]).Return(testV1Pods[1], nil)

				return fields{
					ctrl:      ctrl,
					bcmClient: bcmClient,
					podCache:  podCache,
				}
			},
			assert: func(es *eventSinker) {
				<-es.notifyCollectDone
				assert.Equal(t, len(er.Events), 3)
				for i := 0; i < 3; i++ {
					t.Logf("18.got event: %s", <-er.Events)
				}
			},
		},
		{
			name: "19th collection retrives failed events in 18th and new events",
			args: func(es *eventSinker) args {
				return args{
					after: time.Second,
				}
			},
			fields: func(es *eventSinker, repeatIndex int64) fields {
				start := clockStart.Add(es.collectInterval + 24*time.Second)
				ctrl := gomock.NewController(t)
				bcmClient := mock.NewMockInterface(ctrl)
				podCache := NewMockPodCache(ctrl)

				bcmClient.EXPECT().ListEvents(gomock.Any(), &bcm.ListOption{
					PageNo:    1,
					PageSize:  es.listPageSize,
					StartTime: start,
					EndTime:   start.Add(es.collectInterval + time.Second),
					Scope:     "BCE_BCI",
					Region:    "bj",
					AccountID: accountID,
				}, nil).Return(&bcm.ListEventsResponse{
					Content: []*bcm.Event{
						newBCMEvent("bj", "19.event-0", testPods[2], bcm.EventLevelNotice,
							bcm.EventType("Pulled"), start.Add(5*time.Second), newBCMEventContent(t, "Pulled", "Pulled")),
						newBCMEvent("bj", "19.event-1", testPods[2], bcm.EventLevelNotice,
							bcm.EventType("Pulling"), start.Add(5*time.Second), newBCMEventContent(t, "Pulling", "Pulling")),
						// Items below are events which should be sent within 18th collection.
						newBCMEvent("bj", "18.event-0", testPods[0], bcm.EventLevelNotice,
							bcm.EventType("Pulled"), start.Add(4*time.Second), newBCMEventContent(t, "Pulled", "Pulled")),
					},
					PageNo:        1,
					PageSize:      es.listPageSize,
					TotalPages:    2,
					TotalElements: 6,
					Last:          false,
				}, nil)
				bcmClient.EXPECT().ListEvents(gomock.Any(), &bcm.ListOption{
					PageNo:    2,
					PageSize:  es.listPageSize,
					StartTime: start,
					EndTime:   start.Add(es.collectInterval + time.Second),
					Scope:     "BCE_BCI",
					Region:    "bj",
					AccountID: accountID,
				}, nil).Return(&bcm.ListEventsResponse{
					Content: []*bcm.Event{
						newBCMEvent("bj", "18.event-1", testPods[0], bcm.EventLevelNotice,
							bcm.EventType("Pulling"), start.Add(3*time.Second), newBCMEventContent(t, "Pulling", "Pulling")),
						newBCMEvent("bj", "18.event-2", testPods[1], bcm.EventLevelNotice,
							bcm.EventType("Pulled"), start.Add(2*time.Second), newBCMEventContent(t, "Pulled", "Pulled")),
						newBCMEvent("bj", "18.event-3", testPods[1], bcm.EventLevelNotice,
							bcm.EventType("Pulling"), start.Add(1*time.Second), newBCMEventContent(t, "Pulling", "Pulling")),
					},
					PageNo:        2,
					PageSize:      es.listPageSize,
					TotalPages:    2,
					TotalElements: 6,
					Last:          true,
				}, nil)

				// 18.event-0, 18.event-1 and 18.event-2 should be ignored as they have been sent already.
				podCache.EXPECT().GetPodByPodID(gomock.Any(), testPods[2]).Return(testV1Pods[2], nil).Times(2)
				podCache.EXPECT().GetPodByPodID(gomock.Any(), testPods[1]).Return(testV1Pods[1], nil)

				return fields{
					ctrl:      ctrl,
					bcmClient: bcmClient,
					podCache:  podCache,
				}
			},
			assert: func(es *eventSinker) {
				<-es.notifyCollectDone
				// only failed events from 18th and new events from 19th are sent
				assert.Equal(t, len(er.Events), 3)
				for i := 0; i < 3; i++ {
					t.Log("19.got event:", <-er.Events)
				}
			},
		},
	}

	for _, tt := range tests {
		var repeat int64 = 1
		if tt.repeat > 1 {
			repeat = tt.repeat
		}
		for i := int64(0); i < repeat; i++ {
			t.Run(tt.name, func(t *testing.T) {
				fields := tt.fields(es, i)
				if fields.ctrl != nil {
					defer fields.ctrl.Finish()
				}

				es.bcmClient = fields.bcmClient
				es.provider.podCache = fields.podCache

				clock.Increment(tt.args(es).after)
				<-time.After(10 * time.Millisecond) // Wait one collection to finish.
				t.Logf("assert at %s", clock.Now().Format(time.RFC3339))
				tt.assert(es)
			})
		}

	}
}

func newTestPodDetail(podID, ns, name, uid, nodeName, clusterID string, createdAt int64,
	status bci.PodStatus) *bci.DescribePodResponse {
	return &bci.DescribePodResponse{
		Pod: &bci.Pod{
			PodID: podID,
			CCEID: clusterID,
			Labels: []bci.PodLabel{
				{
					LabelKey:   PodNameLabelKey,
					LabelValue: name,
				},
				{
					LabelKey:   NamespaceLabelKey,
					LabelValue: ns,
				},
				{
					LabelKey:   NodeNameLabelKey,
					LabelValue: nodeName,
				},
				{
					LabelKey:   UIDLabelKey,
					LabelValue: uid,
				},
				{
					LabelKey:   CreationTimestampLabelKey,
					LabelValue: strconv.FormatInt(createdAt, 10),
				},
				{
					LabelKey:   ClusterIDLabelKey,
					LabelValue: clusterID,
				},
			},
			CreatedTime: time.Unix(createdAt, 0),
			UpdatedTime: time.Unix(createdAt, 0),
			V2:          true,
			Status:      status,
		},
		Containers: []bci.Container{
			{
				Name: "container-0",
				ContainerImageInfo: &bci.ContainerImageInfo{
					ImageAddress: "nginx",
					ImageName:    "nginx",
					ImageVersion: "latest",
				},
				CPUInCore:  1.0,
				MemoryInGB: 2.0,
				Status:     &bci.ContainerStatus{},
			},
		},
	}

}

func newTestV1Pod(ns, name, uid, nodeName, clusterID string, createdAt int64, status bci.PodStatus) *corev1.Pod {
	return &corev1.Pod{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Pod",
			APIVersion: "v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:              name,
			Namespace:         ns,
			UID:               types.UID(uid),
			CreationTimestamp: metav1.NewTime(time.Unix(createdAt, 0)),
			// ClusterName:       clusterID,
		},
		Spec: corev1.PodSpec{
			NodeName: nodeName,
			Volumes:  []corev1.Volume{},
			Containers: []corev1.Container{
				{
					Name:  "container-0",
					Image: "nginx:latest",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							corev1.ResourceCPU:    resource.MustParse(fmt.Sprintf("%.2f", 1.0)),
							corev1.ResourceMemory: resource.MustParse(fmt.Sprintf("%.1fGi", 2.0)),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase:      bciPodStatusToPodPhase(status, true, false),
			Conditions: []corev1.PodCondition{},
			StartTime: func() *metav1.Time {
				v := metav1.NewTime(time.Unix(createdAt, 0))
				return &v
			}(),
			ContainerStatuses: []corev1.ContainerStatus{
				{
					Name: "container-0",
					State: corev1.ContainerState{
						Waiting: &corev1.ContainerStateWaiting{
							Reason: "Creating",
						},
					},
					Image: "nginx:latest",
				},
			},
		},
	}
}

func TestFindInvolvedPodWithoutPodCache(t *testing.T) {
	testPods := []string{
		"p-gjblcq1l",
		"p-bd1b4iwk",
		"p-anrbxjud",
	}
	provider := &BCIProvider{
		region:    "bj",
		clusterID: "cce-xxxx1111",
		nodeName:  "bci-vk",
	}
	accountID := "00dc1b52d8354d9193536e4dd2c41ae6"
	er := record.NewFakeRecorder(64)
	ctrl := gomock.NewController(t)
	rm := manager.NewMockResourceManager(ctrl)
	rm.EXPECT().GetRawClient().Return(nil)
	provider.resourceManager = rm
	es, err := NewEventSinker(context.TODO(), accountID, nil, newDefaultEventSinkerConfig(), er, provider)
	if err != nil {
		t.Fatal(err)
	}
	ctrl.Finish()

	type fields struct {
		ctrl        *gomock.Controller
		bciV2Client bciv2.Client
	}
	type args struct {
		ctx   context.Context
		podID string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *corev1.Pod
	}{
		// TODO: Add test cases.
		{
			name: "new unknown pod belongs to us",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bciV2Client := bciv2.NewMockClient(ctrl)
				bciV2Client.EXPECT().DescribePodWithDeleted(gomock.Any(), testPods[0], nil).Return(newTestPodDetail(testPods[0],
					"default", "pod-0", "xxxx-xx-xx-xxxx", "bci-vk", "cce-xxxx1111", **********, bci.PodStatusPending), nil)

				return fields{
					ctrl:        ctrl,
					bciV2Client: bciV2Client,
				}
			}(),
			args: args{
				ctx:   context.TODO(),
				podID: testPods[0],
			},
			want: func() *corev1.Pod {
				pod := newTestV1Pod("default", "pod-0", "xxxx-xx-xx-xxxx", "bci-vk", "cce-xxxx1111", **********, bci.PodStatusPending)
				pod.Status.Conditions = bciPodStatusToConditions(newTestPodDetail(testPods[0], "default", "pod-0", "xxxx-xx-xx-xxxx", "bci-vk",
					"cce-xxxx1111", **********, bci.PodStatusPending), corev1.PodPending)
				return pod
			}(),
		},
		{
			name: "known pod belongs to us",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bciV2Client := bciv2.NewMockClient(ctrl)

				return fields{
					ctrl:        ctrl,
					bciV2Client: bciV2Client,
				}
			}(),
			args: args{
				ctx:   context.TODO(),
				podID: testPods[0],
			},
			want: func() *corev1.Pod {
				pod := newTestV1Pod("default", "pod-0", "xxxx-xx-xx-xxxx", "bci-vk", "cce-xxxx1111", **********, bci.PodStatusPending)
				pod.Status.Conditions = bciPodStatusToConditions(newTestPodDetail(testPods[0], "default", "pod-0", "xxxx-xx-xx-xxxx", "bci-vk",
					"cce-xxxx1111", **********, bci.PodStatusPending), corev1.PodPending)
				return pod
			}(),
		},
		{
			name: "unknown pod not belongs to our cluster",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bciV2Client := bciv2.NewMockClient(ctrl)
				bciV2Client.EXPECT().DescribePodWithDeleted(gomock.Any(), "p-xxxxxxxx", nil).Return(newTestPodDetail("p-xxxxxxxx", "default", "pod-0",
					"xxxx-xx-xx-xxxx", "bci-vk", "cce-xxxxxxxx", **********, bci.PodStatusPending), nil)

				return fields{
					ctrl:        ctrl,
					bciV2Client: bciV2Client,
				}
			}(),
			args: args{
				ctx:   context.TODO(),
				podID: "p-xxxxxxxx",
			},
			want: nil,
		},
		{
			name: "known pod not belongs to our cluster",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bciV2Client := bciv2.NewMockClient(ctrl)

				return fields{
					ctrl:        ctrl,
					bciV2Client: bciV2Client,
				}
			}(),
			args: args{
				ctx:   context.TODO(),
				podID: "p-xxxxxxxx",
			},
			want: nil,
		},
		{
			name: "unknown pod not belongs to our node",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bciV2Client := bciv2.NewMockClient(ctrl)
				bciV2Client.EXPECT().DescribePodWithDeleted(gomock.Any(), "p-xxxxxxx2", nil).Return(newTestPodDetail("p-xxxxxxx2", "default", "pod-0",
					"xxxx-xx-xx-xxxx", "bci-vk-0", "cce-xxxx1111", **********, bci.PodStatusPending), nil)

				return fields{
					ctrl:        ctrl,
					bciV2Client: bciV2Client,
				}
			}(),
			args: args{
				ctx:   context.TODO(),
				podID: "p-xxxxxxx2",
			},
			want: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			es.provider.bciV2Client = tt.fields.bciV2Client
			got := es.findInvolvedPodWithoutPodCache(tt.args.ctx, tt.args.podID)
			assert.DeepEqual(t, got, tt.want)
		})
	}
}
