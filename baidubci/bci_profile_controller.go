package baidubci

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"
	"time"

	"github.com/virtual-kubelet/virtual-kubelet/log"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/types"
	corev1 "k8s.io/api/core/v1"
	kubeerror "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	kubeinformers "k8s.io/client-go/informers"
	coreinformers "k8s.io/client-go/informers/core/v1"
	corelisters "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/tools/cache"
	"k8s.io/klog"
)

func NewBCIProfileConfig() *types.BCIProfileConfig {
	return &types.BCIProfileConfig{
		EnableReserveFailedPod: types.DefaultEnableReserveFailedPod,
	}
}

func (p *BCIProvider) buildSubnetOptionsFromProfile(sInfos []types.SubnetInfo) (map[string]*SubnetOption, error) {
	options := make(map[string]*SubnetOption)
	if len(sInfos) == 0 {
		return nil, errors.New("subnet info is empty")
	}
	for _, info := range sInfos {
		subnetID := info.SubnetID
		if subnetID == "" {
			continue
		}
		if _, ok := options[subnetID]; ok {
			options[subnetID].Weight = options[subnetID].Weight + 1
			continue
		}
		options[subnetID] = &SubnetOption{
			Weight:      1,
			LogicalZone: info.LogicalZone,
		}
	}
	return options, nil
}

func (p *BCIProvider) buildSubnetInfoFromSubnetOptions() []types.SubnetInfo {
	sInfo := make([]types.SubnetInfo, 0)
	for subnetID, info := range p.subnetOptions {
		sInfo = append(sInfo, types.SubnetInfo{
			SubnetID:    subnetID,
			LogicalZone: info.LogicalZone,
		})

	}
	return sInfo
}

type BCIProfileController struct {
	provider          *BCIProvider
	configmapInformer coreinformers.ConfigMapInformer
	configmapLister   corelisters.ConfigMapLister
}

func NewBCIProfileController(ctx context.Context, provider *BCIProvider) (*BCIProfileController, error) {
	if provider == nil {
		return nil, errors.New("bci provider can not be nil")
	}
	systemInformerFactory := kubeinformers.NewSharedInformerFactoryWithOptions(
		provider.resourceManager.GetRawClient(),
		60*time.Second,
		kubeinformers.WithNamespace(types.SystemNamespace),
	)
	configmapInformer := systemInformerFactory.Core().V1().ConfigMaps()
	configmapLister := configmapInformer.Lister()
	go systemInformerFactory.Start(ctx.Done())

	cmc := &BCIProfileController{
		provider:          provider,
		configmapInformer: configmapInformer,
		configmapLister:   configmapLister,
	}
	return cmc, nil
}

func (bmc *BCIProfileController) setDefaultBCIProfileConfig(ctx context.Context) {
	// check if bci-profile configmap exist
	existedConfigMap, err := bmc.provider.resourceManager.GetRawClient().CoreV1().ConfigMaps(types.SystemNamespace).Get(ctx,
		types.BciProfileConfigMapName, metav1.GetOptions{})
	if err != nil {
		if kubeerror.IsNotFound(err) {
			bconfig := types.BCIProfileConfig{}
			bconfig.Region = bmc.provider.region
			bconfig.ClusterID = bmc.provider.clusterID
			bconfig.Subnets = bmc.provider.buildSubnetInfoFromSubnetOptions()
			bconfig.SecurityGroupID = bmc.provider.securityGroupID
			bconfig.EnableReserveFailedPod = types.DefaultEnableReserveFailedPod
			bconfig.EnableAutoInjectKubeProxy = types.DefaultEnableAutoInjectKubeProxy
			bconfig.EnableForceInjectDNSConfig = types.DefaultEnableForceInjectDNSConfig
			bconfig.DisableAutoMatchImageCache = types.DefaultDisableAutoMatchImageCache

			bconfig.DNSConfig = bmc.provider.nodeDNSConfig

			configData, err := json.MarshalIndent(bconfig, "", "  ")
			if err != nil {
				log.G(ctx).Errorf("BCIProfileController: setDefaultBCIProfileConfig marshal error, %v", err)
				return
			}

			configMap := &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Name:      types.BciProfileConfigMapName,
					Namespace: types.SystemNamespace,
				},
				Data: map[string]string{
					"config": string(configData),
				},
			}

			// create bci-profile configmap
			_, err = bmc.provider.resourceManager.GetRawClient().CoreV1().ConfigMaps(types.SystemNamespace).Create(ctx, configMap, metav1.CreateOptions{})
			if err != nil {
				log.G(ctx).Errorf("BCIProfileController: setDefaultBCIProfileConfig create error, %v", err)
				return
			}
			bmc.provider.setBCIProfileConfig(&bconfig)

		} else {
			log.G(ctx).Errorf("BCIProfileController: setDefaultBCIProfileConfig get error, %v", err)
			return
		}

	} else {
		// set existedConfigMap to memory
		bmc.handleBciProfileConfig(existedConfigMap)

	}

	log.G(ctx).Infof("BCIProfileController: setDefaultBCIProfileConfig success")
}

func (bmc *BCIProfileController) Run(ctx context.Context) error {
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	log.G(ctx).Infof("BCIProfileController: starting sync informer")
	// Wait for the caches to be synced *before* starting to do work.
	if ok := cache.WaitForCacheSync(ctx.Done(), bmc.configmapInformer.Informer().HasSynced); !ok {
		return errors.New("BCIProfileController configmap informer cache is not synced")
	}
	log.G(ctx).Infof("BCIProfileController: starting set default bci-profile configmap")
	bmc.setDefaultBCIProfileConfig(ctx)
	log.G(ctx).Infof("BCIProfileController: ennding set default bci-profile configmap")

	log.G(ctx).Infof("BCIProfileController: starting list watch bci-profile configmap changed")
	var configmapEventHandler cache.ResourceEventHandler = cache.ResourceEventHandlerFuncs{
		AddFunc:    bmc.addBciProfileConfig,
		UpdateFunc: bmc.updateBciProfileConfig,
		DeleteFunc: bmc.deleteBciProfileConfig,
	}
	bmc.configmapInformer.Informer().AddEventHandler(configmapEventHandler)
	defer utilruntime.HandleCrash()
	defer log.G(ctx).Infof("BCIProfileController: shutting BCIProfileController")

	<-ctx.Done()
	return nil
}

func (bmc *BCIProfileController) updateBciProfileConfig(oldObj, newObj interface{}) {
	oldConfigMap := oldObj.(*corev1.ConfigMap)
	newConfigMap := newObj.(*corev1.ConfigMap)
	if newConfigMap.Namespace == types.SystemNamespace && newConfigMap.Name == types.BciProfileConfigMapName {
		klog.Infof("BCIProfileController: configmap change, configmap name: %s, namespace: %s", newConfigMap.Name, newConfigMap.Namespace)
		if !isConfigMapChange(oldConfigMap, newConfigMap) {
			klog.Infof("BCIProfileController: configmap does not change, configmap name: %s, namespace: %s", newConfigMap.Name, newConfigMap.Namespace)
			return
		}
		// handle configmap change
		bmc.handleBciProfileConfig(newConfigMap)
	}
}

func (bmc *BCIProfileController) addBciProfileConfig(obj interface{}) {

	newConfigMap := obj.(*corev1.ConfigMap)
	if newConfigMap.Namespace == types.SystemNamespace && newConfigMap.Name == types.BciProfileConfigMapName {
		klog.Infof("BCIProfileController: configmap add, configmap name: %s, namespace: %s", newConfigMap.Name, newConfigMap.Namespace)
	}
}

func (bmc *BCIProfileController) deleteBciProfileConfig(obj interface{}) {
	newConfigMap := obj.(*corev1.ConfigMap)
	if newConfigMap.Namespace == types.SystemNamespace && newConfigMap.Name == types.BciProfileConfigMapName {
		klog.Infof("BCIProfileController: configmap delete, configmap name: %s, namespace: %s", newConfigMap.Name, newConfigMap.Namespace)
	}
}

func (bmc *BCIProfileController) handleBciProfileConfig(newConfigMap *corev1.ConfigMap) {
	if newConfigMap.Data == nil {
		return
	}
	// 完全以config字段为准
	if configData, ok := newConfigMap.Data["config"]; ok {
		bmc.updateBCIProfileConfig(configData)
	}
	// 兼容一些老的版本，兼容老的字段
	if enableReserveFailedPod, ok := newConfigMap.Data["enableReserveFailedPod"]; ok {
		if bmc.provider.bciProfileConfig == nil {
			bmc.provider.bciProfileConfig = NewBCIProfileConfig()
		}
		bmc.provider.bciProfileConfig.EnableReserveFailedPod, _ = strconv.ParseBool(enableReserveFailedPod)
	}
}

func (bmc *BCIProfileController) updateBCIProfileConfig(configData string) {
	var bconfig types.BCIProfileConfig
	err := json.Unmarshal([]byte(configData), &bconfig)
	if err != nil {
		klog.Errorf("BCIProfileController: handleBciProfileConfig unmarshal error, %v", err)
		return
	}
	bmc.setBCIProfileConfig(&bconfig)
}

func (bmc *BCIProfileController) setBCIProfileConfig(bconfig *types.BCIProfileConfig) {
	// set bciProfileConfig
	klog.Infof("BCIProfileController: bconfig begin update: %+v", *bconfig)
	bmc.provider.setBCIProfileConfig(bconfig)

	if bconfig.Subnets != nil {
		klog.Infof("BCIProfileController: old subnets: %+v ,new subnets: %v", bmc.provider.subnetOptions, bconfig.Subnets)
		subnetOptions, err := bmc.provider.buildSubnetOptionsFromProfile(bconfig.Subnets)
		if err != nil {
			klog.Errorf("BCIProfileController: buildSubnetOptionsFromProfile error, %v", err)
			return
		}
		bmc.provider.subnetOptions = subnetOptions

	}
	if bconfig.SecurityGroupID != "" {
		securityGroupID, err := parseSecurityGroup(bconfig.SecurityGroupID)
		if err != nil {
			klog.Errorf("BCIProfileController: securityGroupID invalid, %v", err)
			return
		}
		klog.Infof("BCIProfileController: old securityGroupID: %+v ,new securityGroupID: %+v", bmc.provider.securityGroupID, bconfig.SecurityGroupID)
		bmc.provider.securityGroupID = securityGroupID
	}
	if bconfig.DNSConfig != nil {
		if bmc.provider.nodeDNSConfig != nil {
			klog.Infof("BCIProfileController: old DNSConfig: %+v ,new DNSConfig: %+v", *(bmc.provider.nodeDNSConfig), *(bconfig.DNSConfig))
		} else {
			klog.Infof("BCIProfileController: old DNSConfig: nil ,new DNSConfig: %+v", *(bconfig.DNSConfig))
		}
		bmc.provider.nodeDNSConfig = bconfig.DNSConfig
	}
}
