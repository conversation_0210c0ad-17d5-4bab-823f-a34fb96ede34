# 百度 Go 编译环境使用指南 https://ku.baidu-int.com/d/SzGt0sD37hWmmp

HOMEDIR := $(shell pwd)
OUTDIR  := $(HOMEDIR)/output

# 设置编译时所需要的 go 环境
export GOENV = $(HOMEDIR)/go.env

GOPKGS  := $$(go list ./...| grep -vE "vendor")

GOOS ?= linux
GOARCH ?= amd64

# 获取构建信息
GitCommitLog := $(shell git log --pretty=oneline -n 1 | sed "s/'/\"/g")
BuildTime := $(shell date '+%Y.%m.%d.%H%M%S')
BuildUsername := $(shell whoami)

# LDFlags 设置
LDFlags = \
	-X 'icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/buildinfo.GitCommitLog=$(GitCommitLog)' \
	-X 'icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/buildinfo.BuildTime=$(BuildTime)' \
	-X 'icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/buildinfo.BuildUsername=$(BuildUsername)'

# make, make all
all: prepare compile package

prepare:
	git version     # 低于 2.17.1 可能不能正常工作
	go env          # 打印出 go 环境信息，可用于排查问题
	go mod download || go mod download -x  # 下载 依赖

#make compile
#compile: build
build:
	GOOS=${GOOS} GOARCH=${GOARCH} go build -o $(HOMEDIR)/virtual-kubelet baidubci/cmd/virtual-kubelet/main.go
	#GOOS=${GOOS} GOARCH=${GOARCH} go build -o ${OUTDIR}/virtual-kubelet/ cmd/virtual-kubelet/main.go

compile:
	mkdir -p ${OUTDIR}/bci-virtual-kubelet && cp baidubci/Dockerfile  ${OUTDIR}/bci-virtual-kubelet
	mkdir -p ${OUTDIR}/bci-virtual-kubelet && cp baidubci/Dockerfile.bsc  ${OUTDIR}/bci-virtual-kubelet
	mkdir -p ${OUTDIR}/bci-virtual-kubelet && cp baidubci/stsgenerator.jar  ${OUTDIR}/bci-virtual-kubelet
	mkdir -p ${OUTDIR}/bci-virtual-kubelet && cp -r baidubci/docker ${OUTDIR}/bci-virtual-kubelet
	mkdir -p ${OUTDIR}/bci-virtual-kubelet && cp -r baidubci/config ${OUTDIR}/bci-virtual-kubelet
	#CGO_ENABLED=0 GOOS=${GOOS} GOARCH=${GOARCH} go build -o ${OUTDIR}/virtual-kubelet cmd/virtual-kubelet/main.go
	CGO_ENABLED=0 GOOS=${GOOS} GOARCH=${GOARCH} go build -o $(HOMEDIR)/virtual-kubelet baidubci/cmd/virtual-kubelet/main.go
	mv virtual-kubelet $(OUTDIR)/bci-virtual-kubelet
	cp plugin_build_info ${OUTDIR}/
	cp -r config/charts/virtual-kubelet ${OUTDIR}/


# make clean
clean:
	go clean
	rm -rf $(OUTDIR)

# avoid filename conflict and speed up build
.PHONY: all prepare compile test package clean build
