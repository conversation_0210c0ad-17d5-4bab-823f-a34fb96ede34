// Copyright 2024 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2024/10/14 15:00:00, by z<PERSON><PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
DESCRIPTION
容器网络burst模式下IP借用功能验证工具 - 主程序入口
*/

package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/logic/kube"
)

var (
	kubeConfigPath string
)

func init() {
	flag.StringVar(&kubeConfigPath, "kubeconfig", "/Users/<USER>/cce/cce-network-v2-vpc.conf", "Kubernetes配置文件路径")
}

func main() {
	flag.Parse()

	ctx := context.Background()

	fmt.Println("===== 容器网络burst模式下IP借用功能验证工具 =====")

	// 1. 初始化客户端
	kubeClient, err := initKubeClient(kubeConfigPath)
	if err != nil {
		fmt.Printf("初始化Kubernetes客户端失败: %v\n", err)
		os.Exit(1)
	}

	// 2. 获取集群中的网络资源集合
	nrsList, err := kubeClient.ListNrs(ctx, &kube.ListOptions{})
	if err != nil {
		fmt.Printf("获取NetworkResourceSet列表失败: %v\n", err)
		os.Exit(1)
	}

	// 3. 筛选使用ENI模式且启用了Burst模式的节点
	burstEnabledNodes := make([]string, 0)
	for _, nrs := range nrsList.Items {
		if nrs.Spec.Eni.UseMode == "eni" && nrs.Spec.Eni.BurstableMehrfachENI > 0 {
			burstEnabledNodes = append(burstEnabledNodes, nrs.Name)
			fmt.Printf("节点 %s 使用ENI模式，BurstableMehrfachENI = %d\n",
				nrs.Name, nrs.Spec.Eni.BurstableMehrfachENI)
		}
	}

	if len(burstEnabledNodes) == 0 {
		fmt.Println("集群中没有找到启用Burst模式的ENI节点")
		os.Exit(1)
	}

	// 4. 获取所有ENI
	eniList, err := kubeClient.ListENI(ctx, &kube.ListOptions{})
	if err != nil {
		fmt.Printf("获取ENI列表失败: %v\n", err)
		os.Exit(1)
	}

	// 5. 检查每个节点上的ENI是否有借用IP
	hasFoundBorrowIP := false

	for _, nodeName := range burstEnabledNodes {
		fmt.Printf("检查节点 %s 上的ENI借用IP情况:\n", nodeName)

		nodeENIs := 0
		borrowedIPCount := 0

		for _, eni := range eniList.Items {
			if eni.Spec.NodeName == nodeName {
				nodeENIs++

				if eni.Spec.BorrowIPCount > 0 {
					hasFoundBorrowIP = true
					borrowedIPCount += eni.Spec.BorrowIPCount

					fmt.Printf("  - ENI: %s, 子网: %s, 借用IP数: %d, 当前IP集合数: %d\n",
						eni.Spec.ID, eni.Spec.SubnetID, eni.Spec.BorrowIPCount, len(eni.Spec.PrivateIPSet))

					// 检查节点的NRS中是否反映了借用状态
					nrs, err := kubeClient.GetNrs(ctx, nodeName, &kube.GetOptions{})
					if err != nil {
						fmt.Printf("    警告: 获取节点 %s 的NRS失败: %v\n", nodeName, err)
						continue
					}

					// 检查ENI状态
					if eniStatus, ok := nrs.Status.Enis[eni.Spec.ID]; ok {
						fmt.Printf("    * NRS中该ENI状态: CCEStatus=%s, SubnetId=%s\n",
							eniStatus.CceStatus, eniStatus.SubnetId)
					}
				}
			}
		}

		fmt.Printf("节点 %s 总计: ENI数量 = %d, 借用IP数量 = %d\n", nodeName, nodeENIs, borrowedIPCount)
	}

	// 6. 收集和显示子网信息
	subnetList, err := kubeClient.ListSubnet(ctx, &kube.ListOptions{})
	if err != nil {
		fmt.Printf("获取子网列表失败: %v\n", err)
	} else {
		fmt.Println("集群中的子网信息:")
		for _, subnet := range subnetList.Items {
			fmt.Printf("  - 子网: %s, 可用IP数: %d, 是否有更多IP: %t\n",
				subnet.Spec.ID, subnet.Status.AvailableIPNum, !subnet.Status.HasNoMoreIP)
		}
	}

	// 7. 结论
	if hasFoundBorrowIP {
		fmt.Println("验证成功: 集群中检测到ENI的IP借用，burst模式功能正常")
	} else {
		fmt.Println("未检测到ENI的IP借用，可能是以下原因:")
		fmt.Println("  1. 集群负载不足，未触发IP借用")
		fmt.Println("  2. 子网中有足够的IP，不需要借用")
		fmt.Println("  3. Burst模式配置可能存在问题")
		fmt.Println("建议创建更多Pod或检查网络配置")
	}
}

// 初始化Kubernetes客户端
func initKubeClient(kubeConfigPath string) (*kube.Client, error) {
	ctx := context.Background()

	// 确保文件存在
	if _, err := os.Stat(kubeConfigPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("kubeconfig文件不存在: %s", kubeConfigPath)
	}

	// 读取kubeconfig内容
	kubeConfigBytes, err := os.ReadFile(kubeConfigPath)
	if err != nil {
		return nil, fmt.Errorf("读取kubeconfig文件失败: %v", err)
	}

	fmt.Printf("使用kubeconfig: %s\n", kubeConfigPath)

	// 创建客户端
	kubeClient, err := kube.NewClientByKubeConfig(string(kubeConfigBytes), 30*time.Second, false)
	if err != nil {
		return nil, fmt.Errorf("创建Kubernetes客户端失败: %v", err)
	}

	// 测试客户端连接
	_, err = kubeClient.ClientSet.CoreV1().Nodes().List(ctx, metav1.ListOptions{Limit: 1})
	if err != nil {
		return nil, fmt.Errorf("测试Kubernetes客户端连接失败: %v", err)
	}

	fmt.Println("Kubernetes客户端连接成功")
	return kubeClient, nil
}
