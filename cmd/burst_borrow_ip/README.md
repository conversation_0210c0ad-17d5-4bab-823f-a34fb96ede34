# 容器网络Burst模式下IP借用功能测试工具

## 功能介绍

本工具用于测试和验证容器网络在burst模式下的IP借用功能。在容器网络的burst模式下，当开启BurstableMehrfachENI（值大于0）时，系统会通过borrow IP的方式确保网卡能够足量分配IP。

主要功能包括：
1. 检测集群中启用了burst模式的节点
2. 检查ENI的BorrowIPCount配置
3. 分析节点上ENI的IP借用情况
4. 验证子网IP借用状态

## 原理说明

在Kubernetes容器网络实现中，borrowed subnet支持定时同步的功能是为了避免因单次IP计算错误而导致错误借用未归还的问题。这个功能主要在burst模式下工作。

burst模式是容器网络IP分配的一种工作模式，当开启BurstableMehrfachENI（值大于0）时，系统会通过borrow IP的方式确保网卡能够足量分配IP。这种模式下的关键步骤包括：

- 配置启用：通过在NetResourceSet资源的spec.eni.burstableMehrfachENI字段设置大于0的值（默认为1）来启用burst模式。
- 借用IP流程：当创建新的ENI时，系统会计算需要借用的IP数量，并通过subnet.Borrow(eniID, ipCount)尝试借用IP。
- 借用状态管理：BorrowedSubnet结构维护每个子网的借用状态，包含已借用IP数和可借用IP数。
- 定时同步机制：通过resyncBSM函数实现全量同步，确保IP借用状态正确。

## 使用方法

```bash
# 使用默认kubeconfig路径
go run ./cmd/burst_borrow_ip/main.go

# 自定义kubeconfig路径
go run ./cmd/burst_borrow_ip/main.go -kubeconfig=/path/to/your/kubeconfig
```

## 输出说明

程序会输出以下信息：
1. 集群中支持burst模式的节点列表
2. 每个节点上的ENI借用IP情况
3. 子网的可用IP状态
4. 验证结论

## 注意事项

- 要验证IP借用功能，集群中需要至少有一个节点启用burst模式
- 如果未检测到IP借用，可能是因为集群负载不足，未触发IP借用机制
- 建议在大规模集群或IP资源紧张的环境中测试该功能 