package cmd

import (
	"context"
	"errors"
	"fmt"
	"os"

	"github.com/spf13/cobra"

	"icode.baidu.com/baidu/cce-test/e2e-test/conf"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/report"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

var reportCmd = &cobra.Command{
	Use:   "report",
	Short: "检测指定批次的任务状态并生成测试报告",
	Example: `
./cce-e2e-test report --batch-id 20240710_164949
`,
	Run: func(cmd *cobra.Command, args []string) {
		ctx := context.WithValue(context.TODO(), logger.RequestID, logger.GetUUID())
		var err error

		defer func() {
			// 统一打印Run的err信息
			if err != nil {
				logger.Errorf(ctx, err.Error())
				os.Exit(1)
			}
		}()

		err = prepareReportConfig(ctx)
		if err != nil {
			return
		}
		batchID = clientConfig.BatchID
		region = clientConfig.Region

		reporter, err := report.NewReporter(ctx, &clientConfig)
		if err != nil {
			err = fmt.Errorf("create reporter failed, err: %v", err)
			return
		}
		err = reporter.Build(ctx)
		return
	},
}

func init() {
	rootCmd.AddCommand(reportCmd)

	reportCmd.Flags().StringVar(&batchID, "batch-id", "", "任务批次ID，默认从配置文件中获取")
	reportCmd.Flags().StringVar(&region, "region", "", "集群地域，默认从配置文件中获取")
	reportCmd.Flags().StringVar(&configFile, "config", "config.yaml", "运行环境配置文件，配置文件读取目录为 conf/")
}

func prepareReportConfig(ctx context.Context) (err error) {
	err = conf.LoadConfig(ctx, configFile, &clientConfig)
	if err != nil {
		return
	}
	// 如果命令行指定了batch-id，则覆盖配置文件中的batch-id
	if batchID != "" {
		clientConfig.BatchID = batchID
	}
	// 如果命令行指定了region，则覆盖配置文件中的region
	if region != "" {
		clientConfig.Region = region
	}
	if clientConfig.ReportWaitTimeoutSeconds == 0 {
		clientConfig.ReportWaitTimeoutSeconds = conf.DefaultReportWaitTimeoutSeconds
	}
	// 如果batch-id仍然为空，从环境变量读取
	if clientConfig.BatchID == "" {
		envBatchID := conf.GetBatchID(ctx, false)
		if envBatchID == "" {
			err = errors.New("BatchID is empty")
			return
		}
		clientConfig.BatchID = envBatchID
	}
	if clientConfig.Region == "" {
		err = errors.New("Region is empty")
		return
	}
	if clientConfig.QAMySQLEndpoint == "" {
		err = errors.New("Endpoint.QAMySQLEndpoint is empty")
		return
	}
	if clientConfig.BosBucket == "" {
		err = errors.New("BosBucket is empty")
		return
	}
	return
}
