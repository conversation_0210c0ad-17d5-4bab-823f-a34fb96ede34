package cmd

import (
	"context"
	"sort"
	"strconv"

	"github.com/spf13/cobra"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

/*
cce-e2e-test show-case
*/
var showCaseCmd = &cobra.Command{
	Use:   "show-case",
	Short: "显示所有可用 Case",
	Example: `
./cce-e2e-test show-case
`,
	Run: func(cmd *cobra.Command, args []string) {
		ctx := context.Background()
		caseList := cases.CCECaseList(ctx)

		// 为了确保map的name是顺序的，这里把name提取出来排序
		caseNameList := make([]string, 0, len(caseList))
		for caseName := range caseList {
			caseNameList = append(caseNameList, string(caseName))
		}
		sort.Strings(caseNameList)

		idx := 1
		rows := make([][]string, 0, len(caseList))
		for _, caseName := range caseNameList {
			if f, ok := caseList[cases.CaseName(caseName)]; ok {
				caseInfo := f(ctx)
				rows = append(rows, []string{
					strconv.Itoa(idx),
					caseName,
					caseInfo.Desc(),
				})
				idx++
			}
		}

		header := []string{"NO", "CASE", "DESCRIPTION"}
		tableString := utils.PrintTable(header, rows, nil)

		logger.Infof(ctx, "\n"+tableString.String())
	},
}

func init() {
	rootCmd.AddCommand(showCaseCmd)
}
