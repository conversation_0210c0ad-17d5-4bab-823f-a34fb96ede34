package cmd

import (
	"context"
	"fmt"
	"os"

	"github.com/spf13/cobra"

	exec "icode.baidu.com/baidu/cce-test/e2e-test/internal/executor"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

var queryCmd = &cobra.Command{
	Use:   "query",
	Short: "根据任务检索测试结果",
	Example: `
# 查询指定任务测试结果
./cce-e2e-test query --job check-e2e-test
`,
	Run: func(cmd *cobra.Command, args []string) {
		ctx := context.WithValue(context.TODO(), logger.RequestID, logger.GetUUID())
		var err error

		defer func() {
			// 统一打印Run的err信息
			if err != nil {
				logger.Errorf(ctx, err.Error())
				os.Exit(1)
			}
		}()

		err = prepareQueryConfig(ctx)
		if err != nil {
			return
		}
		// 初始化qaDB client
		qaDB, newDBErr := models.NewClient(ctx, clientConfig.QAMySQLEndpoint)
		if newDBErr != nil {
			err = fmt.Errorf("models.NewClient failed: %v", newDBErr)
			return
		}
		testJob, exists, getErr := qaDB.GetTestJobByName(jobName)
		if getErr != nil {
			err = fmt.Errorf("models.GetTestJobByName failed: %v", getErr)
			return
		}
		if !exists {
			err = fmt.Errorf("job %s not found", jobName)
			return
		}

		testResults := testJob.TestResults
		resultCount := len(testResults)
		failedList := make([]models.TestResult, 0, resultCount)
		successList := make([]models.TestResult, 0, resultCount)
		for _, r := range testResults {
			if !r.Success {
				failedList = append(failedList, r)
			}

			if r.Success {
				successList = append(successList, r)
			}
		}
		result := exec.Result{
			BatchID:     testJob.BatchID,
			Region:      testJob.Region,
			FailedList:  failedList,
			SuccessList: successList,
		}
		// 输出报告
		exec.PrintReport(ctx, result)
	},
}

func init() {
	rootCmd.AddCommand(queryCmd)

	queryCmd.Flags().StringVar(&jobName, "job", "", "测试任务名称")
	queryCmd.Flags().StringVar(&configFile, "config", "config.yaml", "运行环境配置文件，配置文件读取目录为 conf/")
	_ = queryCmd.MarkFlagRequired("job")
}
