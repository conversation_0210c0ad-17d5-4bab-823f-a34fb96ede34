package cmd

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/spf13/cobra"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

var queryJobCmd = &cobra.Command{
	Use:   "query-job",
	Short: "检索历史测试任务",
	Example: `
# 查询指定批次的测试任务
./cce-e2e-test query-job --batch-id 20240710_164949

# 查询所有测试任务
./cce-e2e-test query-job -l --max-count 10
`,
	Run: func(cmd *cobra.Command, args []string) {
		ctx := context.WithValue(context.TODO(), logger.RequestID, logger.GetUUID())
		var err error

		defer func() {
			// 统一打印Run的err信息
			if err != nil {
				logger.Errorf(ctx, err.Error())
				os.Exit(1)
			}
		}()

		err = prepareQueryConfig(ctx)
		if err != nil {
			return
		}
		// 初始化qaDB client
		qaDB, newDBErr := models.NewClient(ctx, clientConfig.QAMySQLEndpoint)
		if newDBErr != nil {
			err = fmt.Errorf("models.NewClient failed: %v", newDBErr)
			return
		}
		listConditions := models.ListTestJobConditions{
			BatchID: batchID,
			Region:  region,
			Name:    jobName,
			Limit:   maxListCount,
		}
		if !isList {
			listConditions.Limit = 0
		}
		testJobList, listErr := qaDB.ListTestJob(listConditions)
		if listErr != nil {
			err = fmt.Errorf("qaDB.ListTestJob failed: %v", listErr)
			return
		}
		header := []string{"NO", "BATCH-ID", "REGION", "CLUSTER-ID", "CLUSTER", "NAME", "COST", "SUCCESS", "FAILED", "FINISHED", "CREATED"}
		rows := make([][]string, 0, len(testJobList))
		for _, job := range testJobList {
			rows = append(rows, []string{
				fmt.Sprintf("%d", job.ID),
				job.BatchID,
				job.Region,
				job.ClusterID,
				job.ClusterName,
				job.Name,
				job.Cost.String(),
				strconv.Itoa(job.SuccessCount),
				strconv.Itoa(job.FailedCount),
				utils.GetSafeFormattedTime(job.FinishedAt, time.DateTime),
				utils.GetSafeFormattedTime(job.CreatedAt, time.DateTime),
			})
		}
		tableString := utils.PrintTable(header, rows, nil)
		logger.Infof(ctx, "\n"+tableString.String())
	},
}

func init() {
	rootCmd.AddCommand(queryJobCmd)

	queryJobCmd.Flags().StringVar(&batchID, "batch-id", "", "任务批次ID")
	queryJobCmd.Flags().StringVar(&region, "region", "", "集群地域")
	queryJobCmd.Flags().BoolVarP(&isList, "list", "l", false, "列出所有测试结果")
	queryJobCmd.Flags().IntVar(&maxListCount, "max-count", 50, "最大查询条数")
	queryJobCmd.Flags().StringVar(&jobName, "job", "", "测试任务名称")
	queryJobCmd.Flags().StringVar(&configFile, "config", "config.yaml", "运行环境配置文件，配置文件读取目录为 conf/")
	queryJobCmd.MarkFlagsOneRequired("batch-id", "list")
}
