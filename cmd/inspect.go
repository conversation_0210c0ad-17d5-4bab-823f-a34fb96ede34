/* inspect.go */
/*
modification history
--------------------
2024/12/4, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
*/

package cmd

import (
	"context"
	"errors"
	"fmt"
	"os"
	"sync"
	"time"

	"github.com/spf13/cobra"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/inspect"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

var (
	product     string
	pollingType string
	pastDays    int
	TimePeriod  string
)

var inspectCmd = &cobra.Command{
	Use:   "inspect",
	Short: "查询巡检接口粒度结果",
	Example: `
# 查询指定7天内cce的时延成功率
./cce-e2e-test inspect --project cce --type latencyStandardRate -p 7
# 查询1天内cce的接口成功率
./cce-e2e-test inspect
`,
	Run: func(cmd *cobra.Command, args []string) {
		ctx := context.WithValue(context.TODO(), logger.RequestID, logger.GetUUID())
		var err error

		defer func() {
			// 统一打印Run的err信息
			if err != nil {
				logger.Errorf(ctx, err.Error())
				os.Exit(1)
			}
		}()

		err = prepareInspectConfig(ctx)
		if err != nil {
			return
		}

		client := inspect.NewClient("")
		logger.Infof(ctx, TimePeriod)
		dataKeyList, err := client.GetPollingDataKey(&inspect.ListDataKeyRequest{
			Product:     inspect.Product(product),
			PollingType: inspect.OpenApiType,
			Key:         "case",
		})
		if err != nil {
			err = fmt.Errorf("Get polling data key failed: %v ", err)
			return
		}
		fmt.Println("| 接口名称 | 成功率      |")
		fmt.Println("|:--------:| -------------:|")

		ch := make(chan string, len(dataKeyList.Options))
		var wg sync.WaitGroup
		for _, dataKey := range dataKeyList.Options {
			if _, exist := dataKey["label"]; !exist {
				continue
			}
			caseName := dataKey["label"]
			ch <- caseName
		}
		// 5并发请求单接口的成功率数据
		for i := 0; i < 5; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				for caseName := range ch {
					GetPollingDataSuccessRate(client, caseName, pollingType)
				}
			}()
		}
		go func() {
			for {
				if len(ch) == 0 {
					close(ch)
					break
				}
				time.Sleep(time.Second)
			}
		}()
		wg.Wait()
	},
}

func init() {
	rootCmd.AddCommand(inspectCmd)

	inspectCmd.Flags().StringVar(&product, "project", "cce", "巡检项目名称，默认为cce")
	inspectCmd.Flags().StringVar(&pollingType, "type", "successRate", "查询类型，接口成功率successRate或时延达标率latencyStandardRate，默认successRate")
	inspectCmd.Flags().IntVarP(&pastDays, "pastDays", "p", 1, "时间范围，过去多少天至今，默认为1天")
}

func prepareInspectConfig(ctx context.Context) (err error) {
	if product != "cce" && product != "ccr" {
		err = errors.New("Project is only supported to cce and ccr ")
		return
	}
	if pollingType != "successRate" && pollingType != "latencyStandardRate" {
		err = errors.New("Type is only supported to successRate and latencyStandardRate ")
		return
	}

	TimePeriod = GetTimePeriod(pastDays)
	return
}

func GetTimePeriod(days int) string {
	now := time.Now()
	daysAgo := now.AddDate(0, 0, -days)
	startOfDay := time.Date(daysAgo.Year(), daysAgo.Month(), daysAgo.Day(), 0, 0, 0, 0, daysAgo.Location())
	endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
	return fmt.Sprintf("%s,%s", startOfDay.Format(time.DateTime), endOfDay.Format(time.DateTime))
}

func GetPollingDataSuccessRate(client *inspect.Client, caseName, pollingType string) {
	var totalCount, successCount, failedCount int
	totalReq, err := client.GetPollingData(&inspect.ListDataRequest{
		Product:     inspect.Product(product),
		PollingType: inspect.OpenApiType,
		Case:        caseName,
		TimeRange:   TimePeriod,
		Result: func(ptype string) *int {
			if ptype == "latencyStandardRate" {
				res := 1
				return &res
			}
			return nil
		}(pollingType),
	})
	if err != nil {
		fmt.Printf("get inspection data failed: %v", err)
		return
	}

	if totalReq.Total == 0 {
		return
	}

	totalCount = totalReq.Total

	failedCountReq, err := client.GetPollingData(&inspect.ListDataRequest{
		Product:     inspect.Product(product),
		PollingType: inspect.OpenApiType,
		Case:        caseName,
		TimeRange:   TimePeriod,
		Result: func(ptype string) *int {
			if ptype == "successRate" {
				res := 0
				return &res
			}
			return nil
		}(pollingType),
		IsMatch: func(ptype string) *int {
			if ptype == "latencyStandardRate" {
				res := 0
				return &res
			}
			return nil
		}(pollingType),
	})
	if err != nil {
		fmt.Printf("Get inspection data failed: %v ", err)
		return
	}
	failedCount = failedCountReq.Total
	successCount = totalCount - failedCount
	if successCount == totalCount {
		fmt.Printf("| %s | %d/%d = %.2f%% |\n", caseName, successCount, totalCount, float64(successCount)*100/float64(totalCount))
	} else {
		fmt.Printf("| \033[31m%s | %d/%d = %.2f%%\033[0m |\n", caseName, successCount, totalCount, float64(successCount)*100/float64(totalCount))
	}
	return
}
