package cmd

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"text/template"

	"github.com/huandu/xstrings"
	"github.com/spf13/cobra"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

var (
	newCaseName     string
	newCaseDesc     string
	newCaseFilePath string
)

type newCaseFileRenderData struct {
	CaseName      string
	CaseDesc      string
	SnakeCaseName string
	PackageName   string
}

/*
cce-e2e-test add-case --name=TestCase --path=new/test_case.go --desc=新Case
*/
var addCaseCmd = &cobra.Command{
	Use:   "add-case",
	Short: "新增新的 Case 文件",
	Example: `
# path 支持多级目录，最后一级目录作为package名称
./cce-e2e-test add-case --name TestCase --path new/test_case.go --desc 新Case
`,
	RunE: func(cmd *cobra.Command, args []string) (err error) {
		ctx := context.Background()
		baseName := filepath.Base(newCaseFilePath)
		dir := strings.ReplaceAll(filepath.Dir(newCaseFilePath), "internal/cases/", "")
		ext := filepath.Ext(baseName)
		if ext != ".go" {
			err = errors.New("path 文件后缀必须是 .go")
			return
		}
		if dir == "." {
			err = errors.New("path 文件路径必须包含目录，目录不能为 `.`")
			return
		}
		// 检查case是否已经存在，不能反复添加
		caseList := cases.CCECaseList(ctx)
		if _, exists := caseList[cases.CaseName(newCaseName)]; exists {
			err = fmt.Errorf("case %s 已经存在", newCaseName)
			return
		}
		fullRelDir := filepath.Join("internal", "cases", dir)
		fullCaseFilePath := filepath.Join(fullRelDir, baseName)

		newCaseFileRenderData := newCaseFileRenderData{
			CaseName:      xstrings.ToPascalCase(newCaseName),
			CaseDesc:      newCaseDesc,
			SnakeCaseName: xstrings.ToCamelCase(newCaseName),
			PackageName:   filepath.Base(dir),
		}
		// 加载模板进行渲染
		caseTemplate, readErr := os.ReadFile(filepath.Join("templates", "new-case.tmpl"))
		if readErr != nil {
			err = fmt.Errorf("读取模板文件失败：%v", readErr)
			return
		}
		tpl := template.New("new-case")
		var tplInstance *template.Template
		tplInstance, err = tpl.Parse(string(caseTemplate))
		if err != nil {
			err = fmt.Errorf("parse case template failed, %v", err)
			return
		}
		buf := bytes.NewBuffer(nil)
		err = tplInstance.Execute(buf, newCaseFileRenderData)
		if err != nil {
			err = fmt.Errorf("render case template failed, %w", err)
			return
		}
		// 创建新的case目录
		err = os.MkdirAll(fullRelDir, 0755)
		if err != nil {
			err = fmt.Errorf("创建 Case 目录失败：%v", err)
			return
		}
		// 写入新的case文件
		err = os.WriteFile(fullCaseFilePath, buf.Bytes(), 0644)
		if err != nil {
			err = fmt.Errorf("创建 Case 文件失败：%v", err)
			return
		}

		logger.Infof(ctx, "成功生成新的 Case 文件: %s", fullCaseFilePath)
		logger.Infof(ctx, "如果是新的包请在 cmd/root.go 文件中引入 _ \"icode.baidu.com/baidu/cce-test/e2e-test/%s\"", fullRelDir)
		return
	},
}

func init() {
	rootCmd.AddCommand(addCaseCmd)

	addCaseCmd.Flags().StringVar(&newCaseName, "name", "", "新增 Case 的名称")
	addCaseCmd.Flags().StringVar(&newCaseFilePath, "path", "", "新增 Case 的文件路径，包含文件名，默认添加 internal/cases/ 作为前缀目录")
	addCaseCmd.Flags().StringVar(&newCaseDesc, "desc", "[待补充]", "新增 Case 的描述，可在 Desc 方法中修改")
	_ = addCaseCmd.MarkFlagRequired("name")
	_ = addCaseCmd.MarkFlagRequired("path")
}
