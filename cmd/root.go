package cmd

import (
	"context"
	"errors"
	"os"

	"github.com/spf13/cobra"

	"icode.baidu.com/baidu/cce-test/e2e-test/conf"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/casegroup"
	_ "icode.baidu.com/baidu/cce-test/e2e-test/internal/cases/aiinfra"
	_ "icode.baidu.com/baidu/cce-test/e2e-test/internal/cases/appservice"
	_ "icode.baidu.com/baidu/cce-test/e2e-test/internal/cases/autoscaler"
	_ "icode.baidu.com/baidu/cce-test/e2e-test/internal/cases/cluster"
	_ "icode.baidu.com/baidu/cce-test/e2e-test/internal/cases/csi"
	_ "icode.baidu.com/baidu/cce-test/e2e-test/internal/cases/helm"
	_ "icode.baidu.com/baidu/cce-test/e2e-test/internal/cases/ingress"
	_ "icode.baidu.com/baidu/cce-test/e2e-test/internal/cases/inspection"
	_ "icode.baidu.com/baidu/cce-test/e2e-test/internal/cases/instance"
	_ "icode.baidu.com/baidu/cce-test/e2e-test/internal/cases/instancegroup"
	_ "icode.baidu.com/baidu/cce-test/e2e-test/internal/cases/k8s"
	_ "icode.baidu.com/baidu/cce-test/e2e-test/internal/cases/monitor"
	_ "icode.baidu.com/baidu/cce-test/e2e-test/internal/cases/network"
	_ "icode.baidu.com/baidu/cce-test/e2e-test/internal/cases/plugin"
	_ "icode.baidu.com/baidu/cce-test/e2e-test/internal/cases/service/nodebackend-lb-service"
	_ "icode.baidu.com/baidu/cce-test/e2e-test/internal/cases/service/podbackend-lb-service"
	_ "icode.baidu.com/baidu/cce-test/e2e-test/internal/cases/workflow"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	beegologger "icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger/beego"
	pluginclients "icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
)

var (
	logFile          string
	configFile       string
	pluginConfigFile string
	caseGroupFile    string
	batchID          string
	region           string
)

var (
	jobName      string
	isList       bool
	maxListCount int
)

var (
	clientConfig    conf.Config
	pluginConfig    pluginclients.Config
	caseGroupConfig casegroup.Config
)

var rootCmd = &cobra.Command{
	Use:   "cce-e2e-test",
	Short: "cce-stack 测试框架",
	Long: `该测试框架主要用于进行 CCE 集群的端到端测试。
主要采用 golang 语言进行开发。它通过模拟用户的行为，确保 k8s 集群的各个组件在真实环境中能够正常工作。
该框架主要用于验证集群的部署，节点及节点组的扩容和缩容，服务的更新，网络的联通以及 CCE 所提供的各插件功能等场景。`,
}

// Execute 添加命令行入口
func Execute() {
	err := rootCmd.Execute()
	if err != nil {
		os.Exit(1)
	}
}

func init() {
	cobra.OnInitialize(initConfig)

	rootCmd.CompletionOptions.DisableDefaultCmd = true
	rootCmd.PersistentFlags().StringVar(&logFile, "log-file", "", "日志路径, 不设置则打印到 STDOUT")
}

func initConfig() {
	// 选择beego log作为日志组件
	logger.SetLogger(beegologger.NewLogger(logFile))
}

func prepareQueryConfig(ctx context.Context) (err error) {
	err = conf.LoadConfig(ctx, configFile, &clientConfig)
	if err != nil {
		return
	}
	if clientConfig.QAMySQLEndpoint == "" {
		err = errors.New("Endpoint.QAMySQLEndpoint is empty")
		return
	}
	return
}
