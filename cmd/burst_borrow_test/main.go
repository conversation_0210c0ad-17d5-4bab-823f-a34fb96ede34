// Copyright 2024 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2024/10/07 11:00:00, by z<PERSON><PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
DESCRIPTION
测试ENI Burst模式下IP借用功能的主程序
*/

package main

import (
	"context"
	"flag"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"reflect"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/cases"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

var (
	kubeConfigPath string
	testTimeoutMin int
)

func init() {
	flag.StringVar(&kubeConfigPath, "kubeconfig", "/Users/<USER>/cce/cce-network-v2-vpc.conf", "Path to kubeconfig file")
	flag.IntVar(&testTimeoutMin, "timeout", 15, "Test timeout in minutes")
}

func main() {
	flag.Parse()

	ctx := context.Background()
	logger.Infof(ctx, "启动Burst模式下IP借用功能测试")

	// 确保kubeconfig文件存在
	if _, err := os.Stat(kubeConfigPath); os.IsNotExist(err) {
		log.Fatalf("kubeconfig文件不存在: %s", kubeConfigPath)
	}

	// 读取kubeconfig文件
	kubeConfigContent, err := ioutil.ReadFile(kubeConfigPath)
	if err != nil {
		log.Fatalf("读取kubeconfig文件失败: %v", err)
	}

	// 创建测试实例 - 由于是包内私有函数，我们创建burstBorrowedSubnetTest实例
	// 使用反射获取案例列表
	caseList := cases.CCECaseList(ctx)
	var test cases.Interface
	for name, creator := range caseList {
		// 找到BurstBorrowedSubnetTest测试
		if name == "BurstBorrowedSubnetTest" {
			test = creator(ctx)
			break
		}
	}

	if test == nil {
		log.Fatalf("未找到测试用例: BurstBorrowedSubnetTest")
	}

	// 执行测试
	if err := runTest(ctx, test, kubeConfigContent); err != nil {
		log.Fatalf("测试失败: %v", err)
	}

	logger.Infof(ctx, "测试完成")
}

// 定义一个自定义的Run方法，因为Interface中没有定义Run
type testRunner interface {
	cases.Interface
	Run(ctx context.Context) error
}

func runTest(ctx context.Context, test cases.Interface, kubeConfig []byte) error {
	// 创建带超时的上下文
	testCtx, cancel := context.WithTimeout(ctx, time.Duration(testTimeoutMin)*time.Minute)
	defer cancel()

	// 初始化测试
	logger.Infof(ctx, "初始化测试...")
	if err := test.Init(testCtx, kubeConfig, nil); err != nil {
		return fmt.Errorf("初始化测试失败: %v", err)
	}

	// 执行前置检查
	logger.Infof(ctx, "执行前置检查...")
	resources, err := test.Check(testCtx)
	if err != nil {
		return fmt.Errorf("前置检查失败: %v", err)
	}
	logger.Infof(ctx, "前置检查完成，资源数量: %d", len(resources))

	// 尝试通过反射调用Run方法
	logger.Infof(ctx, "运行测试...")
	runMethod := reflect.ValueOf(test).MethodByName("Run")
	if !runMethod.IsValid() {
		return fmt.Errorf("测试实例不支持Run方法")
	}

	results := runMethod.Call([]reflect.Value{reflect.ValueOf(testCtx)})
	if len(results) > 0 && !results[0].IsNil() {
		// 获取错误信息
		err := results[0].Interface().(error)

		// 尝试清理资源
		cleanErr := test.Clean(context.Background())
		if cleanErr != nil {
			logger.Warnf(ctx, "清理资源失败: %v", cleanErr)
		}
		return fmt.Errorf("运行测试失败: %v", err)
	}

	if !test.Continue(ctx) {
		logger.Infof(ctx, "测试指示不需要继续执行")
		return nil
	}

	// 清理测试资源
	logger.Infof(ctx, "清理测试资源...")
	if err := test.Clean(context.Background()); err != nil {
		return fmt.Errorf("清理资源失败: %v", err)
	}

	return nil
}
