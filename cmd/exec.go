package cmd

import (
	"context"
	"errors"
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/cobra"

	"icode.baidu.com/baidu/cce-test/e2e-test/conf"
	"icode.baidu.com/baidu/cce-test/e2e-test/internal/casegroup"
	exec "icode.baidu.com/baidu/cce-test/e2e-test/internal/executor"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	beegologger "icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger/beego"
	pluginclients "icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

var (
	caseNames []string
	isDag     bool
)

var execCmd = &cobra.Command{
	Use:   "exec",
	Short: "指定case group批量执行case",
	Example: `
# 随机生成批次ID
./cce-e2e-test exec -g internal/casegroup/check-e2e-test.yaml

# 指定批次ID
BATCH_ID=20240710_164949 ./cce-e2e-test exec -g internal/casegroup/check-e2e-test.yaml
`,
	Run: func(cmd *cobra.Command, args []string) {
		uuid := logger.GetUUID()
		ctx := context.WithValue(context.TODO(), logger.RequestID, uuid)
		var executor *exec.Executor
		var err error

		defer func() {
			// 统一打印Run的err信息
			if err != nil {
				// 避免日志写入文件看不到退出原因
				fmt.Printf("\n%v\n", err)
				logger.Errorf(ctx, err.Error())
				os.Exit(1)
			}
		}()

		err = prepareConfig(ctx)
		if err != nil {
			return
		}
		caseGroupFileName := utils.GetFileName(caseGroupFile, false)

		// 脚本名称默认读取环境变量，需要在Job里面注入Env，如果读取不到则以casegroup的文件名作为Job名称
		jobName := os.Getenv(casegroup.EnvJobName)
		if jobName == "" {
			// 增加本地测试job名称的随机性，模拟正式跑的job名称
			jobName = caseGroupFileName + "-" + uuid[:5]
		}

		// 如果需要上传job日志，需要将日志输出到文件
		if clientConfig.UploadJobLog {
			caseGroupLogFile := fmt.Sprintf("%s_%s.log", clientConfig.BatchID, jobName)
			logger.SetLogger(beegologger.NewLogger(filepath.Join(logger.DefaultLoggerDir, caseGroupLogFile)))
		}

		// 初始化case执行器
		executor, err = exec.NewExecutor(ctx, &clientConfig, &caseGroupConfig, isDag)
		if err != nil {
			err = fmt.Errorf("create executor failed, err: %v", err)
			return
		}
		executor.JobName = jobName
		executor.CaseNames = caseNames
		// 初始化任务
		err = executor.Setup()
		if err != nil {
			err = fmt.Errorf("setup executor failed: %v", err)
			return
		}

		defer func() {
			tdErr := executor.Teardown()
			if tdErr != nil {
				err = fmt.Errorf("teardown executor failed, err: %v", tdErr)
				return
			}
			return
		}()

		// 执行case
		err = executor.Run(ctx)
		if err != nil {
			// 这里必须要用 %w 来wrap错误类型
			err = fmt.Errorf("case run failed, err: %w", err)
			// 如果不是失败case导致的err不需要退出
			if !errors.Is(err, exec.CaseErr) {
				return
			}
			// 这里需要打印每个case的失败信息之后清空err，方便排查问题
			logger.Errorf(ctx, err.Error())
			err = nil
		}

		// 获取case执行结果
		var result exec.Result
		// 日志注入集群ID信息
		ctx = context.WithValue(ctx, logger.ClusterID, executor.ClusterID)
		result, err = executor.Result(ctx)
		if err != nil {
			err = fmt.Errorf("get case result failed, err: %v", err)
			return
		}

		// 输出报告
		exec.PrintReport(ctx, result)

		failedCaseCount := len(result.FailedList)
		if len(result.FailedList) > 0 {
			err = fmt.Errorf("test case failed, count is %d", failedCaseCount)
			return
		}
		return
	},
}

func init() {
	rootCmd.AddCommand(execCmd)

	execCmd.Flags().StringVarP(&caseGroupFile, "case-group", "g", "", "需要执行的case group文件。")
	execCmd.Flags().StringVar(&configFile, "config", "config.yaml", "运行环境配置文件，配置文件默认读取目录为 conf/。")
	execCmd.Flags().StringVar(&pluginConfigFile, "plugin-config", "plugin-config.yaml", "运行环境插件配置文件，配置文件读取目录为 conf/。")
	execCmd.Flags().StringSliceVar(&caseNames, "cases", nil, "需要明确执行的case名称，多个case用逗号分隔，不填写时按照配置文件全部执行。")
	execCmd.Flags().BoolVar(&isDag, "dag", false, "是否以DAG模式执行，默认false。")
	_ = execCmd.MarkFlagRequired("case-group")
}

func prepareConfig(ctx context.Context) (err error) {
	// 加载 cluster-client 配置文件
	err = conf.LoadConfig(ctx, configFile, &clientConfig)
	if err != nil {
		return
	}
	err = conf.CheckConfig(ctx, &clientConfig)
	if err != nil {
		return
	}
	// 加载 plugin-client 配置文件
	err = conf.LoadConfig(ctx, pluginConfigFile, &pluginConfig)
	if err != nil {
		return
	}
	err = pluginclients.CheckConfig(ctx, &pluginConfig)
	if err != nil {
		return
	}
	// 加载 case group 配置文件
	err = conf.LoadConfig(ctx, caseGroupFile, &caseGroupConfig)
	if err != nil {
		return
	}
	err = caseGroupConfig.CheckConfig(ctx, isDag)
	if err != nil {
		return
	}
	clientConfig.PluginConfig = &pluginConfig
	return
}
