# virtual-kubelet架构

virtual-kubelet 在用户的集群伪装成一个k8s node，将pod调度至云厂商的serverless产品，并对Pod进行生命周期管理。

## virtual-kubelet项目结构
- baidubci - BCI Provider相关接口实现
- config - virtual-kubelet stateful部署配置
- package - 依赖package工具包

## 主要接口
- CreatePod() - 创建bci pod
- UpdatePod() - 更新bci pod
- DeletePod() - 删除bci pod
- GetPod() - 获取bci pod详情
- GetPodStatus() - 获取bci pod status
- GetPods() - 获取bci pods列表
- GetContainerLogs() - 获取bci pod容器日志
- RunInContainer() - 登录bci pod容器
- AttachToContainer() - 登录bci pod容器
- GetStatsSummary() - 获取bci pod stats
- GetMetricsResource() - 获取bci pod metrics
- PortForward() - forwards a local port to a port on the pod
- UpdateConfigMap() - 更新configmap

- PodCache interface - 缓存bci pod相关接口

## 主要数据结构
- podCache - 缓存bci pod相关数据
- cachedPod - 缓存bci pod详情
- creatingPod - 记录创建中的bci pod
- subnetUsage - 缓存bci pod子网使用情况