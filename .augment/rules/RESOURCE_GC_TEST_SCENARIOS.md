# 容器网络资源回收功能测试需求文档

## 概述

本文档基于Code-review规则，全面识别并记录容器网络资源回收功能的测试场景，包括单元测试需求和E2E测试需求。

## 🧪 单元测试需求 (必须标记为需要单测)

### 1. BoltDB 持久化存储模块测试

#### 1.1 **核心业务逻辑函数** (必须标记为需要单测)
- **测试文件**: `pkg/resourcegc/database/boltdb_test.go`
- **测试函数**:
  - `TestStorePodResource` - 测试Pod资源记录存储
  - `TestGetPodResource` - 测试Pod资源记录查询
  - `TestDeletePodResource` - 测试Pod资源记录删除
  - `TestListAllPodResources` - 测试批量资源记录查询
  - `TestBatchOperations` - 测试批量存储和删除操作

#### 1.2 **并发访问和数据一致性测试** (必须标记为需要单测)
- **测试函数**:
  - `TestConcurrentReadWrite` - 测试读写并发安全性
  - `TestFileLockMechanism` - 测试文件锁防止多进程冲突
  - `TestTransactionIntegrity` - 测试事务完整性
  - `TestDatabaseCorruption` - 测试数据库损坏恢复

#### 1.3 **边界条件和异常处理测试** (必须标记为需要单测)
- **测试函数**:
  - `TestInvalidPodRecord` - 测试无效Pod记录处理
  - `TestEmptyDatabase` - 测试空数据库操作
  - `TestDiskSpaceExhaustion` - 测试磁盘空间不足处理
  - `TestPermissionDenied` - 测试权限不足异常处理

### 2. 垃圾收集控制器模块测试

#### 2.1 **核心业务逻辑函数** (必须标记为需要单测)
- **测试文件**: `pkg/resourcegc/controller/garbage_collector_test.go`
- **测试函数**:
  - `TestDetectLeakedResources` - 测试泄漏资源检测逻辑
  - `TestGCCycleExecution` - 测试完整GC周期执行
  - `TestConfigMapConfigUpdate` - 测试ConfigMap配置动态更新
  - `TestGCIntervalControl` - 测试GC间隔时间控制

#### 2.2 **复杂算法和计算逻辑测试** (必须标记为需要单测)
- **测试函数**:
  - `TestPodStateComparison` - 测试Pod状态对比算法
  - `TestResourceLeakDetermination` - 测试资源泄漏判定算法
  - `TestBatchProcessingLogic` - 测试批量处理逻辑

#### 2.3 **边界条件和异常处理测试** (必须标记为需要单测)
- **测试函数**:
  - `TestK8sAPIUnavailable` - 测试Kubernetes API不可用处理
  - `TestLargeScaleResourceCleanup` - 测试大规模资源清理边界
  - `TestGCTimeout` - 测试GC超时处理

### 3. 清理任务执行器模块测试

#### 3.1 **核心业务逻辑函数** (必须标记为需要单测)
- **测试文件**: `pkg/resourcegc/cleanup/tasks_test.go`
- **测试函数**:
  - `TestVethCleanupTask` - 测试veth接口清理任务
  - `TestRouteCleanupTask` - 测试路由清理任务
  - `TestQdiscCleanupTask` - 测试流量控制清理任务
  - `TestExclusiveENICleanupTask` - 测试独占ENI清理任务

#### 3.2 **数据转换和验证函数测试** (必须标记为需要单测)
- **测试函数**:
  - `TestTaskPriorityOrdering` - 测试任务优先级排序
  - `TestTaskRetryMechanism` - 测试任务重试机制
  - `TestTaskResultValidation` - 测试任务结果验证

#### 3.3 **边界条件和异常处理测试** (必须标记为需要单测)
- **测试函数**:
  - `TestNetworkInterfaceNotFound` - 测试网络接口不存在处理
  - `TestInsufficientPermissions` - 测试权限不足处理
  - `TestConcurrentTaskExecution` - 测试并发任务执行安全性

### 4. CNI 前置清理模块测试

#### 4.1 **核心业务逻辑函数** (必须标记为需要单测)
- **测试文件**: `plugins/cptp/ptp_test.go`
- **测试函数**:
  - `TestPerformLocalResourceCleanup` - 测试前置本地资源清理
  - `TestCleanupHostVethInterface` - 测试主机veth接口清理
  - `TestCleanupTrafficControl` - 测试流量控制清理

#### 4.2 **数据转换和验证函数测试** (必须标记为需要单测)
- **测试函数**:
  - `TestK8SArgsValidation` - 测试K8S参数验证
  - `TestVethNameGeneration` - 测试veth名称生成算法
  - `TestErrorHandling` - 测试错误处理和降级逻辑

### 5. 数据结构和类型验证测试

#### 5.1 **数据转换和验证函数测试** (必须标记为需要单测)
- **测试文件**: `pkg/resourcegc/database/types_test.go`
- **测试函数**:
  - `TestPodResourceRecordValidation` - 测试Pod资源记录验证
  - `TestPodKeyGeneration` - 测试Pod唯一键生成
  - `TestResourceAddRemove` - 测试资源添加和移除操作
  - `TestJSONSerialization` - 测试JSON序列化和反序列化

## 🌐 E2E测试需求 (必须标记为需要E2E)

### 1. 完整用户操作流程测试 (必须标记为需要E2E)

#### 1.1 **P0 核心场景**
- **E2E-001**: Pod正常创建和删除的完整资源生命周期
  - **测试目标**: 验证Pod从创建到删除的完整网络资源管理流程
  - **预期结果**: 资源正确分配、记录、使用和回收
  
- **E2E-002**: Pod强制删除场景的资源回收
  - **测试目标**: 验证Pod被强制删除时，资源能被GC正确回收
  - **预期结果**: 泄漏资源被及时发现和清理

- **E2E-003**: 大规模Pod并发创建删除场景
  - **测试目标**: 验证系统在高并发场景下的资源管理能力
  - **预期结果**: 无资源泄漏，性能满足要求

#### 1.2 **P1 重要场景**
- **E2E-004**: 节点重启后资源状态恢复
  - **测试目标**: 验证节点重启后BoltDB数据恢复和GC继续工作
  - **预期结果**: 历史数据完整，GC正常运行

- **E2E-005**: ConfigMap配置动态更新
  - **测试目标**: 验证GC配置通过ConfigMap动态更新能力
  - **预期结果**: 配置变更实时生效，无需重启组件

### 2. 关键业务场景的端到端验证 (必须标记为需要E2E)

#### 2.1 **P0 核心场景**
- **E2E-006**: VPC-ENI网络模式完整流程
  - **测试目标**: 验证VPC-ENI模式下的完整资源管理流程
  - **预期结果**: ENI资源正确分配、使用和回收

- **E2E-007**: VPC-Route网络模式完整流程
  - **测试目标**: 验证VPC-Route模式下的完整资源管理流程
  - **预期结果**: 路由资源正确分配、使用和回收

#### 2.2 **P1 重要场景**
- **E2E-008**: 网络策略变更影响下的资源管理
  - **测试目标**: 验证网络策略变更时资源管理的稳定性
  - **预期结果**: 资源管理不受网络策略影响

### 3. 外部系统集成的完整链路测试 (必须标记为需要E2E)

#### 3.1 **P0 核心场景**
- **E2E-009**: Agent组件异常场景下的降级处理
  - **测试目标**: 验证agent不可用时CNI的降级清理能力
  - **预期结果**: 本地资源能被CNI正确清理

- **E2E-010**: Kubernetes API Server异常场景
  - **测试目标**: 验证K8s API不可用时的系统稳定性
  - **预期结果**: 本地功能正常，恢复后自动同步

#### 3.2 **P1 重要场景**
- **E2E-011**: 百度云API集成链路测试
  - **测试目标**: 验证与百度云后端API的完整集成链路
  - **预期结果**: 云资源操作正确同步到本地记录

### 4. API接口的完整调用链路测试 (必须标记为需要E2E)

#### 4.1 **P0 核心场景**
- **E2E-012**: CNI ADD/DEL接口完整链路
  - **测试目标**: 验证CNI ADD和DEL接口的完整调用链路
  - **预期结果**: 接口调用成功，资源状态正确变更

- **E2E-013**: ResourceManager API完整调用链路
  - **测试目标**: 验证ResourceManager各个API的端到端调用
  - **预期结果**: API调用成功，数据库状态正确更新

#### 4.2 **P1 重要场景**
- **E2E-014**: 监控指标API端到端验证
  - **测试目标**: 验证Prometheus监控指标的完整采集链路
  - **预期结果**: 指标数据正确采集和上报

### 5. 异常故障场景的端到端测试 (必须标记为需要E2E)

#### 5.1 **P0 核心场景**
- **E2E-015**: 磁盘空间耗尽场景
  - **测试目标**: 验证磁盘空间不足时的系统行为
  - **预期结果**: 系统优雅降级，关键功能保持可用

- **E2E-016**: 网络分区故障场景
  - **测试目标**: 验证节点间网络分区时的系统行为
  - **预期结果**: 本地功能正常，恢复后自动同步

#### 5.2 **P1 重要场景**
- **E2E-017**: 数据库文件损坏恢复场景
  - **测试目标**: 验证BoltDB文件损坏时的恢复能力
  - **预期结果**: 能够重建数据库，恢复正常功能

## 📋 测试优先级和排期

### 单元测试优先级
1. **P0 (必须完成)**: 核心业务逻辑、数据一致性、异常处理
2. **P1 (重要)**: 边界条件、性能测试、复杂算法
3. **P2 (补充)**: 代码覆盖率完善、边缘场景

### E2E测试优先级
1. **P0 (必须完成)**: 基本功能流程、故障恢复、核心业务场景
2. **P1 (重要)**: 高级功能、集成测试、性能验证
3. **P2 (补充)**: 边缘场景、压力测试、兼容性测试

## 🔧 测试环境需求

### 单元测试环境
- Go测试环境 (>=1.19)
- Mock Kubernetes客户端
- 临时文件系统 (用于BoltDB测试)
- 网络命名空间支持

### E2E测试环境
- 完整Kubernetes集群 (>=1.20)
- CCE网络组件部署
- 百度云测试环境
- 监控系统 (Prometheus)

## 📊 测试覆盖率目标

- **单元测试代码覆盖率**: >= 80%
- **核心模块代码覆盖率**: >= 90%
- **E2E场景覆盖率**: >= 95% (核心功能)

## 🚀 实施计划

### 第一阶段 (P0测试)
- [ ] 实现核心单元测试 (BoltDB、GC控制器、清理任务)
- [ ] 实现关键E2E测试 (基本流程、异常处理)

### 第二阶段 (P1测试)
- [ ] 完善单元测试覆盖率
- [ ] 增加集成测试和性能测试

### 第三阶段 (P2测试)
- [ ] 补充边缘场景测试
- [ ] 压力测试和稳定性验证

---

**注：所有标记为"必须标记为需要单测"和"必须标记为需要E2E"的场景都是强制要求，必须在代码上线前完成。** 