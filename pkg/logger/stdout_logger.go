package logger

import (
	"context"
	"fmt"
	"log"
	"os"
)

// <PERSON><PERSON>utLogger the implement of logger.Interface to print log in stdout
type StdoutLogger struct {
	log.Logger
	loggerFuncCallDepth int // depth to trace log caller when computing the file name and line number
}

// NewStdoutLogger returns Logger
func NewStdoutLogger(prefix string, loggerFuncCallDepth int) *StdoutLogger {
	return &StdoutLogger{
		Logger:              *log.New(os.Stdout, "["+prefix+"] -- ", log.Ldate|log.Ltime|log.Lmicroseconds|log.Lshortfile),
		loggerFuncCallDepth: loggerFuncCallDepth,
	}
}

// Infof info log
func (logger *StdoutLogger) Infof(ctx context.Context, format string, v ...interface{}) {
	if ctx != nil {
		if requestID := ctx.Value(RequestID); requestID != nil {
			_ = logger.Output(logger.loggerFuncCallDepth,
				fmt.Sprintf("["+ctx.Value(RequestID).(string)+"] INFO -- "+format+"\n", v...))
			return
		}
	}
	_ = logger.Output(logger.loggerFuncCallDepth, fmt.Sprintf("INFO -- "+format+"\n", v...))
}

// Warnf warn log
func (logger *StdoutLogger) Warnf(ctx context.Context, format string, v ...interface{}) {
	if ctx != nil {
		if requestID := ctx.Value(RequestID); requestID != nil {
			_ = logger.Output(logger.loggerFuncCallDepth,
				fmt.Sprintf("["+ctx.Value(RequestID).(string)+"] WARN -- "+format+"\n", v...))
			return
		}
	}
	_ = logger.Output(logger.loggerFuncCallDepth, fmt.Sprintf("WARN -- "+format+"\n", v...))
}

// Errorf error log
func (logger *StdoutLogger) Errorf(ctx context.Context, format string, v ...interface{}) {
	if ctx != nil {
		if requestID := ctx.Value(RequestID); requestID != nil {
			_ = logger.Output(logger.loggerFuncCallDepth,
				fmt.Sprintf("["+ctx.Value(RequestID).(string)+"] ERROR -- "+format+"\n", v...))
			return
		}
	}
	_ = logger.Output(logger.loggerFuncCallDepth, fmt.Sprintf("ERROR -- "+format+"\n", v...))
}
