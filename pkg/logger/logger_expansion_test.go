package logger

import (
	"context"
	"testing"
)

func TestLoggerExpansion_WithValues(t *testing.T) {
	testCases := []struct {
		name              string
		logger            *loggerExpansion
		kvs               []interface{}
		expectedKVSLength int
	}{
		{
			name: "初始0个kv，添加2个",
			logger: &loggerExpansion{
				kvs: nil,
			},
			kvs:               []interface{}{"k", "v"},
			expectedKVSLength: 1,
		},
		{
			name: "初始0个kv，添加3个",
			logger: &loggerExpansion{
				kvs: nil,
			},
			kvs:               []interface{}{"k", "v", "xxx"},
			expectedKVSLength: 1,
		},
		{
			name: "初始2个kv，添加2个",
			logger: &loggerExpansion{
				kvs: []KeyValue{
					{
						Key:   "k1",
						Value: "v1",
					},
					{
						Key:   "k2",
						Value: "v2",
					},
				},
			},
			kvs:               []interface{}{"k", "v"},
			expectedKVSLength: 3,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			l := tc.logger.WithValues(tc.kvs...).(*loggerExpansion)
			if len(l.kvs) != tc.expectedKVSLength {
				t.Errorf("expected len(kvs) == %d, actual: %d", tc.expectedKVSLength, len(l.kvs))
			}
		})
	}
}

func TestLoggerExpansion_Infof(t *testing.T) {
	testCases := []struct {
		name   string
		kvs    []interface{}
		logger InterfaceEx
	}{
		{
			name:   "no kvs set",
			logger: WrapExpansion(logger),
		},
		{
			name:   "kvs is set",
			kvs:    []interface{}{"k", "v", "a", "b"},
			logger: WrapExpansion(logger),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			l := tc.logger.WithValues(tc.kvs...)
			ctx := context.WithValue(context.Background(), "ctx_key", "ctx_value")
			l.Infof(ctx, "%s", "xxx")
		})
	}
}
