package logger

import (
	"context"
	"fmt"

	uuid "github.com/satori/go.uuid"
)

// ContextKeyType context key
type ContextKeyType string

const (
	// RequestID context key name
	RequestID ContextKeyType = "RequestID"
	ClusterID ContextKeyType = "ClusterID"

	Region ContextKeyType = "Region"

	// UserUUID context key name
	UserUUID ContextKeyType = "UserUUID"
)

// CtxSprintf add ctx value as prefix (if exists) in the fmt.Sprintf output
func CtxSprintf(ctx context.Context, format string, v ...interface{}) string {
	if ctx != nil {
		fmtBaseString := ""
		if requestID := ctx.Value(RequestID); requestID != nil {
			fmtBaseString += fmt.Sprintf("[RequestID: %s] ", ctx.Value(RequestID).(string))
		}
		if clusterID := ctx.Value(ClusterID); clusterID != nil {
			fmtBaseString += fmt.Sprintf("[ClusterID: %s] ", ctx.Value(ClusterID).(string))
		}
		return fmt.Sprintf("%s %s", fmtBaseString, fmt.Sprintf(format, v...))
	}
	return fmt.Sprintf(format, v...)
}

// GetUserUUID extracts userUUID from context
// If userUUID is not set in context, return empty
func GetUserUUID(ctx context.Context) string {
	if ctx != nil {
		if userUUID := ctx.Value(UserUUID); userUUID != nil {
			return ctx.Value(UserUUID).(string)
		}
	}
	return ""
}

// GetUUID generates a uuid V4 with error and context concern
func GetUUID() string {
	return uuid.NewV4().String()
}

// EnsureRequestIDInCtx ensures a non-empty RequestID is present in context
func EnsureRequestIDInCtx(ctx context.Context) context.Context {
	if GetRequestID(ctx) == "" {
		return WithRequestID(ctx, GetUUID())
	}
	return ctx
}

func GetRequestID(ctx context.Context) string {
	if requestID := ctx.Value(RequestID); requestID != nil {
		return requestID.(string)
	}
	return ""
}

func WithRequestID(ctx context.Context, reqID string) context.Context {
	return context.WithValue(ctx, RequestID, reqID)
}
