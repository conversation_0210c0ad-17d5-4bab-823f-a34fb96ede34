package logrus

import (
	"context"

	"github.com/virtual-kubelet/virtual-kubelet/log"

	loggerT "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"
)

// logger implements icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger.Interface
type logger struct{}

func NewLogger() loggerT.Interface {
	return &logger{}
}

func (l *logger) Infof(ctx context.Context, format string, args ...interface{}) {
	log.G(ctx).Infof(format, args...)
}

func (l *logger) Warnf(ctx context.Context, format string, args ...interface{}) {
	log.G(ctx).Warnf(format, args...)
}

func (l *logger) Errorf(ctx context.Context, format string, args ...interface{}) {
	log.G(ctx).Errorf(format, args...)
}
