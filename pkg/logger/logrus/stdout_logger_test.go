package logrus

import (
	"context"
	"testing"

	"github.com/virtual-kubelet/virtual-kubelet/log"
	"github.com/virtual-kubelet/virtual-kubelet/trace"
)

func TestInitStdoutLogger(t *testing.T) {
	ctx := context.TODO()
	defer func() {
		log.G(ctx).Info("exit test")
	}()
	logCfg := &StdoutLogConfig{}

	InitStdoutLogger(logCfg)

	log.G(ctx).Infof("log initialized with config %+v", logCfg)

	child(ctx, 3)

}

func child(ctx context.Context, depth int) {
	ctx, span := trace.StartSpan(ctx, "child")
	defer span.End()

	ctx = span.WithField(ctx, "depth", depth)

	log.G(ctx).Info("start evaluate depth")

	if depth == 0 {
		return
	}

	log.G(ctx).Info("start to call child with depth-1 recursively")

	innerChild(ctx, depth)

	child(ctx, depth-1)
}

func innerChild(ctx context.Context, innerDepth int) {
	ctx, span := trace.StartSpan(ctx, "innerChild")
	defer span.End()

	ctx = span.WithField(ctx, "innerDepth", innerDepth)

	log.G(ctx).Info("start evaluate innerDepth")

	if innerDepth == 0 {
		return
	}

	log.G(ctx).Info("start to call innerChild with innerDepth-1 recursively")

	innerChild(ctx, innerDepth-1)
}
