package logrus

import (
	l "github.com/sirupsen/logrus"
	vklog "github.com/virtual-kubelet/virtual-kubelet/log"
	vklogrus "github.com/virtual-kubelet/virtual-kubelet/log/logrus"
	"github.com/virtual-kubelet/virtual-kubelet/trace"
	"gopkg.in/natefinch/lumberjack.v2"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger/logrus/opencensus"
)

type RotateFileLogConfig struct {
	Filename    string
	MaxSizeInMB int
	MaxBackups  int
	MaxAgeInDay int
	Compress    bool
	LocalTime   bool
}

func InitRotateFileLogger(cfg *RotateFileLogConfig) {
	rotateLogger := l.New()
	rotateLogger.SetOutput(&lumberjack.Logger{
		Filename:   cfg.Filename,
		MaxSize:    cfg.MaxSizeInMB, // megabytes
		MaxBackups: cfg.MaxBackups,
		MaxAge:     cfg.MaxAgeInDay, //days
		Compress:   cfg.Compress,    // disabled by default
		LocalTime:  cfg.LocalTime,
	})
	rotateLogger.SetFormatter(&l.TextFormatter{
		FullTimestamp:   true,
		TimestampFormat: "2006-01-02T15:04:05.000000Z07:00", // always reserve six digit in fraction
		// NOTE(yezichao): extra caller frame skip is needed for following code to work,
		// See https://github.com/sirupsen/logrus/issues/972.
		//
		// CallerPrettyfier: func(f *runtime.Frame) (string, string) {
		// 	filename := filepath.Base(f.File)
		// 	function := filepath.Base(f.Function)
		// 	return fmt.Sprintf("%s()", function), fmt.Sprintf("%s:%d", filename, f.Line)
		// },
	})
	// rotateLogger.SetReportCaller(true)

	vklog.L = vklogrus.FromLogrus(l.NewEntry(rotateLogger))
	trace.T = opencensus.Adapter{}
}
