package logrus

import (
	"os"

	l "github.com/sirupsen/logrus"
	vklog "github.com/virtual-kubelet/virtual-kubelet/log"
	vklogrus "github.com/virtual-kubelet/virtual-kubelet/log/logrus"
	"github.com/virtual-kubelet/virtual-kubelet/trace"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger/logrus/opencensus"
)

type StdoutLogConfig struct {
	// TODO (yezichao)
}

func InitStdoutLogger(cfg *StdoutLogConfig) {
	var stdoutLogger = l.New()
	stdoutLogger.Formatter = new(l.TextFormatter)                  //default
	stdoutLogger.Formatter.(*l.TextFormatter).DisableColors = true // remove colors
	stdoutLogger.Formatter.(*l.TextFormatter).TimestampFormat = "2006-01-02T15:04:05.000000Z07:00"
	stdoutLogger.Level = l.TraceLevel
	stdoutLogger.Out = os.Stdout

	vklog.L = vklogrus.FromLogrus(l.<PERSON>(stdoutLogger))
	trace.T = opencensus.Adapter{}
}
