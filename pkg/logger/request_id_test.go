package logger

import (
	"context"
	"testing"
)

func TestCtxSprintf(t *testing.T) {
	cases := []struct {
		name string

		ctx    context.Context
		format string
		msg    string

		target string
	}{
		{
			name:   "Without requestID",
			ctx:    context.Background(),
			format: "create failed: %v",
			msg:    "mem not enough",
			target: " create failed: mem not enough",
		},
		{
			name:   "With requestID",
			ctx:    context.WithValue(context.Background(), RequestID, "aaaaaaaa"),
			format: "create failed: %v",
			msg:    "mem not enough",
			target: "[RequestID: aaaaaaaa]  create failed: mem not enough",
		},
	}

	for _, c := range cases {
		got := CtxSprintf(c.ctx, c.format, c.msg)

		if got != c.target {
			t.Errorf("CtxSprintf name=%s failed: got=%v target=%v", c.name, got, c.target)
		}
	}
}

func TestGetUserUUID(t *testing.T) {
	cases := []struct {
		name   string
		ctx    context.Context
		target string
	}{
		{
			name:   "With userUUID",
			ctx:    context.WithValue(context.Background(), UserUUID, "userUUID"),
			target: "userUUID",
		},
		{
			name:   "Without userUUID",
			ctx:    context.Background(),
			target: "",
		},
	}

	for _, c := range cases {
		got := GetUserUUID(c.ctx)

		if got != c.target {
			t.Errorf("GetUserUUID name=%s failed: got=%v target=%v", c.name, got, c.target)
		}
	}
}

func TestGetUUID(t *testing.T) {
	cases := []struct {
		name string
	}{
		{
			name: "",
		},
	}

	for _, c := range cases {
		got := GetUUID()

		if got == "" {
			t.Errorf("GetUUID name=%s failed: got=%v", c.name, got)
		}
	}
}

func TestEnsureRequestIDInCtx(t *testing.T) {
	var reqID string
	ctx := context.TODO()
	ctx = EnsureRequestIDInCtx(ctx)
	if requestID := ctx.Value(RequestID); requestID != nil {
		reqID = requestID.(string)
		t.Logf("EnsureRequestIDInCtx adds requestID %s", reqID)
	} else {
		t.Error("EnsureRequestIDInCtx do not add requestID in ctx")
		return
	}

	ctx = EnsureRequestIDInCtx(ctx)
	requestID := ctx.Value(RequestID)
	if requestID == nil {
		t.Errorf("EnsureRequestIDInCtx removes original requestID, old=%s", reqID)
		return
	}
	if reqID != requestID.(string) {
		t.Errorf("EnsureRequestIDInCtx modifies original requestID, old=%s, new=%s", reqID, requestID.(string))
		return
	}
	t.Logf("EnsureRequestIDInCtx reservers original requestID, old=%s, new=%s", reqID, requestID.(string))

}

func TestGetRequestID(t *testing.T) {
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// All test cases.
		{
			name: "empty context",
			args: args{context.TODO()},
			want: "",
		},
		{
			name: "context with request id",
			args: args{WithRequestID(context.TODO(), "aaaaaaaa")},
			want: "aaaaaaaa",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetRequestID(tt.args.ctx); got != tt.want {
				t.Errorf("GetRequestID() = %v, want %v", got, tt.want)
			}
		})
	}
}
