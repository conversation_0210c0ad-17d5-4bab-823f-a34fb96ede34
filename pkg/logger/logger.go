package logger

import (
	"context"
)

// Interface defines the functions of logger
type Interface interface {
	// Info log
	Infof(context.Context, string, ...interface{})
	// Warn log
	Warnf(context.Context, string, ...interface{})
	// Error log
	Errorf(context.Context, string, ...interface{})
}

const (
	loggerFuncCallDepth = 3
)

// Logger the global var
var (
	logger   Interface
	loggerEx InterfaceEx
)

func init() {
	// Init logger to print log into stdout
	logger = NewStdoutLogger("common", loggerFuncCallDepth)
	loggerEx = WrapExpansion(logger)
}

// SetLogger reset logger
func SetLogger(loggerT Interface) {
	if loggerT != nil {
		logger = loggerT
		loggerEx = WrapExpansion(loggerT)
	}
}

// InfofWithoutCtx info log
func InfofWithoutCtx(format string, v ...interface{}) {
	logger.Infof(nil, format, v...)
}

// Infof info log
func Infof(ctx context.Context, format string, v ...interface{}) {
	logger.Infof(ctx, format, v...)
}

// Warnf warn log
func Warnf(ctx context.Context, format string, v ...interface{}) {
	logger.Warnf(ctx, format, v...)
}

// Errorf error log
func Errorf(ctx context.Context, format string, v ...interface{}) {
	logger.Errorf(ctx, format, v...)
}

// WithValues - WithValues get expansion logger with values set
func WithValues(keyAndValues ...interface{}) InterfaceEx {
	return loggerEx.WithValues(keyAndValues...)
}
