package logger

import (
	"context"
	"fmt"
)

type InterfaceEx interface {
	Interface
	WithValues(keyAndValues ...interface{}) InterfaceEx
}

type loggerExpansion struct {
	base Interface
	kvs  KeyValues
}

type KeyValues []KeyValue

type KeyValue struct {
	Key   interface{}
	Value interface{}
}

func (kvs KeyValues) String() string {
	s := ""
	for _, kv := range kvs {
		// 末尾留一个空格
		s += fmt.Sprintf("%v=%v ", kv.Key, kv.Value)
	}
	return s
}

func (l *loggerExpansion) Infof(ctx context.Context, format string, v ...interface{}) {
	l.base.Infof(ctx, fmt.Sprintf("%s%s", l.kvs.String(), fmt.Sprintf(format, v...)))
}

func (l *loggerExpansion) Warnf(ctx context.Context, format string, v ...interface{}) {
	l.base.Warnf(ctx, fmt.Sprintf("%s%s", l.kvs.String(), fmt.Sprintf(format, v...)))
}

func (l *loggerExpansion) Errorf(ctx context.Context, format string, v ...interface{}) {
	l.base.Errorf(ctx, fmt.Sprintf("%s%s", l.kvs.String(), fmt.Sprintf(format, v...)))
}

func (l *loggerExpansion) WithValues(keyAndValues ...interface{}) InterfaceEx {
	kvs := make([]KeyValue, len(l.kvs)+(len(keyAndValues)/2))
	for i := 0; i < len(l.kvs); i++ {
		kvs[i] = l.kvs[i]
	}
	for i := 0; i < len(keyAndValues)/2; i++ {
		kvs[len(l.kvs)+i] = KeyValue{
			Key:   keyAndValues[i*2],
			Value: keyAndValues[i*2+1],
		}
	}
	return &loggerExpansion{
		base: l.base,
		kvs:  kvs,
	}
}

func WrapExpansion(l Interface) InterfaceEx {
	return &loggerExpansion{
		base: l,
		kvs:  nil,
	}
}
