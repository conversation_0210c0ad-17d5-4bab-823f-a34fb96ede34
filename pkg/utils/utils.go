package utils

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strings"
	"unicode"
	"unicode/utf8"

	"github.com/astaxie/beego/validation"
)

// Valid validates all struct fields recursively in obj, which itself must also be a struct/struct porinter or slice/array of struct/struct pointer
// Wraps beego validation.Valid function for briefer usage
// See https://beego.me/docs/mvc/controller/validation.md
func Valid(obj interface{}) (err error) {
	if obj == nil {
		return
	}
	var errMsg []string
	t := reflect.TypeOf(obj)
	v := reflect.ValueOf(obj)

	// dereference first if obj is a ptr
	if t.Kind() == reflect.Ptr {
		if v.IsNil() {
			return
		}
		// obj is not nil here so the dereference is safe
		v = reflect.Indirect(v)
		t = v.Type()
	}

	// validSliceOrArray accepts a value with type of slice or array and validates element of v
	validSliceOrArray := func(v reflect.Value, prefix string) {
		for i := 0; i < v.Len(); i++ {
			elem := v.Index(i)
			kind := elem.Type().Kind()
			if kind == reflect.Ptr {
				if elem.IsZero() {
					continue
				}
				elem = reflect.Indirect(elem)
				kind = elem.Type().Kind()
			}
			if kind != reflect.Struct && kind != reflect.Slice && kind != reflect.Array {
				continue
			}
			if !v.CanInterface() {
				errMsg = append(errMsg, fmt.Sprintf("%s[%d](%s): cannot get interface",
					prefix, i, elem.Type().Name()))
				continue
			}
			if err := Valid(elem.Interface()); err != nil {
				errMsg = append(errMsg, fmt.Sprintf("%s[%d].%s", prefix, i, err.Error()))
			}
		}
	}

	if t.Kind() == reflect.Slice || t.Kind() == reflect.Array {
		// validate elements within
		validSliceOrArray(v, "")
	} else {
		// validate obj itself
		validation := validation.Validation{}
		valid, err := validation.Valid(obj)
		if err != nil {
			return err
		}
		if !valid {
			for _, err := range validation.Errors {
				errMsg = append(errMsg, fmt.Sprintf("%v: %v", err.Key, err.Message))
			}
		}
	}

	if t.Kind() == reflect.Struct {
		// validate all exported fields within recursively
		for i := 0; i < t.NumField(); i++ {
			if r, _ := utf8.DecodeRuneInString(t.Field(i).Name); !unicode.IsUpper(r) {
				// Unexported struct field, ignore it as we cannot get its value as an interface.
				continue
			}
			// type name prefix
			prefix := t.Name()
			val := v.Field(i)
			typ := t.Field(i).Type
			if typ.Kind() == reflect.Ptr {
				if val.IsZero() {
					continue
				}
				val = reflect.Indirect(val)
				typ = val.Type()
			}
			// only consider struct/slice/array fields, as Valid only accept these types
			switch {
			case typ.Kind() == reflect.Struct:
				if val.CanInterface() {
					err := Valid(val.Interface())
					if err != nil {
						errMsg = append(errMsg, fmt.Sprintf("%s.%s", prefix, err.Error()))
					}
				} else {
					errMsg = append(errMsg, fmt.Sprintf("%s.%s: cannot get interface", prefix, t.Field(i).Name))
				}

			case typ.Kind() == reflect.Slice || typ.Kind() == reflect.Array:
				validSliceOrArray(val, prefix+"."+t.Field(i).Name)

			}
		}
	}

	if len(errMsg) > 0 {
		return fmt.Errorf(strings.Join(errMsg, ", "))
	}

	return nil
}

// ToJSON - 结构体以 JSON 格式打印
func ToJSON(obj interface{}) string {
	if str, err := json.Marshal(obj); err == nil {
		return string(str)
	}

	return ""
}
