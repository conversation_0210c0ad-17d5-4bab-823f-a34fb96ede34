package retrypolicy

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	bceerror "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/error"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/util"
)

func TestGetMaxErrorRetry(t *testing.T) {
	expected := 3

	retryPolicy := &DefaultRetryPolicy{
		MaxErrorRetry: expected,
	}

	if retryPolicy.MaxErrorRetry != expected {
		t.Error(util.FormatTest("GetMaxErrorRetry", strconv.Itoa(retryPolicy.MaxErrorRetry), strconv.Itoa(expected)))
	}
}

func TestGetMaxDelay(t *testing.T) {
	expected := 20

	retryPolicy := &DefaultRetryPolicy{
		MaxDelay: expected,
	}

	if retryPolicy.MaxDelay != expected {
		t.Error(util.FormatTest("GetMaxDelay", strconv.Itoa(retryPolicy.MaxDelay), strconv.Itoa(expected)))
	}
}

func TestGetDelayBeforeNextRetry(t *testing.T) {
	maxErrorRetry := 3
	maxDelay := 20

	retryPolicy := DefaultRetryPolicy{
		MaxErrorRetry: maxErrorRetry,
		MaxDelay:      maxDelay,
	}

	delay := retryPolicy.GetDelayBeforeNextRetry(context.Background(), errors.New("Unknown Error"), 5)
	if delay != -1 {
		t.Error(util.FormatTest("GetDelayBeforeNextRetry", delay.String(), strconv.Itoa(-1)))
	}

	delay = retryPolicy.GetDelayBeforeNextRetry(context.Background(), &bceerror.Error{StatusCode: http.StatusInternalServerError}, 1)
	expected := (1 << 1) * 300 * time.Millisecond
	if delay != expected {
		t.Error(util.FormatTest("GetDelayBeforeNextRetry 1<<1", delay.String(), expected.String()))
	}

	delay = retryPolicy.GetDelayBeforeNextRetry(context.Background(), &bceerror.Error{StatusCode: http.StatusServiceUnavailable}, 2)
	expected = (1 << 2) * 300 * time.Millisecond
	if delay != expected {
		t.Error(util.FormatTest("GetDelayBeforeNextRetry 1<<2", delay.String(), expected.String()))
	}

	maxDelay = 1
	retryPolicy = DefaultRetryPolicy{
		MaxErrorRetry: maxErrorRetry,
		MaxDelay:      maxDelay,
	}

	delay = retryPolicy.GetDelayBeforeNextRetry(context.Background(), &bceerror.Error{StatusCode: http.StatusServiceUnavailable}, 2)
	expected = retryPolicy.getMaxDelay()

	if delay != expected {
		t.Error(util.FormatTest("GetDelayBeforeNextRetry", delay.String(), expected.String()))
	}
}
