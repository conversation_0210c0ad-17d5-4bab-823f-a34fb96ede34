package retrypolicy

import (
	"context"
	"net/http"
	"time"

	bceerror "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/error"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"
)

// DefaultRetryPolicy is the default implemention of interface bce.RetryPolicy.
type DefaultRetryPolicy struct {
	MaxErrorRetry int
	MaxDelay      int
}

// NewDefaultRetryPolicy 返回默认重试策略
func NewDefaultRetryPolicy(maxErrorRetry int, maxDelay int) RetryPolicy {
	return &DefaultRetryPolicy{
		MaxErrorRetry: maxErrorRetry,
		MaxDelay:      maxDelay,
	}
}

// GetMaxErrorRetry specifies the max retry count.
func (p *DefaultRetryPolicy) GetMaxErrorRetry() int {
	return p.MaxErrorRetry
}

// GetMaxDelay specifies the max delay time for retrying.
func (p *DefaultRetryPolicy) getMaxDelay() time.Duration {
	return time.Duration(p.MaxDelay) * time.Second
}

func (p *DefaultRetryPolicy) GetMaxDelayInSeconds() int {
	if p == nil {
		return 0
	}
	return p.MaxDelay
}

// GetDelayBeforeNextRetry specifies the delay time for next retry.
func (p *DefaultRetryPolicy) GetDelayBeforeNextRetry(ctx context.Context, err error, retriesAttempted int) time.Duration {
	if !p.shouldRetry(ctx, err, retriesAttempted) {
		return -1
	}

	// 2^retriesAttempted
	duration := (1 << uint(retriesAttempted)) * 300 * time.Millisecond

	if duration > p.getMaxDelay() {
		return p.getMaxDelay()
	}

	return duration
}

func (p *DefaultRetryPolicy) shouldRetry(ctx context.Context, err error, retriesAttempted int) bool {
	if retriesAttempted > p.GetMaxErrorRetry() {
		return false
	}

	if bceError, ok := err.(*bceerror.Error); ok {
		if bceError.StatusCode == http.StatusInternalServerError {
			logger.Warnf(ctx, "Retry for internal server error.")
			return true
		}

		if bceError.StatusCode == http.StatusServiceUnavailable {
			logger.Warnf(ctx, "Retry for service unavailable.")
			return true
		}
	} else {
		logger.Warnf(ctx, "Retry for unknow error: %s", err.Error())
		return true
	}

	return false
}
