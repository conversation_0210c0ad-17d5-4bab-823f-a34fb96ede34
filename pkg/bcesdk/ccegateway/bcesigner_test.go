package ccegateway

import (
	"testing"

	"github.com/baidubce/bce-sdk-go/auth"
	"github.com/baidubce/bce-sdk-go/services/bos"
	"gotest.tools/assert"
)

// For manual test, disabled in ut.
func testNewBCESigner(t *testing.T) {
	signer := NewBCESigner("gz", "cce-5mxt6iao")
	tokenGetter := func(*auth.BceCredentials, *auth.SignOptions) ([]byte, []byte, error) {
		return []byte("d52f7655-f8fc-4aef-91ac-10f603d82654"), []byte("1612340571"), nil
	}
	signer.SetTokenSource(tokenGetter)

	// 创建BOS服务的Client
	bosClient, err := bos.NewClient("ak", "sk", "gz.bcebos.com")
	if err != nil {
		t.Fatal("bos.NewClient err:", err)
	}

	bosClient.Signer = signer

	acl, err := bosClient.GetBucketAcl("bos-csi-test-gz-jpaas")
	if err != nil {
		t.<PERSON>al("GetBucketAcl err:", err)
	}
	t.Log("acl:", acl)
}

func TestBCESignerSign(t *testing.T) {
	signer := NewBCESigner("gz", "cce-5mxt6iao")
	// 创建BOS服务的Client
	bosClient, err := bos.NewClient("ak", "sk", "gz.bcebos.com")
	assert.NilError(t, err)

	bosClient.Signer = signer

	_, err = bosClient.GetBucketAcl("bos-csi-test-gz-jpaas")
	assert.Assert(t, err != nil)
}
