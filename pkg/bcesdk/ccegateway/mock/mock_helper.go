// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/ccegateway (interfaces: Helper)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	v1 "k8s.io/api/core/v1"
)

// MockHelper is a mock of Helper interface.
type MockHelper struct {
	ctrl     *gomock.Controller
	recorder *MockHelperMockRecorder
}

// MockHelperMockRecorder is the mock recorder for MockHelper.
type MockHelperMockRecorder struct {
	mock *MockHelper
}

// NewMockHelper creates a new mock instance.
func NewMockHelper(ctrl *gomock.Controller) *MockHelper {
	mock := &MockHelper{ctrl: ctrl}
	mock.recorder = &MockHelperMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockHelper) EXPECT() *MockHelperMockRecorder {
	return m.recorder
}

// GetHostAndPort mocks base method.
func (m *MockHelper) GetHostAndPort() (string, int) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHostAndPort")
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(int)
	return ret0, ret1
}

// GetHostAndPort indicates an expected call of GetHostAndPort.
func (mr *MockHelperMockRecorder) GetHostAndPort() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHostAndPort", reflect.TypeOf((*MockHelper)(nil).GetHostAndPort))
}

// NewSignOption mocks base method.
func (m *MockHelper) NewSignOption(arg0 context.Context, arg1 string) *bce.SignOption {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewSignOption", arg0, arg1)
	ret0, _ := ret[0].(*bce.SignOption)
	return ret0
}

// NewSignOption indicates an expected call of NewSignOption.
func (mr *MockHelperMockRecorder) NewSignOption(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewSignOption", reflect.TypeOf((*MockHelper)(nil).NewSignOption), arg0, arg1)
}

// NewSignOptionFromSecret mocks base method.
func (m *MockHelper) NewSignOptionFromSecret(arg0 context.Context, arg1 func(string, string) (*v1.Secret, error)) (*bce.SignOption, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewSignOptionFromSecret", arg0, arg1)
	ret0, _ := ret[0].(*bce.SignOption)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewSignOptionFromSecret indicates an expected call of NewSignOptionFromSecret.
func (mr *MockHelperMockRecorder) NewSignOptionFromSecret(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewSignOptionFromSecret", reflect.TypeOf((*MockHelper)(nil).NewSignOptionFromSecret), arg0, arg1)
}

// NewSignOptionFromVolume mocks base method.
func (m *MockHelper) NewSignOptionFromVolume(arg0 context.Context, arg1 string) (*bce.SignOption, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewSignOptionFromVolume", arg0, arg1)
	ret0, _ := ret[0].(*bce.SignOption)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewSignOptionFromVolume indicates an expected call of NewSignOptionFromVolume.
func (mr *MockHelperMockRecorder) NewSignOptionFromVolume(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewSignOptionFromVolume", reflect.TypeOf((*MockHelper)(nil).NewSignOptionFromVolume), arg0, arg1)
}
