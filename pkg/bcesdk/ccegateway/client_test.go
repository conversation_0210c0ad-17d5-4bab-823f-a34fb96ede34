package ccegateway

import (
	"net/http"
	"net/http/httptest"

	"github.com/gorilla/mux"
)

type testEnv struct {
	method  string
	path    string
	handler func(w http.ResponseWriter, r *http.Request)
}

func setupTestEnv(envs []*testEnv) *httptest.Server {
	r := mux.NewRouter()
	for _, env := range envs {
		r.HandleFunc(env.path, env.handler).Methods(env.method)
	}

	return httptest.NewServer(r)
}

func tearDownTestEnv(s *httptest.Server) {
	s.Close()
}
