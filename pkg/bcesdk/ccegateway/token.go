package ccegateway

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

func (c *client) EnsureTokenSecret(ctx context.Context, clusterID string, opt *bce.SignOption) (
	string, error) {
	if clusterID == "" {
		return "", errors.New("cluster id cannot be empty")
	}

	params := map[string]string{
		"clusterId": clusterID,
	}
	req, err := bce.NewRequest("POST", c.GetURL("/api/v1/token/ensure", params), nil)
	if err != nil {
		return "", err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return "", err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}

	return string(bodyContent), nil
}
