package ccegateway

import (
	"context"
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"
)

const (
	TokenKey     = "token"
	ExpiredAtKey = "expiredAt"

	TokenSecretName      = "cce-plugin-token"
	TokenSecretNamespace = "kube-system"

	TokenHeaderKey      = "cce-token"
	ClusterIDHeaderKey  = "cce-cluster"
	RemoteHostHeaderKey = "cce-remote-host"

	EndpointOverrideEnv = "CCE_GATEWAY_ENDPOINT"
)

var _ Helper = &helper{}

//go:generate mockgen -destination=./mock/mock_helper.go -package=mock icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/ccegateway Helper

type Helper interface {
	// GetHostAndPort gets host and port of cce gateway.
	// Typically used for setting ProxyHost and ProxyPort.
	GetHostAndPort() (string, int)
	// NewSignOption generates a new sign option from plugin token directly.
	NewSignOption(ctx context.Context, token string) *bce.SignOption
	// NewSignOptionFromVolume generates a new sign option from a volume.
	// Typically used when token secret is mounted as a volume.
	NewSignOptionFromVolume(ctx context.Context, dir string) (*bce.SignOption, error)
	// NewSignOptionFromSecret generates a new sign option from a secret which can be
	// fetched by a caller provided secret getter func.
	// Typically used when token secret can be directly accessed by caller.
	NewSignOptionFromSecret(ctx context.Context, secretGetter func(namespace, name string) (*corev1.Secret, error)) (*bce.SignOption, error)
}

type helper struct {
	Region    string
	ClusterID string

	Token     string
	ExpiredAt int64
}

func NewHelper(region, clusterID string) *helper {
	return &helper{
		Region:    region,
		ClusterID: clusterID,
	}
}

func (h *helper) GetHostAndPort() (string, int) {
	// default host and port
	host, port := "cce-gateway.bj.baidubce.com", 80
	if h.Region != "" {
		host = "cce-gateway." + h.Region + ".baidubce.com"
	}

	if strings.HasPrefix(h.Region, "qa") {
		if endpoint, ok := Endpoint[h.Region]; ok {
			host = endpoint
		}
	}

	if env := os.Getenv(EndpointOverrideEnv); env != "" {
		// use the endpoint explicitly set in env if exists
		hostPort := strings.Split(env, ":")
		host = hostPort[0]
		if len(hostPort) == 2 {
			if portNum, err := strconv.Atoi(hostPort[1]); err == nil {
				port = portNum
			}
		}
	}
	return host, port
}

func (h *helper) NewSignOptionFromVolume(ctx context.Context, dir string) (*bce.SignOption, error) {
	if h.Token == "" || time.Now().Unix() >= h.ExpiredAt {
		tokenFile := filepath.Join(dir, TokenKey)
		tokenBytes, err := ioutil.ReadFile(tokenFile)
		if err != nil {
			return nil, fmt.Errorf("fail to read %s: %w", tokenFile, err)
		}
		expiredAtFile := filepath.Join(dir, ExpiredAtKey)
		expiredAtBytes, err := ioutil.ReadFile(expiredAtFile)
		if err != nil {
			return nil, fmt.Errorf("fail to read %s: %w", expiredAtFile, err)
		}

		h.ExpiredAt, err = strconv.ParseInt(string(expiredAtBytes), 10, 64)
		if err != nil {
			return nil, fmt.Errorf("fail to parse expiredAt=%s: %w", string(expiredAtBytes), err)
		}
		h.Token = string(tokenBytes)
	}
	return h.NewSignOption(ctx, h.Token), nil
}

func (h *helper) NewSignOptionFromSecret(
	ctx context.Context,
	secretGetter func(namespace, name string) (*corev1.Secret, error)) (
	*bce.SignOption, error) {
	if h.Token == "" || time.Now().Unix() >= h.ExpiredAt {
		tokenSecret, err := secretGetter(TokenSecretNamespace, TokenSecretName)
		if err != nil {
			return nil, fmt.Errorf("fail to get secret %s/%s: %w",
				TokenSecretNamespace, TokenSecretName, err)
		}
		if tokenSecret == nil {
			return nil, errors.New("token secret is nil")
		}

		expiredAtBytes, ok := tokenSecret.Data[ExpiredAtKey]
		if !ok {
			return nil, fmt.Errorf("fail to find expiredAt key=%s in secret", ExpiredAtKey)
		}
		h.ExpiredAt, err = strconv.ParseInt(string(expiredAtBytes), 10, 64)
		if err != nil {
			return nil, fmt.Errorf("fail to parse expiredAt=%s: %w", string(expiredAtBytes), err)
		}

		tokenBytes, ok := tokenSecret.Data[TokenKey]
		if !ok {
			return nil, fmt.Errorf("fail to find token key=%s in secret", TokenKey)
		}
		h.Token = string(tokenBytes)
	}
	return h.NewSignOption(ctx, h.Token), nil
}

func (h *helper) NewSignOption(ctx context.Context, token string) *bce.SignOption {
	return &bce.SignOption{
		CustomSignFunc: func(ctx context.Context, req *bce.Request) {
			req.Header.Set(TokenHeaderKey, token)
			req.Header.Set(ClusterIDHeaderKey, h.ClusterID)
			// add default content-type if not set
			if req.Header.Get("Content-Type") == "" {
				req.Header.Set("Content-Type", "application/json")
			}
			// add x-bce-request-id if not set
			if req.Header.Get("x-bce-request-id") == "" {
				if id := logger.GetRequestID(ctx); id != "" {
					req.Header.Set("x-bce-request-id", id)
				} else {
					req.Header.Set("x-bce-request-id", logger.GetUUID())
				}
			}
			gatewayHost, _ := h.GetHostAndPort()
			// make SignFunc idempotent as it may be invoked many times under retry
			if req.Host != gatewayHost {
				req.Header.Set(RemoteHostHeaderKey, req.Host)
				req.Host = gatewayHost
			}
		},
	}
}
