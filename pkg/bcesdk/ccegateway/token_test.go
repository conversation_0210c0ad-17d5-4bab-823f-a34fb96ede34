package ccegateway

import (
	"context"
	"fmt"
	"net/http"
	"testing"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/util"
)

var testTokenYAML = `apiVersion: v1
data:
  expiredAt: NDA4MA==
  token: Zjk2YzY2NGEtOGNmOC1jNWY4LTg3MzAtMTczMTkyYTI3ZTQw
kind: Secret
metadata:
  creationTimestamp: null
  name: cce-plugin-token
  namespace: kube-system
`

func handleEnsureTokenSecret(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	clusterID := r.URL.Query().Get("clusterId")
	if clusterID == "" {
		w.WriteHeader(http.StatusBadRequest)
		_, _ = w.Write([]byte(fmt.Sprintf(`{"Code":"CCE.Gateway.BadRequest","Message":"invalid cluster id,"RequestID":%s}`, util.GetRequestID())))
		return
	}

	w.<PERSON>rite<PERSON>ead<PERSON>(http.StatusOK)
	_, _ = w.Write([]byte(testTokenYAML))
}

func TestClientEnsureTokenSecret(t *testing.T) {
	type args struct {
		ctx       context.Context
		clusterID string
		opt       *bce.SignOption
	}
	tests := []struct {
		name    string
		envs    []*testEnv
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "success case",
			envs: []*testEnv{
				{
					method:  "POST",
					path:    "/api/v1/token/ensure",
					handler: handleEnsureTokenSecret,
				},
			},
			args: args{
				ctx:       context.TODO(),
				clusterID: "c-f99UG69T",
				opt: &bce.SignOption{
					CustomSignFunc: func(context.Context, *bce.Request) { return },
				},
			},
			want:    testTokenYAML,
			wantErr: false,
		},
		{
			name: "empty clusterID case",
			envs: []*testEnv{
				{
					method:  "POST",
					path:    "/api/v1/token/ensure",
					handler: handleEnsureTokenSecret,
				},
			},
			args: args{
				ctx:       context.TODO(),
				clusterID: "",
				opt: &bce.SignOption{
					CustomSignFunc: func(context.Context, *bce.Request) { return },
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testServer := setupTestEnv(tt.envs)
			defer tearDownTestEnv(testServer)

			c := NewClient(NewConfig(
				&bce.Config{
					Endpoint: testServer.URL,
				}))
			c.SetDebug(true)
			got, err := c.EnsureTokenSecret(tt.args.ctx, tt.args.clusterID, tt.args.opt)
			if (err != nil) != tt.wantErr {
				t.Errorf("client.EnsureTokenSecret() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("client.EnsureTokenSecret() = %v, want %v", got, tt.want)
			}
		})
	}
}
