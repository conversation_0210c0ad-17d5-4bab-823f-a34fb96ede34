/*
modification history
--------------------
2024/12/23 14:20:00, by <EMAIL>, create
*/

package cceprobe

import (
	"context"
	"strconv"
	"testing"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/retrypolicy"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

var gzTestProbeConfig = bce.Config{
	Credentials: bce.NewCredentials("xxx", "xxx"),
	Region:      "gztest",
	Endpoint:    "http://10.164.32.142:8424/v2",
	Timeout:     30 * time.Second,
	RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
}

var client *Client
var ctx context.Context
var clusterID string

func init() {
	ctx = context.TODO()
	client = NewClient(&gzTestProbeConfig)
	clusterID = "cce-gnwwpryy"
}

func TestListInspectionReport(t *testing.T) {
	res, err := client.ListInspectionReport(ctx, clusterID, &ListInspectionReportParams{
		InspectionType:   InspectionTypeManual,
		InspectionStatus: InspectionStatusHasRisks,
		OrderBy:          InspectionOrderByStartTime,
		Order:            OrderASC,
		PageNo:           1,
		PageSize:         10,
	}, nil)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(utils.ToPrettyJSON(res))
}

func TestGetInspectionReport(t *testing.T) {
	latestRes, err := client.GetLatestInspectionReport(ctx, clusterID, nil)
	if err != nil {
		t.Fatal(err)
	}
	if latestRes == nil {
		t.Fatal("GetLatestInspectionReport nil")
	}
	taskId := latestRes.TaskId
	infoRes, err := client.GetInspectionReport(ctx, clusterID, taskId, nil)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(utils.ToPrettyJSON(infoRes))
	if infoRes == nil {
		t.Fatal("GetInspectionReport nil")
	}
}

func TestInspectionItems(t *testing.T) {
	res, err := client.GetInspectionItems(ctx, clusterID, nil)
	if err != nil {
		t.Fatal(err)
	}
	itemMap := make(map[int]bool)
	for k, v := range res {
		// 数字的key即将废弃，不需要
		_, toIntErr := strconv.ParseInt(k, 10, 64)
		if toIntErr == nil {
			continue
		}
		for _, item := range v {
			itemMap[item.ItemId] = item.Status
		}
	}
	if len(itemMap) == 0 {
		t.Fatal("inspection items empty")
	}
	itemMap[10001] = !itemMap[10001]
	err = client.UpdateInspectionItems(ctx, clusterID, itemMap, nil)
	if err != nil {
		t.Fatal(err)
	}
}

func TestInspectionPlan(t *testing.T) {
	reporterTypeRes, err := client.GetInspectionReporterType(ctx, clusterID, nil)
	if err != nil {
		t.Fatal(err)
	}
	if reporterTypeRes == nil || len(reporterTypeRes.ReporterTypeList) < 0 {
		t.Fatal("reporterType empty")
	}

	res, err := client.GetInspectionPlan(ctx, clusterID, nil)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(utils.ToPrettyJSON(res))
	updateRequest := res
	updateRequest.InspectionConfig = InspectionPlanConfig{
		InspectionFrequency: "30 8 * * *",
		IsInspectionEnabled: true,
	}
	updateRequest.SubscriptionConfig = SubscriptionPlanConfig{
		IsSubscriptionEnabled: true,
		ReceiveMethod: [][3]string{{
			string(ReporterTypeDingTalk),
			"https://oapi.dingtalk.com/robot/send?access_token=ae585333ebefac2a767c7e5655d741e0be71f8126932c706320ba9ce80b20062",
			"test dd",
		}},
		SubscriptionFrequency: "58 16 * * *",
	}
	err = client.UpdateInspectionPlan(ctx, clusterID, res, nil)
	if err != nil {
		t.Fatal(err)
	}
}
