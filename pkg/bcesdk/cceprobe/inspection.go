/*
modification history
--------------------
2024/12/23 14:20:00, by <EMAIL>, create
*/

package cceprobe

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/inspect"
)

// ListInspectionReport 获取集群巡检报告列表
func (c *Client) ListInspectionReport(ctx context.Context, clusterID string, args *ListInspectionReportParams,
	option *bce.SignOption) (*ListInspectionReportResponse, error) {
	if clusterID == "" {
		return nil, errors.New("empty clusterID")
	}

	params := map[string]string{}
	if args != nil {
		params = map[string]string{
			"inspectionType":   string(args.InspectionType),
			"inspectionStatus": string(args.InspectionStatus),
			"orderBy":          string(args.OrderBy),
			"order":            string(args.Order),
			"pageNo":           strconv.Itoa(args.PageNo),
			"pageSize":         strconv.Itoa(args.PageSize),
		}
	}

	req, err := bce.NewRequest(http.MethodGet, c.GetURL(fmt.Sprintf("cluster/%s/inspections", clusterID), params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取集群巡检报告列表",
		inspect.InspectLatency: "5000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListInspectionReportResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// GetLatestInspectionReport 获取集群巡检的最新报告
func (c *Client) GetLatestInspectionReport(ctx context.Context, clusterID string, option *bce.SignOption) (*GetInspectionReportResponse, error) {
	if clusterID == "" {
		return nil, errors.New("empty clusterID")
	}

	req, err := bce.NewRequest(http.MethodGet, c.GetURL(fmt.Sprintf("cluster/%s/inspection_latest_report", clusterID), nil), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取集群巡检的最新报告",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r GetInspectionReportResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// GetInspectionReport 获取集群巡检的报告
func (c *Client) GetInspectionReport(ctx context.Context, clusterID, taskID string, option *bce.SignOption) (*GetInspectionReportResponse, error) {
	if clusterID == "" {
		return nil, errors.New("empty clusterID")
	}
	if taskID == "" {
		return nil, errors.New("empty taskID")
	}

	req, err := bce.NewRequest(http.MethodGet, c.GetURL(fmt.Sprintf("cluster/%s/inspection/%s/report", clusterID, taskID), nil), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取集群巡检的报告",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r GetInspectionReportResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// GetInspectionItems 获取集群巡检项
func (c *Client) GetInspectionItems(ctx context.Context, clusterID string, option *bce.SignOption) (GetInspectionItemsResponse, error) {
	if clusterID == "" {
		return nil, errors.New("empty clusterID")
	}

	req, err := bce.NewRequest(http.MethodGet, c.GetURL(fmt.Sprintf("cluster/%s/inspection_items", clusterID), nil), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取集群巡检项",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r GetInspectionItemsResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return r, nil
}

// UpdateInspectionItems 更新巡检项状态
func (c *Client) UpdateInspectionItems(ctx context.Context, clusterID string, items map[int]bool, option *bce.SignOption) error {
	if clusterID == "" {
		return errors.New("empty clusterID")
	}
	if len(items) == 0 {
		return errors.New("empty items")
	}

	bodyBytes, err := json.Marshal(items)
	if err != nil {
		return err
	}
	req, err := bce.NewRequest(http.MethodPut, c.GetURL(fmt.Sprintf("cluster/%s/inspection_items", clusterID), nil), bytes.NewBuffer(bodyBytes))
	if err != nil {
		return err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "更新巡检项状态",
		inspect.InspectLatency: "2000",
	})

	_, err = c.SendRequest(ctx, req, option)
	return err
}

// IsInspectionRunning 判断是否有巡检中的任务
func (c *Client) IsInspectionRunning(ctx context.Context, clusterID string, option *bce.SignOption) (bool, error) {
	if clusterID == "" {
		return false, errors.New("empty clusterID")
	}

	req, err := bce.NewRequest(http.MethodGet, c.GetURL(fmt.Sprintf("cluster/%s/inspection_is_running", clusterID), nil), nil)
	if err != nil {
		return false, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "判断集群是否有巡检中的任务",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return false, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return false, err
	}

	isRunning, err := strconv.ParseBool(string(bodyContent))
	if err != nil {
		return false, err
	}

	return isRunning, nil
}

// CreateInspection 触发集群巡检
func (c *Client) CreateInspection(ctx context.Context, clusterID string, option *bce.SignOption) (*CreateInspectionResponse, error) {
	if clusterID == "" {
		return nil, errors.New("empty clusterID")
	}

	req, err := bce.NewRequest(http.MethodPost, c.GetURL(fmt.Sprintf("cluster/%s/inspection", clusterID), nil), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "触发集群巡检",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r CreateInspectionResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// GetInspectionPlan 获取集群自动巡检和订阅配置
func (c *Client) GetInspectionPlan(ctx context.Context, clusterID string, option *bce.SignOption) (*GetInspectionPlanResponse, error) {
	if clusterID == "" {
		return nil, errors.New("empty clusterID")
	}

	req, err := bce.NewRequest(http.MethodGet, c.GetURL(fmt.Sprintf("cluster/%s/inspection_plan", clusterID), nil), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取集群自动巡检和订阅配置",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r GetInspectionPlanResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// UpdateInspectionPlan 更新集群自动巡检和订阅配置
func (c *Client) UpdateInspectionPlan(ctx context.Context, clusterID string, args *UpdateInspectionRequest, option *bce.SignOption) error {
	if clusterID == "" {
		return errors.New("empty clusterID")
	}

	bodyBytes, err := json.Marshal(args)
	if err != nil {
		return err
	}
	req, err := bce.NewRequest(http.MethodPut, c.GetURL(fmt.Sprintf("cluster/%s/inspection_plan", clusterID), nil), bytes.NewBuffer(bodyBytes))
	if err != nil {
		return err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "更新集群自动巡检和订阅配置",
		inspect.InspectLatency: "2000",
	})

	_, err = c.SendRequest(ctx, req, option)
	return err
}

// GetInspectionReporterType 获取集群报告通知方式
func (c *Client) GetInspectionReporterType(ctx context.Context, clusterID string, option *bce.SignOption) (*GetInspectionReporterTypeResponse, error) {
	if clusterID == "" {
		return nil, errors.New("empty clusterID")
	}

	req, err := bce.NewRequest(http.MethodGet, c.GetURL(fmt.Sprintf("cluster/%s/inspection_reporter_type", clusterID), nil), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取集群报告通知方式",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r GetInspectionReporterTypeResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}
