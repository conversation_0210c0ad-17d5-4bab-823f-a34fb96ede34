/*
modification history
--------------------
2024/12/23 14:20:00, by <EMAIL>, create
*/

package cceprobe

import (
	"context"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

type Interface interface {
	SetDebug(debug bool)

	ListInspectionReport(ctx context.Context, clusterID string, params *ListInspectionReportParams, option *bce.SignOption) (*ListInspectionReportResponse, error)
	GetLatestInspectionReport(ctx context.Context, clusterID string, option *bce.SignOption) (*GetInspectionReportResponse, error)
	GetInspectionReport(ctx context.Context, clusterID, taskID string, option *bce.SignOption) (*GetInspectionReportResponse, error)
	GetInspectionItems(ctx context.Context, clusterID string, option *bce.SignOption) (GetInspectionItemsResponse, error)
	UpdateInspectionItems(ctx context.Context, clusterID string, items map[int]bool, option *bce.SignOption) error
	IsInspectionRunning(ctx context.Context, clusterID string, option *bce.SignOption) (bool, error)
	CreateInspection(ctx context.Context, clusterID string, option *bce.SignOption) (*CreateInspectionResponse, error)
	GetInspectionPlan(ctx context.Context, clusterID string, option *bce.SignOption) (*GetInspectionPlanResponse, error)
	UpdateInspectionPlan(ctx context.Context, clusterID string, args *UpdateInspectionRequest, option *bce.SignOption) error
	GetInspectionReporterType(ctx context.Context, clusterID string, option *bce.SignOption) (*GetInspectionReporterTypeResponse, error)
}

// Order 巡检查询排序
type Order string

const (
	OrderASC  Order = "asc"
	OrderDESC Order = "desc"
)

// InspectionOrderBy 巡检查询排序字段
type InspectionOrderBy string

const (
	// InspectionOrderByStartTime 创建时间
	InspectionOrderByStartTime InspectionOrderBy = "inspectStartTime"

	// InspectionOrderByEndTime 结束时间
	InspectionOrderByEndTime InspectionOrderBy = "inspectEndTime"
)

// InspectionType 巡检类型
type InspectionType string

const (
	// InspectionTypeManual 手动巡检
	InspectionTypeManual InspectionType = "manual"

	// InspectionTypeAutomatic 自动巡检
	InspectionTypeAutomatic InspectionType = "automatic"
)

// InspectionStatus 巡检检查状态
type InspectionStatus string

const (
	// InspectionStatusNormal 健康
	InspectionStatusNormal InspectionStatus = "normal"

	// InspectionStatusHasRisks 存在风险
	InspectionStatusHasRisks InspectionStatus = "hasRisks"

	// InspectionStatusInspectionFailed 巡检失败
	InspectionStatusInspectionFailed InspectionStatus = "inspectionFailed"

	// InspectionStatusInspecting 巡检中
	InspectionStatusInspecting InspectionStatus = "inspecting"
)

type ListInspectionReportParams struct {
	InspectionType   InspectionType    `json:"inspectionType"`
	InspectionStatus InspectionStatus  `json:"inspectionStatus"`
	OrderBy          InspectionOrderBy `json:"orderBy"`
	Order            Order             `json:"order"`
	PageNo           int               `json:"pageNo"`
	PageSize         int               `json:"pageSize"`
}

type ListInspectionReportResponse struct {
	Order          string                     `json:"order"`
	OrderBy        string                     `json:"orderBy"`
	PageNo         int                        `json:"pageNo"`
	PageSize       int                        `json:"pageSize"`
	ReportAbstract []InspectionReportAbstract `json:"reportAbstract"`
	TotalCount     int                        `json:"totalCount"`
}

type InspectionReportAbstract struct {
	InspectEndTime   time.Time `json:"inspectEndTime"`
	InspectStartTime time.Time `json:"inspectStartTime"`
	InspectionStatus string    `json:"inspectionStatus"`
	InspectionType   string    `json:"inspectionType"`
	TaskId           string    `json:"taskId"`
}

type CreateInspectionResponse struct {
	TaskId string `json:"taskId"`
}

type GetInspectionReportResponse struct {
	TaskId               string                                   `json:"taskId"`
	ReportItems          map[string]map[string]InspectionItemInfo `json:"reportItems"`
	ReportDeliveryStatus ReportDeliveryStatus                     `json:"reportDeliveryStatus"`
	InspectionItemsCount int                                      `json:"inspectionItemsCount"`
	InspectStartTime     time.Time                                `json:"inspectStartTime"`
}

type ReportDeliveryStatus struct {
	ReceiveMethod [][]string `json:"receiveMethod"`
}

type InspectionItemInfo struct {
	ItemId         int    `json:"itemId"`
	ItemNameZH     string `json:"itemNameZH"`
	Effect         string `json:"effect"`
	Grade          string `json:"grade"`
	Result         string `json:"result"`
	Suggestion     string `json:"suggestion"`
	ComposedResult string `json:"composedResult"`
}

type GetInspectionItemsResponse = map[string]map[string]InspectionItem

type InspectionItem struct {
	ItemId      int    `json:"itemId"`
	ItemNameZH  string `json:"itemNameZH"`
	Description string `json:"description"`
	Status      bool   `json:"status"`
}

type UpdateInspectionRequest = GetInspectionPlanResponse

type GetInspectionPlanResponse struct {
	InspectionConfig   InspectionPlanConfig   `json:"inspectionConfig"`
	SubscriptionConfig SubscriptionPlanConfig `json:"subscriptionConfig"`
}

type InspectionPlanConfig struct {
	InspectionFrequency string `json:"inspectionFrequency"`
	IsInspectionEnabled bool   `json:"isInspectionEnabled"`
}

type SubscriptionPlanConfig struct {
	IsSubscriptionEnabled bool        `json:"isSubscriptionEnabled"`
	ReceiveMethod         [][3]string `json:"receiveMethod"`
	SubscriptionFrequency string      `json:"subscriptionFrequency"`
}

type GetInspectionReporterTypeResponse struct {
	ReporterTypeList []InspectionReporterType `json:"reporterTypeList"`
}

type ReporterType string

const (
	// ReporterTypeDingTalk 钉钉
	ReporterTypeDingTalk ReporterType = "dingTalk"

	// ReporterTypeLark 飞书
	ReporterTypeLark ReporterType = "lark"

	// ReporterTypeInfoflow 如流
	ReporterTypeInfoflow ReporterType = "infoflow"

	// ReporterTypeWeCom 企业微信
	ReporterTypeWeCom ReporterType = "weCom"
)

type InspectionReporterType struct {
	ReportTypeName string       `json:"reportTypeName"`
	ReporterType   ReporterType `json:"reporterType"`
	UrlPrefix      string       `json:"urlPrefix"`
}
