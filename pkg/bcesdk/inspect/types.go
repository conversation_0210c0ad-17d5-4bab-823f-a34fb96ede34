/* types.go */
/*
modification history
--------------------
2024/9/12, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
*/

package inspect

import (
	"context"
	"net/http"
)

type Interface interface {
	PollingData(params *PollingDataParams) (*Data, error)
	PollingLatencyData(ctx context.Context, req *http.Request, errMsg string, latency float64) (*Data, error)
	GetPollingData(args *ListDataRequest) (*ListData, error)
	GetPollingDataKey(args *ListDataKeyRequest) (*ListDataKey, error)
	DeletePollingData(id int) error
}

type PollingDataParams struct {
	Product      Product      `json:"product"`
	Region       string       `json:"region"`
	Cluster      string       `json:"cluster"`
	PollingType  PollingType  `json:"polling_type"`
	Case         string       `json:"case"`
	CaseDesc     string       `json:"case_desc"`
	CreateTime   string       `json:"create_time"`
	UpdateTime   string       `json:"update_time"`
	Result       bool         `json:"result"`
	ErrorInfo    string       `json:"error_info"`
	CaseResults  []CaseResult `json:"case_results"`
	Debug        bool         `json:"debug"`
	TestCaseFall bool         `json:"test_case_fall"`
}

type Product string

const (
	CCE              Product = "cce"
	CCEApp           Product = "cce-app"
	CCECluster       Product = "cce-cluster"
	CCEInstance      Product = "cce-ins"
	CCEInstanceGroup Product = "cce-ig"
	CCEMonitor       Product = "cce-monitor"
)

type PollingType string

const (
	OpenApiType  PollingType = "openapi"
	DataflowType PollingType = "dataflow"
	OtherType    PollingType = "other"
)

type CaseResult struct {
	Name    string  `json:"name"`
	Desc    string  `json:"desc"`
	Value   float64 `json:"value"`
	Checker string  `json:"checker"`
	Limit   float64 `json:"limit"`
}

type PollingDataResponse struct {
	Status int    `json:"status"`
	Msg    string `json:"msg"`
	Data   Data   `json:"data"`
}

func (c *PollingDataResponse) IsOK() bool {
	return c.Status == 0
}

type Data struct {
	PollingID string `json:"polling_id"`
}

const (
	InspectDesc    string = "inspect-desc"
	InspectLatency string = "inspect-latency"
)

type ListDataRequest struct {
	Product     Product     `json:"product"`
	PollingID   string      `json:"polling_id"`
	PollingType PollingType `json:"polling_type"`
	Case        string      `json:"case"`
	IsMatch     *int        `json:"is_Match"`
	Page        int         `json:"page"`
	Result      *int        `json:"result"`
	TimeRange   string      `json:"time_range"`
}

type ListDataKeyRequest struct {
	Product     Product     `json:"product"`
	PollingType PollingType `json:"polling_type"`
	Key         string      `json:"key"`
}

type ListDataResponse struct {
	Status   int      `json:"status"`
	Msg      string   `json:"msg"`
	ListData ListData `json:"data"`
}

func (c *ListDataResponse) IsOK() bool {
	return c.Status == 0
}

type ListDataKeyResponse struct {
	Status      int         `json:"status"`
	Msg         string      `json:"msg"`
	ListDataKey ListDataKey `json:"data"`
}

func (c *ListDataKeyResponse) IsOK() bool {
	return c.Status == 0
}

type ListData struct {
	Count int           `json:"count"`
	Rows  []PollingData `json:"rows"`
	Total int           `json:"total"`
}

type ListDataKey struct {
	Options []map[string]string `json:"options"`
}

type PollingData struct {
	ID        int    `json:"id"`
	PollingID string `json:"polling_id"`
	Value     string `json:"value"`
	Region    string `json:"region"`
}

type TakingTimeCaseName string

const (
	CreateClusterCase              TakingTimeCaseName = "创建集群"
	CreateInstanceCase             TakingTimeCaseName = "为集群添加节点"
	CreateAIInfraClusterCase       TakingTimeCaseName = "创建ai-infra集群"
	CreateAIInfraBasicNetworkCase  TakingTimeCaseName = "Step1-创建基础网络配置"
	CheckAIInfraBasicNetworkCase   TakingTimeCaseName = "Step2-检查基础网络配置"
	CreateAIInfraCCEClusterCase    TakingTimeCaseName = "Step3-创建CCE集群"
	CreateAIInfraResourceGroupCase TakingTimeCaseName = "Step4-创建资源组"
	InstallAIInfraPluginCase       TakingTimeCaseName = "Step5-安装插件"
	RelateAIInfraMonitorCase       TakingTimeCaseName = "Step6-关联CProm监控"
	SyncAIInfraResourceGroupCase   TakingTimeCaseName = "Step7-同步资源组"
)
