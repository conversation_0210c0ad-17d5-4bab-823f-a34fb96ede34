/* client.go */
/*
modification history
--------------------
2024/9/12, by <PERSON><PERSON>, create
*/
/*
DESCRIPTION
*/

package inspect

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
)

const (
	APITimeout = 30 * time.Second
	Endpoint   = "http://************:8848"

	APIOnlinePollingData    = "/v1/online_polling_data"
	APIOnlinePollingDataKey = "/v1/get_online_polling_key_option"
	ContentType             = "application/json"
	LatencyName             = "latency"
	TakingTimeName          = "taking_time"
)

type Client struct {
	Client *http.Client
	Region string
}

func NewClient(region string) *Client {
	return &Client{
		Client: &http.Client{
			Timeout: APITimeout,
		},
		Region: region,
	}
}

// PollingLatencyData 根据关键信息上传时延巡检内容
func (c *Client) PollingLatencyData(ctx context.Context, req *http.Request, errMsg string, latency float64) (*Data, error) {

	bceRequestIDHeaderKey := "x-bce-request-id"
	bceRequestID := req.Header.Get(bceRequestIDHeaderKey)

	apiDescription := req.Header.Get(InspectDesc)
	expectedLatencyVal := req.Header.Get(InspectLatency)

	if apiDescription == "" || expectedLatencyVal == "" {
		return nil, errors.New("request do not has header of desc or latency, skip inspect data push")
	}

	expectedLatency, err := strconv.ParseFloat(expectedLatencyVal, 64)
	if err != nil {
		return nil, fmt.Errorf("expected latency string to float64 failed: %v", err)
	}

	errorInfo, isOk := func() (string, bool) {
		if errMsg == "" {
			return "", true
		}
		// 如果报错是404则不认为是失败
		if strings.Contains(errMsg, "404") || strings.Contains(errMsg, "AlreadyExist") || strings.Contains(errMsg, "InvalidParam") {
			return "", true
		}
		return errMsg, false
	}()

	params := PollingDataParams{
		Case:      apiDescription,
		Region:    c.Region,
		CaseDesc:  fmt.Sprintf("%s %s", req.Method, req.URL.String()),
		Result:    isOk,
		ErrorInfo: errorInfo,
		Debug:     false,
		CaseResults: []CaseResult{
			{
				Name:    LatencyName,
				Desc:    fmt.Sprintf("reqID: %s", bceRequestID),
				Value:   latency,
				Checker: "<",
				Limit:   expectedLatency,
			},
		},
	}

	return c.PollingData(&params)
}

func (c *Client) PollingData(params *PollingDataParams) (*Data, error) {
	if params.Region == "" {
		params.Region = "all"
	}

	if params.Product == "" {
		switch {
		case strings.Contains(params.CaseDesc, "8010"):
			params.Product = CCEApp
		case strings.Contains(params.CaseDesc, "8086"):
			params.Product = CCEMonitor
		case strings.Contains(params.CaseDesc, "instancegroup"):
			params.Product = CCEInstanceGroup
		case strings.Contains(params.CaseDesc, "instance"):
			params.Product = CCEInstance
		case strings.Contains(params.CaseDesc, "cluster"):
			params.Product = CCECluster
		default:
			params.Product = CCE
		}
	}

	if params.PollingType == "" {
		params.PollingType = OpenApiType
	}

	params.CreateTime = time.Now().Format(time.DateTime)

	// 未了防止 HTML 字符被转义
	// json.Marshal 函数默认会将 ‘<’ 等特殊字符转义为 Unicode 字符来防止可能的跨站脚本攻击（XSS）
	buffer := &bytes.Buffer{}
	encoder := json.NewEncoder(buffer)
	encoder.SetEscapeHTML(false)
	encoder.Encode(params)

	baseUrl := fmt.Sprintf("%s%s", Endpoint, APIOnlinePollingData)
	resp, err := c.Client.Post(baseUrl, ContentType, buffer)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode >= http.StatusBadRequest {
		return nil, fmt.Errorf("resp status code %d is not as excepted", resp.StatusCode)
	}

	defer resp.Body.Close()

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var response PollingDataResponse
	err = json.Unmarshal(responseBody, &response)
	if err != nil {
		return nil, err
	}

	if !response.IsOK() {
		return nil, fmt.Errorf("polling data failed: %v", response.Msg)
	}

	return &response.Data, nil
}

func (c *Client) GetPollingData(args *ListDataRequest) (*ListData, error) {
	if args == nil {
		return nil, errors.New("list data request is empty")
	}

	params := url.Values{}
	if args.Product != "" {
		params.Set("product", string(args.Product))
	}
	if args.PollingID != "" {
		params.Set("polling_id", args.PollingID)
	}
	if args.PollingType != "" {
		params.Set("polling_type", string(args.PollingType))
	}
	if args.Page != 0 {
		params.Set("page", strconv.Itoa(args.Page))
	}
	if args.Case != "" {
		params.Set("case", args.Case)
	}
	if args.TimeRange != "" {
		params.Set("time_range", args.TimeRange)
	}
	if args.IsMatch != nil {
		params.Set("is_match", strconv.Itoa(*args.IsMatch))
	}
	if args.Result != nil {
		params.Set("result", strconv.Itoa(*args.Result))
	}

	baseUrl := fmt.Sprintf("%s%s", Endpoint, APIOnlinePollingData)
	resp, err := c.Client.Get(baseUrl + "?" + params.Encode())
	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var response ListDataResponse
	err = json.Unmarshal(responseBody, &response)
	if err != nil {
		return nil, err
	}

	if !response.IsOK() {
		return nil, fmt.Errorf("get polling data failed: %v", response.Msg)
	}
	return &response.ListData, nil
}

func (c *Client) DeletePollingData(id int) error {
	baseUrl := fmt.Sprintf("%s%s/%d", Endpoint, APIOnlinePollingData, id)
	req, err := http.NewRequest("DELETE", baseUrl, nil)
	if err != nil {
		return err
	}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return err
	}

	defer resp.Body.Close()

	_, err = io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) GetPollingDataKey(args *ListDataKeyRequest) (*ListDataKey, error) {
	if args == nil {
		return nil, errors.New("list data key request is empty")
	}

	params := url.Values{}
	if args.Product != "" {
		params.Set("product", string(args.Product))
	}

	if args.PollingType != "" {
		params.Set("polling_type", string(args.PollingType))
	}
	if args.Key != "" {
		params.Set("key", args.Key)
	}

	baseUrl := fmt.Sprintf("%s%s", Endpoint, APIOnlinePollingDataKey)
	resp, err := c.Client.Get(baseUrl + "?" + params.Encode())
	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var response ListDataKeyResponse
	err = json.Unmarshal(responseBody, &response)
	if err != nil {
		return nil, err
	}

	if !response.IsOK() {
		return nil, fmt.Errorf("get polling data failed: %v", response.Msg)
	}
	return &response.ListDataKey, nil
}
