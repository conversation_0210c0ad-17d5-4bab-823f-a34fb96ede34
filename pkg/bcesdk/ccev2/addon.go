package ccev2

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/inspect"
)

// ListAddOns 查询集群中的组件列表
func (c *Client) ListAddOns(ctx context.Context, clusterID string, args *ListParams, option *bce.SignOption) (*ListAddOnResponse, error) {
	if clusterID == "" {
		return nil, errors.New("empty clusterID")
	}
	if args == nil {
		return nil, errors.New("args is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	if args != nil {
		params["addons"] = args.TargetAddons
	}

	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("cluster/%s/addon", clusterID), params), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取组件的基本信息与状态",
		inspect.InspectLatency: "15000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListAddOnResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// InstallAddon 向集群中安装组件
func (c *Client) InstallAddon(ctx context.Context, clusterID string, args *InstallParams, option *bce.SignOption) (*InstallAddOnResponse, error) {
	if clusterID == "" {
		return nil, errors.New("empty clusterID")
	}
	if args == nil {
		return nil, errors.New("args is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL(fmt.Sprintf("cluster/%s/addon", clusterID), params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "向集群中安装组件",
		inspect.InspectLatency: "10000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r InstallAddOnResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// UninstallAddon 向集群中安装组件
func (c *Client) UninstallAddon(ctx context.Context, clusterID string, args *UninstallParams, option *bce.SignOption) (*UninstallAddOnResponse, error) {
	if clusterID == "" {
		return nil, errors.New("empty clusterID")
	}
	if args == nil {
		return nil, errors.New("args is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("DELETE", c.GetURL(fmt.Sprintf("cluster/%s/addon", clusterID), params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "卸载集群中已经安装的组件",
		inspect.InspectLatency: "10000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r UninstallAddOnResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// UpdateAddon 向集群中安装组件
func (c *Client) UpdateAddon(ctx context.Context, clusterID string, args *UpdateParams, option *bce.SignOption) (*UpdateAddOnResponse, error) {
	if clusterID == "" {
		return nil, errors.New("empty clusterID")
	}
	if args == nil {
		return nil, errors.New("args is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("PUT", c.GetURL(fmt.Sprintf("cluster/%s/addon", clusterID), params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "更新集群中已经安装的组件的部署参数",
		inspect.InspectLatency: "10000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r UpdateAddOnResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

func (c *Client) UpgradeAddon(ctx context.Context, clusterID string, args *UpgradeParams, option *bce.SignOption) (*UpdateAddOnResponse, error) {
	if clusterID == "" {
		return nil, errors.New("empty clusterID")
	}
	if args == nil {
		return nil, errors.New("args is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL(fmt.Sprintf("cluster/%s/addon/upgrade", clusterID), params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "升级集群中已经安装的组件的版本",
		inspect.InspectLatency: "10000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r UpdateAddOnResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

func (c *Client) EnableLogAddon(ctx context.Context, clusterID string, args *LoggingEnableParams, option *bce.SignOption) (*LoggingAddOnResponse, error) {
	if clusterID == "" {
		return nil, errors.New("empty clusterID")
	}
	if args == nil {
		return nil, errors.New("args is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL(fmt.Sprintf("cluster/%s/addon/enablelog", clusterID), params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "集群开启组件日志",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r LoggingAddOnResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}
