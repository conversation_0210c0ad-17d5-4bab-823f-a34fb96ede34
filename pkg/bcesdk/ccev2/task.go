/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  task
 * @Version: 1.0.0
 * @Date: 2021/6/24 11:11 上午
 */
package ccev2

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/inspect"
)

// GetTask - 获取指定的Task
// PARAMS:
//   - ctx: The context to trace request
//   - taskType: Task 类型
//   - taskID: Task ID
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	*GetTaskResp: 获取 Task 结果
//	error: nil if succeed, error if fail
func (c *Client) GetTask(ctx context.Context, taskType, taskID string, option *bce.SignOption) (*GetTaskResp, error) {
	if taskType == "" {
		return nil, errors.New("empty taskType")
	}

	if taskID == "" {
		return nil, errors.New("empty taskID")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("task/%s/%s", taskType, taskID), params), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取任务列表",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r GetTaskResp
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// ListTasks - 获取 Task 列表
// PARAMS:
//   - ctx: The context to trace request
//   - taskType: Task 类型
//   - listOption: 列表筛选参数
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	*ListTaskResp: 获取 Task 列表结果
//	error: nil if succeed, error if fail
func (c *Client) ListTasks(ctx context.Context, taskType string, listOption ListTaskOption, signOption *bce.SignOption) (*ListTaskResp, error) {
	if taskType == "" {
		return nil, errors.New("empty taskType")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	if listOption.TargetID != "" {
		params["targetID"] = listOption.TargetID
	}
	if listOption.PageNo > 0 && listOption.PageSize > 0 {
		params["pageNo"] = fmt.Sprintf("%d", listOption.PageNo)
		params["pageSize"] = fmt.Sprintf("%d", listOption.PageSize)
	}

	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("tasks/%s", taskType), params), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取任务列表",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, signOption)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListTaskResp
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}
