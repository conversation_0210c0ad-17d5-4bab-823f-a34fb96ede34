package ccev2

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/addon"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

var _ Interface = &FakeClient{}

// FakeClient for unit test
type FakeClient struct {
	ClusterMap map[string]*Cluster
}

func (f *FakeClient) GetClusterCRD(ctx context.Context, clusterID string, option *bce.SignOption) (*GetClusterCRDResponse, error) {
	return nil, nil
}

func (f *FakeClient) GetInstanceCRD(ctx context.Context, clusterID, cceInstanceID string, option *bce.SignOption) (*GetInstanceCRDResponse, error) {
	return nil, nil
}

func (c *FakeClient) EnableInstanceScaleDown(ctx context.Context, clusterID string, args *EnableInstanceScaleDownRequest, option *bce.SignOption) (*EnableInstanceScaleDownResponse, error) {
	return nil, nil
}

// NewFakeClient for AppBLB fake client
func NewFakeClient() *FakeClient {
	return &FakeClient{
		ClusterMap: map[string]*Cluster{},
	}
}

func (f *FakeClient) SetDebug(debug bool) {
	return
}

func (f *FakeClient) CreateCluster(ctx context.Context, args *CreateClusterRequest, option *bce.SignOption) (*CreateClusterResponse, error) {
	if args == nil {
		return nil, errors.New("CreateCluster failed: args is nil")
	}

	cluster := &Cluster{}
	cluster.Spec = &ClusterSpec{}
	cluster.Spec.VPCID = args.Cluster.VPCID
	cluster.Spec.ContainerNetworkConfig = args.Cluster.ContainerNetworkConfig

	// Generate ClusterID
	for {
		clusterID := util.GenerateBCEShortID("cce-")
		if _, ok := f.ClusterMap[clusterID]; !ok {
			cluster.Spec.ClusterID = clusterID
			f.ClusterMap[clusterID] = cluster
			break
		}
	}
	return &CreateClusterResponse{
		ClusterID: cluster.Spec.ClusterID,
		RequestID: "123456",
	}, nil
}

func (f *FakeClient) UpdateCluster(ctx context.Context, clusterID string, args *ccetypes.ClusterSpec,
	option *bce.SignOption) (*UpdateClusterResponse, error) {
	return nil, nil
}

func (f *FakeClient) GetCluster(ctx context.Context, clusterID string, option *bce.SignOption) (*GetClusterResponse, error) {
	resp := &GetClusterResponse{}
	if cluster, exist := f.ClusterMap[clusterID]; exist {
		resp.Cluster = cluster
	}
	resp.RequestID = "123456"
	return resp, nil
}

func (f *FakeClient) DeleteCluster(ctx context.Context, clusterID string, deleteOpts *DeleteOptions,
	option *bce.SignOption) (*DeleteClusterResponse, error) {
	return nil, nil
}

func (f *FakeClient) ListClusters(ctx context.Context, keywordType ClusterKeywordType, keyword string,
	orderBy ClusterOrderBy, order Order, pageNum, pageSize int, option *bce.SignOption) (*ListClustersResponse, error) {
	return nil, nil
}

func (f *FakeClient) CreateInstances(ctx context.Context, clusterID string, args []*InstanceSet,
	option *bce.SignOption) (*CreateInstancesResponse, error) {
	return nil, nil
}

func (f *FakeClient) UpdateInstance(ctx context.Context, clusterID string, instanceID string,
	spec *ccetypes.InstanceSpec, option *bce.SignOption) (*UpdateInstancesResponse, error) {
	return nil, nil
}

func (f *FakeClient) GetInstance(ctx context.Context, clusterID, instanceID string, option *bce.SignOption) (*GetInstanceResponse, error) {
	return nil, nil
}

func (f *FakeClient) GetInstanceByNodeName(ctx context.Context, clusterID string, nodeName string, option *bce.SignOption) (*GetInstanceByNodeNameResponse, error) {
	return nil, nil
}

func (f *FakeClient) DeleteInstances(ctx context.Context, clusterID string, args *DeleteInstancesRequest,
	option *bce.SignOption) (*DeleteInstancesResponse, error) {
	return nil, nil
}

func (f *FakeClient) DrainNodes(ctx context.Context, args *DrainNodesRequest,
	option *bce.SignOption) (*DrainNodesResponse, error) {
	return nil, nil
}

func (f *FakeClient) ListInstancesByPage(ctx context.Context, clusterID string, args *ListInstancesByPageParams,
	option *bce.SignOption) (*ListInstancesResponse, error) {
	return nil, nil
}

func (f *FakeClient) ListInstanceGroupInstancesByParams(ctx context.Context, clusterID, instanceGroupID string, args *ListInstancesByPageParams,
	option *bce.SignOption) (*ListInstancesGroupInstanceResponse, error) {
	return nil, nil
}

func (f *FakeClient) ListInstancesByInstanceGroupID(ctx context.Context, clusterID string,
	instanceGroupID string, pageNo, pageSize int, option *bce.SignOption) (*ListInstancesByInstanceGroupIDResponse, error) {
	return nil, nil
}

func (f *FakeClient) ResetClusterRetryCount(ctx context.Context, clusterID string, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) ResetInstanceRetryCount(ctx context.Context, clusterID, cceInstanceID string, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) GetClusterQuota(ctx context.Context, option *bce.SignOption) (*GetQuotaResponse, error) {
	return nil, nil
}

func (f *FakeClient) GetClusterNodeQuota(ctx context.Context, clusterID string, option *bce.SignOption) (*GetQuotaResponse, error) {
	return nil, nil
}

func (f *FakeClient) GetAdminKubeConfig(ctx context.Context, clusterID string, kubeConfigType models.KubeConfigType,
	option *bce.SignOption) (*GetKubeConfigResponse, error) {
	return nil, nil
}

func (f *FakeClient) CreateRBACKubeConfig(ctx context.Context, request *KubeConfigRequest, option *bce.SignOption) (*CreateRBACResponse, error) {
	return nil, nil
}

func (f *FakeClient) RenewRBACKubeConfig(ctx context.Context, request *KubeConfigRequest, option *bce.SignOption) (*RenewRBACResponse, error) {
	return nil, nil
}

func (f *FakeClient) CheckClusterIPCIDR(ctx context.Context, args *CheckClusterIPCIDRequest,
	option *bce.SignOption) (*CheckClusterIPCIDRResponse, error) {
	return nil, nil
}

func (f *FakeClient) CheckContainerNetworkCIDR(ctx context.Context, args *CheckContainerNetworkCIDRRequest,
	option *bce.SignOption) (*CheckContainerNetworkCIDRResponse, error) {
	return nil, nil
}

func (f *FakeClient) RecommendContainerCIDR(ctx context.Context, args *RecommendContainerCIDRRequest,
	option *bce.SignOption) (*RecommendContainerCIDRResponse, error) {
	return nil, nil
}

func (f *FakeClient) RecommendClusterIPCIDR(ctx context.Context, args *RecommendClusterIPCIDRRequest,
	option *bce.SignOption) (*RecommendClusterIPCIDRResponse, error) {
	return nil, nil
}

func (f *FakeClient) CreateInstanceGroup(ctx context.Context, clusterID string, request *CreateInstanceGroupRequest, option *bce.SignOption) (*CreateInstanceGroupResponse, error) {
	return nil, nil
}

func (f *FakeClient) ListInstanceGroups(ctx context.Context, clusterID string, listOption *InstanceGroupListOption, option *bce.SignOption) (*ListInstanceGroupResponse, error) {
	return nil, nil
}

func (f *FakeClient) GetInstanceGroup(ctx context.Context, clusterID, instanceGroupID string, option *bce.SignOption) (*GetInstanceGroupResponse, error) {
	return nil, nil
}

func (f *FakeClient) UpdateInstanceGroupReplicas(ctx context.Context, clusterID, instanceGroupID string,
	request *UpdateInstanceGroupReplicasRequest, option *bce.SignOption) (*UpdateInstanceGroupReplicasResponse, error) {
	return nil, nil
}

func (f *FakeClient) UpdateInstanceGroupClusterAutoscalerSpec(ctx context.Context, clusterID, instanceGroupID string,
	request *ClusterAutoscalerSpec, option *bce.SignOption) (*UpdateInstanceGroupClusterAutoscalerSpecResponse, error) {
	return nil, nil
}

func (f *FakeClient) UpdateInstanceGroupInstanceTemplate(ctx context.Context, clusterID, instanceGroupID string,
	request *UpdateInstanceGroupInstanceTemplateRequest, option *bce.SignOption) (*UpdateInstanceGroupInstanceTemplateResponse, error) {
	return nil, nil
}

func (f *FakeClient) UpdateInstanceGroupPausedStatus(ctx context.Context, clusterID, instanceGroupID string,
	request PauseDetail, option *bce.SignOption) (*UpdateInstanceGroupPausedStatusResponse, error) {
	return nil, nil
}

func (f *FakeClient) UpdateInstanceGroupConfig(ctx context.Context, clusterID, instanceGroupID string,
	request *UpdateInstanceGroupRequest, option *bce.SignOption) (*UpdateInstanceGroupResponse, error) {
	return nil, nil
}

func (f *FakeClient) UpdateInstanceScaleDownDisabled(ctx context.Context, clusterID string, args *UpdateNodeScaleDownRequest) (*UpdateNodeScaleDownResponse, error) {
	return nil, nil
}

func (f *FakeClient) DeleteInstanceGroup(ctx context.Context, clusterID, instanceGroupID string, deleteInstances bool,
	option *bce.SignOption) (*DeleteInstanceGroupResponse, error) {
	return nil, nil
}

// 提供 BBE 使用, 不对外暴露
func (f *FakeClient) GetClusterOfBBE(ctx context.Context, clusterID string, option *bce.SignOption) (*GetClusterOfBBEResponse, error) {
	return nil, nil
}

func (f *FakeClient) GetAutoScalerConfig(ctx context.Context, clusterID string, option *bce.SignOption) (*GetAutoscalerResponse, error) {
	return nil, nil
}

func (f *FakeClient) CreateAutoScalerConfig(ctx context.Context, clusterID string, option *bce.SignOption) (*CommonResponse, error) {
	return nil, nil
}

func (f *FakeClient) UpdateAutoScalerConfig(ctx context.Context, clusterID string, args *addon.ClusterAutoscalerConfig, option *bce.SignOption) (*CommonResponse, error) {
	return nil, nil
}

func (f *FakeClient) GetClusterExtraInfo(ctx context.Context, clusterID string, option *bce.SignOption) (*ClusterExtraInfo, error) {
	return nil, nil
}

func (f *FakeClient) ConfigureClusterEIP(ctx context.Context, clusterID string, args *ConfigureClusterEIPRequest,
	option *bce.SignOption) (*ConfigureClusterEIPResponse, error) {
	return nil, nil
}

func (f *FakeClient) UpdateClusterForbidDeleteConfig(ctx context.Context, clusterID string,
	args *UpdateClusterForbidDeleteRequest, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) InstallPlugin(ctx context.Context, clusterID, pluginName string, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) CordonInstances(ctx context.Context, cordonReq *CordonNodesRequest, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) CreateScaleUpInstanceGroupTask(ctx context.Context, clusterID, instanceGroupID string,
	targetReplicas int, option *bce.SignOption) (*CreateTaskResp, error) {
	return &CreateTaskResp{}, nil
}

func (f *FakeClient) CreateScaleDownInstanceGroupByCleanPolicy(ctx context.Context, clusterID, instanceGroupID string,
	instancesToBeRemoved []string, cleanPolicy CleanPolicy, deleteOption *ccetypes.DeleteOption,
	option *bce.SignOption) (*CreateTaskResp, error) {
	return &CreateTaskResp{}, nil
}

func (f *FakeClient) AttachInstancesToInstanceGroup(ctx context.Context, clusterID string, instanceGroupID string,
	request *AttachInstancesToInstanceGroupRequest, option *bce.SignOption) (*AttachInstancesToInstanceGroupResponse, error) {
	return &AttachInstancesToInstanceGroupResponse{}, nil
}

func (f *FakeClient) CreateScaleDownInstanceGroupTask(ctx context.Context, clusterID, instanceGroupID string,
	instancesToBeRemoved []string, option *bce.SignOption) (*CreateTaskResp, error) {
	return &CreateTaskResp{}, nil
}

func (f *FakeClient) GetTask(ctx context.Context, taskType, taskID string, option *bce.SignOption) (*GetTaskResp, error) {
	return &GetTaskResp{}, nil
}

func (f *FakeClient) ListTasks(ctx context.Context, taskType string, listOption ListTaskOption, signOption *bce.SignOption) (*ListTaskResp, error) {
	return &ListTaskResp{}, nil
}

func (f *FakeClient) SyncInstance(ctx context.Context, clusterID string, option *bce.SignOption) (*SyncInstancesResponse, error) {
	return &SyncInstancesResponse{}, nil
}

func (f *FakeClient) CreateWorkflow(ctx context.Context, clusterID string, args *CreateWorkflowRequest, option *bce.SignOption) (*CreateWorkflowResponse, error) {
	return nil, nil
}

func (f *FakeClient) ListWorkflows(ctx context.Context, clusterID string, option *bce.SignOption) (*ListWorkflowsResponse, error) {
	return nil, nil
}

func (f *FakeClient) GetWorkflow(ctx context.Context, clusterID, workflowID string, option *bce.SignOption) (*GetWorkflowResponse, error) {
	return nil, nil
}

func (f *FakeClient) DeleteWorkflow(ctx context.Context, clusterID, workflowID string, option *bce.SignOption) (*DeleteWorkflowResponse, error) {
	return nil, nil
}

func (f *FakeClient) UpdateWorkflow(ctx context.Context, clusterID, workflowID string, request *UpdateWorkflowRequest, option *bce.SignOption) (*UpdateWorkflowResponse, error) {
	return nil, nil
}

func (f *FakeClient) TargetK8SVersion(ctx context.Context, clusterID string, clusterRole ccetypes.ClusterRole, option *bce.SignOption) (*TargetK8SVersionResponse, error) {
	return nil, nil
}

func (f *FakeClient) ListNodesCanBeUpgradedByPage(ctx context.Context, clusterID string, option *bce.SignOption) (*ListNodesCanBeUpgradedByPageResponse, error) {
	return nil, nil
}

func (f *FakeClient) GetClusterEventSteps(ctx context.Context, clusterID string, option *bce.SignOption) (*GetEventStepsResponse, error) {
	return nil, nil
}

func (f *FakeClient) GetInstanceEventSteps(ctx context.Context, cceInstanceID string, option *bce.SignOption) (*GetEventStepsResponse, error) {
	return nil, nil
}

func (f *FakeClient) ListAddOns(ctx context.Context, clusterID string, args *ListParams, option *bce.SignOption) (*ListAddOnResponse, error) {
	return nil, nil
}

func (f *FakeClient) InstallAddon(ctx context.Context, clusterID string, args *InstallParams, option *bce.SignOption) (*InstallAddOnResponse, error) {
	return nil, nil
}

func (f *FakeClient) UninstallAddon(ctx context.Context, clusterID string, args *UninstallParams, option *bce.SignOption) (*UninstallAddOnResponse, error) {
	return nil, nil
}

func (f *FakeClient) UpdateAddon(ctx context.Context, clusterID string, args *UpdateParams, option *bce.SignOption) (*UpdateAddOnResponse, error) {
	return nil, nil
}

func (f *FakeClient) UpgradeAddon(ctx context.Context, clusterID string, args *UpgradeParams, option *bce.SignOption) (*UpdateAddOnResponse, error) {
	return nil, nil
}

func (f *FakeClient) EnableLogAddon(ctx context.Context, clusterID string, args *LoggingEnableParams, option *bce.SignOption) (*LoggingAddOnResponse, error) {
	return nil, nil
}

func (f *FakeClient) GetBindingCpromInstances(ctx context.Context, args *ListBindingCPromInstanceResquest, option *bce.SignOption) (*ListBindingCPromInstanceResponse, error) {
	return nil, nil
}

func (f *FakeClient) ListLogStores(ctx context.Context, clusterID, logStoreType string, option *bce.SignOption) (*ListLogStoreResponse, error) {
	return nil, nil
}

func (f *FakeClient) ToggleClusterBCM(ctx context.Context, clusterID string, enable bool, option *bce.SignOption) (*BCMResponse, error) {
	return nil, nil
}

func (f *FakeClient) GetInstanceGroupComponentsUpgradeVersions(ctx context.Context, clusterID string, instanceGroupID string, option *bce.SignOption) (*GetComponentsUpgradeVersionResponse, error) {
	return nil, nil
}
