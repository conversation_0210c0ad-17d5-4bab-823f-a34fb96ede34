// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/baiduFile/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccev2/types.go

// Package mock_ccev2 is a generated GoMock package.
package mock_ccev2

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	models "icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	ccev2 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccev2"
	addon "icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/addon"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// CheckClusterIPCIDR mocks base method.
func (m *MockInterface) CheckClusterIPCIDR(ctx context.Context, args *ccev2.CheckClusterIPCIDRequest, option *bce.SignOption) (*ccev2.CheckClusterIPCIDRResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckClusterIPCIDR", ctx, args, option)
	ret0, _ := ret[0].(*ccev2.CheckClusterIPCIDRResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckClusterIPCIDR indicates an expected call of CheckContainerNetworkCIDR.
func (mr *MockInterfaceMockRecorder) CheckClusterIPCIDR(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckClusterIPCIDR", reflect.TypeOf((*MockInterface)(nil).CheckClusterIPCIDR), ctx, args, option)
}

// CheckContainerNetworkCIDR mocks base method.
func (m *MockInterface) CheckContainerNetworkCIDR(ctx context.Context, args *ccev2.CheckContainerNetworkCIDRRequest, option *bce.SignOption) (*ccev2.CheckContainerNetworkCIDRResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckContainerNetworkCIDR", ctx, args, option)
	ret0, _ := ret[0].(*ccev2.CheckContainerNetworkCIDRResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckContainerNetworkCIDR indicates an expected call of CheckContainerNetworkCIDR.
func (mr *MockInterfaceMockRecorder) CheckContainerNetworkCIDR(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckContainerNetworkCIDR", reflect.TypeOf((*MockInterface)(nil).CheckContainerNetworkCIDR), ctx, args, option)
}

// CordonInstances mocks base method.
func (m *MockInterface) CordonInstances(ctx context.Context, cordonReq *ccev2.CordonNodesRequest, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CordonInstances", ctx, cordonReq, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CordonInstances indicates an expected call of CordonInstances.
func (mr *MockInterfaceMockRecorder) CordonInstances(ctx, cordonReq, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CordonInstances", reflect.TypeOf((*MockInterface)(nil).CordonInstances), ctx, cordonReq, option)
}

// CreateAutoScalerConfig mocks base method.
func (m *MockInterface) CreateAutoScalerConfig(ctx context.Context, clusterID string, option *bce.SignOption) (*ccev2.CommonResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAutoScalerConfig", ctx, clusterID, option)
	ret0, _ := ret[0].(*ccev2.CommonResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAutoScalerConfig indicates an expected call of CreateAutoScalerConfig.
func (mr *MockInterfaceMockRecorder) CreateAutoScalerConfig(ctx, clusterID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAutoScalerConfig", reflect.TypeOf((*MockInterface)(nil).CreateAutoScalerConfig), ctx, clusterID, option)
}

// CreateCluster mocks base method.
func (m *MockInterface) CreateCluster(ctx context.Context, args *ccev2.CreateClusterRequest, option *bce.SignOption) (*ccev2.CreateClusterResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCluster", ctx, args, option)
	ret0, _ := ret[0].(*ccev2.CreateClusterResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCluster indicates an expected call of CreateCluster.
func (mr *MockInterfaceMockRecorder) CreateCluster(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCluster", reflect.TypeOf((*MockInterface)(nil).CreateCluster), ctx, args, option)
}

// CreateInstanceGroup mocks base method.
func (m *MockInterface) CreateInstanceGroup(ctx context.Context, clusterID string, request *ccev2.CreateInstanceGroupRequest, option *bce.SignOption) (*ccev2.CreateInstanceGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstanceGroup", ctx, clusterID, request, option)
	ret0, _ := ret[0].(*ccev2.CreateInstanceGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInstanceGroup indicates an expected call of CreateInstanceGroup.
func (mr *MockInterfaceMockRecorder) CreateInstanceGroup(ctx, clusterID, request, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstanceGroup", reflect.TypeOf((*MockInterface)(nil).CreateInstanceGroup), ctx, clusterID, request, option)
}

// CreateInstances mocks base method.
func (m *MockInterface) CreateInstances(ctx context.Context, clusterID string, args []*ccev2.InstanceSet, option *bce.SignOption) (*ccev2.CreateInstancesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstances", ctx, clusterID, args, option)
	ret0, _ := ret[0].(*ccev2.CreateInstancesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInstances indicates an expected call of CreateInstances.
func (mr *MockInterfaceMockRecorder) CreateInstances(ctx, clusterID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstances", reflect.TypeOf((*MockInterface)(nil).CreateInstances), ctx, clusterID, args, option)
}

// CreateRBACKubeConfig mocks base method.
func (m *MockInterface) CreateRBACKubeConfig(ctx context.Context, request *ccev2.KubeConfigRequest, option *bce.SignOption) (*ccev2.CreateRBACResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRBACKubeConfig", ctx, request, option)
	ret0, _ := ret[0].(*ccev2.CreateRBACResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRBACKubeConfig indicates an expected call of CreateRBACKubeConfig.
func (mr *MockInterfaceMockRecorder) CreateRBACKubeConfig(ctx, request, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRBACKubeConfig", reflect.TypeOf((*MockInterface)(nil).CreateRBACKubeConfig), ctx, request, option)
}

// CreateScaleDownInstanceGroupByCleanPolicy mocks base method.
func (m *MockInterface) CreateScaleDownInstanceGroupByCleanPolicy(ctx context.Context, clusterID, instanceGroupID string, instancesToBeRemoved []string, cleanPolicy ccev2.CleanPolicy, deleteOption *ccetypes.DeleteOption, option *bce.SignOption) (*ccev2.CreateTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateScaleDownInstanceGroupByCleanPolicy", ctx, clusterID, instanceGroupID, instancesToBeRemoved, cleanPolicy, deleteOption, option)
	ret0, _ := ret[0].(*ccev2.CreateTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateScaleDownInstanceGroupByCleanPolicy indicates an expected call of CreateScaleDownInstanceGroupByCleanPolicy.
func (mr *MockInterfaceMockRecorder) CreateScaleDownInstanceGroupByCleanPolicy(ctx, clusterID, instanceGroupID, instancesToBeRemoved, cleanPolicy, deleteOption, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateScaleDownInstanceGroupByCleanPolicy", reflect.TypeOf((*MockInterface)(nil).CreateScaleDownInstanceGroupByCleanPolicy), ctx, clusterID, instanceGroupID, instancesToBeRemoved, cleanPolicy, deleteOption, option)
}

// CreateScaleDownInstanceGroupTask mocks base method.
func (m *MockInterface) CreateScaleDownInstanceGroupTask(ctx context.Context, clusterID, instanceGroupID string, instancesToBeRemoved []string, option *bce.SignOption) (*ccev2.CreateTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateScaleDownInstanceGroupTask", ctx, clusterID, instanceGroupID, instancesToBeRemoved, option)
	ret0, _ := ret[0].(*ccev2.CreateTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateScaleDownInstanceGroupTask indicates an expected call of CreateScaleDownInstanceGroupTask.
func (mr *MockInterfaceMockRecorder) CreateScaleDownInstanceGroupTask(ctx, clusterID, instanceGroupID, instancesToBeRemoved, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateScaleDownInstanceGroupTask", reflect.TypeOf((*MockInterface)(nil).CreateScaleDownInstanceGroupTask), ctx, clusterID, instanceGroupID, instancesToBeRemoved, option)
}

// CreateScaleUpInstanceGroupTask mocks base method.
func (m *MockInterface) CreateScaleUpInstanceGroupTask(ctx context.Context, clusterID, instanceGroupID string, targetReplicas int, option *bce.SignOption) (*ccev2.CreateTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateScaleUpInstanceGroupTask", ctx, clusterID, instanceGroupID, targetReplicas, option)
	ret0, _ := ret[0].(*ccev2.CreateTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateScaleUpInstanceGroupTask indicates an expected call of CreateScaleUpInstanceGroupTask.
func (mr *MockInterfaceMockRecorder) CreateScaleUpInstanceGroupTask(ctx, clusterID, instanceGroupID, targetReplicas, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateScaleUpInstanceGroupTask", reflect.TypeOf((*MockInterface)(nil).CreateScaleUpInstanceGroupTask), ctx, clusterID, instanceGroupID, targetReplicas, option)
}

// CreateWorkflow mocks base method.
func (m *MockInterface) CreateWorkflow(ctx context.Context, clusterID string, args *ccev2.CreateWorkflowRequest, option *bce.SignOption) (*ccev2.CreateWorkflowResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateWorkflow", ctx, clusterID, args, option)
	ret0, _ := ret[0].(*ccev2.CreateWorkflowResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateWorkflow indicates an expected call of CreateWorkflow.
func (mr *MockInterfaceMockRecorder) CreateWorkflow(ctx, clusterID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateWorkflow", reflect.TypeOf((*MockInterface)(nil).CreateWorkflow), ctx, clusterID, args, option)
}

// DeleteCluster mocks base method.
func (m *MockInterface) DeleteCluster(ctx context.Context, clusterID string, options *ccev2.DeleteOptions, option *bce.SignOption) (*ccev2.DeleteClusterResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteCluster", ctx, clusterID, options, option)
	ret0, _ := ret[0].(*ccev2.DeleteClusterResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteCluster indicates an expected call of DeleteCluster.
func (mr *MockInterfaceMockRecorder) DeleteCluster(ctx, clusterID, options, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCluster", reflect.TypeOf((*MockInterface)(nil).DeleteCluster), ctx, clusterID, options, option)
}

// DeleteInstanceGroup mocks base method.
func (m *MockInterface) DeleteInstanceGroup(ctx context.Context, clusterID, instanceGroupID string, deleteInstances bool, option *bce.SignOption) (*ccev2.DeleteInstanceGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteInstanceGroup", ctx, clusterID, instanceGroupID, deleteInstances, option)
	ret0, _ := ret[0].(*ccev2.DeleteInstanceGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteInstanceGroup indicates an expected call of DeleteInstanceGroup.
func (mr *MockInterfaceMockRecorder) DeleteInstanceGroup(ctx, clusterID, instanceGroupID, deleteInstances, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInstanceGroup", reflect.TypeOf((*MockInterface)(nil).DeleteInstanceGroup), ctx, clusterID, instanceGroupID, deleteInstances, option)
}

// DeleteInstances mocks base method.
func (m *MockInterface) DeleteInstances(ctx context.Context, clusterID string, args *ccev2.DeleteInstancesRequest, option *bce.SignOption) (*ccev2.DeleteInstancesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteInstances", ctx, clusterID, args, option)
	ret0, _ := ret[0].(*ccev2.DeleteInstancesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteInstances indicates an expected call of DeleteInstances.
func (mr *MockInterfaceMockRecorder) DeleteInstances(ctx, clusterID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInstances", reflect.TypeOf((*MockInterface)(nil).DeleteInstances), ctx, clusterID, args, option)
}

// DeleteWorkflow mocks base method.
func (m *MockInterface) DeleteWorkflow(ctx context.Context, clusterID, workflowID string, option *bce.SignOption) (*ccev2.DeleteWorkflowResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteWorkflow", ctx, clusterID, workflowID, option)
	ret0, _ := ret[0].(*ccev2.DeleteWorkflowResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteWorkflow indicates an expected call of DeleteWorkflow.
func (mr *MockInterfaceMockRecorder) DeleteWorkflow(ctx, clusterID, workflowID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteWorkflow", reflect.TypeOf((*MockInterface)(nil).DeleteWorkflow), ctx, clusterID, workflowID, option)
}

// DrainNodes mocks base method.
func (m *MockInterface) DrainNodes(ctx context.Context, args *ccev2.DrainNodesRequest, option *bce.SignOption) (*ccev2.DrainNodesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DrainNodes", ctx, args, option)
	ret0, _ := ret[0].(*ccev2.DrainNodesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DrainNodes indicates an expected call of DrainNodes.
func (mr *MockInterfaceMockRecorder) DrainNodes(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DrainNodes", reflect.TypeOf((*MockInterface)(nil).DrainNodes), ctx, args, option)
}

// EnableLogAddon mocks base method.
func (m *MockInterface) EnableLogAddon(ctx context.Context, clusterID string, args *ccev2.LoggingEnableParams, option *bce.SignOption) (*ccev2.LoggingAddOnResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnableLogAddon", ctx, clusterID, args, option)
	ret0, _ := ret[0].(*ccev2.LoggingAddOnResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EnableLogAddon indicates an expected call of EnableLogAddon.
func (mr *MockInterfaceMockRecorder) EnableLogAddon(ctx, clusterID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnableLogAddon", reflect.TypeOf((*MockInterface)(nil).EnableLogAddon), ctx, clusterID, args, option)
}

// GetAdminKubeConfig mocks base method.
func (m *MockInterface) GetAdminKubeConfig(ctx context.Context, clusterID string, kubeConfigType models.KubeConfigType, option *bce.SignOption) (*ccev2.GetKubeConfigResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAdminKubeConfig", ctx, clusterID, kubeConfigType, option)
	ret0, _ := ret[0].(*ccev2.GetKubeConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAdminKubeConfig indicates an expected call of GetAdminKubeConfig.
func (mr *MockInterfaceMockRecorder) GetAdminKubeConfig(ctx, clusterID, kubeConfigType, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAdminKubeConfig", reflect.TypeOf((*MockInterface)(nil).GetAdminKubeConfig), ctx, clusterID, kubeConfigType, option)
}

// GetAutoScalerConfig mocks base method.
func (m *MockInterface) GetAutoScalerConfig(ctx context.Context, clusterID string, option *bce.SignOption) (*ccev2.GetAutoscalerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAutoScalerConfig", ctx, clusterID, option)
	ret0, _ := ret[0].(*ccev2.GetAutoscalerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAutoScalerConfig indicates an expected call of GetAutoScalerConfig.
func (mr *MockInterfaceMockRecorder) GetAutoScalerConfig(ctx, clusterID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAutoScalerConfig", reflect.TypeOf((*MockInterface)(nil).GetAutoScalerConfig), ctx, clusterID, option)
}

// GetBindingCpromInstances mocks base method.
func (m *MockInterface) GetBindingCpromInstances(ctx context.Context, args *ccev2.ListBindingCPromInstanceResquest, option *bce.SignOption) (*ccev2.ListBindingCPromInstanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBindingCpromInstances", ctx, args, option)
	ret0, _ := ret[0].(*ccev2.ListBindingCPromInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBindingCpromInstances indicates an expected call of GetBindingCpromInstances.
func (mr *MockInterfaceMockRecorder) GetBindingCpromInstances(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindingCpromInstances", reflect.TypeOf((*MockInterface)(nil).GetBindingCpromInstances), ctx, args, option)
}

// GetCluster mocks base method.
func (m *MockInterface) GetCluster(ctx context.Context, clusterID string, option *bce.SignOption) (*ccev2.GetClusterResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCluster", ctx, clusterID, option)
	ret0, _ := ret[0].(*ccev2.GetClusterResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCluster indicates an expected call of GetCluster.
func (mr *MockInterfaceMockRecorder) GetCluster(ctx, clusterID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCluster", reflect.TypeOf((*MockInterface)(nil).GetCluster), ctx, clusterID, option)
}

// GetClusterEventSteps mocks base method.
func (m *MockInterface) GetClusterEventSteps(ctx context.Context, clusterID string, option *bce.SignOption) (*ccev2.GetEventStepsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterEventSteps", ctx, clusterID, option)
	ret0, _ := ret[0].(*ccev2.GetEventStepsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterEventSteps indicates an expected call of GetClusterEventSteps.
func (mr *MockInterfaceMockRecorder) GetClusterEventSteps(ctx, clusterID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterEventSteps", reflect.TypeOf((*MockInterface)(nil).GetClusterEventSteps), ctx, clusterID, option)
}

// GetClusterExtraInfo mocks base method.
func (m *MockInterface) GetClusterExtraInfo(ctx context.Context, clusterID string, option *bce.SignOption) (*ccev2.ClusterExtraInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterExtraInfo", ctx, clusterID, option)
	ret0, _ := ret[0].(*ccev2.ClusterExtraInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterExtraInfo indicates an expected call of GetClusterExtraInfo.
func (mr *MockInterfaceMockRecorder) GetClusterExtraInfo(ctx, clusterID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterExtraInfo", reflect.TypeOf((*MockInterface)(nil).GetClusterExtraInfo), ctx, clusterID, option)
}

// GetClusterNodeQuota mocks base method.
func (m *MockInterface) GetClusterNodeQuota(ctx context.Context, clusterID string, option *bce.SignOption) (*ccev2.GetQuotaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterNodeQuota", ctx, clusterID, option)
	ret0, _ := ret[0].(*ccev2.GetQuotaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterNodeQuota indicates an expected call of GetClusterNodeQuota.
func (mr *MockInterfaceMockRecorder) GetClusterNodeQuota(ctx, clusterID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterNodeQuota", reflect.TypeOf((*MockInterface)(nil).GetClusterNodeQuota), ctx, clusterID, option)
}

// GetClusterOfBBE mocks base method.
func (m *MockInterface) GetClusterOfBBE(ctx context.Context, clusterID string, option *bce.SignOption) (*ccev2.GetClusterOfBBEResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterOfBBE", ctx, clusterID, option)
	ret0, _ := ret[0].(*ccev2.GetClusterOfBBEResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterOfBBE indicates an expected call of GetClusterOfBBE.
func (mr *MockInterfaceMockRecorder) GetClusterOfBBE(ctx, clusterID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterOfBBE", reflect.TypeOf((*MockInterface)(nil).GetClusterOfBBE), ctx, clusterID, option)
}

// GetClusterQuota mocks base method.
func (m *MockInterface) GetClusterQuota(ctx context.Context, option *bce.SignOption) (*ccev2.GetQuotaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterQuota", ctx, option)
	ret0, _ := ret[0].(*ccev2.GetQuotaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterQuota indicates an expected call of GetClusterQuota.
func (mr *MockInterfaceMockRecorder) GetClusterQuota(ctx, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterQuota", reflect.TypeOf((*MockInterface)(nil).GetClusterQuota), ctx, option)
}

// GetInstance mocks base method.
func (m *MockInterface) GetInstance(ctx context.Context, clusterID, instanceID string, option *bce.SignOption) (*ccev2.GetInstanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstance", ctx, clusterID, instanceID, option)
	ret0, _ := ret[0].(*ccev2.GetInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstance indicates an expected call of GetInstance.
func (mr *MockInterfaceMockRecorder) GetInstance(ctx, clusterID, instanceID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstance", reflect.TypeOf((*MockInterface)(nil).GetInstance), ctx, clusterID, instanceID, option)
}

// GetInstanceByNodeName mocks base method.
func (m *MockInterface) GetInstanceByNodeName(ctx context.Context, clusterID, nodeName string, option *bce.SignOption) (*ccev2.GetInstanceByNodeNameResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceByNodeName", ctx, clusterID, nodeName, option)
	ret0, _ := ret[0].(*ccev2.GetInstanceByNodeNameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceByNodeName indicates an expected call of GetInstanceByNodeName.
func (mr *MockInterfaceMockRecorder) GetInstanceByNodeName(ctx, clusterID, nodeName, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceByNodeName", reflect.TypeOf((*MockInterface)(nil).GetInstanceByNodeName), ctx, clusterID, nodeName, option)
}

// GetInstanceEventSteps mocks base method.
func (m *MockInterface) GetInstanceEventSteps(ctx context.Context, cceInstanceID string, option *bce.SignOption) (*ccev2.GetEventStepsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceEventSteps", ctx, cceInstanceID, option)
	ret0, _ := ret[0].(*ccev2.GetEventStepsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceEventSteps indicates an expected call of GetInstanceEventSteps.
func (mr *MockInterfaceMockRecorder) GetInstanceEventSteps(ctx, cceInstanceID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceEventSteps", reflect.TypeOf((*MockInterface)(nil).GetInstanceEventSteps), ctx, cceInstanceID, option)
}

// GetInstanceGroup mocks base method.
func (m *MockInterface) GetInstanceGroup(ctx context.Context, clusterID, instanceGroupID string, option *bce.SignOption) (*ccev2.GetInstanceGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceGroup", ctx, clusterID, instanceGroupID, option)
	ret0, _ := ret[0].(*ccev2.GetInstanceGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceGroup indicates an expected call of GetInstanceGroup.
func (mr *MockInterfaceMockRecorder) GetInstanceGroup(ctx, clusterID, instanceGroupID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceGroup", reflect.TypeOf((*MockInterface)(nil).GetInstanceGroup), ctx, clusterID, instanceGroupID, option)
}

// GetTask mocks base method.
func (m *MockInterface) GetTask(ctx context.Context, taskType, taskID string, option *bce.SignOption) (*ccev2.GetTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTask", ctx, taskType, taskID, option)
	ret0, _ := ret[0].(*ccev2.GetTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTask indicates an expected call of GetTask.
func (mr *MockInterfaceMockRecorder) GetTask(ctx, taskType, taskID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTask", reflect.TypeOf((*MockInterface)(nil).GetTask), ctx, taskType, taskID, option)
}

// GetWorkflow mocks base method.
func (m *MockInterface) GetWorkflow(ctx context.Context, clusterID, workflowID string, option *bce.SignOption) (*ccev2.GetWorkflowResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflow", ctx, clusterID, workflowID, option)
	ret0, _ := ret[0].(*ccev2.GetWorkflowResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflow indicates an expected call of GetWorkflow.
func (mr *MockInterfaceMockRecorder) GetWorkflow(ctx, clusterID, workflowID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflow", reflect.TypeOf((*MockInterface)(nil).GetWorkflow), ctx, clusterID, workflowID, option)
}

// InstallAddon mocks base method.
func (m *MockInterface) InstallAddon(ctx context.Context, clusterID string, args *ccev2.InstallParams, option *bce.SignOption) (*ccev2.InstallAddOnResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InstallAddon", ctx, clusterID, args, option)
	ret0, _ := ret[0].(*ccev2.InstallAddOnResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InstallAddon indicates an expected call of InstallAddon.
func (mr *MockInterfaceMockRecorder) InstallAddon(ctx, clusterID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InstallAddon", reflect.TypeOf((*MockInterface)(nil).InstallAddon), ctx, clusterID, args, option)
}

// InstallPlugin mocks base method.
func (m *MockInterface) InstallPlugin(ctx context.Context, clusterID, pluginName string, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InstallPlugin", ctx, clusterID, pluginName, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// InstallPlugin indicates an expected call of InstallPlugin.
func (mr *MockInterfaceMockRecorder) InstallPlugin(ctx, clusterID, pluginName, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InstallPlugin", reflect.TypeOf((*MockInterface)(nil).InstallPlugin), ctx, clusterID, pluginName, option)
}

// ListAddOns mocks base method.
func (m *MockInterface) ListAddOns(ctx context.Context, clusterID string, args *ccev2.ListParams, option *bce.SignOption) (*ccev2.ListAddOnResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAddOns", ctx, clusterID, args, option)
	ret0, _ := ret[0].(*ccev2.ListAddOnResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAddOns indicates an expected call of ListAddOns.
func (mr *MockInterfaceMockRecorder) ListAddOns(ctx, clusterID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAddOns", reflect.TypeOf((*MockInterface)(nil).ListAddOns), ctx, clusterID, args, option)
}

// ListClusters mocks base method.
func (m *MockInterface) ListClusters(ctx context.Context, keywordType ccev2.ClusterKeywordType, keyword string, orderBy ccev2.ClusterOrderBy, order ccev2.Order, pageNum, pageSize int, option *bce.SignOption) (*ccev2.ListClustersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListClusters", ctx, keywordType, keyword, orderBy, order, pageNum, pageSize, option)
	ret0, _ := ret[0].(*ccev2.ListClustersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListClusters indicates an expected call of ListClusters.
func (mr *MockInterfaceMockRecorder) ListClusters(ctx, keywordType, keyword, orderBy, order, pageNum, pageSize, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListClusters", reflect.TypeOf((*MockInterface)(nil).ListClusters), ctx, keywordType, keyword, orderBy, order, pageNum, pageSize, option)
}

// ListInstanceGroups mocks base method.
func (m *MockInterface) ListInstanceGroups(ctx context.Context, clusterID string, listOption *ccev2.InstanceGroupListOption, option *bce.SignOption) (*ccev2.ListInstanceGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstanceGroups", ctx, clusterID, listOption, option)
	ret0, _ := ret[0].(*ccev2.ListInstanceGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstanceGroups indicates an expected call of ListInstanceGroups.
func (mr *MockInterfaceMockRecorder) ListInstanceGroups(ctx, clusterID, listOption, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstanceGroups", reflect.TypeOf((*MockInterface)(nil).ListInstanceGroups), ctx, clusterID, listOption, option)
}

// ListInstancesByInstanceGroupID mocks base method.
func (m *MockInterface) ListInstancesByInstanceGroupID(ctx context.Context, clusterID, instanceGroupID string, pageNo, pageSize int, option *bce.SignOption) (*ccev2.ListInstancesByInstanceGroupIDResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstancesByInstanceGroupID", ctx, clusterID, instanceGroupID, pageNo, pageSize, option)
	ret0, _ := ret[0].(*ccev2.ListInstancesByInstanceGroupIDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstancesByInstanceGroupID indicates an expected call of ListInstancesByInstanceGroupID.
func (mr *MockInterfaceMockRecorder) ListInstancesByInstanceGroupID(ctx, clusterID, instanceGroupID, pageNo, pageSize, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstancesByInstanceGroupID", reflect.TypeOf((*MockInterface)(nil).ListInstancesByInstanceGroupID), ctx, clusterID, instanceGroupID, pageNo, pageSize, option)
}

// ListInstancesByPage mocks base method.
func (m *MockInterface) ListInstancesByPage(ctx context.Context, clusterID string, args *ccev2.ListInstancesByPageParams, option *bce.SignOption) (*ccev2.ListInstancesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstancesByPage", ctx, clusterID, args, option)
	ret0, _ := ret[0].(*ccev2.ListInstancesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstancesByPage indicates an expected call of ListInstancesByPage.
func (mr *MockInterfaceMockRecorder) ListInstancesByPage(ctx, clusterID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstancesByPage", reflect.TypeOf((*MockInterface)(nil).ListInstancesByPage), ctx, clusterID, args, option)
}

// ListLogStores mocks base method.
func (m *MockInterface) ListLogStores(ctx context.Context, clusterID, logStoreType string, option *bce.SignOption) (*ccev2.ListLogStoreResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListLogStores", ctx, clusterID, logStoreType, option)
	ret0, _ := ret[0].(*ccev2.ListLogStoreResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListLogStores indicates an expected call of ListLogStores.
func (mr *MockInterfaceMockRecorder) ListLogStores(ctx, clusterID, logStoreType, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListLogStores", reflect.TypeOf((*MockInterface)(nil).ListLogStores), ctx, clusterID, logStoreType, option)
}

// ListNodesCanBeUpgradedByPage mocks base method.
func (m *MockInterface) ListNodesCanBeUpgradedByPage(ctx context.Context, clusterID string, option *bce.SignOption) (*ccev2.ListNodesCanBeUpgradedByPageResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListNodesCanBeUpgradedByPage", ctx, clusterID, option)
	ret0, _ := ret[0].(*ccev2.ListNodesCanBeUpgradedByPageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListNodesCanBeUpgradedByPage indicates an expected call of ListNodesCanBeUpgradedByPage.
func (mr *MockInterfaceMockRecorder) ListNodesCanBeUpgradedByPage(ctx, clusterID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListNodesCanBeUpgradedByPage", reflect.TypeOf((*MockInterface)(nil).ListNodesCanBeUpgradedByPage), ctx, clusterID, option)
}

// ListTasks mocks base method.
func (m *MockInterface) ListTasks(ctx context.Context, taskType string, listOption ccev2.ListTaskOption, signOption *bce.SignOption) (*ccev2.ListTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTasks", ctx, taskType, listOption, signOption)
	ret0, _ := ret[0].(*ccev2.ListTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTasks indicates an expected call of ListTasks.
func (mr *MockInterfaceMockRecorder) ListTasks(ctx, taskType, listOption, signOption interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTasks", reflect.TypeOf((*MockInterface)(nil).ListTasks), ctx, taskType, listOption, signOption)
}

// ListWorkflows mocks base method.
func (m *MockInterface) ListWorkflows(ctx context.Context, clusterID string, option *bce.SignOption) (*ccev2.ListWorkflowsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListWorkflows", ctx, clusterID, option)
	ret0, _ := ret[0].(*ccev2.ListWorkflowsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListWorkflows indicates an expected call of ListWorkflows.
func (mr *MockInterfaceMockRecorder) ListWorkflows(ctx, clusterID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListWorkflows", reflect.TypeOf((*MockInterface)(nil).ListWorkflows), ctx, clusterID, option)
}

// RecommendClusterIPCIDR mocks base method.
func (m *MockInterface) RecommendClusterIPCIDR(ctx context.Context, args *ccev2.RecommendClusterIPCIDRRequest, option *bce.SignOption) (*ccev2.RecommendClusterIPCIDRResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecommendClusterIPCIDR", ctx, args, option)
	ret0, _ := ret[0].(*ccev2.RecommendClusterIPCIDRResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecommendClusterIPCIDR indicates an expected call of RecommendClusterIPCIDR.
func (mr *MockInterfaceMockRecorder) RecommendClusterIPCIDR(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecommendClusterIPCIDR", reflect.TypeOf((*MockInterface)(nil).RecommendClusterIPCIDR), ctx, args, option)
}

// RecommendContainerCIDR mocks base method.
func (m *MockInterface) RecommendContainerCIDR(ctx context.Context, args *ccev2.RecommendContainerCIDRRequest, option *bce.SignOption) (*ccev2.RecommendContainerCIDRResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecommendContainerCIDR", ctx, args, option)
	ret0, _ := ret[0].(*ccev2.RecommendContainerCIDRResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecommendContainerCIDR indicates an expected call of RecommendContainerCIDR.
func (mr *MockInterfaceMockRecorder) RecommendContainerCIDR(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecommendContainerCIDR", reflect.TypeOf((*MockInterface)(nil).RecommendContainerCIDR), ctx, args, option)
}

// ResetClusterRetryCount mocks base method.
func (m *MockInterface) ResetClusterRetryCount(ctx context.Context, clusterID string, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetClusterRetryCount", ctx, clusterID, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResetClusterRetryCount indicates an expected call of ResetClusterRetryCount.
func (mr *MockInterfaceMockRecorder) ResetClusterRetryCount(ctx, clusterID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetClusterRetryCount", reflect.TypeOf((*MockInterface)(nil).ResetClusterRetryCount), ctx, clusterID, option)
}

// ResetInstanceRetryCount mocks base method.
func (m *MockInterface) ResetInstanceRetryCount(ctx context.Context, clusterID, cceInstanceID string, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetInstanceRetryCount", ctx, clusterID, cceInstanceID, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResetInstanceRetryCount indicates an expected call of ResetInstanceRetryCount.
func (mr *MockInterfaceMockRecorder) ResetInstanceRetryCount(ctx, clusterID, cceInstanceID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetInstanceRetryCount", reflect.TypeOf((*MockInterface)(nil).ResetInstanceRetryCount), ctx, clusterID, cceInstanceID, option)
}

// SetDebug mocks base method.
func (m *MockInterface) SetDebug(debug bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", debug)
}

// SetDebug indicates an expected call of SetDebug.
func (mr *MockInterfaceMockRecorder) SetDebug(debug interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), debug)
}

// SyncInstance mocks base method.
func (m *MockInterface) SyncInstance(ctx context.Context, clusterID string, option *bce.SignOption) (*ccev2.SyncInstancesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncInstance", ctx, clusterID, option)
	ret0, _ := ret[0].(*ccev2.SyncInstancesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncInstance indicates an expected call of SyncInstance.
func (mr *MockInterfaceMockRecorder) SyncInstance(ctx, clusterID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncInstance", reflect.TypeOf((*MockInterface)(nil).SyncInstance), ctx, clusterID, option)
}

// TargetK8SVersion mocks base method.
func (m *MockInterface) TargetK8SVersion(ctx context.Context, clusterID string, clusterRole ccetypes.ClusterRole, option *bce.SignOption) (*ccev2.TargetK8SVersionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TargetK8SVersion", ctx, clusterID, clusterRole, option)
	ret0, _ := ret[0].(*ccev2.TargetK8SVersionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TargetK8SVersion indicates an expected call of TargetK8SVersion.
func (mr *MockInterfaceMockRecorder) TargetK8SVersion(ctx, clusterID, clusterRole, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TargetK8SVersion", reflect.TypeOf((*MockInterface)(nil).TargetK8SVersion), ctx, clusterID, clusterRole, option)
}

// UninstallAddon mocks base method.
func (m *MockInterface) UninstallAddon(ctx context.Context, clusterID string, args *ccev2.UninstallParams, option *bce.SignOption) (*ccev2.UninstallAddOnResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UninstallAddon", ctx, clusterID, args, option)
	ret0, _ := ret[0].(*ccev2.UninstallAddOnResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UninstallAddon indicates an expected call of UninstallAddon.
func (mr *MockInterfaceMockRecorder) UninstallAddon(ctx, clusterID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UninstallAddon", reflect.TypeOf((*MockInterface)(nil).UninstallAddon), ctx, clusterID, args, option)
}

// UpdateAddon mocks base method.
func (m *MockInterface) UpdateAddon(ctx context.Context, clusterID string, args *ccev2.UpdateParams, option *bce.SignOption) (*ccev2.UpdateAddOnResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAddon", ctx, clusterID, args, option)
	ret0, _ := ret[0].(*ccev2.UpdateAddOnResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAddon indicates an expected call of UpdateAddon.
func (mr *MockInterfaceMockRecorder) UpdateAddon(ctx, clusterID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAddon", reflect.TypeOf((*MockInterface)(nil).UpdateAddon), ctx, clusterID, args, option)
}

// UpdateAutoScalerConfig mocks base method.
func (m *MockInterface) UpdateAutoScalerConfig(ctx context.Context, clusterID string, args *addon.ClusterAutoscalerConfig, option *bce.SignOption) (*ccev2.CommonResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAutoScalerConfig", ctx, clusterID, args, option)
	ret0, _ := ret[0].(*ccev2.CommonResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAutoScalerConfig indicates an expected call of UpdateAutoScalerConfig.
func (mr *MockInterfaceMockRecorder) UpdateAutoScalerConfig(ctx, clusterID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAutoScalerConfig", reflect.TypeOf((*MockInterface)(nil).UpdateAutoScalerConfig), ctx, clusterID, args, option)
}

// UpdateCluster mocks base method.
func (m *MockInterface) UpdateCluster(ctx context.Context, clusterID string, args *ccetypes.ClusterSpec, option *bce.SignOption) (*ccev2.UpdateClusterResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCluster", ctx, clusterID, args, option)
	ret0, _ := ret[0].(*ccev2.UpdateClusterResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCluster indicates an expected call of UpdateCluster.
func (mr *MockInterfaceMockRecorder) UpdateCluster(ctx, clusterID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCluster", reflect.TypeOf((*MockInterface)(nil).UpdateCluster), ctx, clusterID, args, option)
}

// UpdateInstance mocks base method.
func (m *MockInterface) UpdateInstance(ctx context.Context, clusterID, instanceID string, spec *ccetypes.InstanceSpec, option *bce.SignOption) (*ccev2.UpdateInstancesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstance", ctx, clusterID, instanceID, spec, option)
	ret0, _ := ret[0].(*ccev2.UpdateInstancesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateInstance indicates an expected call of UpdateInstance.
func (mr *MockInterfaceMockRecorder) UpdateInstance(ctx, clusterID, instanceID, spec, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstance", reflect.TypeOf((*MockInterface)(nil).UpdateInstance), ctx, clusterID, instanceID, spec, option)
}

// UpdateInstanceGroupClusterAutoscalerSpec mocks base method.
func (m *MockInterface) UpdateInstanceGroupClusterAutoscalerSpec(ctx context.Context, clusterID, instanceGroupID string, request *ccev2.ClusterAutoscalerSpec, option *bce.SignOption) (*ccev2.UpdateInstanceGroupClusterAutoscalerSpecResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceGroupClusterAutoscalerSpec", ctx, clusterID, instanceGroupID, request, option)
	ret0, _ := ret[0].(*ccev2.UpdateInstanceGroupClusterAutoscalerSpecResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateInstanceGroupClusterAutoscalerSpec indicates an expected call of UpdateInstanceGroupClusterAutoscalerSpec.
func (mr *MockInterfaceMockRecorder) UpdateInstanceGroupClusterAutoscalerSpec(ctx, clusterID, instanceGroupID, request, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceGroupClusterAutoscalerSpec", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceGroupClusterAutoscalerSpec), ctx, clusterID, instanceGroupID, request, option)
}

// UpdateInstanceGroupConfig mocks base method.
func (m *MockInterface) UpdateInstanceGroupConfig(ctx context.Context, clusterID, instanceGroupID string, request *ccev2.UpdateInstanceGroupRequest, option *bce.SignOption) (*ccev2.UpdateInstanceGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceGroupConfig", ctx, clusterID, instanceGroupID, request, option)
	ret0, _ := ret[0].(*ccev2.UpdateInstanceGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateInstanceGroupConfig indicates an expected call of UpdateInstanceGroupConfig.
func (mr *MockInterfaceMockRecorder) UpdateInstanceGroupConfig(ctx, clusterID, instanceGroupID, request, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceGroupConfig", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceGroupConfig), ctx, clusterID, instanceGroupID, request, option)
}

// UpdateInstanceScaleDownDisabled mocks base method.
func (m *MockInterface) UpdateInstanceScaleDownDisabled(ctx context.Context, clusterID string, args *ccev2.UpdateNodeScaleDownRequest) (*ccev2.UpdateNodeScaleDownResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceScaleDownDisabled", ctx, clusterID, args)
	ret0, _ := ret[0].(*ccev2.UpdateNodeScaleDownResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

//	UpdateInstanceScaleDownDisabled indicates an expected call of UpdateInstanceScaleDownDisabled.
func (mr *MockInterfaceMockRecorder) UpdateInstanceScaleDownDisabled(ctx, clusterID, args interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceScaleDownDisabled", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceScaleDownDisabled), ctx, clusterID, args)
}

// UpdateInstanceGroupInstanceTemplate mocks base method.
func (m *MockInterface) UpdateInstanceGroupInstanceTemplate(ctx context.Context, clusterID, instanceGroupID string, request *ccev2.UpdateInstanceGroupInstanceTemplateRequest, option *bce.SignOption) (*ccev2.UpdateInstanceGroupInstanceTemplateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceGroupInstanceTemplate", ctx, clusterID, instanceGroupID, request, option)
	ret0, _ := ret[0].(*ccev2.UpdateInstanceGroupInstanceTemplateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateInstanceGroupInstanceTemplate indicates an expected call of UpdateInstanceGroupInstanceTemplate.
func (mr *MockInterfaceMockRecorder) UpdateInstanceGroupInstanceTemplate(ctx, clusterID, instanceGroupID, request, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceGroupInstanceTemplate", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceGroupInstanceTemplate), ctx, clusterID, instanceGroupID, request, option)
}

// UpdateInstanceGroupPausedStatus mocks base method.
func (m *MockInterface) UpdateInstanceGroupPausedStatus(ctx context.Context, clusterID, instanceGroupID string, request ccev2.PauseDetail, option *bce.SignOption) (*ccev2.UpdateInstanceGroupPausedStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceGroupPausedStatus", ctx, clusterID, instanceGroupID, request, option)
	ret0, _ := ret[0].(*ccev2.UpdateInstanceGroupPausedStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateInstanceGroupPausedStatus indicates an expected call of UpdateInstanceGroupPausedStatus.
func (mr *MockInterfaceMockRecorder) UpdateInstanceGroupPausedStatus(ctx, clusterID, instanceGroupID, request, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceGroupPausedStatus", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceGroupPausedStatus), ctx, clusterID, instanceGroupID, request, option)
}

// UpdateInstanceGroupReplicas mocks base method.
func (m *MockInterface) UpdateInstanceGroupReplicas(ctx context.Context, clusterID, instanceGroupID string, request *ccev2.UpdateInstanceGroupReplicasRequest, option *bce.SignOption) (*ccev2.UpdateInstanceGroupReplicasResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceGroupReplicas", ctx, clusterID, instanceGroupID, request, option)
	ret0, _ := ret[0].(*ccev2.UpdateInstanceGroupReplicasResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateInstanceGroupReplicas indicates an expected call of UpdateInstanceGroupReplicas.
func (mr *MockInterfaceMockRecorder) UpdateInstanceGroupReplicas(ctx, clusterID, instanceGroupID, request, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceGroupReplicas", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceGroupReplicas), ctx, clusterID, instanceGroupID, request, option)
}

// UpdateWorkflow mocks base method.
func (m *MockInterface) UpdateWorkflow(ctx context.Context, clusterID, workflowID string, request *ccev2.UpdateWorkflowRequest, option *bce.SignOption) (*ccev2.UpdateWorkflowResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWorkflow", ctx, clusterID, workflowID, request, option)
	ret0, _ := ret[0].(*ccev2.UpdateWorkflowResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateWorkflow indicates an expected call of UpdateWorkflow.
func (mr *MockInterfaceMockRecorder) UpdateWorkflow(ctx, clusterID, workflowID, request, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWorkflow", reflect.TypeOf((*MockInterface)(nil).UpdateWorkflow), ctx, clusterID, workflowID, request, option)
}

// UpgradeAddon mocks base method.
func (m *MockInterface) UpgradeAddon(ctx context.Context, clusterID string, args *ccev2.UpgradeParams, option *bce.SignOption) (*ccev2.UpdateAddOnResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpgradeAddon", ctx, clusterID, args, option)
	ret0, _ := ret[0].(*ccev2.UpdateAddOnResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpgradeAddon indicates an expected call of UpgradeAddon.
func (mr *MockInterfaceMockRecorder) UpgradeAddon(ctx, clusterID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpgradeAddon", reflect.TypeOf((*MockInterface)(nil).UpgradeAddon), ctx, clusterID, args, option)
}
