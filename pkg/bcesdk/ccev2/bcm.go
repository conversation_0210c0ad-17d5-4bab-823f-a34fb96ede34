package ccev2

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/inspect"
)

func (c *Client) ToggleClusterBCM(ctx context.Context, clusterID string, enable bool, option *bce.SignOption) (*BCMResponse, error) {
	if clusterID == "" {
		return nil, errors.New("empty clusterID")
	}

	params := map[string]string{
		"enable": fmt.Sprintf("%t", enable),
	}

	req, err := bce.NewRequest("POST", c.GetURL(fmt.Sprintf("cluster/%s/bcm", clusterID), params), nil)

	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "BCM推送开关",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r BCMResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}
