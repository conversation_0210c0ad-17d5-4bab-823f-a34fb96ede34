// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2022/02/23 10:37:00, by <EMAIL>, create
*/
/*
实现 CCE V2 SDK Instance 相关方法
*/

package ccev2

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/inspect"
)

// CreateRBACKubeConfig - 根据 RBAC 授权创建 Kubeconfig, 支持临时 Kubeconfig
// PARAMS:
//   - ctx: The context to trace request
//   - request: *KubeConfigRequest
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	*CreateRBACResponse: 返回
//	error: nil if succeed, error if fail
func (c *Client) CreateRBACKubeConfig(ctx context.Context, request *KubeConfigRequest, option *bce.SignOption) (*CreateRBACResponse, error) {
	if request == nil {
		return nil, errors.New("request is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("rbac", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "根据RBAC授权创建Kubeconfig",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r CreateRBACResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// RenewRBACKubeConfig - 更新集群 Kubeconfig
// PARAMS:
//   - ctx: The context to trace request
//   - request: *KubeConfigRequest
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	*RenewRBACResponse: 返回
//	error: nil if success, error if fail
func (c *Client) RenewRBACKubeConfig(ctx context.Context, request *KubeConfigRequest, option *bce.SignOption) (*RenewRBACResponse, error) {
	if request == nil {
		return nil, errors.New("request is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("rbac", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "更新集群Kubeconfig",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r RenewRBACResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}
