// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/04/16 19:11:00, by <EMAIL>, create
*/
/*
实现 CCE V2 SDK Network 相关方法
*/

package ccev2

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net"

	"k8s.io/apimachinery/pkg/util/sets"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/inspect"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
)

const (
	// Ref: https://github.com/kubernetes/kubernetes/blob/41505f71092b960ddc52338fcba1724aab9cae93/cmd/kube-apiserver/app/options/validation.go#L36-L97
	clusterIPMaxCIDRBits = 20
)

func (c *Client) CheckClusterIPCIDR(ctx context.Context, args *CheckClusterIPCIDRequest, option *bce.SignOption) (*CheckClusterIPCIDRResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("net/check_clusterip_cidr", nil), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "检查集群 ClusterIP 网段",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r CheckClusterIPCIDRResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

func (c *Client) CheckContainerNetworkCIDR(ctx context.Context, args *CheckContainerNetworkCIDRRequest, option *bce.SignOption) (*CheckContainerNetworkCIDRResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("net/check_container_network_cidr", nil), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "检查集群容器网络网段",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r CheckContainerNetworkCIDRResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

func (c *Client) RecommendContainerCIDR(ctx context.Context, args *RecommendContainerCIDRRequest, option *bce.SignOption) (*RecommendContainerCIDRResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("net/recommend_container_cidr", nil), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "推荐容器网络网段",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r RecommendContainerCIDRResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

func (c *Client) RecommendClusterIPCIDR(ctx context.Context, args *RecommendClusterIPCIDRRequest, option *bce.SignOption) (*RecommendClusterIPCIDRResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("net/recommend_clusterip_cidr", nil), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "推荐 ClusterIP 网络网段",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r RecommendClusterIPCIDRResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// Validate 校验容器网络请求参数
func (req *CheckContainerNetworkCIDRRequest) Validate() error {
	if req.VPCID == "" {
		return errors.New("VPCID is empty")
	}
	if req.MaxPodsPerNode <= 0 {
		return fmt.Errorf("MaxPodsPerNode must be > 0, currentValue=%d", req.MaxPodsPerNode)
	}

	// if IPVersion not set, use IPv4
	if req.IPVersion == "" {
		req.IPVersion = ccetypes.ContainerNetworkIPTypeIPv4
	}

	switch req.IPVersion {
	case ccetypes.ContainerNetworkIPTypeIPv4:
		return req.validateIPv4()
	case ccetypes.ContainerNetworkIPTypeIPv6:
		return req.validateIPv6()
	case ccetypes.ContainerNetworkIPTypeDualStack:
		if err := req.validateIPv4(); err != nil {
			return err
		}
		return req.validateIPv6()
	default:
		return fmt.Errorf("invalid ipVersion: %s", req.IPVersion)
	}
}

func (req *CheckContainerNetworkCIDRRequest) validateIPv4() error {
	if !util.IsValidCIDR(req.VPCCIDR) {
		return fmt.Errorf("VPCCIDR %s is invalid", req.VPCCIDR)
	}

	if !util.IsValidCIDR(req.ContainerCIDR) {
		return fmt.Errorf("ContainerCIDR %s is invalid", req.ContainerCIDR)
	}

	if !util.IsValidCIDR(req.ClusterIPCIDR) {
		return fmt.Errorf("ClusterIPCIDR %s is invalid", req.ClusterIPCIDR)
	}

	return nil
}

func (req *CheckContainerNetworkCIDRRequest) validateIPv6() error {
	if !util.IsValidCIDR(req.VPCCIDRIPv6) {
		return fmt.Errorf("VPCCIDRIPv6 %s is invalid", req.VPCCIDRIPv6)
	}

	if !util.IsValidCIDR(req.ContainerCIDRIPv6) {
		return fmt.Errorf("ContainerCIDRIPv6 %s is invalid", req.ContainerCIDRIPv6)
	}

	if !util.IsValidCIDR(req.ClusterIPCIDRIPv6) {
		return fmt.Errorf("ClusterIPCIDRIPv6 %s is invalid", req.ClusterIPCIDRIPv6)
	}

	return nil
}

// Validate - 校验 CheckClusterIPCIDRequest
func (req *CheckClusterIPCIDRequest) Validate() error {
	if req.VPCID == "" {
		return errors.New("VPCID is empty")
	}

	if req.IPVersion == "" {
		return errors.New("IPVersion is empty")
	}

	switch req.IPVersion {
	case ccetypes.ContainerNetworkIPTypeIPv4:
		return req.validateIPv4()
	case ccetypes.ContainerNetworkIPTypeIPv6:
		return req.validateIPv6()
	case ccetypes.ContainerNetworkIPTypeDualStack:
		if err := req.validateIPv4(); err != nil {
			return err
		}
		return req.validateIPv6()
	default:
		return fmt.Errorf("invalid IPVersion: %s", req.IPVersion)
	}
}

func (req *CheckClusterIPCIDRequest) validateIPv4() error {
	if !util.IsValidCIDR(req.VPCCIDR) {
		return fmt.Errorf("VPCCIDR is invalid: %s", req.VPCCIDR)
	}

	if !util.IsValidCIDR(req.ClusterIPCIDR) {
		return fmt.Errorf("ClusterIPCIDR is invalid: %s", req.ClusterIPCIDR)
	}

	_, cidr, _ := net.ParseCIDR(req.ClusterIPCIDR)
	ones, bits := cidr.Mask.Size()
	if bits-ones > clusterIPMaxCIDRBits {
		return fmt.Errorf("specified ipv4 %s is too large; for %d-bit addresses, the mask must be >= %d", req.ClusterIPCIDR, bits, bits-clusterIPMaxCIDRBits)
	}

	return nil
}

func (req *CheckClusterIPCIDRequest) validateIPv6() error {
	if !util.IsValidCIDR(req.VPCCIDRIPv6) {
		return fmt.Errorf("VPCCIDRIPv6 is invalid: %s", req.VPCCIDRIPv6)
	}

	if !util.IsValidCIDR(req.ClusterIPCIDRIPv6) {
		return fmt.Errorf("ClusterIPCIDRIPv6 is invalid: %s", req.ClusterIPCIDRIPv6)
	}

	_, cidr, _ := net.ParseCIDR(req.ClusterIPCIDRIPv6)
	ones, bits := cidr.Mask.Size()
	if bits-ones > clusterIPMaxCIDRBits {
		return fmt.Errorf("specified ipv6 %s is too large; for %d-bit addresses, the mask must be >= %d", req.ClusterIPCIDRIPv6, bits, bits-clusterIPMaxCIDRBits)
	}

	return nil
}

// Validate 校验一次性推荐容器网段、ClusterIP 网段的请求参数
func (req *RecommendContainerNetworkCIDRRequest) Validate() error {
	// if IPVersion not set, use IPv4
	if req.IPVersion == "" {
		req.IPVersion = ccetypes.ContainerNetworkIPTypeIPv4
	}

	// 校验推荐容器网段的相关参数
	if err := req.validateContainerCIDRParams(); err != nil {
		return err
	}

	// 校验推荐 ClusterIP 网段的相关参数
	if err := req.validateClusterIPCIDRParams(); err != nil {
		return err
	}

	return nil
}

func (req *RecommendContainerNetworkCIDRRequest) validateContainerCIDRParams() error {
	containerCIDRReq := &RecommendContainerCIDRRequest{
		VPCID:               req.VPCID,
		VPCCIDR:             req.VPCCIDR,
		VPCCIDRIPv6:         req.VPCCIDRIPv6,
		ClusterMaxNodeNum:   req.ClusterMaxNodeNum,
		MaxPodsPerNode:      req.MaxPodsPerNode,
		PrivateNetCIDRs:     req.ContainerPrivateNetCIDRs,
		PrivateNetCIDRIPv6s: req.ContainerPrivateNetCIDRIPv6s,
		K8SVersion:          req.K8SVersion,
		IPVersion:           req.IPVersion,
	}

	return containerCIDRReq.Validate()
}

func (req *RecommendContainerNetworkCIDRRequest) validateClusterIPCIDRParams() error {
	clusterIPCIDRReq := &RecommendClusterIPCIDRRequest{
		VPCCIDR:              req.VPCCIDR,
		VPCCIDRIPv6:          req.VPCCIDRIPv6,
		ContainerCIDR:        "10.0.0.0/24", // mock 数据，在一次性推荐中无此参数
		ContainerCIDRIPv6:    "fc00::/120",  // mock 数据，在一次性推荐中无此参数
		ClusterMaxServiceNum: req.ClusterMaxServiceNum,
		PrivateNetCIDRs:      req.ClusterIPPrivateNetCIDRs,
		PrivateNetCIDRIPv6s:  req.ClusterIPPrivateNetCIDRIPv6s,
		IPVersion:            req.IPVersion,
	}

	return clusterIPCIDRReq.Validate()
}

// Validate 校验推荐容器网段请求参数
func (req *RecommendContainerCIDRRequest) Validate() error {
	if req.VPCID == "" {
		return errors.New("VPCID is empty")
	}

	if err := validateMaxPodsPerNode(req.MaxPodsPerNode); err != nil {
		return err
	}

	// 校验集群最大节点数
	if req.ClusterMaxNodeNum <= 0 {
		return fmt.Errorf("invalid max node num of cluster %d", req.ClusterMaxNodeNum)
	}

	// validate k8s version
	if err := validateK8SVersion(req.K8SVersion); err != nil {
		return err
	}

	// if IPVersion not set, use IPv4
	if req.IPVersion == "" {
		req.IPVersion = ccetypes.ContainerNetworkIPTypeIPv4
	}

	switch req.IPVersion {
	case ccetypes.ContainerNetworkIPTypeIPv4:
		return req.validateIPv4()
	case ccetypes.ContainerNetworkIPTypeIPv6:
		return req.validateIPv6()
	case ccetypes.ContainerNetworkIPTypeDualStack:
		if err := req.validateIPv4(); err != nil {
			return err
		}
		return req.validateIPv6()
	default:
		return fmt.Errorf("invalid ipVersion: %s", req.IPVersion)
	}
}

func (req *RecommendContainerCIDRRequest) validateIPv4() error {
	// VPCCIDR 合法性
	if !util.IsValidCIDR(req.VPCCIDR) {
		return errors.New("VPCCIDR is invalid")
	}

	// 候选网段不能为空
	if len(req.PrivateNetCIDRs) == 0 {
		return errors.New("no candidate container cidr to recommend")
	}

	// IPv4 候选网段范围
	privateIPv4NetSet := sets.NewString(util.PrivateIPv4NetsString...)
	for _, cidr := range req.PrivateNetCIDRs {
		if !privateIPv4NetSet.Has(string(cidr)) {
			return fmt.Errorf("invalid container cidr %s: must be in [10.0.0.0/8, **********/12, ***********/16]", cidr)
		}
	}

	return nil
}

func (req *RecommendContainerCIDRRequest) validateIPv6() error {
	// VPCCIDR 合法性
	if !util.IsValidCIDR(req.VPCCIDRIPv6) {
		return errors.New("VPCCIDRIPv6 is invalid")
	}

	// 候选网段不能为空
	if len(req.PrivateNetCIDRIPv6s) == 0 {
		return errors.New("no candidate container cidr to recommend")
	}

	// IPv6 候选网段范围
	privateIPv6NetSet := sets.NewString(util.PrivateIPv6NetsString...)
	for _, cidr := range req.PrivateNetCIDRIPv6s {
		if !privateIPv6NetSet.Has(string(cidr)) {
			return fmt.Errorf("invalid container cidr %s: must be in [fd00::/8]", cidr)
		}
	}

	var ipv6UnsupportedVersions = map[ccetypes.K8SVersion]string{
		ccetypes.K8S_1_6_2:   "",
		ccetypes.K8S_1_8_6:   "",
		ccetypes.K8S_1_8_12:  "",
		ccetypes.K8S_1_11_1:  "",
		ccetypes.K8S_1_11_5:  "",
		ccetypes.K8S_1_13_4:  "",
		ccetypes.K8S_1_13_10: "",
	}
	// IPv6 not support version less than v1.16
	if _, ok := ipv6UnsupportedVersions[req.K8SVersion]; ok {
		return fmt.Errorf("k8s version %v does not support IPv6", req.K8SVersion)
	}

	return nil
}

// Validate 校验推荐 ClusterIP 网段请求参数
func (req *RecommendClusterIPCIDRRequest) Validate() error {
	if req.ClusterMaxServiceNum < MinClusterIPServiceNum || req.ClusterMaxServiceNum > MaxClusterIPServiceNum {
		return fmt.Errorf("invalid service num of cluster %d, must be in [%d, %d]", req.ClusterMaxServiceNum, MinClusterIPServiceNum, MaxClusterIPServiceNum)
	}

	// if IPVersion not set, use IPv4
	if req.IPVersion == "" {
		req.IPVersion = ccetypes.ContainerNetworkIPTypeIPv4
	}

	switch req.IPVersion {
	case ccetypes.ContainerNetworkIPTypeIPv4:
		return req.validateIPv4()
	case ccetypes.ContainerNetworkIPTypeIPv6:
		return req.validateIPv6()
	case ccetypes.ContainerNetworkIPTypeDualStack:
		if err := req.validateIPv4(); err != nil {
			return err
		}
		return req.validateIPv6()
	default:
		return fmt.Errorf("invalid ipVersion: %s", req.IPVersion)
	}
}

func (req *RecommendClusterIPCIDRRequest) validateIPv4() error {
	if !util.IsValidCIDR(req.VPCCIDR) {
		return errors.New("VPCCIDR is invalid")
	}

	if !util.IsValidCIDR(req.ContainerCIDR) {
		return errors.New("ContainerCIDR is invalid")
	}

	// 候选网段不能为空
	if len(req.PrivateNetCIDRs) == 0 {
		return errors.New("no candidate clusterip cidr to recommend")
	}

	// IPv4 候选网段范围
	privateIPv4NetSet := sets.NewString(util.PrivateIPv4NetsString...)
	for _, cidr := range req.PrivateNetCIDRs {
		if !privateIPv4NetSet.Has(string(cidr)) {
			return fmt.Errorf("invalid clusterip cidr %s: must be in [10.0.0.0/8, **********/12, ***********/16]", cidr)
		}
	}

	return nil
}

func (req *RecommendClusterIPCIDRRequest) validateIPv6() error {
	if !util.IsValidCIDR(req.VPCCIDRIPv6) {
		return errors.New("VPCCIDRIPv6 is invalid")
	}

	if !util.IsValidCIDR(req.ContainerCIDRIPv6) {
		return errors.New("ContainerCIDRIPv6 is invalid")
	}

	// 候选网段不能为空
	if len(req.PrivateNetCIDRIPv6s) == 0 {
		return errors.New("no candidate clusterip cidr to recommend")
	}

	// IPv6 候选网段范围
	privateIPv6NetSet := sets.NewString(util.PrivateIPv6NetsString...)
	for _, cidr := range req.PrivateNetCIDRIPv6s {
		if !privateIPv6NetSet.Has(string(cidr)) {
			return fmt.Errorf("invalid clusterip cidr %s: must be in [fd00::/8]", cidr)
		}
	}

	return nil
}

func validateMaxPodsPerNode(maxPodsPerNode int) error {
	// 检查 MaxNodePod，必须为 2 的幂次方
	maxPodSet := sets.NewInt(32, 64, 128, 256, 512)
	if !maxPodSet.Has(maxPodsPerNode) {
		return fmt.Errorf("unsupported max pod num per node %v, must be in [32, 64, 128, 256, 512]", maxPodsPerNode)
	}
	return nil
}

func validateK8SVersion(version ccetypes.K8SVersion) error {
	if version.IsSupported() {
		return nil
	}
	return fmt.Errorf("invalid k8s version: %v", version)
}
