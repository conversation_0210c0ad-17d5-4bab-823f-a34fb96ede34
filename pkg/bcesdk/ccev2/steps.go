// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2022/02/24 15:17:00, by <EMAIL>, create
*/
/*
实现 CCE V2 SDK Step 相关方法, 展示集群等操作过程
*/

package ccev2

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/inspect"
)

// GetClusterEventSteps -
func (c *Client) GetClusterEventSteps(ctx context.Context, clusterID string, option *bce.SignOption) (*GetEventStepsResponse, error) {
	if clusterID == "" {
		return nil, errors.New("cluster is empty")
	}

	url := fmt.Sprintf("event/cluster/%s", clusterID)
	req, err := bce.NewRequest("GET", c.GetURL(url, nil), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取集群事件步骤",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r GetEventStepsResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// GetInstanceEventSteps -
func (c *Client) GetInstanceEventSteps(ctx context.Context, cceInstanceID string, option *bce.SignOption) (*GetEventStepsResponse, error) {
	if cceInstanceID == "" {
		return nil, errors.New("cceInstanceID is empty")
	}

	url := fmt.Sprintf("event/instance/%s", cceInstanceID)
	req, err := bce.NewRequest("GET", c.GetURL(url, nil), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取实例事件步骤",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r GetEventStepsResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}
