// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/11/05 14:32:00, by <EMAIL>, create
*/
/*
实现 CCE V2 SDK Plugin 相关方法
*/

package ccev2

import (
	"context"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/inspect"
)

// InstallPlugin - 安装插件
func (c *Client) InstallPlugin(ctx context.Context, clusterID, pluginName string, option *bce.SignOption) error {
	if clusterID == "" {
		return errors.New("clusterID is empty")
	}

	if pluginName == "" {
		return errors.New("pluginName is empty")
	}

	url := fmt.Sprintf("cluster/%s/plugin/%s", clusterID, pluginName)
	req, err := bce.NewRequest("POST", c.GetURL(url, nil), nil)
	if err != nil {
		return err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "安装插件",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}
