package ccev2

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/inspect"
)

// ListLogStores 查询集群的logstore列表
func (c *Client) ListLogStores(ctx context.Context, clusterID, logStoreType string, option *bce.SignOption) (*ListLogStoreResponse, error) {
	if clusterID == "" {
		return nil, errors.New("empty clusterID")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	if logStoreType != "" {
		params["logStoreType"] = logStoreType
	}

	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("cluster/%s/logstores", clusterID), params), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "查询节点日志规则列表",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListLogStoreResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}
