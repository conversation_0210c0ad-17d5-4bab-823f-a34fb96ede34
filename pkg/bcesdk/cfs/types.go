// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/11/20, by <EMAIL>, create
*/
/*
文件定义 CFS OpenAPI 相关接口
*/
package cfs

import (
	"context"
)

// Interface 定义 CFS OpenAPI 相关 Interface
type Interface interface {
	// fileSystem
	ListFileSystems(ctx context.Context) (*ListFileSystemsResponse, error)

	CheckFileSystemExist(ctx context.Context, fsName string) (bool, string, error)
	CreateFileSystem(ctx context.Context, fsName string) (string, error)
	DropFileSystem(ctx context.Context, fsID string) error

	// mountTarget
	ListMountTargets(ctx context.Context, fsID string) (*ListMountTargetsResponse, error)

	CheckMountTargetExist(ctx context.Context, fsID, subnetID string) (bool, string, error)
	CreateMountTarget(ctx context.Context, fsID, vpcID, subnetID string) (string, error)
	DeleteMountTarget(ctx context.Context, fsID, mountTargetID string) error
}

type MountTarget struct {
	AccessGroupName string `json:"accessGroupName"`
	Domain          string `json:"domain"`
	SubnetID        string `json:"subnetId"`
	MountID         string `json:"mountId"`
	OVip            string `json:"ovip"`
}

type FileSystem struct {
	FsID            string        `json:"fsId"`
	Status          string        `json:"status"`
	Name            string        `json:"fsName"`
	Type            string        `json:"type"`
	Protocol        string        `json:"protocol"`
	VpcID           string        `json:"vpcId"`
	MountTargetList []MountTarget `json:"mountTargetList"`
}

type ListFileSystemsResponse struct {
	Marker         string       `json:"marker"`
	IsTruncated    bool         `json:"isTruncated"`
	MaxKeys        int          `json:"maxKeys"`
	FileSystemList []FileSystem `json:"fileSystemList"`
}

type CreateFileSystemRequest struct {
	FsName   string `json:"fsName"`
	Type     string `json:"type"`
	Protocol string `json:"protocol"`
}

type CreateFileSystemResponse struct {
	FsID string `json:"fsId"`
}

type ListMountTargetsResponse struct {
	Marker          string        `json:"marker"`
	NextMarker      string        `json:"nextMarker"`
	IsTruncated     bool          `json:"isTruncated"`
	MaxKeys         int           `json:"maxKeys"`
	MountTargetList []MountTarget `json:"mountTargetList"`
}

type CreateMountTargetResponse struct {
	Domain  string `json:"domain"`
	MountID string `json:"mountId"`
}

type CreateMountTargetRequest struct {
	VpcID    string `json:"vpcId"`
	SubnetID string `json:"subnetId"`
}
