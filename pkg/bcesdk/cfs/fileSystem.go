// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/11/20 , by <EMAIL>, create
*/
/*
实现 CFS fileSystem 相关方法
*/

package cfs

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

// ListFileSystems - 获取用户的文件系统列表
// PARAMS:
//   - ctx: The context to trace request
//
// RETURNS:
//
//	ListFileSystemsResponse: 文件系统列表
//	error: nil if succeed, error if fail
func (c *Client) ListFileSystems(ctx context.Context) (*ListFileSystemsResponse, error) {
	req, err := bce.NewRequest("GET", c.GetURL("v1/cfs", nil), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var listFileSystemsResponse ListFileSystemsResponse
	err = json.Unmarshal(bodyContent, &listFileSystemsResponse)
	if err != nil {
		return nil, err
	}
	return &listFileSystemsResponse, nil
}

// CheckFileSystemExist - 检查用户的文件系统是否存在
// PARAMS:
//   - ctx: The context to trace request
//   - fsName: 文件系统名字
//
// RETURNS:
//
//	bool: 文件系统是否存在
//	string: 文件系统ID
//	error: nil if succeed, error if fail
func (c *Client) CheckFileSystemExist(ctx context.Context, fsName string) (bool, string, error) {
	if fsName == "" {
		return false, "", errors.New("fsName is nil")
	}
	fileSystemList, err := c.ListFileSystems(ctx)
	if err != nil {
		logger.Errorf(ctx, "ListFileSystems failed: %s", err)
		return false, "", err
	}
	if len(fileSystemList.FileSystemList) == 0 {
		logger.Infof(ctx, "The user does not have a fileSystem")
		return false, "", nil
	}
	for _, fileSystem := range fileSystemList.FileSystemList {
		if fileSystem.Name == fsName {
			return true, fileSystem.FsID, nil
		}
	}
	logger.Infof(ctx, "The user does not have fileSystem %s", fsName)
	return false, "", nil
}

// CreateFileSystem - 创建文件系统
// PARAMS:
//   - ctx: The context to trace request
//   - fsName: 文件系统名字
//
// RETURNS:
//
//	string: 文件系统ID
//	error: nil if succeed, error if fail
func (c *Client) CreateFileSystem(ctx context.Context, fsName string) (string, error) {
	if fsName == "" {
		return "", errors.New("fsName is nil")
	}

	request := new(CreateFileSystemRequest)
	request.FsName = fsName
	postContent, err := json.Marshal(request)
	if err != nil {
		return "", err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v1/cfs", nil), bytes.NewBuffer(postContent))
	if err != nil {
		return "", err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return "", err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}

	var createFileSystemResponse CreateFileSystemResponse
	err = json.Unmarshal(bodyContent, &createFileSystemResponse)
	if err != nil {
		return "", err
	}
	return createFileSystemResponse.FsID, nil
}

// DropFileSystem - 获取用户的文件系统列表
// PARAMS:
//   - ctx: The context to trace request
//   - fsID: 文件系统ID
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) DropFileSystem(ctx context.Context, fsID string) error {
	req, err := bce.NewRequest("DELETE", c.GetURL("v1/cfs/"+fsID, nil), nil)
	if err != nil {
		return err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return err
	}
	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}
	return nil
}
