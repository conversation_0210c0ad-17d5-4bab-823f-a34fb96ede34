// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/11/20 , by <EMAIL>, create
*/
/*
实现 CFS mountTarget 相关方法
*/

package cfs

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

// ListMountTargets - 获取用户指定文件系统的挂载点列表
// PARAMS:
//   - ctx: The context to trace request
//   - fsID: 文件系统ID
//
// RETURNS:
//
//	ListMountTargetsResponse: 挂载点列表
//	error: nil if succeed, error if fail
func (c *Client) ListMountTargets(ctx context.Context, fsID string) (*ListMountTargetsResponse, error) {
	req, err := bce.NewRequest("GET", c.GetURL("v1/cfs/"+fsID, nil), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var listMountTargetsResponse ListMountTargetsResponse
	err = json.Unmarshal(bodyContent, &listMountTargetsResponse)
	if err != nil {
		return nil, err
	}
	return &listMountTargetsResponse, nil
}

// CheckMountTargetExist - 检查用户的挂载点是否存在
// PARAMS:
//   - ctx: The context to trace request
//   - fsID: 文件系统ID
//   - subnetID: 子网ID
//
// RETURNS:
//
//	bool: 挂载点是否存在
//	string: 挂载点地址
//	error: nil if succeed, error if fail
func (c *Client) CheckMountTargetExist(ctx context.Context, fsID, subnetID string) (bool, string, error) {
	mountTargetList, err := c.ListMountTargets(ctx, fsID)
	if err != nil {
		logger.Errorf(ctx, "ListMountTargets %s failed: %s", fsID, err)
		return false, "", err
	}
	if len(mountTargetList.MountTargetList) == 0 {
		logger.Infof(ctx, "The user fileSystem %s does not have a mountTargetList", fsID)
		return false, "", nil
	}
	for _, mountTarget := range mountTargetList.MountTargetList {
		if mountTarget.SubnetID == subnetID {
			return true, mountTarget.Domain, nil
		}
	}
	logger.Infof(ctx, "The user fileSystem %s does not have mountTarget in subnet %s", fsID, subnetID)
	return false, "", nil
}

// CreateMountTarget - 创建文件系统
// PARAMS:
//   - ctx: The context to trace request
//   - fsID: 文件系统ID
//   - vpcID: 私有网络ID
//   - subnetID: 子网ID
//
// RETURNS:
//
//	string: 挂载点地址
//	error: nil if succeed, error if fail
func (c *Client) CreateMountTarget(ctx context.Context, fsID, vpcID, subnetID string) (string, error) {
	if fsID == "" {
		return "", errors.New("fsID is nil")
	}
	if subnetID == "" {
		return "", errors.New("subnetID is nil")
	}

	request := new(CreateMountTargetRequest)
	request.VpcID = vpcID
	request.SubnetID = subnetID
	postContent, err := json.Marshal(request)
	if err != nil {
		return "", err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v1/cfs/"+fsID, nil), bytes.NewBuffer(postContent))
	if err != nil {
		return "", err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return "", err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}

	var createMountTargetResponse CreateMountTargetResponse
	err = json.Unmarshal(bodyContent, &createMountTargetResponse)
	if err != nil {
		return "", err
	}
	return createMountTargetResponse.Domain, nil
}

// DeleteMountTarget - 获取用户的文件系统列表
// PARAMS:
//   - ctx: The context to trace request
//   - fsID: 文件系统ID
//   - mountTargetID: 挂载点ID
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) DeleteMountTarget(ctx context.Context, fsID, mountTargetID string) error {
	req, err := bce.NewRequest("DELETE", c.GetURL("v1/cfs/"+fsID+"/"+mountTargetID, nil), nil)
	if err != nil {
		return err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return err
	}
	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}
	return nil
}
