package bce

import (
	"context"
	"time"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/retrypolicy"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"
)

func GenConfig(ctx context.Context, endpoint string, timeout int, region string) *Config {
	if endpoint == "" {
		logger.Errorf(ctx, "Generate bce.Config failed: endpoint is empty")
		return nil
	}

	return &Config{
		Checksum:    true,
		Endpoint:    endpoint,
		Timeout:     time.Duration(timeout) * time.Second,
		Region:      region,
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(ctx, 0, 0),
	}
}
