package bce

import (
	"encoding/json"
	"net/http"
	"testing"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/util"
)

// 遇到单测的机器无法联通 http://bcc.bj.baidubce.com
func testGetBodyContent(t *testing.T) {
	request, err := http.NewRequest("GET", "http://bcc.bj.baidubce.com", nil)

	if err != nil {
		t.Error(util.FormatTest("GetBodyContent", err.Error(), "nil"))
	}

	client := &http.Client{}
	resp, err := client.Do(request)

	if err != nil {
		t.Error(util.FormatTest("GetBodyContent", err.Error(), "nil"))
	}

	bceResponse := NewResponse(resp)
	bodyContent, err := bceResponse.GetBodyContent()

	if err != nil {
		t.Error(util.FormatTest("GetBodyContent", err.Error(), "nil"))
	}

	if bodyContent == nil {
		t.Error(util.FormatTest("GetBodyContent", "nil", "not nil"))
	} else if string(bodyContent) == "" {
		t.Error(util.FormatTest("GetBodyContent", "empty string", "none empty string"))
	}
}

func TestRemoveInvalidChar(t *testing.T) {
	jsonStr := "{\n" +
		"    \"marker\":\"\",\n" +
		"    \"isTruncated\":false,\n" +
		"    \"maxKeys\":1000,\n" +
		"    \"securityGroups\":[\n" +
		"        {\n" +
		"            \"id\":\"g-0013dd4jifzp\",\n" +
		"            \"name\":\"rule_生产全网开UDP5050\",\n" +
		"            \"vpcId\":\"vpc-ky18hzkt66ex\",\n" +
		"            \"desc\":\"UDP5050端口\n全网开放\",\n" +
		"            \"createdTime\":\"2019-11-22T02:45:39Z\",\n" +
		"            \"sgVersion\":0,\n" +
		"            \"rules\":[\n" +
		"                {\n" +
		"                    \"remark\":\"UDP5050端口\t全网开放\",\n" +
		"                    \"direction\":\"ingress\",\n" +
		"                    \"ethertype\":\"IPv4\",\n" +
		"                    \"portRange\":\"5050\",\n" +
		"                    \"sourceGroupId\":\"\",\n" +
		"                    \"sourceIp\":\"0.0.0.0/0\",\n" +
		"                    \"securityGroupId\":\"g-0013dd4jifzp\",\n" +
		"                    \"securityGroupRuleId\":\"r-ytp48qqjafcx\",\n" +
		"                    \"createdTime\":\"2019-12-10T08:38:28Z\",\n" +
		"                    \"updatedTime\":\"2019-12-10T08:38:28Z\",\n" +
		"                    \"protocol\":\"udp\"\n" +
		"                }\n" +
		"            ]\n" +
		"        }\n" +
		"    ]\n" +
		"}"
	type SecurityGroupRule struct {
		SecurityGroupID string `json:"securityGroupId"`
		SourceGroupID   string `json:"sourceGroupId"`
		SourceIP        string `json:"sourceIp"`
		DestGroupID     string `json:"destGroupId"`
		DestIP          string `json:"destIp"`
		PortRange       string `json:"portRange"`
		Remark          string `json:"remark"`
	}

	type SecurityGroup struct {
		ID    string              `json:"id"`
		Name  string              `json:"name"`
		Desc  string              `json:"desc"`
		VPCID string              `json:"vpcId"`
		Rules []SecurityGroupRule `json:"rules"`
	}
	type GetSecurityGroupsResponse struct {
		NextMarker     string          `json:"nextMarker"`
		Marker         string          `json:"marker"`
		MaxKeys        int64           `json:"maxKeys"`
		IsTruncated    bool            `json:"isTruncated"`
		SecurityGroups []SecurityGroup `json:"securityGroups"`
	}

	response := new(GetSecurityGroupsResponse)

	// t.Logf("json: %v", jsonStr)

	err := json.Unmarshal(RemoveInvalidChar([]byte(jsonStr)), response)
	if err != nil {
		t.Errorf("error: %s", err.Error())
	}
	t.Logf("response: %v", response)
}
