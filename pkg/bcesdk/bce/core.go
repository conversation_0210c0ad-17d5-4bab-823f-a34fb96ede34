package bce

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"os"
	"runtime"
	"sort"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/retrypolicy"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/util"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"
)

const (
	Version = "0.0.1"

	// ExpirationPeriodInSeconds 1800s is the default expiration period.
	ExpirationPeriodInSeconds = 1800
)

// DefaultUserAgent is the default value of http request UserAgent header.
//
// We can change it by specifying the UserAgent field of bce.Config.
var DefaultUserAgent = strings.Join([]string{
	"baidubce-sdk-go",
	Version,
	runtime.GOOS,
	runtime.Version(),
}, "/")

// Region contains all regions of Baidu Cloud.
var Region = map[string]string{
	"bj": "bj",
	"gz": "gz",
	"hk": "hk",
}

type Credentials struct {
	AccessKeyID     string `json:"AccessKeyID"`
	SecretAccessKey string `json:"SecretAccessKey"`
}

func NewCredentials(AccessKeyID, secretAccessKey string) *Credentials {
	return &Credentials{AccessKeyID, secretAccessKey}
}

func NewCredentialsFromFile(filePath string) (*Credentials, error) {
	bytes, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}
	var c *Credentials
	err = json.Unmarshal(bytes, &c)
	if err != nil {
		return nil, err
	}
	return c, nil
}

// Config contains all options for bce.Client.
type Config struct {
	*Credentials
	Region     string `json:"region"`
	Endpoint   string
	APIVersion string
	Protocol   string
	UserAgent  string
	ProxyHost  string
	ProxyPort  int

	// ConnectionTimeoutInMillis time.Duration // default value: 10 * time.Second in http.DefaultTransport
	MaxConnections int // default value: 2 in http.DefaultMaxIdleConnsPerHost

	Timeout     time.Duration // default value: 0 in http.Client
	RetryPolicy retrypolicy.RetryPolicy
	Checksum    bool

	// IAM 容灾配置
	// IAM Endpoint
	IAMEndpoint string

	// IAM 前置机 Endpoint
	IAMAgentEndpoint string

	// IAM 容灾开关
	IAMDisasterToleranceEnabled bool

	// 服务号，用于auth 认证+鉴权
	ServiceName string

	// 服务号密码，用于auth 认证+鉴权
	ServicePassword string

	// 要鉴权的服务，用于auth 鉴权
	ServiceIAMPermissionCheckName string

	// 服务角色，用于authorization签名
	ServiceRoleName string
}

// NewConfig create a config from credentials
func NewConfig(credentials *Credentials) *Config {
	return &Config{
		Credentials: credentials,
		Region:      Region["bj"],
	}
}

// NewCommonConfig - 通用 Config
func NewCommonConfig(ctx context.Context, endpoint string) *Config {
	config := &Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Endpoint: endpoint,
	}

	return config
}

// NewConfigFromFile create a new config from cloud config.
func NewConfigFromFile(filePath string) (*Config, error) {
	bytes, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}
	var c *Config
	err = json.Unmarshal(bytes, &c)
	if err != nil {
		return nil, err
	}
	return c, nil
}

// GetRegion gets region from bce.Config.
//
// If no region specified in bce.Config, the bj region will be return.
func (config *Config) GetRegion() string {
	region := config.Region

	if region == "" {
		region = Region["bj"]
	}

	return region
}

// WithAPIVersion set config apiVersion
func (config *Config) WithAPIVersion(ctx context.Context, apiVersion string) *Config {
	if apiVersion != "" {
		config.APIVersion = apiVersion
	}
	return config
}

// WithRetry set config RetryPolicy
func (config *Config) WithRetry(ctx context.Context, retry int) *Config {
	config.RetryPolicy = retrypolicy.NewIntervalRetryPolicy(ctx, retry, 0)

	return config
}

// GetUserAgent gets UserAgent from bce.Config.
//
// If no UserAgent specified in bce.Config, the bce.DefaultUserAgent will be return.
func (config *Config) GetUserAgent() string {
	userAgent := config.UserAgent

	if userAgent == "" {
		userAgent = DefaultUserAgent
	}

	return userAgent
}

// SignOption contains all signature options of Baidu Cloud API.
type SignOption struct {
	Timestamp                 string            `json:"timestamp"`
	ExpirationPeriodInSeconds int               `json:"expirationPeriodInSeconds"`
	Headers                   map[string]string `json:"headers"`
	HeadersToSign             []string          `json:"headerToSgin"`
	Credentials               *Credentials      `json:"credentials"`
	headersToSignSpecified    bool
	initialized               bool

	// custom authorization generating function, override ak/sk sign
	CustomSignFunc func(context.Context, *Request) `json:"-"`

	// for service requires specific UA (like cce-service)
	CustomUserAgent string

	// for PostForm
	PostForm bool
}

func NewSignOption(timestamp string, expirationPeriodInSeconds int,
	headers map[string]string, headersToSign []string) *SignOption {
	return &SignOption{
		Timestamp:                 timestamp,
		ExpirationPeriodInSeconds: expirationPeriodInSeconds,
		Headers:                   headers,
		HeadersToSign:             headersToSign,
		Credentials:               nil,
		headersToSignSpecified:    len(headersToSign) > 0,
		initialized:               false,
		CustomSignFunc:            nil,
	}
}

func NewSignOptionWithoutAuth() *SignOption {
	option := &SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")

	// don't add Authorization header
	option.CustomSignFunc = func(ctx context.Context, req *Request) {
		req.PrepareHeaders(option)
	}

	return option
}

func NewSignOptionWithSessionToken(ctx context.Context, sessionToken string) *SignOption {
	option := &SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")
	option.AddHeader("X-Bce-Security-Token", sessionToken)
	// don't add Authorization header
	option.CustomSignFunc = func(ctx context.Context, req *Request) {
		req.PrepareHeaders(option)
	}

	return option
}

// CheckSignOption returns a new empty bce.SignOption instance if no option specified.
func CheckSignOption(option *SignOption) *SignOption {
	if option == nil {
		return &SignOption{}
	}

	return option
}

// AddHeadersToSign adds some headers for authentication process of Baidu Cloud API.
//
// For details, please refer https://cloud.baidu.com/doc/Reference/AuthenticationMechanism.html#1.1.20.E6.A6.82.E8.BF.B0
func (option *SignOption) AddHeadersToSign(headers ...string) {
	if option.HeadersToSign == nil {
		option.HeadersToSign = []string{}
		option.HeadersToSign = append(option.HeadersToSign, headers...)
	} else {
		for _, header := range headers {
			if !util.Contains(option.HeadersToSign, header, true) {
				option.HeadersToSign = append(option.HeadersToSign, header)
			}
		}
	}
}

// AddHeader adds a header and it's value for authentication process of Baidu Cloud API.
//
// For details, please refer https://cloud.baidu.com/doc/Reference/AuthenticationMechanism.html#1.1.20.E6.A6.82.E8.BF.B0
func (option *SignOption) AddHeader(key, value string) {
	if option.Headers == nil {
		option.Headers = make(map[string]string)
		option.Headers[key] = value
	}

	if !util.MapContains(option.Headers, generateHeaderValidCompareFunc(key)) {
		option.Headers[key] = value
	}
}

// AddHeaders adds some headers for authentication process of Baidu Cloud API.
//
// For details, please refer https://cloud.baidu.com/doc/Reference/AuthenticationMechanism.html#1.1.20.E6.A6.82.E8.BF.B0
func (option *SignOption) AddHeaders(headers map[string]string) {
	if headers == nil {
		return
	}

	if option.Headers == nil {
		option.Headers = make(map[string]string)
	}

	for key, value := range headers {
		option.AddHeader(key, value)
	}
}

// Init to Initialize SignOption
func (option *SignOption) init() {
	if option.initialized {
		return
	}

	option.headersToSignSpecified = len(option.HeadersToSign) > 0

	if option.Timestamp == "" {
		option.Timestamp = util.TimeToUTCString(time.Now())
	}

	if option.ExpirationPeriodInSeconds <= 0 {
		option.ExpirationPeriodInSeconds = ExpirationPeriodInSeconds
	}

	if option.Headers == nil {
		option.Headers = make(map[string]string, 3)
	} else {
		util.MapKeyToLower(option.Headers)
	}

	util.SliceToLower(option.HeadersToSign)

	if !util.Contains(option.HeadersToSign, "host", true) {
		option.HeadersToSign = append(option.HeadersToSign, "host")
	}

	if !option.headersToSignSpecified {
		option.HeadersToSign = append(option.HeadersToSign, "x-bce-date")
		option.Headers["x-bce-date"] = option.Timestamp
	} else if util.Contains(option.HeadersToSign, "date", true) {
		if !util.MapContains(option.Headers, generateHeaderValidCompareFunc("date")) {
			option.Headers["date"] = time.Now().Format(time.RFC1123)
		} else {
			option.Headers["date"] = util.TimeStringToRFC1123(util.GetMapValue(option.Headers, "date", true))
		}
	} else {
		if !util.MapContains(option.Headers, generateHeaderValidCompareFunc("x-bce-date")) {
			option.Headers["x-bce-date"] = option.Timestamp
		}
	}

	option.initialized = true
}

func (option *SignOption) signedHeadersToString(req Request) string {
	headers := make([]string, 0, int(math.Max(float64(len(req.Header)), float64(len(option.HeadersToSign)))))

	// the headers got here must be identical with the ones generated by toCanonicalHeaderString
	if option.headersToSignSpecified {
		headers = append(headers, option.HeadersToSign...)
	} else {
		for key := range req.Header {
			if isCanonicalHeader(key) {
				headers = append(headers, strings.ToLower(key))
			}
		}
	}

	sort.Strings(headers)

	return strings.Join(headers, ";")
}

// GenerateAuthorization generates authorization code for authorization process of Baidu Cloud API.
func GenerateAuthorization(credentials Credentials, req Request, option *SignOption) string {
	if option == nil {
		option = &SignOption{}
	}
	option.init()

	authorization := "bce-auth-v1/" + credentials.AccessKeyID
	authorization += "/" + option.Timestamp
	authorization += "/" + strconv.Itoa(option.ExpirationPeriodInSeconds)
	signature := sign(credentials, req, option)
	authorization += "/" + option.signedHeadersToString(req) + "/" + signature

	req.setHeader("Authorization", authorization)

	return authorization
}

// Client is the base client implemention for Baidu Cloud API.
type Client struct {
	*Config
	httpClient *http.Client
	debug      bool
}

func NewClient(config *Config) *Client {
	return &Client{config, newHTTPClient(config), false}
}

// SetDebug enables debug mode of bce.Client instance.
func (c *Client) SetDebug(debug bool) {
	c.debug = debug
}

// SetHttpClient set http client for bce.Client use by ut
func (c *Client) SetHTTPClient(client *http.Client) {
	c.httpClient = client
}

func newHTTPClient(config *Config) *http.Client {
	transport := new(http.Transport)

	if defaultTransport, ok := http.DefaultTransport.(*http.Transport); ok {
		transport.Proxy = defaultTransport.Proxy
		transport.Dial = defaultTransport.Dial
		transport.TLSHandshakeTimeout = defaultTransport.TLSHandshakeTimeout
	}

	if config.ProxyHost != "" {
		host := config.ProxyHost

		if config.ProxyPort > 0 {
			host += ":" + strconv.Itoa(config.ProxyPort)
		}

		proxyURL, err := url.Parse(util.HostToURL(host, "http"))

		if err != nil {
			panic(err)
		}

		transport.Proxy = http.ProxyURL(proxyURL)
	}

	/*
		if c.ConnectionTimeout > 0 {
			transport.TLSHandshakeTimeout = c.ConnectionTimeout
		}
	*/

	if config.MaxConnections > 0 {
		transport.MaxIdleConnsPerHost = config.MaxConnections
	}

	return &http.Client{
		Transport: transport,
		Timeout:   config.Timeout,
	}
}

// GetURL generates the full URL of http request for Baidu Cloud API.
func (c *Client) GetURL(host, uriPath string, params map[string]string) string {
	if strings.Index(uriPath, "/") == 0 {
		uriPath = uriPath[1:]
	}

	if c.APIVersion != "" {
		uriPath = fmt.Sprintf("%s/%s", c.APIVersion, uriPath)
	}

	return util.GetURL(c.Protocol, host, uriPath, params)
}

// SessionTokenRequest contains all options for STS（Security Token Service）of Baidu Cloud API.
//
// For details, please refer https://cloud.baidu.com/doc/BOS/API.html#STS.E7.AE.80.E4.BB.8B
type SessionTokenRequest struct {
	DurationSeconds   int                     `json:"durationSeconds"`
	ID                string                  `json:"id"`
	AccessControlList []AccessControlListItem `json:"accessControlList"`
}

// AccessControlListItem contains sub options for bce.SessionTokenRequest
//
// For details, please refer https://cloud.baidu.com/doc/BOS/API.html#STS.E7.AE.80.E4.BB.8B
type AccessControlListItem struct {
	Eid        string   `json:"eid"`
	Service    string   `json:"service"`
	Region     string   `json:"region"`
	Effect     string   `json:"effect"`
	Resource   []string `json:"resource"`
	Permission []string `json:"permission"`
}

// SessionTokenResponse contains all response fields for STS（Security Token Service）of Baidu Cloud API.
//
// For details, please refer https://cloud.baidu.com/doc/BOS/API.html#STS.E7.AE.80.E4.BB.8B
type SessionTokenResponse struct {
	AccessKeyID     string `json:"accessKeyId"`
	SecretAccessKey string `json:"secretAccessKey"`
	SessionToken    string `json:"sessionToken"`
	CreateTime      string `json:"createTime"`
	Expiration      string `json:"expiration"`
	UserID          string `json:"userId"`
}

// GetSessionToken gets response for STS（Security Token Service）of Baidu Cloud API.
//
// For details, please refer https://cloud.baidu.com/doc/BOS/API.html#STS.E7.AE.80.E4.BB.8B
func (c *Client) GetSessionToken(ctx context.Context, sessionTokenRequest SessionTokenRequest,
	option *SignOption) (*SessionTokenResponse, error) {
	var params map[string]string

	if sessionTokenRequest.DurationSeconds > 0 {
		params = map[string]string{"durationSeconds": strconv.Itoa(sessionTokenRequest.DurationSeconds)}
	}

	body, err := util.ToJSON(sessionTokenRequest, "id", "accessControlList")

	if err != nil {
		return nil, err
	}

	uriPath := "sessionToken"

	if c.APIVersion == "" {
		uriPath = "v1/" + uriPath
	}

	req, err := NewRequest("POST", c.GetURL("sts.bj.baidubce.com", uriPath, params), bytes.NewBuffer(body))

	if err != nil {
		return nil, err
	}

	option = CheckSignOption(option)
	option.AddHeader("Content-Type", "application/json")

	resp, err := c.SendRequest(ctx, req, option)

	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}

	var sessionTokenResponse *SessionTokenResponse
	err = json.Unmarshal(bodyContent, &sessionTokenResponse)

	if err != nil {
		return nil, err
	}

	return sessionTokenResponse, nil
}

// SendRequest sends a http request to the endpoint of Baidu Cloud API.
func (c *Client) SendRequest(ctx context.Context, req *Request, option *SignOption) (bceResponse *Response, err error) {
	if req == nil {
		return nil, errors.New("request is nil")
	}

	if option == nil {
		option = &SignOption{}
	}

	// unify logger requestID and X-Bce-Request-Id header if not set
	// loggerRequestID := logger.GetRequestID(ctx)
	// bceRequestIDHeaderKey := "x-bce-request-id"
	// bceRequestID := req.Header.Get(bceRequestIDHeaderKey)
	// if loggerRequestID == "" && bceRequestID != "" {
	// 	ctx = logger.WithRequestID(ctx, bceRequestID)
	// }
	// if loggerRequestID != "" && bceRequestID == "" {
	// 	option.AddHeader(bceRequestIDHeaderKey, loggerRequestID)
	// }
	// if loggerRequestID == "" && bceRequestID == "" {
	// 	reqID := logger.GetUUID()
	// 	ctx = logger.WithRequestID(ctx, reqID)
	// 	option.AddHeader(bceRequestIDHeaderKey, reqID)
	// }

	// 保证 x-bec-request-id 为随机串
	bceRequestIDHeaderKey := "x-bce-request-id"
	bceRequestID := req.Header.Get(bceRequestIDHeaderKey)
	if bceRequestID == "" {
		bceRequestID = logger.GetUUID()
	}

	// 日志 ctx 仍然使用 ctx 的 requestID
	loggerRequestID := logger.GetRequestID(ctx)
	if loggerRequestID == "" {
		ctx = logger.WithRequestID(ctx, bceRequestID)
	}

	req.Header.Set(bceRequestIDHeaderKey, bceRequestID)
	option.AddHeader(bceRequestIDHeaderKey, bceRequestID)
	option.Headers[bceRequestIDHeaderKey] = bceRequestID // option.AddHeader 不会覆盖已有 Key

	if len(option.CustomUserAgent) > 0 {
		option.AddHeader("User-Agent", option.CustomUserAgent)
	} else {
		option.AddHeader("User-Agent", c.GetUserAgent())
	}

	option.AddHeader("Content-Type", "application/json")
	if c.RetryPolicy == nil {
		c.RetryPolicy = retrypolicy.NewDefaultRetryPolicy(3, 20)
	}

	var buf []byte
	if req.Body != nil {
		buf, _ = io.ReadAll(req.Body)
	}

	for i := 0; i < 5; i++ {
		bceResponse, err = nil, nil

		// Use CustomSignFunc to sign request if exists
		if option.CustomSignFunc != nil {
			option.CustomSignFunc(ctx, req)
		} else {
			if option.Credentials != nil {
				GenerateAuthorization(*option.Credentials, *req, option)
			} else {
				if c.Credentials == nil {
					return nil, errors.New("Credentials is nil in both Client and SignOption")
				}
				GenerateAuthorization(*c.Credentials, *req, option)
			}
		}

		if c.debug {
			logger.Infof(ctx, "Request: httpMethod = %s, requestUrl = %s, requestHeader = %v, requestBody = %s",
				req.Method, req.URL.String(), req.Header, string(buf))
		}
		t0 := time.Now()
		req.Body = io.NopCloser(bytes.NewBuffer(buf))

		rawRequest := req.Raw()
		if ctx != nil {
			rawRequest = rawRequest.WithContext(ctx)
		}

		if option.PostForm {
			rawRequest.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		}

		resp, httpError := c.httpClient.Do(rawRequest)
		t1 := time.Now()
		bceResponse = NewResponse(resp)
		statusCode := -1
		if resp != nil {
			statusCode = resp.StatusCode
		}
		logger.Infof(ctx, "http request: %s  do use time: %v", req.URL.String(), t1.Sub(t0))
		logger.Infof(ctx, "Response: status code = %d, httpMethod = %s, requestUrl = %s",
			statusCode, req.Method, req.URL.String())
		if c.debug {
			resString := ""
			var resHead http.Header
			if resp != nil {
				re, err := bceResponse.GetBodyContent()
				if err != nil {
					logger.Infof(ctx, "getbodycontent error: %v", err)
				}
				// print 16KB resp at most to avoid log flood
				maxBytes := 1024 * 16
				if l := len(re); l < maxBytes {
					resString = string(re)
				} else {
					resString = string(re[:maxBytes]) + fmt.Sprintf(" ... (%d more bytes)", l-maxBytes)
				}
				resHead = resp.Header
			}

			logger.Infof(ctx, "Response Header:  = %v", resHead)
			logger.Infof(ctx, "Response body:  = %s", resString)
		} else {
			// 打印简单的信息, 便于排查问题
			requestID := bceRequestID
			if resp != nil {
				requestID = resp.Header.Get(bceRequestIDHeaderKey)
			}
			logger.Infof(ctx, "%s = %s", bceRequestIDHeaderKey, requestID)
		}

		if httpError != nil {
			duration := c.RetryPolicy.GetDelayBeforeNextRetry(ctx, httpError, i+1)
			if duration <= 0 {
				err = httpError
				return bceResponse, err
			}
			time.Sleep(duration)
			continue
		}

		if resp.StatusCode >= http.StatusBadRequest {
			err = buildError(bceResponse)
		}

		if err == nil {
			return bceResponse, err
		}

		duration := c.RetryPolicy.GetDelayBeforeNextRetry(ctx, err, i+1)

		if duration <= 0 {
			return bceResponse, err
		}

		time.Sleep(duration)
	}

	return nil, errors.New("SendRequest beyond max retrycount")
}

// GenerateClientToken generates the Client Token with random string
func (c *Client) GenerateClientToken() string {
	return util.CreateRandomString()
}

func generateHeaderValidCompareFunc(headerKey string) func(string, string) bool {
	return func(key, value string) bool {
		return strings.ToLower(key) == strings.ToLower(headerKey) && value != ""
	}
}

// sign returns signed signature.
func sign(credentials Credentials, req Request, option *SignOption) string {
	signingKey := getSigningKey(credentials, option)
	req.PrepareHeaders(option)
	canonicalRequest := req.canonical(option)
	signature := util.HmacSha256Hex(signingKey, canonicalRequest)

	return signature
}

func getSigningKey(credentials Credentials, option *SignOption) string {
	var authStringPrefix = fmt.Sprintf("bce-auth-v1/%s", credentials.AccessKeyID)
	authStringPrefix += "/" + option.Timestamp
	authStringPrefix += "/" + strconv.Itoa(option.ExpirationPeriodInSeconds)

	return util.HmacSha256Hex(credentials.SecretAccessKey, authStringPrefix)
}
