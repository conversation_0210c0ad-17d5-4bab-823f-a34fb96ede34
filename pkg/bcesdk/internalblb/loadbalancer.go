// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/11/25 16:30:00, by <EMAIL>, create
*/
/*
本 Package 包含 BLB InternalAPI SDK 的 Interface 定义和实现
参考文档: http://gollum.baidu.com/BLB#BLB-API
*/

package internalblb

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// CreateLoadBalancer create a application loadbalancer
func (c *Client) CreateLoadBalancer(ctx context.Context, args *CreateLoadBalancerArgs, option *bce.SignOption) (*CreateLoadBalancerResponse, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("open-api/v2/blb", params),
		bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var blbsResp *CreateLoadBalancerResponse
	err = json.Unmarshal(bodyContent, &blbsResp)
	if err != nil {
		return nil, err
	}

	return blbsResp, nil
}

// ListLoadBalancer - 按条件 List LoadBalancer
//
// PARAMS:
//   - ctx: The context to trace request
//   - args: List 参数
//   - option: SignOption
//
// RETURNS:
//
//	[]LoadBalancer: LoadBalancer 列表
//	error: nil if succeed, error if fail
//	注意: 如果对应条件的 LB 不存在, 返回 []*LoadBalancer{}, nil
func (c *Client) ListLoadBalancer(ctx context.Context, args *ListLoadBalancersArgs, option *bce.SignOption) (*ListLoadBalancersResponse, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	if args != nil {
		params["id"] = args.ID
		params["name"] = args.Name
		params["ip"] = args.IP
		params["vip"] = args.VIP
		params["ovip"] = args.OVIP
		params["eip"] = args.EIP
		params["bccId"] = args.BCCID
		params["vpcId"] = args.VPCID
		params["exactMatch"] = string(args.ExactMatch)
	}

	req, err := bce.NewRequest("GET", c.GetURL("open-api/v1/blb", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var blbsResp *ListLoadBalancersResponse
	err = json.Unmarshal(bodyContent, &blbsResp)
	if err != nil {
		return nil, err
	}

	if blbsResp == nil {
		return nil, errors.New("ListLoadBalancer failed: resp is nil")
	}

	return blbsResp, nil
}

// ListLoadBalancerByName 通过名字获取 LoadBalancer 列表, 可能出现重名的 LoadBalancer
//
// PARAMS:
//   - ctx: The context to trace request
//   - name: LoadBalancer 名字
//   - option: SignOption
//
// RETURNS:
//
//	[]*LoadBalancer: LoadBalancer 列表
//	error: nil if succeed, error if fail
func (c *Client) ListLoadBalancerByName(ctx context.Context, name string, option *bce.SignOption) (*ListLoadBalancersResponse, error) {
	if name == "" {
		return nil, errors.New("GetLoadBalancersByName failed: name is nil")
	}

	// 必须为精确匹配
	args := &ListLoadBalancersArgs{
		Name:       name,
		ExactMatch: ExactMatchTrue,
	}

	return c.ListLoadBalancer(ctx, args, option)
}

// DescribeLoadBalancerByID 通过 ID 获取 LoadBalancer 列表, 如果存在则唯一
//
// PARAMS:
//   - ctx: The context to trace request
//   - id: LoadBalancer ID
//   - option: SignOption
//
// RETURNS:
//
//	*LoadBalancer: BLB ID 对应的 BLB
//	error: nil if succeed, error if fail
//	注意: 如果 ID 对应 BLB 不存在, 返回 nil, nil
func (c *Client) DescribeLoadBalancerByID(ctx context.Context, id string, option *bce.SignOption) (*LoadBalancer, error) {
	if id == "" {
		return nil, errors.New("GetLoadBalancersByName failed: id is nil")
	}

	// 必须为精确匹配
	args := &ListLoadBalancersArgs{
		ID:         id,
		ExactMatch: ExactMatchTrue,
	}

	resp, err := c.ListLoadBalancer(ctx, args, option)
	if err != nil {
		return nil, err
	}

	if resp == nil {
		return nil, errors.New("ListLoadBalancer faield: resp is nil")
	}

	if len(resp.BLBList) > 1 {
		return nil, errors.New("ListLoadBalancer faield: len(resp.BLBList) > 0")
	}

	if len(resp.BLBList) == 1 {
		return resp.BLBList[0], nil
	}

	return nil, nil
}

// GetLoadBalancer - 通过 BLB ID 查询 BLB
// id = 长 ID 或 短 ID 都可以生效
// 接口文档: http://gollum.baidu.com/BLB#BLB-query
func (c *Client) GetLoadBalancer(ctx context.Context, id string, option *bce.SignOption) (*LoadBalancer, error) {
	if id == "" {
		return nil, errors.New("GetLoadBalancersByName failed: id is nil")
	}

	// GET META_SERVER_URL/open-api/v1/blb/{blb_id}
	url := fmt.Sprintf("open-api/v1/blb/%s", id)
	req, err := bce.NewRequest("GET", c.GetURL(url, nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var blb *LoadBalancer
	err = json.Unmarshal(bodyContent, &blb)
	if err != nil {
		return nil, err
	}

	if blb == nil {
		return nil, errors.New("GetLoadBalancer failed: resp is nil")
	}

	return blb, nil
}

// DeleteLoadBalancer delete loadbalancer
func (c *Client) DeleteLoadBalancer(ctx context.Context, id string, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	url := fmt.Sprintf("open-api/v1/blb/%s", id)
	req, err := bce.NewRequest("DELETE", c.GetURL(url, params), nil)
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()

	return err
}
