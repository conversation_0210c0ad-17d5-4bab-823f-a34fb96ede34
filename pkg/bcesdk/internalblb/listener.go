// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/11/25 16:30:00, by <EMAIL>, create
*/
/*
本 Package 包含 BLB InternalAPI SDK 的 Interface 定义和实现
参考文档: http://gollum.baidu.com/BLB#BLB-API
*/

package internalblb

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// CreateListener - 创建 Listener
//
// PARAMS:
//   - ctx: The context to trace request
//   - lbShortID: BLB 短 ID
//   - args: 创建 Listener 参数
//   - option: *bce.SignOption
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) CreateListener(ctx context.Context, lbShortID string, args *CreateListenerArgs, option *bce.SignOption) error {
	if args.Type == "" || args.Scheduler == "" {
		return errors.New("createListener failed: type, port, scheduler is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("open-api/v1/blb/%s/listener", lbShortID)
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

// ListListener - 根据 Type 或 Port 获取 Listener 列表
//
// PARAMS:
//   - ctx: The context to trace request
//   - args: ListListenerArgs
//   - option: *bce.SignOption
//
// RETURNS:
//
//	*ListListenerResponse: Listener 列表
//	error: nil if succeed, error if fail
//	注意: 如果符合条件的 Listener 不存在, 返回为 ListListenerResponse.ListenerList{}
func (c *Client) ListListener(ctx context.Context, lbShortID string, args *ListListenerArgs, option *bce.SignOption) (*ListListenerResponse, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	if args.Type != "" {
		params["type"] = string(args.Type)
	}

	if args.Port != 0 {
		params["port"] = fmt.Sprintf("%d", args.Port)
	}

	url := fmt.Sprintf("open-api/v1/blb/%s/listener", lbShortID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var blbsResp *ListListenerResponse
	err = json.Unmarshal(bodyContent, &blbsResp)
	if err != nil {
		return nil, err
	}

	return blbsResp, nil
}

// DescribeListener - 根据 Type 和 Port 查询 Listener
//
// PARAMS:
//   - ctx: The context to trace request
//   - lbShortID: lb 短 ID
//   - port: listener 端口
//   - option: *bce.SignOption
//
// RETURNS:
//
//	*Listener: The URL's HTML string content
//	error: nil if succeed, error if fail
//	注意: Listener 不存在, 返回 nil, nil
func (c *Client) DescribeListener(ctx context.Context, lbShortID string, port int, option *bce.SignOption) (*Listener, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	url := fmt.Sprintf("open-api/v1/blb/%s/listener/%d", lbShortID, port)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		// 如果对应的 Listener 不存在, 返回 nil, nil
		if strings.Contains(err.Error(), "ListenerNotFound") {
			return nil, nil
		}
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var blbsResp *Listener
	err = json.Unmarshal(bodyContent, &blbsResp)
	if err != nil {
		return nil, err
	}

	return blbsResp, nil
}

// DeleteListener - 删除 BLB 指定端口 Listener
//
// PARAMS:
//   - ctx: The context to trace request
//   - lbShortID: lb 短 ID
//   - port: listener 端口
//   - option: *bce.SignOption
//
// RETURNS:
//
//	error: nil if succeed, error if fail
//	注意: 如果对应的 Listener 不存在, 会报错
func (c *Client) DeleteListener(ctx context.Context, lbShortID string, port int32, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	url := fmt.Sprintf("open-api/v1/blb/%s/listener/%d", lbShortID, port)
	req, err := bce.NewRequest("DELETE", c.GetURL(url, params), nil)
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}
