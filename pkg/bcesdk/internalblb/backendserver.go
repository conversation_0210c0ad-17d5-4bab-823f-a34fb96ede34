// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/11/25 16:30:00, by <EMAIL>, create
*/
/*
本 Package 包含 BLB InternalAPI SDK 的 Interface 定义和实现
参考文档: http://gollum.baidu.com/BLB#BLB-API
*/

package internalblb

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// CreateBackendServer - 创建 BLB BackendServer
//
// PARAMS:
//   - ctx: The context to trace request
//   - lbShortID: BLB 短 ID
//   - args: 创建 BakcendServer 参数
//   - option: *bce.SignOption
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) CreateBackendServer(ctx context.Context, lbShortID string, args *CreateBackendServerArgs, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("open-api/v1/blb/%s/backendserver", lbShortID)
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

// ListBackendServer - 获取 BackendServer 列表
//
// PARAMS:
//   - ctx: The context to trace request
//   - lbShortID: BLB 短 ID
//   - args: 创建 BakcendServer 参数
//   - option: *bce.SignOption
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) ListBackendServer(ctx context.Context, lbShortID string, option *bce.SignOption) (*ListBackendServerResponse, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	url := fmt.Sprintf("open-api/v1/blb/%s/backendserver", lbShortID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var blbsResp *ListBackendServerResponse
	err = json.Unmarshal(bodyContent, &blbsResp)
	if err != nil {
		return nil, err
	}

	return blbsResp, nil
}

// BatchDeleteBackendServer - 批量删除 BackendServers
//
// PARAMS:
//   - ctx: The context to trace request
//   - lbShortID: BLB 短 ID
//   - option: *bce.SignOption
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) BatchDeleteBackendServer(ctx context.Context, lbShortID string, instanceUUIDList []string, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
		"action":      "batchdelete",
	}

	postContent, err := json.Marshal(map[string][]string{
		"backendServerIdList": instanceUUIDList,
	})
	if err != nil {
		return err
	}

	url := fmt.Sprintf("json-api/v2/blb/%s/backendservers", lbShortID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}
