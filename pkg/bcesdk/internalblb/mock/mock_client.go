// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/internalblb (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	internalblb "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/internalblb"
	reflect "reflect"
)

// MockInterface is a mock of Interface interface
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// BatchDeleteBackendServer mocks base method
func (m *MockInterface) BatchDeleteBackendServer(arg0 context.Context, arg1 string, arg2 []string, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteBackendServer", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDeleteBackendServer indicates an expected call of BatchDeleteBackendServer
func (mr *MockInterfaceMockRecorder) BatchDeleteBackendServer(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteBackendServer", reflect.TypeOf((*MockInterface)(nil).BatchDeleteBackendServer), arg0, arg1, arg2, arg3)
}

// CreateBackendServer mocks base method
func (m *MockInterface) CreateBackendServer(arg0 context.Context, arg1 string, arg2 *internalblb.CreateBackendServerArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBackendServer", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBackendServer indicates an expected call of CreateBackendServer
func (mr *MockInterfaceMockRecorder) CreateBackendServer(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBackendServer", reflect.TypeOf((*MockInterface)(nil).CreateBackendServer), arg0, arg1, arg2, arg3)
}

// CreateListener mocks base method
func (m *MockInterface) CreateListener(arg0 context.Context, arg1 string, arg2 *internalblb.CreateListenerArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateListener", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateListener indicates an expected call of CreateListener
func (mr *MockInterfaceMockRecorder) CreateListener(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateListener", reflect.TypeOf((*MockInterface)(nil).CreateListener), arg0, arg1, arg2, arg3)
}

// CreateLoadBalancer mocks base method
func (m *MockInterface) CreateLoadBalancer(arg0 context.Context, arg1 *internalblb.CreateLoadBalancerArgs, arg2 *bce.SignOption) (*internalblb.CreateLoadBalancerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLoadBalancer", arg0, arg1, arg2)
	ret0, _ := ret[0].(*internalblb.CreateLoadBalancerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLoadBalancer indicates an expected call of CreateLoadBalancer
func (mr *MockInterfaceMockRecorder) CreateLoadBalancer(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLoadBalancer", reflect.TypeOf((*MockInterface)(nil).CreateLoadBalancer), arg0, arg1, arg2)
}

// DeleteListener mocks base method
func (m *MockInterface) DeleteListener(arg0 context.Context, arg1 string, arg2 int32, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteListener", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteListener indicates an expected call of DeleteListener
func (mr *MockInterfaceMockRecorder) DeleteListener(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteListener", reflect.TypeOf((*MockInterface)(nil).DeleteListener), arg0, arg1, arg2, arg3)
}

// DeleteLoadBalancer mocks base method
func (m *MockInterface) DeleteLoadBalancer(arg0 context.Context, arg1 string, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteLoadBalancer", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteLoadBalancer indicates an expected call of DeleteLoadBalancer
func (mr *MockInterfaceMockRecorder) DeleteLoadBalancer(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLoadBalancer", reflect.TypeOf((*MockInterface)(nil).DeleteLoadBalancer), arg0, arg1, arg2)
}

// DescribeListener mocks base method
func (m *MockInterface) DescribeListener(arg0 context.Context, arg1 string, arg2 int, arg3 *bce.SignOption) (*internalblb.Listener, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeListener", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*internalblb.Listener)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeListener indicates an expected call of DescribeListener
func (mr *MockInterfaceMockRecorder) DescribeListener(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeListener", reflect.TypeOf((*MockInterface)(nil).DescribeListener), arg0, arg1, arg2, arg3)
}

// DescribeLoadBalancerByID mocks base method
func (m *MockInterface) DescribeLoadBalancerByID(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*internalblb.LoadBalancer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeLoadBalancerByID", arg0, arg1, arg2)
	ret0, _ := ret[0].(*internalblb.LoadBalancer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeLoadBalancerByID indicates an expected call of DescribeLoadBalancerByID
func (mr *MockInterfaceMockRecorder) DescribeLoadBalancerByID(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeLoadBalancerByID", reflect.TypeOf((*MockInterface)(nil).DescribeLoadBalancerByID), arg0, arg1, arg2)
}

// GetLoadBalancer mocks base method
func (m *MockInterface) GetLoadBalancer(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*internalblb.LoadBalancer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLoadBalancer", arg0, arg1, arg2)
	ret0, _ := ret[0].(*internalblb.LoadBalancer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoadBalancer indicates an expected call of GetLoadBalancer
func (mr *MockInterfaceMockRecorder) GetLoadBalancer(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoadBalancer", reflect.TypeOf((*MockInterface)(nil).GetLoadBalancer), arg0, arg1, arg2)
}

// ListBackendServer mocks base method
func (m *MockInterface) ListBackendServer(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*internalblb.ListBackendServerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBackendServer", arg0, arg1, arg2)
	ret0, _ := ret[0].(*internalblb.ListBackendServerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBackendServer indicates an expected call of ListBackendServer
func (mr *MockInterfaceMockRecorder) ListBackendServer(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBackendServer", reflect.TypeOf((*MockInterface)(nil).ListBackendServer), arg0, arg1, arg2)
}

// ListListener mocks base method
func (m *MockInterface) ListListener(arg0 context.Context, arg1 string, arg2 *internalblb.ListListenerArgs, arg3 *bce.SignOption) (*internalblb.ListListenerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListListener", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*internalblb.ListListenerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListListener indicates an expected call of ListListener
func (mr *MockInterfaceMockRecorder) ListListener(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListListener", reflect.TypeOf((*MockInterface)(nil).ListListener), arg0, arg1, arg2, arg3)
}

// ListLoadBalancer mocks base method
func (m *MockInterface) ListLoadBalancer(arg0 context.Context, arg1 *internalblb.ListLoadBalancersArgs, arg2 *bce.SignOption) (*internalblb.ListLoadBalancersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListLoadBalancer", arg0, arg1, arg2)
	ret0, _ := ret[0].(*internalblb.ListLoadBalancersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListLoadBalancer indicates an expected call of ListLoadBalancer
func (mr *MockInterfaceMockRecorder) ListLoadBalancer(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListLoadBalancer", reflect.TypeOf((*MockInterface)(nil).ListLoadBalancer), arg0, arg1, arg2)
}

// ListLoadBalancerByName mocks base method
func (m *MockInterface) ListLoadBalancerByName(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*internalblb.ListLoadBalancersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListLoadBalancerByName", arg0, arg1, arg2)
	ret0, _ := ret[0].(*internalblb.ListLoadBalancersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListLoadBalancerByName indicates an expected call of ListLoadBalancerByName
func (mr *MockInterfaceMockRecorder) ListLoadBalancerByName(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListLoadBalancerByName", reflect.TypeOf((*MockInterface)(nil).ListLoadBalancerByName), arg0, arg1, arg2)
}

// SetDebug mocks base method
func (m *MockInterface) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug
func (mr *MockInterfaceMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), arg0)
}
