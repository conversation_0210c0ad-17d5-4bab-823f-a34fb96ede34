package testutil

import (
	"net/http"
	"net/http/httptest"

	"github.com/gorilla/mux"
)

type TestEnv struct {
	Method  string
	Path    string
	Handler func(w http.ResponseWriter, r *http.Request)
}

func SetupTestEnv(envs []*TestEnv) *httptest.Server {
	r := mux.NewRouter()
	for _, env := range envs {
		if env == nil {
			continue
		}
		r.<PERSON>le<PERSON>(env.Path, env.Handler).Methods(env.Method)
	}

	return httptest.NewServer(r)
}

func TearDownTestEnv(s *httptest.Server) {
	if s != nil {
		s.Close()
	}
}
