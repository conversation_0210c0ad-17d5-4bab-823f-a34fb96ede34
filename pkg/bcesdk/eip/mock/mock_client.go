// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	eip "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eip"
	reflect "reflect"
)

// MockInterface is a mock of Interface interface
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// BindEIP mocks base method
func (m *MockInterface) BindEIP(arg0 context.Context, arg1 string, arg2 *eip.BindEIPArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindEIP", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// BindEIP indicates an expected call of BindEIP
func (mr *MockInterfaceMockRecorder) BindEIP(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindEIP", reflect.TypeOf((*MockInterface)(nil).BindEIP), arg0, arg1, arg2, arg3)
}

// CreateEIP mocks base method
func (m *MockInterface) CreateEIP(arg0 context.Context, arg1 *eip.CreateEIPArgs, arg2 *bce.SignOption) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateEIP", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateEIP indicates an expected call of CreateEIP
func (mr *MockInterfaceMockRecorder) CreateEIP(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEIP", reflect.TypeOf((*MockInterface)(nil).CreateEIP), arg0, arg1, arg2)
}

// CreateEIPWithClientToken mocks base method
func (m *MockInterface) CreateEIPWithClientToken(arg0 context.Context, arg1 *eip.CreateEIPArgs, arg2 string, arg3 *bce.SignOption) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateEIPWithClientToken", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateEIPWithClientToken indicates an expected call of CreateEIPWithClientToken
func (mr *MockInterfaceMockRecorder) CreateEIPWithClientToken(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEIPWithClientToken", reflect.TypeOf((*MockInterface)(nil).CreateEIPWithClientToken), arg0, arg1, arg2, arg3)
}

// DeleteEIP mocks base method
func (m *MockInterface) DeleteEIP(arg0 context.Context, arg1 string, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteEIP", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteEIP indicates an expected call of DeleteEIP
func (mr *MockInterfaceMockRecorder) DeleteEIP(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEIP", reflect.TypeOf((*MockInterface)(nil).DeleteEIP), arg0, arg1, arg2)
}

// DeleteEIPWithDeletionOption mocks base method
func (m *MockInterface) DeleteEIPWithDeletionOption(arg0 context.Context, arg1 string, arg2 eip.EIPDeletionOption, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteEIPWithDeletionOption", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteEIPWithDeletionOption indicates an expected call of DeleteEIPWithDeletionOption
func (mr *MockInterfaceMockRecorder) DeleteEIPWithDeletionOption(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEIPWithDeletionOption", reflect.TypeOf((*MockInterface)(nil).DeleteEIPWithDeletionOption), arg0, arg1, arg2, arg3)
}

// GetEIPs mocks base method
func (m *MockInterface) GetEIPs(arg0 context.Context, arg1 *eip.GetEIPsArgs, arg2 *bce.SignOption) ([]*eip.EIP, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEIPs", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*eip.EIP)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEIPs indicates an expected call of GetEIPs
func (mr *MockInterfaceMockRecorder) GetEIPs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEIPs", reflect.TypeOf((*MockInterface)(nil).GetEIPs), arg0, arg1, arg2)
}

// GetRecycleEIPs mocks base method
func (m *MockInterface) GetRecycleEIPs(arg0 context.Context, arg1 *eip.GetEIPsArgs, arg2 *bce.SignOption) ([]*eip.RecycleEIP, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecycleEIPs", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*eip.RecycleEIP)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecycleEIPs indicates an expected call of GetRecycleEIPs
func (mr *MockInterfaceMockRecorder) GetRecycleEIPs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecycleEIPs", reflect.TypeOf((*MockInterface)(nil).GetRecycleEIPs), arg0, arg1, arg2)
}

// DeleteRecycleEIP mocks base method
func (m *MockInterface) DeleteRecycleEIP(arg0 context.Context, arg1 string, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRecycleEIP", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteRecycleEIP indicates an expected call of DeleteRecycleEIP
func (mr *MockInterfaceMockRecorder) DeleteRecycleEIP(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRecycleEIP", reflect.TypeOf((*MockInterface)(nil).DeleteRecycleEIP), arg0, arg1, arg2)
}

// QueryEIPPrice mocks base method
func (m *MockInterface) QueryEIPPrice(arg0 context.Context, arg1 *eip.QueryEIPPriceRequest, arg2 *bce.SignOption) (*eip.QueryEIPPriceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryEIPPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(*eip.QueryEIPPriceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryEIPPrice indicates an expected call of QueryEIPPrice
func (mr *MockInterfaceMockRecorder) QueryEIPPrice(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryEIPPrice", reflect.TypeOf((*MockInterface)(nil).QueryEIPPrice), arg0, arg1, arg2)
}

// ResizeEIP mocks base method
func (m *MockInterface) ResizeEIP(arg0 context.Context, arg1 string, arg2 *eip.ResizeEIPArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResizeEIP", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResizeEIP indicates an expected call of ResizeEIP
func (mr *MockInterfaceMockRecorder) ResizeEIP(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResizeEIP", reflect.TypeOf((*MockInterface)(nil).ResizeEIP), arg0, arg1, arg2, arg3)
}

// SetDebug mocks base method
func (m *MockInterface) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug
func (mr *MockInterfaceMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), arg0)
}

// UnbindEIP mocks base method
func (m *MockInterface) UnbindEIP(arg0 context.Context, arg1 string, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnbindEIP", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnbindEIP indicates an expected call of UnbindEIP
func (mr *MockInterfaceMockRecorder) UnbindEIP(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnbindEIP", reflect.TypeOf((*MockInterface)(nil).UnbindEIP), arg0, arg1, arg2)
}
