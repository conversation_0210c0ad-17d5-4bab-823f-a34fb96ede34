// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/09/09 , by <PERSON><PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
实现 CCE V2 SDK AppService 相关方法
*/

package appservice

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/serializer/yaml"
	"k8s.io/apimachinery/pkg/util/version"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/inspect"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/app/app-service/resource/common"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/app/app-service/resource/horizontalpodautoscaler"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/app/app-service/resource/node"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/app/app-service/resource/pod"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/app/app-service/resource/replicaset"
)

// GetUserYaml - 获取用户的Yaml
// PARAMS:
//   - ctx: The context to trace request
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	GetUserYamlResponse: result
//	error: nil if succeed, error if fail
func (c *Client) GetUserYaml(ctx context.Context, yamlType string, option *bce.SignOption) (*GetUserYamlResponse, error) {
	params := map[string]string{
		"type": yamlType,
	}
	url := fmt.Sprintf("yaml")
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取用户yaml",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *GetUserYamlResponse
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// PostUserYaml - 新增用户的Yaml
// PARAMS:
//   - ctx: The context to trace request
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	UserYamlResponse: result
//	error: nil if succeed, error if fail
func (c *Client) PostUserYaml(ctx context.Context, yaml string, yamlName string, yamlType string, option *bce.SignOption) (*UserYamlResponse, error) {
	url := fmt.Sprintf("yaml")
	args := &UserYamlArgs{
		Content: yaml,
		Name:    yamlName,
		Type:    yamlType,
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, fmt.Errorf("marshal body: %w", err)
	}
	req, err := bce.NewRequest("POST", c.GetURL(url, nil), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "新增用户的Yaml",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *UserYamlResponse
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// GetUserYamlByID - 通过ID获取用户的Yaml
// PARAMS:
//   - ctx: The context to trace request
//   - yamID: string yamlID
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	UserYaml: result
//	error: nil if succeed, error if fail
func (c *Client) GetUserYamlByID(ctx context.Context, yamlID string, option *bce.SignOption) (*UserYaml, error) {
	if yamlID == "" {
		return nil, errors.New("yamlID is empty")
	}
	url := fmt.Sprintf("yaml/%s", yamlID)
	req, err := bce.NewRequest("GET", c.GetURL(url, nil), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "通过ID获取用户的Yaml",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *UserYaml
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// UpdateUserYaml - 更新用户的Yaml
// PARAMS:
//   - ctx: The context to trace request
//   - yamID: string yamlID
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	UserYamlResponse: result
//	error: nil if succeed, error if fail
func (c *Client) UpdateUserYaml(ctx context.Context, yamlID string, yaml string, yamlType string, option *bce.SignOption) (*AppResponse, error) {
	if yamlID == "" {
		return nil, errors.New("yamlID is empty")
	}
	url := fmt.Sprintf("yaml/%s", yamlID)
	args := &UserYamlArgs{
		Content: yaml,
		Name:    "test_update",
		Type:    yamlType,
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, fmt.Errorf("marshal body: %w", err)
	}
	req, err := bce.NewRequest("PUT", c.GetURL(url, nil), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "更新用户的Yaml",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *AppResponse
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// DeleteUserYaml - 删除用户的Yaml
// PARAMS:
//   - ctx: The context to trace request
//   - yamID: string yamlID
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	UserYamlResponse: result
//	error: nil if succeed, error if fail
func (c *Client) DeleteUserYaml(ctx context.Context, yamlID string, option *bce.SignOption) (*UserYamlResponse, error) {
	if yamlID == "" {
		return nil, errors.New("yamlID is empty")
	}
	url := fmt.Sprintf("yaml/%s", yamlID)
	req, err := bce.NewRequest("DELETE", c.GetURL(url, nil), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "删除用户的Yaml",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *UserYamlResponse
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// DeployIngressController - 部署 CCE IngressController
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: 集群 ID
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) DeployIngressController(ctx context.Context, clusterID string, option *bce.SignOption) error {
	if clusterID == "" {
		return errors.New("clusterID is empty")
	}

	// e.g. https://console.bce.baidu.com/api/cce/app/deploy/ingresscontroller?clusterUuid=c-5ybLOnzV
	req, err := bce.NewRequest("POST", c.GetURL("deploy/ingresscontroller?clusterUuid="+clusterID, nil), nil)
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

// PostAppDeployment - 新建部署
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: string clusterID
//   - appDeployment: *AppDeploymentRequest
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	AppDeploymentResponse: result
//	error: nil if succeed, error if fail
func (c *Client) PostAppResource(ctx context.Context, clusterID string, sourceName string, sourceYaml string, nameSpace string, option *bce.SignOption) (*AppResourceResponse, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := fmt.Sprintf("appdeploymentfromfile")
	args := &AppResourceRequest{
		Name:      sourceName,
		Content:   sourceYaml,
		Namespace: nameSpace,
		Validate:  true,
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, fmt.Errorf("marshal body: %w", err)
	}
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "新建工作负载",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *AppResourceResponse
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// GetAppDeployment - 获取部署
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: string clusterID
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	AppDeploymentListResponse: result
//	error: nil if succeed, error if fail
func (c *Client) GetAppResource(ctx context.Context, clusterID string, kind string, option *bce.SignOption) (*AppResourceListResponse, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := fmt.Sprintf("%s", kind)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取工作负载",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *AppResourceListResponse
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// GetAppDeploymentList 根据命名空间获取负载列表
func (c *Client) GetAppDeploymentList(ctx context.Context, clusterID string, nameSpace string, option *bce.SignOption) (*DeploymentList, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := fmt.Sprintf("deployment/%s", nameSpace)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "根据命名空间获取deployment列表",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *DeploymentList
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// GetAppDeploymentByName - 根据名字获取部署
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: string clusterID
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	GetAppDeploymentResponse: result
//	error: nil if succeed, error if fail
func (c *Client) GetAppDeploymentByName(ctx context.Context, clusterID string, deploymentName string, nameSpace string, option *bce.SignOption) (*GetAppDeploymentResponse, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := fmt.Sprintf("deployment/%s/%s", nameSpace, deploymentName)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "根据名字获取deployment",
		inspect.InspectLatency: "8000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *GetAppDeploymentResponse
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) GetJobByName(ctx context.Context, clusterID string, jobName string, nameSpace string, option *bce.SignOption) (*JobDetail, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := fmt.Sprintf("job/%s/%s", nameSpace, jobName)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "根据名字获取job",
		inspect.InspectLatency: "8000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *JobDetail
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// GetJobNamespacedList - 获取某个Namespace下的 Job 列表
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: string clusterID
//   - nameSpace: string nameSpace
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	CronJobList: list result of job
//	error: nil if succeed, error if fail
func (c *Client) GetJobNamespacedList(ctx context.Context, clusterID, nameSpace string, option *bce.SignOption) (
	*JobList, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := fmt.Sprintf("/job/%s", nameSpace)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "根据命名空间获取job列表",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *JobList
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) GetServiceByName(ctx context.Context, clusterID string, serviceName string, nameSpace string, option *bce.SignOption) (*ServiceDetail, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := fmt.Sprintf("service/%s/%s", nameSpace, serviceName)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "根据名字获取service",
		inspect.InspectLatency: "5000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *ServiceDetail
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) GetDaemonSetByName(ctx context.Context, clusterID string, daemonSetName string, nameSpace string, option *bce.SignOption) (*DaemonSetDetail, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := fmt.Sprintf("daemonset/%s/%s", nameSpace, daemonSetName)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "根据名字获取daemonset",
		inspect.InspectLatency: "8000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *DaemonSetDetail
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) GetStatefulSetByName(ctx context.Context, clusterID string, statefulSetName string, nameSpace string, option *bce.SignOption) (*StatefulSetDetail, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}

	url := fmt.Sprintf("statefulset/%s/%s", nameSpace, statefulSetName)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "根据名字获取statefulset",
		inspect.InspectLatency: "8000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result *StatefulSetDetail
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// GetPodResource - 获取pod
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: string clusterID
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	PodList: result
//	error: nil if succeed, error if fail
func (c *Client) GetPodResource(ctx context.Context, clusterID string, option *bce.SignOption) (*PodList, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := fmt.Sprintf("pod")
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取pod",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *PodList
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// GetPodResourceByNamespace - 获取namespace下pod
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: string clusterID
//   - namespace: string namespace
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	PodList: result
//	error: nil if succeed, error if fail
func (c *Client) GetPodResourceByNamespace(ctx context.Context,
	clusterID, namespace string, option *bce.SignOption) (*PodList, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := fmt.Sprintf("pod/%s", namespace)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "根据命名空间获取pod",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *PodList
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// GetPodResourceByName - 通过pod名获取pod
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: string clusterID
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	PodList: result
//	error: nil if succeed, error if fail
func (c *Client) GetPodResourceByName(ctx context.Context, clusterID string, PodName string, nameSpace string, option *bce.SignOption) (*PodDetail, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := fmt.Sprintf("pod/%s/%s", nameSpace, PodName)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "根据名字获取pod",
		inspect.InspectLatency: "5000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *PodDetail
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// ReloadAppResource - 重启资源
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: string clusterID
//   - kind: string kind
//   - nameSpace: string nameSpace
//   - name: string deploymentName
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	AppDeploymentResponse: result
//	error: nil if succeed, error if fail
func (c *Client) ReloadAppResource(ctx context.Context, clusterID string, kind string, nameSpace string, name string, option *bce.SignOption) error {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := fmt.Sprintf("reload/%s/%s/%s", kind, nameSpace, name)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), nil)
	if err != nil {
		return err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "重启资源",
		inspect.InspectLatency: "2000",
	})
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

// ScaleAppResource - 扩容资源
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: string clusterID
//   - kind: string kind
//   - nameSpace: string nameSpace
//   - name: string name
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	AppDeploymentResponse: result
//	error: nil if succeed, error if fail
func (c *Client) ScaleAppResource(ctx context.Context, clusterID string, kind string, nameSpace string, name string, scaleNumber string, option *bce.SignOption) (*AppResourceResponse, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
		"scaleBy":     scaleNumber,
	}
	url := fmt.Sprintf("scale/%s/%s/%s", kind, nameSpace, name)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    fmt.Sprintf("扩容%s资源", kind),
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *AppResourceResponse
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// DeleteAppResource - 删除资源
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: string clusterID
//   - kind: string kind
//   - nameSpace: string nameSpace
//   - name: string deploymentName
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	AppDeploymentResponse: result
//	error: nil if succeed, error if fail
func (c *Client) DeleteAppResource(ctx context.Context, clusterID string, kind string, nameSpace string, name string, option *bce.SignOption) error {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := fmt.Sprintf("delete_raw/%s/namespace/%s/name/%s", kind, nameSpace, name)
	req, err := bce.NewRequest("DELETE", c.GetURL(url, params), nil)
	if err != nil {
		return err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    fmt.Sprintf("删除%s资源", kind),
		inspect.InspectLatency: "2000",
	})
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

// DeleteCronHPA - 删除自定义CronHPA资源
func (c *Client) DeleteCronHPA(ctx context.Context, clusterID string, args *DeleteCronHPAArgs, option *bce.SignOption) error {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return fmt.Errorf("marshal args error: %v", err)
	}
	req, err := bce.NewRequest("POST", c.GetURL("_raw/CronHPA", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    fmt.Sprintf("删除CronHPA资源"),
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}

	return nil
}

func (c *Client) GetCronHPAByName(ctx context.Context, clusterID string, nameSpace string, typeName string, name string, option *bce.SignOption) (*GetCronHPAResponse, error) {
	if clusterID == "" || nameSpace == "" || typeName == "" || name == "" {
		return nil, errors.New("clusterID, nameSpace, typeName, name cannot be empty")
	}

	params := map[string]string{
		"keywordType": typeName,
		"keyword":     name,
		"clusterUuid": clusterID,
	}

	url := fmt.Sprintf("cronhorizontalpodautoscaler/%s", nameSpace)

	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    fmt.Sprintf("通过CronHPA名字获取CronHPA资源"),
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result *GetCronHPAResponse
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) ModifyCronHPA(ctx context.Context, clusterID string, nameSpace string, name string, args *CronHPARequest, option *bce.SignOption) (*AppResourceResponse, error) {
	if clusterID == "" || nameSpace == "" || name == "" {
		return nil, errors.New("clusterID, nameSpace, typeName, name cannot be empty")
	}

	params := map[string]string{
		"clusterUuid": clusterID,
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, fmt.Errorf("marshal body: %w", err)
	}

	url := fmt.Sprintf("cronhorizontalpodautoscaler/%s/%s", nameSpace, name)

	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    fmt.Sprintf("编辑CronHPA资源"),
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result *AppResourceResponse
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) GetNamespaceList(ctx context.Context, clusterID string, option *bce.SignOption) (
	*NamespaceList, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := "namespace"
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取命名空间列表",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *NamespaceList
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) GetNodeList(ctx context.Context, args *GetNodeListArgs, option *bce.SignOption) (*node.NodeList, error) {
	if args == nil || args.ClusterUuid == "" {
		return nil, errors.New("missing clusterUuid or args")
	}

	var params map[string]string

	b, err := json.Marshal(&args)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(b, &params)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("GET", c.GetURL("/node", params), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取node列表",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result *node.NodeList
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) GetNodeByName(ctx context.Context, clusterID string, nodeName string, option *bce.SignOption) (*node.NodeDetail, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}

	url := fmt.Sprintf("/node/%s", nodeName)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "根据名字获取node",
		inspect.InspectLatency: "5000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result *node.NodeDetail
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) GetNodeEvents(ctx context.Context, clusterID string, nodeName string, option *bce.SignOption) (*common.EventList, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}

	url := fmt.Sprintf("/node/%s/event", nodeName)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取节点事件",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result *common.EventList
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) GetNodePods(ctx context.Context, clusterID string, nodeName string, option *bce.SignOption) (*pod.PodList, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}

	url := fmt.Sprintf("/node/%s/pod", nodeName)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取节点上的容器",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result *pod.PodList
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) UpdateNode(ctx context.Context, clusterID string, nodeName string, args node.UpdateNodeParams, option *bce.SignOption) (*node.Node, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}

	url := fmt.Sprintf("/node/%s", nodeName)

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, fmt.Errorf("marshal body: %w", err)
	}
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "更新node",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result *node.Node
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) GetHPAForResource(ctx context.Context, clusterID, kind, namespace, name string, option *bce.SignOption) (*horizontalpodautoscaler.HorizontalPodAutoscalerList, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}

	url := fmt.Sprintf("%s/%s/%s/horizontalpodautoscaler", kind, namespace, name)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取hpa资源",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result *horizontalpodautoscaler.HorizontalPodAutoscalerList
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// GetDeploymentRS - 获取 Deployment 对应 RS
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: string clusterID
//   - nameSpace: string nameSpace
//   - name: string deploymentName
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	ReplicaSetList: result
//	error: nil if succeed, error if fail
func (c *Client) GetDeploymentRS(ctx context.Context, clusterID, namespace, name string, option *bce.SignOption) (*replicaset.ReplicaSetList, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}

	url := fmt.Sprintf("/deployment/%s/%s/oldreplicaset", namespace, name)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取deployment对应的rs",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result *replicaset.ReplicaSetList
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// RolloutUndoDeployment - 回滚 Deployment 至指定版本
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: string clusterID
//   - namespace: string nameSpace
//   - name: string deploymentName
//   - revision: 回滚的 RS 版本
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) RolloutUndoDeployment(ctx context.Context, clusterID string, namespace, name, revision string, option *bce.SignOption) error {
	params := map[string]string{
		"clusterUuid": clusterID,
	}

	url := fmt.Sprintf("/deployment/%s/%s/rollout/%s", namespace, name, revision)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), nil)
	if err != nil {
		return err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "回滚deployment到指定版本",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	if _, err := resp.GetBodyContent(); err != nil {
		return err
	}

	return nil
}

// GetResourceYAML - 获取集群对应资源
func (c *Client) GetResourceYAML(ctx context.Context, clusterID, kind, namespace, name string, option *bce.SignOption) (runtime.Object, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}

	url := fmt.Sprintf("/_raw/%s/namespace/%s/name/%s", kind, namespace, name)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    fmt.Sprintf("获取%s的yaml", kind),
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	// var result runtime.Object
	// err = json.Unmarshal(bodyContent, result)
	result, _, err := yaml.NewDecodingSerializer(unstructured.UnstructuredJSONScheme).
		Decode([]byte(bodyContent), nil, &unstructured.Unstructured{})
	if err != nil {
		return nil, err
	}

	return result, nil
}

// GetCronJobByName - 获取某个Namespace下的 CronJob 详情
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: string clusterID
//   - namespace: string namespace
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	CronJobList: list result of cronjob
//	error: nil if succeed, error if fail
func (c *Client) GetCronJobByName(ctx context.Context, clusterID, cronJobName, nameSpace string, option *bce.SignOption) (
	*CronJobDetail, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := fmt.Sprintf("cronjob/%s/%s", nameSpace, cronJobName)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "根据名字获取cronjob",
		inspect.InspectLatency: "5000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *CronJobDetail
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// GetCronJobNamespacedList - 获取某个Namespace下的 CronJob 列表
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: string clusterID
//   - nameSpace: string nameSpace
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	CronJobList: list result of cronjob
//	error: nil if succeed, error if fail
func (c *Client) GetCronJobNamespacedList(ctx context.Context, clusterID, nameSpace string, option *bce.SignOption) (
	*CronJobList, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := fmt.Sprintf("/cronjob/%s", nameSpace)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "根据命名空间获取cronjob",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *CronJobList
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// GetCronJobList - 获取所有Namespace下的 CronJob 列表
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: string clusterID
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	CronJobList: list result of cronjob
//	error: nil if succeed, error if fail
func (c *Client) GetCronJobList(ctx context.Context, clusterID string, option *bce.SignOption) (
	*CronJobList, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := "/cronjob"
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取cronjob列表",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var result *CronJobList
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// GetIngressList - 获取指定Namespace下的 Ingress 列表
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: string clusterID
//   - nameSpace: string nameSpace
//   - clusterVersion: string clusterVersion
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	 IngressListNetwokingV1: list result of ingress with k8sVersion > 1.22
//		IngressList: list result of ingress
//		error: nil if success, error if fail
func (c *Client) GetIngressList(ctx context.Context, clusterID, nameSpace, name, clusterVersion string, option *bce.SignOption) (any, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	if name != "" {
		params["filterBy"] = "name," + name
	}

	url := fmt.Sprintf("/ingress-all-class/%s", nameSpace)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取ingress列表",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	runningVersion, err := version.ParseGeneric(clusterVersion)
	if err != nil {
		return nil, err
	}

	version122, _ := version.ParseGeneric("v1.22.0")
	if runningVersion.AtLeast(version122) {
		var result *IngressListNetwokingV1
		err = json.Unmarshal(bodyContent, &result)
		if err != nil {
			return nil, err
		}
		return result, nil
	}
	var result *IngressList
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// GetIngressDetailByName - 获取指定Namespace下的指定 Ingress yaml信息
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: string clusterID
//   - nameSpace: string nameSpace
//   - clusterVersion: string clusterVersion
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	 IngressDetailNetwokingV1: detail result of ingress with k8sVersion > 1.22
//		IngressDetail: detail result of ingress
//		error: nil if success, error if fail
func (c *Client) GetIngressDetailByName(ctx context.Context, clusterID, nameSpace, name, clusterVersion string, option *bce.SignOption) (any, error) {
	params := map[string]string{
		"clusterUuid":   clusterID,
		"namespaceName": nameSpace,
		"ingressName":   name,
	}

	url := fmt.Sprintf("_raw/ingress/namespace/%s/name/%s", nameSpace, name)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "根据名字获取ingress",
		inspect.InspectLatency: "5000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	runningVersion, err := version.ParseGeneric(clusterVersion)
	if err != nil {
		return nil, err
	}

	version122, _ := version.ParseGeneric("v1.22.0")
	if runningVersion.AtLeast(version122) {
		var result *IngressDetailNetwokingv1
		err = json.Unmarshal(bodyContent, &result)
		if err != nil {
			return nil, err
		}
		return result, nil
	}
	var result *IngressDetail
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// UpdateIngressYaml - 更新指定Namespace下的指定 Ingress yaml信息
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: string clusterID
//   - nameSpace: string nameSpace
//   - clusterVersion: string clusterVersion
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	error: nil if success, error if fail
func (c *Client) UpdateIngressYaml(ctx context.Context, clusterID, nameSpace, name string, yaml any, option *bce.SignOption) error {
	params := map[string]string{
		"clusterUuid": clusterID,
	}

	postContent, err := json.Marshal(yaml)
	if err != nil {
		return fmt.Errorf("marshal body: %w", err)
	}
	fmt.Printf("marshal body: %v", bytes.NewBuffer(postContent))

	url := fmt.Sprintf("_raw/ingress/namespace/%s/name/%s", nameSpace, name)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "更新ingress的yaml",
		inspect.InspectLatency: "2000",
	})
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

func (c *Client) CreateResourceQuota(ctx context.Context, clusterID string, spec *DeployResourceQuotaSpec, option *bce.SignOption) (*corev1.ResourceQuota, error) {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := fmt.Sprintf("resourcequota")
	postBody, err := json.Marshal(spec)
	if err != nil {
		return nil, fmt.Errorf("marshal body: %w", err)
	}

	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postBody))
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "创建resource quota",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result corev1.ResourceQuota
	if err := json.Unmarshal(bodyContent, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// PostDeleteNameSpace 删除namespace
func (c *Client) PostDeleteNameSpace(ctx context.Context, clusterID string, name string, option *bce.SignOption) error {
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := fmt.Sprintf("delete_raw/namespace/name/%s", name)
	req, err := bce.NewRequest("DELETE", c.GetURL(url, params), nil)
	if err != nil {
		return err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "删除命名空间",
		inspect.InspectLatency: "2000",
	})
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}
