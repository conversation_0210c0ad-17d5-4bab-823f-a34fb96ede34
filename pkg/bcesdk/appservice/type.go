// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/09/09, by <PERSON><PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
CCE V2 版本 GO SDK, appservice 定义
*/
package appservice

import (
	"context"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	extensions "k8s.io/api/extensions/v1beta1"
	networkingv1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/app/app-service/api"
	metricapi "icode.baidu.com/baidu/cce-test/e2e-test/services/app/app-service/integration/metric/api"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/app/app-service/resource/common"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/app/app-service/resource/horizontalpodautoscaler"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/app/app-service/resource/node"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/app/app-service/resource/pod"
	"icode.baidu.com/baidu/cce-test/e2e-test/services/app/app-service/resource/replicaset"
)

// Interface - 定义 APPService SDK
type Interface interface {
	GetUserYaml(ctx context.Context, yamlType string, option *bce.SignOption) (*GetUserYamlResponse, error)
	PostUserYaml(ctx context.Context, yaml string, yamlName string, yamlType string, option *bce.SignOption) (*UserYamlResponse, error)
	GetUserYamlByID(ctx context.Context, yamlID string, option *bce.SignOption) (*UserYaml, error)
	UpdateUserYaml(ctx context.Context, yamlID string, yaml string, yamlType string, option *bce.SignOption) (*AppResponse, error)
	DeleteUserYaml(ctx context.Context, yamlID string, option *bce.SignOption) (*UserYamlResponse, error)

	DeployIngressController(ctx context.Context, clusterID string, option *bce.SignOption) error

	PostAppResource(ctx context.Context, clusterID string, resourceName string, resourceYaml string, nameSpace string, option *bce.SignOption) (*AppResourceResponse, error)
	CreateResourceQuota(ctx context.Context, clusterID string, spec *DeployResourceQuotaSpec, option *bce.SignOption) (*corev1.ResourceQuota, error)
	PostDeleteNameSpace(ctx context.Context, clusterID string, name string, option *bce.SignOption) error
	GetAppResource(ctx context.Context, clusterID string, kind string, option *bce.SignOption) (*AppResourceListResponse, error)
	ScaleAppResource(ctx context.Context, clusterID string, kind string, nameSpace string, name string, scaleNumber string, option *bce.SignOption) (*AppResourceResponse, error)
	DeleteAppResource(ctx context.Context, clusterID string, kind string, nameSpace string, name string, option *bce.SignOption) error
	ReloadAppResource(ctx context.Context, clusterID string, kind string, nameSpace string, name string, option *bce.SignOption) error

	GetAppDeploymentByName(ctx context.Context, clusterID string, deploymentName string, nameSpace string, option *bce.SignOption) (*GetAppDeploymentResponse, error)
	GetAppDeploymentList(ctx context.Context, clusterID string, nameSpace string, option *bce.SignOption) (*DeploymentList, error)
	GetJobNamespacedList(ctx context.Context, clusterID string, nameSpace string, option *bce.SignOption) (*JobList, error)
	GetJobByName(ctx context.Context, clusterID string, jobName string, nameSpace string, option *bce.SignOption) (*JobDetail, error)
	GetCronJobNamespacedList(ctx context.Context, clusterID string, nameSpace string, option *bce.SignOption) (*CronJobList, error)
	GetCronJobList(ctx context.Context, clusterID string, option *bce.SignOption) (*CronJobList, error)
	GetCronJobByName(ctx context.Context, clusterID string, cronJobName string, nameSpace string, option *bce.SignOption) (*CronJobDetail, error)
	GetIngressList(ctx context.Context, clusterID, nameSpace, name, clusterVersion string, option *bce.SignOption) (any, error)
	GetIngressDetailByName(ctx context.Context, clusterID, nameSpace, name, clusterVersion string, option *bce.SignOption) (any, error)
	UpdateIngressYaml(ctx context.Context, clusterID, nameSpace, name string, yaml any, option *bce.SignOption) error
	GetServiceByName(ctx context.Context, clusterID string, serviceName string, nameSpace string, option *bce.SignOption) (*ServiceDetail, error)

	GetPodResource(ctx context.Context, clusterID string, option *bce.SignOption) (*PodList, error)
	GetPodResourceByNamespace(ctx context.Context, clusterID, namespace string, option *bce.SignOption) (*PodList, error)
	GetPodResourceByName(ctx context.Context, clusterID string, PodName string, nameSpace string, option *bce.SignOption) (*PodDetail, error)
	GetDaemonSetByName(ctx context.Context, clusterID string, daemonSetName string, nameSpace string, option *bce.SignOption) (*DaemonSetDetail, error)
	GetStatefulSetByName(ctx context.Context, clusterID string, statefulSetName string, nameSpace string, option *bce.SignOption) (*StatefulSetDetail, error)

	GetNamespaceList(ctx context.Context, clusterID string, option *bce.SignOption) (*NamespaceList, error)

	GetNodeList(ctx context.Context, args *GetNodeListArgs, option *bce.SignOption) (*node.NodeList, error)
	GetNodeByName(ctx context.Context, clusterID string, nodeName string, option *bce.SignOption) (*node.NodeDetail, error)
	GetNodeEvents(ctx context.Context, clusterID string, nodeName string, option *bce.SignOption) (*common.EventList, error)
	GetNodePods(ctx context.Context, clusterID string, nodeName string, option *bce.SignOption) (*pod.PodList, error)
	UpdateNode(ctx context.Context, clusterID string, nodeName string, params node.UpdateNodeParams, option *bce.SignOption) (*node.Node, error)

	GetHPAForResource(ctx context.Context, clusterID string, kind, namespace, name string, option *bce.SignOption) (*horizontalpodautoscaler.HorizontalPodAutoscalerList, error)

	GetDeploymentRS(ctx context.Context, clusterID string, namespace, name string, option *bce.SignOption) (*replicaset.ReplicaSetList, error)
	RolloutUndoDeployment(ctx context.Context, clusterID string, namespace, name, revision string, option *bce.SignOption) error

	GetResourceYAML(ctx context.Context, clusterID, kind, namespace, name string, option *bce.SignOption) (runtime.Object, error)

	DeleteCronHPA(ctx context.Context, clusterID string, arg *DeleteCronHPAArgs, option *bce.SignOption) error

	GetCronHPAByName(ctx context.Context, clusterID string, nameSpace string, typeName string, name string, option *bce.SignOption) (*GetCronHPAResponse, error)

	ModifyCronHPA(ctx context.Context, clusterID string, nameSpace string, name string, args *CronHPARequest, option *bce.SignOption) (*AppResourceResponse, error)
}

type UserYaml struct {
	Content string `json:"content"`
	ID      string `json:"id"`
	Name    string `json:"name"`
	Type    string `json:"type"`
	UserID  string `json:"userId"`
}

type GetUserYamlResponse struct {
	Result []UserYaml `json:"result"`
	Total  int        `json:"total"`
}

type UserYamlResponse struct {
	Action string `json:"action"`
	ID     string `json:"id"`
}

type AppResponse struct {
	Name    string `json:"name"`
	Content string `json:"content"`
}

type UserYamlArgs struct {
	Name    string `json:"name"`
	Content string `json:"content"`
	Type    string `json:"type"`
}

type AppResourceRequest struct {
	Name      string `json:"name"`
	Content   string `json:"content"`
	Namespace string `json:"namespace"`
	Validate  bool   `json:"validate"`
}

type AppResourceResponse struct {
	Result  string `json:"result"`
	Success bool   `json:"success"`
}

type ObjectMeta struct {
	Name      string `json:"name"`
	Namespace string `json:"namespace"`

	Labels      map[string]string `json:"labels"`
	Annotations map[string]string `json:"annotations"`

	CreationTimestamp time.Time `json:"creationTimestamp"`

	OwnerReferences []metav1.OwnerReference `json:"ownerReferences,omitempty"`

	DeletionTimestamp *time.Time `json:"deletionTimestamp,omitempty"`
}

type TypeMeta struct {
	Kind string `json:"kind"`
}

type ListMeta struct {
	TotalItems int `json:"totalItems"`
}

type PodStatus struct {
	Status   string          `json:"status"`
	PodPhase corev1.PodPhase `json:"podPhase"`
}

type Pod struct {
	ObjectMeta ObjectMeta `json:"objectMeta"`
	TypeMeta   TypeMeta   `json:"typeMeta"`

	PodStatus PodStatus `json:"podStatus"`

	RestartCount int32 `json:"restartCount"`

	NodeName string `json:"nodeName"`

	PodIP string `json:"PodIP"`

	Metrics *PodMetrics `json:"metrics"`

	Containers []corev1.Container `json:"containers"`
}

type PodMetrics struct {
	// Most recent measure of CPU usage on all cores in nanoseconds.
	CPUUsage *uint64 `json:"cpuUsage"`

	// Pod memory usage in bytes.
	MemoryUsage *uint64 `json:"memoryUsage"`
}

type PodList struct {
	ListMeta ListMeta `json:"listMeta"`
	Pods     []Pod    `json:"pods"`
	Errors   []error  `json:"errors"`
}

type ReplicaSet struct {
	ObjectMeta ObjectMeta `json:"objectMeta"`
	TypeMeta   TypeMeta   `json:"typeMeta"`

	Pods PodInfo `json:"pods"`

	ContainerImages []string `json:"containerImages"`

	InitContainerImages []string `json:"initContainerImages"`
}

type PodInfo struct {
	// Number of pods that are created.
	Current int32 `json:"current"`

	Desired int32 `json:"desired"`

	Running int32 `json:"running"`

	Pending int32 `json:"pending"`

	Failed int32 `json:"failed"`

	Succeeded int32 `json:"succeeded"`
}

type PodDetail struct {
	ObjectMeta   ObjectMeta `json:"objectMeta"`
	TypeMeta     TypeMeta   `json:"typeMeta"`
	PodPhase     string     `json:"podPhase"`
	PodIP        string     `json:"podIP"`
	NodeName     string     `json:"nodeName"`
	RestartCount int32      `json:"restartCount"`
	QOSClass     string     `json:"qosClass"`
	Errors       []error    `json:"errors"`
	PodInfo      PodInfo    `json:"podInfo"`
}

type StatusInfo struct {
	Replicas int32 `json:"replicas"`

	Updated int32 `json:"updated"`

	Available int32 `json:"available"`

	Unavailable int32 `json:"unavailable"`
}

type DeploymentList struct {
	ListMeta          api.ListMeta       `json:"listMeta"`
	CumulativeMetrics []metricapi.Metric `json:"cumulativeMetrics"`

	// Basic information about resources status on the list.
	Status common.ResourceStatus `json:"status"`

	// Unordered list of Deployments.
	Deployments []GetAppDeploymentResponse `json:"deployments"`

	// List of non-critical errors, that occurred during resource retrieval.
	Errors []error `json:"errors"`
}

type GetAppDeploymentResponse struct {
	ObjectMeta       ObjectMeta              `json:"objectMeta"`
	TypeMeta         TypeMeta                `json:"typeMeta"`
	DeploymentStatus appsv1.DeploymentStatus `json:"deploymentStatus"`
	PodList          PodList                 `json:"podList"`

	Status     string     `json:"status"`
	StatusInfo StatusInfo `json:"statusInfo"`

	NewReplicaSet ReplicaSet `json:"newReplicaSet"`

	Errors []error `json:"errors"`
}

type AppResourceListResponse struct {
	ListMeta ListMeta `json:"listMeta"`
}

type JobDetail struct {
	ObjectMeta ObjectMeta `json:"objectMeta"`
	TypeMeta   TypeMeta   `json:"typeMeta"`

	PodInfo PodInfo `json:"podInfo"`

	PodList PodList `json:"podList"`

	ContainerImages []string `json:"containerImages"`

	InitContainerImages []string `json:"initContainerImages"`

	Parallelism *int32 `json:"parallelism"`

	Completions *int32 `json:"completions"`

	Errors []error `json:"errors"`

	Status string `json:"status"`
}

type JobList struct {
	ListMeta ListMeta `json:"listMeta"`

	Jobs []JobDetail `json:"jobs"`

	Errors []error `json:"errors"`
}

type CronJobList struct {
	ListMeta ListMeta `json:"listMeta"`

	CronJobs []CronJobDetail `json:"items"`

	Status map[string]int `json:"status"`

	Errors []error `json:"errors"`
}

type CronJobDetail struct {
	ObjectMeta   ObjectMeta `json:"objectMeta"`
	TypeMeta     TypeMeta   `json:"typeMeta"`
	Schedule     string     `json:"schedule"`
	LastSchedule string     `json:"lastSchedule"`
	Suspend      bool       `json:"suspend"`

	ConcurrencyPolicy       string  `json:"concurrencyPolicy"`
	StartingDeadLineSeconds *int64  `json:"startingDeadlineSeconds"`
	Active                  int32   `json:"active"`
	ActiveJobs              JobList `json:"activeJobs"`

	Errors []error `json:"errors"`
}

type CronHPARequest struct {
	ClusterID string      `json:"clusterId"`
	Namespace string      `json:"namespace"`
	Name      string      `json:"name"`
	CronHPA   CronHPAArgs `json:"cronhpa"`
}

type CronHPAArgs struct {
	Name           string         `json:"name"`
	Namespace      string         `json:"namespace"`
	Kind           string         `json:"kind"`
	KindName       string         `json:"kindName"`
	Cron           []Cron         `json:"cron"`
	ExcludeDates   []string       `json:"excludeDates"`
	ScaleTargetRef ScaleTargetRef `json:"scaleTargetRef"`
}

type GetCronHPAResponse struct {
	CronHPAs         []CronHPA         `json:"cronhpas"`
	RequestID        string            `json:"requestId"`
	PageNo           int32             `json:"pageNo"`
	PageSize         int32             `json:"pageSize"`
	TotalCount       int32             `json:"totalCount"`
	CronTabDescribes map[string]string `json:"cronTabDescribes"`
}

type CronHPA struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Status            CronHPAStatus `json:"status,omitempty"`
	Spec              CronHPASpec   `json:"spec,omitempty"`
}

type CronHPASpec struct {
	Crons          []Cron         `json:"crons"`
	ExcludeDates   []string       `json:"excludeDates,omitempty"`
	ScaleTargetRef ScaleTargetRef `json:"scaleTargetRef"`
}

type Cron struct {
	Name       string `json:"name"`
	Schedule   string `json:"schedule"`
	TargetSize int32  `json:"targetSize"`
	RunOnce    bool   `json:"runOnce,omitempty"`
}

type ScaleTargetRef struct {
	Kind       string `json:"kind"`
	Name       string `json:"name"`
	APIVersion string `json:"apiVersion"`
}

type CronHPAStatus struct {
	Conditions     []CronHPACondition `json:"conditions,omitempty"`
	ExcludeDates   []string           `json:"excludeDates,omitempty"`
	ScaleTargetRef ScaleTargetRef     `json:"scaleTargetRef,omitempty"`
}

type CronHPACondition struct {
	JobID         string      `json:"jobId"`
	LastProbeTime metav1.Time `json:"lastProbeTime"`
	Message       string      `json:"message"`
	Name          string      `json:"name"`
	RunOnce       bool        `json:"runOnce"`
	Schedule      string      `json:"schedule"`
	State         JobState    `json:"state"`
	TargetSize    int32       `json:"targetSize"`
}

type JobState string

const (
	Succeed   JobState = "Succeed"
	Failed    JobState = "Failed"
	Submitted JobState = "Submitted"
)

type DaemonSetDetail struct {
	ObjectMeta ObjectMeta `json:"objectMeta"`
	TypeMeta   TypeMeta   `json:"typeMeta"`

	ContainerImages []string `json:"containerImages"`

	InitContainerImages []string `json:"initContainerImages"`

	PodInfo PodInfo `json:"podInfo"`

	PodList PodList `json:"podList"`

	HasMetrics bool `json:"hasMetrics"`

	Errors []error `json:"errors"`

	Status string `json:"status"`
}

type StatefulSetDetail struct {
	ObjectMeta ObjectMeta `json:"objectMeta"`
	TypeMeta   TypeMeta   `json:"typeMeta"`

	PodInfo PodInfo `json:"podInfo"`
	PodList PodList `json:"podList"`

	ContainerImages     []string `json:"containerImages"`
	InitContainerImages []string `json:"initContainerImages"`

	// List of non-critical errors, that occurred during resource retrieval.
	Errors []error `json:"errors"`

	Status string `json:"status"`
}

type ServiceDetail struct {
	ObjectMeta ObjectMeta `json:"objectMeta"`
	TypeMeta   TypeMeta   `json:"typeMeta"`

	Selector map[string]string `json:"selector"`

	Type string `json:"type"`

	ClusterIP string `json:"clusterIP"`

	PodList PodList `json:"podList"`

	Errors []error `json:"errors"`

	ExternalEndpoints []common.Endpoint `json:"externalEndpoints"`
}

type NamespaceList struct {
	ListMeta ListMeta `json:"listMeta"`

	// Unordered list of Namespaces.
	Namespaces []Namespace `json:"namespaces"`

	// List of non-critical errors, that occurred during resource retrieval.
	Errors []error `json:"errors"`
}

// additional augmented data we can get from other sources.
type Namespace struct {
	ObjectMeta ObjectMeta `json:"objectMeta"`
	TypeMeta   TypeMeta   `json:"typeMeta"`

	// Phase is the current lifecycle phase of the namespace.
	Phase corev1.NamespacePhase `json:"phase"`
}

type GetNodeListArgs struct {
	ClusterUuid  string `json:"clusterUuid"`
	FilterBy     string `json:"filterBy"`
	ItemsPerPage string `json:"itemsPerPage"`
	Name         string `json:"name"`
	Page         string `json:"page"`
	SortBy       string `json:"sortBy"`
	BatchQuery   string `json:"batchQuery"`
}

type IngressNetworkingV1 struct {
	api.ObjectMeta `json:"objectMeta"`
	TypeMeta       api.TypeMetaWithAPIVersion `json:"typeMeta"`

	Endpoints []common.Endpoint `json:"endpoints"`

	Spec networkingv1.IngressSpec `json:"spec"`

	Status networkingv1.IngressStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

type Ingress struct {
	ObjectMeta `json:"objectMeta"`
	TypeMeta   api.TypeMetaWithAPIVersion `json:"typeMeta"`

	Endpoints []common.Endpoint `json:"endpoints"`

	Spec extensions.IngressSpec `json:"spec"`

	Status extensions.IngressStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

type IngressListNetwokingV1 struct {
	ListMeta `json:"listMeta"`

	Items []IngressNetworkingV1 `json:"items"`

	Errors []error `json:"errors"`
}

type IngressList struct {
	ListMeta `json:"listMeta"`

	Items []Ingress `json:"items"`

	Errors []error `json:"errors"`
}

type IngressDetailNetwokingv1 struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	Spec networkingv1.IngressSpec `json:"spec"`

	Status networkingv1.IngressStatus `json:"status"`

	Errors []error `json:"errors"`
}

type IngressDetail struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	Spec extensions.IngressSpec `json:"spec"`

	Status extensions.IngressStatus `json:"status"`

	Errors []error `json:"errors"`
}

type DeployResourceQuotaSpec struct {
	Name      string                    `json:"name"`
	Namespace string                    `json:"namespace"`
	Spec      *corev1.ResourceQuotaSpec `json:"spec"`
}

type DeleteCronHPAArgs struct {
	Kind         string            `json:"kind"`
	ClusterUuid  string            `json:"clusterUuid"`
	ResourceList []CronHPAResource `json:"resourceList"`
	Method       string            `json:"method"`
}

type CronHPAResource struct {
	Kind      string `json:"kind"`
	Group     string `json:"group"`
	Version   string `json:"version"`
	NameSpace string `json:"namespace"`
	Name      string `json:"name"`
}
