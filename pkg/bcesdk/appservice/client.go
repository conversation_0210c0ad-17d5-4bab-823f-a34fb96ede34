// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/09/09, by <PERSON><PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
app service
*/

package appservice

import (
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

var _ Interface = &Client{}

// Client 实现 appservice.Interface
type Client struct {
	*bce.Client
}

// NewClient client
func NewClient(config *bce.Config) *Client {
	return &Client{
		Client: bce.NewClient(config),
	}
}

// SetDebug 是否开启 debug 模式
func (c *Client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

// GetURL generates the full URL of http request for Baidu Cloud BOS API.
func (c *Client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}
