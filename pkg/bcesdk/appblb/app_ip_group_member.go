package appblb

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

type CreateIPGroupMemberArgs struct {
	IPGroupID  string              `json:"ipGroupId"`
	MemberList []*AppIPGroupMember `json:"memberList"`
}

func (c *Client) CreateIPGroupMember(ctx context.Context, lbShortID string, args *CreateIPGroupMemberArgs, option *bce.SignOption) error {
	if lbShortID == "" || args == nil {
		return errors.New("missing lbShortID or args is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/ipgroup/member", lbShortID)
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)

	return err
}

type UpdateIPGroupMemberArgs struct {
	IPGroupID  string              `json:"ipGroupId"`
	MemberList []*AppIPGroupMember `json:"memberList"`
}

func (c *Client) UpdateIPGroupMember(ctx context.Context, lbShortID string, args *UpdateIPGroupMemberArgs, option *bce.SignOption) error {
	if lbShortID == "" || args == nil {
		return errors.New("missing lbShortID or args is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/ipgroup/member", lbShortID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)

	return err
}

type DeleteIPGroupMemberArgs struct {
	IPGroupID    string   `json:"ipGroupId"`
	MemberIDList []string `json:"memberIdList"`
}

func (c *Client) DeleteIPGroupMember(ctx context.Context, lbShortID string, args *DeleteIPGroupMemberArgs, option *bce.SignOption) error {
	if lbShortID == "" || args == nil {
		return errors.New("missing lbShortID or args is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/ipgroup/member", lbShortID)
	req, err := bce.NewRequest("DELETE", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)

	return err
}

type ListIPGroupMemberArgs struct {
	IPGroupID string `json:"ipGroupId"`
	Marker    string `json:"marker"`
	MaxKeys   int    `json:"maxKeys"`
}

type ListIPGroupMemberResponse struct {
	Marker      string              `json:"marker"`
	IsTruncated bool                `json:"isTruncated"`
	NextMarker  string              `json:"nextMarker"`
	MaxKeys     int                 `json:"maxKeys"`
	MemberList  []*AppIPGroupMember `json:"memberList"`
}

func (c *Client) ListIPGroupMember(ctx context.Context, lbShortID string, args *ListIPGroupMemberArgs, option *bce.SignOption) (*ListIPGroupMemberResponse, error) {
	if lbShortID == "" {
		return nil, errors.New("missing lbShortID")
	}

	if args == nil || args.IPGroupID == "" {
		return nil, errors.New("missing IPGroupID")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	if args.IPGroupID != "" {
		params["ipGroupId"] = args.IPGroupID
	}
	if args.Marker != "" {
		params["marker"] = args.Marker
	}
	if args.MaxKeys > 0 {
		params["maxKeys"] = strconv.Itoa(args.MaxKeys)
	}

	url := fmt.Sprintf("appblb/%s/ipgroup/member", lbShortID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, fmt.Errorf("failed to SendRequest: %v", err)
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, fmt.Errorf("get body content: %w", err)
	}

	listResp := new(ListIPGroupMemberResponse)
	err = json.Unmarshal(bodyContent, listResp)
	if err != nil {
		return nil, fmt.Errorf("unmarshal %s: %w", string(bodyContent), err)
	}

	return listResp, nil
}
