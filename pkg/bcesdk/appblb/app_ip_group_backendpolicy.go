package appblb

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"k8s.io/apimachinery/pkg/util/intstr"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

type CreateIPGroupBackendPolicyArgs struct {
	IPGroupID                   string                   `json:"ipGroupId"`
	Type                        BackendPolicyType        `json:"type"`
	HealthCheck                 BackendPolicyHealthCheck `json:"healthCheck"`
	HealthCheckPort             *intstr.IntOrString      `json:"healthCheckPort,omitempty"`
	HealthCheckTimeoutInSecond  int                      `json:"healthCheckTimeoutInSecond,omitempty"`
	HealthCheckDownRetry        int                      `json:"healthCheckDownRetry,omitempty"`
	HealthCheckUpRetry          int                      `json:"healthCheckUpRetry,omitempty"`
	HealthCheckIntervalInSecond int                      `json:"healthCheckIntervalInSecond,omitempty"`
	HealthCheckNormalStatus     string                   `json:"healthCheckNormalStatus,omitempty"`
	HealthCheckURLPath          string                   `json:"healthCheckUrlPath,omitempty"`
	UDPHealthCheckString        string                   `json:"udpHealthCheckString,omitempty"`
}

func (c *Client) CreateIPGroupBackendPolicy(ctx context.Context, lbShortID string, args *CreateIPGroupBackendPolicyArgs, option *bce.SignOption) error {
	if lbShortID == "" || args == nil {
		return errors.New("missing lbShortID or args is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/ipgroup/backendpolicy", lbShortID)
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)

	return err
}

type UpdateIPGroupBackendPolicyArgs struct {
	IPGroupID                   string                   `json:"ipGroupId"`
	ID                          string                   `json:"id"`
	HealthCheck                 BackendPolicyHealthCheck `json:"healthCheck"`
	HealthCheckPort             intstr.IntOrString       `json:"healthCheckPort"`
	HealthCheckTimeoutInSecond  int                      `json:"healthCheckTimeoutInSecond,omitempty"`
	HealthCheckDownRetry        int                      `json:"healthCheckDownRetry,omitempty"`
	HealthCheckUpRetry          int                      `json:"healthCheckUpRetry,omitempty"`
	HealthCheckIntervalInSecond int                      `json:"healthCheckIntervalInSecond,omitempty"`
	HealthCheckNormalStatus     string                   `json:"healthCheckNormalStatus,omitempty"`
	HealthCheckURLPath          string                   `json:"healthCheckUrlPath,omitempty"`
	UDPHealthCheckString        string                   `json:"udpHealthCheckString,omitempty"`
}

func (c *Client) UpdateIPGroupBackendPolicy(ctx context.Context, lbShortID string, args *UpdateIPGroupBackendPolicyArgs, option *bce.SignOption) error {
	if lbShortID == "" || args == nil {
		return errors.New("missing lbShortID or args is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/ipgroup/backendpolicy", lbShortID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)

	return err
}

type DeleteIPGroupBackendPolicyArgs struct {
	IPGroupID           string   `json:"ipGroupId"`
	BackendPolicyIDList []string `json:"backendPolicyIdList"`
}

func (c *Client) DeleteIPGroupBackendPolicy(ctx context.Context, lbShortID string, args *DeleteIPGroupBackendPolicyArgs, option *bce.SignOption) error {
	if lbShortID == "" || args == nil {
		return errors.New("missing lbShortID or args is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/ipgroup/backendpolicy", lbShortID)
	req, err := bce.NewRequest("DELETE", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)

	return err
}
