// Package appblb for bce application loadbalancer
package appblb

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// CreateAppServerGroupPort create appblb servergroup port
func (c *Client) CreateAppServerGroupPort(ctx context.Context, lbShortID string,
	args *CreateAppServerGroupPortArgs, option *bce.SignOption) (*CreateAppServerGroupPortResponse, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("appblb/%s/appservergroupport", lbShortID)
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var blbsResp *CreateAppServerGroupPortResponse
	err = json.Unmarshal(bodyContent, &blbsResp)
	if err != nil {
		return nil, err
	}

	return blbsResp, nil
}

// UpdateAppServerGroupPort update appblb servergroup
func (c *Client) UpdateAppServerGroupPort(ctx context.Context, lbShortID string,
	args *UpdateAppServerGroupPortArgs, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/appservergroupport", lbShortID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()

	return err
}

// DeleteAppServerGroupPort delete appblb servergroup port
func (c *Client) DeleteAppServerGroupPort(ctx context.Context, lbShortID string,
	args *DeleteAppServerGroupPortArgs, option *bce.SignOption) error {
	params := map[string]string{
		"batchdelete": "",
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/appservergroupport", lbShortID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}
