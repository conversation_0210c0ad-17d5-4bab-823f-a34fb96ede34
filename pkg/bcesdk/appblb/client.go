// Package appblb for bce application loadbalancer
package appblb

import (
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
)

// Endpoint contains all endpoints of Baidu Cloud BCC.
var Endpoint = map[string]string{
	"bj":  "blb.bj.baidubce.com",
	"gz":  "blb.gz.baidubce.com",
	"su":  "blb.su.baidubce.com",
	"hkg": "blb.hkg.baidubce.com",
	"fwh": "blb.fwh.baidubce.com",
	"bd":  "blb.bd.baidubce.com",
}

// Client is the bos client implemention for AppBLB.Interface.
type Client struct {
	*bce.Client
}

// NewClient return AppBLB client
func NewClient(config *bce.Config) *Client {
	bceClient := bce.NewClient(config)
	return &Client{Client: bceClient}
}

// SetDebug 是否开启 debug 模式
func (c *Client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

// GetURL generates the full URL of http request for Baidu Cloud BOS API.
func (c *Client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoint[c.GetRegion()]
	}
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}

// NewAppBLBSignOption return a app blb specified sign option
func NewAppBLBSignOption() *bce.SignOption {
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")

	return option
}
