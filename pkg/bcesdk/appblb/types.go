package appblb

import (
	"context"

	"k8s.io/apimachinery/pkg/util/intstr"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb Interface

// Interface for AppBLB Operation
type Interface interface {
	SetDebug(bool)

	// AppLoadBalancer
	CreateAppLoadBalancer(ctx context.Context, args *CreateAppLoadBalancerArgs, option *bce.SignOption) (*CreateAppLoadBalancerResponse, error)

	UpdateAppLoadBalancer(ctx context.Context, blbShortID string, args *UpdateAppLoadBalancerArgs, option *bce.SignOption) error

	DescribeAppLoadBalancersByName(ctx context.Context, blbName string, option *bce.SignOption) ([]*AppLoadBalancer, error)
	DescribeAppLoadBalancerByID(ctx context.Context, blbShortID string, option *bce.SignOption) (*AppLoadBalancer, error)
	DescribeAppLoadBalancers(ctx context.Context, args *DescribeLoadBalancerArgs, option *bce.SignOption) (*DescribeAppLoadBalancerResponse, error)

	DeleteAppLoadBalancer(ctx context.Context, blbShortID string, option *bce.SignOption) error

	// Listener
	CreateAppTCPListener(ctx context.Context, lbShortID string, args *CreateAppTCPListenerArgs, option *bce.SignOption) error

	DescribeAppTCPListener(ctx context.Context, lbShortID string, port int, option *bce.SignOption) (*DescribeAppTCPListenerResponse, error)

	CreateAppUDPListener(ctx context.Context, lbShortID string, args *CreateAppUDPListenerArgs, option *bce.SignOption) error
	DescribeAppUDPListener(ctx context.Context, lbShortID string, port int, option *bce.SignOption) (*DescribeAppUDPListenerResponse, error)

	CreateAppTCPSSLListener(ctx context.Context, lbShortID string, args *CreateAppTCPSSLListenerArgs, option *bce.SignOption) error
	DescribeAppTCPSSLListener(ctx context.Context, lbShortID string, port int, option *bce.SignOption) (*DescribeAppTCPSSLListenerResponse, error)

	CreateAppHTTPListener(ctx context.Context, lbShortID string, args *CreateAppHTTPListenerArgs, option *bce.SignOption) error
	UpdateAppHTTPListener(ctx context.Context, shortID string, port int, updateArgs *UpdateAppHTTPListenerArgs, option *bce.SignOption) error
	DescribeAppHTTPListener(ctx context.Context, lbShortID string, port int, option *bce.SignOption) (*DescribeAppHTTPListenerResponse, error)

	CreateAppHTTPSListener(ctx context.Context, lbShortID string, args *CreateAppHTTPSListenerArgs, option *bce.SignOption) error
	UpdateAppHTTPSListener(ctx context.Context, shortID string, port int, updateArgs *UpdateAppHTTPSListenerArgs, option *bce.SignOption) error
	DescribeAppHTTPSListener(ctx context.Context, shortID string, port int, option *bce.SignOption) (*DescribeAppHTTPSListenerResponse, error)

	DeleteAppListeners(ctx context.Context, lbShortID string, args *DeleteAppListenersArgs, option *bce.SignOption) error

	// Policy
	CreateAppPolicys(ctx context.Context, lbShortID string, args *CreateAppPolicyArgs, option *bce.SignOption) error

	DescribeAppPolicys(ctx context.Context, lbShortID string, args *DescribeAppPolicysArgs, option *bce.SignOption) (*DescribeAppPolicysResponse, error)
	DeleteAppPolicys(ctx context.Context, lbShortID string, args *DeleteAppPolicysArgs, option *bce.SignOption) error

	// ServerGroup
	CreateAppServerGroup(ctx context.Context, lbShortID string, args *CreateAppServerGroupArgs, option *bce.SignOption) (*CreateAppServerGroupResponse, error)

	UpdateAppServerGroup(ctx context.Context, lbShortID string, args *UpdateAppServerGroupArgs, option *bce.SignOption) error

	DescribeAppServerGroup(ctx context.Context, lbShortID string, args *DescribeAppServerGroupArgs, option *bce.SignOption) (*DescribeAppServerGroupResponse, error)
	DescribeAppServerGroupByID(ctx context.Context, lbShortID, serverGroupID, serverGroupName string, option *bce.SignOption) (*AppServerGroup, error)

	DeleteAppServerGroup(ctx context.Context, lbShortID string, args *DeleteAppServerGroupArgs, option *bce.SignOption) error

	// ServerGroupRS
	CreateAppServerGroupRS(ctx context.Context, lbShortID string, args *CreateAppServerGroupRSArgs, option *bce.SignOption) error

	UpdateAppServerGroupRSWeight(ctx context.Context, lbShortID string, args *UpdateAppServerGroupRSWeightArgs, option *bce.SignOption) error

	DescribeAppServerGroupRS(ctx context.Context, lbShortID, serverGroupID string, option *bce.SignOption) (*DescribeAppServerGroupRSResponse, error)
	DescribeAppServerGroupRSMount(ctx context.Context, lbShortID, serverGroupID string, option *bce.SignOption) ([]*AppBackendServer, error)
	DescribeAppServerGroupRSUnMount(ctx context.Context, lbShortID, serverGroupID string, option *bce.SignOption) ([]*AppBackendServer, error)

	DeleteAppServerGroupRS(ctx context.Context, lbShortID string, args *DeleteAppServerGroupRSArgs, option *bce.SignOption) error

	// ServerGroupPort
	CreateAppServerGroupPort(ctx context.Context, lbShortID string, args *CreateAppServerGroupPortArgs, option *bce.SignOption) (*CreateAppServerGroupPortResponse, error)

	UpdateAppServerGroupPort(ctx context.Context, lbShortID string, args *UpdateAppServerGroupPortArgs, option *bce.SignOption) error
	DeleteAppServerGroupPort(ctx context.Context, lbShortID string, args *DeleteAppServerGroupPortArgs, option *bce.SignOption) error

	CreateIPGroup(ctx context.Context, lbShortID string, args *CreateIPGroupArgs, option *bce.SignOption) (*CreateIPGroupResponse, error)
	ListIPGroups(ctx context.Context, lbShortID string, args *ListIPGroupArgs, option *bce.SignOption) (*ListIPGroupResponse, error)
	DeleteIPGroup(ctx context.Context, lbShortID string, args *DeleteIPGroupArgs, option *bce.SignOption) error
	GetIPGroupByID(ctx context.Context, lbShortID, ipGroupID string, option *bce.SignOption) (*AppIPGroup, error)

	ListIPGroupMember(ctx context.Context, lbShortID string, args *ListIPGroupMemberArgs, option *bce.SignOption) (*ListIPGroupMemberResponse, error)
	CreateIPGroupMember(ctx context.Context, lbShortID string, args *CreateIPGroupMemberArgs, option *bce.SignOption) error
	DeleteIPGroupMember(ctx context.Context, lbShortID string, args *DeleteIPGroupMemberArgs, option *bce.SignOption) error

	CreateIPGroupBackendPolicy(ctx context.Context, lbShortID string, args *CreateIPGroupBackendPolicyArgs, option *bce.SignOption) error
	DeleteIPGroupBackendPolicy(ctx context.Context, lbShortID string, args *DeleteIPGroupBackendPolicyArgs, option *bce.SignOption) error
}

// AppLoadBalancer for AppLoadBalancer
type AppLoadBalancer struct {
	BLBID         string        `json:"blbId"`
	Name          string        `json:"name"`
	Status        BLBStatus     `json:"status"`
	Desc          string        `json:"desc"`
	Address       string        `json:"address"`
	IPv6Address   string        `json:"ipv6"`
	PublicIP      string        `json:"publicIp"`
	CIDR          string        `json:"cidr"`
	VPCName       string        `json:"vpcName"`
	VPCID         string        `json:"vpcId"`
	SubNetCIDR    string        `json:"subnetCider"`
	SubNetName    string        `json:"subnetName"`
	SubNetID      string        `json:"subnetId"`
	CreateTime    string        `json:"createTime"`
	ReleaseTime   string        `json:"releaseTime"`
	ListenerList  []*Listener   `json:"listener"`
	Tags          []Tag         `json:"tags"`
	AllowDelete   bool          `json:"allowDelete"`
	UnderlayVIP   string        `json:"underlayVip"`
	PaymentTiming PaymentTiming `json:"paymentTiming"`
	ExpireTime    string        `json:"expireTime"`

	// 四层网关是否使用专属集群  True:使用专属集群 False:不使用专属集群 Nil:采用BLB默认行为(存在专属集群时建在专属集群否则建在共享集群)
	Layer4ClusterExclusive *bool `json:"layer4ClusterExclusive,omitempty"`

	// 四层 BLB 专属集群 ID
	Layer4ClusterID *string `json:"layer4ClusterId,omitempty"`

	// 七层网关是否使用专属集群  True:使用专属集群 False:不使用专属集群 Nil:采用BLB默认行为(存在专属集群时建在专属集群否则建在共享集群)
	Layer7ClusterExclusive *bool `json:"layer7ClusterExclusive,omitempty"`

	// 七层BLB 专属集群 ID
	Layer7ClusterID *string `json:"layer7ClusterId,omitempty"`
}

// BLBStatus for BLB status
type BLBStatus string

const (
	// BLBStatusCreating APP BLB creating
	BLBStatusCreating BLBStatus = "creating"

	// BLBStatusAvailable APP BLB available
	BLBStatusAvailable BLBStatus = "available"

	// BLBStatusUpdating APP BLB updating
	BLBStatusUpdating BLBStatus = "updating"

	// BLBStatusPaused APP BLB paused
	BLBStatusPaused BLBStatus = "paused"

	// BLBStatusUnavailable APP BLB unavailable
	BLBStatusUnavailable BLBStatus = "unavailable"
)

// Listener APP BLB Listener
type Listener struct {
	// Port 监听器端口 BLB OpenAPI may return type of string
	Port intstr.IntOrString `json:"port"`

	// Type 监听器协议类型
	Type ListenerType `json:"type"`

	// Description 描述信息，长度不超过200个字符。
	Description string `json:"description"`
}

type ListenerType string

const (
	ListenerTypeHTTP  ListenerType = "HTTP"
	ListenerTypeHTTPS ListenerType = "HTTPS"
	ListenerTypeTCP   ListenerType = "TCP"
	ListenerTypeUDP   ListenerType = "UDP"
)

// Tag BCE tag
type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

// TCPListener APP BLB TCP Listener
type TCPListener struct {
	ListenerPort      int           `json:"listenerPort"` // 1-65535
	Scheduler         SchedulerType `json:"scheduler"`
	TcpSessionTimeout int           `json:"tcpSessionTimeout,omitempty"`
}

// UDPListener APP BLB UDP Listener
type UDPListener struct {
	ListenerPort int           `json:"listenerPort"`
	Scheduler    SchedulerType `json:"scheduler"`
}

// TCPSSLListener TCP-SSL Listener
type TCPSSLListener struct {
	ListenerPort        int            `json:"listenerPort"`
	Scheduler           SchedulerType  `json:"scheduler"`
	CertIDs             []string       `json:"certIds"`
	EncryptionType      EncryptionType `json:"encryptionType,omitempty"`
	EncryptionProtocols []string       `json:"encryptionProtocols,omitempty"`
	AppliedCiphers      string         `json:"appliedCiphers,omitempty"`
	DualAuth            bool           `json:"dualAuth,omitempty"`
	ClientCertIDs       []string       `json:"clientCertIds,omitempty"`
}

// HTTPListener APP BLB HTTP Listener
type HTTPListener struct {
	ListenerPort          int                 `json:"listenerPort"` // 1-65535
	Scheduler             SchedulerType       `json:"scheduler"`
	KeepSession           bool                `json:"keepSession,omitempty"`
	KeepSessionType       KeepSessionType     `json:"keepSessionType,omitempty"`
	KeepSessionTimeout    int                 `json:"keepSessionTimeout,omitempty"`
	KeepSessionCookieName string              `json:"keepSessionCookieName,omitempty"`
	XForwardedFor         bool                `json:"xForwardedFor,omitempty"`
	ServerTimeout         int                 `json:"serverTimeout,omitempty"`
	RedirectPort          int                 `json:"redirectPort,omitempty"`
	ExtendedAttributes    map[string][]string `json:"extendedAttributes,omitempty"`
}

// HTTPSListener APP BLB HTTPS Listener
type HTTPSListener struct {
	ListenerPort          int                    `json:"listenerPort"` // 1-65535
	Scheduler             SchedulerType          `json:"scheduler"`
	KeepSession           bool                   `json:"keepSession,omitempty"`
	KeepSessionType       KeepSessionType        `json:"keepSessionType,omitempty"`
	KeepSessionTimeout    int                    `json:"keepSessionTimeout,omitempty"`
	KeepSessionCookieName string                 `json:"keepSessionCookieName,omitempty"`
	XForwardedFor         bool                   `json:"xForwardedFor,omitempty"`
	ServerTimeout         int                    `json:"serverTimeout,omitempty"`
	CertIDs               []string               `json:"certIds"`
	EncryptionType        EncryptionType         `json:"encryptionType,omitempty"`
	EncryptionProtocols   []string               `json:"encryptionProtocols,omitempty"`
	AppliedCiphers        string                 `json:"appliedCiphers,omitempty"`
	DualAuth              bool                   `json:"dualAuth,omitempty"`
	ClientCertIDs         []string               `json:"clientCertIds,omitempty"`
	ExtendedAttributes    map[string]interface{} `json:"extendedAttributes,omitempty"`
	AdditionalCertDomains []CertDomain           `json:"additionalCertDomains,omitempty"`
}

type CertDomain struct {
	CertID string `json:"certId"`
	Host   string `json:"host"`
}

// SchedulerType for HTTP Listener scheduler type
type SchedulerType string

const (
	// SchedulerTypeRoundRobin scheduler type = "RoundRobin"
	SchedulerTypeRoundRobin SchedulerType = "RoundRobin"

	// SchedulerTypeLeastConnection scheduler type = "LeastConnection"
	SchedulerTypeLeastConnection SchedulerType = "LeastConnection"

	// SchedulerTypeHash scheduler type = "Hash"
	SchedulerTypeHash SchedulerType = "Hash"
)

// KeepSessionType for HTTP Listener keep session type
type KeepSessionType string

const (
	// KeepSessionTypeInsert keep session type insert
	KeepSessionTypeInsert KeepSessionType = "insert"

	// KeepSessionTypeRewrite keep session type rewrite
	KeepSessionTypeRewrite KeepSessionType = "rewrite"

	// KeepSessionTypeSourceIP keep session type sourceip
	KeepSessionTypeSourceIP KeepSessionType = "srcip"
)

// EncryptionType for HTTPS Listener encryption type
type EncryptionType string

const (
	// EncryptionTypeCompatibleIE encryption type compatibleIE
	EncryptionTypeCompatibleIE EncryptionType = "compatibleIE"

	// EncryptionTypeIncompatibleIE encryption type IncompatibleIE
	EncryptionTypeIncompatibleIE EncryptionType = "incompatibleIE"

	// EncryptionTypeUserDefind encryption type userDefind
	EncryptionTypeUserDefind EncryptionType = "userDefind"

	// tls_cipher_policy_default。支持：tls_cipher_policy_default/tls_cipher_policy_1_1/tls_cipher_policy_1_2/tls_cipher_policy_1_2_secure/userDefind

	EncryptionTypeDefault EncryptionType = "tls_cipher_policy_default"

	EncryptionType11 EncryptionType = "tls_cipher_policy_1_1"

	EncryptionType12 EncryptionType = "tls_cipher_policy_1_2"

	EncryptionType12Secure EncryptionType = "tls_cipher_policy_1_2_secure"
)

// AppBackendServer App BLB backend server
type AppBackendServer struct {
	InstanceID string       `json:"instanceId"`
	Weight     int          `json:"weight"`
	PrivateIP  string       `json:"privateIp,omitempty"` // Only used in Query
	PortList   []*AppRSPort `json:"portList,omitempty"`  // Only used in Query
}

// AppRSPort App BLB RS Port
type AppRSPort struct {
	ListenerPort        intstr.IntOrString             `json:"listenerPort"`
	BackendPort         intstr.IntOrString             `json:"backendPort"`
	PortType            ServerGroupPortType            `json:"portType"`
	HealthCheckPortType ServerGroupPortHealthCheckType `json:"healthCheckPortType"`
	Status              RSPortStatus                   `json:"status"`
	PortID              string                         `json:"portId"`
	PolicyID            string                         `json:"policyId"`
}

// RSPortStatus AppBLB RS Port status
type RSPortStatus string

const (
	// RSPortStatusAlive RS port status = "Alive"
	RSPortStatusAlive RSPortStatus = "Alive"

	// RSPortStatusDead RS port status = "Dead"
	RSPortStatusDead RSPortStatus = "Dead"

	// RSPortStatusUnknown RS port status = "Unknown"
	RSPortStatusUnknown RSPortStatus = "Unknown"
)

// AppServerGroup for app blb server group
type AppServerGroup struct {
	ID       string                `json:"id"`
	Name     string                `json:"name"`
	Desc     string                `json:"desc"`
	Status   BLBStatus             `json:"status"`
	PortList []*AppServerGroupPort `json:"portList"`
}

// AppServerGroupPort for appServerGroup port
type AppServerGroupPort struct {
	ID                          string                         `json:"id"`
	Port                        int                            `json:"port"`
	Type                        ServerGroupPortType            `json:"type"`
	Status                      BLBStatus                      `json:"status"`
	HealthCheck                 ServerGroupPortHealthCheckType `json:"healthCheck"`
	HealthCheckPort             int                            `json:"healthCheckPort"`
	HealthCheckTimeoutInSecond  int                            `json:"healthCheckTimeoutInSecond"`
	HealthCheckIntervalInSecond int                            `json:"healthCheckIntervalInSecond"`
	HealthCheckDowsRetry        int                            `json:"healthCheckDownRetry"`
	HealthCheckUpRetry          int                            `json:"healthCheckUpRetry"`
	HealthCheckNormalStatus     string                         `json:"healthCheckNormalStatus"`
	HealthCheckURLPath          string                         `json:"healthCheckUrlPath"`
	UDPHealthCheckString        string                         `json:"udpHealthCheckString"`
}

// ServerGroupPortType ServerGroupPort type
type ServerGroupPortType string

const (
	// ServerGroupPortTypeTCP type TCP
	ServerGroupPortTypeTCP ServerGroupPortType = "TCP"

	// ServerGroupPortTypeUDP type UDP
	ServerGroupPortTypeUDP ServerGroupPortType = "UDP"

	// ServerGroupPortTypeHTTP type HTTP
	ServerGroupPortTypeHTTP ServerGroupPortType = "HTTP"
)

// ServerGroupPortHealthCheckType appServerGroupPort health check type
type ServerGroupPortHealthCheckType string

const (
	// ServerGroupPortHealthCheckTypeHTTP health check type = "HTTP"
	ServerGroupPortHealthCheckTypeHTTP ServerGroupPortHealthCheckType = "HTTP"

	// ServerGroupPortHealthCheckTypeTCP health check type = "TCP"
	ServerGroupPortHealthCheckTypeTCP ServerGroupPortHealthCheckType = "TCP"

	// ServerGroupPortHealthCheckTypeUDP health check type = "UDP"
	ServerGroupPortHealthCheckTypeUDP ServerGroupPortHealthCheckType = "UDP"
)

type GroupType string

const (
	GroupTypeIP     GroupType = "Ip"
	GroupTypeServer GroupType = "Server"
)

// AppPolicy AppBLB Policy
type AppPolicy struct {
	ID                 string              `json:"id,omitempty"`
	Desc               string              `json:"desc"`
	AppServerGroupID   string              `json:"appServerGroupId"`
	AppServerGroupName string              `json:"appServerGroupName,omitempty"`
	AppIPGroupID       string              `json:"appIpGroupId"`
	AppIPGroupName     string              `json:"appIpGroupName,omitempty"`
	GroupType          GroupType           `json:"groupType,omitempty"`
	FrontendPort       int                 `json:"frontendPort,omitempty"`
	BackendPort        int                 `json:"backendPort"`
	PortType           ServerGroupPortType `json:"portType,omitempty"`
	Priority           int                 `json:"priority"`
	RuleList           []*AppRule          `json:"ruleList"`
}

// AppRule AppBLB Rule
type AppRule struct {
	Key   AppRuleKeyType `json:"key"`
	Value string         `json:"value"`
}

// AppRuleKeyType AppRule key type
type AppRuleKeyType string

const (
	// AppRuleKeyTypeHost AppRuleKeyType host
	AppRuleKeyTypeHost AppRuleKeyType = "host"

	// AppRuleKeyTypeURI AppRuleKeyType uri
	AppRuleKeyTypeURI AppRuleKeyType = "uri"

	// AppRuleKeyTypeAll AppRuleKeyType *
	// Key = "*", Value Must be "*"
	AppRuleKeyTypeAll AppRuleKeyType = "*"
)

type AppIPGroup struct {
	ID                string                     `json:"id"`
	Name              string                     `json:"name"`
	Desc              string                     `json:"desc"`
	BackendPolicyList []*AppIPGroupBackendPolicy `json:"backendPolicyList"`
}

type BackendPolicyType string

const (
	BackendPolicyTypeTCP  BackendPolicyType = "TCP"
	BackendPolicyTypeUDP  BackendPolicyType = "UDP"
	BackendPolicyTypeHTTP BackendPolicyType = "HTTP"
)

type BackendPolicyHealthCheck string

const (
	BackendPolicyHealthCheckTCP  BackendPolicyHealthCheck = "TCP"
	BackendPolicyHealthCheckUDP  BackendPolicyHealthCheck = "UDP"
	BackendPolicyHealthCheckHTTP BackendPolicyHealthCheck = "HTTP"
)

type AppIPGroupBackendPolicy struct {
	ID                          string                   `json:"id"`
	Type                        BackendPolicyType        `json:"type"`
	HealthCheck                 BackendPolicyHealthCheck `json:"healthCheck"`
	HealthCheckPort             intstr.IntOrString       `json:"healthCheckPort"`
	HealthCheckTimeoutInSecond  int                      `json:"healthCheckTimeoutInSecond,omitempty"`
	HealthCheckDownRetry        int                      `json:"healthCheckDownRetry,omitempty"`
	HealthCheckUpRetry          int                      `json:"healthCheckUpRetry,omitempty"`
	HealthCheckIntervalInSecond int                      `json:"healthCheckIntervalInSecond,omitempty"`
	HealthCheckNormalStatus     string                   `json:"healthCheckNormalStatus,omitempty"`
	HealthCheckURLPath          string                   `json:"healthCheckUrlPath,omitempty"`
	UDPHealthCheckString        string                   `json:"udpHealthCheckString,omitempty"`
}

type AppIPGroupMember struct {
	IP       string             `json:"ip"`
	Port     intstr.IntOrString `json:"port"`
	Weight   int                `json:"weight"`
	MemberID string             `json:"memberId,omitempty"`
}

// CreateAppLoadBalancerArgs args for create app loadbalancer
type CreateAppLoadBalancerArgs struct {
	Name        string `json:"name"`
	Desc        string `json:"desc,omitempty"`
	VPCID       string `json:"vpcId"`
	SubNetID    string `json:"subnetId"`
	Tags        []Tag  `json:"tags,omitempty"`
	AllocateVIP bool   `json:"allocateVip,omitempty"`

	// AllowDelete 为 true or null 允许删除, false 开启删除保护不允许用户删除
	AllowDelete bool `json:"allowDelete"`

	Billing           *BillingForCreate `json:"billing"`
	AutoRenewTimeUnit string            `json:"autoRenewTimeUnit,omitempty"`
	AutoRenewTime     int               `json:"autoRenewTime,omitempty"`
	PerformanceLevel  PerformanceLevel  `json:"performanceLevel,omitempty"`

	// 四层网关是否使用专属集群 True:使用专属集群 False:不使用专属集群 Nil:采用BLB默认行为(存在专属集群时建在专属集群否则建在共享集群)
	// 标志四层实例是否建立在专属集群或lcc类型专属集群上
	Layer4ClusterExclusive *bool `json:"layer4ClusterExclusive,omitempty"`

	// 四层 BLB 专属集群 ID 非必填, 当layer4ClusterExclusive为true时必填, 四层实例所在专属集群或lcc类型专属集群id
	Layer4ClusterID *string `json:"layer4ClusterId,omitempty"`

	// 七层网关是否使用专属集群  True:使用专属集群 False:不使用专属集群 Nil:采用BLB默认行为(存在专属集群时建在专属集群否则建在共享集群)
	// 标志七层实例是否建立在专属集群或lcc类型专属集群上
	Layer7ClusterExclusive *bool `json:"layer7ClusterExclusive,omitempty"`

	// 七层BLB 专属集群 ID 非必填, 当layer7ClusterExclusive为true时必填, 七层实例所在专属集群或lcc类型专属集群id
	Layer7ClusterID *string `json:"layer7ClusterId,omitempty"`

	// 非必填, 当选择lcc类型专属集群时必填, 四层实例集群类型, 0 => 共享集群, 1 => 专属集群, 2 => lcc类型专属集群
	Layer4ClusterType *int `json:"layer4ClusterType,omitempty"`

	// 非必填, 当选择lcc类型专属集群时必填, 七层实例集群类型, 0 => 共享集群, 1 => 专属集群, 2 => lcc类型专属集群
	Layer7ClusterType *int `json:"layer7ClusterType,omitempty"`
}

type BLBClusterType int

var (
	BLBClusterTypeShared    BLBClusterType = 0
	BLBClusterTypeExclusive BLBClusterType = 1
	BLBClusterTypeLCC       BLBClusterType = 2
)

// CreateAppLoadBalancerResponse response for create app loadbalancer
type CreateAppLoadBalancerResponse struct {
	Name        string `json:"name"`
	Desc        string `json:"desc"`
	Address     string `json:"address"`
	BLBID       string `json:"blbId"`
	UnderlayVIP string `json:"underlayVip"`
}

type PerformanceLevel string

const (
	PerformanceLevelSmall1  PerformanceLevel = "small1"
	PerformanceLevelSmall2  PerformanceLevel = "small2"
	PerformanceLevelMedium1 PerformanceLevel = "medium1"
	PerformanceLevelMedium2 PerformanceLevel = "medium2"
)

type BillingForCreate struct {
	PaymentTiming PaymentTiming        `json:"paymentTiming"`
	Reservation   ReservationForCreate `json:"reservation"` // 购买月份时长，[1,2,3,4,5,6,7,8,9,12,24,36]
}

type ReservationForCreate struct {
	ReservationLength int `json:"reservationLength"`
}

// PaymentTiming 付费时间选择
type PaymentTiming string

const (
	// PaymentTimingPrepaid 预付费
	PaymentTimingPrepaid PaymentTiming = "Prepaid"

	// PaymentTimingPostpaid 后付费
	PaymentTimingPostpaid PaymentTiming = "Postpaid"
)

// UpdateAppLoadBalancerArgs to update APP BLB
type UpdateAppLoadBalancerArgs struct {
	Name         string `json:"name,omitempty"`
	Desc         string `json:"desc,omitempty"`
	AllocateIPv6 bool   `json:"allocateIpv6"`
	AllowDelete  bool   `json:"allowDelete"`
}

// DescribeLoadBalancerArgs describe APP BLB by conditions
type DescribeLoadBalancerArgs struct {
	Address      string `json:"address,omitempty"`
	Name         string `json:"name,omitempty"`
	BLBID        string `json:"blbId,omitempty"`
	BCCID        string `json:"bccId,omitempty"`
	ExactlyMatch bool   `json:"exactlyMatch,omitempty"`
	Marker       string `json:"marker,omitempty"`
	MaxKeys      int    `json:"maxKeys,omitempty"`
}

// DescribeAppLoadBalancerResponse describe APP BLBs
type DescribeAppLoadBalancerResponse struct {
	BLBList     []*AppLoadBalancer `json:"blbList"`
	Marker      string             `json:"marker"`
	Istruncated bool               `json:"isTruncated"`
	NextMarker  string             `json:"nextMarker"`
	MaxKeys     int                `json:"maxKeys"`
}

// CreateAppTCPListenerArgs create APP BLB TCP Listener
type CreateAppTCPListenerArgs struct {
	ListenerPort      int           `json:"listenerPort"` // 1-65535
	Scheduler         SchedulerType `json:"scheduler"`
	TcpSessionTimeout int           `json:"tcpSessionTimeout,omitempty"`
}

// CreateAppUDPListenerArgs create APP BLB UDP Listener
type CreateAppUDPListenerArgs struct {
	ListenerPort      int           `json:"listenerPort"` // 1-65535
	Scheduler         SchedulerType `json:"scheduler"`
	UdpSessionTimeout int           `json:"udpSessionTimeout,omitempty"`
}

// CreateAppTCPSSLListenerArgs 创建应用型 BLB TCP-SSL 监听器参数
type CreateAppTCPSSLListenerArgs struct {
	ListenerPort        int            `json:"listenerPort"` // 1-65535
	Scheduler           SchedulerType  `json:"scheduler"`
	CertIDs             []string       `json:"certIds"`
	EncryptionType      EncryptionType `json:"encryptionType,omitempty"`
	EncryptionProtocols []string       `json:"encryptionProtocols,omitempty"`
	AppliedCiphers      string         `json:"appliedCiphers,omitempty"`
	DualAuth            bool           `json:"dualAuth,omitempty"`
	ClientCertIDs       []string       `json:"clientCertIds,omitempty"`
	ServerTimeout       int            `json:"serverTimeout,omitempty"`
}

// CreateAppHTTPListenerArgs create APP BLB HTTP Listener
type CreateAppHTTPListenerArgs struct {
	ListenerPort          int                 `json:"listenerPort"` // 1-65535
	Scheduler             SchedulerType       `json:"scheduler"`
	KeepSession           bool                `json:"keepSession,omitempty"`
	KeepSessionType       KeepSessionType     `json:"keepSessionType,omitempty"`
	KeepSessionTimeout    int                 `json:"keepSessionTimeout,omitempty"`
	KeepSessionCookieName string              `json:"keepSessionCookieName,omitempty"`
	XForwardedFor         bool                `json:"xForwardedFor,omitempty"`
	XForwardedProto       bool                `json:"xForwardedProto,omitempty"`
	ServerTimeout         int                 `json:"serverTimeout,omitempty"`
	RedirectPort          int                 `json:"redirectPort,omitempty"`
	ExtendedAttributes    map[string][]string `json:"extendedAttributes,omitempty"`
}

// CreateAppHTTPSListenerArgs create APP BLB HTTPS Listener
type CreateAppHTTPSListenerArgs struct {
	ListenerPort          int                 `json:"listenerPort"` // 1-65535
	Scheduler             SchedulerType       `json:"scheduler"`
	CertIDs               []string            `json:"certIds"`
	KeepSession           bool                `json:"keepSession,omitempty"`
	KeepSessionType       KeepSessionType     `json:"keepSessionType,omitempty"`
	KeepSessionTimeout    int                 `json:"keepSessionTimeout,omitempty"`
	KeepSessionCookieName string              `json:"keepSessionCookieName,omitempty"`
	XForwardedFor         bool                `json:"xForwardedFor,omitempty"`
	XForwardedProto       bool                `json:"xForwardedProto,omitempty"`
	ServerTimeout         int                 `json:"serverTimeout,omitempty"`
	IE6Compatible         bool                `json:"ie6Compatible,omitempty"`
	EncryptionType        EncryptionType      `json:"encryptionType,omitempty"`
	EncryptionProtocols   []string            `json:"encryptionProtocols,omitempty"`
	AppliedCiphers        string              `json:"appliedCiphers,omitempty"`
	DualAuth              bool                `json:"dualAuth,omitempty"`
	ClientCertIDs         []string            `json:"clientCertIds,omitempty"`
	ExtendedAttributes    map[string][]string `json:"extendedAttributes,omitempty"`
}

// UpdateAppHTTPListenerArgs update APP BLB HTTP Listener
// RedirectPort must be set, if don't want change
type UpdateAppHTTPListenerArgs struct {
	Scheduler             SchedulerType   `json:"scheduler"`
	KeepSession           bool            `json:"keepSession,omitempty"`
	KeepSessionType       KeepSessionType `json:"keepSessionType,omitempty"`
	KeepSessionTimeout    int             `json:"keepSessionTimeout,omitempty"`
	KeepSessionCookieName string          `json:"keepSessionCookieName,omitempty"`
	XForwardedFor         bool            `json:"xForwardedFor,omitempty"`
	ServerTimeout         int             `json:"serverTimeout,omitempty"`
	RedirectPort          *int            `json:"redirectPort,omitempty"` // nil = maintain current status; 0 = close redirect
}

// UpdateAppHTTPSListenerArgs update APP BLB HTTPS Listener
type UpdateAppHTTPSListenerArgs struct {
	Scheduler             SchedulerType          `json:"scheduler"`
	KeepSession           bool                   `json:"keepSession,omitempty"`
	KeepSessionType       KeepSessionType        `json:"keepSessionType,omitempty"`
	KeepSessionTimeout    int                    `json:"keepSessionTimeout,omitempty"`
	KeepSessionCookieName string                 `json:"keepSessionCookieName,omitempty"`
	XForwardedFor         bool                   `json:"xForwardedFor,omitempty"`
	ServerTimeout         int                    `json:"serverTimeout,omitempty"`
	CertIDs               []string               `json:"certIds"`
	CompatibleIE          bool                   `json:"compatibleIE,omitempty"`
	EncryptionType        EncryptionType         `json:"encryptionType,omitempty"`
	EncryptionProtocols   []string               `json:"encryptionProtocols,omitempty"`
	DualAuth              bool                   `json:"dualAuth,omitempty"`
	ClientCertIDs         []string               `json:"clientCertIds,omitempty"`
	ExtendedAttributes    map[string]interface{} `json:"extendedAttributes,omitempty"`
}

// DescribeAppTCPListenerResponse describe APP BLB TCP Listener Response
type DescribeAppTCPListenerResponse struct {
	ListenerList []*TCPListener `json:"listenerList"`
	Marker       string         `json:"marker"`
	IsTruncated  bool           `json:"isTruncated"`
	NextMarker   string         `json:"nextMarker"`
	MaxKeys      int            `json:"maxKeys"`
}

// DescribeAppUDPListenerResponse describe APP BLB UDP Listener Response
type DescribeAppUDPListenerResponse struct {
	ListenerList []*UDPListener `json:"listenerList"`
	Marker       string         `json:"marker"`
	IsTruncated  bool           `json:"isTruncated"`
	NextMarker   string         `json:"nextMarker"`
	MaxKeys      int            `json:"maxKeys"`
}

// DescribeAppTCPSSLListenerResponse describe APP BLB TCP-SSL Listener Response
type DescribeAppTCPSSLListenerResponse struct {
	ListenerList []*TCPSSLListener `json:"listenerList"`
	Marker       string            `json:"marker"`
	IsTruncated  bool              `json:"isTruncated"`
	NextMarker   string            `json:"nextMarker"`
	MaxKeys      int               `json:"maxKeys"`
}

// DescribeAppHTTPListenerResponse describe APP BLB HTTP Listener Response
type DescribeAppHTTPListenerResponse struct {
	ListenerList []*HTTPListener `json:"listenerList"`
	Marker       string          `json:"marker"`
	IsTruncated  bool            `json:"isTruncated"`
	NextMarker   string          `json:"nextMarker"`
	MaxKeys      int             `json:"maxKeys"`
}

// DescribeAppHTTPSListenerResponse describe APP BLB HTTPS Listener Response
type DescribeAppHTTPSListenerResponse struct {
	ListenerList []*HTTPSListener `json:"listenerList"`
	Marker       string           `json:"marker"`
	IsTruncated  bool             `json:"isTruncated"`
	NextMarker   string           `json:"nextMarker"`
	MaxKeys      int              `json:"maxKeys"`
}

// DeleteAppListenersArgs delete APP BLB Listeners args
type DeleteAppListenersArgs struct {
	PortList []int `json:"portList"`
}

// CreateAppPolicyArgs create appblb policy args
type CreateAppPolicyArgs struct {
	ListenerPort  int          `json:"listenerPort"`
	AppPolicyList []*AppPolicy `json:"appPolicyVos"`
}

// DescribeAppPolicysArgs describe AppPolicy args
type DescribeAppPolicysArgs struct {
	Port int `json:"port"`
	// 当监听器端口下有多个协议时，type必传
	Type    string `json:"type,omitempty"`
	Marker  string `json:"marker,omitempty"`
	MaxKeys int    `json:"maxKeys,omitempty"`
}

// DescribeAppPolicysResponse describe AppPolicy Response
type DescribeAppPolicysResponse struct {
	PolicyList  []*AppPolicy `json:"policyList"`
	Marker      string       `json:"marker,omitempty"`
	IsTruncated bool         `json:"isTruncated,omitempty"`
	NextMarker  string       `json:"nextMarker,omitempty"`
	MaxKeys     int          `json:"maxKeys,omitempty"`
}

// DeleteAppPolicysArgs delete appPolicy args
type DeleteAppPolicysArgs struct {
	Port         int      `json:"port"`
	PolicyIDList []string `json:"policyIdList"`
}

type BindSecurityGroupArgs struct {
	SecurityGroupIds []string `json:"securityGroupIds"`
}

type UnbindSecurityGroupArgs struct {
	SecurityGroupIds []string `json:"securityGroupIds"`
}

type GetBLBSecurityGroupResponse struct {
	SecurityGroups []BLBSecurityGroup `json:"securityGroups"`
}

type BLBSecurityGroup struct {
	SecurityGroupId    string              `json:"securityGroupId,omitempty"`
	SecurityGroupName  string              `json:"securityGroupName,omitempty"`
	SecurityGroupDesc  string              `json:"securityGroupDesc,omitempty"`
	VpcName            string              `json:"vpcName,omitempty"`
	SecurityGroupRules []SecurityGroupRule `json:"securityGroupRules,omitempty"`
}

type SecurityGroupRule struct {
	SecurityGroupRuleId string `json:"securityGroupRuleId,omitempty"` // 安全组规则ID
	Direction           string `json:"direction,omitempty"`           // 入站/出站，取值ingress或egress
	Ethertype           string `json:"ethertype,omitempty"`           // 网络类型，取值IPv4或IPv6，值为空时表示默认取值IPv4
	PortRange           string `json:"portRange,omitempty"`           // 端口范围，可以指定80等单个端口，值为空时默认取值1-65535
	Protocol            string `json:"protocol,omitempty"`            // 协议类型，tcp、udp或icmp，值为空时默认取值all
	SourceGroupId       string `json:"sourceGroupId,omitempty"`       // 源安全组ID
	SourceIp            string `json:"sourceIp,omitempty"`            // 源IP地址，与sourceGroupId不能同时设定值
	DestGroupId         string `json:"destGroupId,omitempty"`         // 目的安全组ID
	DestIp              string `json:"destIp,omitempty"`              // 目的IP地址，与destGroupId不能同时设定值
}

type BindSecurityGroupResponse struct {
}

// CreateAppServerGroupPortArgs create appblb servergroup port args
type CreateAppServerGroupPortArgs struct {
	ServerGroupID               string                         `json:"sgId"`
	Port                        int                            `json:"port"`
	Type                        ServerGroupPortType            `json:"type"`
	HealthCheck                 ServerGroupPortHealthCheckType `json:"healthCheck,omitempty"`
	HealthCheckPort             int                            `json:"healthCheckPort,omitempty"`
	HealthCheckURLPath          string                         `json:"healthCheckUrlPath,omitempty"`
	HealthCheckTimeoutInSecond  int                            `json:"healthCheckTimeoutInSecond,omitempty"`
	HealthCheckIntervalInSecond int                            `json:"healthCheckIntervalInSecond,omitempty"`
	HealthCheckDownRetry        int                            `json:"healthCheckDownRetry,omitempty"`
	HealthCheckUpRetry          int                            `json:"healthCheckUpRetry,omitempty"`
	HealthCheckNormalStatus     string                         `json:"healthCheckNormalStatus,omitempty"`
	UDPHealthCheckString        string                         `json:"udpHealthCheckString,omitempty"`
}

// CreateAppServerGroupPortResponse create appblb servergroup port response
type CreateAppServerGroupPortResponse struct {
	ID     string    `json:"id"`
	Status BLBStatus `json:"status"`
}

// UpdateAppServerGroupPortArgs update appblb servergroup port args
type UpdateAppServerGroupPortArgs struct {
	ServerGroupID               string                         `json:"sgId"`
	PortID                      string                         `json:"portId"`
	HealthCheck                 ServerGroupPortHealthCheckType `json:"healthCheck,omitempty"`
	HealthCheckPort             int                            `json:"healthCheckPort,omitempty"`
	HealthCheckURLPath          string                         `json:"healthCheckUrlPath,omitempty"`
	HealthCheckTimeoutInSecond  int                            `json:"healthCheckTimeoutInSecond,omitempty"`
	HealthCheckIntervalInSecond int                            `json:"healthCheckIntervalInSecond,omitempty"`
	HealthCheckDownRetry        int                            `json:"healthCheckDownRetry,omitempty"`
	HealthCheckUpRetry          int                            `json:"healthCheckUpRetry,omitempty"`
	HealthCheckNormalStatus     string                         `json:"healthCheckNormalStatus,omitempty"`
}

// DeleteAppServerGroupPortArgs delete AppBLB ServerGroup Port args
type DeleteAppServerGroupPortArgs struct {
	ServerGroupID string   `json:"sgId"`
	PortIDList    []string `json:"portIdList"`
}

// CreateAppServerGroupArgs for create appServerGroup args
type CreateAppServerGroupArgs struct {
	Name              string              `json:"name,omitempty"`
	Desc              string              `json:"desc,omitempty"`
	BackendServerList []*AppBackendServer `json:"backendServerList,omitempty"`
}

// CreateAppServerGroupResponse for crreate appServerGroup response
type CreateAppServerGroupResponse struct {
	ID     string    `json:"id"`
	Name   string    `json:"name"`
	Desc   string    `json:"desc"`
	Status BLBStatus `json:"status"` // the same as BLBStatus
}

// UpdateAppServerGroupArgs update appServerGroup
type UpdateAppServerGroupArgs struct {
	ServerGroupID string `json:"sgId"`
	Name          string `json:"name,omitempty"`
	Desc          string `json:"desc,omitempty"`
}

// DescribeAppServerGroupArgs to describe appServerGroup args
type DescribeAppServerGroupArgs struct {
	Name         string `json:"name"`
	ExactlyMatch bool   `json:"exactlyMatch"`
	Marker       string `json:"marker"`
	MaxKeys      int    `json:"maxKeys"`
}

// DescribeAppServerGroupResponse to describe appServerGroup response
type DescribeAppServerGroupResponse struct {
	AppServerGroupList []*AppServerGroup `json:"appServerGroupList"`
	Marker             string            `json:"marker"`
	IsTruncated        bool              `json:"isTruncated"`
	NextMarker         string            `json:"nextMarker"`
	MaxKeys            int               `json:"maxKeys"`
}

// DeleteAppServerGroupArgs for delete appServerGroup
type DeleteAppServerGroupArgs struct {
	ServerGroupID string `json:"sgId"`
}
