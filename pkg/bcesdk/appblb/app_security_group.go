package appblb

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func (c *Client) BindSecurityGroupForAppBLB(ctx context.Context, blbID string, args *BindSecurityGroupArgs, option *bce.SignOption) error {
	params := map[string]string{
		"bind":        "",
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("blb/%s/securitygroup", blbID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

func (c *Client) DescribeSecurityGroupForAppBLB(ctx context.Context, blbID string,
	option *bce.SignOption) (*GetBLBSecurityGroupResponse, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	url := fmt.Sprintf("blb/%s/securitygroup", blbID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, fmt.Errorf("get body content: %w", err)
	}

	var listResp *GetBLBSecurityGroupResponse
	err = json.Unmarshal(bodyContent, &listResp)
	if err != nil {
		return nil, fmt.Errorf("unmarshal %s: %w", string(bodyContent), err)
	}

	return listResp, nil
}

func (c *Client) UnbindSecurityGroupForAppBLB(ctx context.Context, blbID string, args *UnbindSecurityGroupArgs,
	option *bce.SignOption) error {
	params := map[string]string{
		"unbind":      "",
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("blb/%s/securitygroup", blbID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}
