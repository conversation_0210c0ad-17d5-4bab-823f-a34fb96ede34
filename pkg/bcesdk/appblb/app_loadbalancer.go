// Package appblb for bce application loadbalancer
package appblb

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// CreateAppLoadBalancer create a application loadbalancer
func (c *Client) CreateAppLoadBalancer(ctx context.Context, args *CreateAppLoadBalancerArgs, option *bce.SignOption) (
	*CreateAppLoadBalancerResponse, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("appblb", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var blbsResp *CreateAppLoadBalancerResponse
	err = json.Unmarshal(bodyContent, &blbsResp)
	if err != nil {
		return nil, err
	}

	return blbsResp, nil
}

// UpdateAppLoadBalancer update APP BLB
func (c *Client) UpdateAppLoadBalancer(ctx context.Context, blbShortID string, args *UpdateAppLoadBalancerArgs, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s", blbShortID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()

	return err
}

// DescribeAppLoadBalancers describe AppLoadBalancers by conditions
// If BLB not exist, err == nil but resq.BLBList = []
func (c *Client) DescribeAppLoadBalancers(ctx context.Context, args *DescribeLoadBalancerArgs, option *bce.SignOption) (*DescribeAppLoadBalancerResponse, error) {
	// Get conditions
	params := map[string]string{}

	if args != nil {
		if args.Address != "" {
			params["address"] = args.Address
		}

		if args.Name != "" {
			params["name"] = args.Name
		}

		if args.BLBID != "" {
			params["blbId"] = args.BLBID
		}

		if args.BCCID != "" {
			params["bccId"] = args.BCCID
		}

		// True = exactlyMatch
		if args.ExactlyMatch {
			params["exactlyMatch"] = "true"
		} else {
			params["exactlyMatch"] = "false"
		}

		if args.Marker != "" {
			params["marker"] = args.Marker
		}

		if args.MaxKeys != 0 {
			params["maxKeys"] = fmt.Sprintf("%d", args.MaxKeys)
		}
	}

	// Send Request
	req, err := bce.NewRequest("GET", c.GetURL("appblb", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var blbsResp *DescribeAppLoadBalancerResponse
	err = json.Unmarshal(bodyContent, &blbsResp)
	if err != nil {
		return nil, err
	}

	return blbsResp, nil
}

// DescribeAppLoadBalancersByName describe APP BLBs by blbName
// If blbName not exist, return err == nil
func (c *Client) DescribeAppLoadBalancersByName(ctx context.Context, blbName string, option *bce.SignOption) ([]*AppLoadBalancer, error) {
	if blbName == "" {
		return nil, errors.New("DescribeAppLoadBalancerByName failed: blbName is nil")
	}

	args := &DescribeLoadBalancerArgs{
		Name:         blbName,
		ExactlyMatch: true,
	}

	blbsResp, err := c.DescribeAppLoadBalancers(ctx, args, option)
	if err != nil || blbsResp == nil {
		return nil, err
	}

	// If BLB not exist, err == nil
	if len(blbsResp.BLBList) == 0 {
		return []*AppLoadBalancer{}, nil
	}

	return blbsResp.BLBList, nil
}

// DescribeAppLoadBalancerByID describe load balancer detail by blbID
// DescribeAppLoadBalancerByID after CreateAppBLB immediately may return NoSuchObject
// If blbShortID not exist, err != nil and contains "NoSuchObject"
func (c *Client) DescribeAppLoadBalancerByID(ctx context.Context, blbShortID string, option *bce.SignOption) (*AppLoadBalancer, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	url := fmt.Sprintf("appblb/%s", blbShortID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)

	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var blbsResp *AppLoadBalancer
	err = json.Unmarshal(bodyContent, &blbsResp)
	if err != nil {
		return nil, err
	}

	return blbsResp, nil
}

// DeleteAppLoadBalancer delete loadbalancer
func (c *Client) DeleteAppLoadBalancer(ctx context.Context, blbShortID string, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	url := fmt.Sprintf("appblb/%s", blbShortID)

	req, err := bce.NewRequest("DELETE", c.GetURL(url, params), nil)
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()

	return err
}
