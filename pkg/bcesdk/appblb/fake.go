// Package appblb for bce application loadbalancer
package appblb

import (
	"context"
	"errors"
	"fmt"

	"k8s.io/apimachinery/pkg/util/intstr"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
)

type appServerGroupDetail struct {
	appServerGroup       *AppServerGroup
	AppBackendServerList map[string]AppBackendServer
	AppServerGroupPort   map[int]int
}

type appIPGroupDetail struct {
	appIPGroup           *AppIPGroup
	appIPGroupMemberList map[string]AppIPGroupMember
}

// FakeClient for AppBLB fake client
type FakeClient struct {
	AppLoadBalancerMap map[string]*AppLoadBalancer
	HTTPListenerMap    map[string]*HTTPListener
	HTTPSListenerMap   map[string]*HTTPSListener
	TCPListenerMap     map[string]*TCPListener
	UDPListenerMap     map[string]*UDPListener
	PolicysMap         map[string]map[string]*AppPolicy
	ServerGroupMap     map[string]*appServerGroupDetail
	IPGroupMap         map[string]*appIPGroupDetail
}

// NewFakeClient for AppBLB fake client
func NewFakeClient() *FakeClient {
	return &FakeClient{
		// Key = blb_id
		AppLoadBalancerMap: map[string]*AppLoadBalancer{},

		// Key = blb_id|port
		HTTPListenerMap: map[string]*HTTPListener{},

		// Key = blb_id|port
		HTTPSListenerMap: map[string]*HTTPSListener{},

		// Key = blb_id|port
		// Key = policyID
		PolicysMap: map[string]map[string]*AppPolicy{},

		// Key = blb_id|server_group_id
		ServerGroupMap: map[string]*appServerGroupDetail{},
	}
}

// SetDebug 开启调试
func (f *FakeClient) SetDebug(debug bool) {
	return
}

// CreateAppLoadBalancer to create AppLoadBalancer
func (f *FakeClient) CreateAppLoadBalancer(ctx context.Context, args *CreateAppLoadBalancerArgs, option *bce.SignOption) (*CreateAppLoadBalancerResponse, error) {
	if args == nil {
		return nil, errors.New("CreateAppLoadBalancer faile: args is nil")
	}

	blb := &AppLoadBalancer{
		Name:   args.Name,
		Status: BLBStatusAvailable,
	}

	for {
		blbID := util.GenerateBCEShortID("lb")
		if _, ok := f.AppLoadBalancerMap[blbID]; !ok {
			blb.BLBID = blbID
			f.AppLoadBalancerMap[blbID] = blb

			break
		}
	}

	return &CreateAppLoadBalancerResponse{
		Name:  blb.Name,
		BLBID: blb.BLBID,
	}, nil
}

// DescribeAppLoadBalancerByID get BLB by ID
func (f *FakeClient) DescribeAppLoadBalancerByID(ctx context.Context, blbShortID string,
	option *bce.SignOption) (*AppLoadBalancer, error) {
	if blbShortID == "lb-xxxx" {
		return &AppLoadBalancer{
			BLBID:       "lb-xxx",
			Address:     "**********",
			IPv6Address: "240c:4082:0:d104::7f",
		}, nil
	}
	if blb, ok := f.AppLoadBalancerMap[blbShortID]; ok {
		return blb, nil
	}

	return nil, errors.New("NoSuchObject")
}

// DescribeAppLoadBalancersByName get BLBs by Name
func (f *FakeClient) DescribeAppLoadBalancersByName(ctx context.Context, blbName string,
	option *bce.SignOption) ([]*AppLoadBalancer, error) {
	result := []*AppLoadBalancer{}

	for _, blb := range f.AppLoadBalancerMap {
		if blb.Name == blbName {
			result = append(result, blb)
		}
	}

	return result, nil
}

func (f *FakeClient) UpdateAppLoadBalancer(ctx context.Context, blbShortID string,
	args *UpdateAppLoadBalancerArgs, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) DescribeAppLoadBalancers(ctx context.Context,
	args *DescribeLoadBalancerArgs, option *bce.SignOption) (*DescribeAppLoadBalancerResponse, error) {
	return &DescribeAppLoadBalancerResponse{
		BLBList: []*AppLoadBalancer{
			{
				BLBID:       "lb-xxx",
				Address:     "**********",
				IPv6Address: "240c:4082:0:d104::7f",
			},
		},
	}, nil
}

// DeleteAppLoadBalancer delete pointed AppLoadBalancer by BLBID
func (f *FakeClient) DeleteAppLoadBalancer(ctx context.Context, blbShortID string, option *bce.SignOption) error {
	if _, ok := f.AppLoadBalancerMap[blbShortID]; ok {
		delete(f.AppLoadBalancerMap, blbShortID)
		return nil
	}

	return fmt.Errorf("AppLoadBalancer %s not exist", blbShortID)
}

// CreateAppTCPListener to create TCP Listener
func (f *FakeClient) CreateAppTCPListener(ctx context.Context, lbShortID string, args *CreateAppTCPListenerArgs, option *bce.SignOption) error {
	// Check if AppBLB exist
	blb, ok := f.AppLoadBalancerMap[lbShortID]
	if !ok {
		return fmt.Errorf("CreateAppTCPListener failed: app_blb %s not exist", lbShortID)
	}

	// Check if AppTCPListener exist
	for _, listener := range blb.ListenerList {
		if listener.Type == ListenerTypeHTTP && int(listener.Port.IntVal) == args.ListenerPort {
			return fmt.Errorf("CreateAppTCPListener failed: listener %d already exist", args.ListenerPort)
		}
	}

	// Add Listener
	blb.ListenerList = append(blb.ListenerList, &Listener{
		Type: ListenerTypeHTTP,
		Port: intstr.FromInt(args.ListenerPort),
	})

	// Set default
	if args.TcpSessionTimeout == 0 {
		args.TcpSessionTimeout = 30
	}

	// listenerKey
	listenerKey := httpListenerKey(lbShortID, args.ListenerPort)
	f.TCPListenerMap[listenerKey] = &TCPListener{
		ListenerPort: args.ListenerPort,
		Scheduler:    args.Scheduler,
	}

	return nil
}

// DescribeAppTCPListener to get TCP Listeners
func (f *FakeClient) DescribeAppTCPListener(ctx context.Context, lbShortID string, port int, option *bce.SignOption) (*DescribeAppTCPListenerResponse, error) {
	return nil, nil
}

// CreateAppHTTPListener to create HTTP Listener
func (f *FakeClient) CreateAppHTTPListener(ctx context.Context, lbShortID string, args *CreateAppHTTPListenerArgs, option *bce.SignOption) error {
	// Check if AppBLB exist
	blb, ok := f.AppLoadBalancerMap[lbShortID]
	if !ok {
		return fmt.Errorf("CreateAppHTTPListener failed: app_blb %s not exist", lbShortID)
	}

	// Check if HTTPListener exist
	for _, listener := range blb.ListenerList {
		if listener.Type == ListenerTypeHTTP && int(listener.Port.IntVal) == args.ListenerPort {
			return fmt.Errorf("CreateAppHTTPListener failed: listener %d already exist", args.ListenerPort)
		}
	}

	// Add Listener
	blb.ListenerList = append(blb.ListenerList, &Listener{
		Type: ListenerTypeHTTP,
		Port: intstr.FromInt(args.ListenerPort),
	})

	// Set default
	if args.ServerTimeout == 0 {
		args.ServerTimeout = 30
	}

	// If set RedirectPort, check if HTTPSListener exist
	if args.RedirectPort != 0 {
		httpsListenerKey := httpsListenerKey(lbShortID, args.RedirectPort)
		_, ok := f.HTTPSListenerMap[httpsListenerKey]
		if !ok {
			return fmt.Errorf("CreateAppHTTPListener failed: redirect-port %d not exist", args.RedirectPort)
		}
	}

	// listenerKey
	listenerKey := httpListenerKey(lbShortID, args.ListenerPort)
	f.HTTPListenerMap[listenerKey] = &HTTPListener{
		ListenerPort:          args.ListenerPort,
		Scheduler:             args.Scheduler,
		KeepSession:           args.KeepSession,
		KeepSessionType:       args.KeepSessionType,
		KeepSessionTimeout:    args.KeepSessionTimeout,
		KeepSessionCookieName: args.KeepSessionCookieName,
		XForwardedFor:         args.XForwardedFor,
		ServerTimeout:         args.ServerTimeout,
		RedirectPort:          args.RedirectPort,
	}

	return nil
}

// UpdateAppHTTPListener update AppBLB HTTPListener
func (f *FakeClient) UpdateAppHTTPListener(ctx context.Context, lbShortID string, port int, args *UpdateAppHTTPListenerArgs, option *bce.SignOption) error {
	// Check if AppBLB exist
	_, ok := f.AppLoadBalancerMap[lbShortID]
	if !ok {
		return fmt.Errorf("UpdateAppHTTPListener failed: app_blb %s not exist", lbShortID)
	}

	// Check if port exist
	httpListenerKey := httpListenerKey(lbShortID, port)
	listener, ok := f.HTTPListenerMap[httpListenerKey]
	if !ok {
		return fmt.Errorf("UpdateAppHTTPListener failed: HTTP listener %d not exist", port)
	}

	// Scheduer
	if args.Scheduler != "" {
		listener.Scheduler = args.Scheduler
	}

	// ServerTimeout
	if args.ServerTimeout != 0 {
		if args.ServerTimeout < 0 || args.ServerTimeout > 3600 {
			return errors.New("UpdateAppHTTPListener failed: serverTimeout must between 0~3600")
		}

		listener.ServerTimeout = args.ServerTimeout
	}

	// Check if HTTPS Listener exist
	if args.RedirectPort != nil {
		redirectPort := *args.RedirectPort

		if redirectPort != 0 {
			httpsListenerKey := httpsListenerKey(lbShortID, redirectPort)
			if _, ok := f.HTTPSListenerMap[httpsListenerKey]; !ok {
				return fmt.Errorf("CreateAppHTTPListener failed: redirect-port %d not exist", redirectPort)
			}
		}

		listener.RedirectPort = redirectPort
	}

	return nil
}

// DescribeAppHTTPListener to get HTTP Listeners
func (f *FakeClient) DescribeAppHTTPListener(ctx context.Context, lbShortID string, port int, option *bce.SignOption) (*DescribeAppHTTPListenerResponse, error) {
	// Check if AppBLB exist
	_, ok := f.AppLoadBalancerMap[lbShortID]
	if !ok {
		return nil, fmt.Errorf("DescribeAppHTTPListener failed: app_blb %s not exist", lbShortID)
	}

	// Check if port exist
	httpListenerKey := httpListenerKey(lbShortID, port)
	listener, ok := f.HTTPListenerMap[httpListenerKey]
	if !ok {
		return &DescribeAppHTTPListenerResponse{
			ListenerList: []*HTTPListener{},
		}, nil
	}

	return &DescribeAppHTTPListenerResponse{
		ListenerList: []*HTTPListener{
			listener,
		},
	}, nil
}

// CreateAppHTTPSListener to create HTTPS Listener
func (f *FakeClient) CreateAppHTTPSListener(ctx context.Context, lbShortID string, args *CreateAppHTTPSListenerArgs, option *bce.SignOption) error {
	// Check if AppBLB exist
	blb, ok := f.AppLoadBalancerMap[lbShortID]
	if !ok {
		return fmt.Errorf("CreateAppHTTPListener failed: app_blb %s not exist", lbShortID)
	}

	// Check if HTTPSListener exist
	for _, listener := range blb.ListenerList {
		if listener.Type == ListenerTypeHTTPS && int(listener.Port.IntVal) == args.ListenerPort {
			return fmt.Errorf("CreateAppHTTPSListener failed: listener %d already exist", args.ListenerPort)
		}
	}

	// Add Listener
	blb.ListenerList = append(blb.ListenerList, &Listener{
		Type: ListenerTypeHTTPS,
		Port: intstr.FromInt(args.ListenerPort),
	})

	// Set default
	if args.ServerTimeout == 0 {
		args.ServerTimeout = 30
	}

	// listenerKey
	listenerKey := httpsListenerKey(lbShortID, args.ListenerPort)
	f.HTTPSListenerMap[listenerKey] = &HTTPSListener{
		ListenerPort:          args.ListenerPort,
		Scheduler:             args.Scheduler,
		KeepSession:           args.KeepSession,
		KeepSessionType:       args.KeepSessionType,
		KeepSessionTimeout:    args.KeepSessionTimeout,
		KeepSessionCookieName: args.KeepSessionCookieName,
		XForwardedFor:         args.XForwardedFor,
		ServerTimeout:         args.ServerTimeout,
		CertIDs:               args.CertIDs,
		EncryptionProtocols:   args.EncryptionProtocols,
		EncryptionType:        args.EncryptionType,
		DualAuth:              args.DualAuth,
		ClientCertIDs:         args.ClientCertIDs,
	}

	return nil
}

// UpdateAppHTTPSListener update AppBLB HTTPS Listener
func (f *FakeClient) UpdateAppHTTPSListener(ctx context.Context, lbShortID string, port int, args *UpdateAppHTTPSListenerArgs, option *bce.SignOption) error {
	// Check if AppBLB exist
	_, ok := f.AppLoadBalancerMap[lbShortID]
	if !ok {
		return fmt.Errorf("UpdateAppHTTPListener failed: app_blb %s not exist", lbShortID)
	}

	// Check if port exist
	httpsListenerKey := httpsListenerKey(lbShortID, port)
	listener, ok := f.HTTPSListenerMap[httpsListenerKey]
	if !ok {
		return fmt.Errorf("UpdateAppHTTPSListener failed: HTTP listener %d not exist", port)
	}

	// Scheduer
	if args.Scheduler != "" {
		listener.Scheduler = args.Scheduler
	}

	// ServerTimeout
	if args.ServerTimeout != 0 {
		if args.ServerTimeout < 0 || args.ServerTimeout > 3600 {
			return errors.New("UpdateAppHTTPListener failed: serverTimeout must between 0~3600")
		}

		listener.ServerTimeout = args.ServerTimeout
	}

	return nil
}

// DescribeAppHTTPSListener return AppBLB's listener
func (f *FakeClient) DescribeAppHTTPSListener(ctx context.Context, lbShortID string, port int, option *bce.SignOption) (*DescribeAppHTTPSListenerResponse, error) {
	// Check if AppBLB exist
	_, ok := f.AppLoadBalancerMap[lbShortID]
	if !ok {
		return nil, fmt.Errorf("DescribeAppHTTPSListener failed: app_blb %s not exist", lbShortID)
	}

	// Check if port exist
	httpListenerKey := httpListenerKey(lbShortID, port)
	listener, ok := f.HTTPSListenerMap[httpListenerKey]
	if !ok {
		return &DescribeAppHTTPSListenerResponse{
			ListenerList: []*HTTPSListener{},
		}, nil
	}

	return &DescribeAppHTTPSListenerResponse{
		ListenerList: []*HTTPSListener{
			listener,
		},
	}, nil
}

func (f *FakeClient) DeleteAppListeners(ctx context.Context, lbShortID string, args *DeleteAppListenersArgs, option *bce.SignOption) error {
	return nil
}

// CreateAppPolicys create app policys under listener
func (f *FakeClient) CreateAppPolicys(ctx context.Context, lbShortID string,
	args *CreateAppPolicyArgs, option *bce.SignOption) error {
	// Check if AppBLB exist
	_, ok := f.AppLoadBalancerMap[lbShortID]
	if !ok {
		return fmt.Errorf("CreateAppPolicys failed: app_blb %s not exist", lbShortID)
	}

	// Check if port exist
	listenerKey := httpListenerKey(lbShortID, args.ListenerPort)

	isListenerExist := false

	if _, ok := f.HTTPSListenerMap[listenerKey]; ok {
		isListenerExist = true
	}

	if _, ok := f.HTTPSListenerMap[listenerKey]; ok {
		isListenerExist = true
	}

	if !isListenerExist {
		return fmt.Errorf("CreateAppPolicys failed: app_blb %s port %d not exist", lbShortID, args.ListenerPort)
	}

	// Existing Policy
	policysMap := f.PolicysMap[listenerKey]
	priorityMap := map[int]int{}
	policyIDMap := map[string]int{}

	for _, policy := range policysMap {
		priorityMap[policy.Priority] = 0
	}

	// Update Policys Info
	for _, p := range args.AppPolicyList {
		_, ok := priorityMap[p.Priority]
		if ok {
			return fmt.Errorf("CreateAppPolicys failed: priority %d already exist", p.Priority)
		}

		for {
			policyID := util.GenerateBCEShortID("p")
			if _, ok := policyIDMap[policyID]; !ok {
				p.ID = policyID

				policysMap[policyID] = p
				break
			}
		}
	}

	return nil
}

// DescribeAppPolicys to return AppPolicy
func (f *FakeClient) DescribeAppPolicys(ctx context.Context, lbShortID string,
	args *DescribeAppPolicysArgs, option *bce.SignOption) (*DescribeAppPolicysResponse, error) {
	// Check if AppBLB exist
	_, ok := f.AppLoadBalancerMap[lbShortID]
	if !ok {
		return nil, fmt.Errorf("DescribeAppPolicys failed: app_blb %s not exist", lbShortID)
	}

	// Check if port exist
	listenerKey := httpListenerKey(lbShortID, args.Port)

	isListenerExist := false

	if _, ok := f.HTTPSListenerMap[listenerKey]; ok {
		isListenerExist = true
	}

	if _, ok := f.HTTPSListenerMap[listenerKey]; ok {
		isListenerExist = true
	}

	if !isListenerExist {
		return nil, fmt.Errorf("DescribeAppPolicys failed: app_blb %s port %d not exist", lbShortID, args.Port)
	}

	// Check if Policys exist
	policyList := []*AppPolicy{}
	policysMap, ok := f.PolicysMap[listenerKey]
	if ok {
		for _, p := range policysMap {
			policyList = append(policyList, p)
		}
	}

	return &DescribeAppPolicysResponse{
		PolicyList: policyList,
	}, nil
}

// DeleteAppPolicys delete AppBLB policys
func (f *FakeClient) DeleteAppPolicys(ctx context.Context, lbShortID string,
	args *DeleteAppPolicysArgs, option *bce.SignOption) error {
	// Check args
	if args == nil {
		return nil
	}

	// Check if AppBLB exist
	_, ok := f.AppLoadBalancerMap[lbShortID]
	if !ok {
		return fmt.Errorf("DeleteAppPolicys failed: app_blb %s not exist", lbShortID)
	}

	// Check if port exist
	listenerKey := httpListenerKey(lbShortID, args.Port)

	isListenerExist := false

	if _, ok := f.HTTPSListenerMap[listenerKey]; ok {
		isListenerExist = true
	}

	if _, ok := f.HTTPSListenerMap[listenerKey]; ok {
		isListenerExist = true
	}

	if !isListenerExist {
		return fmt.Errorf("DeleteAppPolicys failed: app_blb %s port %d not exist", lbShortID, args.Port)
	}

	// if Port Policys not exist, return nil
	existingPolicyList, ok := f.PolicysMap[listenerKey]
	if !ok {
		return nil
	}

	for _, policyID := range args.PolicyIDList {
		if _, ok := existingPolicyList[policyID]; ok {
			delete(existingPolicyList, policyID)
		}
	}

	return nil
}

// CreateAppServerGroup to create AppServerGroup
func (f *FakeClient) CreateAppServerGroup(ctx context.Context, lbShortID string,
	args *CreateAppServerGroupArgs, option *bce.SignOption) (*CreateAppServerGroupResponse, error) {
	// Check args
	if args == nil {
		return nil, nil
	}

	// Check if AppBLB exist
	_, ok := f.AppLoadBalancerMap[lbShortID]
	if !ok {
		return nil, fmt.Errorf("CreateAppServerGroup failed: app_blb %s not exist", lbShortID)
	}

	serverGroup := &AppServerGroup{
		Name:   args.Name,
		Desc:   args.Desc,
		Status: BLBStatusAvailable,
	}

	for {
		serverGroupID := util.GenerateBCEShortID("sg")
		key := serverGroupKey(lbShortID, serverGroupID)

		if _, ok := f.ServerGroupMap[key]; !ok {
			serverGroup.ID = serverGroupID
			f.ServerGroupMap[key] = &appServerGroupDetail{
				appServerGroup: serverGroup,
			}

			break
		}
	}

	return &CreateAppServerGroupResponse{
		ID:     serverGroup.ID,
		Name:   serverGroup.Name,
		Desc:   serverGroup.Desc,
		Status: serverGroup.Status,
	}, nil
}

func (f *FakeClient) UpdateAppServerGroup(ctx context.Context, lbShortID string, args *UpdateAppServerGroupArgs, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) DescribeAppServerGroup(ctx context.Context, lbShortID string, args *DescribeAppServerGroupArgs, option *bce.SignOption) (*DescribeAppServerGroupResponse, error) {
	return nil, nil
}

func (f *FakeClient) DescribeAppServerGroupByID(ctx context.Context, lbShortID, serverGroupID, serverGroupName string, option *bce.SignOption) (*AppServerGroup, error) {
	return nil, nil
}

func (f *FakeClient) DeleteAppServerGroup(ctx context.Context, lbShortID string, args *DeleteAppServerGroupArgs, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) CreateAppServerGroupRS(ctx context.Context, lbShortID string, args *CreateAppServerGroupRSArgs, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) UpdateAppServerGroupRSWeight(ctx context.Context, lbShortID string, args *UpdateAppServerGroupRSWeightArgs, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) DescribeAppServerGroupRS(ctx context.Context, lbShortID, serverGroupID string, option *bce.SignOption) (*DescribeAppServerGroupRSResponse, error) {
	return nil, nil
}

func (f *FakeClient) DescribeAppServerGroupRSMount(ctx context.Context, lbShortID, serverGroupID string, option *bce.SignOption) ([]*AppBackendServer, error) {
	return nil, nil
}

func (f *FakeClient) DescribeAppServerGroupRSUnMount(ctx context.Context, lbShortID, serverGroupID string, option *bce.SignOption) ([]*AppBackendServer, error) {
	return nil, nil
}

func (f *FakeClient) DeleteAppServerGroupRS(ctx context.Context, lbShortID string, args *DeleteAppServerGroupRSArgs, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) CreateAppServerGroupPort(ctx context.Context, lbShortID string, args *CreateAppServerGroupPortArgs, option *bce.SignOption) (*CreateAppServerGroupPortResponse, error) {
	return nil, nil
}

func (f *FakeClient) UpdateAppServerGroupPort(ctx context.Context, lbShortID string, args *UpdateAppServerGroupPortArgs, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) DeleteAppServerGroupPort(ctx context.Context, lbShortID string, args *DeleteAppServerGroupPortArgs, option *bce.SignOption) error {
	return nil
}

// TODO: @sun IPGroup SDK add ut
func (f *FakeClient) CreateAppUDPListener(ctx context.Context, lbShortID string, args *CreateAppUDPListenerArgs, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) DescribeAppUDPListener(ctx context.Context, lbShortID string, port int, option *bce.SignOption) (*DescribeAppUDPListenerResponse, error) {
	return nil, nil
}

func (f *FakeClient) CreateIPGroup(ctx context.Context, lbShortID string, args *CreateIPGroupArgs, option *bce.SignOption) (*CreateIPGroupResponse, error) {
	return nil, nil
}

func (f *FakeClient) ListIPGroups(ctx context.Context, lbShortID string, args *ListIPGroupArgs, option *bce.SignOption) (*ListIPGroupResponse, error) {
	return nil, nil
}

func (f *FakeClient) ListIPGroupMember(ctx context.Context, lbShortID string, args *ListIPGroupMemberArgs, option *bce.SignOption) (*ListIPGroupMemberResponse, error) {
	return nil, nil
}

func (f *FakeClient) CreateIPGroupMember(ctx context.Context, lbShortID string, args *CreateIPGroupMemberArgs, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) DeleteIPGroupMember(ctx context.Context, lbShortID string, args *DeleteIPGroupMemberArgs, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) CreateIPGroupBackendPolicy(ctx context.Context, lbShortID string, args *CreateIPGroupBackendPolicyArgs, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) DeleteIPGroupBackendPolicy(ctx context.Context, lbShortID string, args *DeleteIPGroupBackendPolicyArgs, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) DeleteIPGroup(ctx context.Context, lbShortID string, args *DeleteIPGroupArgs, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) CreateAppTCPSSLListener(ctx context.Context, lbShortID string, args *CreateAppTCPSSLListenerArgs, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) DescribeAppTCPSSLListener(ctx context.Context, lbShortID string, port int, option *bce.SignOption) (*DescribeAppTCPSSLListenerResponse, error) {
	return nil, nil
}

func (f *FakeClient) GetIPGroupByID(ctx context.Context, lbShortID, ipGroupID string, option *bce.SignOption) (*AppIPGroup, error) {
	return nil, errors.New("not implement")
}

func httpListenerKey(blbID string, port int) string {
	return fmt.Sprintf("%s|%d", blbID, port)
}

func httpsListenerKey(blbID string, port int) string {
	return fmt.Sprintf("%s|%d", blbID, port)
}

func serverGroupKey(blbID string, serverGroupID string) string {
	return fmt.Sprintf("%s|%s", blbID, serverGroupID)
}
