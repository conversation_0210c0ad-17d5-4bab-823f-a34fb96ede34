// Package appblb for bce application loadbalancer
package appblb

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// CreateAppServerGroup create server group in app blb
// BLB.Name can be the same with others.
func (c *Client) CreateAppServerGroup(ctx context.Context, lbShortID string,
	args *CreateAppServerGroupArgs, option *bce.SignOption) (*CreateAppServerGroupResponse, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("appblb/%s/appservergroup", lbShortID)
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.<PERSON><PERSON><PERSON><PERSON>(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var blbsResp *CreateAppServerGroupResponse
	err = json.Unmarshal(bodyContent, &blbsResp)
	if err != nil {
		return nil, err
	}

	return blbsResp, nil
}

// UpdateAppServerGroup to update server group
func (c *Client) UpdateAppServerGroup(ctx context.Context, lbShortID string,
	args *UpdateAppServerGroupArgs, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	if args.ServerGroupID == "" {
		return errors.New("UpdateAppServerGroup need AppServerGroupID")
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/appservergroup", lbShortID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

// DescribeAppServerGroupByID get app servergroup by serverGroupID
func (c *Client) DescribeAppServerGroupByID(ctx context.Context, lbShortID, serverGroupID,
	serverGroupName string, option *bce.SignOption) (*AppServerGroup, error) {
	sgResq, err := c.DescribeAppServerGroup(ctx, lbShortID, &DescribeAppServerGroupArgs{
		Name:         serverGroupName,
		ExactlyMatch: true,
	}, option)
	if err != nil {
		return nil, err
	}

	if sgResq == nil {
		return nil, errors.New("DescribeAppServerGroup failed: sgResq is nil")
	}

	for _, sg := range sgResq.AppServerGroupList {
		if sg.ID == serverGroupID {
			return sg, nil
		}
	}

	return nil, fmt.Errorf("AppBLB %s serverGroupID=%s not found", lbShortID, serverGroupID)
}

// DescribeAppServerGroup list app servergroup by lbShortID, Port
func (c *Client) DescribeAppServerGroup(ctx context.Context, lbShortID string,
	args *DescribeAppServerGroupArgs, option *bce.SignOption) (*DescribeAppServerGroupResponse, error) {
	// Get conditions
	params := map[string]string{}

	if args != nil {
		if args.Name != "" {
			params["name"] = args.Name
		}

		if args.ExactlyMatch {
			params["exactlyMatch"] = "true"
		} else {
			params["exactlyMatch"] = "false"
		}

		if args.Marker != "" {
			params["marker"] = args.Marker
		}

		if args.MaxKeys != 0 {
			params["maxKeys"] = fmt.Sprintf("%d", args.MaxKeys)
		}
	}

	url := fmt.Sprintf("appblb/%s/appservergroup", lbShortID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)

	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var blbsResp *DescribeAppServerGroupResponse
	err = json.Unmarshal(bodyContent, &blbsResp)
	if err != nil {
		return nil, err
	}

	return blbsResp, nil
}

// DeleteAppServerGroup delete app blb server group
func (c *Client) DeleteAppServerGroup(ctx context.Context, lbShortID string,
	args *DeleteAppServerGroupArgs, option *bce.SignOption) error {
	params := map[string]string{
		"delete":      "",
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/appservergroup", lbShortID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}
