// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	appblb "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/appblb"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// CreateAppHTTPListener mocks base method.
func (m *MockInterface) CreateAppHTTPListener(arg0 context.Context, arg1 string, arg2 *appblb.CreateAppHTTPListenerArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAppHTTPListener", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAppHTTPListener indicates an expected call of CreateAppHTTPListener.
func (mr *MockInterfaceMockRecorder) CreateAppHTTPListener(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAppHTTPListener", reflect.TypeOf((*MockInterface)(nil).CreateAppHTTPListener), arg0, arg1, arg2, arg3)
}

// CreateAppHTTPSListener mocks base method.
func (m *MockInterface) CreateAppHTTPSListener(arg0 context.Context, arg1 string, arg2 *appblb.CreateAppHTTPSListenerArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAppHTTPSListener", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAppHTTPSListener indicates an expected call of CreateAppHTTPSListener.
func (mr *MockInterfaceMockRecorder) CreateAppHTTPSListener(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAppHTTPSListener", reflect.TypeOf((*MockInterface)(nil).CreateAppHTTPSListener), arg0, arg1, arg2, arg3)
}

// CreateAppLoadBalancer mocks base method.
func (m *MockInterface) CreateAppLoadBalancer(arg0 context.Context, arg1 *appblb.CreateAppLoadBalancerArgs, arg2 *bce.SignOption) (*appblb.CreateAppLoadBalancerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAppLoadBalancer", arg0, arg1, arg2)
	ret0, _ := ret[0].(*appblb.CreateAppLoadBalancerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAppLoadBalancer indicates an expected call of CreateAppLoadBalancer.
func (mr *MockInterfaceMockRecorder) CreateAppLoadBalancer(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAppLoadBalancer", reflect.TypeOf((*MockInterface)(nil).CreateAppLoadBalancer), arg0, arg1, arg2)
}

// CreateAppPolicys mocks base method.
func (m *MockInterface) CreateAppPolicys(arg0 context.Context, arg1 string, arg2 *appblb.CreateAppPolicyArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAppPolicys", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAppPolicys indicates an expected call of CreateAppPolicys.
func (mr *MockInterfaceMockRecorder) CreateAppPolicys(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAppPolicys", reflect.TypeOf((*MockInterface)(nil).CreateAppPolicys), arg0, arg1, arg2, arg3)
}

// CreateAppServerGroup mocks base method.
func (m *MockInterface) CreateAppServerGroup(arg0 context.Context, arg1 string, arg2 *appblb.CreateAppServerGroupArgs, arg3 *bce.SignOption) (*appblb.CreateAppServerGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAppServerGroup", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*appblb.CreateAppServerGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAppServerGroup indicates an expected call of CreateAppServerGroup.
func (mr *MockInterfaceMockRecorder) CreateAppServerGroup(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAppServerGroup", reflect.TypeOf((*MockInterface)(nil).CreateAppServerGroup), arg0, arg1, arg2, arg3)
}

// CreateAppServerGroupPort mocks base method.
func (m *MockInterface) CreateAppServerGroupPort(arg0 context.Context, arg1 string, arg2 *appblb.CreateAppServerGroupPortArgs, arg3 *bce.SignOption) (*appblb.CreateAppServerGroupPortResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAppServerGroupPort", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*appblb.CreateAppServerGroupPortResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAppServerGroupPort indicates an expected call of CreateAppServerGroupPort.
func (mr *MockInterfaceMockRecorder) CreateAppServerGroupPort(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAppServerGroupPort", reflect.TypeOf((*MockInterface)(nil).CreateAppServerGroupPort), arg0, arg1, arg2, arg3)
}

// CreateAppServerGroupRS mocks base method.
func (m *MockInterface) CreateAppServerGroupRS(arg0 context.Context, arg1 string, arg2 *appblb.CreateAppServerGroupRSArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAppServerGroupRS", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAppServerGroupRS indicates an expected call of CreateAppServerGroupRS.
func (mr *MockInterfaceMockRecorder) CreateAppServerGroupRS(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAppServerGroupRS", reflect.TypeOf((*MockInterface)(nil).CreateAppServerGroupRS), arg0, arg1, arg2, arg3)
}

// CreateAppTCPListener mocks base method.
func (m *MockInterface) CreateAppTCPListener(arg0 context.Context, arg1 string, arg2 *appblb.CreateAppTCPListenerArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAppTCPListener", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAppTCPListener indicates an expected call of CreateAppTCPListener.
func (mr *MockInterfaceMockRecorder) CreateAppTCPListener(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAppTCPListener", reflect.TypeOf((*MockInterface)(nil).CreateAppTCPListener), arg0, arg1, arg2, arg3)
}

// CreateAppTCPSSLListener mocks base method.
func (m *MockInterface) CreateAppTCPSSLListener(arg0 context.Context, arg1 string, arg2 *appblb.CreateAppTCPSSLListenerArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAppTCPSSLListener", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAppTCPSSLListener indicates an expected call of CreateAppTCPSSLListener.
func (mr *MockInterfaceMockRecorder) CreateAppTCPSSLListener(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAppTCPSSLListener", reflect.TypeOf((*MockInterface)(nil).CreateAppTCPSSLListener), arg0, arg1, arg2, arg3)
}

// CreateAppUDPListener mocks base method.
func (m *MockInterface) CreateAppUDPListener(arg0 context.Context, arg1 string, arg2 *appblb.CreateAppUDPListenerArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAppUDPListener", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAppUDPListener indicates an expected call of CreateAppUDPListener.
func (mr *MockInterfaceMockRecorder) CreateAppUDPListener(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAppUDPListener", reflect.TypeOf((*MockInterface)(nil).CreateAppUDPListener), arg0, arg1, arg2, arg3)
}

// CreateIPGroup mocks base method.
func (m *MockInterface) CreateIPGroup(arg0 context.Context, arg1 string, arg2 *appblb.CreateIPGroupArgs, arg3 *bce.SignOption) (*appblb.CreateIPGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateIPGroup", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*appblb.CreateIPGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateIPGroup indicates an expected call of CreateIPGroup.
func (mr *MockInterfaceMockRecorder) CreateIPGroup(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIPGroup", reflect.TypeOf((*MockInterface)(nil).CreateIPGroup), arg0, arg1, arg2, arg3)
}

// CreateIPGroupBackendPolicy mocks base method.
func (m *MockInterface) CreateIPGroupBackendPolicy(arg0 context.Context, arg1 string, arg2 *appblb.CreateIPGroupBackendPolicyArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateIPGroupBackendPolicy", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateIPGroupBackendPolicy indicates an expected call of CreateIPGroupBackendPolicy.
func (mr *MockInterfaceMockRecorder) CreateIPGroupBackendPolicy(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIPGroupBackendPolicy", reflect.TypeOf((*MockInterface)(nil).CreateIPGroupBackendPolicy), arg0, arg1, arg2, arg3)
}

// CreateIPGroupMember mocks base method.
func (m *MockInterface) CreateIPGroupMember(arg0 context.Context, arg1 string, arg2 *appblb.CreateIPGroupMemberArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateIPGroupMember", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateIPGroupMember indicates an expected call of CreateIPGroupMember.
func (mr *MockInterfaceMockRecorder) CreateIPGroupMember(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIPGroupMember", reflect.TypeOf((*MockInterface)(nil).CreateIPGroupMember), arg0, arg1, arg2, arg3)
}

// DeleteAppListeners mocks base method.
func (m *MockInterface) DeleteAppListeners(arg0 context.Context, arg1 string, arg2 *appblb.DeleteAppListenersArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAppListeners", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAppListeners indicates an expected call of DeleteAppListeners.
func (mr *MockInterfaceMockRecorder) DeleteAppListeners(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAppListeners", reflect.TypeOf((*MockInterface)(nil).DeleteAppListeners), arg0, arg1, arg2, arg3)
}

// DeleteAppLoadBalancer mocks base method.
func (m *MockInterface) DeleteAppLoadBalancer(arg0 context.Context, arg1 string, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAppLoadBalancer", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAppLoadBalancer indicates an expected call of DeleteAppLoadBalancer.
func (mr *MockInterfaceMockRecorder) DeleteAppLoadBalancer(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAppLoadBalancer", reflect.TypeOf((*MockInterface)(nil).DeleteAppLoadBalancer), arg0, arg1, arg2)
}

// DeleteAppPolicys mocks base method.
func (m *MockInterface) DeleteAppPolicys(arg0 context.Context, arg1 string, arg2 *appblb.DeleteAppPolicysArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAppPolicys", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAppPolicys indicates an expected call of DeleteAppPolicys.
func (mr *MockInterfaceMockRecorder) DeleteAppPolicys(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAppPolicys", reflect.TypeOf((*MockInterface)(nil).DeleteAppPolicys), arg0, arg1, arg2, arg3)
}

// DeleteAppServerGroup mocks base method.
func (m *MockInterface) DeleteAppServerGroup(arg0 context.Context, arg1 string, arg2 *appblb.DeleteAppServerGroupArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAppServerGroup", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAppServerGroup indicates an expected call of DeleteAppServerGroup.
func (mr *MockInterfaceMockRecorder) DeleteAppServerGroup(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAppServerGroup", reflect.TypeOf((*MockInterface)(nil).DeleteAppServerGroup), arg0, arg1, arg2, arg3)
}

// DeleteAppServerGroupPort mocks base method.
func (m *MockInterface) DeleteAppServerGroupPort(arg0 context.Context, arg1 string, arg2 *appblb.DeleteAppServerGroupPortArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAppServerGroupPort", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAppServerGroupPort indicates an expected call of DeleteAppServerGroupPort.
func (mr *MockInterfaceMockRecorder) DeleteAppServerGroupPort(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAppServerGroupPort", reflect.TypeOf((*MockInterface)(nil).DeleteAppServerGroupPort), arg0, arg1, arg2, arg3)
}

// DeleteAppServerGroupRS mocks base method.
func (m *MockInterface) DeleteAppServerGroupRS(arg0 context.Context, arg1 string, arg2 *appblb.DeleteAppServerGroupRSArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAppServerGroupRS", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAppServerGroupRS indicates an expected call of DeleteAppServerGroupRS.
func (mr *MockInterfaceMockRecorder) DeleteAppServerGroupRS(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAppServerGroupRS", reflect.TypeOf((*MockInterface)(nil).DeleteAppServerGroupRS), arg0, arg1, arg2, arg3)
}

// DeleteIPGroup mocks base method.
func (m *MockInterface) DeleteIPGroup(arg0 context.Context, arg1 string, arg2 *appblb.DeleteIPGroupArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteIPGroup", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteIPGroup indicates an expected call of DeleteIPGroup.
func (mr *MockInterfaceMockRecorder) DeleteIPGroup(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteIPGroup", reflect.TypeOf((*MockInterface)(nil).DeleteIPGroup), arg0, arg1, arg2, arg3)
}

// DeleteIPGroupBackendPolicy mocks base method.
func (m *MockInterface) DeleteIPGroupBackendPolicy(arg0 context.Context, arg1 string, arg2 *appblb.DeleteIPGroupBackendPolicyArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteIPGroupBackendPolicy", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteIPGroupBackendPolicy indicates an expected call of DeleteIPGroupBackendPolicy.
func (mr *MockInterfaceMockRecorder) DeleteIPGroupBackendPolicy(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteIPGroupBackendPolicy", reflect.TypeOf((*MockInterface)(nil).DeleteIPGroupBackendPolicy), arg0, arg1, arg2, arg3)
}

// DeleteIPGroupMember mocks base method.
func (m *MockInterface) DeleteIPGroupMember(arg0 context.Context, arg1 string, arg2 *appblb.DeleteIPGroupMemberArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteIPGroupMember", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteIPGroupMember indicates an expected call of DeleteIPGroupMember.
func (mr *MockInterfaceMockRecorder) DeleteIPGroupMember(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteIPGroupMember", reflect.TypeOf((*MockInterface)(nil).DeleteIPGroupMember), arg0, arg1, arg2, arg3)
}

// DescribeAppHTTPListener mocks base method.
func (m *MockInterface) DescribeAppHTTPListener(arg0 context.Context, arg1 string, arg2 int, arg3 *bce.SignOption) (*appblb.DescribeAppHTTPListenerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAppHTTPListener", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*appblb.DescribeAppHTTPListenerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAppHTTPListener indicates an expected call of DescribeAppHTTPListener.
func (mr *MockInterfaceMockRecorder) DescribeAppHTTPListener(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAppHTTPListener", reflect.TypeOf((*MockInterface)(nil).DescribeAppHTTPListener), arg0, arg1, arg2, arg3)
}

// DescribeAppHTTPSListener mocks base method.
func (m *MockInterface) DescribeAppHTTPSListener(arg0 context.Context, arg1 string, arg2 int, arg3 *bce.SignOption) (*appblb.DescribeAppHTTPSListenerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAppHTTPSListener", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*appblb.DescribeAppHTTPSListenerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAppHTTPSListener indicates an expected call of DescribeAppHTTPSListener.
func (mr *MockInterfaceMockRecorder) DescribeAppHTTPSListener(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAppHTTPSListener", reflect.TypeOf((*MockInterface)(nil).DescribeAppHTTPSListener), arg0, arg1, arg2, arg3)
}

// DescribeAppLoadBalancerByID mocks base method.
func (m *MockInterface) DescribeAppLoadBalancerByID(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*appblb.AppLoadBalancer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAppLoadBalancerByID", arg0, arg1, arg2)
	ret0, _ := ret[0].(*appblb.AppLoadBalancer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAppLoadBalancerByID indicates an expected call of DescribeAppLoadBalancerByID.
func (mr *MockInterfaceMockRecorder) DescribeAppLoadBalancerByID(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAppLoadBalancerByID", reflect.TypeOf((*MockInterface)(nil).DescribeAppLoadBalancerByID), arg0, arg1, arg2)
}

// DescribeAppLoadBalancers mocks base method.
func (m *MockInterface) DescribeAppLoadBalancers(arg0 context.Context, arg1 *appblb.DescribeLoadBalancerArgs, arg2 *bce.SignOption) (*appblb.DescribeAppLoadBalancerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAppLoadBalancers", arg0, arg1, arg2)
	ret0, _ := ret[0].(*appblb.DescribeAppLoadBalancerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAppLoadBalancers indicates an expected call of DescribeAppLoadBalancers.
func (mr *MockInterfaceMockRecorder) DescribeAppLoadBalancers(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAppLoadBalancers", reflect.TypeOf((*MockInterface)(nil).DescribeAppLoadBalancers), arg0, arg1, arg2)
}

// DescribeAppLoadBalancersByName mocks base method.
func (m *MockInterface) DescribeAppLoadBalancersByName(arg0 context.Context, arg1 string, arg2 *bce.SignOption) ([]*appblb.AppLoadBalancer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAppLoadBalancersByName", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*appblb.AppLoadBalancer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAppLoadBalancersByName indicates an expected call of DescribeAppLoadBalancersByName.
func (mr *MockInterfaceMockRecorder) DescribeAppLoadBalancersByName(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAppLoadBalancersByName", reflect.TypeOf((*MockInterface)(nil).DescribeAppLoadBalancersByName), arg0, arg1, arg2)
}

// DescribeAppPolicys mocks base method.
func (m *MockInterface) DescribeAppPolicys(arg0 context.Context, arg1 string, arg2 *appblb.DescribeAppPolicysArgs, arg3 *bce.SignOption) (*appblb.DescribeAppPolicysResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAppPolicys", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*appblb.DescribeAppPolicysResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAppPolicys indicates an expected call of DescribeAppPolicys.
func (mr *MockInterfaceMockRecorder) DescribeAppPolicys(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAppPolicys", reflect.TypeOf((*MockInterface)(nil).DescribeAppPolicys), arg0, arg1, arg2, arg3)
}

// DescribeAppServerGroup mocks base method.
func (m *MockInterface) DescribeAppServerGroup(arg0 context.Context, arg1 string, arg2 *appblb.DescribeAppServerGroupArgs, arg3 *bce.SignOption) (*appblb.DescribeAppServerGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAppServerGroup", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*appblb.DescribeAppServerGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAppServerGroup indicates an expected call of DescribeAppServerGroup.
func (mr *MockInterfaceMockRecorder) DescribeAppServerGroup(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAppServerGroup", reflect.TypeOf((*MockInterface)(nil).DescribeAppServerGroup), arg0, arg1, arg2, arg3)
}

// DescribeAppServerGroupByID mocks base method.
func (m *MockInterface) DescribeAppServerGroupByID(arg0 context.Context, arg1, arg2, arg3 string, arg4 *bce.SignOption) (*appblb.AppServerGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAppServerGroupByID", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*appblb.AppServerGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAppServerGroupByID indicates an expected call of DescribeAppServerGroupByID.
func (mr *MockInterfaceMockRecorder) DescribeAppServerGroupByID(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAppServerGroupByID", reflect.TypeOf((*MockInterface)(nil).DescribeAppServerGroupByID), arg0, arg1, arg2, arg3, arg4)
}

// DescribeAppServerGroupRS mocks base method.
func (m *MockInterface) DescribeAppServerGroupRS(arg0 context.Context, arg1, arg2 string, arg3 *bce.SignOption) (*appblb.DescribeAppServerGroupRSResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAppServerGroupRS", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*appblb.DescribeAppServerGroupRSResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAppServerGroupRS indicates an expected call of DescribeAppServerGroupRS.
func (mr *MockInterfaceMockRecorder) DescribeAppServerGroupRS(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAppServerGroupRS", reflect.TypeOf((*MockInterface)(nil).DescribeAppServerGroupRS), arg0, arg1, arg2, arg3)
}

// DescribeAppServerGroupRSMount mocks base method.
func (m *MockInterface) DescribeAppServerGroupRSMount(arg0 context.Context, arg1, arg2 string, arg3 *bce.SignOption) ([]*appblb.AppBackendServer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAppServerGroupRSMount", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*appblb.AppBackendServer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAppServerGroupRSMount indicates an expected call of DescribeAppServerGroupRSMount.
func (mr *MockInterfaceMockRecorder) DescribeAppServerGroupRSMount(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAppServerGroupRSMount", reflect.TypeOf((*MockInterface)(nil).DescribeAppServerGroupRSMount), arg0, arg1, arg2, arg3)
}

// DescribeAppServerGroupRSUnMount mocks base method.
func (m *MockInterface) DescribeAppServerGroupRSUnMount(arg0 context.Context, arg1, arg2 string, arg3 *bce.SignOption) ([]*appblb.AppBackendServer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAppServerGroupRSUnMount", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*appblb.AppBackendServer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAppServerGroupRSUnMount indicates an expected call of DescribeAppServerGroupRSUnMount.
func (mr *MockInterfaceMockRecorder) DescribeAppServerGroupRSUnMount(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAppServerGroupRSUnMount", reflect.TypeOf((*MockInterface)(nil).DescribeAppServerGroupRSUnMount), arg0, arg1, arg2, arg3)
}

// DescribeAppTCPListener mocks base method.
func (m *MockInterface) DescribeAppTCPListener(arg0 context.Context, arg1 string, arg2 int, arg3 *bce.SignOption) (*appblb.DescribeAppTCPListenerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAppTCPListener", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*appblb.DescribeAppTCPListenerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAppTCPListener indicates an expected call of DescribeAppTCPListener.
func (mr *MockInterfaceMockRecorder) DescribeAppTCPListener(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAppTCPListener", reflect.TypeOf((*MockInterface)(nil).DescribeAppTCPListener), arg0, arg1, arg2, arg3)
}

// DescribeAppTCPSSLListener mocks base method.
func (m *MockInterface) DescribeAppTCPSSLListener(arg0 context.Context, arg1 string, arg2 int, arg3 *bce.SignOption) (*appblb.DescribeAppTCPSSLListenerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAppTCPSSLListener", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*appblb.DescribeAppTCPSSLListenerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAppTCPSSLListener indicates an expected call of DescribeAppTCPSSLListener.
func (mr *MockInterfaceMockRecorder) DescribeAppTCPSSLListener(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAppTCPSSLListener", reflect.TypeOf((*MockInterface)(nil).DescribeAppTCPSSLListener), arg0, arg1, arg2, arg3)
}

// DescribeAppUDPListener mocks base method.
func (m *MockInterface) DescribeAppUDPListener(arg0 context.Context, arg1 string, arg2 int, arg3 *bce.SignOption) (*appblb.DescribeAppUDPListenerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAppUDPListener", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*appblb.DescribeAppUDPListenerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAppUDPListener indicates an expected call of DescribeAppUDPListener.
func (mr *MockInterfaceMockRecorder) DescribeAppUDPListener(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAppUDPListener", reflect.TypeOf((*MockInterface)(nil).DescribeAppUDPListener), arg0, arg1, arg2, arg3)
}

// GetIPGroupByID mocks base method.
func (m *MockInterface) GetIPGroupByID(arg0 context.Context, arg1, arg2 string, arg3 *bce.SignOption) (*appblb.AppIPGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIPGroupByID", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*appblb.AppIPGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIPGroupByID indicates an expected call of GetIPGroupByID.
func (mr *MockInterfaceMockRecorder) GetIPGroupByID(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIPGroupByID", reflect.TypeOf((*MockInterface)(nil).GetIPGroupByID), arg0, arg1, arg2, arg3)
}

// ListIPGroupMember mocks base method.
func (m *MockInterface) ListIPGroupMember(arg0 context.Context, arg1 string, arg2 *appblb.ListIPGroupMemberArgs, arg3 *bce.SignOption) (*appblb.ListIPGroupMemberResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListIPGroupMember", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*appblb.ListIPGroupMemberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListIPGroupMember indicates an expected call of ListIPGroupMember.
func (mr *MockInterfaceMockRecorder) ListIPGroupMember(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListIPGroupMember", reflect.TypeOf((*MockInterface)(nil).ListIPGroupMember), arg0, arg1, arg2, arg3)
}

// ListIPGroups mocks base method.
func (m *MockInterface) ListIPGroups(arg0 context.Context, arg1 string, arg2 *appblb.ListIPGroupArgs, arg3 *bce.SignOption) (*appblb.ListIPGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListIPGroups", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*appblb.ListIPGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListIPGroups indicates an expected call of ListIPGroups.
func (mr *MockInterfaceMockRecorder) ListIPGroups(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListIPGroups", reflect.TypeOf((*MockInterface)(nil).ListIPGroups), arg0, arg1, arg2, arg3)
}

// SetDebug mocks base method.
func (m *MockInterface) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug.
func (mr *MockInterfaceMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), arg0)
}

// UpdateAppHTTPListener mocks base method.
func (m *MockInterface) UpdateAppHTTPListener(arg0 context.Context, arg1 string, arg2 int, arg3 *appblb.UpdateAppHTTPListenerArgs, arg4 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAppHTTPListener", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAppHTTPListener indicates an expected call of UpdateAppHTTPListener.
func (mr *MockInterfaceMockRecorder) UpdateAppHTTPListener(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAppHTTPListener", reflect.TypeOf((*MockInterface)(nil).UpdateAppHTTPListener), arg0, arg1, arg2, arg3, arg4)
}

// UpdateAppHTTPSListener mocks base method.
func (m *MockInterface) UpdateAppHTTPSListener(arg0 context.Context, arg1 string, arg2 int, arg3 *appblb.UpdateAppHTTPSListenerArgs, arg4 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAppHTTPSListener", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAppHTTPSListener indicates an expected call of UpdateAppHTTPSListener.
func (mr *MockInterfaceMockRecorder) UpdateAppHTTPSListener(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAppHTTPSListener", reflect.TypeOf((*MockInterface)(nil).UpdateAppHTTPSListener), arg0, arg1, arg2, arg3, arg4)
}

// UpdateAppLoadBalancer mocks base method.
func (m *MockInterface) UpdateAppLoadBalancer(arg0 context.Context, arg1 string, arg2 *appblb.UpdateAppLoadBalancerArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAppLoadBalancer", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAppLoadBalancer indicates an expected call of UpdateAppLoadBalancer.
func (mr *MockInterfaceMockRecorder) UpdateAppLoadBalancer(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAppLoadBalancer", reflect.TypeOf((*MockInterface)(nil).UpdateAppLoadBalancer), arg0, arg1, arg2, arg3)
}

// UpdateAppServerGroup mocks base method.
func (m *MockInterface) UpdateAppServerGroup(arg0 context.Context, arg1 string, arg2 *appblb.UpdateAppServerGroupArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAppServerGroup", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAppServerGroup indicates an expected call of UpdateAppServerGroup.
func (mr *MockInterfaceMockRecorder) UpdateAppServerGroup(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAppServerGroup", reflect.TypeOf((*MockInterface)(nil).UpdateAppServerGroup), arg0, arg1, arg2, arg3)
}

// UpdateAppServerGroupPort mocks base method.
func (m *MockInterface) UpdateAppServerGroupPort(arg0 context.Context, arg1 string, arg2 *appblb.UpdateAppServerGroupPortArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAppServerGroupPort", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAppServerGroupPort indicates an expected call of UpdateAppServerGroupPort.
func (mr *MockInterfaceMockRecorder) UpdateAppServerGroupPort(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAppServerGroupPort", reflect.TypeOf((*MockInterface)(nil).UpdateAppServerGroupPort), arg0, arg1, arg2, arg3)
}

// UpdateAppServerGroupRSWeight mocks base method.
func (m *MockInterface) UpdateAppServerGroupRSWeight(arg0 context.Context, arg1 string, arg2 *appblb.UpdateAppServerGroupRSWeightArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAppServerGroupRSWeight", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAppServerGroupRSWeight indicates an expected call of UpdateAppServerGroupRSWeight.
func (mr *MockInterfaceMockRecorder) UpdateAppServerGroupRSWeight(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAppServerGroupRSWeight", reflect.TypeOf((*MockInterface)(nil).UpdateAppServerGroupRSWeight), arg0, arg1, arg2, arg3)
}
