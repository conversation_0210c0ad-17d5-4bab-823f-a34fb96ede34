// Package appblb for bce application loadbalancer
package appblb

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// CreateAppPolicys create AppBLB policys
func (c *Client) CreateAppPolicys(ctx context.Context, lbShortID string, args *CreateAppPolicyArgs, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/policys", lbShortID)
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)

	return err
}

// DescribeAppPolicys describe AppPolicy
func (c *Client) DescribeAppPolicys(ctx context.Context, lbShortID string, args *DescribeAppPolicysArgs, option *bce.SignOption) (*DescribeAppPolicysResponse, error) {
	if lbShortID == "" {
		return nil, errors.New("missing args blb_id or server_group_id")
	}

	// Get conditions
	params := map[string]string{}

	if args != nil {
		if args.Port != 0 {
			params["port"] = fmt.Sprintf("%d", args.Port)
		}

		if args.Marker != "" {
			params["marker"] = args.Marker
		}

		if args.MaxKeys != 0 {
			params["maxKeys"] = fmt.Sprintf("%d", args.MaxKeys)
		}
	}

	url := fmt.Sprintf("appblb/%s/policys", lbShortID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var blbsResp *DescribeAppPolicysResponse
	err = json.Unmarshal(bodyContent, &blbsResp)
	if err != nil {
		return nil, err
	}

	return blbsResp, nil
}

// DeleteAppPolicys delete appblb policies
func (c *Client) DeleteAppPolicys(ctx context.Context, lbShortID string, args *DeleteAppPolicysArgs, option *bce.SignOption) error {
	params := map[string]string{
		"batchdelete": "",
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/policys", lbShortID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	return err
}
