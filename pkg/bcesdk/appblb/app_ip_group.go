package appblb

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

type CreateIPGroupArgs struct {
	Name       string              `json:"name"`
	Desc       string              `json:"desc"`
	MemberList []*AppIPGroupMember `json:"memberList"`
}

type CreateIPGroupResponse struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Desc string `json:"desc"`
}

func (c *Client) CreateIPGroup(ctx context.Context, lbShortID string, args *CreateIPGroupArgs, option *bce.SignOption) (*CreateIPGroupResponse, error) {
	if lbShortID == "" || args == nil {
		return nil, errors.New("missing lbShortID or args")
	}

	params := map[string]string{
		"clientToken": c.<PERSON>(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, fmt.Errorf("marshal body: %w", err)
	}

	url := fmt.Sprintf("appblb/%s/ipgroup", lbShortID)
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, fmt.Errorf("bce.NewRequest: %w", err)
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, fmt.Errorf("send request: %w", err)
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, fmt.Errorf("get body content: %w", err)
	}

	createResp := new(CreateIPGroupResponse)
	err = json.Unmarshal(bodyContent, createResp)
	if err != nil {
		return nil, fmt.Errorf("unmarshal %s: %w", string(bodyContent), err)
	}

	return createResp, nil
}

type UpdateIPGroupArgs struct {
	IPGroupID string `json:"ipGroupId"`
	Name      string `json:"name"`
	Desc      string `json:"desc"`
}

func (c *Client) UpdateIPGroup(ctx context.Context, lbShortID, groupID string, args *UpdateIPGroupArgs, option *bce.SignOption) error {
	if lbShortID == "" || groupID == "" || args == nil {
		return errors.New("missing lbShortID or groupID or args")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return fmt.Errorf("marshal body: %w", err)
	}

	url := fmt.Sprintf("appblb/%s/ipgroup", lbShortID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return fmt.Errorf("bce.NewRequest: %w", err)
	}

	_, err = c.SendRequest(ctx, req, option)
	return err
}

type ListIPGroupArgs struct {
	Name         string `json:"name"`
	Marker       string `json:"marker"`
	MaxKeys      int    `json:"maxKeys"`
	ExactlyMatch bool   `json:"exactlyMatch"`
}

type ListIPGroupResponse struct {
	Marker         string        `json:"marker"`
	IsTruncated    bool          `json:"isTruncated"`
	NextMarker     string        `json:"nextMarker"`
	MaxKeys        int           `json:"maxKeys"`
	AppIPGroupList []*AppIPGroup `json:"appIpGroupList"`
}

func (c *Client) ListIPGroups(ctx context.Context, lbShortID string, args *ListIPGroupArgs, option *bce.SignOption) (*ListIPGroupResponse, error) {
	if lbShortID == "" {
		return nil, errors.New("missing lbShortID")
	}

	params := make(map[string]string)
	if args != nil {
		if args.Name != "" {
			params["name"] = args.Name
			if args.ExactlyMatch {
				params["exactlyMatch"] = "true"
			} else {
				params["exactlyMatch"] = "false"
			}
		}
		if args.Marker != "" {
			params["marker"] = args.Marker
		}
		if args.MaxKeys > 0 {
			params["maxKeys"] = strconv.Itoa(args.MaxKeys)
		}
	}

	url := fmt.Sprintf("appblb/%s/ipgroup", lbShortID)

	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, fmt.Errorf("bce.NewRequest: %w", err)
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, fmt.Errorf("send request: %w", err)
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, fmt.Errorf("get body content: %w", err)
	}

	listResp := new(ListIPGroupResponse)
	err = json.Unmarshal(bodyContent, listResp)
	if err != nil {
		return nil, fmt.Errorf("unmarshal %s: %w", string(bodyContent), err)
	}

	return listResp, nil
}

type DeleteIPGroupArgs struct {
	IPGroupID string `json:"ipGroupId"`
}

func (c *Client) DeleteIPGroup(ctx context.Context, lbShortID string, args *DeleteIPGroupArgs, option *bce.SignOption) error {
	if lbShortID == "" {
		return errors.New("missing lbShortID")
	}

	if args == nil || args.IPGroupID == "" {
		return errors.New("DeleteIPGroupArgs is nil or missing IPGroupID")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return fmt.Errorf("marshal body: %w", err)
	}

	url := fmt.Sprintf("appblb/%s/ipgroup", lbShortID)
	req, err := bce.NewRequest("DELETE", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return fmt.Errorf("bce.NewRequest: %w", err)
	}

	_, err = c.SendRequest(ctx, req, option)
	return err
}

func (c *Client) GetIPGroupByID(ctx context.Context, lbShortID, ipGroupID string, option *bce.SignOption) (*AppIPGroup, error) {
	listResp, err := c.ListIPGroups(ctx, lbShortID, &ListIPGroupArgs{}, option)
	if err != nil {
		return nil, err
	}

	for _, ipGroup := range listResp.AppIPGroupList {
		if ipGroup.ID == ipGroupID {
			return ipGroup, nil
		}
	}
	return nil, errors.New("not found")
}
