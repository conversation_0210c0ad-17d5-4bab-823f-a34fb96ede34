// Package appblb for bce application loadbalancer
package appblb

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// CreateAppTCPListener create APP BLB TCP listener
func (c *Client) CreateAppTCPListener(ctx context.Context, lbShortID string,
	args *CreateAppTCPListenerArgs, option *bce.SignOption) error {
	if args.ListenerPort == 0 || args.Scheduler == "" {
		return errors.New("CreateAppTCPListener failed: ListenerPort, Scheduler is nil")
	}

	params := map[string]string{
		"clientToken": c.Gene<PERSON>lient<PERSON>oken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/TCPlistener", lbShortID)
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

// CreateAppUDPListener create APP BLB UDP Listener
func (c *Client) CreateAppUDPListener(ctx context.Context, lbShortID string, args *CreateAppUDPListenerArgs,
	option *bce.SignOption) error {
	if args.ListenerPort == 0 || args.Scheduler == "" {
		return errors.New("CreateAppTCPListener failed: ListenerPort, Scheduler is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/UDPlistener", lbShortID)
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

// CreateAppTCPSSLListener 创建 TCP SSL 监听器
func (c *Client) CreateAppTCPSSLListener(ctx context.Context, lbShortID string, args *CreateAppTCPSSLListenerArgs,
	option *bce.SignOption) error {
	if args.ListenerPort == 0 {
		return errors.New("CreateAppTCPSSLListener failed: ListenerPort is nil")
	}
	if args.Scheduler == "" {
		return errors.New("CreateAppTCPSSLListener failed: Scheduler is nil")
	}
	if args.CertIDs == nil {
		return errors.New("CreateAppTCPSSLListener failed: CertIDs is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/SSLlistener", lbShortID)
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

// CreateAppHTTPListener create APP BLB HTTP listener
func (c *Client) CreateAppHTTPListener(ctx context.Context, lbShortID string,
	args *CreateAppHTTPListenerArgs, option *bce.SignOption) error {
	if args.ListenerPort == 0 || args.Scheduler == "" {
		return errors.New("CreateAppHTTPListener failed: ListenerPort, Scheduler is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/HTTPlistener", lbShortID)
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

// CreateAppHTTPSListener create APP BLB HTTP listener
func (c *Client) CreateAppHTTPSListener(ctx context.Context, lbShortID string,
	args *CreateAppHTTPSListenerArgs, option *bce.SignOption) error {
	if args.ListenerPort == 0 || args.Scheduler == "" {
		return errors.New("CreateAppHTTPSListener failed: ListenerPort, Scheduler is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/HTTPSlistener", lbShortID)
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

// UpdateAppHTTPListener update APP BLB HTTP listener
// If RedirectPort params not set, HTTP Listner.RedirectPort will not change
func (c *Client) UpdateAppHTTPListener(ctx context.Context, shortID string, port int,
	updateArgs *UpdateAppHTTPListenerArgs, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken":  c.GenerateClientToken(),
		"listenerPort": fmt.Sprintf("%d", port),
	}

	postContent, err := json.Marshal(updateArgs)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/HTTPlistener", shortID)

	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

// UpdateAppHTTPSListener update APP BLB HTTP listener
func (c *Client) UpdateAppHTTPSListener(ctx context.Context, shortID string, port int,
	updateArgs *UpdateAppHTTPSListenerArgs, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken":  c.GenerateClientToken(),
		"listenerPort": fmt.Sprintf("%d", port),
	}

	postContent, err := json.Marshal(updateArgs)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/HTTPSlistener", shortID)

	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

// DescribeAppTCPListener describe APP BLB TCP listener
// If Listener not exist, err == nil but resq.ListenerList = []
func (c *Client) DescribeAppTCPListener(ctx context.Context, lbShortID string, port int,
	option *bce.SignOption) (*DescribeAppTCPListenerResponse, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	if port > 0 {
		params["listenerPort"] = fmt.Sprintf("%d", port)
	}

	url := fmt.Sprintf("appblb/%s/TCPlistener", lbShortID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var listenerResp *DescribeAppTCPListenerResponse
	err = json.Unmarshal(bodyContent, &listenerResp)
	if err != nil {
		return nil, err
	}

	return listenerResp, nil
}

// DescribeAppUDPListener describe APP BLB UDP Listener
func (c *Client) DescribeAppUDPListener(ctx context.Context, lbShortID string, port int,
	option *bce.SignOption) (*DescribeAppUDPListenerResponse, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	if port > 0 {
		params["listenerPort"] = fmt.Sprintf("%d", port)
	}

	url := fmt.Sprintf("appblb/%s/UDPlistener", lbShortID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var listenerResp *DescribeAppUDPListenerResponse
	err = json.Unmarshal(bodyContent, &listenerResp)
	if err != nil {
		return nil, err
	}

	return listenerResp, nil
}

func (c *Client) DescribeAppTCPSSLListener(ctx context.Context, lbShortID string, port int,
	option *bce.SignOption) (*DescribeAppTCPSSLListenerResponse, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	if port > 0 {
		params["listenerPort"] = fmt.Sprintf("%d", port)
	}

	url := fmt.Sprintf("appblb/%s/SSLlistener", lbShortID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var listenerResp *DescribeAppTCPSSLListenerResponse
	err = json.Unmarshal(bodyContent, &listenerResp)
	if err != nil {
		return nil, err
	}

	return listenerResp, nil
}

// DescribeAppHTTPListener describe APP BLB HTTP listener
// If Listener not exist, err == nil but resq.ListenerList = []
func (c *Client) DescribeAppHTTPListener(ctx context.Context, lbShortID string, port int,
	option *bce.SignOption) (*DescribeAppHTTPListenerResponse, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	if port > 0 {
		params["listenerPort"] = fmt.Sprintf("%d", port)
	}

	url := fmt.Sprintf("appblb/%s/HTTPlistener", lbShortID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var listenerResp *DescribeAppHTTPListenerResponse
	err = json.Unmarshal(bodyContent, &listenerResp)
	if err != nil {
		return nil, err
	}

	return listenerResp, nil
}

// DescribeAppHTTPSListener describe APP BLB HTTPS listener
// If Listener not exist, err == nil but resq.ListenerList = []
func (c *Client) DescribeAppHTTPSListener(ctx context.Context, shortID string, port int,
	option *bce.SignOption) (*DescribeAppHTTPSListenerResponse, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	if port > 0 {
		params["listenerPort"] = fmt.Sprintf("%d", port)
	}

	url := fmt.Sprintf("appblb/%s/HTTPSlistener", shortID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var listenerResp *DescribeAppHTTPSListenerResponse
	err = json.Unmarshal(bodyContent, &listenerResp)
	if err != nil {
		return nil, err
	}

	return listenerResp, nil
}

// DeleteAppListeners delete app blb listener
func (c *Client) DeleteAppListeners(ctx context.Context, lbShortID string,
	args *DeleteAppListenersArgs, option *bce.SignOption) error {
	params := map[string]string{
		"batchdelete": "",
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/listener", lbShortID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}
