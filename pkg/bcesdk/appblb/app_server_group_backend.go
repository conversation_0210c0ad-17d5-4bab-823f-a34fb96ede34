// Package appblb for bce application loadbalancer
package appblb

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// CreateAppServerGroupRSArgs create AppServerGroupRS Args
type CreateAppServerGroupRSArgs struct {
	ServerGroupID string `json:"sgId"`

	// When used in CreateAppServerGroupRSArgs, only instanceID & Weight make sense.
	BackendServerList []*AppBackendServer `json:"backendServerList"`
}

// UpdateAppServerGroupRSWeightArgs update AppServerGroupRS Args
type UpdateAppServerGroupRSWeightArgs struct {
	ServerGroupID string `json:"sgId"`

	// When used in CreateAppServerGroupRSArgs, only instanceID & Weight make sense.
	BackendServerList []*AppBackendServer `json:"backendServerList"`
}

// DescribeAppServerGroupRSResponse describe appServerGroupRS response
type DescribeAppServerGroupRSResponse struct {
	BackendServerList []*AppBackendServer `json:"backendServerList"`
	Marker            string              `json:"marker,omitempty"`
	IsTruncated       bool                `json:"isTruncated,omitempty"`
	NextMarker        string              `json:"nextMarker,omitempty"`
	MaxKeys           int                 `json:"maxKeys,omitempty"`
}

// DeleteAppServerGroupRSArgs delete appServerGroupRS args
type DeleteAppServerGroupRSArgs struct {
	ServerGroupID       string   `json:"sgId"`
	BackendServerIDList []string `json:"backendServerIdList"`
}

// CreateAppServerGroupRS create appblb servergrop backend server
func (c *Client) CreateAppServerGroupRS(ctx context.Context, lbShortID string,
	args *CreateAppServerGroupRSArgs, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/blbrs", lbShortID)
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	return err
}

// UpdateAppServerGroupRSWeight update appblb servergrop backend server
func (c *Client) UpdateAppServerGroupRSWeight(ctx context.Context, lbShortID string,
	args *UpdateAppServerGroupRSWeightArgs, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/blbrs", lbShortID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	return err
}

// DescribeAppServerGroupRS list all the backendserver under appblb servergroup
func (c *Client) DescribeAppServerGroupRS(ctx context.Context, lbShortID, serverGroupID string,
	option *bce.SignOption) (*DescribeAppServerGroupRSResponse, error) {
	if lbShortID == "" || serverGroupID == "" {
		return nil, errors.New("missing args blb_id or server_group_id")
	}

	params := map[string]string{
		"sgId": serverGroupID,
	}

	url := fmt.Sprintf("appblb/%s/blbrs", lbShortID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var blbsResp *DescribeAppServerGroupRSResponse
	err = json.Unmarshal(bodyContent, &blbsResp)
	if err != nil {
		return nil, err
	}

	return blbsResp, nil
}

// DescribeAppServerGroupRSMount List RS in VPC and Mounted in ServerGroup
func (c *Client) DescribeAppServerGroupRSMount(ctx context.Context, lbShortID, serverGroupID string,
	option *bce.SignOption) ([]*AppBackendServer, error) {
	if lbShortID == "" || serverGroupID == "" {
		return nil, errors.New("missing args blb_id or server_group_id")
	}

	params := map[string]string{
		"sgId": serverGroupID,
	}

	url := fmt.Sprintf("appblb/%s/blbrsmount", lbShortID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var blbsResp *DescribeAppServerGroupRSResponse
	err = json.Unmarshal(bodyContent, &blbsResp)
	if err != nil {
		return nil, err
	}

	return blbsResp.BackendServerList, nil
}

// DescribeAppServerGroupRSUnMount List RS in VPC but not mounted in ServerGroup
func (c *Client) DescribeAppServerGroupRSUnMount(ctx context.Context, lbShortID,
	serverGroupID string, option *bce.SignOption) ([]*AppBackendServer, error) {
	if lbShortID == "" || serverGroupID == "" {
		return nil, errors.New("missing args blb_id or server_group_id")
	}

	params := map[string]string{
		"sgId": serverGroupID,
	}

	url := fmt.Sprintf("appblb/%s/blbrsunmount", lbShortID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var blbsResp *DescribeAppServerGroupRSResponse
	err = json.Unmarshal(bodyContent, &blbsResp)
	if err != nil {
		return nil, err
	}

	return blbsResp.BackendServerList, nil
}

// DeleteAppServerGroupRS delete AppServerGroup RS
func (c *Client) DeleteAppServerGroupRS(ctx context.Context, lbShortID string,
	args *DeleteAppServerGroupRSArgs, option *bce.SignOption) error {
	params := map[string]string{
		"batchdelete": "",
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("appblb/%s/blbrs", lbShortID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	return err
}
