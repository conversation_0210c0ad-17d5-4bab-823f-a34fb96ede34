// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/internalvpc (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	internalvpc "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/internalvpc"
	reflect "reflect"
)

// MockInterface is a mock of Interface interface
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// AttachENI mocks base method
func (m *MockInterface) AttachENI(arg0 context.Context, arg1, arg2 string, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AttachENI", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AttachENI indicates an expected call of AttachENI
func (mr *MockInterfaceMockRecorder) AttachENI(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AttachENI", reflect.TypeOf((*MockInterface)(nil).AttachENI), arg0, arg1, arg2, arg3)
}

// DeleteENI mocks base method
func (m *MockInterface) DeleteENI(arg0 context.Context, arg1 string, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteENI", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteENI indicates an expected call of DeleteENI
func (mr *MockInterfaceMockRecorder) DeleteENI(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteENI", reflect.TypeOf((*MockInterface)(nil).DeleteENI), arg0, arg1, arg2)
}

// DetachENI mocks base method
func (m *MockInterface) DetachENI(arg0 context.Context, arg1, arg2 string, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DetachENI", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DetachENI indicates an expected call of DetachENI
func (mr *MockInterfaceMockRecorder) DetachENI(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DetachENI", reflect.TypeOf((*MockInterface)(nil).DetachENI), arg0, arg1, arg2, arg3)
}

// GetENIs mocks base method
func (m *MockInterface) GetENIs(arg0 context.Context, arg1 *internalvpc.GetENIsRequest, arg2 *bce.SignOption) (*internalvpc.GetENIsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetENIs", arg0, arg1, arg2)
	ret0, _ := ret[0].(*internalvpc.GetENIsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetENIs indicates an expected call of GetENIs
func (mr *MockInterfaceMockRecorder) GetENIs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetENIs", reflect.TypeOf((*MockInterface)(nil).GetENIs), arg0, arg1, arg2)
}

// GetSubnetByID mocks base method
func (m *MockInterface) GetSubnetByID(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*internalvpc.Subnet, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSubnetByID", arg0, arg1, arg2)
	ret0, _ := ret[0].(*internalvpc.Subnet)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSubnetByID indicates an expected call of GetSubnetByID
func (mr *MockInterfaceMockRecorder) GetSubnetByID(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSubnetByID", reflect.TypeOf((*MockInterface)(nil).GetSubnetByID), arg0, arg1, arg2)
}

// MapByLongID mocks base method
func (m *MockInterface) MapByLongID(arg0 context.Context, arg1 []string, arg2 int, arg3 *bce.SignOption) (map[string]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MapByLongID", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(map[string]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MapByLongID indicates an expected call of MapByLongID
func (mr *MockInterfaceMockRecorder) MapByLongID(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MapByLongID", reflect.TypeOf((*MockInterface)(nil).MapByLongID), arg0, arg1, arg2, arg3)
}

// MapByShortID mocks base method
func (m *MockInterface) MapByShortID(arg0 context.Context, arg1 []string, arg2 *bce.SignOption) (map[string]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MapByShortID", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[string]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MapByShortID indicates an expected call of MapByShortID
func (mr *MockInterfaceMockRecorder) MapByShortID(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MapByShortID", reflect.TypeOf((*MockInterface)(nil).MapByShortID), arg0, arg1, arg2)
}
