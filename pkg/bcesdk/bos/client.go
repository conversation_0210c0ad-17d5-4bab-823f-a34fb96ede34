// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/11/20, by <EMAIL>, create
*/
/*
bos
*/

package bos

import (
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

var _ Interface = &Client{}

// Endpoint contains all endpoints of Baidu Cloud BOS.
var Endpoint = map[string]string{
	"bj":  "bj.bcebos.com",
	"gz":  "gz.bcebos.com",
	"su":  "su.bcebos.com",
	"hkg": "hkg.bcebos.com",
	"fwh": "fwh.bcebos.com",
	"bd":  "bd.bcebos.com",
}

// Client 实现 helm.Interface
type Client struct {
	*bce.Client
}

// NewClient client
func NewClient(config *bce.Config) *Client {
	return &Client{
		Client: bce.NewClient(config),
	}
}

// SetDebug 是否开启 debug 模式
func (c *Client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

// GetURL generates the full URL of http request for Baidu Cloud API.
func (c *Client) GetURL(endpoint string, path string, params map[string]string) string {
	host := endpoint
	uriPath := path
	c.APIVersion = ""
	return c.Client.GetURL(host, uriPath, params)
}
