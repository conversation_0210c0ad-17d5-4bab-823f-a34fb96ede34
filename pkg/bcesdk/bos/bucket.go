// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/11/20 , by <EMAIL>, create
*/
/*
实现 BOS bucket 相关方法
*/

package bos

import (
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

// ListBuckets - 获取用户的bucket列表
// PARAMS:
//   - ctx: The context to trace request
//
// RETURNS:
//
//	ListBucketsResponse: bucket列表
//	error: nil if succeed, error if fail
func (c *Client) ListBuckets(ctx context.Context) (*ListBucketsResponse, error) {
	req, err := bce.NewRequest("GET", c.GetURL(c.Endpoint, "", nil), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var listBucketsResponse ListBucketsResponse
	err = json.Unmarshal(bodyContent, &listBucketsResponse)
	if err != nil {
		return nil, err
	}
	return &listBucketsResponse, nil
}

// CheckBucketExist - 检查用户的bucket是否存在
// PARAMS:
//   - ctx: The context to trace request
//   - bucketName: bucket名字
//
// RETURNS:
//
//	bool: bucket是否存在
//	error: nil if succeed, error if fail
func (c *Client) CheckBucketExist(ctx context.Context, bucketName string) (bool, error) {
	bucketList, err := c.ListBuckets(ctx)
	if err != nil {
		logger.Errorf(ctx, "ListBuckets failed: %s", err)
		return false, err
	}
	if len(bucketList.Buckets) == 0 {
		logger.Errorf(ctx, "The user does not have a bucket")
		return false, nil
	}
	for _, bucket := range bucketList.Buckets {
		if bucket.Name == bucketName {
			return true, nil
		}
	}
	logger.Errorf(ctx, "The user does not have bucket %s", bucketName)
	return false, nil
}

// PutBucket - 创建bucket
// PARAMS:
//   - ctx: The context to trace request
//   - bucketName: bucket名字
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) PutBucket(ctx context.Context, bucketName string) error {
	endpoint := bucketName + "." + c.Endpoint
	req, err := bce.NewRequest("PUT", c.GetURL(endpoint, "", nil), nil)
	if err != nil {
		return err
	}
	option := &bce.SignOption{}
	option.AddHeader("Content-Type", "text/plain")
	req.Host = endpoint
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}
	return nil
}

// DeleteBucket - 删除bucket
// PARAMS:
//   - ctx: The context to trace request
//   - bucketName: bucket名字
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) DeleteBucket(ctx context.Context, bucketName string) error {
	endpoint := bucketName + "." + c.Endpoint
	req, err := bce.NewRequest("DELETE", c.GetURL(endpoint, "", nil), nil)
	if err != nil {
		return err
	}
	req.Host = endpoint
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return err
	}
	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}
	return nil
}
