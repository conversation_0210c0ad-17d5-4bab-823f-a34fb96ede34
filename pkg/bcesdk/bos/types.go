// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/11/20, by <EMAIL>, create
*/
/*
文件定义 BOS OpenAPI 相关接口
*/
package bos

import (
	"context"
)

// Interface 定义 BOS OpenAPI 相关 Interface
type Interface interface {
	// bucket
	ListBuckets(ctx context.Context) (*ListBucketsResponse, error)

	CheckBucketExist(ctx context.Context, bucketName string) (bool, error)
	PutBucket(ctx context.Context, bucketName string) error
	DeleteBucket(ctx context.Context, bucketName string) error
}

type Owner struct {
	ID          string `json:"id"`
	DisplayName string `json:"displayName"`
}

type Bucket struct {
	Name         string `json:"name"`
	Location     string `json:"location"`
	CreationDate string `json:"creationDate"`
}

type ListBucketsResponse struct {
	Owner   Owner    `json:"owner"`
	Buckets []Bucket `json:"buckets"`
}
