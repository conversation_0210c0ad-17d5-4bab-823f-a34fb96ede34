package bce

import (
	"fmt"
	"net/http"
)

// Error implements the error interface
//
// Most methods in the SDK will return bcerrror.Error instance.
type Error struct {
	StatusCode               int
	Code, Message, RequestID string
}

// Error returns the formatted error message.
func (err *Error) Error() string {
	return fmt.Sprintf("Error Message: \"%s\", Error Code: \"%s\", Status Code: %d, Request Id: \"%s\"",
		err.Message, err.Code, err.StatusCode, err.RequestID)
}

func IsNotFound(err error) bool {
	bceErr, ok := err.(*Error)
	if !ok {
		return false
	}

	return bceErr.StatusCode == http.StatusNotFound
}
