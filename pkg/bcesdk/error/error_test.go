package bce

import (
	"testing"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
)

func TestError(t *testing.T) {
	bceError := &Error{
		StatusCode: 500,
		Code:       "StatusInternalServerError",
		Message:    "failed",
		RequestID:  "123",
	}

	result := bceError.Error()
	expected := "Error Message: \"failed\", Error Code: \"StatusInternalServerError\", Status Code: 500, Request Id: \"123\""

	if result != expected {
		t.Error(util.FormatTest("Error", result, expected))
	}
}
