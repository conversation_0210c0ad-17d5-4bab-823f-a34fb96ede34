package bbc

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func (c *Client) GetImage(ctx context.Context, imageID string, option *bce.SignOption) (*Image, error) {
	if imageID == "" {
		return nil, errors.New("image ID is empty")
	}

	params := map[string]string{}

	req, err := bce.NewRequest("GET", c.GetURL("v1/image/"+imageID, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	imageResp := struct {
		Image *Image `json:"image"`
	}{}
	err = json.Unmarshal(bodyContent, &imageResp)
	if err != nil {
		return nil, err
	}

	return imageResp.Image, nil
}

func (c *Client) GetImages(ctx context.Context, request *GetImagesRequest, option *bce.SignOption) (*GetImagesResponse, error) {
	params := map[string]string{}

	if request.Marker != "" {
		params["marker"] = request.Marker
	}
	if request.MaxKeys != 0 {
		params["maxKeys"] = fmt.Sprintf("%d", request.MaxKeys)
	}
	if request.ImageType != "" {
		params["imageType"] = string(request.ImageType)
	}

	req, err := bce.NewRequest("GET", c.GetURL("/v1/image", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var getImagesResponse *GetImagesResponse
	err = json.Unmarshal(bodyContent, &getImagesResponse)
	if err != nil {
		return nil, err
	}

	return getImagesResponse, nil
}

func (c *Client) GetFlavorImages(ctx context.Context, option *bce.SignOption) (*FlavorImagesResponse, error) {
	params := map[string]string{}

	req, err := bce.NewRequest("POST", c.GetURL("/v1/flavor/image", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var response *FlavorImagesResponse
	err = json.Unmarshal(bodyContent, &response)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (c *Client) GetCustomFlavorImages(ctx context.Context, option *bce.SignOption) (*FlavorImagesResponse, error) {
	params := map[string]string{}

	req, err := bce.NewRequest("POST", c.GetURL("/v1/customFlavor/image", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var response *FlavorImagesResponse
	err = json.Unmarshal(bodyContent, &response)
	if err != nil {
		return nil, err
	}

	return response, nil
}
