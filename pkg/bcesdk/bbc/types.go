package bbc

import (
	"context"

	bbcapi "github.com/baidubce/bce-sdk-go/services/bbc"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bccimage"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/internalvpc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/tag"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bbc Interface
type Interface interface {
	SetDebug(bool)

	GetInstance(ctx context.Context, instanceID string, option *bce.SignOption) (*Instance, error)
	GetInstanceWithDeploySet(ctx context.Context, instanceID string, isDeploySet bool, option *bce.SignOption) (*Instance, error)
	CreateInstance(ctx context.Context, args *CreateInstanceArgsShell, clientToken string, option *bce.SignOption) (*bbcapi.CreateInstanceResult, error)
	DeleteInstances(ctx context.Context, args *bbcapi.DeleteInstanceArgs, option *bce.SignOption) error
	BatchRebuildInstances(ctx context.Context, args *bbcapi.RebuildBatchInstanceArgs, option *bce.SignOption) (*bbcapi.BatchRebuildResponse, error)

	GetImage(ctx context.Context, imageID string, option *bce.SignOption) (*Image, error)
	GetImages(ctx context.Context, request *GetImagesRequest, option *bce.SignOption) (*GetImagesResponse, error)
	GetFlavorImages(ctx context.Context, option *bce.SignOption) (*FlavorImagesResponse, error)
	GetCustomFlavorImages(ctx context.Context, option *bce.SignOption) (*FlavorImagesResponse, error)

	BatchAddIP(ctx context.Context, args *BatchAddIPArgs, signOpt *bce.SignOption) (*BatchAddIPResponse, error)
	BatchDelIP(ctx context.Context, args *BatchDelIPArgs, signOpt *bce.SignOption) error
	GetInstanceENI(ctx context.Context, instanceID string, signOpt *bce.SignOption) (*GetInstanceENIResponse, error)

	QueryPrice(ctx context.Context, args *PriceRequest, signOpt *bce.SignOption) (*PriceResponse, error)

	BindTags(ctx context.Context, instanceID string, tags []tag.Tag, option *bce.SignOption) error
	UnbindTags(ctx context.Context, instanceID string, tags []tag.Tag, option *bce.SignOption) error

	GetFlavors(ctx context.Context, option *bce.SignOption) (*bbcapi.ListFlavorsResult, error)

	// 安全组
	BindSecurityGroup(ctx context.Context, instanceID, securityGroupID []string, option *bce.SignOption) error

	UnbindSecurityGroup(ctx context.Context, instanceID, securityGroupID string, option *bce.SignOption) error
}

type CreateInstanceArgsShell struct {
	bbcapi.CreateInstanceArgs
	EnterpriseSecurityGroupID string `json:"enterpriseSecurityGroupId,omitempty"`
}

type Instance struct {
	InstanceID      string                    `json:"id"`
	InstanceName    string                    `json:"name"`
	Status          string                    `json:"status"`
	PaymentTiming   bcc.PaymentTiming         `json:"paymentTiming"`
	AutoRenew       bool                      `json:"autoRenew"`
	Description     string                    `json:"desc"`
	InternalIP      string                    `json:"internalIp"`
	PublicIP        string                    `json:"publicIp"`
	BandwidthInMbps int                       `json:"networkCapacityInMbps"`
	ImageID         string                    `json:"imageId"`
	FlavorID        string                    `json:"flavorId"`
	AvailableZone   internalvpc.AvailableZone `json:"zone"`
	Region          string                    `json:"region"`
	CreateTime      string                    `json:"createTime"`
	ExpireTime      string                    `json:"expireTime"`
	Tags            []tag.Tag                 `json:"tags"`
	GpuCard         string                    `json:"gpuCard"`
	CardCount       string                    `json:"cardCount"`
	GpuTotalMemory  string                    `json:"gpuTotalMemory"`

	// RackID 机架 ID，非公开，仅查询传入 isDeploySet=true 时展示
	RackID string `json:"rackId"`

	// HostID 宿主机 ID，非公开，仅查询传入 isDeploySet=true 时展示
	HostID string `json:"hostId"`

	// SwitchID 交换机 ID，非公开，仅查询传入 isDeploySet=true 时展示
	SwitchID string `json:"switchId"`
}

type Image struct {
	ImageID    string             `json:"id"`
	ImageName  string             `json:"name"`
	ImageType  bccimage.ImageType `json:"type"`
	OSType     string             `json:"osType"`
	OSVersion  string             `json:"osVersion"`
	OSName     string             `json:"osName"`
	OSBuild    string             `json:"osBuild"`
	OSArch     string             `json:"osArch"`
	Status     string             `json:"status"`
	CreateTime string             `json:"createTime"`
}

type GetImagesRequest struct {
	Marker    string             `json:"marker"`
	MaxKeys   int                `json:"maxKeys"`
	ImageType bccimage.ImageType `json:"imageType"`
}

type GetImagesResponse struct {
	Marker      string  `json:"marker"`
	MaxKeys     int     `json:"maxKeys"`
	IsTruncated bool    `json:"isTruncated"`
	NextMarker  string  `json:"nextMarker"`
	Images      []Image `json:"images"`
}

type BatchAddIPArgs struct {
	InstanceID                     string   `json:"instanceId"`
	PrivateIPs                     []string `json:"privateIps"`
	SecondaryPrivateIPAddressCount int      `json:"secondaryPrivateIpAddressCount"`
}

type BatchAddIPResponse struct {
	PrivateIPs []string `json:"privateIps"`
}

type BatchDelIPArgs struct {
	InstanceID string   `json:"instanceId"`
	PrivateIPs []string `json:"privateIps"`
}

type GetInstanceENIResponse struct {
	ENIID        string      `json:"eniId"`
	Name         string      `json:"name"`
	ZoneName     string      `json:"zoneName"`
	Description  string      `json:"description"`
	InstanceID   string      `json:"instanceId"`
	MacAddress   string      `json:"macAddress"`
	VPCID        string      `json:"vpcId"`
	SubnetID     string      `json:"subnetId"`
	Status       string      `json:"status"`
	PrivateIPSet []PrivateIP `json:"privateIpSet"`
}

type PrivateIP struct {
	PublicIPAddress  string `json:"publicIpAddress"`
	Primary          bool   `json:"primary"`
	PrivateIPAddress string `json:"privateIpAddress"`
	IPv6Address      string `json:"ipv6Address"`
}

type PriceRequest struct {
	FlavorID      string  `json:"flavorId"`
	PurchaseCount int     `json:"purchaseCount"`
	Billing       Billing `json:"billing"`
}

type Billing struct {
	PaymentTiming string      `json:"paymentTiming"` // Prepaid Postpaid
	Reservation   Reservation `json:"reservation"`
}

type Reservation struct {
	ReservationLength int `json:"reservationLength"`
}

type PriceResponse struct {
	Price string `json:"price"`
}

type FlavorImagesResponse struct {
	Result []FlavorImage `json:"result"`
}

type FlavorImage struct {
	FlavorID string  `json:"flavorId"`
	Images   []Image `json:"images"`
}

type ImageType string

const (
	Common ImageType = "common"
	Custom ImageType = "custom"
)
