package bbc

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

func (args *BatchAddIPArgs) validate() error {
	if args == nil {
		return errors.New("BatchAddIP validate failed: args cannot be nil")
	}

	if args.InstanceID == "" {
		return errors.New("BatchAddIP validate failed: InstanceID cannot be empty")
	}

	return nil
}

func (args *BatchDelIPArgs) validate() error {
	if args == nil {
		return errors.New("BatchDelIP validate failed: args cannot be nil")
	}

	if args.InstanceID == "" {
		return errors.New("BatchDelIP validate failed: InstanceID cannot be empty")
	}

	if len(args.PrivateIPs) == 0 {
		return errors.New("BatchDelIP validate failed: PrivateIPs cannot be empty")
	}

	return nil
}

func (c *Client) BatchAddIP(ctx context.Context, args *BatchAddIPArgs, signOpt *bce.SignOption) (*BatchAddIPResponse, error) {
	ctx = logger.EnsureRequestIDInCtx(ctx)

	err := args.validate()
	if err != nil {
		return nil, err
	}

	bodyContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("v1/instance/batchAddIp", nil), bytes.NewBuffer(bodyContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return nil, err
	}
	bodyContent, err = resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	batchAddIPResponse := &BatchAddIPResponse{}
	err = json.Unmarshal(bodyContent, batchAddIPResponse)
	if err != nil {
		return nil, err
	}

	return batchAddIPResponse, nil
}

func (c *Client) BatchDelIP(ctx context.Context, args *BatchDelIPArgs, signOpt *bce.SignOption) error {
	ctx = logger.EnsureRequestIDInCtx(ctx)

	err := args.validate()
	if err != nil {
		return err
	}

	bodyContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("v1/instance/batchDelIp", nil), bytes.NewBuffer(bodyContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return err
	}
	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}

	return nil
}

func (c *Client) GetInstanceENI(ctx context.Context, instanceID string, signOpt *bce.SignOption) (*GetInstanceENIResponse, error) {
	ctx = logger.EnsureRequestIDInCtx(ctx)

	if instanceID == "" {
		return nil, errors.New("GetInstanceENI failed: instanceID is empty")
	}

	url := fmt.Sprintf("v1/vpcPort/%s", instanceID)
	req, err := bce.NewRequest("GET", c.GetURL(url, nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	instanceENIResponse := &GetInstanceENIResponse{}
	err = json.Unmarshal(bodyContent, instanceENIResponse)
	if err != nil {
		return nil, err
	}

	return instanceENIResponse, nil
}
