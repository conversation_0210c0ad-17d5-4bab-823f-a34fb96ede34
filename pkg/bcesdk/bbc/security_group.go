package bbc

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// instanceIds和securityGroups不同于BCC的字符串形式，在BBC中是list
func (c *Client) BindSecurityGroup(ctx context.Context, instanceIDs, securityGroups []string, option *bce.SignOption) error {
	if len(instanceIDs) == 0 || securityGroups == nil {
		return errors.New("instanceID or securityGroupID is empty")
	}

	params := map[string]string{
		"bind": "",
	}

	body := struct {
		InstanceIds    []string `json:"instanceIds"`
		SecurityGroups []string `json:"securityGroups"`
	}{
		InstanceIds:    instanceIDs,
		SecurityGroups: securityGroups,
	}
	postContent, err := json.Marshal(body)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v1/instance/securitygroup", params), bytes.<PERSON>Buffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

func (c *Client) UnbindSecurityGroup(ctx context.Context, instanceID, securityGroupID string, option *bce.SignOption) error {
	if instanceID == "" || securityGroupID == "" {
		return errors.New("instanceID or securityGroupID is empty")
	}

	params := map[string]string{
		"unbind": "",
	}

	body := struct {
		InstanceID      string `json:"instanceId"`
		SecurityGroupId string `json:"securityGroupId"`
	}{
		InstanceID:      instanceID,
		SecurityGroupId: securityGroupID,
	}
	postContent, err := json.Marshal(body)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v1/instance/securitygroup", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}
