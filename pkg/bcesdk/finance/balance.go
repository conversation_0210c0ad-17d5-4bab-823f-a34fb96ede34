package finance

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func (c *client) GetAccountBalance(ctx context.Context, accountID string, option *bce.SignOption) (*GetAccountBalanceResponse, error) {
	params := map[string]string{
		"accountId": accountID,
	}

	req, err := bce.NewRequest("GET", c.GetURL("/v3/finance/account/fund", params), bytes.NewBuffer(nil))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var response *GetAccountBalanceResponse
	err = json.Unmarshal(bodyContent, &response)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (c *client) PurchaseValidation(ctx context.Context, request *PurchaseValidationRequest, option *bce.SignOption) (*PurchaseValidationResponse, error) {
	if request == nil {
		return nil, errors.New("request is nil")
	}

	params := map[string]string{
		"accountId":   request.AccountID,
		"productType": string(request.ProductType),
		"region":      request.Region,
		"chargeType":  string(request.ChargeType),
	}

	req, err := bce.NewRequest("GET", c.GetURL("/v3/finance/purchase_validation_v2", params), bytes.NewBuffer(nil))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var response *PurchaseValidationResponse
	err = json.Unmarshal(bodyContent, &response)
	if err != nil {
		return nil, err
	}

	return response, nil
}
