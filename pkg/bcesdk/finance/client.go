package finance

import (
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
)

// Endpoints 包含所有地域的 endpoints
var Endpoints = map[string]string{
	"bj":      "finance.bce-internal.baidu.com:8662",
	"gz":      "finance.bce-internal.baidu.com:8662",
	"su":      "finance.bce-internal.baidu.com:8662",
	"hkg":     "finance.bce-internal.baidu.com:8662",
	"fwh":     "finance.bce-internal.baidu.com:8662",
	"bd":      "finance.bce-internal.baidu.com:8662",
	"sandbox": "bjyz-y22-sandbox002.bjyz.baidu.com:8662",
}

var _ Interface = &client{}

// client is the zone client implementation for Interface
type client struct {
	*bce.Client
}

// NewClient return client
func NewClient(config *bce.Config) *client {
	return &client{
		Client: bce.NewClient(config),
	}
}

// GetURL generates the full URL of http request for BaiDu Cloud API
func (c *client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoints[c.GetRegion()]
	}
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}

// SetDebug open or close Debug Mode
func (c *client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

// NewSignOption return zone specified sign option
func NewSignOption() *bce.SignOption {
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")
	return option
}
