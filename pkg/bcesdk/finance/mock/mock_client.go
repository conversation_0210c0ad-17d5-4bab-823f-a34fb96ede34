// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/finance (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	finance "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/finance"
	reflect "reflect"
)

// MockInterface is a mock of Interface interface
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// GetAccountBalance mocks base method
func (m *MockInterface) GetAccountBalance(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*finance.GetAccountBalanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountBalance", arg0, arg1, arg2)
	ret0, _ := ret[0].(*finance.GetAccountBalanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountBalance indicates an expected call of GetAccountBalance
func (mr *MockInterfaceMockRecorder) GetAccountBalance(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountBalance", reflect.TypeOf((*MockInterface)(nil).GetAccountBalance), arg0, arg1, arg2)
}

// PurchaseValidation mocks base method
func (m *MockInterface) PurchaseValidation(arg0 context.Context, arg1 *finance.PurchaseValidationRequest, arg2 *bce.SignOption) (*finance.PurchaseValidationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PurchaseValidation", arg0, arg1, arg2)
	ret0, _ := ret[0].(*finance.PurchaseValidationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PurchaseValidation indicates an expected call of PurchaseValidation
func (mr *MockInterfaceMockRecorder) PurchaseValidation(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PurchaseValidation", reflect.TypeOf((*MockInterface)(nil).PurchaseValidation), arg0, arg1, arg2)
}

// SetDebug mocks base method
func (m *MockInterface) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug
func (mr *MockInterfaceMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), arg0)
}
