package finance

import (
	"context"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logicbcc"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/finance Interface

// 参考：http://agroup.baidu.com/share/md/f50853ad4a654a3eba000c9f408eaac6
type Interface interface {
	SetDebug(debug bool)
	GetAccountBalance(ctx context.Context, accountID string, option *bce.SignOption) (*GetAccountBalanceResponse, error)
	PurchaseValidation(ctx context.Context, request *PurchaseValidationRequest, option *bce.SignOption) (*PurchaseValidationResponse, error)
}

type GetAccountBalanceResponse struct {
	Cash   float64 `json:"cash"`
	Rebate float64 `json:"rebate"`
	Total  float64 `json:"totalAmount"`
}

type PurchaseValidationRequest struct {
	AccountID   string               `json:"accountId"`
	ProductType ProductType          `json:"productType"`
	Region      string               `json:"region"`
	ChargeType  logicbcc.ProductType `json:"chargeType"`
}

type ProductType string

const (
	ProductTypeBCC = "BCC"
	ProductTypeCCE = "CCE"
)

type PurchaseValidationResponse struct {
	Status bool    `json:"status"`
	Reason string  `json:"reason"`
	Limit  float64 `json:"limitAmount"`
}
