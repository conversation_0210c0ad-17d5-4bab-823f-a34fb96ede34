package cce

import (
	"context"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logicbcc"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/vpc"
)

const (
	InstanceStatusRunning      InstanceStatus = "RUNNING"
	InstanceStatusCreating     InstanceStatus = "CREATING"
	InstanceStatusDeleting     InstanceStatus = "DELETING"
	InstanceStatusDeleted      InstanceStatus = "DELETED"
	InstanceStatusCreateFailed InstanceStatus = "CREATE_FAILED"
	InstanceStatusError        InstanceStatus = "ERROR"
	InstanceStatusReady        InstanceStatus = "READY"
)

var _ Interface = &Client{}

// Interface defines the interface of CCE Client
type Interface interface {
	SetDebug(debug bool)

	CreateCluster(ctx context.Context, args *CreateClusterArgs) (*CreateClusterResponse, error)

	ListClusterNodes(ctx context.Context, clusterID string, option *bce.SignOption) (*ListClusterNodesResponse, error)

	UpdateClusterComment(ctx context.Context, clusterID string, args *ClusterComment, option *bce.SignOption) error

	CreateInstance(ctx context.Context, args *CreateInstanceArgs, option *bce.SignOption) (*CreateInstanceResponse, error)

	AddExistedInstance(ctx context.Context, args *AddExistedInstanceArgs, option *bce.SignOption) error

	GenerateSignature(ctx context.Context, args *GenerateSignatureArgs) (*Signature, error)

	// TODO: Add more
}

// CreateClusterArgs createCluster's args
// TODO: just for cce-ingress-controller's unit test
type CreateClusterArgs struct {
	ClusterName string `json:"clusterName"`
	VPCID       string `json:"vpcId"`
	SubnetID    string `json:"subnetId"`
	VMCount     int    `json:"vmCount"`
}

// CreateClusterResponse createCluster's response
type CreateClusterResponse struct {
	ClusterID string   `json:"clusterUuid"`
	OrderID   []string `json:"orderId"`
}

type AddExistedInstanceArgs struct {
	ClusterUuid  string            `json:"clusterUuid"`
	NeedRebuild  bool              `json:"needRebuild"`
	ImageId      string            `json:"imageId,omitempty"`
	AdminPass    string            `json:"adminPass"`
	InstanceType ShiftInstanceType `json:"instanceType"`
	NodeInfoList []CceNodeInfo     `json:"nodeInfoList"`
}

type CceNodeInfo struct {
	InstanceId string `json:"instanceId"`
	AdminPass  string `json:"adminPass,omitempty"`
}

type ShiftInstanceType string

const (
	ShiftInstanceTypeBcc ShiftInstanceType = "BCC"
	ShiftInstanceTypeBBC ShiftInstanceType = "BBC"
)

type CreateInstanceArgs struct {
	ClusterUuid     string                      `json:"clusterUuid"`
	DccUuid         string                      `json:"dccUuid,omitempty"`
	CdsPreMountInfo *CdsPreMountInfo            `json:"cdsPreMountInfo,omitempty"`
	OrderContent    *logicbcc.CreateServersArgs `json:"orderContent"`
}

type CdsPreMountInfo struct {
	MountPath string           `json:"mountPath"`
	CdsConfig []DiskSizeConfig `json:"cdsConfig"`
}

type DiskSizeConfig struct {
	Size       string     `json:"size"`
	VolumeType VolumeType `json:"volumeType"`
	SnapshotId string     `json:"snapshotId,omitempty"`
}

type VolumeType string

const (
	VolumeTypeSata       VolumeType = "sata"
	VolumeTypeSsd        VolumeType = "ssd"
	VolumeTypePremiumSsd VolumeType = "premium_ssd"
)

type CreateInstanceResponse struct {
	ClusterUuid string   `json:"clusterUuid"`
	OrderId     []string `json:"orderId"`
}

// Cluster for CCE Cluster
// TODO: just for cce-ingress-controller's unit test
type Cluster struct {
	ClusterID    string  `json:"clusterUuid"`
	InstanceList []*Node `json:"instanceList"`
}

// Node fot CCE Node
type Node struct {
	InstanceID   string       `json:"instanceShortId"`
	InstanceName string       `json:"instanceName"`
	Hostname     string       `json:"hostname"`
	InstanceType InstanceType `json:"instanceType"`

	Status InstanceStatus `json:"status"`

	IP           string `json:"fixIp"`
	EIP          string `json:"eip"`
	EIPBandwidth int    `json:"eipBandwidth"`

	VPCID      string         `json:"vpcId"`
	VPCCIDR    string         `json:"vpcCidr"`
	SubnetID   string         `json:"subnetId"`
	SubnetType vpc.SubnetType `json:"subnetType"`

	AvailableZone string `json:"availableZone"`

	ClusterID string `json:"clusterUuid"`

	CPU         int        `json:"cpu"`     // unit = core
	Memory      int        `json:"memory"`  // unit = GB
	SysDiskSize int        `json:"sysDisk"` // unit = GB
	CDSDisk     []*CDSDisk `json:"cdsDisk,omitempty"`

	RuntimeVersion string `json:"runtimeVersion"`

	PaymentMethod PaymentType `json:"paymentMethod"`

	CreateTime time.Time `json:"createTime"`
	DeleteTime time.Time `json:"deleteTime"`
	ExpireTime time.Time `json:"expireTime"`
}

// InstanceType node instance type
type InstanceType string

// InstanceStatus node instance status
type InstanceStatus string

// PaymentType node payment type
type PaymentType string

// CDSDisk node's cds disk
type CDSDisk struct {
}

// ListClusterNodesResponse the return of ListClusterNodes
type ListClusterNodesResponse struct {
	Marker      string  `json:"marker"`
	IsTruncated bool    `json:"isTruncated"`
	NextMarker  string  `json:"nextMarker"`
	MaxKeys     int     `json:"maxKeys"`
	Nodes       []*Node `json:"nodes"`
}

// ClusterComment the request of UpdateClusterComment
type ClusterComment struct {
	Comment string `json:"comment"`
}
