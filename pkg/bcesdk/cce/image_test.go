package cce

import (
	"context"
	"net/http"
	"testing"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func TestClientUpdateImageUserPassword(t *testing.T) {
	type args struct {
		ctx      context.Context
		username string
		password string
		signOpt  *bce.SignOption
	}
	tests := []struct {
		name    string
		envs    []*testEnvConfig
		args    args
		wantErr bool
	}{
		// All test cases.
		{
			name: "normal case",
			envs: []*testEnvConfig{
				{
					uri:        "/v1/image/user",
					method:     "PUT",
					statusCode: http.StatusOK,
				},
			},
			args: args{
				ctx:      context.TODO(),
				username: "username",
				password: "password",
			},
		},
		{
			name: "emtpy username case",
			envs: []*testEnvConfig{
				{
					uri:        "/v1/image/user",
					method:     "PUT",
					statusCode: http.StatusOK,
				},
			},
			args: args{
				ctx:      context.TODO(),
				username: "",
				password: "password",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			setupTestEnv(tt.envs)
			defer tearDownTestEnv()

			c := cceClient
			err := c.UpdateImageUserPassword(tt.args.ctx, tt.args.username, tt.args.password, tt.args.signOpt)
			if (err != nil) != tt.wantErr {
				t.Errorf("Client.CheckWhiteList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
