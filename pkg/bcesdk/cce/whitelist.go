package cce

import (
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

const EnableClusterRBAC FeatureType = "EnableClusterRBAC"

type FeatureType string

type CheckWhiteListResponse struct {
	IsExist bool `json:"isExist"`
}

func (c *Client) CheckWhiteList(ctx context.Context, featureType FeatureType, signOpt *bce.SignOption) (bool, error) {
	if len(featureType) == 0 {
		return false, errors.New("featureType cannot be empty")
	}
	params := map[string]string{
		"featureType": string(featureType),
	}
	req, err := bce.NewRequest("GET", c.GetURL("/v1/cluster/check_white_list", params), nil)
	if err != nil {
		return false, err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return false, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return false, err
	}

	checkWhiteListResponse := new(CheckWhiteListResponse)
	err = json.Unmarshal(bodyContent, checkWhiteListResponse)
	if err != nil {
		return false, err
	}

	return checkWhiteListResponse.IsExist, nil
}
