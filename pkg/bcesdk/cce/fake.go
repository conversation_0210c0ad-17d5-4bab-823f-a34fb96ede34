package cce

import (
	"context"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
)

// FakeClient for unit test
type FakeClient struct {
	ClusterMap map[string]*Cluster
	NodeMap    map[string]*Node
}

// NewFakeClient for AppBLB fake client
func NewFakeClient() *FakeClient {
	return &FakeClient{
		ClusterMap: map[string]*Cluster{},
		NodeMap:    map[string]*Node{},
	}
}

// SetDebug 开启调试
func (f *FakeClient) SetDebug(debug bool) {
	return
}

// CreateCluster to create CCE cluster
// TODO: just for cce-ingress-controller's unit test
func (f *FakeClient) CreateCluster(ctx context.Context, args *CreateClusterArgs) (*CreateClusterResponse, error) {
	if args == nil {
		return nil, errors.New("CreateCluster failed: args is nil")
	}

	cluster := &Cluster{}

	// Generate ClusterID
	for {
		clusterID := util.GenerateBCEShortID("c")
		if _, ok := f.ClusterMap[clusterID]; !ok {
			cluster.ClusterID = clusterID
			f.ClusterMap[clusterID] = cluster
			break
		}
	}

	// Generate Cluster Node
	nodes := []*Node{}
	for i := 0; i < args.VMCount; i++ {
		node := &Node{
			ClusterID:     cluster.ClusterID,
			InstanceName:  fmt.Sprintf("test%v", i),
			Hostname:      fmt.Sprintf("test%v", i),
			IP:            "0.0.0.0",
			VPCID:         args.VPCID,
			SubnetID:      args.SubnetID,
			AvailableZone: "A",
		}

		for {
			instanceID := util.GenerateBCEShortID("i")
			if _, ok := f.NodeMap[instanceID]; !ok {
				node.InstanceID = instanceID
				f.NodeMap[instanceID] = node
				break
			}
		}

		nodes = append(nodes, node)
	}

	return &CreateClusterResponse{
		ClusterID: cluster.ClusterID,
	}, nil
}

// ListClusterNodes list cluster nodes
func (f *FakeClient) ListClusterNodes(ctx context.Context, clusterID string, option *bce.SignOption) (*ListClusterNodesResponse, error) {
	nodes := []*Node{}

	if _, ok := f.ClusterMap[clusterID]; !ok {
		return nil, fmt.Errorf("ClusterID %s not exist: NoSuchObject", clusterID)
	}

	for _, node := range f.NodeMap {
		if node.ClusterID == clusterID {
			nodes = append(nodes, node)
		}
	}

	return &ListClusterNodesResponse{
		Nodes: nodes,
	}, nil
}

// UpdateClusterComment upgrade cluster description
func (f *FakeClient) UpdateClusterComment(ctx context.Context, clusterID string, comment *ClusterComment, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) CreateInstance(ctx context.Context, args *CreateInstanceArgs, option *bce.SignOption) (*CreateInstanceResponse, error) {
	return nil, nil
}

func (f *FakeClient) AddExistedInstance(ctx context.Context, args *AddExistedInstanceArgs, option *bce.SignOption) error {
	return nil
}

func (f *FakeClient) GenerateSignature(ctx context.Context, args *GenerateSignatureArgs) (*Signature, error) {
	return nil, nil
}
