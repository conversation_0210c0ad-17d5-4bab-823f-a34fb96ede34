package cce

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func (c *Client) CreateInstance(ctx context.Context, args *CreateInstanceArgs, option *bce.SignOption) (*CreateInstanceResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	params := map[string]string{
		"scalingUp": "",
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v1/cluster", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	result := new(CreateInstanceResponse)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) AddExistedInstance(ctx context.Context, args *AddExistedInstanceArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("args is nil")
	}

	params := map[string]string{
		"action": "shift_in",
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v1/cluster/existed_node", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}
