package cce

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func newClient(accessKeyID, secretAccessKey, region, endpoint string) *Client {
	return NewClient(&Config{
		Config: &bce.Config{
			Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
			Checksum:    true,
			Timeout:     30 * time.Second,
			Region:      region,
			Endpoint:    endpoint,
		},
	})
}

// TODO: just for debug manually
func testListClusterNodes(t *testing.T) {
	ctx := context.Background()

	// Init BCE Client
	ak := "e38d78f7b74841ec92727531369834d8"
	sk := "xxxxxxxx"
	region := "sz"
	endpoint := "cce.su.baidubce.com"

	c := newClient(ak, sk, region, endpoint)

	// Test ListClusterNodes
	nodesResq, err := c.ListClusterNodes(ctx, "c-pI7BmJYi", nil)
	if err != nil {
		t.Errorf("ListClusterNodes failed: %v", err)
		return
	}

	str, _ := json.Marshal(nodesResq)
	t.Errorf("ListClusterNodes failed: %v", string(str))
}

func testUpdateClusterComment(t *testing.T) {
	ctx := context.Background()

	ak := "6e249e45efa842118f92e4a68f31bb6a"
	sk := "xxxxxxxxx"
	region := "gz"
	endpoint := "100.67.176.12:8693"

	c := newClient(ak, sk, region, endpoint)

	err := c.UpdateClusterComment(ctx, "cce-lfb078r8", &ClusterComment{
		Comment: "test",
	}, nil)
	if err != nil {
		t.Errorf("update cluster comment failed: %v", err)
		return
	}
}
