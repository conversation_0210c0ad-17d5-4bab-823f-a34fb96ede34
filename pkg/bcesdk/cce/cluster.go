package cce

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// CreateCluster create CCE cluster
func (c *Client) CreateCluster(ctx context.Context, args *CreateClusterArgs) (*CreateClusterResponse, error) {
	return &CreateClusterResponse{}, nil
}

// ListClusterNodes gets all Instances of a cluster.
func (c *Client) ListClusterNodes(ctx context.Context, clusterID string, option *bce.SignOption) (*ListClusterNodesResponse, error) {
	if clusterID == "" {
		return nil, errors.New("clusterID is nil")
	}

	params := map[string]string{
		"clusterUuid": clusterID,
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/node", params), nil)

	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)

	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}

	var nodesResq ListClusterNodesResponse
	err = json.Unmarshal(bodyContent, &nodesResq)

	if err != nil {
		return nil, err
	}

	return &nodesResq, nil
}

// UpdateClusterComment update cluster description
func (c *Client) UpdateClusterComment(ctx context.Context, clusterID string, args *ClusterComment, option *bce.SignOption) error {
	if clusterID == "" {
		return errors.New("clusterID is nil")
	}

	params := map[string]string{
		"clusterUuid": clusterID,
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}
	req, err := bce.NewRequest("PUT", c.GetURL("v1/cluster/comment", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}
