package cce

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

type ImageUser struct {
	Username             string `json:"username"`
	Password             string `json:"password"`
	PasswordConfirmation string `json:"passwordConfirmation"`
}

func (c *Client) UpdateImageUserPassword(ctx context.Context, username, password string, signOpt *bce.SignOption) error {
	if username == "" || password == "" {
		return errors.New("username or password cannot be empty")
	}
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	args := &ImageUser{
		Username:             username,
		Password:             password,
		PasswordConfirmation: password,
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}
	req, err := bce.NewRequest("PUT", c.GetURL("/v1/image/user", params), bytes.<PERSON><PERSON><PERSON>(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return err
	}
	return nil
}
