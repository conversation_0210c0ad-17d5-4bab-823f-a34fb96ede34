package bci

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

type PullTaskArgs struct {
	Image       string       `json:"image" valid:"Required"`
	ImageSecret *ImageSecret `json:"imageSecret,omitempty"`
}

type ImageSecret struct {
	Server   string `json:"server" valid:"Required"`
	UserName string `json:"userName" valid:"Required"`
	Password string `json:"password" valid:"Required"`
}

type CreatePullTaskResponse struct {
	TaskID string `json:"taskId"`
}

func (c *client) CreatePullTask(ctx context.Context, args *PullTaskArgs, opt *bce.SignOption) (*CreatePullTaskResponse, error) {
	ctx = logger.EnsureRequestIDInCtx(ctx)

	params := map[string]string{
		"clientToken": c.<PERSON>oken(),
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("api/logical/bci/v1/pod/imageCache", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	task := new(CreatePullTaskResponse)
	err = json.Unmarshal(bodyContent, task)
	if err != nil {
		return nil, err
	}
	return task, nil
}

type QueryPullTaskResponse struct {
	Status PullTaskStatus `json:"status"`
}

type PullTaskStatus string

const (
	PullTaskStatusDoing  PullTaskStatus = "doing"
	PullTaskStatusDone   PullTaskStatus = "done"
	PullTaskStatusFailed PullTaskStatus = "failed"
)

func (c *client) QueryPullTask(ctx context.Context, taskID string, opt *bce.SignOption) (*QueryPullTaskResponse, error) {
	ctx = logger.EnsureRequestIDInCtx(ctx)
	if taskID == "" {
		return nil, errors.New("task id cannot be empty")
	}

	req, err := bce.NewRequest("GET", c.GetURL("api/logical/bci/v1/pod/imageCache"+"/"+taskID, nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	queryResponse := new(QueryPullTaskResponse)
	err = json.Unmarshal(bodyContent, queryResponse)
	if err != nil {
		return nil, err
	}
	return queryResponse, nil
}
