package bci

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

func TestListPodsMetrics(t *testing.T) {
	type args struct {
		podIds  []string
		signOpt *bce.SignOption
	}
	tests := []struct {
		name    string
		args    args
		envs    []*testEnv
		wantErr bool
	}{
		{
			name: "list pods metrics success",
			args: args{
				podIds:  []string{"testutils-pod"},
				signOpt: bce.NewSignOptionWithoutAuth(),
			},
			envs: []*testEnv{
				{
					method:  "POST",
					path:    "/api/logical/bci/v2/pod/listMetricsByShortIds",
					handler: newHandler(http.StatusOK, []byte(fmt.Sprint(`{"result": [{"podShortId":"testutils-pod", "metrics": [{"value":10000}]}]}`))),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := setupTestEnv(tt.envs)
			defer tearDownTestEnv(s)
			c := NewClient(NewConfig(&bce.Config{
				Credentials: new(bce.Credentials),
				Checksum:    true,
				Endpoint:    s.URL,
			}))
			c.SetDebug(true)
			ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
			defer cancel()
			_, err := c.ListPodsMetrics(ctx, tt.args.podIds, tt.args.signOpt)
			if (err != nil) != tt.wantErr {
				t.Errorf("client.ListPodsMetrics() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestListPodsSummary(t *testing.T) {
	type args struct {
		podIds  []string
		signOpt *bce.SignOption
	}
	tests := []struct {
		name    string
		args    args
		envs    []*testEnv
		wantErr bool
	}{
		{
			name: "list pods summary success",
			args: args{
				podIds:  []string{"testutils-pod"},
				signOpt: bce.NewSignOptionWithoutAuth(),
			},
			envs: []*testEnv{
				{
					method:  "POST",
					path:    "/api/logical/bci/v2/pod/listMetricsSummary",
					handler: newHandler(http.StatusOK, []byte(fmt.Sprint(`{"result": [{"podShortId":"testutils-pod", "metrics": [{"value":10000}]}]}`))),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := setupTestEnv(tt.envs)
			defer tearDownTestEnv(s)
			c := NewClient(NewConfig(&bce.Config{
				Credentials: new(bce.Credentials),
				Checksum:    true,
				Endpoint:    s.URL,
			}))
			c.SetDebug(true)
			ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
			defer cancel()
			_, err := c.ListPodsSummary(ctx, tt.args.podIds, tt.args.signOpt)
			if (err != nil) != tt.wantErr {
				t.Errorf("client.ListPodsMetrics() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
