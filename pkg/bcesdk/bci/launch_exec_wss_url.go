package bci

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

type LaunchExecWSSUrlArgs struct {
	PodID          string   `json:"podId"`
	ContainerName  string   `json:"containerName"`
	TerminalHeight int      `json:"terminalHeight,omitempty"`
	TerminalWeight int      `json:"terminalWeight,omitempty"`
	TTY            bool     `json:"tty"`
	Stdout         bool     `json:"stdout,omitempty"`
	Stdin          bool     `json:"stdin"`
	Stderr         bool     `json:"stderr,omitempty"`
	Command        []string `json:"command"`
}

type launchExecWSSUrlResponse struct {
	Success bool    `json:"success"`
	Status  int     `json:"status"`
	Result  execURL `json:"result"`
}

type execURL struct {
	URL string `json:"url"`
}

func (args *LaunchExecWSSUrlArgs) validate() error {
	if args.PodID == "" {
		return errors.New("empty pod ID")
	}
	if args.ContainerName == "" {
		return errors.New("empty container name")
	}
	if args.TerminalHeight < 0 {
		return errors.New("invalid terminal height")
	}
	if args.TerminalWeight < 0 {
		return errors.New("invalid terminal weight")
	}
	if len(args.Command) == 0 {
		return errors.New("empty command")
	}
	return nil
}

// LaunchExecWSSUrl launch exec wss url
func (c *client) LaunchExecWSSUrl(ctx context.Context, args *LaunchExecWSSUrlArgs, opt *bce.SignOption) (string, error) {
	ctx = logger.EnsureRequestIDInCtx(ctx)
	if err := args.validate(); err != nil {
		return "", err
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return "", err
	}

	req, err := bce.NewRequest(
		"POST", c.GetURL("/api/logical/bci/v1/pod/webshell", params), bytes.NewBuffer(postContent))
	if err != nil {
		return "", err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return "", err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}

	launchWebshellResponse := new(launchExecWSSUrlResponse)
	err = json.Unmarshal(bodyContent, launchWebshellResponse)
	if err != nil {
		return "", err
	}

	return launchWebshellResponse.Result.URL, nil
}
