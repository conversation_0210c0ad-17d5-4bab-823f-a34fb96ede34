package bci

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

type DeletePod struct {
	PodID string `json:"podId"`
	CCEID string `json:"cceId"`
}

func NewDeletePod(podID, cceID string) *DeletePod {
	return &DeletePod{
		PodID: podID,
		CCEID: cceID,
	}
}

type DeletePodArgs struct {
	DeletePods         []*DeletePod `json:"deletePods"`
	RelatedReleaseFlag bool         `json:"relatedReleaseFlag"`
}

func NewDeletePodArgs(podID, clusterID string, relatedReleaseFlag bool) *DeletePodArgs {
	return &DeletePodArgs{
		DeletePods: []*DeletePod{
			{
				PodID: podID,
				CCEID: clusterID,
			},
		},
		RelatedReleaseFlag: relatedReleaseFlag,
	}
}

func NewDeleteMultiplePodsArgs(pods []*DeletePod, relatedReleaseFlag bool) *DeletePodArgs {
	return &DeletePodArgs{
		DeletePods:         pods,
		RelatedReleaseFlag: relatedReleaseFlag,
	}
}

func (args *DeletePodArgs) validate() error {
	for _, pod := range args.DeletePods {
		if pod.PodID == "" {
			return errors.New("bci pod id cannot be empty")
		}
	}
	return nil
}

func (c *client) DeletePod(ctx context.Context, args *DeletePodArgs, opt *bce.SignOption) error {
	ctx = logger.EnsureRequestIDInCtx(ctx)
	if err := args.validate(); err != nil {
		return err
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("POST", c.GetURL("/api/logical/bci/v1/pod/delete", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, opt)
	if err != nil {
		return err
	}
	return nil
}
