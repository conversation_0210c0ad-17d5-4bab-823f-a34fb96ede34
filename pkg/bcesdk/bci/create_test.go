package bci

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"gotest.tools/assert"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/util"
)

var testPodCfg = &PodConfig{
	Name:            "testutils-pod-1",
	RestartPolicy:   RestartPolicyAlways,
	VPCID:           "vpc-cwtrgdc3cre4",
	VPCUUID:         "ed82e80f-8e2a-40e3-804c-96d5eb040a53",
	CCEID:           "c-f99UG69T",
	SubnetID:        "sbn-jsd8zeifut82",
	SubnetUUID:      "*************-4f95-bc19-4efb0e186119",
	SecurityGroupID: "g-x7uc1wktj0y5",
	ServiceType:     ServiceTypeBCI,
	PurchaseNum:     1,
	ProductType:     ProductTypePostPay,
	LogicalZone:     "gzns",
	Volumes: &Volumes{
		NFS: []VolumeNFS{
			{
				Name:     "testutils-nfs",
				Server:   "a.b.c",
				ReadOnly: false,
				Path:     "/",
			},
		},
	},
	Tags: []PodTag{},
	Containers: []Container{
		{
			Name:               "container-1",
			ContainerImageInfo: &ContainerImageInfo{},
			MemoryInGB:         1.0,
			CPUInCore:          0.5,
			WorkingDir:         "/home/<USER>",
			ImagePullPolicy:    PullAlways,
			Commands:           []string{"sleep", "10"},
			VolumeMounts: []VolumeMount{
				{
					MountPath: "/nfs",
					ReadOnly:  false,
					Name:      "testutils-nfs",
				},
			},
		},
	},
	ImageRegistrySecret: []ImageRegistrySecret{
		{
			Server:   "hub.baidubce.com",
			UserName: "username",
			Password: "password",
		},
	},
}

func TestCreatePod(t *testing.T) {
	type args struct {
		cfg    *PodConfig
		eipCfg *EIPConfig
		opt    *bce.SignOption
	}
	tests := []struct {
		name    string
		envs    []*testEnv
		args    args
		wantErr bool
	}{
		// All testutils cases.
		{
			name: "create pod without eip case",
			envs: []*testEnv{
				{
					method: "POST",
					path:   "/api/logical/bci/v1/pod/create",
					handler: func(w http.ResponseWriter, r *http.Request) {
						w.Header().Set("Content-Type", "application/json")
						body, err := ioutil.ReadAll(r.Body)
						if err != nil {
							w.WriteHeader(http.StatusBadRequest)
							_, _ = w.Write([]byte(err.Error()))
							return
						}
						args := new(CreatePodArgs)
						if err := json.Unmarshal(body, args); err != nil {
							w.WriteHeader(http.StatusBadRequest)
							_, _ = w.Write([]byte(err.Error()))
							return
						}
						if err := args.validate(); err != nil {
							w.WriteHeader(http.StatusBadRequest)
							_, _ = w.Write([]byte(err.Error()))
							return
						}
						if args.Items[0].Config.Type != ServiceTypeBCI {
							w.WriteHeader(http.StatusBadRequest)
							_, _ = w.Write([]byte(fmt.Sprintf("%+v", args.Items[0].Config)))
							return
						}
						podCfg := args.Items[0].Config.Pod
						// assert default application
						if podCfg.Application != DefaultApplication {
							w.WriteHeader(http.StatusBadRequest)
							_, _ = w.Write([]byte("unexpected application for item[0]"))
							return
						}
						w.WriteHeader(http.StatusOK)
						_, _ = w.Write([]byte(fmt.Sprintf(`{"orderId": "%s","podIds": ["%s"]}`, util.GetRequestID(), util.GenerateBCEShortID("p"))))
					},
				},
			},
			args: args{
				cfg: func() *PodConfig {
					c := *testPodCfg
					return &c
				}(),
			},
			wantErr: false,
		},
		{
			name: "non-empty application field case",
			envs: []*testEnv{
				{
					method: "POST",
					path:   "/api/logical/bci/v1/pod/create",
					handler: func(w http.ResponseWriter, r *http.Request) {
						w.Header().Set("Content-Type", "application/json")
						body, err := ioutil.ReadAll(r.Body)
						if err != nil {
							w.WriteHeader(http.StatusBadRequest)
							_, _ = w.Write([]byte(err.Error()))
							return
						}
						args := new(CreatePodArgs)
						if err := json.Unmarshal(body, args); err != nil {
							w.WriteHeader(http.StatusBadRequest)
							_, _ = w.Write([]byte(err.Error()))
							return
						}
						if err := args.validate(); err != nil {
							w.WriteHeader(http.StatusBadRequest)
							_, _ = w.Write([]byte(err.Error()))
							return
						}
						if args.Items[0].Config.Type != ServiceTypeBCI {
							w.WriteHeader(http.StatusBadRequest)
							_, _ = w.Write([]byte(fmt.Sprintf("%+v", args.Items[0].Config)))
							return
						}
						podCfg := args.Items[0].Config.Pod
						// assert default application
						if podCfg.Application != "pingo" {
							w.WriteHeader(http.StatusBadRequest)
							_, _ = w.Write([]byte("unexpected application for item[0]"))
							return
						}
						w.WriteHeader(http.StatusOK)
						_, _ = w.Write([]byte(fmt.Sprintf(`{"orderId": "%s","podIds": ["%s"]}`, util.GetRequestID(), util.GenerateBCEShortID("p"))))
					},
				},
			},
			args: args{
				cfg: func() *PodConfig {
					c := *testPodCfg
					c.Application = "pingo"
					return &c
				}(),
			},
			wantErr: false,
		},
		{
			name: "create pod with eip case",
			envs: []*testEnv{
				{
					method: "POST",
					path:   "/api/logical/bci/v1/pod/create",
					handler: func(w http.ResponseWriter, r *http.Request) {
						w.Header().Set("Content-Type", "application/json")
						body, err := ioutil.ReadAll(r.Body)
						if err != nil {
							w.WriteHeader(http.StatusBadRequest)
							_, _ = w.Write([]byte(err.Error()))
							return
						}
						args := new(CreatePodArgs)
						if err := json.Unmarshal(body, args); err != nil {
							w.WriteHeader(http.StatusBadRequest)
							_, _ = w.Write([]byte(err.Error()))
							return
						}
						if err := args.validate(); err != nil {
							w.WriteHeader(http.StatusBadRequest)
							_, _ = w.Write([]byte(err.Error()))
							return
						}
						if args.Items[0].Config.Type != ServiceTypeBCI {
							w.WriteHeader(http.StatusBadRequest)
							_, _ = w.Write([]byte(fmt.Sprintf("args.Items[0].Config=%+v", args.Items[0].Config)))
							return
						}
						podCfg := args.Items[0].Config.Pod
						// assert default application
						if podCfg.Application != DefaultApplication {
							w.WriteHeader(http.StatusBadRequest)
							_, _ = w.Write([]byte("unexpected application for item[0]"))
							return
						}
						if args.Items[1].Config.Type != ServiceTypeEIP {
							w.WriteHeader(http.StatusBadRequest)
							_, _ = w.Write([]byte(fmt.Sprintf("args.Items[0].Config=%+v", args.Items[1].Config)))
							return
						}
						eipCfg := args.Items[1].Config.EIP
						if eipCfg.Name != "testutils-eip" || eipCfg.Region != "gz" || eipCfg.BandwidthInMbps != 1000 {
							w.WriteHeader(http.StatusBadRequest)
							_, _ = w.Write([]byte(fmt.Sprintf("unexpected eip config for item[1]: %+v", eipCfg)))
							return
						}
						w.WriteHeader(http.StatusOK)
						_, _ = w.Write([]byte(fmt.Sprintf(`{"orderId": "%s","podIds": ["%s"]}`, util.GetRequestID(), util.GenerateBCEShortID("p"))))
					},
				},
			},
			args: args{
				cfg: func() *PodConfig {
					c := *testPodCfg
					return &c
				}(),
				eipCfg: NewEIPConfig(context.TODO(), "testutils-eip", "gz", "", 1000, "", false),
			},
			wantErr: false,
		},
		{
			name: "illegal pod config case",
			args: args{
				cfg: func() *PodConfig {
					c := *testPodCfg
					c.Name = ""
					return &c
				}(),
			},
			wantErr: true,
		},
		{
			name: "illegal eip config case",
			args: args{
				cfg: func() *PodConfig {
					c := *testPodCfg
					return &c
				}(),
				eipCfg: NewEIPConfig(context.TODO(), "testutils-eip-a-very-longgggggggggggggggggggggggggggggggggggggggggggggggggggggggg-name", "gz", "", 1000, "", false),
			},
			wantErr: true,
		},
		{
			name: "remote error case",
			envs: []*testEnv{
				{
					method: "POST",
					path:   "/api/logical/bci/v1/pod/create",
					handler: newHandler(http.StatusInternalServerError,
						[]byte(fmt.Sprintf(`{"code":"InternalError","message":"error occurs",requestId":"%s"}`, util.GetRequestID()))),
				},
			},
			args: args{
				cfg: func() *PodConfig {
					c := *testPodCfg
					return &c
				}(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := setupTestEnv(tt.envs)
			defer tearDownTestEnv(s)

			c := NewClient(NewConfig(&bce.Config{
				Credentials: new(bce.Credentials),
				Checksum:    true,
				Endpoint:    s.URL,
			}))
			c.SetDebug(true)

			ctx, cancel := context.WithTimeout(context.TODO(), 10*time.Second)
			defer cancel()

			got, err := c.CreatePod(ctx, tt.args.cfg, tt.args.eipCfg, tt.args.opt)
			if (err != nil) != tt.wantErr {
				t.Errorf("client.CreatePod() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && (len(got.PodIDs) == 0 || got.OrderID == "") {
				t.Errorf("client.CreatePod() = %v, want non-empty ids", got)
			}
		})
	}
}

func Test_generateVolumeTypes(t *testing.T) {
	testPodConfig := PodConfig{
		Name: "testutils-pod",
		Volumes: &Volumes{
			EmptyDir: []VolumeEmptyDir{
				{
					Name: "volume-EmptyDir",
				},
			},
			NFS: []VolumeNFS{
				{
					Name:   "volume-NFS",
					Server: "a.b.c.com",
					Path:   "/ddeeff",
				},
			},
		},
		Containers: []Container{
			{
				Name: "testutils-container",
				VolumeMounts: []VolumeMount{
					{
						Name:      "volume-EmptyDir",
						MountPath: "/emptyDir",
					},
					{
						Name:      "volume-NFS",
						MountPath: "/nfs",
					},
				},
			},
		},
	}
	wantPodConfig := testPodConfig
	wantPodConfig.Containers[0].VolumeMounts[0].Type = VolumeTypeEmptyDir
	wantPodConfig.Containers[0].VolumeMounts[1].Type = VolumeTypeNFS

	badPodConfig := PodConfig{
		Name: "testutils-pod",
		Volumes: &Volumes{
			EmptyDir: []VolumeEmptyDir{
				{
					Name: "volume-EmptyDir",
				},
			},
			NFS: []VolumeNFS{
				{
					Name:   "volume-NFS",
					Server: "a.b.c.com",
					Path:   "/ddeeff",
				},
			},
		},
		Containers: []Container{
			{
				Name: "testutils-container",
				VolumeMounts: []VolumeMount{
					{
						Name:      "volume-EmptyDir",
						MountPath: "/emptyDir",
					},
					{
						Name:      "volume-ConfigFile",
						MountPath: "/configfiles",
					},
				},
			},
		},
	}
	type args struct {
		cfg *PodConfig
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		wantCfg *PodConfig
	}{
		// All testutils cases.
		{
			name: "normal case",
			args: args{
				cfg: &testPodConfig,
			},
			wantErr: false,
			wantCfg: &wantPodConfig,
		},
		{
			name: "volume not found case",
			args: args{
				cfg: &badPodConfig,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := generateVolumeTypes(tt.args.cfg)
			if (err != nil) != tt.wantErr {
				t.Errorf("generateVolumeTypes() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !cmp.Equal(tt.args.cfg, tt.wantCfg) {
				t.Errorf("generateVolumeTypes() got = %v, want %v, diff is %s", tt.args.cfg, tt.wantCfg, cmp.Diff(tt.args.cfg, tt.wantCfg))
			}
		})
	}
}

func TestPodOrEIPConfigJSON(t *testing.T) {
	// PodConfig
	cfg := &PodOrEIPConfig{
		Pod: &PodConfig{
			ServiceType: ServiceTypeBCI,
			Name:        "testutils-pod",
		},
		Type: ServiceTypeBCI,
	}
	out, err := json.Marshal(cfg)
	assert.NilError(t, err)
	//assert.Equal(t, string(out),
	//	`{"name":"testutils-pod","restartPolicy":"","cceId":"","securityGroupId":"","serviceType":"BCI","purchaseNum":0,"productType":"",`+
	//		`"volumes":null,"application":"","containers":null,"imageRegistrySecret":null}`)

	newCfg := new(PodOrEIPConfig)
	err = json.Unmarshal(out, newCfg)
	assert.NilError(t, err)
	assert.DeepEqual(t, cfg, newCfg)

	// EIP Config
	cfg = &PodOrEIPConfig{
		EIP:  NewEIPConfig(context.TODO(), "testutils-eip", "bj", "", 10, "", false),
		Type: ServiceTypeEIP,
	}
	out, err = json.Marshal(cfg)
	assert.NilError(t, err)
	assert.Equal(t, string(out),
		`{"name":"testutils-eip","region":"bj","serviceType":"EIP","bandwidthInMbps":10,"purchaseNum":1,"subProductType":"netraffic","productType":"postpay"}`)

	newCfg = new(PodOrEIPConfig)
	err = json.Unmarshal(out, newCfg)
	assert.NilError(t, err)
	assert.DeepEqual(t, cfg, newCfg)
}

func Test_validateLogCollections(t *testing.T) {
	type args struct {
		containers []Container
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// All testutils cases.
		{
			name: "unique index case",
			args: args{
				containers: []Container{
					{
						Name: "container-0",
						ContainerImageInfo: &ContainerImageInfo{
							ImageAddress: "mysql",
							ImageName:    "mysql",
							ImageVersion: "5.7",
						},
						LogCollections: []*LogCollection{
							{
								Name:    "config-1",
								SrcType: "internal",
								Index:   1,
								SrcConfig: SrcConfig{
									SrcDir:         "/var/log-1",
									MatchedPattern: "^*.log$",
									TTL:            4,
								},
								DestConfig: DestConfig{
									DestType:  "BLS",
									LogStore:  "logstore-1",
									RateLimit: 50,
								},
							},
						},
					},
					{
						Name: "container-1",
						ContainerImageInfo: &ContainerImageInfo{
							ImageAddress: "nginx",
							ImageName:    "nginx",
							ImageVersion: "latest",
						},
						LogCollections: []*LogCollection{
							{
								Name:    "config-stdout",
								SrcType: "stdout",
								Index:   2,
								SrcConfig: SrcConfig{
									SrcDir:         "stdout",
									MatchedPattern: "^.*$",
									TTL:            5,
								},
								DestConfig: DestConfig{
									DestType:  "BLS",
									LogStore:  "logstore-stdout",
									RateLimit: 20,
								},
							},
						},
					},
				},
			},
		},
		{
			name: "duplicate index case",
			args: args{
				containers: []Container{
					{
						Name: "container-0",
						ContainerImageInfo: &ContainerImageInfo{
							ImageAddress: "mysql",
							ImageName:    "mysql",
							ImageVersion: "5.7",
						},
						LogCollections: []*LogCollection{
							{
								Name:    "config-1",
								SrcType: "internal",
								Index:   1,
								SrcConfig: SrcConfig{
									SrcDir:         "/var/log-1",
									MatchedPattern: "^*.log$",
									TTL:            4,
								},
								DestConfig: DestConfig{
									DestType:  "BLS",
									LogStore:  "logstore-1",
									RateLimit: 50,
								},
							},
						},
					},
					{
						Name: "container-1",
						ContainerImageInfo: &ContainerImageInfo{
							ImageAddress: "nginx",
							ImageName:    "nginx",
							ImageVersion: "latest",
						},
						LogCollections: []*LogCollection{
							{
								Name:    "config-stdout",
								SrcType: "stdout",
								Index:   1,
								SrcConfig: SrcConfig{
									SrcDir:         "stdout",
									MatchedPattern: "^.*$",
									TTL:            5,
								},
								DestConfig: DestConfig{
									DestType:  "BLS",
									LogStore:  "logstore-stdout",
									RateLimit: 20,
								},
							},
						},
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := validateLogCollections(tt.args.containers); (err != nil) != tt.wantErr {
				t.Errorf("validateLogCollections() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
