// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci (interfaces: Client)

// Package bci is a generated GoMock package.
package bci

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// CreatePod mocks base method.
func (m *MockClient) CreatePod(arg0 context.Context, arg1 *PodConfig, arg2 *EIPConfig, arg3 *bce.SignOption) (*CreatePodResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePod", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*CreatePodResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePod indicates an expected call of CreatePod.
func (mr *MockClientMockRecorder) CreatePod(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePod", reflect.TypeOf((*MockClient)(nil).CreatePod), arg0, arg1, arg2, arg3)
}

// CreatePullTask mocks base method.
func (m *MockClient) CreatePullTask(arg0 context.Context, arg1 *PullTaskArgs, arg2 *bce.SignOption) (*CreatePullTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePullTask", arg0, arg1, arg2)
	ret0, _ := ret[0].(*CreatePullTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePullTask indicates an expected call of CreatePullTask.
func (mr *MockClientMockRecorder) CreatePullTask(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePullTask", reflect.TypeOf((*MockClient)(nil).CreatePullTask), arg0, arg1, arg2)
}

// DeletePod mocks base method.
func (m *MockClient) DeletePod(arg0 context.Context, arg1 *DeletePodArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePod", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePod indicates an expected call of DeletePod.
func (mr *MockClientMockRecorder) DeletePod(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePod", reflect.TypeOf((*MockClient)(nil).DeletePod), arg0, arg1, arg2)
}

// DescribeBatchPodForLight mocks base method.
func (m *MockClient) DescribeBatchPodForLight(arg0 context.Context, arg1 []string, arg2 *bce.SignOption) ([]*DescribePodResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeBatchPodForLight", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*DescribePodResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeBatchPodForLight indicates an expected call of DescribeBatchPodForLight.
func (mr *MockClientMockRecorder) DescribeBatchPodForLight(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeBatchPodForLight", reflect.TypeOf((*MockClient)(nil).DescribeBatchPodForLight), arg0, arg1, arg2)
}

// DescribePod mocks base method.
func (m *MockClient) DescribePod(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*DescribePodResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribePod", arg0, arg1, arg2)
	ret0, _ := ret[0].(*DescribePodResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribePod indicates an expected call of DescribePod.
func (mr *MockClientMockRecorder) DescribePod(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribePod", reflect.TypeOf((*MockClient)(nil).DescribePod), arg0, arg1, arg2)
}

// DescribePodWithDeleted mocks base method.
func (m *MockClient) DescribePodWithDeleted(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*DescribePodResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribePodWithDeleted", arg0, arg1, arg2)
	ret0, _ := ret[0].(*DescribePodResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribePodWithDeleted indicates an expected call of DescribePodWithDeleted.
func (mr *MockClientMockRecorder) DescribePodWithDeleted(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribePodWithDeleted", reflect.TypeOf((*MockClient)(nil).DescribePodWithDeleted), arg0, arg1, arg2)
}

// GetContainerLog mocks base method.
func (m *MockClient) GetContainerLog(arg0 context.Context, arg1, arg2 string, arg3 *LogOptions, arg4 *bce.SignOption) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContainerLog", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContainerLog indicates an expected call of GetContainerLog.
func (mr *MockClientMockRecorder) GetContainerLog(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContainerLog", reflect.TypeOf((*MockClient)(nil).GetContainerLog), arg0, arg1, arg2, arg3, arg4)
}

// GetPodQuota mocks base method.
func (m *MockClient) GetPodQuota(arg0 context.Context, arg1 *bce.SignOption) (*PodQuotaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPodQuota", arg0, arg1)
	ret0, _ := ret[0].(*PodQuotaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPodQuota indicates an expected call of GetPodQuota.
func (mr *MockClientMockRecorder) GetPodQuota(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPodQuota", reflect.TypeOf((*MockClient)(nil).GetPodQuota), arg0, arg1)
}

// GetUserVersion mocks base method.
func (m *MockClient) GetUserVersion(arg0 context.Context, arg1 *bce.SignOption) (*UserVersionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserVersion", arg0, arg1)
	ret0, _ := ret[0].(*UserVersionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserVersion indicates an expected call of GetUserVersion.
func (mr *MockClientMockRecorder) GetUserVersion(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserVersion", reflect.TypeOf((*MockClient)(nil).GetUserVersion), arg0, arg1)
}

// LaunchExecWSSUrl mocks base method.
func (m *MockClient) LaunchExecWSSUrl(arg0 context.Context, arg1 *LaunchExecWSSUrlArgs, arg2 *bce.SignOption) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LaunchExecWSSUrl", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LaunchExecWSSUrl indicates an expected call of LaunchExecWSSUrl.
func (mr *MockClientMockRecorder) LaunchExecWSSUrl(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LaunchExecWSSUrl", reflect.TypeOf((*MockClient)(nil).LaunchExecWSSUrl), arg0, arg1, arg2)
}

// ListPods mocks base method.
func (m *MockClient) ListPods(arg0 context.Context, arg1 *ListOption, arg2 *bce.SignOption) (*ListPodsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPods", arg0, arg1, arg2)
	ret0, _ := ret[0].(*ListPodsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPods indicates an expected call of ListPods.
func (mr *MockClientMockRecorder) ListPods(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPods", reflect.TypeOf((*MockClient)(nil).ListPods), arg0, arg1, arg2)
}

// ListPodsForLight mocks base method.
func (m *MockClient) ListPodsForLight(arg0 context.Context, arg1 *ListOption, arg2 *bce.SignOption) (*ListPodsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPodsForLight", arg0, arg1, arg2)
	ret0, _ := ret[0].(*ListPodsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPodsForLight indicates an expected call of ListPodsForLight.
func (mr *MockClientMockRecorder) ListPodsForLight(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPodsForLight", reflect.TypeOf((*MockClient)(nil).ListPodsForLight), arg0, arg1, arg2)
}

// ListPodsMetrics mocks base method.
func (m *MockClient) ListPodsMetrics(arg0 context.Context, arg1 []string, arg2 *bce.SignOption) (*ListPodsMetricsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPodsMetrics", arg0, arg1, arg2)
	ret0, _ := ret[0].(*ListPodsMetricsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPodsMetrics indicates an expected call of ListPodsMetrics.
func (mr *MockClientMockRecorder) ListPodsMetrics(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPodsMetrics", reflect.TypeOf((*MockClient)(nil).ListPodsMetrics), arg0, arg1, arg2)
}

// ListPodsSummary mocks base method.
func (m *MockClient) ListPodsSummary(arg0 context.Context, arg1 []string, arg2 *bce.SignOption) (*ListPodsMetricsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPodsSummary", arg0, arg1, arg2)
	ret0, _ := ret[0].(*ListPodsMetricsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPodsSummary indicates an expected call of ListPodsSummary.
func (mr *MockClientMockRecorder) ListPodsSummary(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPodsSummary", reflect.TypeOf((*MockClient)(nil).ListPodsSummary), arg0, arg1, arg2)
}

// QueryPullTask mocks base method.
func (m *MockClient) QueryPullTask(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*QueryPullTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryPullTask", arg0, arg1, arg2)
	ret0, _ := ret[0].(*QueryPullTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryPullTask indicates an expected call of QueryPullTask.
func (mr *MockClientMockRecorder) QueryPullTask(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryPullTask", reflect.TypeOf((*MockClient)(nil).QueryPullTask), arg0, arg1, arg2)
}

// ReportPods mocks base method.
func (m *MockClient) ReportPods(arg0 context.Context, arg1 map[string][]*ClusterInfo, arg2 string, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportPods", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReportPods indicates an expected call of ReportPods.
func (mr *MockClientMockRecorder) ReportPods(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportPods", reflect.TypeOf((*MockClient)(nil).ReportPods), arg0, arg1, arg2, arg3)
}

// SetDebug mocks base method.
func (m *MockClient) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug.
func (mr *MockClientMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockClient)(nil).SetDebug), arg0)
}

// UpdateConfigMap mocks base method.
func (m *MockClient) UpdateConfigMap(arg0 context.Context, arg1 string, arg2 *VolumeConfigFile, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateConfigMap", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateConfigMap indicates an expected call of UpdateConfigMap.
func (mr *MockClientMockRecorder) UpdateConfigMap(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateConfigMap", reflect.TypeOf((*MockClient)(nil).UpdateConfigMap), arg0, arg1, arg2, arg3)
}

// UpdateDsContainers mocks base method.
func (m *MockClient) UpdateDsContainers(arg0 context.Context, arg1 *InjectDsContainersRequest, arg2 string, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDsContainers", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDsContainers indicates an expected call of UpdateDsContainers.
func (mr *MockClientMockRecorder) UpdateDsContainers(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDsContainers", reflect.TypeOf((*MockClient)(nil).UpdateDsContainers), arg0, arg1, arg2, arg3)
}

// UpdateServiceAccountToken mocks base method.
func (m *MockClient) UpdateServiceAccountToken(arg0 context.Context, arg1 string, arg2 *VolumeConfigFile, arg3 map[string]string, arg4 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateServiceAccountToken", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateServiceAccountToken indicates an expected call of UpdateServiceAccountToken.
func (mr *MockClientMockRecorder) UpdateServiceAccountToken(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateServiceAccountToken", reflect.TypeOf((*MockClient)(nil).UpdateServiceAccountToken), arg0, arg1, arg2, arg3, arg4)
}
