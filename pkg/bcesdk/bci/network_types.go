package bci

import (
	"time"
)

type SecurityGroup struct {
	ID              string `json:"id"`
	SecurityGroupID string `json:"securityGroupId"`
	UUID            string `json:"uuid"`
	Name            string `json:"name"`
	Description     string `json:"description"`
	TenantID        string `json:"tenantId"`
	AssociateNum    int    `json:"associateNum"`
	VPCID           string `json:"vpcId"`
	VPCShortID      string `json:"vpcShortId"`
	Creator         string `json:"creator"`
}

type VPC struct {
	VPCID            string    `json:"vpcId"`
	ShortID          string    `json:"shortId"`
	Name             string    `json:"name"`
	CIDR             string    `json:"cidr"`
	Status           int       `json:"status"`
	SecurityGroupNum int       `json:"securityGroupNum"`
	SubnetNum        int       `json:"subnetNum"`
	CreateTime       time.Time `json:"createTime"`
	Description      string    `json:"description"`
	DefaultVPC       bool      `json:"defaultVpc"`
}

type Subnet struct {
	Name        string    `json:"name"`
	SubnetID    string    `json:"subnetId"`
	AZ          string    `json:"az"`
	CIDR        string    `json:"cidr"`
	VPCID       string    `json:"vpcId"`
	VPCShortID  string    `json:"vpcShortId"`
	SubnetUUID  string    `json:"subnetUuid"`
	AccountID   string    `json:"accountId"`
	SubnetType  int       `json:"subnetType"`
	Type        int       `json:"type"`
	CreatedTime time.Time `json:"createdTime"`
	UpdatedTime time.Time `json:"updatedTime"`
	Description string    `json:"description"`
	ShortID     string    `json:"shortId"`
	UsedIPs     int       `json:"usedIps"`
	TotalIPs    int       `json:"totalIps"`
}
