package bci

import (
	"time"

	corev1 "k8s.io/api/core/v1"
)

type Volumes struct {
	NFS         []VolumeNFS        `json:"nfs"`
	CephFS      []VolumeCephFS     `json:"cephfs"`
	EmptyDir    []VolumeEmptyDir   `json:"emptyDir"`
	ConfigFile  []VolumeConfigFile `json:"configFile"`
	FlexVolume  []VolumeFlexVolume `json:"flexVolume,omitempty"`
	PFS         []VolumePFS        `json:"pfs,omitempty"`
	HostPath    []VolumeHostPath   `json:"hostPath,omitempty"`
	BOS         []VolumeBOS        `json:"bos,omitempty"`
	TokenExtras map[string]string  `json:"-"`
}

type VolumeBOS struct {
	Name      string `json:"name"`
	Bucket    string `json:"bucket"`
	URL       string `json:"url"`
	OtherOpts string `json:"otherOpts"`
	AccessKey string `json:"ak"`
	SecretKey string `json:"sk"`
	ReadyOnly bool   `json:"readyOnly"`
}

type VolumeNFS struct {
	Name     string `json:"name"`
	Server   string `json:"server"`
	ReadOnly bool   `json:"readOnly"`
	Path     string `json:"path"`
	DsVolume bool   `json:"dsVolume,omitempty"`
}

type VolumeCephFS struct {
	Name     string   `json:"name"`
	Monitors []string `json:"monitors"`
	Path     string   `json:"path"`
	User     string   `json:"user"`
	// serverless 不支持在节点上读取SecretFile
	// SecretFile string                `json:"secretFile"`
	SecretRef *corev1.LocalObjectReference `json:"secretRef"`
	ReadOnly  bool                         `json:"readOnly"`
}

type VolumeEmptyDir struct {
	Name         string        `json:"name"`
	Medium       StorageMedium `json:"medium,omitempty"`
	SizeLimitGiB float64       `json:"sizeLimit,omitempty"`
	DsVolume     bool          `json:"dsVolume,omitempty"`
}

type VolumeHostPath struct {
	Name string `json:"name"`
	// path of the directory on the host.
	// If the path is a symlink, it will follow the link to the real path.
	// More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath
	Path string `json:"path"`
	// type for HostPath Volume
	// Defaults to ""
	// More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath
	// +optional
	Type     *corev1.HostPathType `json:"type,omitempty" protobuf:"bytes,2,opt,name=type"`
	DsVolume bool                 `json:"dsVolume,omitempty"`
}

type StorageMedium string

const (
	StorageMediumDefault         StorageMedium = ""
	StorageMediumMemory          StorageMedium = "Memory"
	StorageMediumHugePages       StorageMedium = "HugePages"
	StorageMediumHugePagesPrefix StorageMedium = "HugePages-"
)

type VolumeConfigFile struct {
	Name        string       `json:"name"`
	ConfigFiles []ConfigFile `json:"configFiles"`
	DefaultMode *int32       `json:"defaultMode,omitempty"`
	DsVolume    bool         `json:"dsVolume,omitempty"`
}

type VolumeFlexVolume struct {
	Name   string `json:"name"`
	Driver string `json:"driver"`
}

type ConfigFile struct {
	Path string `json:"path"`
	File string `json:"file"`
}

type VolumePFS struct {
	Name   string `json:"name"`
	Server string `json:"server"`
	Path   string `json:"path"`
}

type VolumeType string

const (
	VolumeTypeNFS        VolumeType = "NFS"
	VolumeTypeEmptyDir   VolumeType = "EmptyDir"
	VolumeTypeConfigFile VolumeType = "ConfigFile"
	VolumeTypeFlexVolume VolumeType = "FlexVolume"
	VolumeTypePFS        VolumeType = "PFS"
	VolumeTypeHostPath   VolumeType = "HostPath"
	VolumeTypeBOS        VolumeType = "BOS"
	VolumeTypeCephFS     VolumeType = "CephFS"
)

type VolumeMount struct {
	MountPath   string     `json:"mountPath"`
	ReadOnly    bool       `json:"readOnly"`
	Name        string     `json:"name"`
	Type        VolumeType `json:"type"`
	SubPath     string     `json:"subPath"`
	SubPathExpr string     `json:"subPathExpr"`
}

type ContainerPort struct {
	Port     int32                    `json:"port"`
	Protocol ContainerNetworkProtocol `json:"protocol"`
	Name     string                   `json:"name,omitempty"`
}

type ContainerNetworkProtocol string

const (
	ContainerNetworkProtocolUDP ContainerNetworkProtocol = "UDP"
	ContainerNetworkProtocolTCP ContainerNetworkProtocol = "TCP"
)

type Env struct {
	Key   string `json:"key"`
	Value string `json:"value"`

	ValueFrom *corev1.EnvVarSource `json:"valueFrom,omitempty"`
}

// PullPolicy describes a policy for if/when to pull a container image
type PullPolicy string

const (
	PullAlways       PullPolicy = "Always"
	PullNever        PullPolicy = "Never"
	PullIfNotPresent PullPolicy = "IfNotPresent"
)

type ContainerImageInfo struct {
	ImageAddress string `json:"imageAddress"`
	ImageName    string `json:"imageName"`
	ImageVersion string `json:"imageVersion"`
}

type Container struct {
	Name                string `json:"name" valid:"Required"`
	*ContainerImageInfo `valid:"Required"`
	EphemeralStorageGB  float64                 `json:"ephemeral-storage,omitempty"`
	MemoryInGB          float64                 `json:"memory"`
	CPUInCore           float64                 `json:"cpu"`
	GPUType             string                  `json:"gpuType,omitempty"`
	GPUCount            float64                 `json:"gpuCount,omitempty"`
	WorkingDir          string                  `json:"workingDir"`
	ImagePullPolicy     PullPolicy              `json:"imagePullPolicy"`
	Commands            []string                `json:"commands"`
	Args                []string                `json:"args"`
	VolumeMounts        []VolumeMount           `json:"volumeMounts"`
	Ports               []ContainerPort         `json:"ports"`
	Envs                []Env                   `json:"envs"`
	Status              *ContainerStatus        `json:"status,omitempty"`
	UserID              string                  `json:"userId,omitempty"`
	ContainerUUID       string                  `json:"containerUuid,omitempty"`
	ImageID             string                  `json:"imageID,omitempty"`
	CreatedTime         time.Time               `json:"createdTime,omitempty"`
	UpdatedTime         time.Time               `json:"updatedTime,omitempty"`
	DeletedTime         time.Time               `json:"deletedTime,omitempty"`
	ContainerType       ContainerType           `json:"containerType,omitempty"`
	LivenessProbe       *corev1.Probe           `json:"livenessProbe,omitempty"`
	ReadinessProbe      *corev1.Probe           `json:"readinessProbe,omitempty"`
	StartupProbe        *corev1.Probe           `json:"startupProbe,omitempty"`
	Stdin               bool                    `json:"stdin,omitempty"`
	StdinOnce           bool                    `json:"stdinOnce,omitempty"`
	TTY                 bool                    `json:"tty,omitempty"`
	Lifecycle           *corev1.Lifecycle       `json:"lifecycle,omitempty"`
	SecurityContext     *corev1.SecurityContext `json:"securityContext,omitempty"`
	LogCollections      []*LogCollection        `json:"logCollections,omitempty"`
}

type LogCollection struct {
	Name       string     `json:"name,omitempty" valid:"Required"`
	SrcConfig  SrcConfig  `json:"srcConfig,omitempty" valid:"Required"`
	DestConfig DestConfig `json:"destConfig,omitempty" valid:"Required"`
	// Fields for internal logic, ignored in json.
	SrcType string `json:"-" valid:"Required"`
	Index   int    `json:"-" valid:"Required"`
}

type SrcConfig struct {
	SrcDir         string `json:"srcDir,omitempty" valid:"Required"`
	MatchedPattern string `json:"matchedPattern,omitempty" valid:"Required"`
	TTL            int    `json:"ttl,omitempty" valid:"Required"`
}

type DestConfig struct {
	DestType  string `json:"destType,omitempty" valid:"Required"`
	LogStore  string `json:"logStore,omitempty" valid:"Required"`
	RateLimit int    `json:"rateLimit,omitempty" valid:"Required"`
}

type ContainerType string

const (
	ContainerTypeInit     ContainerType = "init"
	ContainerTypeWorkload ContainerType = "workload"
)

type ContainerStatus struct {
	PreviousState *ContainerState `json:"previousState"`
	CurrentState  *ContainerState `json:"currentState"`
	RestartCount  int32           `json:"restartCount"`

	// Added in v2 for probe capability.
	Started *bool `json:"started,omitempty"`
	Ready   *bool `json:"ready,omitempty"` // Use pointer here to be compatible with v1.
}

type ContainerState struct {
	State               ContainerStateString `json:"state,omitempty" description:"Pending/Creating/Running/Succeeded/Failed"`
	ContainerStartTime  time.Time            `json:"containerStartTime,omitempty"`
	ExitCode            int32                `json:"exitCode,omitempty"`
	ContainerFinishTime time.Time            `json:"containerFinishTime,omitempty"`
	DetailStatus        string               `json:"detailStatus,omitempty" description:"detailed message"`
}

type ContainerStateString string

const (
	ContainerStateStringPending   ContainerStateString = "Pending"
	ContainerStateStringCreating  ContainerStateString = "Creating"
	ContainerStateStringRunning   ContainerStateString = "Running"
	ContainerStateStringSucceeded ContainerStateString = "Succeeded"
	ContainerStateStringFailed    ContainerStateString = "Failed"
)

type ImageRegistrySecret struct {
	Server   string `json:"server"`
	UserName string `json:"userName"`
	Password string `json:"password"`
}

type ServiceType string

const (
	ServiceTypeBCI ServiceType = "BCI"
	ServiceTypeEIP ServiceType = "EIP"
)

type ProductType string

const (
	ProductTypePostPay ProductType = "PostPay"
	ProductTypeBidding ProductType = "bidding"
)

type EIPProductType string

const (
	EIPProductTypePostPay = "postpay"

	V2EIPProductTypePostPay = "Postpaid"
	V2EIPProductTypePrePay  = "Prepaid"
)

type SubProductType string

const (
	SubProductTypeNetraffic SubProductType = "netraffic"

	V2SubProductTypeByTraffic   SubProductType = "ByTraffic"   // 按照流量计费
	V2SubProductTypeByBandwidth SubProductType = "ByBandwidth" // 按照带宽计费
)

const (
	BidModelMarketPrice string = "MARKET_PRICE_BID"
	BidModelCustom      string = "CUSTOM_BID"
)

type BidOption struct {
	BidModel string  `json:"bidModel"`
	BidPrice float64 `json:"bidPrice,omitempty"`
}

type PodConfig struct {
	Name                          string                             `json:"name" valid:"Required"`
	RestartPolicy                 PodRestartPolicy                   `json:"restartPolicy" valid:"Required"`
	VPCID                         string                             `json:"vpcId,omitempty"`
	VPCUUID                       string                             `json:"vpcUuid,omitempty"`
	CCEID                         string                             `json:"cceId"`
	SubnetID                      string                             `json:"subnetId,omitempty"`
	SubnetUUID                    string                             `json:"subnetUuid,omitempty"`
	SecurityGroupID               string                             `json:"securityGroupId"`
	SubnetIDs                     string                             `json:"subnetIds,omitempty"`
	ServiceType                   ServiceType                        `json:"serviceType"`
	PurchaseNum                   int                                `json:"purchaseNum" valid:"Min(1)"`
	ProductType                   ProductType                        `json:"productType" valid:"Required"`
	BidOption                     *BidOption                         `json:"bidOption,omitempty"`
	LogicalZone                   string                             `json:"logicalZone,omitempty"`
	Volumes                       *Volumes                           `json:"volumes"`
	Tags                          []PodTag                           `json:"tags,omitempty"`
	Labels                        []PodLabel                         `json:"labels,omitempty"`
	MetadataLabels                []PodLabel                         `json:"metadataLabels,omitempty"`
	Application                   string                             `json:"application" valid:"Required"`
	Containers                    []Container                        `json:"containers" valid:"Required;MinSize(1)"`
	ImageRegistrySecret           []ImageRegistrySecret              `json:"imageRegistrySecret"`
	Annotations                   string                             `json:"annotations,omitempty"`
	EnableLog                     bool                               `json:"enableLog,omitempty"`
	ClientToken                   string                             `json:"-"`
	EIPIP                         string                             `json:"eipIp,omitempty"`
	CPUType                       string                             `json:"cpuType,omitempty"`
	Affinity                      *corev1.Affinity                   `json:"affinity,omitempty"`
	TerminationGracePeriodSeconds *int64                             `json:"terminationGracePeriodSeconds,omitempty"`
	DelayReleaseDurationMinute    int                                `json:"delayReleaseDurationMinute,omitempty"`
	DelayReleaseSucceeded         bool                               `json:"delayReleaseSucceeded,omitempty"`
	Hostname                      string                             `json:"hostname,omitempty"`
	SecurityContext               *corev1.PodSecurityContext         `json:"securityContext,omitempty"`
	IsTidal                       bool                               `json:"isTidal,omitempty"`
	DNSConfig                     *corev1.PodDNSConfig               `json:"dnsConfig,omitempty"`
	Extras                        map[string]string                  `json:"extras,omitempty"`
	AutoMatchImageCache           bool                               `json:"autoMatchImageCache"`
	TopologySpreadConstraints     []*corev1.TopologySpreadConstraint `json:"topologySpreadConstraints,omitempty"`
	EnableIPv6                    bool                               `json:"enableIPv6,omitempty"`
}

type EIPConfig struct {
	Name            string         `json:"name" valid:"Required;MaxSize(65)"`
	Region          string         `json:"region" valid:"Required"`
	ServiceType     ServiceType    `json:"serviceType" valid:"Required"`
	BandwidthInMbps int            `json:"bandwidthInMbps" valid:"Min(1)"`
	PurchaseNum     int            `json:"purchaseNum" valid:"Min(1)"`
	PurchaseLength  int            `json:"purchaseLength,omitempty"`
	SubProductType  SubProductType `json:"subProductType" valid:"Required"`
	ProductType     EIPProductType `json:"productType" valid:"Required"`
	// Added in v2.
	RouteType string `json:"routeType,omitempty"`
}

type Pod struct {
	Name          string           `json:"name"`
	PodID         string           `json:"podId"`
	PodUUID       string           `json:"podUuid"`
	Status        PodStatus        `json:"status"`
	VCPU          float64          `json:"vCpu"`
	MemoryInGB    float64          `json:"memory"`
	CCEID         string           `json:"cceUuid"`
	InternalIP    string           `json:"internalIp"`
	InternalIPv6  string           `json:"internalIPv6,omitempty"`
	RestartPolicy PodRestartPolicy `json:"restartPolicy"`
	OrderID       string           `json:"orderId"`
	CreatedTime   time.Time        `json:"createdTime"`
	UpdatedTime   time.Time        `json:"updatedTime"`
	DeletedTime   time.Time        `json:"deletedTime"`
	Description   string           `json:"description"`
	Region        string           `json:"region"`
	LogicalZone   string           `json:"logicalZone,omitempty"`
	UserID        string           `json:"userId"`
	ResourceUUID  string           `json:"resourceUuid"`
	TaskStatus    string           `json:"taskStatus"`
	Tags          []PodTag         `json:"tags"`
	Labels        []PodLabel       `json:"labels"`
	SubnetUUID    string           `json:"subnetUuid"`
	*Volumes
	BoundEIPConfig
	// only set for v2 bci pod
	V2         bool                  `json:"v2,omitempty"`
	Conditions []corev1.PodCondition `json:"conditions,omitempty"`
	CPUType    string                `json:"cpuType,omitempty"`
	Extras     map[string]string     `json:"extras,omitempty"`
}

// Config for a BCI bound EIP.
type BoundEIPConfig struct {
	EIPID              string `json:"eipId,omitempty"`
	EIPUUID            string `json:"eipUuid,omitempty"`
	PublicIP           string `json:"publicIp,omitempty"`
	BandwidthInMbps    int64  `json:"bandwidthInMbps,omitempty"`
	EIPRouteType       string `json:"eipRouteType,omitempty"`
	EIPPayMethod       string `json:"eipPayMethod,omitempty"`
	EIPIsUserSpecified bool   `json:"eipIsUserSpecified,omitempty"`
}

type PodStatus string

const (
	PodStatusRunning   PodStatus = "Running"
	PodStatusPending   PodStatus = "Pending"
	PodStatusSucceeded PodStatus = "Succeeded"
	PodStatusFailed    PodStatus = "Failed"
	PodStatusCrashed   PodStatus = "Crashed"
	PodStatusUnknown   PodStatus = "Unknown"
	// Statuses only present for bidding instance.
	PodStatusBidding  PodStatus = "Bidding"
	PodStatusRecycled PodStatus = "Recycled"
)

type PodRestartPolicy string

const (
	RestartPolicyAlways    PodRestartPolicy = "Always"
	RestartPolicyOnFailure PodRestartPolicy = "OnFailure"
	RestartPolicyNever     PodRestartPolicy = "Never"
)

type PodTag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

type PodLabel struct {
	LabelKey   string `json:"labelKey"`
	LabelValue string `json:"labelValue"`
}

const DefaultApplication string = "default"
