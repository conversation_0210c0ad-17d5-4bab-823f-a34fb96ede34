package v2

import (
	"context"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	bciv1 "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
)

func (c *client) ListPods(ctx context.Context, opt *bciv1.ListOption, signOpt *bce.SignOption) (*bciv1.ListPodsResponse, error) {
	return c.v1Client.ListPods(ctx, opt, signOpt)
}

func (c *client) ListPodsForLight(ctx context.Context, opt *bciv1.ListOption, signOpt *bce.SignOption) (*bciv1.ListPodsResponse, error) {
	return c.v1Client.ListPodsForLight(ctx, opt, signOpt)
}
