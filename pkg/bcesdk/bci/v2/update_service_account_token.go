package v2

import (
	"context"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
)

type UpdateServiceAccountTokenRequest struct {
	ConfigFileInfo *bci.VolumeConfigFile `json:"configFile"`
	Extras         map[string]string     `json:"extras"`
}

func (c *client) UpdateServiceAccountToken(ctx context.Context, podID string, configFile *bci.VolumeConfigFile, extras map[string]string,
	signOpt *bce.SignOption) error {
	return c.UpdateConfigFile(ctx, podID, "", configFile, extras, signOpt)
}
