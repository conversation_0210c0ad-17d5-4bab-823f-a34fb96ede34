package v2

import (
	"net/http"
	"net/http/httptest"

	"github.com/gorilla/mux"
)

type testEnv struct {
	method  string
	path    string
	handler func(w http.ResponseWriter, r *http.Request)
}

func setupTestEnv(envs []*testEnv) *httptest.Server {
	r := mux.NewRouter()
	for _, env := range envs {
		r.<PERSON>le<PERSON>(env.path, env.handler).Methods(env.method)
	}

	return httptest.NewServer(r)
}

func tearDownTestEnv(s *httptest.Server) {
	if s != nil {
		s.Close()
	}
}

func newHandler(code int, body []byte) func(w http.ResponseWriter, r *http.Request) {
	return func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(code)
		if body != nil {
			_, _ = w.Write(body)
		}
	}
}
