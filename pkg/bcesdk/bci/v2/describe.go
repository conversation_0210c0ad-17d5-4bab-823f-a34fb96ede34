package v2

import (
	"context"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	bciv1 "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
)

func (c *client) DescribePod(ctx context.Context, podID string, opt *bce.SignOption) (*bciv1.DescribePodResponse, error) {
	return c.v1Client.DescribePod(ctx, podID, opt)
}

func (c *client) DescribeBatchPodForLight(ctx context.Context, podIds []string, opt *bce.SignOption) ([]*bciv1.DescribePodResponse, error) {
	return c.v1Client.DescribeBatchPodForLight(ctx, podIds, opt)
}

func (c *client) DescribePodWithDeleted(ctx context.Context, podID string, opt *bce.SignOption) (*bciv1.DescribePodResponse, error) {
	return c.v1Client.DescribePodWithDeleted(ctx, podID, opt)
}
