package v2

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
)

func TestUpdateServiceAccountToken(t *testing.T) {
	var mode int32 = 420
	type args struct {
		podID     string
		updateReq *UpdateServiceAccountTokenRequest
		signOpt   *bce.SignOption
	}
	tests := []struct {
		name    string
		envs    []*testEnv
		args    args
		wantErr bool
	}{
		{
			name: "normal case",
			envs: []*testEnv{
				{
					method:  "PUT",
					path:    "/v2/instance/{podID}/configfile",
					handler: newHandler(http.StatusOK, nil),
				},
			},
			args: args{
				podID: "pod-testutils",
				updateReq: &UpdateServiceAccountTokenRequest{
					ConfigFileInfo: &bci.VolumeConfigFile{
						Name: "token",
						ConfigFiles: []bci.ConfigFile{
							{
								Path: "token",
								File: "file",
							},
						},
						DefaultMode: &mode,
					},
					Extras: map[string]string{"token": "3760000"},
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := setupTestEnv(tt.envs)
			defer tearDownTestEnv(s)

			c := NewClient(NewConfig(&bce.Config{
				Credentials: new(bce.Credentials),
				Checksum:    true,
				Endpoint:    s.URL}))
			c.SetDebug(true)
			fmt.Println(c)

			ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
			defer cancel()
			err := c.UpdateServiceAccountToken(ctx, tt.args.podID, tt.args.updateReq.ConfigFileInfo, tt.args.updateReq.Extras, tt.args.signOpt)
			if (err != nil) != tt.wantErr {
				t.Errorf("client.ListPods() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
