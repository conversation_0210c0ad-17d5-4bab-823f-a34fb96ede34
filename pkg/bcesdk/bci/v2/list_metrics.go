package v2

import (
	"context"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	bciv1 "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
)

func (c *client) ListPodsMetrics(ctx context.Context, podIDs []string, opt *bce.SignOption) (*bciv1.ListPodsMetricsResponse, error) {
	return c.v1Client.ListPodsMetrics(ctx, podIDs, opt)
}

func (c *client) ListPodsSummary(ctx context.Context, podIDs []string, opt *bce.SignOption) (*bciv1.ListPodsMetricsResponse, error) {
	return c.v1Client.ListPodsSummary(ctx, podIDs, opt)
}
