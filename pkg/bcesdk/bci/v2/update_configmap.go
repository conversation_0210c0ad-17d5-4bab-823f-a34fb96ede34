package v2

import (
	"context"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
)

func (c *client) UpdateConfigMap(ctx context.Context, podID string, configFile *bci.VolumeConfigFile, signOpt *bce.SignOption) error {
	extras := make(map[string]string)
	volumeType := "ConfigMap"
	return c.UpdateConfigFile(ctx, podID, volumeType, configFile, extras, signOpt)
}
