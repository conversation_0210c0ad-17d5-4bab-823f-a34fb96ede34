package v2

import (
	"context"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	bciv1 "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
)

var Endpoint = map[string]string{
	"qa00": "bci.bce-api.baidu-int.com",
	"qa":   "logic-bci.internal-qasandbox.baidu-int.com:8784",
}

//go:generate mockgen -destination ./mock.go -package v2 -self_package icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci/v2 icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci/v2 Client
type Client interface {
	SetDebug(bool)

	CreatePod(ctx context.Context, cfg *bciv1.PodConfig, eipCfg *bciv1.EIPConfig, opt *bce.SignOption) (*bciv1.CreatePodResponse, error)
	ListPods(ctx context.Context, opt *bciv1.ListOption, signOpt *bce.SignOption) (*bciv1.ListPodsResponse, error)
	ListPodsForLight(ctx context.Context, opt *bciv1.ListOption, signOpt *bce.SignOption) (*bciv1.ListPodsResponse, error)
	DeletePod(ctx context.Context, args *bciv1.DeletePodArgs, opt *bce.SignOption) error
	DescribePod(ctx context.Context, podID string, opt *bce.SignOption) (*bciv1.DescribePodResponse, error)
	DescribeBatchPodForLight(ctx context.Context, podIds []string, opt *bce.SignOption) ([]*bciv1.DescribePodResponse, error)
	DescribePodWithDeleted(ctx context.Context, podID string, opt *bce.SignOption) (*bciv1.DescribePodResponse, error)
	GetContainerLog(ctx context.Context, podID, containerName string, logOpts *bciv1.LogOptions, opt *bce.SignOption) ([]byte, error)
	LaunchExecWSSUrl(ctx context.Context, args *bciv1.LaunchExecWSSUrlArgs, opt *bce.SignOption) (string, error)

	GetUserVersion(ctx context.Context, opt *bce.SignOption) (*bciv1.UserVersionResponse, error)
	ListPodsMetrics(ctx context.Context, podIDs []string, signOpt *bce.SignOption) (*bciv1.ListPodsMetricsResponse, error)

	ReportPods(ctx context.Context, clusters map[string][]*bciv1.ClusterInfo, version string, signOpt *bce.SignOption) error
	UpdateServiceAccountToken(ctx context.Context, podID string, configFile *bciv1.VolumeConfigFile, extras map[string]string, signOpt *bce.SignOption) error
	UpdateConfigMap(ctx context.Context, podID string, configFile *bciv1.VolumeConfigFile, signOpt *bce.SignOption) error
	ListPodsSummary(ctx context.Context, podIDs []string, opt *bce.SignOption) (*bciv1.ListPodsMetricsResponse, error)
	UpdateDsContainers(ctx context.Context, dsContainers *bciv1.InjectDsContainersRequest, clientToken string, opt *bce.SignOption) error
}

type client struct {
	v1Client bciv1.Client
	*bce.Client
}

type Config struct {
	*bce.Config
}

func NewConfig(config *bce.Config) *Config {
	return &Config{config}
}

func NewClient(config *Config) *client {
	return &client{
		Client:   bce.NewClient(config.Config),
		v1Client: bciv1.NewClient(bciv1.NewConfig(config.Config, true)),
	}
}

// SetDebug enables debug mode of bce.Client instance.
func (c *client) SetDebug(debug bool) {
	c.v1Client.SetDebug(debug)
	c.Client.SetDebug(debug)
}

func (c *client) GetURL(objectKey string, params map[string]string) string {
	host := c.Endpoint

	if host == "" {
		host = Endpoint[c.GetRegion()]
	}

	if host == "" {
		host = "bci." + c.GetRegion() + ".baidubce.com"
	}

	uriPath := objectKey

	return c.Client.GetURL(host, uriPath, params)
}
