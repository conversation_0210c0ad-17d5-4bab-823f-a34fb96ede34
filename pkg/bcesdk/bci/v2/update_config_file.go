package v2

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci"
)

type UpdateConfigFileRequest struct {
	ConfigFileInfo *bci.VolumeConfigFile `json:"configFile"`
	Extras         map[string]string     `json:"extras"`
	VolumeType     string                `json:"volumeType"`
}

func (c *client) UpdateConfigFile(ctx context.Context, podID string, volumeType string, configFile *bci.VolumeConfigFile,
	extras map[string]string, signOpt *bce.SignOption) error {
	if configFile != nil && len(configFile.ConfigFiles) == 0 {
		return nil
	}
	updateConfigFileReq := &UpdateConfigFileRequest{
		ConfigFileInfo: configFile,
		Extras:         extras,
		VolumeType:     volumeType,
	}

	updateContent, err := json.Marshal(updateConfigFileReq)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("/v2/instance/"+podID+"/configfile", nil), bytes.NewBuffer(updateContent))
	if err != nil {
		return err
	}
	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		// not fount return nil
		if resp != nil && resp.StatusCode != http.StatusNotFound {
			return err
		}
	}
	return nil
}
