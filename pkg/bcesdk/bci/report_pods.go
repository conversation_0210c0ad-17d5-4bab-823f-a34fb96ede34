package bci

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	v1 "k8s.io/api/core/v1"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"
)

type InstanceInfo struct {
	InstanceID string     `json:"instanceId"`
	Name       string     `json:"name"`
	Namespace  string     `json:"namespace"`
	Status     StatusInfo `json:"status"`
}

type StatusInfo struct {
	Phase  v1.PodPhase `json:"phase"`
	Reason string      `json:"reason"`
}

type ClusterInfo struct {
	ClusterID string         `json:"clusterId"`
	Instances []InstanceInfo `json:"instances"`
}

func (c *client) ReportPods(ctx context.Context, clusters map[string][]*ClusterInfo, version string, signOpt *bce.SignOption) error {
	if len(clusters) == 0 {
		return errors.New("report pods cannot be nil")
	}

	ctx = logger.EnsureRequestIDInCtx(ctx)
	postContent, err := json.Marshal(clusters)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("inner/v%s/cluster/report", version)
	req, err := bce.NewRequest("POST", c.GetURL(url, nil), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return err
	}

	return nil
}
