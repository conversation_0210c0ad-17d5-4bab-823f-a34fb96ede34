package bci

import (
	"context"
	"net/http"
	"testing"

	"gotest.tools/assert"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

func TestGetUserVersion(t *testing.T) {
	type args struct {
		ctx context.Context
		opt *bce.SignOption
	}
	tests := []struct {
		name    string
		envs    []*testEnv
		args    args
		want    *UserVersionResponse
		wantErr bool
	}{
		// TODO: Add testutils cases.
		{
			name: "v2 user",
			envs: []*testEnv{
				{
					method:  http.MethodGet,
					path:    "/api/logical/bci/v2/pod/userVersion",
					handler: newHandler(http.StatusOK, []byte(`{"accountID":"00dc1b52d8354d9193536e4dd2c41ae6","isv2":true}`)),
				},
			},
			args: args{
				ctx: context.TODO(),
				opt: nil,
			},
			want: &UserVersionResponse{
				AccountID: "00dc1b52d8354d9193536e4dd2c41ae6",
				IsV2:      true,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := setupTestEnv(tt.envs)
			defer tearDownTestEnv(s)

			c := NewClient(NewConfig(&bce.Config{
				Credentials: new(bce.Credentials),
				Checksum:    true,
				Endpoint:    s.URL,
			}))

			got, err := c.GetUserVersion(tt.args.ctx, tt.args.opt)
			if (err != nil) != tt.wantErr {
				t.Errorf("client.GetUserVersion() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.DeepEqual(t, got, tt.want)
		})
	}
}
