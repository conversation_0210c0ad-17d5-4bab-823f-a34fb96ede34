package bci

import (
	"bytes"
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"
)

const MaxPodsPerListPodsMetricsRequest int = 100

type ListPodsMetricsArgs struct {
	IDs  []string `json:"ids"`
	From string   `json:"from"`
}

type ListPodsMetricsResponse struct {
	Result []*PodMetrics `json:"result"`
}

type PodMetrics struct {
	PodShortID string    `json:"podShortId"`
	Metrics    []*Metric `json:"metrics"`
}

type Metric struct {
	Value     float64    `json:"value"`
	TimeStamp int64      `json:"timeStamp"`
	Meta      MetricMeta `json:"metric"`
}

type MetricMeta struct {
	Name        string `json:"__name__"`
	Image       string `json:"image"`
	Container   string `json:"container"`
	ContainerID string `json:"containerID"`
}

func (c *client) ListPodsMetrics(ctx context.Context, podIDs []string, signOpt *bce.SignOption) (*ListPodsMetricsResponse, error) {
	if len(podIDs) == 0 {
		return nil, nil
	}

	ctx = logger.EnsureRequestIDInCtx(ctx)

	args := &ListPodsMetricsArgs{
		IDs:  podIDs,
		From: "api",
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("api/logical/bci/v2/pod/listMetricsByShortIds", nil), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	listPodMetricsResponse := new(ListPodsMetricsResponse)
	err = json.Unmarshal(bodyContent, listPodMetricsResponse)
	if err != nil {
		return nil, err
	}

	return listPodMetricsResponse, nil
}

// ListPodsSummary list pod summary
func (c *client) ListPodsSummary(ctx context.Context, podIDs []string, signOpt *bce.SignOption) (*ListPodsMetricsResponse, error) {
	if len(podIDs) == 0 {
		return nil, nil
	}

	ctx = logger.EnsureRequestIDInCtx(ctx)

	args := &ListPodsMetricsArgs{
		IDs:  podIDs,
		From: "api",
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("api/logical/bci/v2/pod/listMetricsSummary", nil), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	listPodMetricsResponse := new(ListPodsMetricsResponse)
	err = json.Unmarshal(bodyContent, listPodMetricsResponse)
	if err != nil {
		return nil, err
	}

	return listPodMetricsResponse, nil
}
