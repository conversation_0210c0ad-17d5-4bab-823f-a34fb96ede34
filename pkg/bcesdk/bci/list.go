package bci

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"
)

type ListManner string

const (
	ListMannerPage  ListManner = "page"
	MaxListPageSize int64      = 1000

	KeywordTypePodID   = "podId"
	KeywordTypePodName = "name"
	KeywordTypeCCEID   = "cceId"

	ListMannerMarker ListManner = "marker"
	DefaultMaxKeys   int64      = 1000
)

type ListKeyword struct {
	KeywordType string
	Keyword     string
}

func NewListKeyword(keywordType, keyword string) *ListKeyword {
	return &ListKeyword{
		KeywordType: keywordType,
		Keyword:     keyword,
	}
}

func NewListOption(cceID string, pageNo, pageSize int64, order, orderBy string, filters map[string]string, keyword *ListKeyword) *ListOption {
	return &ListOption{
		Manner:   ListMannerPage, // only support page manner currently
		CCEID:    cceID,
		Order:    order,
		OrderBy:  orderBy,
		PageNo:   pageNo,
		PageSize: pageSize,
		Filters:  ListFilters(filters),
		Keyword:  keyword,
	}
}

func NewMarkerListOption(cceID, marker, order, orderBy string, maxKeys int64, filters map[string]string, keyword *ListKeyword) *ListOption {
	return &ListOption{
		Manner:  ListMannerMarker,
		CCEID:   cceID,
		Order:   order,
		OrderBy: orderBy,
		Filters: ListFilters(filters),
		Keyword: keyword,
		Marker:  marker,
		MaxKeys: maxKeys,
	}
}

type ListOption struct {
	Manner           ListManner
	CCEID            string
	Order            string
	OrderBy          string
	PageNo, PageSize int64
	Filters          ListFilters
	Keyword          *ListKeyword
	Marker           string
	MaxKeys          int64
}

type ListFilters map[string]string

func (f *ListFilters) ToString() string {
	// TODO
	return ""
}

type ListPodsResponse struct {
	Result  []*Pod      `json:"result"`
	Orders  interface{} `json:"orders"`
	OrderBy string      `json:"orderBy"`
	Order   string      `json:"order"`
	// for ListMannerPage
	PageNo     int64 `json:"pageNo"`
	PageSize   int64 `json:"pageSize"`
	TotalCount int64 `json:"totalCount"`
	// for ListMannerMarker
	Marker      string `json:"marker"`
	IsTruncated bool   `json:"isTruncated"`
	NextMarker  string `json:"nextMarker"`
	MaxKeys     int64  `json:"maxKeys"`
}

func (opt *ListOption) validate() error {
	// TODO
	if opt == nil {
		return errors.New("list opt is nil")
	}

	switch opt.Manner {
	case ListMannerPage:
		if opt.PageNo <= 0 || opt.PageSize <= 0 {
			return fmt.Errorf("pageNo and pageSize must be greater than zero: got pageNo=%d, pageSize=%d",
				opt.PageNo, opt.PageSize)
		}
	case ListMannerMarker:
		if opt.MaxKeys <= 0 {
			return fmt.Errorf("maxKeys must be greater than zero: got maxKeys=%d", opt.MaxKeys)
		}
	default:
		return fmt.Errorf("unknown list manner %s", opt.Manner)
	}

	return nil
}

func (opt *ListOption) ToQueries() map[string]string {
	if opt == nil {
		return nil
	}
	ret := map[string]string{
		"manner":  string(opt.Manner),
		"cceId":   opt.CCEID,
		"orderBy": opt.OrderBy,
		"order":   opt.Order,
		"filters": opt.Filters.ToString(),
	}
	if opt.Keyword != nil {
		ret["keywordType"] = opt.Keyword.KeywordType
		ret["keyword"] = opt.Keyword.Keyword
	}
	switch opt.Manner {
	case ListMannerPage:
		ret["pageNo"] = strconv.FormatInt(opt.PageNo, 10)
		ret["pageSize"] = strconv.FormatInt(opt.PageSize, 10)
	case ListMannerMarker:
		ret["marker"] = opt.Marker
		if opt.MaxKeys > 0 {
			ret["maxKeys"] = strconv.FormatInt(opt.MaxKeys, 10)
		}
	}
	return ret
}

func (c *client) ListPods(ctx context.Context, opt *ListOption, signOpt *bce.SignOption) (*ListPodsResponse, error) {
	ctx = logger.EnsureRequestIDInCtx(ctx)
	if err := opt.validate(); err != nil {
		return nil, err
	}

	if !c.wrappedByV2 && opt.Manner == ListMannerMarker {
		return nil, fmt.Errorf("list mannger %s is not supported by bci v1", opt.Manner)
	}

	params := opt.ToQueries()

	req, err := bce.NewRequest("GET", c.GetURL("api/logical/bci/v1/pod/list", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	listPodsResponse := new(ListPodsResponse)
	err = json.Unmarshal(bodyContent, listPodsResponse)
	if err != nil {
		return nil, err
	}

	return listPodsResponse, nil
}

func (c *client) ListPodsForLight(ctx context.Context, opt *ListOption, signOpt *bce.SignOption) (*ListPodsResponse, error) {
	ctx = logger.EnsureRequestIDInCtx(ctx)
	if err := opt.validate(); err != nil {
		return nil, err
	}

	if !c.wrappedByV2 && opt.Manner == ListMannerMarker {
		return nil, fmt.Errorf("list mannger %s is not supported by bci v1", opt.Manner)
	}

	params := opt.ToQueries()

	req, err := bce.NewRequest("GET", c.GetURL("api/logical/bci/v1/pod/list/light", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	listPodsResponse := new(ListPodsResponse)
	err = json.Unmarshal(bodyContent, listPodsResponse)
	if err != nil {
		return nil, err
	}

	return listPodsResponse, nil
}
