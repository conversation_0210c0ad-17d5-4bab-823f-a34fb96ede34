package bci

import (
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

type PodQuotaResponse struct {
	PodTotal        int `json:"podTotal"`
	PodCreated      int `json:"podCreated"`
	VolumeRatio     int `json:"volumeRatio"`
	NfsRatio        int `json:"nfsRatio"`
	EmptyDirRatio   int `json:"emptyDirRatio"`
	ConfigFileRatio int `json:"configFileRatio"`
	EnvRatio        int `json:"envRatio"`
	PortRatio       int `json:"portRatio"`
}

func (c *client) GetPodQuota(ctx context.Context, opt *bce.SignOption) (*PodQuotaResponse, error) {
	ctx = logger.EnsureRequestIDInCtx(ctx)
	req, err := bce.NewRequest("GET", c.GetURL("api/logical/bci/v1/pod/quota", nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	quota := new(PodQuotaResponse)
	err = json.Unmarshal(bodyContent, quota)
	if err != nil {
		return nil, err
	}
	return quota, nil
}
