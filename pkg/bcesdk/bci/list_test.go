package bci

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gotest.tools/assert"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

func TestListPods(t *testing.T) {
	type args struct {
		opt     *ListOption
		signOpt *bce.SignOption
	}
	tests := []struct {
		name    string
		envs    []*testEnv
		args    args
		want    *ListPodsResponse
		wantErr bool
	}{
		// TODO: Add testutils cases.
		{
			name: "normal case",
			envs: []*testEnv{
				{
					method: "GET",
					path:   "/api/logical/bci/v1/pod/list",
					handler: newH<PERSON><PERSON>(http.StatusOK, []byte(fmt.Sprintf(`{"result":[{"name":"pod","podId":"p-Caa113K0",
"podUuid":"ac3bf5ff-336d-4475-9442-900eb88a7a4a","status":"Running","vCpu":1,"memory":0.5,"eipUuid":"","publicIp":"","bandwidthInMbps":0,"cceUuid":"%s",
"internalIp":"***********","restartPolicy":"Always","orderId":"31e69f08-90d1-432b-aa16-7c1154ce43c7","createdTime":"2019-05-08T01:15:30Z",
"updatedTime":"2019-05-08T08:20:34Z","deletedTime":null,"description":"","region":"gz","userId":"efcfbe455cf3412d9d27b662794cd812","resourceUuid":"",
"taskStatus":"","nfs":[],"emptyDir":[{"name":"empty_dir"}],"configFile":[],"tags":null}],"orders":null,"orderBy":"%s","order":"%s","pageNo":%d,"pageSize":%d,
"totalCount":3}`, "c-f99UG69T", "", "", 1, 10))),
				},
			},
			args: args{
				opt: NewListOption("c-f99UG69T", 1, 10, "", "", nil, nil),
			},
			wantErr: false,
		},
		{
			name: "invalid page list option",
			args: args{
				opt: NewListOption("cce-xx11xx11", 1, -10, "", "", nil, nil),
			},
			wantErr: true,
		},
		{
			name: "invalid marker list option",
			args: args{
				opt: NewMarkerListOption("cce-xx11xx11", "", "", "", 0, nil, nil),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := setupTestEnv(tt.envs)
			defer tearDownTestEnv(s)

			c := NewClient(NewConfig(&bce.Config{
				Credentials: new(bce.Credentials),
				Checksum:    true,
				Endpoint:    s.URL,
			}))
			c.SetDebug(true)

			ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
			defer cancel()
			_, err := c.ListPods(ctx, tt.args.opt, tt.args.signOpt)
			if (err != nil) != tt.wantErr {
				t.Errorf("client.ListPods() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			// TODO assert list resp
		})
	}
}

func TestListOption_ToQueries(t *testing.T) {

	tests := []struct {
		name string
		opt  *ListOption
		want map[string]string
	}{
		// All testutils cases.
		{
			name: "nil opt",
			opt:  nil,
			want: nil,
		},
		{
			name: "page manner",
			opt:  NewListOption("cce-xxxx1111", 1, 10, "", "", nil, nil),
			want: map[string]string{
				"manner":   string(ListMannerPage),
				"cceId":    "cce-xxxx1111",
				"orderBy":  "",
				"order":    "",
				"filters":  "",
				"pageNo":   "1",
				"pageSize": "10",
			},
		},
		{
			name: "marker manner",
			opt:  NewMarkerListOption("cce-xxxx1111", "p-testpod0", "", "", DefaultMaxKeys, nil, nil),
			want: map[string]string{
				"manner":  string(ListMannerMarker),
				"cceId":   "cce-xxxx1111",
				"orderBy": "",
				"order":   "",
				"filters": "",
				"marker":  "p-testpod0",
				"maxKeys": strconv.FormatInt(DefaultMaxKeys, 10),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.opt.ToQueries()
			assert.DeepEqual(t, got, tt.want)
		})
	}
}
