package bci

import (
	"context"
	"fmt"
	"net/http"
	"reflect"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

func handleGetPodQuota(w http.ResponseWriter, r *http.Request) {
	fmt.Println("handleGetPodQuota is called")
	w.<PERSON>er().Set("Content-Type", "application/json")
	w.<PERSON>rite<PERSON>ead<PERSON>(http.StatusOK)
	_, _ = w.Write([]byte(`{
    "podTotal": 5,
    "podCreated": 5,
    "volumeRatio": 5,
    "nfsRatio": 5,
    "emptyDirRatio": 5,
    "configFileRatio": 5,
    "envRatio": 5,
    "portRatio": 5
}`))
}

func TestGetPodQuota(t *testing.T) {
	type args struct {
		opt *bce.SignOption
	}
	tests := []struct {
		name    string
		envs    []*testEnv
		args    args
		want    *PodQuotaResponse
		wantErr bool
	}{
		// TODO: Add testutils cases.
		{
			name: "normal case",
			envs: []*testEnv{
				{
					method: "GET",
					path:   "/api/logical/bci/v1/pod/quota",
					handler: newHandler(http.StatusOK, []byte(`{"podTotal":5,"podCreated":5,"volumeRatio":5,"nfsRatio":5,"emptyDirRatio":5,"configFileRatio":5,
"envRatio":5,"portRatio":5}`)),
				},
			},
			want: &PodQuotaResponse{
				PodTotal:        5,
				PodCreated:      5,
				VolumeRatio:     5,
				NfsRatio:        5,
				EmptyDirRatio:   5,
				ConfigFileRatio: 5,
				EnvRatio:        5,
				PortRatio:       5,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := setupTestEnv(tt.envs)
			defer tearDownTestEnv(s)

			c := NewClient(NewConfig(&bce.Config{
				Credentials: new(bce.Credentials),
				Checksum:    true,
				Endpoint:    s.URL,
			}))
			c.SetDebug(true)

			ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
			defer cancel()
			got, err := c.GetPodQuota(ctx, tt.args.opt)
			if (err != nil) != tt.wantErr {
				t.Errorf("client.GetPodQuota() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("client.GetPodQuota() = %v, want %v", got, tt.want)
			}
		})
	}
}
