package bci

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"
)

type DescribePodResponse struct {
	*Pod
	Containers    []Container    `json:"containers"`
	SecurityGroup *SecurityGroup `json:"securityGroup"`
	VPC           *VPC           `json:"vpc"`
	Subnet        *Subnet        `json:"subnet"`
}

func (c *client) DescribePod(ctx context.Context, podID string, opt *bce.SignOption) (*DescribePodResponse, error) {
	ctx = logger.EnsureRequestIDInCtx(ctx)
	if podID == "" {
		return nil, errors.New("bci pod id cannot be empty")
	}

	req, err := bce.NewRequest("GET", c.GetURL("api/logical/bci/v1/pod"+"/"+podID, nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	describePodResponse := new(DescribePodResponse)
	err = json.Unmarshal(bodyContent, describePodResponse)
	if err != nil {
		return nil, err
	}
	return describePodResponse, nil
}

type BatchDescribeResponse struct {
	Result []*DescribePodResponse `json:"result"`
}

func (c *client) DescribeBatchPodForLight(ctx context.Context, podIds []string, opt *bce.SignOption) ([]*DescribePodResponse, error) {
	ctx = logger.EnsureRequestIDInCtx(ctx)
	if len(podIds) == 0 {
		return nil, errors.New("bci pod id cannot be empty")
	}

	args := &struct {
		PodIds []string `json:"podIds"`
	}{
		PodIds: podIds,
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("api/logical/bci/v1/pod/describe", nil), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	responseStruct := BatchDescribeResponse{}
	err = json.Unmarshal(bodyContent, &responseStruct)
	if err != nil {
		return nil, err
	}
	return responseStruct.Result, nil
}

func (c *client) DescribePodWithDeleted(ctx context.Context, podID string, opt *bce.SignOption) (*DescribePodResponse, error) {
	ctx = logger.EnsureRequestIDInCtx(ctx)
	if podID == "" {
		return nil, errors.New("bci pod id cannot be empty")
	}

	req, err := bce.NewRequest("GET", c.GetURL("api/logical/bci/v1/pod"+"/"+podID+"/describe/deleted", nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	describePodResponse := new(DescribePodResponse)
	err = json.Unmarshal(bodyContent, describePodResponse)
	if err != nil {
		return nil, err
	}
	return describePodResponse, nil
}
