package bci

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"
)

type DsConfig struct {
	ExpectDsContainers []Container `json:"expectDsContainers"`
	ExpectDsVolumes    *Volumes    `json:"expectDsVolumes"`
}

type InjectDsContainersRequest struct {
	PodID  string    `json:"podId"`
	Config *DsConfig `json:"config"`
}

func (c *client) UpdateDsContainers(ctx context.Context, dsContainers *InjectDsContainersRequest, clientToken string, opt *bce.SignOption) error {
	ctx = logger.EnsureRequestIDInCtx(ctx)

	if clientToken == "" {
		clientToken = c.GenerateClientToken()
	}

	params := map[string]string{
		"clientToken": clientToken,
	}
	postContent, err := json.Marshal(dsContainers.Config)
	if err != nil {
		return err
	}
	req, err := bce.NewRequest(
		"PUT", c.GetURL(fmt.Sprintf("api/logical/bci/v1/pod/%s/dsContainers", dsContainers.PodID), params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, opt)
	if err != nil {
		return err
	}

	return nil
}
