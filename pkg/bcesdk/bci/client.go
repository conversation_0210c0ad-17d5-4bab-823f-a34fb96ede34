package bci

import (
	"context"
	"strings"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

// Endpoint contains all valid endpoints of Baidu Container Instance.
var Endpoint = map[string]string{
	"gz":   "bci.gz.baidubce.com",
	"qa00": "bci.bce-api.baidu-int.com",
	"qa":   "logic-bci.internal-qasandbox.baidu-int.com:8784",
}

var _ Client = &client{}

//go:generate mockgen -destination ./mock.go -package bci -self_package icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bci Client

// Client - 定义 bci 相关方法
type Client interface {
	SetDebug(bool)

	CreatePod(ctx context.Context, cfg *PodConfig, eipCfg *EIPConfig, opt *bce.SignOption) (*CreatePodResponse, error)
	ListPods(ctx context.Context, opt *ListOption, signOpt *bce.SignOption) (*ListPodsResponse, error)
	ListPodsForLight(ctx context.Context, opt *ListOption, signOpt *bce.SignOption) (*ListPodsResponse, error)
	DeletePod(ctx context.Context, args *DeletePodArgs, opt *bce.SignOption) error
	DescribePod(ctx context.Context, podID string, opt *bce.SignOption) (*DescribePodResponse, error)
	DescribeBatchPodForLight(ctx context.Context, podIds []string, opt *bce.SignOption) ([]*DescribePodResponse, error)
	DescribePodWithDeleted(ctx context.Context, podID string, opt *bce.SignOption) (*DescribePodResponse, error)
	GetContainerLog(ctx context.Context, podID, containerName string, logOpts *LogOptions, opt *bce.SignOption) ([]byte, error)
	GetPodQuota(ctx context.Context, opt *bce.SignOption) (*PodQuotaResponse, error)
	LaunchExecWSSUrl(ctx context.Context, args *LaunchExecWSSUrlArgs, opt *bce.SignOption) (string, error)

	CreatePullTask(ctx context.Context, args *PullTaskArgs, opt *bce.SignOption) (*CreatePullTaskResponse, error)
	QueryPullTask(ctx context.Context, taskID string, opt *bce.SignOption) (*QueryPullTaskResponse, error)

	GetUserVersion(ctx context.Context, opt *bce.SignOption) (*UserVersionResponse, error)
	ListPodsMetrics(ctx context.Context, podIDs []string, signOpt *bce.SignOption) (*ListPodsMetricsResponse, error)

	ReportPods(ctx context.Context, clusters map[string][]*ClusterInfo, version string, signOpt *bce.SignOption) error
	UpdateServiceAccountToken(ctx context.Context, podID string, configFile *VolumeConfigFile, extras map[string]string, signOpt *bce.SignOption) error
	UpdateConfigMap(ctx context.Context, podID string, configFile *VolumeConfigFile, signOpt *bce.SignOption) error
	ListPodsSummary(ctx context.Context, podIDs []string, signOpt *bce.SignOption) (*ListPodsMetricsResponse, error)
	UpdateDsContainers(ctx context.Context, dsContainers *InjectDsContainersRequest, clientToken string, opt *bce.SignOption) error
}

type client struct {
	*bce.Client
	// wrappedByV2 indicates whether client is wrapped in a v2 client.
	wrappedByV2 bool
}

// Config contains all options for bci.Client.
type Config struct {
	*bce.Config
	wrappedByV2 bool
}

func NewConfig(config *bce.Config, wrappedByV2Arg ...bool) *Config {
	wrappedByV2 := false
	if len(wrappedByV2Arg) > 0 {
		wrappedByV2 = wrappedByV2Arg[0]
	}
	return &Config{
		Config:      config,
		wrappedByV2: wrappedByV2,
	}
}

func NewClient(config *Config) *client {
	bceClient := bce.NewClient(config.Config)
	return &client{
		Client:      bceClient,
		wrappedByV2: config.wrappedByV2,
	}
}

// GetURL generates the full URL of http request for Baidu Cloud BOS API.
func (c *client) GetURL(objectKey string, params map[string]string) string {
	host := c.Endpoint

	if host == "" {
		host = Endpoint[c.GetRegion()]
	}

	if host == "" {
		host = "bci." + c.GetRegion() + ".baidubce.com"
	}

	uriPath := objectKey

	if c.wrappedByV2 {
		uriPath = strings.ReplaceAll(uriPath, "bci/v1/", "bci/v2/")
	}

	return c.Client.GetURL(host, uriPath, params)
}
