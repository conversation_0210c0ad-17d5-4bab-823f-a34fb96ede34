package bci

import (
	"context"
	"net/http"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

func TestDeletePod(t *testing.T) {
	type args struct {
		args *DeletePodArgs
		opt  *bce.SignOption
	}
	tests := []struct {
		name    string
		envs    []*testEnv
		args    args
		wantErr bool
	}{
		// All testutils cases.
		{
			name: "normal case",
			envs: []*testEnv{
				{
					method:  "POST",
					path:    "/api/logical/bci/v1/pod/delete",
					handler: new<PERSON><PERSON><PERSON>(http.StatusOK, nil),
				},
			},
			args: args{
				args: &DeletePodArgs{
					DeletePods: []*DeletePod{
						{
							PodID: "p-1nJ2r9o1",
							CCEID: "c-f99UG69T",
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "empty pod id case",
			args: args{
				args: &DeletePodArgs{
					DeletePods: []*DeletePod{
						{
							CCEID: "c-f99UG69T",
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "remote error case",
			args: args{
				args: &DeletePodArgs{
					DeletePods: []*DeletePod{
						{
							PodID: "p-1nJ2r9o1",
							CCEID: "c-f99UG69T",
						},
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := setupTestEnv(tt.envs)
			defer tearDownTestEnv(s)

			c := NewClient(NewConfig(&bce.Config{
				Credentials: new(bce.Credentials),
				Checksum:    true,
				Endpoint:    s.URL,
			}))
			c.SetDebug(true)

			ctx, cancel := context.WithTimeout(context.TODO(), 10*time.Second)
			defer cancel()
			if err := c.DeletePod(ctx, tt.args.args, tt.args.opt); (err != nil) != tt.wantErr {
				t.Errorf("client.DeletePod() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
