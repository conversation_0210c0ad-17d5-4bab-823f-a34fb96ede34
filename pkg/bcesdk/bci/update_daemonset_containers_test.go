package bci

import (
	"context"
	"net/http"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

var dsContainer = Container{
	Name:          "nginx",
	ImageID:       "nginxId",
	ContainerType: "dsContainer",
}

var dsConfig = DsConfig{
	ExpectDsContainers: []Container{dsContainer},
}

var injectDsRequest = &InjectDsContainersRequest{
	PodID:  "p-test",
	Config: &dsConfig,
}

func TestUpdateDsContainers(t *testing.T) {
	type args struct {
		injectRequest *InjectDsContainersRequest
		opt           *bce.SignOption
		clientToken   string
	}
	tests := []struct {
		name    string
		args    args
		envs    []*testEnv
		wantErr bool
	}{
		{
			name: "update ds containers success",
			args: args{
				injectRequest: injectDsRequest,
				opt:           bce.NewSignOptionWithoutAuth(),
				clientToken:   "test",
			},
			envs: []*testEnv{
				{
					method:  "PUT",
					path:    "/api/logical/bci/v1/pod/{podID}/dsContainers",
					handler: newHand<PERSON>(http.StatusOK, nil),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := setupTestEnv(tt.envs)
			defer tearDownTestEnv(s)
			c := NewClient(NewConfig(&bce.Config{
				Credentials: new(bce.Credentials),
				Checksum:    true,
				Endpoint:    s.URL,
			}))
			c.SetDebug(true)
			ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
			defer cancel()
			err := c.UpdateDsContainers(ctx, tt.args.injectRequest, tt.args.clientToken, tt.args.opt)
			if (err != nil) != tt.wantErr {
				t.Errorf("client.UpdateDsContainers() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
