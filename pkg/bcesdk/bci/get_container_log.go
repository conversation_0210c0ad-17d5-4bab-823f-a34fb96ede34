package bci

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"strconv"
	"time"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/utils"
)

type LogOptions struct {
	LimitBytes   int       `query:"limitBytes" valid:"Min(0)"`
	TailLines    int       `query:"tailLines" valid:"Min(0)"`
	SinceTime    time.Time `query:"sinceTime"`
	SinceSeconds int       `query:"sinceSeconds" valid:"Min(0)"`
	Timestamps   bool      `query:"timestamps"`
	Follow       bool      `query:"follow"`
	Previous     bool      `query:"previous"`
}

func (l *LogOptions) ToQueryValues() map[string]string {
	if l == nil {
		return nil
	}
	q := make(map[string]string)
	v := reflect.ValueOf(*l)
	t := v.Type()
	for i := 0; i < t.NumField(); i++ {
		queryName := t.Field(i).Tag.Get("query")
		if queryName == "" {
			continue
		}
		val := v.Field(i)
		if val.IsZero() {
			continue
		}
		switch val.Type() {
		case reflect.TypeOf(bool(false)):
			q[queryName] = "true" // non-zero bool value can only be true
		case reflect.TypeOf(int(0)):
			q[queryName] = strconv.FormatInt(val.Int(), 10)
		case reflect.TypeOf(time.Time{}):
			q[queryName] = val.Interface().(time.Time).Format(time.RFC3339)
		}
	}
	return q
}

func (c *client) GetContainerLog(ctx context.Context, podID, containerName string,
	logOpts *LogOptions, opt *bce.SignOption) ([]byte, error) {
	if podID == "" || containerName == "" {
		return nil, errors.New("bci pod id and containerName cannot be empty")
	}
	if err := utils.Valid(logOpts); err != nil {
		return nil, fmt.Errorf("illegal LogOptions: %w", err)
	}

	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("api/logical/bci/v1/pod/%s/%s/log",
		podID, containerName), logOpts.ToQueryValues()), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	return bodyContent, nil
}
