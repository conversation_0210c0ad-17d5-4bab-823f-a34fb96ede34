package bci

import (
	"context"
	"net/http"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

var testClusters = []*ClusterInfo{
	{
		ClusterID: "cce-testutils",
		Instances: []InstanceInfo{
			{
				InstanceID: "p-testutils",
				Name:       "testutils",
				Namespace:  "default",
				Status: StatusInfo{
					Phase: "running",
				},
			},
		},
	},
}

var clustersMap = map[string][]*ClusterInfo{
	"clusters": testClusters,
}

func TestReportPods(t *testing.T) {
	type args struct {
		version  string
		clusters map[string][]*ClusterInfo
		opt      *bce.SignOption
	}
	tests := []struct {
		name    string
		args    args
		envs    []*testEnv
		wantErr bool
	}{
		{
			name: "report pods success",
			args: args{
				version:  "2",
				clusters: clustersMap,
				opt:      bce.NewSignOptionWithoutAuth(),
			},
			envs: []*testEnv{
				{
					method:  "POST",
					path:    "/inner/v{version}/cluster/report",
					handler: new<PERSON><PERSON><PERSON>(http.StatusOK, nil),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := setupTestEnv(tt.envs)
			defer tearDownTestEnv(s)
			c := NewClient(NewConfig(&bce.Config{
				Credentials: new(bce.Credentials),
				Checksum:    true,
				Endpoint:    s.URL,
			}))
			c.SetDebug(true)
			ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
			defer cancel()
			err := c.ReportPods(ctx, tt.args.clusters, tt.args.version, tt.args.opt)
			if (err != nil) != tt.wantErr {
				t.Errorf("client.ReportPods() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}

}
