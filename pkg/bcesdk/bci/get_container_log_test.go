package bci

import (
	"context"
	"net/http"
	"testing"
	"time"

	"github.com/gorilla/mux"
	"gotest.tools/assert"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/testutil"
)

func Test_client_GetContainerLog(t *testing.T) {
	type fields struct {
		Client *bce.Client
	}
	type args struct {
		envs []*testutil.TestEnv

		ctx           context.Context
		podID         string
		containerName string
		logOpts       *LogOptions
		opt           *bce.SignOption

		want    []byte
		wantErr bool
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add testutils cases.
		{
			name: "normal case",
			args: func() args {
				podID := "p-UOTKde1L"
				containerName := "container01"
				want := []byte(`[dumb-init] Child spawned with PID 6.
[dumb-init] Unable to attach to controlling tty (errno=25 Inappropriate ioctl for device).
[dumb-init] setsid complete.
I0519 11:38:13.337979       6 launch.go:105] &{NGINX 0.9.0-beta.11 git-a3131c5 https://github.com/kubernetes/ingress}
I0519 11:38:13.338328       6 launch.go:108] Watching for ingress class: nginx
F0519 11:38:13.338347       6 launch.go:112] Please specify --default-backend-service
[dumb-init] Received signal 17.
[dumb-init] A child with PID 6 exited with exit status 255.
[dumb-init] Forwarded signal 15 to children.
[dumb-init] Child exited with status 255. Goodbye.
`)
				return args{
					envs: []*testutil.TestEnv{
						{
							Method: "GET",
							Path:   "/api/logical/bci/v1/pod/{podID}/{containerName}/log",
							Handler: func(w http.ResponseWriter, r *http.Request) {
								vars := mux.Vars(r)
								assert.Equal(t, vars["podID"], podID)
								assert.Equal(t, vars["containerName"], containerName)
								w.Header().Set("Content-Type", "application/octet-stream; charset=utf-8")
								w.WriteHeader(http.StatusOK)
								_, _ = w.Write(want)
							},
						},
					},
					ctx:           context.TODO(),
					podID:         podID,
					containerName: containerName,
					want:          want,
				}
			}(),
		},
		{
			name: "normal case with log option",
			args: func() args {
				podID := "p-1G70Z8Zb"
				containerName := "container02"
				want := []byte(`[dumb-init] Unable to detach from controlling tty (errno=25 Inappropriate ioctl for device).
[dumb-i
`)
				return args{
					envs: []*testutil.TestEnv{
						{
							Method: "GET",
							Path:   "/api/logical/bci/v1/pod/{podID}/{containerName}/log",
							Handler: func(w http.ResponseWriter, r *http.Request) {
								vars := mux.Vars(r)
								assert.Equal(t, vars["podID"], podID)
								assert.Equal(t, vars["containerName"], containerName)
								assert.Equal(t, r.URL.Query().Get("limitBytes"), "100")
								w.Header().Set("Content-Type", "application/octet-stream; charset=utf-8")
								w.WriteHeader(http.StatusOK)
								_, _ = w.Write(want)
							},
						},
					},
					ctx:           context.TODO(),
					podID:         podID,
					containerName: containerName,
					logOpts: &LogOptions{
						LimitBytes: 100,
					},
					want: want,
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := testutil.SetupTestEnv(tt.args.envs)
			defer testutil.TearDownTestEnv(s)

			c := NewClient(NewConfig(&bce.Config{
				Credentials: new(bce.Credentials),
				Checksum:    true,
				Endpoint:    s.URL,
			}))
			c.SetDebug(true)

			got, err := c.GetContainerLog(tt.args.ctx, tt.args.podID, tt.args.containerName, tt.args.logOpts, tt.args.opt)

			assert.Assert(t, (err != nil) == tt.args.wantErr)
			if !tt.args.wantErr {
				assert.Equal(t, string(got), string(tt.args.want))
			}
		})
	}
}

func TestLogOptions_ToQueryValues(t *testing.T) {
	testSinceTime, err := time.Parse(time.RFC3339, "2020-06-01T00:10:00Z")
	if err != nil {
		t.Fatal(err)
	}
	type fields struct {
		*LogOptions
	}
	tests := []struct {
		name   string
		fields fields
		want   map[string]string
	}{
		// TODO: Add testutils cases.
		{
			name: "nil opt case",
			want: nil,
		},
		{
			name: "non-nil opts case",
			fields: fields{
				LogOptions: &LogOptions{
					LimitBytes:   100,
					SinceTime:    testSinceTime,
					SinceSeconds: 1000,
					Timestamps:   true,
				},
			},
			want: map[string]string{
				"limitBytes":   "100",
				"sinceSeconds": "1000",
				"sinceTime":    "2020-06-01T00:10:00Z",
				"timestamps":   "true",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.DeepEqual(t, tt.fields.LogOptions.ToQueryValues(), tt.want)
		})
	}
}
