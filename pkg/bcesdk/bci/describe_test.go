package bci

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

func TestDescribePod(t *testing.T) {
	type args struct {
		podID string
		opt   *bce.SignOption
	}
	tests := []struct {
		name    string
		envs    []*testEnv
		args    args
		want    *DescribePodResponse
		wantErr bool
	}{
		// TODO: Add testutils cases.
		{
			name: "normal case",
			envs: []*testEnv{
				{
					method: "GET",
					path:   "/api/logical/bci/v1/pod/{podID}",
					handler: new<PERSON><PERSON><PERSON>(http.StatusOK, []byte(fmt.Sprintf(`{"name":"pod","podId":"%s","podUuid":"ac3bf5ff-336d-4475-9442-900eb88a7a4a",
"status":"Pending","vCpu":0,"memory":0.5,"eipUuid":"","publicIp":"","bandwidthInMbps":0,"cceUuid":"","internalIp":"",
"securityGroupUuid":"1aaaadf9-9ce1-4140-86ad-da09ee7dbf64","restartPolicy":"Always","orderId":"15c81ac9-c917-45de-8d81-3744be6fc4a6",
"createdTime":"2019-04-29T14:39:37Z","updatedTime":"2019-04-29T14:39:37Z","deletedTime":null,"description":"","userId":"bb45087dee674fcaa21d75b53a35f7fc",
"taskStatus":"","nfs":[{"name":"config","configFiles":[{"path":"pod/","file":"file"}]}],"emptyDir":[{"name":"empty_dir"}],"configFile":[],"tags":[],
"containers":[{"name":"container01","containerUuid":"16151de01cefd87e78322709ac42b12e20254439d0679d1282ac05645cab2235","imageName":"paddlecloud-job",
"imageVersion":"container01","imageAddress":"","cpu":0.25,"memory":0.5,"workingDir":"working_dir","imagePullPolicy":"","commands":["command"],"args":["args"],
"ports":[{"port":8080,"protocol":"TCP"}],"volumeMounts":[{"mountPath":"/usr","readOnly":false,"name":"nfs"}],"envs":[{"key":"envs","value":"envs"}],
"userId":"bb45087dee674fcaa21d75b53a35f7fc","status":{"previousState":{"state":"Failed","startTime":"2019-05-14T16:33:48Z","exitCode":255,
"finishTime":"2019-05-14T16:33:58Z","detailStatus":"container is dead"},"currentState":{"state":"Running","startTime":"2019-05-14T16:34:08Z",
"detailStatus":"container is running"},"restartCount":1},"createdTime":"2019-04-29T14:39:37Z","updatedTime":"2019-04-29T14:39:37Z","deletedTime":null}],
"securityGroup":{"id":"28494","securityGroupId":"g-x7uc1wktj0y5","uuid":"1aaaadf9-9ce1-4140-86ad-da09ee7dbf64","name":"默认安全组","description":"default",
"tenantId":"3340a6f6ae1e48fdae1bd4bbce5b0ecb","associateNum":1,"vpcId":"ed82e80f-8e2a-40e3-804c-96d5eb040a53","vpcShortId":null,"creator":null},
"vpc":{"vpcId":"ed82e80f-8e2a-40e3-804c-96d5eb040a53","shortId":"vpc-cwtrgdc3cre4","name":"默认私有网络","cidr":"***********/16","status":0,"securityGroupNum":0,
"subnetNum":0,"createTime":"2018-12-27T11:22:51Z","description":"default","defaultVpc":true},"subnet":{"name":"系统预定义子网B",
"subnetId":"********-8818-4f95-bc19-4efb0e186119","az":"zoneB","cidr":"************/20","vpcId":"ed82e80f-8e2a-40e3-804c-96d5eb040a53",
"vpcShortId":"vpc-cwtrgdc3cre4","subnetUuid":"********-8818-4f95-bc19-4efb0e186119","accountId":"bb45087dee674fcaa21d75b53a35f7fc","subnetType":1,
"type":1,"createdTime":"2018-12-27T19:22:52Z","updatedTime":"2018-12-27T19:22:52Z","description":"","shortId":"sbn-km0v4stbk78b","usedIps":-1,
"totalIps":-1}}`, "p-1nJ2r9o1"))),
				},
			},
			args: args{
				podID: "p-1nJ2r9o1",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := setupTestEnv(tt.envs)
			defer tearDownTestEnv(s)

			c := NewClient(NewConfig(&bce.Config{
				Credentials: new(bce.Credentials),
				Checksum:    true,
				Endpoint:    s.URL,
			}))
			c.SetDebug(true)

			ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
			defer cancel()
			_, err := c.DescribePod(ctx, tt.args.podID, tt.args.opt)
			if (err != nil) != tt.wantErr {
				t.Errorf("client.DescribePod() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			// TODO assert describe resp
		})
	}
}

func TestUnmarshalBatchResponse(t *testing.T) {
	resp := `{"result":[{"name":"testutils-zuoyebang-nginx-testutils-585594754d-grgn8-1","podId":"p-ptpz6v4y",` +
		`"podUuid":"2ae59095-1828-4574-b0b1-623805cf152c","status":"Running","nodeName":"**********","delayReleaseDurationMinute":0,` +
		`"delayReleaseSucceeded":false,"memory":16.0,"cpuType":"","productType":"PostPay","gpuType":"","gpuCount":0.0,"gpuMemory":0.0,` +
		`"eipUuid":"","eipId":"","publicIp":"","bandwidthInMbps":0,"eipRouteType":"","eipPayMethod":"","eipIsUserSpecified":false,` +
		`"cceUuid":"cce-20h6bz2g","internalIp":"************","securityGroupUuid":null,"restartPolicy":"Always",` +
		`"orderId":"93a481971c3244ab973e1ac878d52e3d","createdTime":"2024-10-18T02:50:24Z","updatedTime":"2024-10-18T02:51:50Z","deletedTime":null,` +
		`"description":"","userId":"eca97e148cb74e9683d7b7240829d1ff","taskStatus":"","nfs":[],"pfs":[],"bos":[],"hostPath":[],"emptyDir":[],` +
		`"podVolumes":[],"configFile":[],"containers":[{"name":"istio-proxy","containerType":"workload",` +
		`"containerUuid":"containerd://bd5c3ecc7c8e70cc30803683805070294f45cc43ae20d7604d2cbead6edbf5c0","imageName":"proxyv2",` +
		`"imageVersion":"1.14.6-baidu","imageID":"sha256:789e9a405e48d02412ec5e3dac886c37474bf485fbbbf66f7351e00a8d5d9cf5",` +
		`"imageAddress":"registry.baidubce.com/online-csm/proxyv2","cpu":2.0,"memory":1.0,"gpuType":null,"gpuCount":0.0,"gpuMemory":0.0,"workingDir":"",` +
		`"imagePullPolicy":"IfNotPresent","commands":[],"args":["proxy","sidecar","--domain","$(POD_NAMESPACE).svc.cluster.local",` +
		`"--proxyLogLevel=warning","--proxyComponentLogLevel=misc:error","--log_output_level=default:info","--concurrency","2"],` +
		`"ports":[{"port":15090,"protocol":"TCP","name":"http-envoy-prom"}],"volumeMounts":[],` +
		`"envs":[{"key":"CA_ADDR","value":"*************:15012","valueFrom":null},{"key":"ISTIO_META_POD_PORTS","value":"[\n]","valueFrom":null},` +
		`{"key":"ISTIO_META_APP_CONTAINERS","value":"pod-perf-testutils","valueFrom":null},{"key":"KUBERNETES_PORT_443_TCP","value":"tcp://**********:443",` +
		`"valueFrom":null},{"key":"ISTIO_META_INTERCEPTION_MODE","value":"REDIRECT","valueFrom":null},{"key":"ISTIO_META_NETWORK","value":"gz",` +
		`"valueFrom":null},{"key":"KUBERNETES_PORT","value":"tcp://**********:443","valueFrom":null},{"key":"ISTIO_META_OWNER",` +
		`"value":"kubernetes://apis/apps/v1/namespaces/testutils/deployments/zuoyebang-nginx-testutils","valueFrom":null},` +
		`{"key":"KUBERNETES_SERVICE_PORT_HTTPS","value":"443","valueFrom":null},{"key":"INSTANCE_IP","value":"",` +
		`"valueFrom":{"configMapKeyRef":null,"fieldRef":{"apiVersion":"","fieldPath":"status.podIP"},"resourceFieldRef":null,"secretKeyRef":null}},` +
		`{"key":"PROXY_CONFIG","value":"{\"discoveryAddress\":\"*************:15012\",` +
		`\"tracing\":{\"zipkin\":{\"address\":\"zipkin.istio-system-csm-csbsr5ev:9411\"},\"sampling\":1},\"meshId\":\"csm-csbsr5ev\",` +
		`\"holdApplicationUntilProxyStarts\":true}\n","valueFrom":null},{"key":"KUBERNETES_SERVICE_HOST","value":"**********","valueFrom":null},` +
		`{"key":"JWT_POLICY","value":"third-party-jwt","valueFrom":null},{"key":"POD_NAME","value":"zuoyebang-nginx-testutils-585594754d-grgn8",` +
		`"valueFrom":null},{"key":"POD_NAMESPACE","value":"testutils","valueFrom":null},{"key":"ISTIO_META_MESH_ID","value":"csm-csbsr5ev",` +
		`"valueFrom":null},{"key":"KUBERNETES_PORT_443_TCP_PROTO","value":"tcp","valueFrom":null},{"key":"KUBERNETES_PORT_443_TCP_ADDR",` +
		`"value":"**********","valueFrom":null},{"key":"PILOT_CERT_PROVIDER","value":"istiod","valueFrom":null},{"key":"ISTIO_META_WORKLOAD_NAME",` +
		`"value":"zuoyebang-nginx-testutils","valueFrom":null},{"key":"TRUST_DOMAIN","value":"cluster.local","valueFrom":null},` +
		`{"key":"KUBERNETES_PORT_443_TCP_PORT","value":"443","valueFrom":null},{"key":"SERVICE_ACCOUNT","value":"default","valueFrom":null},` +
		`{"key":"ISTIO_META_CLUSTER_ID","value":"gz-cce-20h6bz2g","valueFrom":null},{"key":"KUBERNETES_SERVICE_PORT","value":"443","valueFrom":null}],` +
		`"userId":"eca97e148cb74e9683d7b7240829d1ff","status":{"previousState":{"exitCode":0},"currentState":{"state":"Running",` +
		`"containerStartTime":"2024-10-18T02:50:53Z","exitCode":0},"restartCount":0,"ready":false,"started":null},"createdTime":"2024-10-18T02:50:24Z",` +
		`"updatedTime":"2024-10-18T02:51:49Z","deletedTime":null},{"name":"pod-perf-testutils","containerType":"workload",` +
		`"containerUuid":"containerd://284744b71566900def8714b4474bce1fe1f026df932f5b98bb7e7b7f77dedc9c","imageName":"nginx","imageVersion":"1.23.0",` +
		`"imageID":"ccr-3928mxvp-vpc.cnc.gz.baidubce.com/transfer/eca97e148cb74e9683d7b7240829d1ff/registry.baidubce.com/qatest/nginx@sha256:2b5cb20bd4c` +
		`1be0ca9e6420715285b570cc2cf4f54f09eff6076986225af644f","imageAddress":"registry.baidubce.com/qatest/nginx","cpu":5.0,"memory":13.0,"gpuType":null,` +
		`"gpuCount":0.0,"gpuMemory":0.0,"workingDir":"","imagePullPolicy":"IfNotPresent","commands":[],"args":[],"ports":[],"volumeMounts":[],` +
		`"envs":[{"key":"KUBERNETES_PORT_443_TCP_PROTO","value":"tcp","valueFrom":null},{"key":"KUBERNETES_PORT_443_TCP_PORT","value":"443",` +
		`"valueFrom":null},{"key":"KUBERNETES_PORT_443_TCP_ADDR","value":"**********","valueFrom":null},{"key":"KUBERNETES_SERVICE_HOST",` +
		`"value":"**********","valueFrom":null},{"key":"KUBERNETES_SERVICE_PORT","value":"443","valueFrom":null},{"key":"KUBERNETES_SERVICE_PORT_HTTPS",` +
		`"value":"443","valueFrom":null},{"key":"KUBERNETES_PORT","value":"tcp://**********:443","valueFrom":null},{"key":"KUBERNETES_PORT_443_TCP",` +
		`"value":"tcp://**********:443","valueFrom":null}],"userId":"eca97e148cb74e9683d7b7240829d1ff","status":{"previousState":{"exitCode":0},` +
		`"currentState":{"state":"Running","containerStartTime":"2024-10-18T02:51:48Z","exitCode":0},"restartCount":0,"ready":false,"started":null},` +
		`"createdTime":"2024-10-18T02:50:24Z","updatedTime":"2024-10-18T02:51:49Z","deletedTime":null},{"name":"istio-init","containerType":"init",` +
		`"containerUuid":"containerd://4e5f84bc6ad3ecd8e885d12798efcb9b55f1ad04b92ed05f7c3f1ed13f7e8a7f","imageName":"proxyv2",` +
		`"imageVersion":"1.14.6-baidu","imageID":"sha256:789e9a405e48d02412ec5e3dac886c37474bf485fbbbf66f7351e00a8d5d9cf5",` +
		`"imageAddress":"registry.baidubce.com/online-csm/proxyv2","cpu":2.0,"memory":1.0,"gpuType":null,"gpuCount":0.0,"gpuMemory":0.0,"workingDir":"",` +
		`"imagePullPolicy":"IfNotPresent","commands":[],"args":["istio-iptables","-p","15001","-z","15006","-u","1337","-m",` +
		`"REDIRECT","-i","*","-x","","-b","*","-d","15090,15021,15020"],"ports":[],"volumeMounts":[],"envs":[{"key":"KUBERNETES_PORT_443_TCP_ADDR",` +
		`"value":"**********","valueFrom":null},{"key":"KUBERNETES_SERVICE_HOST","value":"**********","valueFrom":null},{"key":"KUBERNETES_SERVICE_PORT",` +
		`"value":"443","valueFrom":null},{"key":"KUBERNETES_SERVICE_PORT_HTTPS","value":"443","valueFrom":null},{"key":"KUBERNETES_PORT",` +
		`"value":"tcp://**********:443","valueFrom":null},{"key":"KUBERNETES_PORT_443_TCP","value":"tcp://**********:443","valueFrom":null},` +
		`{"key":"KUBERNETES_PORT_443_TCP_PROTO","value":"tcp","valueFrom":null},{"key":"KUBERNETES_PORT_443_TCP_PORT","value":"443","valueFrom":null}],` +
		`"userId":"eca97e148cb74e9683d7b7240829d1ff","status":{"previousState":{"exitCode":0},"currentState":{"state":"Succeeded",` +
		`"containerStartTime":"2024-10-18T02:50:28Z","detailStatus":"Completed","exitCode":0,"containerFinishTime":"2024-10-18T02:50:28Z"},` +
		`"restartCount":0,"ready":false,"started":null},"createdTime":"2024-10-18T02:50:24Z","updatedTime":"2024-10-18T02:51:49Z","deletedTime":null},` +
		`{"name":"kube-proxy-edeec1","containerType":"workload",` +
		`"containerUuid":"containerd://2d6405b5cc845eac981da4a1a8d4bd9c138e25744389befca7bfd5cc539dc1da",` +
		`"imageName":"kube-proxy-amd64-with-iptables-ensurer","imageVersion":"v1.16.8-v2-2024080624",` +
		`"imageID":"registry.baidubce.com/cce-plugin-pro/kube-proxy-amd64-with-iptables-ensurer@sha256:45f2c319fc062fa396e5c10ff3f1f539b4fae475203aaf3b4416` +
		`96dd316a1583","imageAddress":"registry.baidubce.com/cce-plugin-pro/kube-proxy-amd64-with-iptables-ensurer","cpu":0.25,"memory":0.5,"gpuType":null,` +
		`"gpuCount":0.0,"gpuMemory":0.0,"workingDir":"","imagePullPolicy":"Always","commands":["/bin/sh"],"args":["-c","iptables-ensurer && kube-proxy ` +
		`--bind-address=$MY_IP --cluster-cidr= --proxy-mode=iptables --masquerade-all=false --hostname-override=$MY_IP --kubeconfig=/conf/kube-proxy.conf ` +
		`--master= --healthz-bind-address=127.0.0.1 --healthz-port=10256 --logtostderr=true --v=6 --conntrack-tcp-timeout-established=0s ` +
		`--conntrack-tcp-timeout-close-wait=0s --conntrack-max-per-core=0"],"ports":[],"volumeMounts":[],"envs":[{"key":"MY_IP","value":"","valueFrom":null},` +
		`{"key":"SERVICEMESH_ENABLE","value":"true","valueFrom":null}],"userId":"eca97e148cb74e9683d7b7240829d1ff","status":{"previousState":{"exitCode":0},` +
		`"currentState":{"state":"Running","containerStartTime":"2024-10-18T02:50:29Z","exitCode":0},"restartCount":0,"ready":false,"started":null},` +
		`"createdTime":"2024-10-18T02:50:24Z","updatedTime":"2024-10-18T02:51:49Z","deletedTime":null}],"securityGroup":null,"securityGroups":null,` +
		`"vpc":null,"subnet":null,"logicalZone":"","region":"","subnetType":"","eipGroupId":"","labels":[{"labelKey":"UID",` +
		`"labelValue":"2e5440b9-bd8c-4191-ae00-0f89b25b94d7"},{"labelKey":"PodName","labelValue":"zuoyebang-nginx-testutils-585594754d-grgn8"},` +
		`{"labelKey":"Namespace","labelValue":"testutils"},{"labelKey":"NodeName","labelValue":"bci-virtual-kubelet-0"},{"labelKey":"CCEClusterID",` +
		`"labelValue":"cce-20h6bz2g"},{"labelKey":"CreationTimestamp","labelValue":"**********"},{"labelKey":"bci.virtual-kubelet.io/hided-containers",` +
		`"labelValue":"[\"kube-proxy-edeec1\"]"},{"labelKey":"bci.virtual-kubelet.io/kubeproxy-container","labelValue":"kube-proxy-edeec1"},` +
		`{"labelKey":"bci.virtual-kubelet.io/kubeproxy-healthz-address","labelValue":"127.0.0.1:10256"},{"labelKey":"vk.bci.baidu.com/net-admin",` +
		`"labelValue":"[\"kube-proxy-edeec1\"]"}],"v2":true,"application":"default","pushLog":false,"conditions":[` +
		`{"lastTransitionTime":"2024-10-18T02:50:29Z","status":"True","type":"Initialized"},{"lastTransitionTime":"2024-10-18T02:51:49Z","status":"True",` +
		`"type":"Ready"},{"lastTransitionTime":"2024-10-18T02:51:49Z","status":"True","type":"ContainersReady"},{` +
		`"lastTransitionTime":"2024-10-18T02:50:26Z","status":"True","type":"PodScheduled"}],"affinity":null,"terminationGracePeriodSeconds":null,` +
		`"extras":{"testutils/zuoyebang-nginx-testutils-585594754d-grgn8/istio-token/default/43200/2e5440b9-bd8c-4191-ae00-0f89b25b94d7/istio-ca":` +
		`"**********","testutils/zuoyebang-nginx-testutils-585594754d-grgn8/kube-api-access-l98z8/default/3607/2e5440b9-bd8c-4191-ae00-0f89b25b94d7":` +
		`"**********"},"tidal":false,"vCpu":8.0,"tags":null}]}`
	responseStruct := BatchDescribeResponse{}
	err := json.Unmarshal([]byte(resp), &responseStruct)
	println(responseStruct.Result[0].PodID)
	assert.Nil(t, err)
}

func TestDescribePodWithDeleted(t *testing.T) {
	type args struct {
		podID string
		opt   *bce.SignOption
	}
	tests := []struct {
		name    string
		envs    []*testEnv
		args    args
		want    *DescribePodResponse
		wantErr bool
	}{
		// TODO: Add testutils cases.
		{
			name: "normal case",
			envs: []*testEnv{
				{
					method: "GET",
					path:   "/api/logical/bci/v1/pod/{podID}/describe/deleted",
					handler: newHandler(http.StatusOK, []byte(fmt.Sprintf(`{"name":"pod","podId":"%s","podUuid":"ac3bf5ff-336d-4475-9442-900eb88a7a4a",
"status":"Pending","vCpu":0,"memory":0.5,"eipUuid":"","publicIp":"","bandwidthInMbps":0,"cceUuid":"","internalIp":"",
"securityGroupUuid":"1aaaadf9-9ce1-4140-86ad-da09ee7dbf64","restartPolicy":"Always","orderId":"15c81ac9-c917-45de-8d81-3744be6fc4a6",
"createdTime":"2019-04-29T14:39:37Z","updatedTime":"2019-04-29T14:39:37Z","deletedTime":null,"description":"","userId":"bb45087dee674fcaa21d75b53a35f7fc",
"taskStatus":"","nfs":[{"name":"config","configFiles":[{"path":"pod/","file":"file"}]}],"emptyDir":[{"name":"empty_dir"}],"configFile":[],"tags":[],
"containers":[{"name":"container01","containerUuid":"16151de01cefd87e78322709ac42b12e20254439d0679d1282ac05645cab2235","imageName":"paddlecloud-job",
"imageVersion":"container01","imageAddress":"","cpu":0.25,"memory":0.5,"workingDir":"working_dir","imagePullPolicy":"","commands":["command"],"args":["args"],
"ports":[{"port":8080,"protocol":"TCP"}],"volumeMounts":[{"mountPath":"/usr","readOnly":false,"name":"nfs"}],"envs":[{"key":"envs","value":"envs"}],
"userId":"bb45087dee674fcaa21d75b53a35f7fc","status":{"previousState":{"state":"Failed","startTime":"2019-05-14T16:33:48Z","exitCode":255,
"finishTime":"2019-05-14T16:33:58Z","detailStatus":"container is dead"},"currentState":{"state":"Running","startTime":"2019-05-14T16:34:08Z",
"detailStatus":"container is running"},"restartCount":1},"createdTime":"2019-04-29T14:39:37Z","updatedTime":"2019-04-29T14:39:37Z","deletedTime":null}],
"securityGroup":{"id":"28494","securityGroupId":"g-x7uc1wktj0y5","uuid":"1aaaadf9-9ce1-4140-86ad-da09ee7dbf64","name":"默认安全组","description":"default",
"tenantId":"3340a6f6ae1e48fdae1bd4bbce5b0ecb","associateNum":1,"vpcId":"ed82e80f-8e2a-40e3-804c-96d5eb040a53","vpcShortId":null,"creator":null},
"vpc":{"vpcId":"ed82e80f-8e2a-40e3-804c-96d5eb040a53","shortId":"vpc-cwtrgdc3cre4","name":"默认私有网络","cidr":"***********/16","status":0,"securityGroupNum":0,
"subnetNum":0,"createTime":"2018-12-27T11:22:51Z","description":"default","defaultVpc":true},"subnet":{"name":"系统预定义子网B",
"subnetId":"********-8818-4f95-bc19-4efb0e186119","az":"zoneB","cidr":"************/20","vpcId":"ed82e80f-8e2a-40e3-804c-96d5eb040a53",
"vpcShortId":"vpc-cwtrgdc3cre4","subnetUuid":"********-8818-4f95-bc19-4efb0e186119","accountId":"bb45087dee674fcaa21d75b53a35f7fc","subnetType":1,
"type":1,"createdTime":"2018-12-27T19:22:52Z","updatedTime":"2018-12-27T19:22:52Z","description":"","shortId":"sbn-km0v4stbk78b","usedIps":-1,
"totalIps":-1}}`, "p-1nJ2r9o1"))),
				},
			},
			args: args{
				podID: "p-1nJ2r9o1",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := setupTestEnv(tt.envs)
			defer tearDownTestEnv(s)

			c := NewClient(NewConfig(&bce.Config{
				Credentials: new(bce.Credentials),
				Checksum:    true,
				Endpoint:    s.URL,
			}))
			c.SetDebug(true)

			ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
			defer cancel()
			_, err := c.DescribePodWithDeleted(ctx, tt.args.podID, tt.args.opt)
			if (err != nil) != tt.wantErr {
				t.Errorf("client.DescribePod() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			// TODO assert describe resp
		})
	}
}
