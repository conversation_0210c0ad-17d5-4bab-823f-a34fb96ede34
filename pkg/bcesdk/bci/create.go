package bci

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/utils"
)

type PaymentMethod struct {
	Type   string `json:"type"`
	Values string `json:"values"`
}

type CreatePodResponse struct {
	OrderID string   `json:"orderId"`
	PodIDs  []string `json:"podIds"`
}

type CreatePodArgs struct {
	PaymentMethod []PaymentMethod `json:"paymentMethod"`
	Items         []CreatePodItem `json:"items"`
}

type CreatePodItem struct {
	PaymentMethod []PaymentMethod `json:"paymentMethod"`
	Config        *PodOrEIPConfig `json:"config,omitempty"`
}

type PodOrEIPConfig struct {
	EIP  *EIPConfig
	Pod  *PodConfig
	Type ServiceType `valid:"Required"`
}

func (cfg *PodOrEIPConfig) MarshalJSON() ([]byte, error) {
	switch cfg.Type {
	case ServiceTypeBCI:
		return json.Marshal(cfg.Pod)
	case ServiceTypeEIP:
		return json.Marshal(cfg.EIP)
	default:
		return []byte{}, errors.New("unknown PodOrEIPConfig.Type")
	}
}

func (cfg *PodOrEIPConfig) UnmarshalJSON(data []byte) error {
	tmp := make(map[string]interface{})
	if err := json.Unmarshal(data, &tmp); err != nil {
		return err
	}
	serviceType, ok := tmp["serviceType"]
	if !ok {
		return errors.New("serviceType not found")
	}
	serviceTypeStr, ok := serviceType.(string)
	if !ok {
		return errors.New("serviceType is not string")
	}
	switch serviceTypeStr {
	case string(ServiceTypeBCI):
		podCfg := new(PodConfig)
		if err := json.Unmarshal(data, podCfg); err != nil {
			return err
		}
		cfg.Type = ServiceTypeBCI
		cfg.Pod = podCfg
	case string(ServiceTypeEIP):
		eipCfg := new(EIPConfig)
		if err := json.Unmarshal(data, eipCfg); err != nil {
			return err
		}
		cfg.Type = ServiceTypeEIP
		cfg.EIP = eipCfg
	default:
		return fmt.Errorf("illegal config: unknown serviceType %s", serviceType)
	}
	return nil
}

func (args *CreatePodArgs) validate() error {
	if len(args.Items) == 0 {
		return errors.New("pod to create cannot be empty")
	}

	for _, item := range args.Items {
		cfg := item.Config
		if err := utils.Valid(cfg); err != nil {
			return err
		}
		if cfg.Pod != nil {
			if err := validateLogCollections(cfg.Pod.Containers); err != nil {
				return err
			}
		}
	}
	return nil
}

// validateLogCollections validates log collections of all containers.
func validateLogCollections(containers []Container) error {
	// Check if log collection indexes is duplicated among containers.
	logCollectionIndexes := make(map[int]string)
	for _, c := range containers {
		for _, collection := range c.LogCollections {
			if cName, ok := logCollectionIndexes[collection.Index]; !ok {
				logCollectionIndexes[collection.Index] = c.Name
			} else {
				return fmt.Errorf("duplicated log collection index %d between containers %s and %s",
					collection.Index, cName, c.Name)
			}
		}
	}
	return nil
}

func generateVolumeTypes(cfg *PodConfig) error {
	nameTypeMap := make(map[string]VolumeType,
		len(cfg.Volumes.ConfigFile)+len(cfg.Volumes.EmptyDir)+len(cfg.Volumes.NFS))
	for _, v := range cfg.Volumes.ConfigFile {
		nameTypeMap[v.Name] = VolumeTypeConfigFile
	}
	for _, v := range cfg.Volumes.EmptyDir {
		nameTypeMap[v.Name] = VolumeTypeEmptyDir
	}
	for _, v := range cfg.Volumes.NFS {
		nameTypeMap[v.Name] = VolumeTypeNFS
	}
	for _, v := range cfg.Volumes.FlexVolume {
		nameTypeMap[v.Name] = VolumeTypeFlexVolume
	}
	for _, v := range cfg.Volumes.PFS {
		nameTypeMap[v.Name] = VolumeTypePFS
	}
	for _, v := range cfg.Volumes.HostPath {
		nameTypeMap[v.Name] = VolumeTypeHostPath
	}
	for _, v := range cfg.Volumes.BOS {
		nameTypeMap[v.Name] = VolumeTypeBOS
	}
	for _, v := range cfg.Volumes.CephFS {
		nameTypeMap[v.Name] = VolumeTypeCephFS
	}

	for cIndex, c := range cfg.Containers {
		for vIndex, v := range c.VolumeMounts {
			volumeType, ok := nameTypeMap[v.Name]
			if !ok {
				return fmt.Errorf("no volume is found for volumeMount %s in container %s", v.Name, c.Name)
			}
			cfg.Containers[cIndex].VolumeMounts[vIndex].Type = volumeType
		}
	}
	return nil
}

const (
	MaxEIPNameLength = 65
)

func NewEIPConfig(ctx context.Context, name, region, routeType string, bandwidth int, billigMethod string, v2 bool) *EIPConfig {
	if v2 {
		subProductType := V2SubProductTypeByTraffic
		// Only ByBandwidth is supported for non-BGP EIP.
		if routeType == "BGP_S" && billigMethod != "" {
			subProductType = SubProductType(billigMethod)

		} else if routeType != "BGP" && routeType != "" {
			subProductType = V2SubProductTypeByBandwidth
		}
		return &EIPConfig{
			Name:            name,
			Region:          region,
			BandwidthInMbps: bandwidth,
			ServiceType:     ServiceTypeEIP,
			PurchaseNum:     1,
			SubProductType:  subProductType,
			ProductType:     V2EIPProductTypePostPay,
			RouteType:       routeType,
		}
	}

	return &EIPConfig{
		Name:            name,
		Region:          region,
		BandwidthInMbps: bandwidth,
		ServiceType:     ServiceTypeEIP,
		PurchaseNum:     1,
		SubProductType:  SubProductTypeNetraffic,
		ProductType:     EIPProductTypePostPay,
	}
}

func (c *client) CreatePod(ctx context.Context, cfg *PodConfig, eipCfg *EIPConfig, opt *bce.SignOption) (*CreatePodResponse, error) {
	if cfg == nil {
		return nil, errors.New("pod config cannot be nil")
	}
	ctx = logger.EnsureRequestIDInCtx(ctx)
	err := generateVolumeTypes(cfg)
	if err != nil {
		return nil, err
	}
	// set application to default if not exists (DefaultApplication is the default value)
	if cfg.Application == "" {
		cfg.Application = DefaultApplication
	}

	args := &CreatePodArgs{
		Items: []CreatePodItem{
			{
				Config: &PodOrEIPConfig{
					Type: ServiceTypeBCI,
					Pod:  cfg,
				},
			},
		},
	}
	// append eip config if necessary
	if eipCfg != nil {
		args.Items = append(args.Items, CreatePodItem{
			Config: &PodOrEIPConfig{
				Type: ServiceTypeEIP,
				EIP:  eipCfg,
			},
		})
	}
	err = args.validate()
	if err != nil {
		return nil, err
	}

	clientToken := cfg.ClientToken
	if clientToken == "" {
		clientToken = c.GenerateClientToken()
	}

	params := map[string]string{
		"clientToken": clientToken,
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest(
		"POST", c.GetURL("api/logical/bci/v1/pod/create", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	createPodResponse := new(CreatePodResponse)
	err = json.Unmarshal(bodyContent, createPodResponse)
	if err != nil {
		return nil, err
	}

	return createPodResponse, nil
}
