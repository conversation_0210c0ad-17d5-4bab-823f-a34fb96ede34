package bci

import (
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"
)

type UserVersionResponse struct {
	AccountID string `json:"accountID"`
	IsV2      bool   `json:"isv2"`
}

func (c *client) GetUserVersion(ctx context.Context, opt *bce.SignOption) (*UserVersionResponse, error) {
	ctx = logger.EnsureRequestIDInCtx(ctx)
	req, err := bce.NewRequest("GET", c.GetURL("api/logical/bci/v2/pod/userVersion", nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	uv := new(UserVersionResponse)
	err = json.Unmarshal(bodyContent, uv)
	if err != nil {
		return nil, err
	}
	return uv, nil
}
