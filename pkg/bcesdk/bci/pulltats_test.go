package bci

import (
	"context"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"testing"
	"time"

	"github.com/gorilla/mux"
	"gotest.tools/assert"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/testutil"
)

func TestClientCreatePullTask(t *testing.T) {
	type args struct {
		envs []*testutil.TestEnv

		ctx  context.Context
		args *PullTaskArgs
		opt  *bce.SignOption

		want *CreatePullTaskResponse
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "normal case",
			args: func() args {
				taskID := "be8a38d3-23e7-4aa1-9541-431cb43f7123"
				want := []byte(`{"taskId":"` + taskID + `"}`)
				wantArgs := &PullTaskArgs{
					Image: "python:slim",
				}
				return args{
					envs: []*testutil.TestEnv{
						{
							Method: "POST",
							Path:   "/api/logical/bci/v1/pod/imageCache",
							Handler: func(w http.ResponseWriter, r *http.Request) {
								body, err := ioutil.ReadAll(r.Body)
								if err != nil {
									t.Fatal(err)
								}
								gotArgs := new(PullTaskArgs)
								if err := json.Unmarshal(body, gotArgs); err != nil {
									t.Fatal(err)
								}
								assert.DeepEqual(t, gotArgs, wantArgs)
								w.Header().Set("Content-Type", "application/json; charset=utf-8")
								w.WriteHeader(http.StatusOK)
								_, _ = w.Write(want)
							},
						},
					},
					ctx:  context.TODO(),
					args: wantArgs,
					want: &CreatePullTaskResponse{taskID},
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := testutil.SetupTestEnv(tt.args.envs)
			defer testutil.TearDownTestEnv(s)

			c := NewClient(NewConfig(&bce.Config{
				Credentials: new(bce.Credentials),
				Checksum:    true,
				Endpoint:    s.URL,
			}))
			c.SetDebug(true)

			got, err := c.CreatePullTask(tt.args.ctx, tt.args.args, tt.args.opt)
			if (err != nil) != tt.wantErr {
				t.Errorf("client.CreatePullTask() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.DeepEqual(t, got, tt.args.want)
		})
	}
}

func TestClientQueryPullTask(t *testing.T) {
	type args struct {
		envs []*testutil.TestEnv

		ctx    context.Context
		taskID string
		opt    *bce.SignOption

		want *QueryPullTaskResponse
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "normal case",
			args: func() args {
				taskID := "be8a38d3-23e7-4aa1-9541-431cb43f7123"
				want := []byte(`{"status":"doing"}`)
				return args{
					envs: []*testutil.TestEnv{
						{
							Method: "GET",
							Path:   "/api/logical/bci/v1/pod/imageCache/{taskID}",
							Handler: func(w http.ResponseWriter, r *http.Request) {
								vars := mux.Vars(r)
								assert.Equal(t, vars["taskID"], taskID)
								w.Header().Set("Content-Type", "application/json; charset=utf-8")
								w.WriteHeader(http.StatusOK)
								_, _ = w.Write(want)
							},
						},
					},
					ctx:    context.TODO(),
					taskID: taskID,
					want:   &QueryPullTaskResponse{PullTaskStatusDoing},
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := testutil.SetupTestEnv(tt.args.envs)
			defer testutil.TearDownTestEnv(s)

			c := NewClient(NewConfig(&bce.Config{
				Credentials: new(bce.Credentials),
				Checksum:    true,
				Endpoint:    s.URL,
			}))
			c.SetDebug(true)

			got, err := c.QueryPullTask(tt.args.ctx, tt.args.taskID, tt.args.opt)
			if (err != nil) != tt.wantErr {
				t.Errorf("client.QueryPullTask() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.DeepEqual(t, got, tt.args.want)
		})
	}
}

func testSandboxPullTask(t *testing.T) {
	c := NewClient(NewConfig(&bce.Config{
		Credentials: bce.NewCredentials("795dbfd411274f22b303a6865940680a", "4c47c842ee0c4bbb920ec9e5251e60cc"), //qa bae4auto
		Region:      "qa",
		Checksum:    true,
		Timeout:     30 * time.Second,
	}))
	c.SetDebug(true)
	ctx := context.TODO()

	var taskIDs []string
	for i := 0; i < 10; i++ {
		resp, err := c.CreatePullTask(ctx, &PullTaskArgs{Image: "python:slim"}, nil)
		if err != nil {
			t.Fatal(err)
		}
		taskIDs = append(taskIDs, resp.TaskID)
	}

	type terminatedTask struct {
		Status PullTaskStatus
		Round  int
	}

	terminatedTasks := make(map[string]terminatedTask, len(taskIDs))
	for i := 0; i < 30; i++ {
		t.Logf("round %d: %d tasks have terminated, remaining %d",
			i, len(terminatedTasks), len(taskIDs)-len(terminatedTasks))
		<-time.After(10 * time.Second)
		for _, taskID := range taskIDs {
			if _, ok := terminatedTasks[taskID]; ok {
				continue
			}
			result, err := c.QueryPullTask(ctx, taskID, nil)
			if err != nil {
				t.Fatal(err)
			}
			if result.Status == PullTaskStatusDoing {
				continue
			}
			t.Logf("task %s terminated with status=%v", taskID, result.Status)
			terminatedTasks[taskID] = terminatedTask{
				Status: result.Status,
				Round:  i,
			}
		}
		if len(terminatedTasks) == len(taskIDs) {
			t.Logf("all tasks have terminated: %+v", terminatedTasks)
			break
		}
	}
}
