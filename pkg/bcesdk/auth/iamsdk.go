package auth

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	http "net/http"
	"net/url"
	"strings"

	iamsdk "icode.baidu.com/baidu/bce-iam/sdk-go/iam"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"
)

// Convert IAMSDK Token  to CCE auth.Token, 与iam.Token 不同，不能共用
func ConvertIAMSDKTokenToCCEToken(tokenResponse *iamsdk.Token) *Token {
	token := &Token{
		ID: tokenResponse.ID,
		User: &User{
			Domain: &Domain{
				Name: tokenResponse.User.Domain.Name,
				ID:   tokenResponse.User.Domain.ID,
			},
			ID:   tokenResponse.User.ID,
			Name: tokenResponse.User.Name,
		},
	}
	return token
}

func ConvertResquestInfoFormToRequest(args *RequestInfoForm) (*http.Request, error) {
	values := url.Values(map[string][]string(args.Parameters))

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	body := strings.NewReader(string(postContent))

	netRequest := &http.Request{
		Host:   args.SignatureHeaders["host"],
		Method: args.Method,
		URL: &url.URL{
			Path:     args.URI,
			RawQuery: values.Encode(),
			Host:     args.SignatureHeaders["host"],
		},
		Header: make(http.Header),
		Body:   io.NopCloser(body),
	}
	for k, v := range args.SignatureHeaders {
		netRequest.Header.Add(k, v)
	}

	return netRequest, nil
}

// Authentication 认证
func (c *IAMSDKClient) Authentication(ctx context.Context, args *RequestInfoForm, options *bce.SignOption) (*ResponseAuth, error) {
	logger.Infof(ctx, "authentication by IAM SDK: use ValidatorRequest to get and validate token")

	netRequest, err := ConvertResquestInfoFormToRequest(args)
	if err != nil {
		logger.Errorf(ctx, "convert request info form to request error: %s", err)
		return nil, err
	}

	tokenResponse, err := c.IAMBCEClient.ValidatorRequest(netRequest)
	if err != nil {
		logger.Errorf(ctx, "authentication failed: ValidatorRequest error: %s", err)
		return nil, err
	}

	// Convert IAMSDK Token  to CCE Token
	token := ConvertIAMSDKTokenToCCEToken(tokenResponse)

	logger.Infof(ctx, "authentication Result: TokenID: %s, UserID: %s, UserName: %s, UserDomainID: %s, UserDomainName: %s",
		token.ID, token.User.ID, token.User.Name, token.User.Domain.ID, token.User.Domain.Name)

	authResp := ResponseAuth{
		Code:   fmt.Sprintf("%d", http.StatusOK),
		Result: token,
		Msg:    "",
	}

	return &authResp, nil
}

// Authorization 认证 + 鉴权
func (c *IAMSDKClient) Authorization(ctx context.Context, args *RequestInfoForm, options *bce.SignOption) (*ResponseAuth, error) {
	// ensure ServiceIAMPermissionCheckName is not empty
	if c.ServiceIAMPermissionCheckName == "" {
		c.ServiceIAMPermissionCheckName = DefaultServiceIAMPermissionCheckName
	}

	permissionRequest := iamsdk.PermissionRequest{
		Service:       c.ServiceIAMPermissionCheckName,
		Region:        args.RegionID,
		Resource:      args.ResourceID,
		ResourceOwner: args.OwnerID,
		Permission:    args.Permissions,
	}

	// IAM condition
	if args.RequestContext != nil {
		permissionRequest.RequestContext = iamsdk.RequestContext{
			IPAddress: args.RequestContext.IPAddress,
			Referer:   args.RequestContext.Referer,
			Variables: make(map[string]any),
		}
		for k, v := range args.RequestContext.Conditions {
			permissionRequest.RequestContext.Variables[k] = v
		}
	}

	netRequest, err := ConvertResquestInfoFormToRequest(args)
	if err != nil {
		logger.Errorf(ctx, "convert request info form to request error: %s", err)
		return nil, err
	}

	logger.Infof(ctx, "authorization by IAM SDK: use AuthAndVerify to get token and verify permission")
	results, err := c.IAMBCEClient.AuthAndVerify(netRequest, permissionRequest)
	if err != nil {
		logger.Errorf(ctx, "call AuthAndVerify failed: %s", err)
		return nil, err
	}

	verifyResult := results.VerifyResult

	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/HXFtYvbMQj/rUkPar4o8W/mkYRHlDfaapnIt
	// 服务对用户的鉴权需要提供鉴权的三元组 (用户，资源，操作)。IAM会根据用户在IAM中的权限配置ACL来对当前用户的操作进行鉴权。
	// 返回的鉴权结果描述用户是否有权限对资源进行相应操作 (ALLOW || DENY || DEFAULT_DENY)
	if verifyResult.Effect == "DENY" || verifyResult.Effect == "DEFAULT_DENY" {
		return nil, fmt.Errorf("code: %d, verify permission denied: %s, service=%s, region=%s, "+
			"resource id=%s, resource owner=%s, permission={%v}, verify result={effect: %s, id: %s, eid: %s}",
			http.StatusForbidden, err, permissionRequest.Service, permissionRequest.Region, permissionRequest.Resource,
			permissionRequest.ResourceOwner, permissionRequest.Permission, verifyResult.Effect, verifyResult.ID, verifyResult.Eid)
	}

	tokenResponse := &results.Token
	token := ConvertIAMSDKTokenToCCEToken(tokenResponse)

	authResp := ResponseAuth{
		Code:   fmt.Sprintf("%d", http.StatusOK),
		Result: token,
		Msg:    "",
	}

	return &authResp, nil
}

// GenAuthorization 获取签名字符串，分为service和user两种类型，就是永久 和 临时 ，sts改造之后，服务号已经没有权限获取永久ak sk 来生成
func (c *IAMSDKClient) GenAuthorization(ctx context.Context, args *RequestSignForm, options *bce.SignOption) (*SignatureResponse, error) {
	params := map[string]string{}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("v1/iam/authorization")
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, options)
	if err != nil {
		return nil, err
	}

	var signature SignatureResponse
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(bodyContent, &signature)
	return &signature, err
}
