package auth

import (
	"context"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/auth Interface

// Interface  bce-api-auth 接口
type Interface interface {
	SetDebug(bool)
	// 认证
	Authentication(ctx context.Context, args *RequestInfoForm, options *bce.SignOption) (*ResponseAuth, error)
	// 认证 + 鉴权
	Authorization(ctx context.Context, args *RequestInfoForm, options *bce.SignOption) (*ResponseAuth, error)
	// 获取签名字符串，分为service和user两种类型，就是永久 和 临时 ，sts改造之后，服务号已经没有权限获取永久ak sk 来生成
	GenAuthorization(ctx context.Context, args *RequestSignForm, options *bce.SignOption) (*SignatureResponse, error)
}

// 认证、鉴权 接口参数
type RequestInfoForm struct {
	Authorization    string              `json:"authorization"`
	Method           string              `json:"method"`
	URI              string              `json:"uri"`
	Permissions      []string            `json:"permissions"`
	RegionID         string              `json:"regionId"`
	ResourceID       string              `json:"resourceId"`
	OwnerID          string              `json:"resourceOwner"`
	SignatureHeaders map[string]string   `json:"signatureHeaders"`
	Parameters       map[string][]string `json:"parameters"`
	RequestContext   *RequestContext     `json:"requestContext,omitempty"`
}

type RequestContext struct {
	IPAddress  string            `json:"ipAddress"`
	Referer    string            `json:"referer"`
	Conditions map[string]string `json:"conditions"`
}

// ResponseAuth 从bce-api-auth收到的Response
type ResponseAuth struct {
	Code   string `json:"code,omitemtpy"`    // 返回码
	Result *Token `json:"result,omitempty"`  // 结果
	Msg    string `json:"message,omitempty"` // 错误信息
}

// Token 认证返回的信息，包含用户信息
type Token struct {
	ID        string   `json:"id"`
	User      *User    `json:"user"`
	STSRole   *STSRole `json:"sts_role"`
	Assumer   string   `json:"assumer"`
	Methods   []string `json:"methods"`
	ExpiresAt string   `json:"expires_at"`
	IssuedAt  string   `json:"issued_at"`
}

// bce用户信息 ID 表示 userID
type User struct {
	ID     string  `json:"id,omitempty"`
	Name   string  `json:"name,omitempty"`
	Domain *Domain `json:"domain,omitempty"`
}

// ID accountID
type Domain struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name"`
}

type STSRole struct {
	ID     string `json:"id"`
	Name   string `json:"name"`
	Domain Domain `json:"domain"`
}

// 获取签名字串的接口参数
type RequestSignForm struct {
	Method           string              `json:"method"`
	URI              string              `json:"uri"`
	SignatureHeaders map[string]string   `json:"signatureHeaders"`
	Parameters       map[string][]string `json:"parameters"`
	AccountID        string              `json:"accountId"`
	AccountType      AccountType         `json:"accountType,omitempty"`
}

// 账号类型
type AccountType string

const (
	AccountTypeService AccountType = "service"
	AccountTypeUser    AccountType = "user"
)

// AuthInfo 认证信息
type InfoAuth struct {
	Authorization    string              `json:"authorization"`
	Method           string              `json:"method"`
	URI              string              `json:"uri"`
	SignatureHeaders map[string]string   `json:"signatureHeaders"`
	Parameters       map[string][]string `json:"parameters"`
}

// AuthStrResponse 从bce-api-auth计算认证字符串时获得的response
type SignatureResponse struct {
	Code   string     `json:"code,omitemtpy"`   // 返回码
	Result *Signature `json:"result,omitempty"` // 结果
}

// Signature STS 认证字符串
type Signature struct {
	Authorization string `json:"authorization"`
	SessionToken  string `json:"sessionToken"`
}
