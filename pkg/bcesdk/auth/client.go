package auth

import (
	iamsdk "icode.baidu.com/baidu/bce-iam/sdk-go/iam"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/retrypolicy"
)

// Endpoints defines the IAM endpoints
var Endpoints = map[string]string{
	"bj": "iam.bj.baidubce.com",
}

const (
	// ServiceName 默认值，防止bce.Config.ServiceName为空
	DefaultServiceName = "cce"

	// ServicePassword 默认值，防止bce.Config.ServicePassword为空
	DefaultServicePassword = "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt"

	// ServiceIAMPermissionCheckName，防止bce.Config.ServiceIAMPermissionCheckName为空
	DefaultServiceIAMPermissionCheckName = "bce:cce"
)

var _ Interface = &Client{}

var _ Interface = &IAMSDKClient{}

// 原Auth Client，访问/v1/iam API进行认证+鉴权
type Client struct {
	*bce.Client
}

// NewClient return AuthClient/IAMSDKClient
// AuthClient: accessing CCE v1/iam API for authentication and authorization
// IAMSDKClient: using IAM SDK with disaster tolerance
func NewClient(config *bce.Config) Interface {
	region := config.Region

	var defaultDisasterToleranceEnabled bool = false

	// init IAM Disaster Tolerance Config
	// iamDisasterToleranceEnabled: bce.Config传参的容灾开关
	// defaultDisasterToleranceEnabled: 容灾开关默认值
	iamDisasterToleranceEnabled := config.IAMDisasterToleranceEnabled
	if region != "" {
		defaultDisasterToleranceEnabled = iam.AgentEnabled[region]
	}

	// 传入参数和默认值只要有一个为true，开启IAM容灾
	// cce-stack涉及IAM Client调用较多，分布零散，避免部分存量或增量调用没有传入IAM容灾参数，使用默认值兜底，默认值为true开启IAM容灾
	if iamDisasterToleranceEnabled || defaultDisasterToleranceEnabled {
		agentEndpoint := config.IAMAgentEndpoint

		// ensure agentEndpoint is not empty
		if agentEndpoint == "" && region != "" {
			agentEndpoint = iam.AgentEndpoints[region]
		}

		agentConfig := &iamsdk.AgentConfiguration{
			Endpoint: iam.AddHTTPProtocol(agentEndpoint),
			IsEnable: true,
		}

		return NewIAMSDKClient(config, agentConfig)
	}
	return NewAuthClient(config)
}

func NewAuthClient(config *bce.Config) *Client {
	bceClient := bce.NewClient(config)
	return &Client{
		Client: bceClient,
	}
}

func (c *Client) GetURL(objectKey string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoints[c.GetRegion()]
	}
	uriPath := objectKey
	return c.Client.GetURL(host, uriPath, params)
}

// SDKClient is the IAM client implementation for Interface using IAM SDK with disaster tolerance
type IAMSDKClient struct {
	*bce.Client
	IAMBCEClient                  *iamsdk.BceClient
	ServiceIAMPermissionCheckName string
}

// NewSDKClient return IAM SDK client
func NewIAMSDKClient(config *bce.Config, agentConfig *iamsdk.AgentConfiguration) *IAMSDKClient {
	// convert retrypolicy.RetryPolicy to iamsdk.Retry
	var retry iamsdk.RetryPolicy = iamsdk.NewNoRetryPolicy()
	if config.RetryPolicy != nil {
		maxErrorRetry := config.RetryPolicy.GetMaxErrorRetry()
		maxDelay := config.RetryPolicy.GetMaxDelayInSeconds()
		_, isIntervalRetry := config.RetryPolicy.(*retrypolicy.IntervalRetryPolicy)

		if maxErrorRetry > 0 {
			if isIntervalRetry {
				// fixed delay
				retry = iamsdk.NewBackOffRetryPolicy(maxErrorRetry, int64(maxDelay*1000), int64(maxDelay*1000))
			} else {
				// increasing delay, from baseDelay to maxDelay
				retry = iamsdk.NewBackOffRetryPolicy(maxErrorRetry, int64(maxDelay*1000), int64(300))
			}
		}
	}

	region := config.Region
	iamEndpoint := config.IAMEndpoint
	serviceName := config.ServiceName
	servicePassword := config.ServicePassword
	serviceIAMPermissionCheckName := config.ServiceIAMPermissionCheckName

	// ensure iamEndpoint is not empty
	if iamEndpoint == "" && region != "" {
		iamEndpoint = iam.Endpoints[region]
	}

	// ensure ServiceName is not empty
	if serviceName == "" {
		serviceName = DefaultServiceName
	}

	// ensure ServicePassword is not empty
	if servicePassword == "" {
		servicePassword = DefaultServicePassword
	}

	// ensure ServiceIAMPermissionCheckName is not empty
	if serviceIAMPermissionCheckName == "" {
		serviceIAMPermissionCheckName = DefaultServiceIAMPermissionCheckName
	}

	// init IAM SDK client Config
	iamConfig := &iamsdk.BceClientConfiguration{
		// IAM SDK uses IAMEndpoint instead of AuthEndpoint
		// Endpoint for IAM SDK: with HTTP protocol and w/o version, e.g. http://iam.bj.baidubce.com
		Endpoint: iam.AddHTTPProtocol(iamEndpoint),
		Version:  "/v3",
		Domain:   "Default",
		Retry:    retry,
		Timeout:  int(config.Timeout.Seconds()),
		// ServiceName and Password are  passed from clientsConfig/providers.Config, originated from values-<region>.yaml
		UserName: serviceName,
		Password: servicePassword,
	}

	return &IAMSDKClient{
		Client:                        bce.NewClient(config),
		IAMBCEClient:                  iamsdk.NewBceClientWithAgent(iamConfig, agentConfig),
		ServiceIAMPermissionCheckName: serviceIAMPermissionCheckName,
	}
}

// GetURL generates the full URL of http request for Baidu Cloud  API.
func (c *IAMSDKClient) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = iam.AddHTTPProtocol(iam.Endpoints[c.GetRegion()])
	}
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}

// SetDebug open or close Debug Mode
func (c *IAMSDKClient) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}
