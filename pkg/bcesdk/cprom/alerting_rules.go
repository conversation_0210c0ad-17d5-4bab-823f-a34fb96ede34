// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
// 实现 CProm SDK Instance 相关方法

package cprom

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// ListAlertingRule - 获取报警规则列表
func (c *Client) ListAlertingRule(ctx context.Context, args *ListAlertingRuleRequest, opt *bce.SignOption) (*ListAlertingRuleResponse, error) {
	var params map[string]string
	if args != nil {
		params = map[string]string{
			"instanceID":  args.InstanceID,
			"keywordType": args.KeywordType,
			"keyword":     args.Keyword,
			"pageNo":      strconv.Itoa(args.PageNo),
			"pageSize":    strconv.Itoa(args.PageSize),
		}
	}
	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("alerting_rules"), params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListAlertingRuleResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}
	return &r, nil
}

// DeleteAlertingRules - 删除报警规则
func (c *Client) DeleteAlertingRule(ctx context.Context, args *DeleteAlertingRuleRequest, opt *bce.SignOption) (*DeleteAlertingRuleResponse, error) {
	req, err := bce.NewRequest("DELETE", c.GetURL(fmt.Sprintf("alerting_rules/%s?instanceID=%s", args.AlertingRuleID, args.InstanceID), nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r DeleteAlertingRuleResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}

	return &r, nil
}
