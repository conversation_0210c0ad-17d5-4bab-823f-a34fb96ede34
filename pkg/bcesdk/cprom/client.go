// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
// Client 实现 cprom.Interface

package cprom

import (
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

var _ Interface = &Client{}

// Endpoints - CCE V2 各地域 Endpoints
var Endpoints = map[string]string{
	"bj":  "cprom.bj.baidubce.com/v1",
	"gz":  "cprom.gz.baidubce.com/v1",
	"su":  "cprom.su.baidubce.com/v1",
	"nj":  "cprom.nj.baidubce.com/v1",
	"hkg": "cprom.hkg.baidubce.com/v1",
	"fwh": "cprom.fwh.baidubce.com/v1",
	"bd":  "cprom.bd.baidubce.com/v1",
}

// Client 实现 cprom.Interface
type Client struct {
	*bce.Client
}

// NewClient client of CCE
func NewClient(config *bce.Config) *Client {
	return &Client{
		Client: bce.NewClient(config),
	}
}

// SetDebug 是否开启 debug 模式
func (c *Client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

// GetURL generates the full URL of http request for Baidu Cloud BOS API.
func (c *Client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}
