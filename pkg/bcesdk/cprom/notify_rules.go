// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
// 实现 CProm SDK Instance 相关方法

package cprom

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// ListNotifyRule - 获取通知规则列表
func (c *Client) ListNotifyRule(ctx context.Context, args *ListNotifyRuleRequest, opt *bce.SignOption) (*ListNotifyRuleResponse, error) {
	var params map[string]string
	if args != nil {
		params = map[string]string{
			"keywordType": args.KeywordType,
			"keyword":     args.Keyword,
			"pageNo":      strconv.Itoa(args.PageNo),
			"pageSize":    strconv.Itoa(args.PageSize),
		}
	}
	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("notify_rules"), params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListNotifyRuleResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}
	return &r, nil
}

// DeleteNotifyRules - 删除通知规则
func (c *Client) DeleteNotifyRule(ctx context.Context, args *DeleteNotifyRuleRequest, opt *bce.SignOption) (*DeleteNotifyRuleResponse, error) {
	req, err := bce.NewRequest("DELETE", c.GetURL(fmt.Sprintf("notify_rules/%s?instanceID=", args.NotifyRuleID), nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r DeleteNotifyRuleResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}

	return &r, nil
}
