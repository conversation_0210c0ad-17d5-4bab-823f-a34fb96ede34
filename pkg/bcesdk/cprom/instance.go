// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
// 实现 CProm SDK Instance 相关方法

package cprom

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// CreateInstance - 创建监控实例
// PARAMS:
//   - ctx: The context to trace request
//   - args: *CreateInstanceRequest 创建监控实例配置
//   - opt: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	string: ClusterID 集群 ID
//	error: nil if succeed, error if fail
func (c *Client) CreateInstance(ctx context.Context, args *CreateInstanceRequest, opt *bce.SignOption) (*CreateInstanceResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("instances", nil), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r CreateInstanceResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}

	return &r, nil
}

// GetInstance 获取监控实例
func (c *Client) GetInstance(ctx context.Context, args *GetInstanceRequest, opt *bce.SignOption) (*GetInstanceResponse, error) {
	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("instances/%s", args.InstanceID), nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r GetInstanceResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}

	return &r, nil
}

// DeleteInstance 删除监控实例
func (c *Client) DeleteInstance(ctx context.Context, args *DeleteInstanceRequest, opt *bce.SignOption) (*DeleteInstanceResponse, error) {
	req, err := bce.NewRequest("DELETE", c.GetURL(fmt.Sprintf("instances/%s", args.InstanceID), nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r DeleteInstanceResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}

	return &r, nil
}

func (c *Client) ListInstance(ctx context.Context, args ListInstanceRequest, opt *bce.SignOption) (*ListInstanceResponse, error) {
	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("instances"), args), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListInstanceResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}
	return &r, nil
}
