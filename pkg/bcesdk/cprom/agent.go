package cprom

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// CreateAgent  创建 Agent
func (c *Client) CreateAgent(ctx context.Context, args *CreateAgentRequest, opt *bce.SignOption) (*CreateAgentResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	params := map[string]string{
		"instanceID": args.Spec.InstanceID,
	}

	req, err := bce.NewRequest("POST", c.GetURL("agents", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r CreateAgentResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}

	return &r, nil
}

// GetAgent 获取 Agent
func (c *Client) GetAgent(ctx context.Context, args *GetAgentRequest, opt *bce.SignOption) (*GetAgentResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	params := map[string]string{
		"instanceID": args.InstanceID,
	}

	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("agents/%s", args.AgentID), params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r GetAgentResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}

	return &r, nil
}

// DeleteAgent 删除 Agent
func (c *Client) DeleteAgent(ctx context.Context, args *DeleteAgentRequest, opt *bce.SignOption) (*DeleteAgentResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}
	params := map[string]string{
		"instanceID": args.InstanceID,
	}

	req, err := bce.NewRequest("DELETE", c.GetURL(fmt.Sprintf("agents/%s", args.AgentID), params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r DeleteAgentResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}

	return &r, nil
}

// ListAgent -
func (c *Client) ListAgent(ctx context.Context, args ListAgentRequest, opt *bce.SignOption) (*ListAgentResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("agents"), args), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListAgentResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}
	return &r, nil
}
