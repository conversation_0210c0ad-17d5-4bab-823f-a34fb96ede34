package v1

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	MonitorAgentPhasePending     MonitorAgentPhase = "Pending"     // 在用户集群部署之前的状态
	MonitorAgentPhaseCreating    MonitorAgentPhase = "Creating"    // 开始在用户集群部署的时候的状态
	MonitorAgentPhaseFailed      MonitorAgentPhase = "Failed"      // Agent 部署失败
	MonitorAgentPhaseRunning     MonitorAgentPhase = "Running"     // Agent 处于就绪状态
	MonitorAgentPhaseNotReady    MonitorAgentPhase = "NotReady"    // Agent 上报状态超时
	MonitorAgentPhaseTerminating MonitorAgentPhase = "Terminating" // 删除中
	MonitorAgentPhasUpgradinge   MonitorAgentPhase = "Upgrading"   // 升级中, 调整副本数，或者调整资源
	MonitorAgentPhasUnknown      MonitorAgentPhase = "Unknown"     // 其它未知状态

	AgentTypeVMAgent   = "VMAgent"
	AgentTypePromAgent = "PromAgent"

	AgentNameLabel = "cprom-agent-name"
	AgentIDLabel   = "cprom-agent-id"
)

type MonitorAgentPhase string

type AgentType string

// MonitorAgent
// +genclient
// +genclient:nonNamespaced
// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
// +kubebuilder:resource:scope=Namespaced,shortName=ma
// +kubebuilder:object:root=true
// +kubebuilder:printcolumn:name="ALIAS",type=string,JSONPath=`.spec.agentName`
// +kubebuilder:printcolumn:name="CLUSTER",type=string,JSONPath=`.spec.userClusterID`
// +kubebuilder:printcolumn:name="PHASE",type=string,JSONPath=`.status.phase`
// +kubebuilder:printcolumn:name="REASON",type=string,JSONPath=`.status.message`
// +kubebuilder:printcolumn:name="JOBS",type=string,JSONPath=`.status.vmagent.jobs`
// +kubebuilder:printcolumn:name="TARGET",type=string,JSONPath=`.status.vmagent.targets`
// +kubebuilder:printcolumn:name="AGE",type="date",JSONPath=".metadata.creationTimestamp"
type MonitorAgent struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   MonitorAgentSpec   `json:"spec,omitempty"`
	Status MonitorAgentStatus `json:"status,omitempty"`
}

// MonitorAgentList
// +kubebuilder:object:root=true
type MonitorAgentList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata"`

	Items []MonitorAgent `json:"items"`
}

// MonitorAgentSpec spec
type MonitorAgentSpec struct {
	Handler      string `json:"handler" yaml:"handler"`
	ChartName    string `json:"chartName" yaml:"chartName"`
	ChartVersion string `json:"chartVersion" yaml:"chartVersion"`

	// AgentName agent 名字
	// +optional
	AgentName *string `json:"agentName,omitempty" yaml:"agentName,omitempty"`

	// InstanceID 监控实例 id
	InstanceID string `json:"instanceID" yaml:"instanceID"`

	// AgentID agent id
	AgentID string `json:"agentID" yaml:"agentID"`

	// AgentType agent 类型，当前只支持 vmagent
	AgentType AgentType `json:"agentType" yaml:"agentType"`

	// Sidecar 组件配置
	Sidecar SidecarConfig `json:"sidecar" yaml:"sidecar"`

	// VMAgent 组件配置
	// +optional
	VMAgent *VMAgentConfig `json:"vmagent,omitempty" yaml:"vmagent,omitempty"`

	// AccountID
	AccountID string `json:"accountID" yaml:"accountID"`

	// UserID
	UserID string `json:"userID" yaml:"userID"`

	// ClusterID agent CRD 所处的 CCE 集群 ID（资源账号）
	ClusterID string `json:"clusterID,omitempty" yaml:"clusterID,omitempty"`

	// Region agent 所处的地区
	Region string `json:"region,omitempty" yaml:"region,omitempty"`

	// AccessToken 访问凭据
	//	sidecar 用来上报 agent 状态
	// 	agent 用来推送监控数据
	AccessToken string `json:"accessToken" yaml:"accessToken"`

	// UserClusterID agent 所处的 CCE 集群 ID
	UserClusterID string `json:"userClusterID,omitempty" yaml:"userClusterID,omitempty"`

	// UserAccountRegion agent 所处的 CCE 集群 的地区
	UserAccountRegion string `json:"userAccountRegion,omitempty" yaml:"userAccountRegion,omitempty"`

	// UserAccountVPCID user account vpc id
	UserAccountVPCID string `json:"userAccountVPCID" yaml:"userAccountVPCID"`

	// UserAccountVPCSubnetID user account vpc id
	UserAccountVPCSubnetID string `json:"userAccountVPCSubnetID" yaml:"userAccountVPCSubnetID"`
}

type VMAgentConfig struct {
	Enable bool `json:"enable" yaml:"enable"`

	// +optional
	ReplicaCount *int `json:"replicaCount,omitempty" yaml:"replicaCount,omitempty"`

	// +optional
	ExternalFlagLabels map[string]string `json:"externalFlagLabels,omitempty" yaml:"externalFlagLabels,omitempty"`

	// +optional
	Image Image `json:"image,omitempty" yaml:"image,omitempty"`

	// +optional
	Resources corev1.ResourceRequirements `json:"resources,omitempty" yaml:"resources,omitempty"`
}

type SidecarConfig struct {
	Image     Image                       `json:"image" yaml:"image"`
	Resources corev1.ResourceRequirements `json:"resources" yaml:"resources"`
}

// MonitorAgentStatus status
type MonitorAgentStatus struct {
	// Last time we probed the condition.
	// +optional
	LastProbeTime metav1.Time `json:"lastProbeTime,omitempty" protobuf:"bytes,3,opt,name=lastProbeTime"`

	// Last time the condition transitioned from one status to another.
	// +optional
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty" protobuf:"bytes,4,opt,name=lastTransitionTime"`

	// +optional
	Phase MonitorAgentPhase `json:"phase,omitempty"`

	// +optional
	Message string `json:"message,omitempty"`

	HelmRelease HelmReleaseStatus `json:"helmRelease" yaml:"helmRelease"`

	// LatestApplyValuesHash 最近一次 渲染的 values.yaml 配置的 hash
	// +optional
	LatestApplyValuesHash string `json:"latestApplyValuesHash"`

	// +optional
	VMAgent VMAgentStatus `json:"vmagent,omitempty" yaml:"vmagent,omitempty"`

	// RemoteWriteURL agent 远程写入地址
	RemoteWriteAddress string `json:"remoteWriteAddress" yaml:"remoteWriteAddress"`

	// ManagerAddress agent manager 地址，sidecar 请求这个地址获取采集任务配置
	ManagerAddress string `json:"managerAddress" yaml:"managerAddress"`

	CNCNetworkReady  bool `json:"cncNetworkReady" yaml:"cncNetworkReady"`
	PrivateLinkReady bool `json:"privateLinkReady" yaml:"privateLinkReady"`
	UserClusterExist bool `json:"userClusterExist" yaml:"userClusterExist"`

	Ready bool `json:"ready" yaml:"ready"`
}

type VMAgentStatus struct {
	// +optional 当前采集的 targets 的状态 (up/total)
	Targets string `json:"targets" yaml:"targets"`

	Jobs int `json:"jobs" yaml:"jobs"`

	// +optional 当前可以用分片
	AvailableReplicas int `json:"availableReplicas" yaml:"availableReplicas"`

	// Unique, one-word, CamelCase reason for the condition's last transition.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,5,opt,name=reason"`

	// Human-readable message indicating details about last transition.
	// +optional
	Message string `json:"message,omitempty" protobuf:"bytes,6,opt,name=message"`

	// +optional
	Ready bool `json:"ready" yaml:"ready"`
}

func init() {
	SchemeBuilder.Register(&MonitorAgent{}, &MonitorAgentList{})
}
