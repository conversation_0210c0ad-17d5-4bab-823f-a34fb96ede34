package v1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	BCMJobIDLabel = "cprom_bcm_job_id"
)

type BCMJobPhase string

// BCMJob
// +genclient
// +genclient:nonNamespaced
// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
// +kubebuilder:resource:scope=Namespaced,shortName=bcmj
// +kubebuilder:object:root=true
// +kubebuilder:printcolumn:name="PHASE",type=string,JSONPath=`.status.phase`
// +kubebuilder:printcolumn:name="READY",type=boolean,JSONPath=`.status.ready`
// +kubebuilder:printcolumn:name="REASON",type=string,JSONPath=`.status.message`
// +kubebuilder:printcolumn:name="Age",type="date",JSONPath=".metadata.creationTimestamp"
type BCMJob struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   BCMJobSpec   `json:"spec,omitempty"`
	Status BCMJobStatus `json:"status,omitempty"`
}

// BCMJobList
// +kubebuilder:object:root=true
type BCMJobList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata"`

	Items []BCMJob `json:"items"`
}

type BCMJobSpec struct {
	// BCMJobId 唯一标识 不支持修改
	BCMJobID string `json:"bcmJobID,omitempty" gorm:"column:bcm_job_id" validate:"readonly"`

	// UserID 子账号 id
	UserID string `json:"userID,omitempty" gorm:"column:user_id" validate:"readonly"`

	// AccountID 百度云账号 id
	AccountID string `json:"accountID,omitempty" gorm:"column:account_id" validate:"readonly"`

	// Region 地区
	Region string `json:"region" gorm:"region" validate:"readonly"`

	// ClusterID 标识 monitor instance 所在的资源账号 CCE  集群 id
	// 后端自动填充
	ClusterID string `json:"clusterID,omitempty" gorm:"column:cluster_id"`

	// InstanceID 监控实例 id
	InstanceID string `json:"instanceID" yaml:"instanceID"`

	BCMConfig BCMConfig `json:"bcmConfig" gorm:"column:bcm_config"`
}

type BCMConfig struct {
	// 订阅者来源，必填 cprom
	Source string `json:"source"`

	// 订阅的数据类型，必填
	// * METRICS: 指标数据
	// * EVENT: 事件
	DataType string `json:"dataType"`

	// 订阅的云服务列表，选填
	// 若未设置，会推送所有云服务的数据
	Scopes []string `json:"scopes,omitempty"`

	// 订阅的metric列表，选填
	// 若未设置，会推送所有metric
	Metrics []Metric `json:"metrics,omitempty"`

	// 用户列表，选填
	// 若未设置，会推送所有用户的数据
	Users []string `json:"users,omitempty"`

	// 数据推送目标，必填
	Sink Sink `json:"sink"`
}

type Sink struct {
	// 推送目标类型，必填，可选项包括
	// * PROM_REMOTE_WRITE: 只可订阅指标数据
	// * KAFKA
	// * BIGPIPE
	// * YUNKUN: 云坤
	Type string `json:"type"`

	// 目标url，必填
	// 该地址必须保证B区可访问
	Url string `json:"url"`

	// 数据格式，选填，若未设置，采用默认格式
	Format string `json:"format"`
}

type Metric struct {
	Scope string   `json:"scope"`
	Names []string `json:"names"`
}

// BCMJobStatus status
type BCMJobStatus struct {
	// Last time we probed the condition.
	// +optional
	LastProbeTime metav1.Time `json:"lastProbeTime,omitempty" protobuf:"bytes,3,opt,name=lastProbeTime"`

	// Last time the condition transitioned from one status to another.
	// +optional
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty" protobuf:"bytes,4,opt,name=lastTransitionTime"`

	// +optional
	Phase BCMJobPhase `json:"phase,omitempty"`

	// +optional
	Message string `json:"message,omitempty"`
}

func init() {
	SchemeBuilder.Register(&BCMJob{}, &BCMJobList{})
}
