package v1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	ScrapeJobPhaseCheckError  ScrapeJobPhase = "CheckError"
	ScrapeJobPhaseCheckPassed ScrapeJobPhase = "CheckPassed"

	ScrapeJobTypeBasic ScrapeJobType = "basic"
	ScrapeJobTypeSelf  ScrapeJobType = "self"

	ScrapeJobIDLabel   = "scrape-job-id"
	ScrapeJobNameLabel = "scrape-job-name"
)

type ScrapeJobType string

type ScrapeJobPhase string

// ScrapeJob
// +genclient
// +genclient:nonNamespaced
// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
// +kubebuilder:resource:scope=Namespaced
// +kubebuilder:object:root=true
// +kubebuilder:printcolumn:name="ALIAS",type=string,JSONPath=`.spec.scrapeJobName`
// +kubebuilder:printcolumn:name="TYPE",type=string,JSONPath=`.spec.type`
// +kubebuilder:printcolumn:name="PHASE",type=string,JSONPath=`.status.phase`
// +kubebuilder:printcolumn:name="REASON",type=string,JSONPath=`.status.message`
// +kubebuilder:printcolumn:name="Age",type="date",JSONPath=".metadata.creationTimestamp"
type ScrapeJob struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   ScrapeJobSpec   `json:"spec,omitempty"`
	Status ScrapeJobStatus `json:"status,omitempty"`
}

// ScrapeJobList
// +kubebuilder:object:root=true
type ScrapeJobList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata"`

	Items []ScrapeJob `json:"items"`
}

// ScrapeJobSpec spec
type ScrapeJobSpec struct {
	// scrapeJobSpec 内容
	// 内容为 prometheus ScrapeConfig yaml 结构
	Content string `json:"content" yaml:"content"`

	ScrapeJobID string `json:"scrapeJobID" yaml:"scrapeJobID"`

	// AccountID 百度云账号 id
	AccountID string `json:"accountID,omitempty" gorm:"column:account_id" validate:"readonly"`

	// InstanceID 监控实例 id
	InstanceID string `json:"instanceID" yaml:"instanceID"`

	// AgentID agent id
	AgentID string `json:"agentID" yaml:"agentID"`

	ScrapeJobName string `json:"scrapeJobName" yaml:"scrapeJobName"`

	MetricsPath string `json:"metricsPath" yaml:"metricsPath"`

	// Type
	// 采集任务类型
	// basic = 基础监控
	// self  = 自定义监控
	// +optional
	Type ScrapeJobType `json:"type,default=basic" yaml:"type"`
}

// ScrapeJobStatus status
type ScrapeJobStatus struct {
	// Last time we probed the condition.
	// +optional
	LastProbeTime metav1.Time `json:"lastProbeTime,omitempty" protobuf:"bytes,3,opt,name=lastProbeTime"`

	// Last time the condition transitioned from one status to another.
	// +optional
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty" protobuf:"bytes,4,opt,name=lastTransitionTime"`

	// +optional
	Phase ScrapeJobPhase `json:"phase,omitempty"`

	// +optional
	Message string `json:"message,omitempty"`
}

func init() {
	SchemeBuilder.Register(&ScrapeJob{}, &ScrapeJobList{})
}
