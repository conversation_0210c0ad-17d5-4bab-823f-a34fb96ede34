package v1

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
)

const (
	FinalizerCProm                corev1.FinalizerName = "cprom.baidubce.com/finalizer"
	AutoDeleteAnnotation                               = "cprom.baidubce.com/auto-delete-at"
	DefaultBindVIPIDAnnotation                         = "cprom.baidubce.com/default-bind-vpc-id"
	DefaultBindSubnetIDAnnotation                      = "cprom.baidubce.com/default-bind-subnet-id"
	InstanceTemplateAnnotation                         = "cprom.baidubce.com/instance-template"
	CreateByAnnotation                                 = "cprom.baidubce.com/create-by"
	InstanceTemplateLabel                              = "cprom-instance-template"
	InstanceNameLabel                                  = "cprom-instance-name"
	InstanceIDLabel                                    = "cprom-instance-id"
	BCEAccountIDLabel                                  = "bce-account-id"
	InstanceTypeLabel                                  = "cprom-instance-type"

	MonitorInstancePhasePending     MonitorInstancePhase = "Pending"
	MonitorInstancePhaseCreating    MonitorInstancePhase = "Creating"
	MonitorInstancePhaseTerminating MonitorInstancePhase = "Terminating"
	MonitorInstancePhaseFailed      MonitorInstancePhase = "Failed"
	MonitorInstancePhaseRunning     MonitorInstancePhase = "Running"
	MonitorInstancePhaseUpgrading   MonitorInstancePhase = "Upgrading"
	MonitorInstancePhaseUnknown     MonitorInstancePhase = "Unknown"
)

type MonitorInstancePhase string

// MonitorInstance
// +genclient
// +genclient:nonNamespaced
// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
// +kubebuilder:resource:scope=Cluster,shortName=mi
// +kubebuilder:object:root=true
// +kubebuilder:printcolumn:name="ALIAS",type=string,JSONPath=`.spec.instanceName`
// +kubebuilder:printcolumn:name="PHASE",type=string,JSONPath=`.status.phase`
// +kubebuilder:printcolumn:name="READY",type=boolean,JSONPath=`.status.ready`
// +kubebuilder:printcolumn:name="REASON",type=string,JSONPath=`.status.message`
// +kubebuilder:printcolumn:name="Age",type="date",JSONPath=".metadata.creationTimestamp"
type MonitorInstance struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   MonitorInstanceSpec   `json:"spec,omitempty"`
	Status MonitorInstanceStatus `json:"status,omitempty"`
}

// MonitorInstanceList
// +kubebuilder:object:root=true
type MonitorInstanceList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata"`

	Items []MonitorInstance `json:"items"`
}

// MonitorInstanceSpec spec
type MonitorInstanceSpec struct {
	// Handler 标识该 Monitor instance 由哪个 Controller 处理
	Handler string `json:"handler,omitempty" gorm:"column:handler"`

	ChartVersion string `json:"chartVersion" gorm:"chartVersion"`
	ChartName    string `json:"chartName" gorm:"chartName"`

	// InstanceID 用于 Monitor instance 唯一标识, 不支持修改
	InstanceID string `json:"instanceID,omitempty" gorm:"column:instance_id" validate:"readonly"`

	// InstanceName 实例名称，支持修改
	InstanceName *string `json:"instanceName,omitempty" gorm:"column:instance_name"`

	// UserID 子账号 id
	UserID string `json:"userID,omitempty" gorm:"column:user_id" validate:"readonly"`

	// AccountID 百度云账号 id
	AccountID string `json:"accountID,omitempty" gorm:"column:account_id" validate:"readonly"`

	// Region 地区
	Region string `json:"region" gorm:"region" validate:"readonly"`

	// InstanceChargingType 后付费或预付费
	InstanceChargingType *PaymentTiming `json:"instanceChargingType,omitempty" gorm:"column:instance_charging_type"`

	// PreChargingOption 预付费选项
	// +optional
	PreChargingOption *InstancePreChargingOption `json:"preChargingOption,omitempty" yaml:"preChargingOption,omitempty"`

	// ClusterID 标识 monitor instance 所在的资源账号 CCE  集群 id
	// 后端自动填充
	ClusterID string `json:"clusterID,omitempty" gorm:"column:cluster_id"`

	// StorageClass monitor instance 实例所用到的存储类型，需要资源账号集群提前准备好
	// 实例规格里面提前设置好
	StorageClass string `json:"storageClass,omitempty" gorm:"storageClass,omitempty"`

	// ReloaderConfig monitor instance 实例下所有组件 config_reloader 自动更新组件配置
	// 后端自动填充
	ReloaderConfig ReloaderConfig `json:"reloaderConfig,omitempty" gorm:"reloaderConfig,omitempty"`

	// VMClusterConfig vmcluster 配置包含 vminsert、vmselect、vmstorage 组件，由 vm-operator 负责其生命周期
	VMClusterConfig VMClusterConfig `json:"vmClusterConfig,omitempty" gorm:"type:json;column:vm_cluster_config"`

	// GrafanaConfig grafana
	GrafanaConfig GrafanaConfig `json:"grafanaConfig,omitempty" gorm:"type:json;column:grafana_config"`

	// VMAlert monitor instance 实例下 `Recording rules` & `Alert rules` 计算组件
	VMAlertConfig VMConfig `json:"vmAlertConfig" yaml:"vmAlertConfig"`

	// VMAlert monitor instance 实例下 `Alert Adaptor`组件
	AlertAdaptorConfig AlertAdaptorConfig `json:"alertAdaptorConfig" yaml:"alertAdaptorConfig"`

	// AlertManagerConfig alertmanager
	AlertManagerConfig VMConfig `json:"alertManagerConfig,omitempty" gorm:"type:json;column:alertmanager_config"`

	// IngressNginxConfig ingress-nginx 配置
	IngressNginxConfig IngressNginxConfig `json:"ingressNginxConfig" yaml:"ingressNginxConfig"`

	// PublicIngressConfig 监控实例公网入口访问
	PublicIngressConfig IngressConfig `json:"publicIngressConfig" yaml:"publicIngressConfig"`

	// PrivateIngressConfig 监控实例服务网卡入口访问
	PrivateIngressConfig IngressConfig `json:"privateIngressConfig" yaml:"privateIngressConfig"`
}

// InstancePreChargingOption 定义付费相关配置
type InstancePreChargingOption struct {
	PurchaseTime      int    `json:"purchaseTime,omitempty" gorm:"column:purchase_time"`            //  预付费才生效：单位月，12 = 12 月
	AutoRenew         bool   `json:"autoRenew,omitempty" gorm:"column:auto_renew"`                  // 是否自动续费
	AutoRenewTimeUnit string `json:"autoRenewTimeUnit,omitempty" gorm:"column:auto_renew_timeunit"` // 续费单位：月
	AutoRenewTime     int    `json:"autoRenewTime,omitempty" gorm:"column:auto_renew_time"`         // 12 = 12 个月
}

// PaymentTiming 付费时间选择
type PaymentTiming string

const (
	// PaymentTimingPrepaid 预付费
	PaymentTimingPrepaid PaymentTiming = "Prepaid"

	// PaymentTimingPostpaid 后付费
	PaymentTimingPostpaid PaymentTiming = "Postpaid"

	// PaymentTimingBid
	PaymentTimingBid PaymentTiming = "bid"
)

// PlacementPolicy 实例放置策略
type PlacementPolicy string

const (
	PlacementPolicyDefault       PlacementPolicy = "default"
	PlacementPolicyDedicatedHost PlacementPolicy = "dedicatedHost"
)

// IngressNginxConfig ingress controller 配置
type IngressNginxConfig struct {
	// Name ingress pod 名字, 名字格式为 ingress-nginx-{name}-{随机字符}
	// Name 应该和 monitorInstance 保持一致
	Name string `json:"name" yaml:"name"`

	// Image image
	Image Image `json:"image" yaml:"image"`

	// Config 配置文件
	Config map[string]string `json:"config" yaml:"config"`

	// IngressClass 监控实例对外暴露的 ingress class 类型，ingressClass=name=monitorInstanceID
	IngressClass string `json:"ingressClass" yaml:"ingressClass"`

	// Resources resources
	Resources corev1.ResourceRequirements `json:"resources" yaml:"resources"`

	// ReplicaCount
	ReplicaCount int `json:"replicaCount" yaml:"replicaCount"`
}

// IngressConfig ingress 配置
type IngressConfig struct {
	// Enable 是否启用
	Enable bool `json:"enable"`

	// Annotations ingress 设置的
	// +optional
	Annotations map[string]string `json:"annotations,omitempty" yaml:"annotations,omitempty"`

	// Host ingress 访问域名
	Host string `json:"host" yaml:"host"`

	// HttpAccess HTTP 访问启动 默认不开启,false
	HttpAccess *bool `json:"httpAccess,omitempty" yaml:"httpAccess,omitempty"`

	// Key ingress 证书 key
	// +optional
	Key string `json:"key,omitempty" yaml:"key,omitempty"`

	// Cert ingress 证书 cert
	// +optional
	Cert string `json:"cert,omitempty" yaml:"cert,omitempty"`
}

// GrafanaConfig grafana 配置
type GrafanaConfig struct {
	// +optional
	Enable *bool `json:"enable,omitempty" yaml:"enable,omitempty"`

	ReplicaCount int `json:"replicaCount" yaml:"replicaCount"`

	// +optional
	AdminPassword *string `json:"adminPassword,omitempty" yaml:"adminPassword,omitempty"`

	Database          DatabaseConfig              `json:"database,omitempty" yaml:"database"`
	Image             Image                       `json:"image" yaml:"image"`
	ProvisioningImage Image                       `json:"provisioningImage" yaml:"provisioningImage"`
	Resources         corev1.ResourceRequirements `json:"resources" yaml:"resources"`

	// +optional
	DataVolumeSize string `json:"dataVolumeSize" yaml:"dataVolumeSize"`
}

// DatabaseConfig grafana 配置结构
type DatabaseConfig struct {
	// +optional
	// InstanceType 数据库实例的规格，默认 BASIC
	InstanceType string `json:"instanceType,omitempty" yaml:"instanceType"`

	// +optional
	// DatabaseKind 数据库实例的类型，默认 postgres
	DatabaseKind string `json:"databaseKind,omitempty" yaml:"databaseKind"`

	// +optional
	// Password 数据库实例密码，需要 base64 编码
	Password string `json:"password,omitempty" yaml:"password,omitempty"`

	// +optional
	// Username 数据库实例登录用户名
	Username string `json:"username,omitempty" yaml:"username"`
}

// MonitorInstanceStatus status
type MonitorInstanceStatus struct {
	// +optional
	Phase MonitorInstancePhase `json:"phase"`

	// +optional
	Ready bool `json:"ready" yaml:"ready"`

	// +optional
	Message string `json:"message,omitempty"`

	// +optional
	IngressNginx BaseStatus `json:"ingressNginx" yaml:"ingressNginx"`

	// +optional
	HelmRelease HelmReleaseStatus `json:"helmRelease" yaml:"helmRelease"`

	// LatestApplyValuesHash 最近一次 渲染的 values.yaml 配置的 hash
	// +optional
	LatestApplyValuesHash string `json:"latestApplyValuesHash"`

	// Grafana grafana 状态
	// +optional
	Grafana GrafanaStatus `json:"grafana" yaml:"grafana"`

	// VMAlert alert 状态
	// +optional
	VMAlert BaseStatus `json:"vmAlert" yaml:"vmAlert"`

	// +optional
	VMCluster VMClusterStatus `json:"vmCluster" yaml:"vmCluster"`

	// +optional
	AlertManager BaseStatus `json:"alertManager" yaml:"alertManager"`

	// Last time we probed the condition.
	// +optional
	LastProbeTime metav1.Time `json:"lastProbeTime,omitempty" protobuf:"bytes,3,opt,name=lastProbeTime"`

	// Last time the condition transitioned from one status to another.
	// +optional
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty" protobuf:"bytes,4,opt,name=lastTransitionTime"`

	// +optional
	AccessEndpoint AccessEndpointStatus `json:"accessEndpoint,omitempty"`
}

type AccessEndpointStatus struct {
	PublishPoint     string `json:"publishPoint,omitempty"`
	PublicDomain     string `json:"publicDomain,omitempty"`
	EIPStatus        string `json:"eipStatus,omitempty"`
	PrivateDomain    string `json:"privateDomain,omitempty"`
	CNCNetworkStatus string `json:"cncNetworkStatus,omitempty"`
}

type HelmReleaseStatus struct {
	// Name release name
	Name string `json:"name" yaml:"name"`

	Updated  string `json:"updated" yaml:"updated"`
	Revision int32  `json:"revision" yaml:"revision"`
	Status   string `json:"status" yaml:"status"`

	// +optional
	Ready bool `json:"ready" yaml:"ready"`
}

// GrafanaStatus grafana 状态
type GrafanaStatus struct {
	// +optional
	DatabasePhase string `json:"databasePhase" yaml:"databasePhase"`

	// +optional
	DatabaseName string `json:"databaseName" yaml:"databaseName"`

	// +optional
	DatabaseUsername string `json:"databaseUsername" yaml:"databaseUsername"`

	// +optional
	DatabasePassword string `json:"databasePassword" yaml:"databasePassword"`

	// +optional
	DatabaseHost string `json:"databaseHost,omitempty" yaml:"databaseHost,omitempty"`

	// +optional
	PostgresNamespace string `json:"postgresNamespace" yaml:"postgresNamespace"`

	// +optional
	PostgresName string `json:"postgresName" yaml:"postgresName"`

	// +optional
	AvailableReplicas int `json:"availableReplicas" yaml:"availableReplicas"`

	// Unique, one-word, CamelCase reason for the condition's last transition.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,5,opt,name=reason"`

	// Human-readable message indicating details about last transition.
	// +optional
	Message string `json:"message,omitempty" protobuf:"bytes,6,opt,name=message"`

	// +optional
	Ready bool `json:"ready" yaml:"ready"`
}

type VMClusterStatus struct {
	// +optional
	VMStorage BaseStatus `json:"vmStorage" yaml:"vmStorage"`

	// +optional
	VMSelect BaseStatus `json:"vmSelect" yaml:"vmSelect"`

	// +optional
	VMInsert BaseStatus `json:"vmInsert" yaml:"vmInsert"`

	// +optional
	Ready bool `json:"ready" yaml:"ready"`
}

// BaseStatus vm status
type BaseStatus struct {
	// Unique, one-word, CamelCase reason for the condition's last transition.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,5,opt,name=reason"`

	// Human-readable message indicating details about last transition.
	// +optional
	Message string `json:"message,omitempty" protobuf:"bytes,6,opt,name=message"`

	// +optional
	Phase string `json:"phase,omitempty" yaml:"phase,omitempty"`

	// +optional
	AvailableReplicas int `json:"availableReplicas" yaml:"availableReplicas"`

	// +optional
	Ready bool `json:"ready" yaml:"ready"`
}

// Image image
type Image struct {
	Registry string `json:"registry" yaml:"registry"`
	Image    string `json:"image" yaml:"image"`
	Tag      string `json:"tag" yaml:"tag"`

	// +optional
	Digest string `json:"digest,omitempty" yaml:"digest,omitempty"`
}

// ReloaderConfig reloader config
type ReloaderConfig struct {
	Image Image `json:"image" yaml:"image"`
}

// VMConfig VM CRD 通用配置
type VMConfig struct {
	// ExtraArgs add extra envs to specify container args
	// +optional
	ExtraEnvs []corev1.EnvVar `json:"extraEnvs,omitempty" yaml:"extraEnvs,omitempty"`

	// ExtraArgs add extra args to specify container args
	// +optional
	ExtraArgs map[string]string `json:"extraArgs,omitempty" yaml:"extraArgs,omitempty"`

	Image        Image                       `json:"image" yaml:"image"`
	ReplicaCount int                         `json:"replicaCount" yaml:"replicaCount"`
	Resources    corev1.ResourceRequirements `json:"resources" yaml:"resources"`

	// +optional
	DataVolumeSize string `json:"dataVolumeSize,omitempty" yaml:"dataVolumeSize,omitempty"`
}

// AlertAdaptorConfig CRD 通用配置
type AlertAdaptorConfig struct {
	Image         Image                       `json:"image" yaml:"image"`
	ReplicaCount  int                         `json:"replicaCount" yaml:"replicaCount"`
	Resources     corev1.ResourceRequirements `json:"resources" yaml:"resources"`
	RedisAddr     string                      `json:"redisAddr" yaml:"redisAddr"`
	RedisPassword string                      `json:"redisPassword" yaml:"redisPassword"`
	RedisDatabase int                         `json:"redisDatabase" yaml:"redisDatabase"`
}

type VMClusterConfig struct {
	// ReplicationFactor vm cluster 分片冗余因子，一般情况下每个分片 2 个冗余即可
	ReplicationFactor int `json:"replicationFactor" yaml:"replicationFactor"`

	// RetentionPeriod vm cluster 数据保存时间，单位为月, 最小时间为 1 个月
	RetentionPeriod *intstr.IntOrString `json:"retentionPeriod" yaml:"retentionPeriod"`

	VMInsert  VMConfig `json:"vmInsert" yaml:"vmInsert"`
	VMSelect  VMConfig `json:"vmSelect" yaml:"vmSelect"`
	VMStorage VMConfig `json:"vmStorage" yaml:"vmStorage"`
}

func init() {
	SchemeBuilder.Register(&MonitorInstance{}, &MonitorInstanceList{})
}
