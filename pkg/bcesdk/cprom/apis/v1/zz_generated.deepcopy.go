//go:build !ignore_autogenerated
// +build !ignore_autogenerated

// Code generated by controller-gen. DO NOT EDIT.

package v1

import (
	corev1 "k8s.io/api/core/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/intstr"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AccessEndpointStatus) DeepCopyInto(out *AccessEndpointStatus) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AccessEndpointStatus.
func (in *AccessEndpointStatus) DeepCopy() *AccessEndpointStatus {
	if in == nil {
		return nil
	}
	out := new(AccessEndpointStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AlertAdaptorConfig) DeepCopyInto(out *AlertAdaptorConfig) {
	*out = *in
	out.Image = in.Image
	in.Resources.DeepCopyInto(&out.Resources)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AlertAdaptorConfig.
func (in *AlertAdaptorConfig) DeepCopy() *AlertAdaptorConfig {
	if in == nil {
		return nil
	}
	out := new(AlertAdaptorConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BCMConfig) DeepCopyInto(out *BCMConfig) {
	*out = *in
	if in.Scopes != nil {
		in, out := &in.Scopes, &out.Scopes
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Metrics != nil {
		in, out := &in.Metrics, &out.Metrics
		*out = make([]Metric, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Users != nil {
		in, out := &in.Users, &out.Users
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	in.Sink.DeepCopyInto(&out.Sink)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BCMConfig.
func (in *BCMConfig) DeepCopy() *BCMConfig {
	if in == nil {
		return nil
	}
	out := new(BCMConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BCMJob) DeepCopyInto(out *BCMJob) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BCMJob.
func (in *BCMJob) DeepCopy() *BCMJob {
	if in == nil {
		return nil
	}
	out := new(BCMJob)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *BCMJob) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BCMJobList) DeepCopyInto(out *BCMJobList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]BCMJob, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BCMJobList.
func (in *BCMJobList) DeepCopy() *BCMJobList {
	if in == nil {
		return nil
	}
	out := new(BCMJobList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *BCMJobList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BCMJobSpec) DeepCopyInto(out *BCMJobSpec) {
	*out = *in
	in.BCMConfig.DeepCopyInto(&out.BCMConfig)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BCMJobSpec.
func (in *BCMJobSpec) DeepCopy() *BCMJobSpec {
	if in == nil {
		return nil
	}
	out := new(BCMJobSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BCMJobStatus) DeepCopyInto(out *BCMJobStatus) {
	*out = *in
	in.LastProbeTime.DeepCopyInto(&out.LastProbeTime)
	in.LastTransitionTime.DeepCopyInto(&out.LastTransitionTime)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BCMJobStatus.
func (in *BCMJobStatus) DeepCopy() *BCMJobStatus {
	if in == nil {
		return nil
	}
	out := new(BCMJobStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BaseStatus) DeepCopyInto(out *BaseStatus) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BaseStatus.
func (in *BaseStatus) DeepCopy() *BaseStatus {
	if in == nil {
		return nil
	}
	out := new(BaseStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DatabaseConfig) DeepCopyInto(out *DatabaseConfig) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DatabaseConfig.
func (in *DatabaseConfig) DeepCopy() *DatabaseConfig {
	if in == nil {
		return nil
	}
	out := new(DatabaseConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EscalateParam) DeepCopyInto(out *EscalateParam) {
	*out = *in
	out.Condition = in.Condition
	in.NotifyActionPublic.DeepCopyInto(&out.NotifyActionPublic)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EscalateParam.
func (in *EscalateParam) DeepCopy() *EscalateParam {
	if in == nil {
		return nil
	}
	out := new(EscalateParam)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EscalateParamBackend) DeepCopyInto(out *EscalateParamBackend) {
	*out = *in
	out.Condition = in.Condition
	if in.NotifyAction != nil {
		in, out := &in.NotifyAction, &out.NotifyAction
		*out = make([]NotifyAction, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EscalateParamBackend.
func (in *EscalateParamBackend) DeepCopy() *EscalateParamBackend {
	if in == nil {
		return nil
	}
	out := new(EscalateParamBackend)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GrafanaConfig) DeepCopyInto(out *GrafanaConfig) {
	*out = *in
	if in.Enable != nil {
		in, out := &in.Enable, &out.Enable
		*out = new(bool)
		**out = **in
	}
	if in.AdminPassword != nil {
		in, out := &in.AdminPassword, &out.AdminPassword
		*out = new(string)
		**out = **in
	}
	out.Database = in.Database
	out.Image = in.Image
	out.ProvisioningImage = in.ProvisioningImage
	in.Resources.DeepCopyInto(&out.Resources)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GrafanaConfig.
func (in *GrafanaConfig) DeepCopy() *GrafanaConfig {
	if in == nil {
		return nil
	}
	out := new(GrafanaConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GrafanaStatus) DeepCopyInto(out *GrafanaStatus) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GrafanaStatus.
func (in *GrafanaStatus) DeepCopy() *GrafanaStatus {
	if in == nil {
		return nil
	}
	out := new(GrafanaStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HelmReleaseStatus) DeepCopyInto(out *HelmReleaseStatus) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HelmReleaseStatus.
func (in *HelmReleaseStatus) DeepCopy() *HelmReleaseStatus {
	if in == nil {
		return nil
	}
	out := new(HelmReleaseStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Image) DeepCopyInto(out *Image) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Image.
func (in *Image) DeepCopy() *Image {
	if in == nil {
		return nil
	}
	out := new(Image)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *IngressConfig) DeepCopyInto(out *IngressConfig) {
	*out = *in
	if in.Annotations != nil {
		in, out := &in.Annotations, &out.Annotations
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.HttpAccess != nil {
		in, out := &in.HttpAccess, &out.HttpAccess
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new IngressConfig.
func (in *IngressConfig) DeepCopy() *IngressConfig {
	if in == nil {
		return nil
	}
	out := new(IngressConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *IngressNginxConfig) DeepCopyInto(out *IngressNginxConfig) {
	*out = *in
	out.Image = in.Image
	if in.Config != nil {
		in, out := &in.Config, &out.Config
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	in.Resources.DeepCopyInto(&out.Resources)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new IngressNginxConfig.
func (in *IngressNginxConfig) DeepCopy() *IngressNginxConfig {
	if in == nil {
		return nil
	}
	out := new(IngressNginxConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Metric) DeepCopyInto(out *Metric) {
	*out = *in
	if in.Names != nil {
		in, out := &in.Names, &out.Names
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Metric.
func (in *Metric) DeepCopy() *Metric {
	if in == nil {
		return nil
	}
	out := new(Metric)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MonitorAgent) DeepCopyInto(out *MonitorAgent) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MonitorAgent.
func (in *MonitorAgent) DeepCopy() *MonitorAgent {
	if in == nil {
		return nil
	}
	out := new(MonitorAgent)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *MonitorAgent) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MonitorAgentList) DeepCopyInto(out *MonitorAgentList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]MonitorAgent, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MonitorAgentList.
func (in *MonitorAgentList) DeepCopy() *MonitorAgentList {
	if in == nil {
		return nil
	}
	out := new(MonitorAgentList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *MonitorAgentList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MonitorAgentSpec) DeepCopyInto(out *MonitorAgentSpec) {
	*out = *in
	if in.AgentName != nil {
		in, out := &in.AgentName, &out.AgentName
		*out = new(string)
		**out = **in
	}
	in.Sidecar.DeepCopyInto(&out.Sidecar)
	if in.VMAgent != nil {
		in, out := &in.VMAgent, &out.VMAgent
		*out = new(VMAgentConfig)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MonitorAgentSpec.
func (in *MonitorAgentSpec) DeepCopy() *MonitorAgentSpec {
	if in == nil {
		return nil
	}
	out := new(MonitorAgentSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MonitorAgentStatus) DeepCopyInto(out *MonitorAgentStatus) {
	*out = *in
	in.LastProbeTime.DeepCopyInto(&out.LastProbeTime)
	in.LastTransitionTime.DeepCopyInto(&out.LastTransitionTime)
	out.HelmRelease = in.HelmRelease
	out.VMAgent = in.VMAgent
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MonitorAgentStatus.
func (in *MonitorAgentStatus) DeepCopy() *MonitorAgentStatus {
	if in == nil {
		return nil
	}
	out := new(MonitorAgentStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MonitorInstance) DeepCopyInto(out *MonitorInstance) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MonitorInstance.
func (in *MonitorInstance) DeepCopy() *MonitorInstance {
	if in == nil {
		return nil
	}
	out := new(MonitorInstance)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *MonitorInstance) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MonitorInstanceList) DeepCopyInto(out *MonitorInstanceList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]MonitorInstance, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MonitorInstanceList.
func (in *MonitorInstanceList) DeepCopy() *MonitorInstanceList {
	if in == nil {
		return nil
	}
	out := new(MonitorInstanceList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *MonitorInstanceList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MonitorInstanceSpec) DeepCopyInto(out *MonitorInstanceSpec) {
	*out = *in
	if in.InstanceName != nil {
		in, out := &in.InstanceName, &out.InstanceName
		*out = new(string)
		**out = **in
	}
	if in.InstanceChargingType != nil {
		in, out := &in.InstanceChargingType, &out.InstanceChargingType
		*out = new(PaymentTiming)
		**out = **in
	}
	if in.PreChargingOption != nil {
		in, out := &in.PreChargingOption, &out.PreChargingOption
		*out = new(InstancePreChargingOption)
		**out = **in
	}
	out.ReloaderConfig = in.ReloaderConfig
	in.VMClusterConfig.DeepCopyInto(&out.VMClusterConfig)
	in.GrafanaConfig.DeepCopyInto(&out.GrafanaConfig)
	in.VMAlertConfig.DeepCopyInto(&out.VMAlertConfig)
	in.AlertAdaptorConfig.DeepCopyInto(&out.AlertAdaptorConfig)
	in.AlertManagerConfig.DeepCopyInto(&out.AlertManagerConfig)
	in.IngressNginxConfig.DeepCopyInto(&out.IngressNginxConfig)
	in.PublicIngressConfig.DeepCopyInto(&out.PublicIngressConfig)
	in.PrivateIngressConfig.DeepCopyInto(&out.PrivateIngressConfig)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MonitorInstanceSpec.
func (in *MonitorInstanceSpec) DeepCopy() *MonitorInstanceSpec {
	if in == nil {
		return nil
	}
	out := new(MonitorInstanceSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MonitorInstanceStatus) DeepCopyInto(out *MonitorInstanceStatus) {
	*out = *in
	out.IngressNginx = in.IngressNginx
	out.HelmRelease = in.HelmRelease
	out.Grafana = in.Grafana
	out.VMAlert = in.VMAlert
	out.VMCluster = in.VMCluster
	out.AlertManager = in.AlertManager
	in.LastProbeTime.DeepCopyInto(&out.LastProbeTime)
	in.LastTransitionTime.DeepCopyInto(&out.LastTransitionTime)
	out.AccessEndpoint = in.AccessEndpoint
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MonitorInstanceStatus.
func (in *MonitorInstanceStatus) DeepCopy() *MonitorInstanceStatus {
	if in == nil {
		return nil
	}
	out := new(MonitorInstanceStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *NotifyAction) DeepCopyInto(out *NotifyAction) {
	*out = *in
	if in.Receivers != nil {
		in, out := &in.Receivers, &out.Receivers
		*out = make([]Receiver, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new NotifyAction.
func (in *NotifyAction) DeepCopy() *NotifyAction {
	if in == nil {
		return nil
	}
	out := new(NotifyAction)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *NotifyActionPublic) DeepCopyInto(out *NotifyActionPublic) {
	*out = *in
	if in.Channel != nil {
		in, out := &in.Channel, &out.Channel
		*out = make([]int, len(*in))
		copy(*out, *in)
	}
	if in.Users != nil {
		in, out := &in.Users, &out.Users
		*out = make([]User, len(*in))
		copy(*out, *in)
	}
	if in.UserGroups != nil {
		in, out := &in.UserGroups, &out.UserGroups
		*out = make([]UserGroup, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new NotifyActionPublic.
func (in *NotifyActionPublic) DeepCopy() *NotifyActionPublic {
	if in == nil {
		return nil
	}
	out := new(NotifyActionPublic)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *NotifyConfig) DeepCopyInto(out *NotifyConfig) {
	*out = *in
	if in.Channel != nil {
		in, out := &in.Channel, &out.Channel
		*out = make([]int, len(*in))
		copy(*out, *in)
	}
	if in.Users != nil {
		in, out := &in.Users, &out.Users
		*out = make([]User, len(*in))
		copy(*out, *in)
	}
	if in.UserGroups != nil {
		in, out := &in.UserGroups, &out.UserGroups
		*out = make([]UserGroup, len(*in))
		copy(*out, *in)
	}
	if in.CallbackUrls != nil {
		in, out := &in.CallbackUrls, &out.CallbackUrls
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new NotifyConfig.
func (in *NotifyConfig) DeepCopy() *NotifyConfig {
	if in == nil {
		return nil
	}
	out := new(NotifyConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *NotifyRule) DeepCopyInto(out *NotifyRule) {
	*out = *in
	in.NotifyActionPublic.DeepCopyInto(&out.NotifyActionPublic)
	if in.EscalateParam != nil {
		in, out := &in.EscalateParam, &out.EscalateParam
		*out = make([]EscalateParam, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new NotifyRule.
func (in *NotifyRule) DeepCopy() *NotifyRule {
	if in == nil {
		return nil
	}
	out := new(NotifyRule)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *NotifyRuleBackend) DeepCopyInto(out *NotifyRuleBackend) {
	*out = *in
	if in.NotifyWindow != nil {
		in, out := &in.NotifyWindow, &out.NotifyWindow
		*out = make([]TimeSpan, len(*in))
		copy(*out, *in)
	}
	if in.NotifyAction != nil {
		in, out := &in.NotifyAction, &out.NotifyAction
		*out = make([]NotifyAction, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.EscalateParam != nil {
		in, out := &in.EscalateParam, &out.EscalateParam
		*out = make([]EscalateParamBackend, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new NotifyRuleBackend.
func (in *NotifyRuleBackend) DeepCopy() *NotifyRuleBackend {
	if in == nil {
		return nil
	}
	out := new(NotifyRuleBackend)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *NotifyRuleList) DeepCopyInto(out *NotifyRuleList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]NotifyRule, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new NotifyRuleList.
func (in *NotifyRuleList) DeepCopy() *NotifyRuleList {
	if in == nil {
		return nil
	}
	out := new(NotifyRuleList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *NotifyRuleList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *NotifyRuleSpec) DeepCopyInto(out *NotifyRuleSpec) {
	*out = *in
	if in.NotifyRuleName != nil {
		in, out := &in.NotifyRuleName, &out.NotifyRuleName
		*out = new(string)
		**out = **in
	}
	in.NotifyConfig.DeepCopyInto(&out.NotifyConfig)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new NotifyRuleSpec.
func (in *NotifyRuleSpec) DeepCopy() *NotifyRuleSpec {
	if in == nil {
		return nil
	}
	out := new(NotifyRuleSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *NotifyRuleStatus) DeepCopyInto(out *NotifyRuleStatus) {
	*out = *in
	in.LastProbeTime.DeepCopyInto(&out.LastProbeTime)
	in.LastTransitionTime.DeepCopyInto(&out.LastTransitionTime)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new NotifyRuleStatus.
func (in *NotifyRuleStatus) DeepCopy() *NotifyRuleStatus {
	if in == nil {
		return nil
	}
	out := new(NotifyRuleStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *NotifyRuleV1) DeepCopyInto(out *NotifyRuleV1) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new NotifyRuleV1.
func (in *NotifyRuleV1) DeepCopy() *NotifyRuleV1 {
	if in == nil {
		return nil
	}
	out := new(NotifyRuleV1)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *NotifyRuleV1) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Receiver) DeepCopyInto(out *Receiver) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Receiver.
func (in *Receiver) DeepCopy() *Receiver {
	if in == nil {
		return nil
	}
	out := new(Receiver)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ReloaderConfig) DeepCopyInto(out *ReloaderConfig) {
	*out = *in
	out.Image = in.Image
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ReloaderConfig.
func (in *ReloaderConfig) DeepCopy() *ReloaderConfig {
	if in == nil {
		return nil
	}
	out := new(ReloaderConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ScrapeJob) DeepCopyInto(out *ScrapeJob) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	out.Spec = in.Spec
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ScrapeJob.
func (in *ScrapeJob) DeepCopy() *ScrapeJob {
	if in == nil {
		return nil
	}
	out := new(ScrapeJob)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ScrapeJob) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ScrapeJobList) DeepCopyInto(out *ScrapeJobList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]ScrapeJob, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ScrapeJobList.
func (in *ScrapeJobList) DeepCopy() *ScrapeJobList {
	if in == nil {
		return nil
	}
	out := new(ScrapeJobList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ScrapeJobList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ScrapeJobSpec) DeepCopyInto(out *ScrapeJobSpec) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ScrapeJobSpec.
func (in *ScrapeJobSpec) DeepCopy() *ScrapeJobSpec {
	if in == nil {
		return nil
	}
	out := new(ScrapeJobSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ScrapeJobStatus) DeepCopyInto(out *ScrapeJobStatus) {
	*out = *in
	in.LastProbeTime.DeepCopyInto(&out.LastProbeTime)
	in.LastTransitionTime.DeepCopyInto(&out.LastTransitionTime)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ScrapeJobStatus.
func (in *ScrapeJobStatus) DeepCopy() *ScrapeJobStatus {
	if in == nil {
		return nil
	}
	out := new(ScrapeJobStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SidecarConfig) DeepCopyInto(out *SidecarConfig) {
	*out = *in
	out.Image = in.Image
	in.Resources.DeepCopyInto(&out.Resources)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SidecarConfig.
func (in *SidecarConfig) DeepCopy() *SidecarConfig {
	if in == nil {
		return nil
	}
	out := new(SidecarConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Sink) DeepCopyInto(out *Sink) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Sink.
func (in *Sink) DeepCopy() *Sink {
	if in == nil {
		return nil
	}
	out := new(Sink)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TimeSpan) DeepCopyInto(out *TimeSpan) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TimeSpan.
func (in *TimeSpan) DeepCopy() *TimeSpan {
	if in == nil {
		return nil
	}
	out := new(TimeSpan)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TimeSpanV2) DeepCopyInto(out *TimeSpanV2) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TimeSpanV2.
func (in *TimeSpanV2) DeepCopy() *TimeSpanV2 {
	if in == nil {
		return nil
	}
	out := new(TimeSpanV2)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *User) DeepCopyInto(out *User) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new User.
func (in *User) DeepCopy() *User {
	if in == nil {
		return nil
	}
	out := new(User)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *UserGroup) DeepCopyInto(out *UserGroup) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UserGroup.
func (in *UserGroup) DeepCopy() *UserGroup {
	if in == nil {
		return nil
	}
	out := new(UserGroup)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VMAgentConfig) DeepCopyInto(out *VMAgentConfig) {
	*out = *in
	if in.ReplicaCount != nil {
		in, out := &in.ReplicaCount, &out.ReplicaCount
		*out = new(int)
		**out = **in
	}
	if in.ExternalFlagLabels != nil {
		in, out := &in.ExternalFlagLabels, &out.ExternalFlagLabels
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	out.Image = in.Image
	in.Resources.DeepCopyInto(&out.Resources)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VMAgentConfig.
func (in *VMAgentConfig) DeepCopy() *VMAgentConfig {
	if in == nil {
		return nil
	}
	out := new(VMAgentConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VMAgentStatus) DeepCopyInto(out *VMAgentStatus) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VMAgentStatus.
func (in *VMAgentStatus) DeepCopy() *VMAgentStatus {
	if in == nil {
		return nil
	}
	out := new(VMAgentStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VMClusterConfig) DeepCopyInto(out *VMClusterConfig) {
	*out = *in
	if in.RetentionPeriod != nil {
		in, out := &in.RetentionPeriod, &out.RetentionPeriod
		*out = new(intstr.IntOrString)
		**out = **in
	}
	in.VMInsert.DeepCopyInto(&out.VMInsert)
	in.VMSelect.DeepCopyInto(&out.VMSelect)
	in.VMStorage.DeepCopyInto(&out.VMStorage)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VMClusterConfig.
func (in *VMClusterConfig) DeepCopy() *VMClusterConfig {
	if in == nil {
		return nil
	}
	out := new(VMClusterConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VMClusterStatus) DeepCopyInto(out *VMClusterStatus) {
	*out = *in
	out.VMStorage = in.VMStorage
	out.VMSelect = in.VMSelect
	out.VMInsert = in.VMInsert
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VMClusterStatus.
func (in *VMClusterStatus) DeepCopy() *VMClusterStatus {
	if in == nil {
		return nil
	}
	out := new(VMClusterStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VMConfig) DeepCopyInto(out *VMConfig) {
	*out = *in
	if in.ExtraEnvs != nil {
		in, out := &in.ExtraEnvs, &out.ExtraEnvs
		*out = make([]corev1.EnvVar, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.ExtraArgs != nil {
		in, out := &in.ExtraArgs, &out.ExtraArgs
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	out.Image = in.Image
	in.Resources.DeepCopyInto(&out.Resources)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VMConfig.
func (in *VMConfig) DeepCopy() *VMConfig {
	if in == nil {
		return nil
	}
	out := new(VMConfig)
	in.DeepCopyInto(out)
	return out
}
