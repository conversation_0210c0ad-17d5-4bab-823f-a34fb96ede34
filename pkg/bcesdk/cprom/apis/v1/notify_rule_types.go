package v1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	NotifyRulePhaseCheckPassed NotifyRulePhase = "CheckPassed" // 检查通过
	NotifyRulePhaseCheckError  NotifyRulePhase = "CheckError"  // 检查不通过

	NotifyRuleIDLabel    = "cprom_notify_rule_id"
	NotifyRuleNameLabel  = "cprom-notify-rule-name"
	RecordingRuleIDLabel = "cprom-recording-rule-id"
	AlertingRuleIDLabel  = "cprom-alerting-rule-id"
	VMRuleTypeLabel      = "cprom-vm-rule-type" // 用于List VMRule时区分 RecordingRule / AlertingRule
	UpdateTimeAnnotation = "cprom.baidubce.com/update-time"
	AlertSourceIDLabel   = "cprom_alerting_rule_id" // 用于从AlertManager Notification 中获取原始告警规则id
)

type NotifyRulePhase string

// NotifyRule
// +genclient
// +genclient:nonNamespaced
// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
// +kubebuilder:resource:scope=Cluster,shortName=nr
// +kubebuilder:object:root=true
// +kubebuilder:printcolumn:name="PHASE",type=string,JSONPath=`.status.phase`
// +kubebuilder:printcolumn:name="READY",type=boolean,JSONPath=`.status.ready`
// +kubebuilder:printcolumn:name="REASON",type=string,JSONPath=`.status.message`
// +kubebuilder:printcolumn:name="Age",type="date",JSONPath=".metadata.creationTimestamp"
type NotifyRuleV1 struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   NotifyRuleSpec   `json:"spec,omitempty"`
	Status NotifyRuleStatus `json:"status,omitempty"`
}

// NotifyRuleList
// +kubebuilder:object:root=true
type NotifyRuleList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata"`

	Items []NotifyRule `json:"items"`
}

type NotifyRuleSpec struct {
	// NotifyRuleID 唯一标识 不支持修改
	NotifyRuleID string `json:"notifyRuleID,omitempty" gorm:"column:notify_rule_id" validate:"readonly"`

	// UserID 子账号 id
	UserID string `json:"userID,omitempty" gorm:"column:user_id" validate:"readonly"`

	// NotifyRuleName 支持修改
	NotifyRuleName *string `json:"notifyRuleName,omitempty" gorm:"column:notify_rule_name"`

	// AccountID 百度云账号 id
	AccountID string `json:"accountID,omitempty" gorm:"column:account_id" validate:"readonly"`

	// Region 地区
	Region string `json:"region" gorm:"region" validate:"readonly"`

	// ClusterID 标识 monitor instance 所在的资源账号 CCE  集群 id
	// 后端自动填充
	ClusterID string `json:"clusterID,omitempty" gorm:"column:cluster_id"`

	InstanceID string `json:"instanceID" yaml:"instanceID"`

	NotifyConfig NotifyConfig `json:"notifyConfig" gorm:"column:notify_config"`
}

// NotifyRuleStatus status
type NotifyRuleStatus struct {
	// Last time we probed the condition.
	// +optional
	LastProbeTime metav1.Time `json:"lastProbeTime,omitempty" protobuf:"bytes,3,opt,name=lastProbeTime"`

	// Last time the condition transitioned from one status to another.
	// +optional
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty" protobuf:"bytes,4,opt,name=lastTransitionTime"`

	// +optional
	Phase NotifyRulePhase `json:"phase,omitempty"`

	// +optional
	Message string `json:"message,omitempty"`
}

// NotifyConfig 通知配置
type NotifyConfig struct {
	// Enable 是否启用
	Enable bool `json:"enable"`

	PolicyName string `json:"policyName"`

	// 时分秒开始时间点 格式为 xx:xx:xx
	StartTime string `json:"startTime"`

	// 时分秒结束时间点 格式为 xx:xx:xx
	EndTime string `json:"endTime"`

	// 通知周期 单位为分钟
	ExecuteCycle int `json:"executeCycle"`

	// 通知渠道： 1 电话 2 短信 3 邮件。dive前的min=1,max=3表示channel的长度必须是[1,3]，dive后的oneof=1 2 3表示channel中的每个元素必须是1、2、3之一
	Channel []int `json:"channel" validate:"min=1,max=3,dive,oneof=1 2 3"`

	// 接收类型 1 用户 2 用户组
	ReceiverType int `json:"receiverType" validate:"oneof=1 2"`

	// 用户列表 对应接收类型为1
	Users []User `json:"users"`

	// 用户组列表 对应接收类型为2
	UserGroups []UserGroup `json:"userGroups"`

	EnableCallback bool `json:"enableCallback"`

	CallbackUrls []string `json:"callbackUrls"`
}

// V1.4 新结构
type NotifyRule struct {
	NotifyRuleId   string `json:"notifyRuleId"`
	NotifyRuleName string `json:"notifyRuleName"`

	// 时分秒开始时间点 格式为 xx:xx:xx
	StartTime string `json:"startTime"`

	// 时分秒结束时间点 格式为 xx:xx:xx
	EndTime string `json:"endTime"`

	// 为了简化结构接收人结构
	NotifyActionPublic

	// 升级参数
	// validate:"max=3"表示限制数组长度最长为3
	EscalateParam []EscalateParam `json:"escalateParam,omitempty" validate:"max=3"`

	CreateTime int `json:"createTime"`
	UpdateTime int `json:"updateTime"`
}

type CallbackConfig struct {
	// custom - 自定义webhook、weCom - 企业微信通知、dingTalk - 钉钉通知
	WebhookType string `json:"webhookType"`

	WebhookList []WebhookDetail `json:"webhookList"`
}

type WebhookDetail struct {
	HookName   string            `json:"hookName"`
	HookMethod string            `json:"hookMethod"`
	HookUrl    string            `json:"hookUrl"`
	Headers    map[string]string `json:"headers"`
	Params     map[string]string `json:"params"`
}

type User struct {
	// UserId PRD中没有，预留
	UserId string `json:"userId"`

	UserName    string `json:"userName"`
	UserType    string `json:"userType"`
	PhoneNumber string `json:"phoneNumber"`
	Email       string `json:"email"`
}

type UserGroup struct {
	// GroupId PRD中没有，预留
	GroupId string `json:"groupId"`

	GroupName   string `json:"groupName"`
	Description string `json:"description"`
}

// 给后端查询的模型
type NotifyRuleBackend struct {
	AccountId          string                 `json:"accountId"`
	NotifyRuleId       string                 `json:"notifyRuleId"`
	NotifyRuleName     string                 `json:"notifyRuleName"`
	NotifyWindow       []TimeSpan             `json:"notifyWindow"`
	NotifyAction       []NotifyAction         `json:"notifyAction"`
	EscalateParam      []EscalateParamBackend `json:"escalateParam"`
	CallbackConfigList []CallbackConfig       `json:"callBackUrls"`
}

type NotifyAction struct {
	Media     string     `json:"media"`
	Receivers []Receiver `json:"receivers"`
}

type Receiver struct {
	ReceiverName string `json:"receiverName"`
	ReceiverId   string `json:"receiverId"` // 只有在receiverType为zhiban时，receiverId为值班表的id；其余情况为空
	ReceiverType string `json:"receiverType"`
}

type TimeSpan struct {
	StartTime string `json:"startTime"` // "00：00：00"格式
	EndTime   string `json:"endTime"`
}

type TimeSpanV2 struct {
	StartTime string `json:"startTimeStr"` // "00：00：00"格式
	EndTime   string `json:"endTimeStr"`
}

type NotifyActionPublic struct {
	// 通知渠道： 1 电话 2 短信 3 邮件
	Channel []int `json:"channel"`

	// 接收类型 1 用户 2 用户组
	ReceiverType int `json:"receiverType"`

	// 用户列表 对应接收类型为1
	Users []User `json:"users"`

	// 用户组列表 对应接收类型为2
	UserGroups []UserGroup `json:"userGroups"`

	// webhook 回调配置信息
	CallbackParam []CallbackConfig `json:"webhookConfigList"`
}

type EscalateParam struct {
	Rank      int       `json:"rank" validate:"oneof=1 2 3"`
	Condition Condition `json:"condition"`

	// NotifyRepeat 			NotifyRepeat 			`json:"notifyRepeat"`
	NotifyActionPublic NotifyActionPublic `json:"notifyAction"`
}

type Condition struct {
	// 多长时间未认领
	StayInUnclaimTime int `json:"stayInUnclaimTime" validate:"gt=0"`

	// 认领后多长时间未恢复
	StayInClaimTime int `json:"stayInClaimTime"`
}

type NotifyRuleListResponse struct {
	KeywordType string        `json:"keywordType,omitempty"`
	Keyword     string        `json:"keyword,omitempty"`
	OrderBy     string        `json:"orderBy,omitempty"`
	Order       string        `json:"order,omitempty"`
	PageNo      int           `json:"pageNo"`
	PageSize    int           `json:"pageSize"`
	TotalCount  int           `json:"totalCount"`
	Items       []*NotifyRule `json:"items"`
}

type EscalateParamBackend struct {
	Rank      int       `json:"rank"`
	Condition Condition `json:"condition"`

	// NotifyRepeat 			NotifyRepeat 			`json:"notifyRepeat"`
	NotifyAction []NotifyAction `json:"notifyAction"`

	// callback config
	CallbackConfigList []CallbackConfig `json:"callBackUrls"`
}
