// Code generated by MockGen. DO NOT EDIT.
// Source: ./types.go

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	cprom "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/cprom"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// CreateAgent mocks base method.
func (m *MockInterface) CreateAgent(ctx context.Context, args *cprom.CreateAgentRequest, opt *bce.SignOption) (*cprom.CreateAgentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAgent", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.CreateAgentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAgent indicates an expected call of CreateAgent.
func (mr *MockInterfaceMockRecorder) CreateAgent(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAgent", reflect.TypeOf((*MockInterface)(nil).CreateAgent), ctx, args, opt)
}

// CreateInstance mocks base method.
func (m *MockInterface) CreateInstance(ctx context.Context, args *cprom.CreateInstanceRequest, opt *bce.SignOption) (*cprom.CreateInstanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstance", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.CreateInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInstance indicates an expected call of CreateInstance.
func (mr *MockInterfaceMockRecorder) CreateInstance(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstance", reflect.TypeOf((*MockInterface)(nil).CreateInstance), ctx, args, opt)
}

// DeleteAgent mocks base method.
func (m *MockInterface) DeleteAgent(ctx context.Context, args *cprom.DeleteAgentRequest, opt *bce.SignOption) (*cprom.DeleteAgentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAgent", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.DeleteAgentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteAgent indicates an expected call of DeleteAgent.
func (mr *MockInterfaceMockRecorder) DeleteAgent(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAgent", reflect.TypeOf((*MockInterface)(nil).DeleteAgent), ctx, args, opt)
}

// DeleteAlertingRule mocks base method.
func (m *MockInterface) DeleteAlertingRule(ctx context.Context, args *cprom.DeleteAlertingRuleRequest, opt *bce.SignOption) (*cprom.DeleteAlertingRuleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAlertingRule", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.DeleteAlertingRuleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteAlertingRule indicates an expected call of DeleteAlertingRule.
func (mr *MockInterfaceMockRecorder) DeleteAlertingRule(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAlertingRule", reflect.TypeOf((*MockInterface)(nil).DeleteAlertingRule), ctx, args, opt)
}

// DeleteInstance mocks base method.
func (m *MockInterface) DeleteInstance(ctx context.Context, args *cprom.DeleteInstanceRequest, opt *bce.SignOption) (*cprom.DeleteInstanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteInstance", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.DeleteInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteInstance indicates an expected call of DeleteInstance.
func (mr *MockInterfaceMockRecorder) DeleteInstance(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInstance", reflect.TypeOf((*MockInterface)(nil).DeleteInstance), ctx, args, opt)
}

// DeleteNotifyRule mocks base method.
func (m *MockInterface) DeleteNotifyRule(ctx context.Context, args *cprom.DeleteNotifyRuleRequest, opt *bce.SignOption) (*cprom.DeleteNotifyRuleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteNotifyRule", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.DeleteNotifyRuleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteNotifyRule indicates an expected call of DeleteNotifyRule.
func (mr *MockInterfaceMockRecorder) DeleteNotifyRule(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteNotifyRule", reflect.TypeOf((*MockInterface)(nil).DeleteNotifyRule), ctx, args, opt)
}

// GetAgent mocks base method.
func (m *MockInterface) GetAgent(ctx context.Context, args *cprom.GetAgentRequest, opt *bce.SignOption) (*cprom.GetAgentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAgent", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.GetAgentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAgent indicates an expected call of GetAgent.
func (mr *MockInterfaceMockRecorder) GetAgent(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAgent", reflect.TypeOf((*MockInterface)(nil).GetAgent), ctx, args, opt)
}

// GetInstance mocks base method.
func (m *MockInterface) GetInstance(ctx context.Context, args *cprom.GetInstanceRequest, opt *bce.SignOption) (*cprom.GetInstanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstance", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.GetInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstance indicates an expected call of GetInstance.
func (mr *MockInterfaceMockRecorder) GetInstance(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstance", reflect.TypeOf((*MockInterface)(nil).GetInstance), ctx, args, opt)
}

// ListAgent mocks base method.
func (m *MockInterface) ListAgent(ctx context.Context, args cprom.ListAgentRequest, opt *bce.SignOption) (*cprom.ListAgentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAgent", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.ListAgentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAgent indicates an expected call of ListAgent.
func (mr *MockInterfaceMockRecorder) ListAgent(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAgent", reflect.TypeOf((*MockInterface)(nil).ListAgent), ctx, args, opt)
}

// ListAlertingRule mocks base method.
func (m *MockInterface) ListAlertingRule(ctx context.Context, args *cprom.ListAlertingRuleRequest, opt *bce.SignOption) (*cprom.ListAlertingRuleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAlertingRule", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.ListAlertingRuleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAlertingRule indicates an expected call of ListAlertingRule.
func (mr *MockInterfaceMockRecorder) ListAlertingRule(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAlertingRule", reflect.TypeOf((*MockInterface)(nil).ListAlertingRule), ctx, args, opt)
}

// ListInstance mocks base method.
func (m *MockInterface) ListInstance(ctx context.Context, args cprom.ListInstanceRequest, opt *bce.SignOption) (*cprom.ListInstanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstance", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.ListInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstance indicates an expected call of ListInstance.
func (mr *MockInterfaceMockRecorder) ListInstance(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstance", reflect.TypeOf((*MockInterface)(nil).ListInstance), ctx, args, opt)
}

// ListNotifyRule mocks base method.
func (m *MockInterface) ListNotifyRule(ctx context.Context, args *cprom.ListNotifyRuleRequest, opt *bce.SignOption) (*cprom.ListNotifyRuleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListNotifyRule", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.ListNotifyRuleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListNotifyRule indicates an expected call of ListNotifyRule.
func (mr *MockInterfaceMockRecorder) ListNotifyRule(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListNotifyRule", reflect.TypeOf((*MockInterface)(nil).ListNotifyRule), ctx, args, opt)
}

// SetDebug mocks base method.
func (m *MockInterface) SetDebug(debug bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", debug)
}

// SetDebug indicates an expected call of SetDebug.
func (mr *MockInterfaceMockRecorder) SetDebug(debug interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), debug)
}

// MockInstanceInterface is a mock of InstanceInterface interface.
type MockInstanceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInstanceInterfaceMockRecorder
}

// MockInstanceInterfaceMockRecorder is the mock recorder for MockInstanceInterface.
type MockInstanceInterfaceMockRecorder struct {
	mock *MockInstanceInterface
}

// NewMockInstanceInterface creates a new mock instance.
func NewMockInstanceInterface(ctrl *gomock.Controller) *MockInstanceInterface {
	mock := &MockInstanceInterface{ctrl: ctrl}
	mock.recorder = &MockInstanceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInstanceInterface) EXPECT() *MockInstanceInterfaceMockRecorder {
	return m.recorder
}

// CreateInstance mocks base method.
func (m *MockInstanceInterface) CreateInstance(ctx context.Context, args *cprom.CreateInstanceRequest, opt *bce.SignOption) (*cprom.CreateInstanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstance", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.CreateInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInstance indicates an expected call of CreateInstance.
func (mr *MockInstanceInterfaceMockRecorder) CreateInstance(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstance", reflect.TypeOf((*MockInstanceInterface)(nil).CreateInstance), ctx, args, opt)
}

// DeleteInstance mocks base method.
func (m *MockInstanceInterface) DeleteInstance(ctx context.Context, args *cprom.DeleteInstanceRequest, opt *bce.SignOption) (*cprom.DeleteInstanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteInstance", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.DeleteInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteInstance indicates an expected call of DeleteInstance.
func (mr *MockInstanceInterfaceMockRecorder) DeleteInstance(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInstance", reflect.TypeOf((*MockInstanceInterface)(nil).DeleteInstance), ctx, args, opt)
}

// GetInstance mocks base method.
func (m *MockInstanceInterface) GetInstance(ctx context.Context, args *cprom.GetInstanceRequest, opt *bce.SignOption) (*cprom.GetInstanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstance", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.GetInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstance indicates an expected call of GetInstance.
func (mr *MockInstanceInterfaceMockRecorder) GetInstance(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstance", reflect.TypeOf((*MockInstanceInterface)(nil).GetInstance), ctx, args, opt)
}

// ListInstance mocks base method.
func (m *MockInstanceInterface) ListInstance(ctx context.Context, args cprom.ListInstanceRequest, opt *bce.SignOption) (*cprom.ListInstanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstance", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.ListInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstance indicates an expected call of ListInstance.
func (mr *MockInstanceInterfaceMockRecorder) ListInstance(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstance", reflect.TypeOf((*MockInstanceInterface)(nil).ListInstance), ctx, args, opt)
}

// MockAgentInterface is a mock of AgentInterface interface.
type MockAgentInterface struct {
	ctrl     *gomock.Controller
	recorder *MockAgentInterfaceMockRecorder
}

// MockAgentInterfaceMockRecorder is the mock recorder for MockAgentInterface.
type MockAgentInterfaceMockRecorder struct {
	mock *MockAgentInterface
}

// NewMockAgentInterface creates a new mock instance.
func NewMockAgentInterface(ctrl *gomock.Controller) *MockAgentInterface {
	mock := &MockAgentInterface{ctrl: ctrl}
	mock.recorder = &MockAgentInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAgentInterface) EXPECT() *MockAgentInterfaceMockRecorder {
	return m.recorder
}

// CreateAgent mocks base method.
func (m *MockAgentInterface) CreateAgent(ctx context.Context, args *cprom.CreateAgentRequest, opt *bce.SignOption) (*cprom.CreateAgentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAgent", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.CreateAgentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAgent indicates an expected call of CreateAgent.
func (mr *MockAgentInterfaceMockRecorder) CreateAgent(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAgent", reflect.TypeOf((*MockAgentInterface)(nil).CreateAgent), ctx, args, opt)
}

// DeleteAgent mocks base method.
func (m *MockAgentInterface) DeleteAgent(ctx context.Context, args *cprom.DeleteAgentRequest, opt *bce.SignOption) (*cprom.DeleteAgentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAgent", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.DeleteAgentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteAgent indicates an expected call of DeleteAgent.
func (mr *MockAgentInterfaceMockRecorder) DeleteAgent(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAgent", reflect.TypeOf((*MockAgentInterface)(nil).DeleteAgent), ctx, args, opt)
}

// GetAgent mocks base method.
func (m *MockAgentInterface) GetAgent(ctx context.Context, args *cprom.GetAgentRequest, opt *bce.SignOption) (*cprom.GetAgentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAgent", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.GetAgentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAgent indicates an expected call of GetAgent.
func (mr *MockAgentInterfaceMockRecorder) GetAgent(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAgent", reflect.TypeOf((*MockAgentInterface)(nil).GetAgent), ctx, args, opt)
}

// ListAgent mocks base method.
func (m *MockAgentInterface) ListAgent(ctx context.Context, args cprom.ListAgentRequest, opt *bce.SignOption) (*cprom.ListAgentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAgent", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.ListAgentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAgent indicates an expected call of ListAgent.
func (mr *MockAgentInterfaceMockRecorder) ListAgent(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAgent", reflect.TypeOf((*MockAgentInterface)(nil).ListAgent), ctx, args, opt)
}

// MockAlertingRuleInterface is a mock of AlertingRuleInterface interface.
type MockAlertingRuleInterface struct {
	ctrl     *gomock.Controller
	recorder *MockAlertingRuleInterfaceMockRecorder
}

// MockAlertingRuleInterfaceMockRecorder is the mock recorder for MockAlertingRuleInterface.
type MockAlertingRuleInterfaceMockRecorder struct {
	mock *MockAlertingRuleInterface
}

// NewMockAlertingRuleInterface creates a new mock instance.
func NewMockAlertingRuleInterface(ctrl *gomock.Controller) *MockAlertingRuleInterface {
	mock := &MockAlertingRuleInterface{ctrl: ctrl}
	mock.recorder = &MockAlertingRuleInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAlertingRuleInterface) EXPECT() *MockAlertingRuleInterfaceMockRecorder {
	return m.recorder
}

// DeleteAlertingRule mocks base method.
func (m *MockAlertingRuleInterface) DeleteAlertingRule(ctx context.Context, args *cprom.DeleteAlertingRuleRequest, opt *bce.SignOption) (*cprom.DeleteAlertingRuleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAlertingRule", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.DeleteAlertingRuleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteAlertingRule indicates an expected call of DeleteAlertingRule.
func (mr *MockAlertingRuleInterfaceMockRecorder) DeleteAlertingRule(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAlertingRule", reflect.TypeOf((*MockAlertingRuleInterface)(nil).DeleteAlertingRule), ctx, args, opt)
}

// ListAlertingRule mocks base method.
func (m *MockAlertingRuleInterface) ListAlertingRule(ctx context.Context, args *cprom.ListAlertingRuleRequest, opt *bce.SignOption) (*cprom.ListAlertingRuleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAlertingRule", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.ListAlertingRuleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAlertingRule indicates an expected call of ListAlertingRule.
func (mr *MockAlertingRuleInterfaceMockRecorder) ListAlertingRule(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAlertingRule", reflect.TypeOf((*MockAlertingRuleInterface)(nil).ListAlertingRule), ctx, args, opt)
}

// MockNotifyRuleInterface is a mock of NotifyRuleInterface interface.
type MockNotifyRuleInterface struct {
	ctrl     *gomock.Controller
	recorder *MockNotifyRuleInterfaceMockRecorder
}

// MockNotifyRuleInterfaceMockRecorder is the mock recorder for MockNotifyRuleInterface.
type MockNotifyRuleInterfaceMockRecorder struct {
	mock *MockNotifyRuleInterface
}

// NewMockNotifyRuleInterface creates a new mock instance.
func NewMockNotifyRuleInterface(ctrl *gomock.Controller) *MockNotifyRuleInterface {
	mock := &MockNotifyRuleInterface{ctrl: ctrl}
	mock.recorder = &MockNotifyRuleInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNotifyRuleInterface) EXPECT() *MockNotifyRuleInterfaceMockRecorder {
	return m.recorder
}

// DeleteNotifyRule mocks base method.
func (m *MockNotifyRuleInterface) DeleteNotifyRule(ctx context.Context, args *cprom.DeleteNotifyRuleRequest, opt *bce.SignOption) (*cprom.DeleteNotifyRuleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteNotifyRule", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.DeleteNotifyRuleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteNotifyRule indicates an expected call of DeleteNotifyRule.
func (mr *MockNotifyRuleInterfaceMockRecorder) DeleteNotifyRule(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteNotifyRule", reflect.TypeOf((*MockNotifyRuleInterface)(nil).DeleteNotifyRule), ctx, args, opt)
}

// ListNotifyRule mocks base method.
func (m *MockNotifyRuleInterface) ListNotifyRule(ctx context.Context, args *cprom.ListNotifyRuleRequest, opt *bce.SignOption) (*cprom.ListNotifyRuleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListNotifyRule", ctx, args, opt)
	ret0, _ := ret[0].(*cprom.ListNotifyRuleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListNotifyRule indicates an expected call of ListNotifyRule.
func (mr *MockNotifyRuleInterfaceMockRecorder) ListNotifyRule(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListNotifyRule", reflect.TypeOf((*MockNotifyRuleInterface)(nil).ListNotifyRule), ctx, args, opt)
}
