// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
// CCE V2 版本 GO SDK, Interface 定义
package cprom

import (
	"context"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	cpromv1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/cprom/apis/v1"
	cpromv1beta "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/cprom/apis/v1beta1"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock -source=./types.go

// Interface 定义 CProm SDK
// 新增 Interface 后注意更新 doc.go !!!!!!!
type Interface interface {
	SetDebug(debug bool)

	InstanceInterface
	AgentInterface
	AlertingRuleInterface
	NotifyRuleInterface
}

type InstanceInterface interface {
	CreateInstance(ctx context.Context, args *CreateInstanceRequest, opt *bce.SignOption) (*CreateInstanceResponse, error)
	GetInstance(ctx context.Context, args *GetInstanceRequest, opt *bce.SignOption) (*GetInstanceResponse, error)
	DeleteInstance(ctx context.Context, args *DeleteInstanceRequest, opt *bce.SignOption) (*DeleteInstanceResponse, error)
	ListInstance(ctx context.Context, args ListInstanceRequest, opt *bce.SignOption) (*ListInstanceResponse, error)
}

type CreateInstanceRequest cpromv1.MonitorInstance

type CreateInstanceResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success"`
	Result  struct {
		InstanceID string `json:"instanceID"`
	} `json:"result"`
}

type GetInstanceRequest struct {
	InstanceID string
}

type GetInstanceResponse struct {
	Message string                   `json:"message"`
	Success bool                     `json:"success"`
	Result  *cpromv1.MonitorInstance `json:"result"`
}

type DeleteInstanceRequest struct {
	InstanceID string
}

type DeleteInstanceResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success"`
}

type ListInstanceRequest map[string]string

type ListInstanceResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success"`
	Result  struct {
		KeywordType string                    `json:"keywordType,omitempty"`
		Keyword     string                    `json:"keyword,omitempty"`
		OrderBy     string                    `json:"orderBy,omitempty"`
		Order       string                    `json:"order,omitempty"`
		PageNo      int                       `json:"pageNo"`
		PageSize    int                       `json:"pageSize"`
		TotalCount  int                       `json:"totalCount"`
		Items       []cpromv1.MonitorInstance `json:"items"`
	} `json:"result"`
}

type AgentInterface interface {
	CreateAgent(ctx context.Context, args *CreateAgentRequest, opt *bce.SignOption) (*CreateAgentResponse, error)
	GetAgent(ctx context.Context, args *GetAgentRequest, opt *bce.SignOption) (*GetAgentResponse, error)
	DeleteAgent(ctx context.Context, args *DeleteAgentRequest, opt *bce.SignOption) (*DeleteAgentResponse, error)
	ListAgent(ctx context.Context, args ListAgentRequest, opt *bce.SignOption) (*ListAgentResponse, error)
}

type CreateAgentRequest cpromv1.MonitorAgent

type CreateAgentResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success"`
	Result  struct {
		AgentID string `json:"agentID"`
	} `json:"result"`
}

type GetAgentRequest struct {
	AgentID    string
	InstanceID string
}

type GetAgentResponse struct {
	Message string                `json:"message"`
	Success bool                  `json:"success"`
	Result  *cpromv1.MonitorAgent `json:"result"`
}

type DeleteAgentRequest struct {
	InstanceID string
	AgentID    string
}

type DeleteAgentResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success"`
}

type ListAgentRequest map[string]string

type ListAgentResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success"`
	Result  struct {
		KeywordType string                 `json:"keywordType,omitempty"`
		Keyword     string                 `json:"keyword,omitempty"`
		OrderBy     string                 `json:"orderBy,omitempty"`
		Order       string                 `json:"order,omitempty"`
		PageNo      int                    `json:"pageNo"`
		PageSize    int                    `json:"pageSize"`
		TotalCount  int                    `json:"totalCount"`
		Items       []cpromv1.MonitorAgent `json:"items"`
	} `json:"result"`
}

type AlertingRuleInterface interface {
	DeleteAlertingRule(ctx context.Context, args *DeleteAlertingRuleRequest, opt *bce.SignOption) (*DeleteAlertingRuleResponse, error)
	ListAlertingRule(ctx context.Context, args *ListAlertingRuleRequest, opt *bce.SignOption) (*ListAlertingRuleResponse, error)
}

type ListAlertingRuleRequest struct {
	InstanceID  string
	KeywordType string
	Keyword     string
	PageNo      int
	PageSize    int
}

type ListAlertingRuleResponse struct {
	Message string                          `json:"message"`
	Success bool                            `json:"success"`
	Result  *cpromv1beta.VMRuleListResponse `json:"result"`
}

type DeleteAlertingRuleRequest struct {
	InstanceID     string
	AlertingRuleID string
}

type DeleteAlertingRuleResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success"`
}

type NotifyRuleInterface interface {
	DeleteNotifyRule(ctx context.Context, args *DeleteNotifyRuleRequest, opt *bce.SignOption) (*DeleteNotifyRuleResponse, error)
	ListNotifyRule(ctx context.Context, args *ListNotifyRuleRequest, opt *bce.SignOption) (*ListNotifyRuleResponse, error)
}

type ListNotifyRuleRequest struct {
	KeywordType string
	Keyword     string
	PageNo      int
	PageSize    int
}

type ListNotifyRuleResponse struct {
	Message string                          `json:"message"`
	Success bool                            `json:"success"`
	Result  *cpromv1.NotifyRuleListResponse `json:"result"`
}

type DeleteNotifyRuleRequest struct {
	InstanceID   string
	NotifyRuleID string
}

type DeleteNotifyRuleResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success"`
}
