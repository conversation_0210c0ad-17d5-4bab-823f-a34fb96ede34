package aiinfra

import (
	"context"
	"fmt"
	"testing"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/retrypolicy"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/sts"
	log "icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

func getStsClient() sts.Interface {
	ctx := context.Background()
	stsClient := sts.NewClient(ctx,
		BCEConfig(ctx, "http://sts.bj.iam.sdns.baidu.com:8586/v1", 30, "bj"),
		BCEConfig(ctx, "http://iam.bj.bce-internal.baidu.com/v3", 30, "bj"),
		"BceServiceRole_aihc-serverless", "aihc-serverless", "XOf7CxlPgX9p9P1tHBvTGFbH8sLjUJPq",
	)
	return stsClient
}

func getSignOption() *bce.SignOption {
	return getStsClient().NewSignOption(
		context.Background(), "eca97e148cb74e9683d7b7240829d1ff")
}

// TODO 换成mockserver，真实server ep已关闭
func getClient() *Client {
	return NewClient(BCEConfig(context.Background(), "http://gwgp-v9tbmnxijtj.b.bdcloudapi.com", 30, "bj"))
}

func BCEConfig(ctx context.Context, endpoint string, timeout int, region string) *bce.Config {
	// endpoint 为空没有关系，最多请求会不通。如果这个方法返回nil，没有error，调用方会空指针
	if endpoint == "" {
		log.Errorf(ctx, "Generate bce.Config: endpoint is empty")
	}

	return &bce.Config{
		Checksum:    true,
		Endpoint:    endpoint,
		Timeout:     time.Duration(timeout) * time.Second,
		Region:      region,
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(ctx, 0, 0),
	}
}

func TestClusterInfo(t *testing.T) {
	client := getClient()
	m, err := client.GetClusterInfo(context.Background(), "c-x3fldhkz3w2v", getSignOption())
	if err != nil {
		t.Log(err)
		return
	}
	fmt.Printf("%+v\n", m)
}

func TestCreateCluster(t *testing.T) {
	client := getClient()
	request := &CreateClusterRequest{
		ClusterName: "test-mock",
		TemplateRef: &TemplateRef{
			Name: "template01",
		},
		Network: &ClusterNetwork{
			PrimaryCIDR:              "21.0.0.0/8",
			SecondaryCIDRs:           []string{"22.0.0.0/24"},
			SubnetAllocationStrategy: "OnDemand",
		},
		Tenant: &ClusterTenant{
			AccountID: "2e1be1eb99e946c3b543ec5a4eaa7d39",
		},
		Tags:    make([]*Tag, 0),
		Plugins: nil,
	}
	cluster, err := client.CreateCluster(context.Background(), request, getSignOption())
	if err != nil {
		t.Log(err)
		return
	}
	fmt.Printf("%+v\n", cluster)
	fmt.Printf("clusterID %s\n", cluster.ClusterID)
	if cluster.ClusterID == "" {
		t.Fatal("clusterID should not be empty")
	}
	//查看详情
	info, err := client.GetClusterInfo(context.Background(), cluster.ClusterID, getSignOption())
	if err != nil {
		t.Error(err)
		return
	}
	fmt.Printf("%+v\n", info)
	fmt.Printf("clusterID %s\n", info.Cluster.Spec.ClusterID)
	if info.Cluster.Spec.ClusterID != cluster.ClusterID {
		t.Error("clusterID should be equal")
		return
	}
	err = client.DeleteCluster(context.Background(), info.Cluster.Spec.ClusterID, getSignOption())
	if err != nil {
		t.Logf("delete cluster %s error: %s", info.Cluster.Spec.ClusterID, err)
		return
	}
	fmt.Printf("delete clusterID %s\n", info.Cluster.Spec.ClusterID)
	//查看详情
	info, err = client.GetClusterInfo(context.Background(), cluster.ClusterID, getSignOption())
	if err != nil {
		t.Log(err)
		return
	}
	fmt.Printf("%+v\n", info)
	fmt.Printf("clusterID %s\n", info.Cluster.Spec.ClusterID)

}

func TestResourcePool(t *testing.T) {
	client := getClient()
	template := NodeTemplate{
		Flavor: "bcc.g5.c2m8",
		NodeResource: NodeResource{
			RootDiskType: "cloud_hp1",
			RootDiskSize: 100,
		},
		Provider:      "bce",
		ImageID:       "b7fb3b9d-c3f6-4828-a749-827cc4057241",
		UserData:      "",
		AdminPassword: "cce@1234",
		ChargeType:    "postpay",
		Tags:          nil,
		Labels:        nil,
		Annotations:   nil,
		Type:          "user",
		EhcClusterID:  "xxx",
	}
	request := &CreateResourcePoolRequest{
		ResourceGroupName: "test-mock-1",
		Tenant: ResourcePoolTenant{
			VpcID:     "vpc-63gz689s324b",
			SubnetIDs: []string{"sbn-0sgdb7cen5c8"},
		},
		NodeSets: []NodeSets{
			{
				Replicas: 1,
				Template: template,
			},
		},
	}
	resourcePool, err := client.CreateResourcePoolInCluster(context.Background(), request, "c-fvnt6o8z7rl0", getSignOption())
	if err != nil {
		t.Logf("create resource pool error: %s", err)
		return
	}
	fmt.Printf("pool: %+v\n", resourcePool)
	//err = client.DeleteResourcePool("c-fvnt6o8z7rl0", pool.ResourceGroupID)
	//if err != nil {
	//	t.Errorf("delete resource pool error: %s", err)
	//	return
	//}
	//resourcePool, err := client.GetResourcePool("c-fvnt6o8z7rl0", pool.ResourceGroupID)
	//if err != nil {
	//	t.Errorf("get resource pool error: %s", err)
	//	return
	//}
	fmt.Printf("resourcePool: %+v\n", resourcePool)
	//if resourcePool.ResourceGroup.Spec.ResourceGroupID != pool.ResourceGroupID {
	//	t.Error("resource pool id should be equal")
	//	return
	//}
	instanceRequest := &CreateInstanceInResourcePoolRequest{
		Replicas:     1,
		NodeTemplate: template,
	}
	instance, err := client.CreateInstanceInResourcePool(context.Background(), instanceRequest, "c-fvnt6o8z7rl0", resourcePool.ResourceGroupID, getSignOption())
	if err != nil {
		t.Logf("create instance in resource pool error: %s", err)
		return
	}
	fmt.Printf("instance: %+v\n", instance)

}

func TestNetwork(t *testing.T) {
	client := getClient()
	req := &CreateNetworkRequest{
		AccountID: "8342d8a8b3654eafba85432324041c47",
		UserID:    "9a5e71c836144cf3b5af8b7c20be0023",
		PrivateLinks: PrivateLink{
			//CreatedBy: "eca97e148cb74e9683d7b7240829d1ff",
			SubnetIDs: []string{"sbn-gbgf9w8qsm62"},
			VpcID:     "vpc-i8a1z7ktkuc9",
		},
		BlbSpec: BlbSpec{
			BlbID: "lb-********",
		},
	}
	network, err := client.CreateNetwork(context.Background(), req, "c-fvnt6o8z7rl0", getSignOption())
	if err != nil {
		t.Logf("create network error: %s", err)
		return
	}
	fmt.Printf("network: %+v\n", network)

}
