package aiinfra

import "time"

type PrivateLink struct {
	CreatedBy string   `json:"createdBy"`
	SubnetIDs []string `json:"subnetIDs"`
	VpcID     string   `json:"vpcID"`
}
type BlbSpec struct {
	BlbID string `json:"blbID"`
}

type RecommendNetworkConfigRequest struct {
	NodeCount int           `json:"nodeCount"`
	Tenant    ClusterTenant `json:"tenant,omitempty"`
}

type RecommendNetworkConfigResponse struct {
	CommonResponse          `json:",inline"`
	*RecommendNetworkConfig `json:",inline"`
}

type RecommendNetworkConfig struct {
	NodeCIDR string `json:"nodeCIDR,omitempty"`
	PodCIDR  string `json:"podCIDR,omitempty"`
	ErrMsg   string `json:"errMsg,omitempty"`
}

type EstimateNodeCountRequest struct {
	NodeCIDR string        `json:"nodeCIDR"`
	PodCIDR  string        `json:"podCIDR,omitempty"`
	Tenant   ClusterTenant `json:"tenant,omitempty"`
}

type EstimateNodeCountResponse struct {
	CommonResponse           `json:",inline"`
	*EstimateNodeCountResult `json:",inline"`
}

type EstimateNodeCountResult struct {
	NodeCount int    `json:"nodeCount,omitempty"`
	ErrMsg    string `json:"errMsg,omitempty"`
}

type CreateNetworkRequest struct {
	AccountID    string      `json:"accountID"`
	UserID       string      `json:"userID"`
	PrivateLinks PrivateLink `json:"privateLinks"`
	BlbSpec      BlbSpec     `json:"blbSpec"`
}
type CreateNetworkResponse struct {
	RequestID string `json:"requestId"`
	NetworkID string `json:"networkID"`
}

type GetNetworkResponse struct {
	RequestID string `json:"requestId"`
	Network   struct {
		Spec struct {
			AccountID    string `json:"accountID"`
			UserID       string `json:"userID"`
			PrivateLinks struct {
				CreatedBy string   `json:"createdBy"`
				SubnetIDs []string `json:"subnetIDs"`
				VpcID     string   `json:"vpcID"`
				BlbSpec   struct {
					BlbID string `json:"blbID"`
				} `json:"blbSpec"`
			} `json:"privateLinks"`
			Status struct {
				Phase         string    `json:"phase"`
				PublishPoint  string    `json:"publishPoint"`
				BlbID         string    `json:"blbID"`
				Reason        string    `json:"reason"`
				LastProbeTime time.Time `json:"lastProbeTime"`
				LinkStatus    []struct {
					VpcID     string    `json:"vpcID"`
					SubnetID  string    `json:"subnetID"`
					ServiceID string    `json:"serviceID"`
					IP        string    `json:"ip"`
					Status    string    `json:"status"`
					StartTime time.Time `json:"startTime"`
					Reason    string    `json:"reason"`
					CreatedBy string    `json:"createdBy"`
				} `json:"linkStatus"`
			} `json:"status"`
		} `json:"spec"`
	} `json:"network"`
}
