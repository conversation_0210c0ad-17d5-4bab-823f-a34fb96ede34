package aiinfra

import (
	ccetypes "icode.baidu.com/baidu/cce-test/e2e-test/pkg/types"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type StepName string

const (
	ClusterStepCreateCertificateAuthority StepName = "创建基础证书"
	ClusterStepCreateLB                   StepName = "创建 BLB"
	ClusterStepCreateEIP                  StepName = "创建 EIP"
	ClusterStepWaitMasterInfrastructure   StepName = "创建 Master"
	ClusterStepWaitAPIServerAccess        StepName = "连通 APIServer"
	ClusterStepDeployK8SPlugin            StepName = "部署 K8S 插件"

	ClusterStepDeleteK8SResource     StepName = "删除 K8S 资源"
	ClusterStepDeleteNodeInstance    StepName = "删除 Worker"
	ClusterStepDeleteMasterInstance  StepName = "删除 Master"
	ClusterStepDeleteEIP             StepName = "删除 EIP"
	ClusterStepDeleteLB              StepName = "删除 BLB"
	ClusterStepDeleteConfigMapInMeta StepName = "清理控制资源"

	InstanceStepCreateMachine               StepName = "准备机器"
	InstanceStepCreateMachineAndReinstallOS StepName = "准备机器 (重装操作系统)"
	InstanceStepEnsureSecurityGroups        StepName = "绑定安全组"
	InstanceStepWaitAPIServerWhiteList      StepName = "前置检查"
	InstanceStepDeploy                      StepName = "部署 K8S"
	InstanceStepSyncNodeInfo                StepName = "对齐节点信息"
	InstanceStepWaitForNodeReady            StepName = "等待就绪"
	InstanceStepEnsurePostUserScript        StepName = "执行用户后置脚本"

	NodeStepDrainK8SNode                    StepName = "节点排水"
	NodeStepDeleteK8SNode                   StepName = "移出集群"
	InstanceStepDeleteMachineAssociatedIaaS StepName = "回收关联资源"
	InstanceStepDeleteMachine               StepName = "删除机器"
	InstanceStepDeleteMachineAssociatedENI  StepName = "回收关联弹性网卡"
)

// StepStatus - 集群操作步骤状态
type StepStatus string

const (
	// StepStatusTodo 待开始
	StepStatusTodo StepStatus = "todo"

	// StepStatusDoing 正在进行中
	StepStatusDoing StepStatus = "doing"

	// StepStatusPaused 暂停
	StepStatusPaused StepStatus = "paused"

	// StepStatusDone 已完成
	StepStatusDone StepStatus = "done"

	// StepStatusFailed 已失败
	StepStatusFailed StepStatus = "failed"
)

// NodeStep ...
type NodeStep struct {
	StepName   StepName   `json:"stepName"`
	StepStatus StepStatus `json:"stepStatus"`
	ccetypes.Step
	Steps []SubStep `json:"step"`
}

type SubStep struct {
	StepName         StepName                    `json:"stepName"`
	StepStatus       StepStatus                  `json:"stepStatus"`
	Ready            bool                        `json:"ready,omitempty"`
	StartTime        *metav1.Time                `json:"startTime,omitempty"`        // 第一次开始时间
	FinishedTime     *metav1.Time                `json:"finishedTime,omitempty"`     // 最后一次成功时间
	CostSeconds      int                         `json:"costSeconds,omitempty"`      // 花费时间
	RetryCount       int                         `json:"retryCount,omitempty"`       // 重试次数
	TraceID          string                      `json:"traceID,omitempty"`          // cce 侧 requestID, errorInfo 暴露后，去除该字段
	ErrMsg           string                      `json:"errMsg,omitempty"`           // 失败信息
	ErrorInfo        ccetypes.ReconcileResponse  `json:"errInfo,omitempty"`          // 失败信息
	DecideDeployType ccetypes.InstanceDeployType `json:"decideDeployType,omitempty"` // 最终部署方式
}
