package aiinfra

import (
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

type Client struct {
	*bce.Client
}

func NewClient(config *bce.Config) *Client {
	bceClient := bce.NewClient(config)
	return &Client{Client: bceClient}
}

func (c *Client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

func (c *Client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}
