package aiinfra

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type ResourcePoolTenant struct {
	AccountID string   `json:"accountID,omitempty"`
	VpcID     string   `json:"vpcID,omitempty"`
	SubnetIDs []string `json:"subnetIDs,omitempty"`
}

type ResourceGroupNetwork struct {
	PublicAccess PublicAccess `json:"publicAccess,omitempty"`
	// SubnetAllocationStrategy 表示子网划分策略
	SubnetAllocationStrategy SubnetAllocationStrategy `json:"subnetAllocationStrategy,omitempty"`
}

type ResourceGroupStorage struct {
	PFS *PFS `json:"pfs,omitempty"`
}

type PFS struct {
	PfsID      string `json:"pfsID,omitempty"`
	PfsMountID string `json:"pfsMountID,omitempty"`
}

type EniSubnet struct {
	Subnets []string `json:"subnets"`
}

type PublicAccess struct {
	Enabled bool   `json:"enabled,omitempty"`
	NatID   string `json:"natID,omitempty"`
}

type MonitorInstance struct {
	AutoCreateMonitorInstance bool   `json:"autoCreateMonitorInstance"`
	MonitorInstanceID         string `json:"monitorInstanceID"`
}

type NodeSets struct {
	Replicas int          `json:"replicas,omitempty"`
	Template NodeTemplate `json:"template,omitempty"`
	// 如果 replicas 和 template 为空时生效
	ExistingNodes []ExistingNode `json:"existingNodes,omitempty"`
}

type CreateResourcePoolRequest struct {
	ResourceGroupName string `json:"resourceGroupName,omitempty"`
	// ResourceGroupNetwork 表示资源组的网络
	Network ResourceGroupNetwork `json:"network,omitempty"`

	// Storage 表示资源组的 PFS 配置
	Storage  ResourceGroupStorage `json:"storage,omitempty"`
	Tenant   ResourcePoolTenant   `json:"tenant,omitempty"`
	NodeSets []NodeSets           `json:"nodeSets,omitempty"`
}

type CreateResourcePoolResponse struct {
	RequestID       string `json:"requestId,omitempty"`
	ResourceGroupID string `json:"ResourceGroupID,omitempty"`
}

type CreateInstanceInResourcePoolRequest struct {
	Existed bool `json:"existed"`
	// Replicas 表示该规格副本数
	Replicas      int `json:"replicas,omitempty"`
	NodeTemplate  `json:",inline"`
	ExistedOption ExistedOption `json:"existedOption,omitempty"`
}

type ExistingNode struct {
	ProviderID string               `json:"providerID"`
	Provider   ResourceProviderType `json:"provider"`
}

type CreateInstanceInResourcePoolResponse struct {
	RequestID   string   `json:"requestID,omitempty"`
	InstanceIDs []string `json:"instanceIDs,omitempty"`
}

// ResourceGroup is the Schema for the resourcegroups API
type ResourceGroup struct {
	Spec   ResourceGroupSpec   `json:"spec,omitempty"`
	Status ResourceGroupStatus `json:"status,omitempty"`
}

type ResourceGroupType string

const (
	ResourceGroupTypeSystem ResourceGroupType = "system"
	ResourceGroupTypeUser   ResourceGroupType = "user"
)

// ResourceGroupSpec defines the desired state of ResourceGroup
type ResourceGroupSpec struct {
	// INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	// ClusterID 表示集群的唯一 ID 这里的集群ID对应的是 AI infra cluster 的 ID
	ClusterID string `json:"clusterID,omitempty"`

	// ResourceGroupID 表示资源组 ID, 同 Metadata.Name
	ResourceGroupID string `json:"resourceGroupID"`

	// ResourceGroupName 表示资源组名称
	ResourceGroupName string `json:"resourceGroupName,omitempty"`

	// Type 表示资源池类型
	Type ResourceGroupType `json:"type,omitempty"`

	// ServiceRole 表示操作 IaaS 接口使用的服务身份
	ServiceRole string `json:"serviceRole,omitempty"`

	// ResourceGroupNetwork 表示资源组的网络
	Network ResourceGroupNetwork `json:"network,omitempty"`

	// Storage 表示资源组的 PFS 配置
	Storage ResourceGroupStorage `json:"storage,omitempty"`

	// Tenant 表示资源组租户信息
	Tenant ResourcePoolTenant `json:"tenant,omitempty"`

	// NodeSpecs 表示集群 NodeSpec 列表
	NodeSets []NodeSets `json:"nodeSets,omitempty"`
}

// ResourceGroupStatus defines the observed state of ResourceGroup
type ResourceGroupStatus struct {
	// INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
	// Important: Run "make" to regenerate code after modifying this file
	Phase ResourceGroupPhase `json:"phase,omitempty"`

	// LastProbeTime the last access time
	LastProbeTime *metav1.Time `json:"lastProbeTime,omitempty"`

	// Reason indicate why the component cannot work. Empty if everything is ok
	Reason string `json:"reason,omitempty"`

	// Message manual message along with Reason
	Message string `json:"message,omitempty"`

	// InstanceGroup 表示底层节点组
	InstanceGroup *InstanceGroup `json:"instanceGroup,omitempty"`

	NodeSetStatuses []NodeSetStatus `json:"nodeSetStatus,omitempty"`

	// Network 表示资源池状态
	Network ResourceGroupNetworkStatus `json:"network,omitempty"`
}

type ResourceGroupNetworkStatus struct {
	Phase      ResourceGroupNetworkPhase `json:"phase,omitempty"`
	VPCs       []InfraVpcStatus          `json:"vpcs,omitempty"`
	CSNs       []InfraCSNStatus          `json:"csns,omitempty"`
	Conditions []metav1.Condition        `json:"conditions,omitempty"`
}

type ResourceGroupNetworkPhase string

type InstanceGroup struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"` //TODO 名称不建议记录，用户修改节点组名称后还存在不一致的情况
}

type NodeSetStatus struct {
	TemplateID string       `json:"templateID,omitempty"`
	NodeInfos  []NodeInfo   `json:"nodeStatus,omitempty"`
	Status     NodeSetPhase `json:"status,omitempty"`
	// Reason indicate why the component cannot work. Empty if everything is ok
	Reason string `json:"reason,omitempty"`

	// Message manual message along with Reason
	Message string `json:"message,omitempty"`

	// LastProbeTime the last access time
	LastProbeTime *metav1.Time `json:"lastProbeTime,omitempty"`
}

type GetResourcePoolResponse struct {
	RequestID     string         `json:"requestId,omitempty"`
	ResourceGroup *ResourceGroup `json:"resourceGroup,omitempty"`
}

type ListResourceGroupPage struct {
	PageNo     int              `json:"pageNo"`
	PageSize   int              `json:"pageSize"`
	TotalCount int              `json:"totalCount"`
	Items      []*ResourceGroup `json:"items"`
}

type ResourcePoolListResponse struct {
	RequestID string                `json:"requestId,omitempty"`
	Page      ListResourceGroupPage `json:"page"`
	ClusterID string                `json:"ClusterID,omitempty"`
}

type NodeInfo struct {
	// InstanceID 表示 CCE 中 节点 ID
	InstanceID string `json:"instanceID,omitempty"`
	// ProviderID 表示在具体 资源来源 的 ID
	ProviderID string `json:"providerID,omitempty"`
	// Provider 表示资源来源
	Provider ResourceProviderType `json:"provider,omitempty"`

	Existing bool       `json:"existing,omitempty"`
	Status   NodeStatus `json:"status,omitempty"`
}

type NodeStatus struct {
	Phase NodePhase `json:"phase,omitempty"`

	// Reason indicate why the component cannot work. Empty if everything is ok
	Reason string `json:"reason,omitempty"`

	// Message manual message along with Reason
	Message string `json:"message,omitempty"`
}
