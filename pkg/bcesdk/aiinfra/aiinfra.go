/*
 * Copyright (c) 2024. Baidu,Inc. All rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package aiinfra

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	log "icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

var (
	ErrorInstanceNotFound = errors.New("ErrorInstanceNotFound")
	ErrorClusterNotFound  = errors.New("ErrorClusterNotFound")
)

func (c *Client) GetClusterInfo(ctx context.Context, clusterID string,
	option *bce.SignOption) (*GetClusterInfoResponse, error) {
	params := NewCommonParams()
	url := fmt.Sprintf("/ai-infra/api/v1/clusters/%s", clusterID)

	req, err := bce.NewRequest(http.MethodGet, c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	clusterInfo := &GetClusterInfoResponse{}
	err = json.Unmarshal(bodyContent, clusterInfo)
	if err != nil {
		return nil, err
	}
	return clusterInfo, nil
}

func (c *Client) GetClusterCreateSteps(ctx context.Context, clusterID string,
	option *bce.SignOption) (*GetClusterCreateStepsResponse, error) {
	params := NewCommonParams()
	url := fmt.Sprintf("/ai-infra/api/v1/clusters/%s/steps", clusterID)

	req, err := bce.NewRequest(http.MethodGet, c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	clusterCreateSteps := &GetClusterCreateStepsResponse{}
	err = json.Unmarshal(bodyContent, clusterCreateSteps)
	if err != nil {
		return nil, err
	}
	return clusterCreateSteps, nil
}

func (c *Client) CreateCluster(ctx context.Context, request *CreateClusterRequest,
	option *bce.SignOption) (*CreateClusterResponse, error) {
	params := NewCommonParams()
	url := "/ai-infra/api/v1/clusters"
	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}
	req, err := bce.NewRequest(http.MethodPost, c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	clusterInfo := &CreateClusterResponse{}
	err = json.Unmarshal(bodyContent, clusterInfo)
	if err != nil {
		return nil, err
	}
	return clusterInfo, nil
}

func (c *Client) CreateClusterV2(ctx context.Context, request *CreateClusterRequestV2,
	option *bce.SignOption) (*CreateClusterResponse, error) {
	params := NewCommonParams()
	url := "/ai-infra/api/v1/clusters"
	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}
	req, err := bce.NewRequest(http.MethodPost, c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	clusterInfo := &CreateClusterResponse{}
	err = json.Unmarshal(bodyContent, clusterInfo)
	if err != nil {
		return nil, err
	}
	return clusterInfo, nil
}

func (c *Client) DeleteCluster(ctx context.Context, id string, option *bce.SignOption) error {
	params := NewCommonParams()

	url := fmt.Sprintf("/ai-infra/api/v1/clusters/%s", id)
	req, err := bce.NewRequest(http.MethodDelete, c.GetURL(url, params), nil)
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}
func (c *Client) CreateResourcePoolInCluster(ctx context.Context,
	request *CreateResourcePoolRequest, clusterID string,
	option *bce.SignOption) (*CreateResourcePoolResponse, error) {
	params := NewCommonParams()

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("/ai-infra/api/v1/clusters/%s/resourceGroups", clusterID)
	req, err := bce.NewRequest(http.MethodPost, c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	createResult := &CreateResourcePoolResponse{}
	err = json.Unmarshal(bodyContent, createResult)
	if err != nil {
		return nil, err
	}
	return createResult, nil
}

func (c *Client) UpdateResourcePoolStorage(ctx context.Context,
	request *ResourceGroupStorage, clusterID, resourceGroupID string,
	option *bce.SignOption) error {
	params := NewCommonParams()

	postContent, err := json.Marshal(request)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("/ai-infra/api/v1/clusters/%s/resourceGroups/%s/storage", clusterID, resourceGroupID)
	req, err := bce.NewRequest(http.MethodPut, c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) UpdateResourcePoolNetwork(ctx context.Context, request *ResourceGroupNetwork,
	clusterID, resourceGroupID string, option *bce.SignOption) error {
	params := NewCommonParams()

	postContent, err := json.Marshal(request)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("/ai-infra/api/v1/clusters/%s/resourceGroups/%s/network", clusterID, resourceGroupID)
	req, err := bce.NewRequest(http.MethodPut, c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) UpdateClusterEniSubnet(ctx context.Context, request *EniSubnet,
	clusterID string, option *bce.SignOption) error {
	params := NewCommonParams()

	postContent, err := json.Marshal(request)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("/ai-infra/api/v1/clusters/%s/addEniSubnets", clusterID)
	req, err := bce.NewRequest(http.MethodPut, c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) UpdateClusterMonitorInstance(ctx context.Context, request *MonitorInstance,
	clusterID string, option *bce.SignOption) error {
	params := NewCommonParams()

	postContent, err := json.Marshal(request)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("/ai-infra/api/v1/clusters/%s/relateMonitor", clusterID)
	req, err := bce.NewRequest(http.MethodPut, c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) GetResourcePool(ctx context.Context, clusterID string,
	resourcePoolID string, option *bce.SignOption) (*GetResourcePoolResponse, error) {
	params := NewCommonParams()
	url := fmt.Sprintf("/ai-infra/api/v1/clusters/%s/resourceGroups/%s", clusterID, resourcePoolID)

	req, err := bce.NewRequest(http.MethodGet, c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	result := &GetResourcePoolResponse{}
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) ListResourceGroups(ctx context.Context, clusterID string, option *bce.SignOption) (*ResourcePoolListResponse, error) {
	params := NewCommonParams()
	url := fmt.Sprintf("/ai-infra/api/v1/clusters/%s/resourceGroups", clusterID)

	req, err := bce.NewRequest(http.MethodGet, c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	result := &ResourcePoolListResponse{}
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) DeleteResourcePool(ctx context.Context, clusterID string, resourcePoolID string,
	option *bce.SignOption) error {
	params := NewCommonParams()

	url := fmt.Sprintf("/ai-infra/api/v1/clusters/%s/resourceGroups/%s", clusterID, resourcePoolID)
	req, err := bce.NewRequest(http.MethodDelete, c.GetURL(url, params), nil)
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) CreateInstanceInResourcePool(ctx context.Context, request *CreateInstanceInResourcePoolRequest,
	clusterID string, resourceGroupID string,
	option *bce.SignOption) (*CreateInstanceInResourcePoolResponse, error) {
	params := NewCommonParams()

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("/ai-infra/api/v1/clusters/%s/resourceGroups/%s/nodes", clusterID, resourceGroupID)
	req, err := bce.NewRequest(http.MethodPost, c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	createResult := &CreateInstanceInResourcePoolResponse{}
	err = json.Unmarshal(bodyContent, createResult)
	if err != nil {
		return nil, err
	}
	return createResult, nil
}

func (c *Client) GetResourcePoolInstances(ctx context.Context, clusterID string, resourcePoolID string,
	option *bce.SignOption) (*InstanceListResponse, error) {
	params := NewCommonParams()
	// TODO 分页
	url := fmt.Sprintf("/ai-infra/api/v1/clusters/%s/resourceGroups/%s/nodes", clusterID, resourcePoolID)

	req, err := bce.NewRequest(http.MethodGet, c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	result := &InstanceListResponse{}
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) DeleteResourcePoolInstances(ctx context.Context, clusterID string, resourcePoolID string,
	args *DeleteInstancesParams, option *bce.SignOption) error {
	params := NewCommonParams()
	url := fmt.Sprintf("/ai-infra/api/v1/clusters/%s/resourceGroups/%s/deleteNodes", clusterID, resourcePoolID)

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest(http.MethodPost, c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) GetInstance(ctx context.Context, clusterID string, resourceGroupID string, instanceID string,
	option *bce.SignOption) (*InstanceGetResponse, error) {
	params := NewCommonParams()
	url := fmt.Sprintf("/ai-infra/api/v1/clusters/%s/resourceGroups/%s/nodes/%s", clusterID, resourceGroupID, instanceID)

	req, err := bce.NewRequest(http.MethodGet, c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	result := &InstanceGetResponse{}
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) CreateNetwork(ctx context.Context, request *CreateNetworkRequest,
	clusterID string, option *bce.SignOption) (*CommonResponse, error) {
	params := NewCommonParams()

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("/ai-infra/api/v1/clusters/%s/networks", clusterID)
	req, err := bce.NewRequest(http.MethodPost, c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	createResult := &CommonResponse{}
	err = json.Unmarshal(bodyContent, createResult)
	if err != nil {
		return nil, err
	}
	return createResult, nil
}

func (c *Client) GetNetwork(ctx context.Context, clusterID, networkID string,
	option *bce.SignOption) (*GetNetworkResponse, error) {

	params := NewCommonParams()
	url := fmt.Sprintf("/ai-infra/api/v1/clusters/%s/networks/%s", clusterID, networkID)

	req, err := bce.NewRequest(http.MethodGet, c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	result := &GetNetworkResponse{}
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) DeleteNetwork(ctx context.Context, clusterID,
	networkID string, option *bce.SignOption) error {
	params := NewCommonParams()

	url := fmt.Sprintf("/ai-infra/api/v1/clusters/%s/networks/%s", clusterID, networkID)
	req, err := bce.NewRequest(http.MethodDelete, c.GetURL(url, params), nil)
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) RecommendNetwork(ctx context.Context, request *RecommendNetworkConfigRequest,
	option *bce.SignOption) (*RecommendNetworkConfigResponse, error) {
	params := NewCommonParams()

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	url := "/ai-infra/api/v1/networks/recommendNetworkCIDRs"
	req, err := bce.NewRequest(http.MethodPost, c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	recommendResult := &RecommendNetworkConfigResponse{}
	err = json.Unmarshal(bodyContent, recommendResult)
	if err != nil {
		return nil, err
	}
	return recommendResult, nil
}

func (c *Client) EstimateNodeCount(ctx context.Context, request *EstimateNodeCountRequest,
	option *bce.SignOption) (*EstimateNodeCountResponse, error) {
	params := NewCommonParams()

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	url := "/ai-infra/api/v1/networks/estimateNodeCount"
	req, err := bce.NewRequest(http.MethodPost, c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	estimateResult := &EstimateNodeCountResponse{}
	err = json.Unmarshal(bodyContent, estimateResult)
	if err != nil {
		return nil, err
	}
	return estimateResult, nil
}

// CreateClusterPrecheck create cluster precheck
func (c *Client) CreateClusterPrecheck(ctx context.Context, request *CreateClusterRequestV2,
	option *bce.SignOption) (*CreateClusterPrecheckResponse, error) {
	params := NewCommonParams()
	url := "/ai-infra/api/v1/clusters/precheck"
	postContent, err := json.Marshal(request)
	if err != nil {
		log.Infof(ctx, "CreateClusterPrecheck, error: %v,request:%v", err, request)
		return nil, err
	}
	req, err := bce.NewRequest(http.MethodPost, c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		log.Infof(ctx, "CreateClusterPrecheck, error: %v,request:%v", err, request)
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		log.Infof(ctx, "CreateClusterPrecheck, error: %v,request:%v", err, request)
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		log.Infof(ctx, "CreateClusterPrecheck, error: %v,request:%v", err, request)
		return nil, err
	}

	res := &CreateClusterPrecheckResponse{}
	err = json.Unmarshal(bodyContent, res)
	if err != nil {
		log.Infof(ctx, "CreateClusterPrecheck, error: %v,request:%v", err, request)
		return nil, err
	}
	log.Infof(ctx, "CreateClusterPrecheck, resp: %s,res: %v\n", string(bodyContent), res)
	if res.WorkflowID == "" {
		return nil, fmt.Errorf("workflowID is empty")
	}
	return res, nil
}

// GetClusterPrecheck get cluster precheck
func (c *Client) GetClusterPrecheck(ctx context.Context, workflowID string,
	option *bce.SignOption) (*GetClusterPrecheckResponse, error) {
	params := NewCommonParams()
	url := fmt.Sprintf("/ai-infra/api/v1/clusters/precheck/%s", workflowID)

	req, err := bce.NewRequest(http.MethodGet, c.GetURL(url, params), nil)
	if err != nil {
		log.Infof(ctx, "GetClusterPrecheck, error: %v,workflowID:%s", err, workflowID)
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		log.Infof(ctx, "GetClusterPrecheck, error: %v,workflowID:%s", err, workflowID)
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		log.Infof(ctx, "GetClusterPrecheck, error: %v,workflowID:%s", err, workflowID)
		return nil, err
	}
	res := &GetClusterPrecheckResponse{}
	err = json.Unmarshal(bodyContent, res)
	if err != nil {
		log.Infof(ctx, "GetClusterPrecheck, error: %v,workflowID:%s,bodyContent:%s", err, workflowID, string(bodyContent))
		return nil, err
	}
	log.Infof(ctx, "GetClusterPrecheck, resp: %s,res: %v\n", string(bodyContent), res)
	return res, nil
}

// GetNodeCreateProcess get node create process
func (c *Client) GetNodeCreateProcess(ctx context.Context, clusterID, resourceGroupID, instanceID string,
	option *bce.SignOption) (*GetNodeCreateProcessResponse, error) {
	params := NewCommonParams()
	url := fmt.Sprintf("/ai-infra/api/v1/clusters/%s/resourceGroups/%s/nodes/%s/steps", clusterID, resourceGroupID, instanceID)

	req, err := bce.NewRequest(http.MethodGet, c.GetURL(url, params), nil)
	if err != nil {
		log.Infof(ctx, "GetNodeCreateProcess, error: %v,clusterID:%s,rgID:%s,instanceID:%s", err, clusterID, resourceGroupID, instanceID)
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		log.Infof(ctx, "GetNodeCreateProcess, error: %v,clusterID:%s,rgID:%s,instanceID:%s", err, clusterID, resourceGroupID, instanceID)
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		log.Infof(ctx, "GetNodeCreateProcess, error: %v,clusterID:%s,rgID:%s,instanceID:%s", err, clusterID, resourceGroupID, instanceID)
		return nil, err
	}
	res := &GetNodeCreateProcessResponse{}
	err = json.Unmarshal(bodyContent, res)
	if err != nil {
		log.Infof(ctx, "GetNodeCreateProcess, error: %v,clusterID:%s,rgID:%s,instanceID:%s", err, clusterID, resourceGroupID, instanceID)
		return nil, err
	}
	log.Infof(ctx, "GetNodeCreateProcess, resp: %s,res: %v\n", string(bodyContent), res)
	return res, nil
}
