package aiinfra

import (
	"context"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
)

// Interface vpc interface
type Interface interface {
	SetDebug(bool)
	GetClusterInfo(ctx context.Context, clusterID string,
		option *bce.SignOption) (*GetClusterInfoResponse, error)
	GetInstance(ctx context.Context, clusterID string, resourceGroupID string, instanceID string,
		option *bce.SignOption) (*InstanceGetResponse, error)
	CreateCluster(ctx context.Context, request *CreateClusterRequest,
		option *bce.SignOption) (*CreateClusterResponse, error)
	CreateClusterV2(ctx context.Context, request *CreateClusterRequestV2,
		option *bce.SignOption) (*CreateClusterResponse, error)
	DeleteCluster(ctx context.Context, id string, option *bce.SignOption) error
	CreateResourcePoolInCluster(ctx context.Context, request *CreateResourcePoolRequest, clusterID string,
		option *bce.SignOption) (*CreateResourcePoolResponse, error)
	UpdateResourcePoolStorage(ctx context.Context, request *ResourceGroupStorage, clusterID, resourceGroupID string,
		option *bce.SignOption) error
	UpdateResourcePoolNetwork(ctx context.Context, request *ResourceGroupNetwork, clusterID, resourceGroupID string,
		option *bce.SignOption) error
	GetResourcePool(ctx context.Context, clusterID string, resourcePoolID string,
		option *bce.SignOption) (*GetResourcePoolResponse, error)
	ListResourceGroups(ctx context.Context, clusterID string, option *bce.SignOption) (*ResourcePoolListResponse, error)
	DeleteResourcePool(ctx context.Context, clusterID string, resourcePoolID string,
		option *bce.SignOption) error
	CreateInstanceInResourcePool(ctx context.Context, request *CreateInstanceInResourcePoolRequest, clusterID string,
		resourcePoolID string, option *bce.SignOption) (*CreateInstanceInResourcePoolResponse, error)
	GetResourcePoolInstances(ctx context.Context, clusterID string, resourcePoolID string,
		option *bce.SignOption) (*InstanceListResponse, error)
	CreateNetwork(ctx context.Context, request *CreateNetworkRequest, clusterID string,
		option *bce.SignOption) (*CommonResponse, error)
	GetNetwork(ctx context.Context, clusterID, networkID string,
		option *bce.SignOption) (*GetNetworkResponse, error)
	DeleteNetwork(ctx context.Context, clusterID, networkID string, option *bce.SignOption) error
	DeleteResourcePoolInstances(ctx context.Context, clusterID string, resourcePoolID string,
		args *DeleteInstancesParams, option *bce.SignOption) error
	GetClusterCreateSteps(ctx context.Context, clusterID string,
		option *bce.SignOption) (*GetClusterCreateStepsResponse, error)
	CreateClusterPrecheck(ctx context.Context, request *CreateClusterRequestV2,
		option *bce.SignOption) (*CreateClusterPrecheckResponse, error)
	GetClusterPrecheck(ctx context.Context, workflowID string,
		option *bce.SignOption) (*GetClusterPrecheckResponse, error)

	RecommendNetwork(ctx context.Context, request *RecommendNetworkConfigRequest,
		option *bce.SignOption) (*RecommendNetworkConfigResponse, error)
	EstimateNodeCount(ctx context.Context, request *EstimateNodeCountRequest,
		option *bce.SignOption) (*EstimateNodeCountResponse, error)
	GetNodeCreateProcess(ctx context.Context, clusterID, resourceGroupID, instanceID string,
		option *bce.SignOption) (*GetNodeCreateProcessResponse, error)
	UpdateClusterMonitorInstance(ctx context.Context, request *MonitorInstance,
		clusterID string, option *bce.SignOption) error
	UpdateClusterEniSubnet(ctx context.Context, request *EniSubnet,
		clusterID string, option *bce.SignOption) error
}

func NewCommonParams() map[string]string {
	return map[string]string{
		"clientToken": util.CreateRandomString(),
	}
}

type CommonResponse struct {
	RequestID string `json:"requestId"`
}
