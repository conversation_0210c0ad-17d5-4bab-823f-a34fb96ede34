package aiinfra

import (
	"fmt"
	"net/http"
	"time"

	corev1 "k8s.io/api/core/v1"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bccimage"
)

type InstancePhase string

type InstanceLabels map[string]string

type InstanceTaints []corev1.Taint

type CDSConfigList []CDSConfig

type ChargingType string

// RuntimeType defines the runtime on each node
type RuntimeType string

// ClusterRole master & slave
type ClusterRole string

// MachineType: BCC, BBC
type MachineType string

type CleanPolicy string

const (
	ChargingTypePrepaid  ChargingType = "Prepaid"
	ChargingTypePostpaid ChargingType = "Postpaid"

	DefaultCleanPolicy             = RemainCleanPolicy
	RemainCleanPolicy  CleanPolicy = "Remain"
	DeleteCleanPolicy  CleanPolicy = "Delete"
)

type DeleteInstancesParams struct {
	InstanceIDs []string    `json:"instanceIDs,omitempty"`
	CleanPolicy CleanPolicy `json:"cleanPolicy,omitempty"`
}

// Instance
type Instance struct {
	Spec   *InstanceSpec   `json:"spec"`
	Status *InstanceStatus `json:"status"`

	CreatedAt time.Time `json:"createdAt,omitempty"`
	UpdatedAt time.Time `json:"updatedAt,omitempty"`
}

// InstanceSpec - Instance Spec
type InstanceSpec struct {
	CCEInstanceID string `json:"cceInstanceID"`
	InstanceName  string `json:"instanceName"`

	RuntimeType    RuntimeType `json:"runtimeType"`
	RuntimeVersion string      `json:"runtimeVersion"`

	ClusterID   string      `json:"clusterID"`
	ClusterRole ClusterRole `json:"clusterRole"`
	UserID      string      `json:"userID"`

	InstanceGroupID   string `json:"instanceGroupID"`
	InstanceGroupName string `json:"instanceGroupName"`

	MachineType MachineType `json:"machineType"`

	// 是否为已有实例
	Existed       bool          `json:"existed"`
	ExistedOption ExistedOption `json:"existedOption"`

	InstanceType string     `json:"instanceType"`
	BBCOption    *BBCOption `json:"bbcOption,omitempty"`

	VPCConfig `json:"vpcConfig"`

	InstanceResource InstanceResource `json:"instanceResource"`

	DeployCustomConfig DeployCustomConfig `json:"deployCustomConfig,omitempty"`

	ImageID    string     `json:"imageID"`
	InstanceOS InstanceOS `json:"instanceOS"`

	NeedEIP   bool       `json:"needEIP"`
	EIPOption *EIPOption `json:"eipOption"`

	SSHKeyID string `json:"sshKeyID"`

	InstanceChargingType string `json:"instanceChargingType"`

	DeleteOption DeleteOption `json:"deleteOption"`

	Tags   []*Tag         `json:"tags,omitempty"`
	Labels InstanceLabels `json:"labels,omitempty"`
	Taints InstanceTaints `json:"taints,omitempty"`
}

// InstanceStatus node instance status
// InstanceStatus - Instance Status
type InstanceStatus struct {
	Machine Machine `json:"machine"`

	InstancePhase InstancePhase `json:"instancePhase"`
	MachineStatus string        `json:"machineStatus"`
}

type InstanceListResponse struct {
	CommonResponse
	Page ListInstancesByInstanceGroupIDPage `json:"page"`
}

type InstanceGetResponse struct {
	CommonResponse
	Instance *Instance `json:"instance"`
}

type ListInstancesByInstanceGroupIDPage struct {
	PageNo     int         `json:"pageNo"`
	PageSize   int         `json:"pageSize"`
	TotalCount int         `json:"totalCount"`
	Items      []*Instance `json:"items"`
}

// CDSConfig clone from BCC
// refer: https://cloud.baidu.com/doc/BCC/s/6jwvyo0q2/#createcdsmodel
type CDSConfig struct {
	Path        string `json:"diskPath,omitempty"`
	StorageType string `json:"storageType,omitempty"`
	CDSSize     int    `json:"cdsSize,omitempty"`
	SnapshotID  string `json:"snapshotID,omitempty"`
}

type EIPOption struct {
	EIPName         string `json:"eipName,omitempty"`
	EIPChargingType string `json:"eipChargeType,omitempty"`
	EIPBandwidth    int    `json:"eipBandwidth,omitempty"`
}

type DeleteOption struct {
	MoveOut           bool `json:"moveOut,omitempty"`
	DeleteResource    bool `json:"deleteResource,omitempty"`
	DeleteCDSSnapshot bool `json:"deleteCDSSnapshot,omitempty"`
}

// Machine - 定义机器相关信息
type Machine struct {
	InstanceID string `json:"instanceID"`

	OrderID string `json:"orderID,omitempty"`

	MountList []MountConfig `json:"mountList,omitempty"`

	VPCIP     string `json:"vpcIP,omitempty"`
	VPCIPIPv6 string `json:"vpcIPIPv6,omitempty"`

	EIP string `json:"eip,omitempty"`

	K8sNodeName string `json:"k8SNodeName,omitempty"`
}

type MountConfig struct {
	Path        string `json:"diskPath,omitempty"` // "/data"
	CDSID       string `json:"cdsID,omitempty"`
	Device      string `json:"device,omitempty"` // "/dev/vdb"
	CDSSize     int    `json:"cdsSize,omitempty"`
	StorageType string `json:"storageType,omitempty"`
}

// ExistedOption 已有实例相关配置
type ExistedOption struct {
	ProviderIDs []string `json:"providerIDs,omitempty"`
	Rebuild     bool     `json:"rebuild"`
}

// BBCOption BBC config
type BBCOption struct {
	ReserveData bool   `json:"reserveData,omitempty"`
	RaidID      string `json:"raidID,omitempty"`
	SysDiskSize int    `json:"sysDiskSize,omitempty"`
}

// VPCConfig 定义 Instance VPC
type VPCConfig struct {
	VPCID           string `json:"vpcID"`
	VPCSubnetID     string `json:"vpcSubnetID"`
	SecurityGroupID string `json:"securityGroupID"`

	VPCSubnetType     string `json:"vpcSubnetType"`
	VPCSubnetCIDR     string `json:"VPCSubnetCIDR"`
	VPCSubnetCIDRIPv6 string `json:"VPCSubnetCIDRIPv6"`

	AvailableZone string `json:"availableZone"`
}

// InstanceResource - Instance CPU/MEM/Disk config
type InstanceResource struct {
	CPU int `json:"cpu,omitempty" valid:"Required"` // unit: Core
	MEM int `json:"mem,omitempty" valid:"Required"` // unit: GB

	NodeCPUQuota int `json:"nodeCPUQuota,omitempty"` // unit: Core
	NodeMEMQuota int `json:"nodeMEMQuota,omitempty"` // unit: GB

	// RootDisk
	RootDiskType string `json:"rootDiskType,omitempty" valid:"Required"`
	RootDiskSize int    `json:"rootDiskSize,omitempty" valid:"Required"` // unit: GB

	// Only necessary when InstanceType = GPU
	LocalDiskSize int `json:"localDiskSize,omitempty"` // unit: GB

	// CDS list
	CDSList CDSConfigList `json:"cdsList,omitempty"`

	// Only necessary when InstanceType = GPU
	GPUType  string `json:"gpuType,omitempty"`
	GPUCount int    `json:"gpuCount,omitempty"`
}

// TODO：需要的时候再填充，同CCE
type NodeResource struct {
	// 新建节点，必填；已有节点，不填；
	CPU int `json:"cpu,omitempty" valid:"Required"` // unit: Core
	// 新建节点，必填；已有节点，不填；
	MEM int `json:"mem,omitempty" valid:"Required"` // unit: GB

	RootDiskType string `json:"rootDiskType,omitempty"`
	RootDiskSize int    `json:"rootDiskSize,omitempty"`

	// GPU 机器必须指定, 其他机器不用
	LocalDiskSize int `json:"localDiskSize,omitempty"` // unit: GB

	// GPU卡型号，eg: gen40
	GPUType string `json:"gpuType,omitempty"`
	// GPU单实例卡数，eg: 8
	GPUCount int `json:"gpuCount,omitempty"`
	// 对应BCC specId, eg：“c1”
	SpecID string `json:"specId,omitempty"`
	// 对应BCC spec, eg：“bcc.c1.c1m2”
	MachineSpec string `json:"machineSpec,omitempty"`
}

type ContainerdConfig struct {
	DataRoot           string   `json:"dataRoot,omitempty"`           // 自定义 containerd 数据目录
	RegistryMirrors    []string `json:"registryMirrors,omitempty"`    // 自定义 RegistryMirrors
	InsecureRegistries []string `json:"insecureRegistries,omitempty"` // 自定义 InsecureRegistries
}
type DeployCustomConfig struct {
	// containerd相关配置
	ContainerdConfig ContainerdConfig `json:"containerdConfig,omitempty"`

	// container unlimit memlock配置，在实例使用 RDMA 时启用
	ContainerUnlimitMemlock bool `json:"unlimitMemlock,omitempty"`

	// kubelet数据目录
	KubeletRootDir string `json:"kubeletRootDir,omitempty"`

	// k8s进程资源预留配额
	// key:value: cpu: 50m, memory: 100Mi
	KubeReserved map[string]string `json:"kubeReserved,omitempty"`
	// 系统进程资源预留配额
	// key:value: cpu: 50m, memory: 100Mi
	SystemReserved map[string]string `json:"systemReserved,omitempty"`

	// 部署前执行脚本
	PreUserScript string `json:"preUserScript,omitempty"`
	// 部署后执行脚本
	PostUserScript string `json:"postUserScript,omitempty"`

	PostUserScriptFailedAutoCordon bool `json:"postUserScriptFailedAutoCordon,omitempty"`

	CudaVersion   string `json:"cudaVersion"`
	CudnnVersion  string `json:"cudnnVersion"`
	DriverVersion string `json:"driverVersion"`
}

type ResourcePreChargingOption struct {
	PurchaseTime      int    `json:"purchaseTime,omitempty"`      // 预付费才生效，12 = 12 月
	PurchaseTimeUnit  string `json:"purchaseTimeUnit,omitempty"`  // 预付费时间单位
	AutoRenew         bool   `json:"autoRenew,omitempty"`         // 是否自动续费
	AutoRenewTimeUnit string `json:"autoRenewTimeUnit,omitempty"` // 续费单位：月
	AutoRenewTime     int    `json:"autoRenewTime,omitempty"`     // 12 = 12 个月
}

// InstanceOS defines the OS of BCC
type InstanceOS struct {
	ImageType      bccimage.ImageType      `json:"imageType,omitempty" gorm:"column:image_type"` // 镜像类型
	ImageName      string                  `json:"imageName,omitempty" gorm:"column:image_name"` // 镜像名字: ubuntu-14.04.1-server-amd64-201506171832
	OSType         bccimage.OSType         `json:"osType,omitempty" gorm:"column:os_type"`       // e.g. linux
	OSName         bccimage.OSName         `json:"osName,omitempty" gorm:"column:os_name"`       // e.g. Ubuntu
	OSVersion      string                  `json:"osVersion,omitempty" gorm:"column:os_version"` // e.g. 14.04.1 LTS
	OSArch         string                  `json:"osArch,omitempty" gorm:"column:os_arch"`       // e.g. x86_64 (64bit)
	OSBuild        string                  `json:"osBuild,omitempty" gorm:"column:os_build"`     // e.g. 2015061700
	SpecialVersion bccimage.SpecialVersion `json:"specialVersion,omitempty" gorm:"-"`            // 特殊字段，目前用来标志内部上云 TODO 入库
}

type NodeTemplate struct {
	TemplateName      string                     `json:"templateName,omitempty"`
	AvailableZone     string                     `json:"availableZone,omitempty"`
	InstanceType      InstanceType               `json:"instanceType"`
	Flavor            string                     `json:"flavor,omitempty"`
	NodeResource      NodeResource               `json:"nodeResource,omitempty"`
	Provider          ResourceProviderType       `json:"provider,omitempty"`
	ImageID           string                     `json:"imageID,omitempty"`
	InstanceOS        InstanceOS                 `json:"instanceOS,omitempty" `
	UserData          string                     `json:"userData,omitempty"`
	AdminPassword     string                     `json:"adminPassword,omitempty"`
	ChargeType        ChargingType               `json:"chargeType,omitempty"`
	PreChargingOption *ResourcePreChargingOption `json:"preChargingOption,omitempty"`
	Tags              []*Tag                     `json:"tags,omitempty"`
	Labels            map[string]string          `json:"labels,omitempty"`
	Annotations       map[string]string          `json:"annotations,omitempty"`
	Taints            []corev1.Taint             `json:"taints,omitempty"`
	Type              string                     `json:"type,omitempty"` // system, user, default user
	// DeployCustomConfig 表示节点的部署配置
	DeployCustomConfig DeployCustomConfig `json:"deployCustomConfig,omitempty"`
	SubnetID           string             `json:"subnetID,omitempty"`
	EhcClusterID       string             `json:"ehcClusterID,omitempty"`
}

// InstanceType instance 类型
type InstanceType string

const (
	// InstanceTypeN1 普通型 BCC 实例: 通用型g1、计算型c1、密集计算型ic1、内存型m1
	InstanceTypeN1 InstanceType = "N1"

	// InstanceTypeN2 普通型II BCC 实例: 通用型g2、计算型c2、密集计算型ic2、内存型m2
	InstanceTypeN2 InstanceType = "N2"

	// InstanceTypeN3 普通型Ⅲ BCC 实例: 通用型g3、计算型c3、密集计算型ic3、内存型m3
	InstanceTypeN3 InstanceType = "N3"

	// InstanceTypeN4 网络增强型 BCC 实例: 通用网络增强型g3ne、计算网络增强型c3ne、内存网络增强型m3ne
	InstanceTypeN4 InstanceType = "N4"

	// InstanceTypeN5 普通型Ⅳ BCC实例: 通用型g4、密集计算型ic4、计算型c4、内存型m4
	InstanceTypeN5 InstanceType = "N5"

	// InstanceTypeC1 计算优化型实例: 高主频计算型hcc1、高主频通用型hcg1
	InstanceTypeC1 InstanceType = "C1"

	// InstanceTypeC2 计算优化 Ⅱ 型实例: 高主频计算型hcc2、高主频通用型hcg2
	InstanceTypeC2 InstanceType = "C2"

	// InstanceTypeS1 存储优化型实例: 本地SSD型l1
	InstanceTypeS1 InstanceType = "S1"

	// InstanceTypeG1 GPU 型实例
	InstanceTypeG1 InstanceType = "G1"

	// InstanceTypeF1 FPGA 型实例
	InstanceTypeF1 InstanceType = "F1"

	// TODO: 以下为 CCE 自行定义
	// InstanceTypeDCC DCC 类型
	InstanceTypeDCC InstanceType = "DCC"

	// InstanceTypeBBC BBC 类型
	InstanceTypeBBC InstanceType = "BBC"

	// InstanceTypeBBCGPU BBC GPU 类型
	InstanceTypeBBCGPU InstanceType = "BBC_GPU"

	// Serverless集群master类型
	InstanceTypeServerlessMaster InstanceType = "ServerlessMaster"

	// Edge集群BEC类型
	InstanceTypeBEC InstanceType = "BEC"

	InstanceTypeKunlun InstanceType = "25"
)

// GetNodeCreateProcessResponse ...
type GetNodeCreateProcessResponse struct {
	Status    string     `json:"status"`
	Steps     []NodeStep `json:"steps"`
	RequestID string     `json:"requestId"`
}

// ErrResponse defines the return messages when an error occurred.
// Reference will be omitted if it does not exist.
// swagger:model
type ErrResponse struct {
	RequestID string `json:"requestId"`

	// Code defines the business error code.
	Code int `json:"code"`

	// Message contains the detail of this message.
	// This message is suitable to be exposed to external
	Message string `json:"message"`
}

func (b *ErrResponse) Error() string {
	return fmt.Sprintf("\"requestId\":\"%s\",\"code\":%d,\"message\":\"%s\"",
		b.Message, b.Code, b.RequestID)
}

func IsNotFound(err error) bool {
	bceErr, ok := err.(*ErrResponse)
	if !ok {
		return false
	}

	return bceErr.Code == http.StatusNotFound
}
