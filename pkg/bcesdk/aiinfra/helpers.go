/*
 * Copyright (c) 2024. Baidu,Inc. All rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package aiinfra

// CanDelete
func (object *Cluster) CanDelete() bool {
	if object.Status.Phase == ClusterStatusPhaseRunning ||
		object.Status.Phase == ClusterStatusPhaseDeleting ||
		object.Status.Phase == ClusterStatusPhaseCreateFailed {
		for _, group := range object.Status.ResourceGroups {
			if group.Type != ResourceGroupTypeSystem {
				return false
			}
		}
		return true
	}
	return false
}

func (object *ResourceGroup) CanDelete() bool {
	if object.Status.Phase == ResourceGroupPhaseRunning {
		return true
	}
	if len(object.Spec.NodeSets) != 0 {
		return false
	}
	return false
}

// IsStable
// 只允许向ResourceGroup的稳定状态，增加节点
func (object *ResourceGroup) IsStable() bool {
	if object.Status.Phase == ResourceGroupPhaseInitializing {
		return false
	}
	for _, nodeSet := range object.Status.NodeSetStatuses {
		if nodeSet.Status == NodeSetStatusAttaching ||
			nodeSet.Status == NodeSetStatusDetaching {
			return false
		}
		if len(nodeSet.NodeInfos) > 0 {
			for _, nodeInfo := range nodeSet.NodeInfos {
				if nodeInfo.Status.Phase == NodePhaseAttaching || nodeInfo.Status.Phase == NodePhaseDetaching {
					return false
				}
			}
		}
	}
	return true
}

// HasNode
// 只允许删除没有节点的resourcegroup
func (object *ResourceGroup) HasNode() bool {

	nodeInfos := make([]NodeInfo, 0)
	for _, nodeSet := range object.Status.NodeSetStatuses {
		for _, nodeInfo := range nodeSet.NodeInfos {
			if nodeInfo.Status.Phase != NodePhaseDetached {
				nodeInfos = append(nodeInfos, nodeInfo)
			}
		}
	}
	if len(nodeInfos) > 0 {
		return true
	}
	return false
}
