package aiinfra

type ClusterStatusPhase string

const (
	ClusterStatusPhasePending       ClusterStatusPhase = "pending"
	ClusterStatusPhaseCreating      ClusterStatusPhase = "creating"
	ClusterPhaseProvisioning        ClusterStatusPhase = "provisioning"
	ClusterPhaseProvisioned         ClusterStatusPhase = "provisioned"
	ClusterStatusPhaseRunning       ClusterStatusPhase = "running"       // 允许删除
	ClusterStatusPhaseCreateFailed  ClusterStatusPhase = "create_failed" // 允许删除
	ClusterStatusPhaseDeleting      ClusterStatusPhase = "deleting"
	ClusterStatusPhaseDeleteFailed  ClusterStatusPhase = "delete_failed"
	ClusterStatusPhaseUpgrading     ClusterStatusPhase = "upgrading"
	ClusterStatusPhaseUpgradeFailed ClusterStatusPhase = "upgrade_failed"
)

type PluginStatusPhase string

const (
	PluginStatusPhasePending        PluginStatusPhase = "Pending"
	PluginStatusPhaseAbnormal       PluginStatusPhase = "Abnormal" // 安装组件流程异常，进入abnormal
	PluginStatusPhaseInstalling     PluginStatusPhase = "Installing"
	PluginStatusPhaseUninstalling   PluginStatusPhase = "Uninstalling"
	PluginStatusPhaseFailed         PluginStatusPhase = "Failed" // 组件安装失败进入failed，这个与组件状态保持一致
	PluginStatusPhaseUninstalled    PluginStatusPhase = "Uninstalled"
	PluginStatusPhaseDeployed       PluginStatusPhase = "Deployed" // 大部分组件安装成功状态
	PluginStatusPhaseRunning        PluginStatusPhase = "Running"  // log operator这种带健康检查类的组件的状态
	PluginStatusPhasePartiallyReady PluginStatusPhase = "Partially Ready"
	PluginStatusPhaseNotFound       PluginStatusPhase = "Not Found"
	PluginStatusPhaseUnknown        PluginStatusPhase = "Unknown"
)

type PublicLinkBridgePhase string

const (
	PublicLinkBridgePhaseReady  = "ready"
	PublicLinkBridgePhaseFailed = "failed"
)

type ResourceGroupPhase string

const (
	ResourceGroupPhasePending      ResourceGroupPhase = ""
	ResourceGroupPhaseInitializing ResourceGroupPhase = "initializing"
	ResourceGroupPhaseRunning      ResourceGroupPhase = "running" // 允许删除
	ResourceGroupPhaseDeleting     ResourceGroupPhase = "deleting"
)

type NodeSetPhase string

const (
	NodeSetStatusAttaching NodeSetPhase = "attaching"
	NodeSetStatusAttached  NodeSetPhase = "attached"
	NodeSetStatusDetaching NodeSetPhase = "detaching"
	NodeSetStatusDetached  NodeSetPhase = "detached"
)

// NodePhase 定义了 Node 的状态,状态同步 CCE 集群的节点状态
type NodePhase string

const (
	NodePhaseAttaching NodePhase = "attaching"
	NodePhaseAttached  NodePhase = "attached"
	// TODO 增加一个状态退单逻辑
	NodePhaseAttachFailed NodePhase = "attach_failed" // 节点异常，允许删除；
	NodePhaseDetaching    NodePhase = "detaching"
	NodePhaseDetached     NodePhase = "detached"
	NodePhaseDetachFailed NodePhase = "detach_failed" // 删除节点异常，人工介入
)

// AiInfra instance 的 phase 状态
const (
	// InstancePhasePending 创建节点时默认状态
	InstancePhasePending InstancePhase = "pending"

	// InstancePhaseProvisioning IaaS 相关资源正在创建中
	InstancePhaseProvisioning InstancePhase = "provisioning"

	// InstancePhaseProvisioned IaaS 相关资源已经 Ready
	InstancePhaseProvisioned InstancePhase = "provisioned"

	// InstancePhaseDeployed 部署已经完成，等待node Ready
	InstancePhaseDeployed InstancePhase = "deployed"

	// InstancePhaseRunning 节点运行正常
	InstancePhaseRunning InstancePhase = "running"

	InstancePhaseCordon InstancePhase = "ready_scheduling_disabled"

	// InstancePhaseCreateFailed 节点异常
	InstancePhaseCreateFailed InstancePhase = "create_failed"

	// InstancePhaseDeleting 节点正在删除
	InstancePhaseDeleting InstancePhase = "deleting"

	// InstancePhaseDeleted 节点删除完成
	InstancePhaseDeleted InstancePhase = "deleted"

	// InstancePhaseDeleteFailed 节点删除失败
	InstancePhaseDeleteFailed InstancePhase = "delete_failed"
)

// EventStatus - Event 类型
type EventStatus string

const (
	// EventStatusCreating -
	EventStatusCreating EventStatus = "creating"

	// EventStatusCreated -
	EventStatusCreated EventStatus = "created"

	// EventStatusCreateFailed -
	EventStatusCreateFailed EventStatus = "create_failed"

	// EventStatusDeleting -
	EventStatusDeleting EventStatus = "deleting"

	// EventStatusDeleted -
	EventStatusDeleted EventStatus = "deleted"

	// EventStatusDeleteFailed -
	EventStatusDeleteFailed EventStatus = "delete_failed"
)
