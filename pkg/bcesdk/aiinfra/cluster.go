package aiinfra

import (
	"encoding/json"
	"errors"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type SubnetAllocationStrategy string

const (
	SubnetAllocationStrategyCustom   SubnetAllocationStrategy = "Custom"
	SubnetAllocationStrategyOnDemand SubnetAllocationStrategy = "OnDemand"
)

type ResourceProviderType string

const (
	ResourceProviderTypeBce     ResourceProviderType = "bce"
	ResourceProviderTypeBceEdge ResourceProviderType = "bceEdge"
)

type ClusterNetwork struct {
	PrimaryCIDR              string                   `json:"primaryCIDR,omitempty"`
	PodCIDR                  string                   `json:"podCIDR,omitempty"`
	SecondaryCIDRs           []string                 `json:"secondaryCIDRs,omitempty"`
	SubnetAllocationStrategy SubnetAllocationStrategy `json:"subnetAllocationStrategy"`
}

type ClusterTenant struct {
	AccountID string `json:"accountID,omitempty"`

	// VPCID 表示租户的 VPC ID
	VPCID string `json:"vpcID,omitempty"`

	ServiceSubnetIDs []string `json:"serviceSubnetIDs,omitempty"`
}

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

type Plugin struct {
	// 插件类型(插件名称) 非必要 用户要部署的是哪个插件,传空时和PluginName保持一致
	PluginName string `json:"pluginName,omitempty"`

	// Version 表示组件版本, 除非用户要指定版本否则无需传递此值
	Version string `json:"version,omitempty"`

	// Namespace 插件部署到哪个命名空间  非必要
	Namespace string `json:"namespace,omitempty"`

	// Description 非必要
	Description string `json:"description,omitempty"`

	// Values 取决于插件 系统插件传空值即可
	Values string `json:"values,omitempty"`

	// Options 表示针对插件的特殊配置，比如 CProm 场景中，配置采集规则
	Options map[string]string `json:"options,omitempty"`
}

type TemplateRef struct {
	Name string `json:"name"`
}

type ClusterSpec struct {
	ClusterName            string           `json:"clusterName,omitempty"`
	ClusterID              string           `json:"clusterID,omitempty"`
	TemplateRef            TemplateRef      `json:"templateRef,omitempty"`
	Network                ClusterNetwork   `json:"network,omitempty"`
	Tenant                 ClusterTenant    `json:"tenant,omitempty"`
	Plugins                []Plugin         `json:"plugins,omitempty"`
	EnableDeleteProtection bool             `json:"enableDeleteProtection,omitempty"`
	PublicLinkBridge       PublicLinkBridge `json:"publicLinkBridge,omitempty"`
	MonitorCollector       MonitorCollector `json:"monitorCollector"`
}

type PublicLinkBridge struct {
	Enabled bool `json:"enabled,omitempty"`
}

// ClusterStatus defines the observed state of Cluster
type ClusterStatus struct {
	// INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	// Phase 表示集群的状态
	Phase ClusterStatusPhase `json:"phase,omitempty"`

	// LastProbeTime the last access time
	LastProbeTime *metav1.Time `json:"lastProbeTime,omitempty"`

	// Reason indicate why the component cannot work. Empty if everything is ok
	Reason string `json:"reason,omitempty"`

	// Message manual message along with Reason
	Message string `json:"message,omitempty"`

	// InfraCluster 表示基础集群的状态
	ClusterInfo InfraClusterStatus `json:"clusterInfo,omitempty"`

	// Network 表示网络状态
	Network InfraNetworkStatus `json:"network,omitempty"`

	// tensorboard的服务网卡的状态
	LinkBridgeStatus PublicLinkBridgeStatus `json:"linkBridgeStatus,omitempty"`

	// 资源组 状态
	ResourceGroups []InfraResourceGroupStatus `json:"resourceGroups,omitempty"`

	PluginStatuses []PluginStatus `json:"pluginStatuses,omitempty"`

	Conditions []ClusterCondition `json:"conditions,omitempty"`

	MonitorCollector MonitorCollectorStatus `json:"monitorCollectorStatus"`

	// 展示 Cluster 部署中过程事件
	ReconcileCreateSteps map[StepName]Step `json:"reconcileCreateSteps,omitempty"`
}

type MonitorCollectorStatus struct {
	MonitorInstanceID string `json:"monitorInstanceID"`
	Phase             string `json:"phase"`
}

type PublicLinkBridgeStatus struct {
	LinkBridgeID string `json:"linkBridgeID,omitempty"` // LinkBridge ID

	Phase PublicLinkBridgePhase `json:"phase,omitempty"`

	Reason string `json:"reason,omitempty"`
}

type ClusterCondition struct {
	metav1.Condition `json:",inline"`
}

type PluginType string

const (
	PluginTypeHelmChart PluginType = "helm"    // 通过内置helm chart 安装的组件
	PluginTypeAddon     PluginType = "addon"   // 通过cce 组件中心安装的服务
	PluginTypeService   PluginType = "service" // 通过服务安装的组件
	PluginTypeUnknown   PluginType = "unknown" // 通过服务安装的组件
)

type PluginStatus struct {
	// 插件类型(插件名称) 非必要 用户要部署的是哪个插件,传空时和PluginName保持一致
	PluginName string `json:"pluginName,omitempty"`

	// Version 表示组件版本, 除非用户要指定版本否则无需传递此值
	Version string `json:"version,omitempty"`

	// Namespace 插件部署到哪个命名空间  非必要
	Namespace string `json:"namespace,omitempty"`

	// Description 非必要
	Description string `json:"description,omitempty"`

	// Values 取决于插件 系统插件传空值即可
	Values string `json:"values,omitempty"`

	// Options 表示针对插件的特殊配置，比如 CProm 场景中，配置采集规则
	Options map[string]string `json:"options,omitempty"`

	// Type 表示插件的类型区分为helm 和 addon
	Type PluginType `json:"type,omitempty"`

	// Phase 表示集群组件的状态
	Phase PluginStatusPhase `json:"phase,omitempty"`

	Reason string `json:"reason,omitempty"`
}

type InfraClusterStatus struct {
	ClusterID    string `json:"clusterID,omitempty"` // CCE 集群 ID
	ClusterName  string `json:"clusterName,omitempty"`
	ClusterPhase string `json:"phase,omitempty"`
}

type InfraNetworkStatus struct {
	// NetworkPhase 网络整体状态
	NetworkPhase string `json:"phase,omitempty"`

	// VPCs 列表
	VPCs []InfraVpcStatus `json:"vpcs,omitempty"`

	// CSNs 列表
	CSNs []InfraCSNStatus `json:"csns,omitempty"`
}

type InfraVpcStatus struct {
	Phase    string               `json:"phase,omitempty"`
	Provider ResourceProviderType `json:"provider,omitempty"`
	ID       string               `json:"id,omitempty"`
	UUID     string               `json:"uuid,omitempty"`
	Name     string               `json:"name,omitempty"`
	CIDR     string               `json:"cidr,omitempty"`
	PeerVPCs []PeerVPC            `json:"peerVPCs,omitempty"`
}

type PeerVPC struct {
	Provider       ResourceProviderType `json:"provider,omitempty"`
	ID             string               `json:"id,omitempty"`
	BceBECRegionID string               `json:"bceBECRegionID,omitempty"`
}

type InfraCSNStatus struct {
	ID    string `json:"id,omitempty"`
	Name  string `json:"name,omitempty"`
	Phase string `json:"phase,omitempty"`
}

type InfraResourceGroupStatus struct {
	ID    string             `json:"id,omitempty"`
	Name  string             `json:"name,omitempty"`
	Type  ResourceGroupType  `json:"type,omitempty"`
	Phase ResourceGroupPhase `json:"phase,omitempty"`
}

type Cluster struct {
	Spec   ClusterSpec   `json:"spec"`
	Status ClusterStatus `json:"status"`
}

type CreateClusterRequest struct {
	ClusterName      string           `json:"clusterName"`
	TemplateRef      *TemplateRef     `json:"templateRef"`
	Network          *ClusterNetwork  `json:"network"`
	Tenant           *ClusterTenant   `json:"tenant"`
	Tags             []*Tag           `json:"tags"`
	Plugins          []*Plugin        `json:"plugins"`
	PublicLinkBridge PublicLinkBridge `json:"publicLinkBridge,omitempty"`
}
type CreateClusterResponse struct {
	CommonResponse
	ClusterID string `json:"clusterID"`
	RequestID string `json:"requestId"`
}
type GetClusterInfoResponse struct {
	RequestID string  `json:"requestId"`
	Cluster   Cluster `json:"cluster"`
}

type GetClusterCreateStepsResponse struct {
	RequestID string  `json:"requestId"`
	Steps     []*Step `json:"steps"`
}

type Step struct {
	StepName     string         `json:"stepName"`
	StepStatus   string         `json:"stepStatus"`
	Ready        bool           `json:"ready"`
	CostSeconds  int64          `json:"costSeconds"`
	StartTime    *metav1.Time   `json:"startTime"`
	FinishedTime *metav1.Time   `json:"finishedTime"`
	RetryCount   int            `json:"retryCount"`
	ErrInfo      *StepErrorInfo `json:"errInfo"`
	Step         []*Step        `json:"step,omitempty"`
}

type StepErrorInfo struct {
	Code       string `json:"code,omitempty"`
	Message    string `json:"message,omitempty"`
	TraceID    string `json:"traceID,omitempty"`
	Suggestion string `json:"suggestion,omitempty"`
}

type CreateClusterRequestV2 struct {
	ClusterID                 string                 `json:"clusterID,omitempty"`
	ClusterName               string                 `json:"clusterName"`
	TemplateRef               *TemplateRef           `json:"templateRef"`
	Network                   *ClusterNetworkV2      `json:"network"`
	Tenant                    *ClusterTenantV2       `json:"tenant"`
	Tags                      []*Tag                 `json:"tags"`
	Plugins                   []*Plugin              `json:"plugins"`
	EnableDeleteProtection    bool                   `json:"enableDeleteProtection"`
	NodeDefaultSecurityGroups []*SecurityGroup       `json:"nodeDefaultSecurityGroups"`
	EniDefaultSecurityGroups  []*SecurityGroup       `json:"eniDefaultSecurityGroups"`
	Options                   *CreateClusterV2Option `json:"options"`
	UserResourceGroups        []UserResourceGroup    `json:"userResourceGroups"`
	MonitorCollector          *MonitorCollector      `json:"monitorCollector"`
	RuntimeVersion            string                 `json:"runtimeVersion"`
}

type ClusterNetworkV2 struct {
	Type                  string   `json:"type"`
	Mode                  string   `json:"mode"`
	ClusterIPServiceCIDR  string   `json:"clusterIPServiceCIDR"`
	LbServiceVPCSubnetID  string   `json:"lbServiceVPCSubnetID"`
	ClusterMasterSubnetID string   `json:"clusterMasterSubnetID"`
	EniVPCSubnetIDs       []string `json:"eniVPCSubnetIDs"`
	NodeSubnetIDs         []string `json:"nodeSubnetIDs"`
}

type ClusterTenantV2 struct {
	AccountID string `json:"accountID"`
	VpcID     string `json:"vpcID"`
}

type SecurityGroup struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Type string `json:"type"`
}

type CreateClusterV2Option struct {
	CheckDefaultSecurityGroups bool                  `json:"checkDefaultSecurityGroups"`
	CreateSecurityGroups       *CreateSecurityGroups `json:"createSecurityGroups"`
	SkipNetworkCheck           bool                  `json:"skipNetworkCheck"`
}

type CreateSecurityGroups struct {
	ENI         string       `json:"eni"`
	Master      string       `json:"master"`
	NetworkInfo *NetworkInfo `json:"networkInfo"`
	Node        string       `json:"node"`
}

type NetworkInfo struct {
	ExposedPublic    bool   `json:"exposedPublic"`
	NodePortRangeMax int    `json:"nodePortRangeMax"`
	NodePortRangeMin int    `json:"nodePortRangeMin"`
	PodCIDR          string `json:"podCIDR"`
	VpcCIDR          string `json:"vpcCIDR"`
	VpcID            string `json:"vpcID"`
}

type MonitorCollector struct {
	AutoCreateMonitorInstance bool   `json:"autoCreateMonitorInstance"`
	Enable                    bool   `json:"enable"`
	MonitorInstanceID         string `json:"monitorInstanceID"`
}

type UserResourceGroup struct {
	ResourceGroupName string       `json:"resourceGroupName"`
	Replicas          int          `json:"replicas"`
	NodeSets          []NodeSetsV2 `json:"nodeSets"`
}

type NodeSetsV2 struct {
	IsDefault bool         `json:"isDefault"`
	Template  NodeTemplate `json:"template"`
}

// CreateClusterPrecheckResponse ...
type CreateClusterPrecheckResponse struct {
	ClusterID  string `json:"clusterID"`
	WorkflowID string `json:"workflowID"`
}

// GetClusterPrecheckResponse ...
type GetClusterPrecheckResponse struct {
	RequestID string   `json:"requestId"`
	Workflow  Workflow `json:"workflow"`
}

type Workflow struct {
	Spec   Spec   `json:"spec"`
	Status Status `json:"status"`
}

type Spec struct {
	Handler        string            `json:"handler"`
	WorkflowID     string            `json:"workflowID"`
	AccountID      string            `json:"accountID"`
	UserID         string            `json:"userID"`
	WorkflowType   string            `json:"workflowType"`
	Config         map[string]string `json:"config"`
	WatchDogConfig map[string]string `json:"watchDogConfig"`
}

type Status struct {
	StartTime         time.Time         `json:"startTime"`
	FinishedTime      time.Time         `json:"finishedTime"`
	Phase             string            `json:"phase"`
	TotalTaskCount    int               `json:"totalTaskCount"`
	FinishedTaskCount int               `json:"finishedTaskCount"`
	TaskGroupList     []TaskGroup       `json:"taskGroupList"`
	WatchDogStatus    map[string]string `json:"watchDogStatus"`
}

type TaskGroup struct {
	TaskGroupName  string `json:"taskGroupName"`
	TaskList       []Task `json:"taskList"`
	TaskGroupPhase string `json:"taskGroupPhase"`
}

type Task struct {
	TaskName          string     `json:"taskName"`
	WorkflowTaskType  string     `json:"workflowTaskType"`
	WorkflowTaskPhase string     `json:"workflowTaskPhase"`
	TaskConfig        RawMessage `json:"taskConfig,omitempty"`
	TaskExecuteResult RawMessage `json:"taskExecuteResult,omitempty"`
	StartTime         time.Time  `json:"startTime"`
	FinishedTime      time.Time  `json:"finishedTime"`
}

type TaskExecuteResult struct {
	Description string `json:"description"`
	Message     string `json:"message"`
	QualifyType string `json:"qualifyType"`
	Result      string `json:"result"`
}

// RawMessage is a raw encoded JSON value.
// It implements Marshaler and Unmarshaler and can
// be used to delay JSON decoding or precompute a JSON encoding.
type RawMessage []byte

// JSON represents any valid JSON value.
// These types are supported: bool, int64, float64, string, []interface{}, map[string]interface{} and nil.
type JSON struct {
	RawMessage `json:",inline"`
}

// MarshalJSON returns m as the JSON encoding of m.
func (m RawMessage) MarshalJSON() ([]byte, error) {
	if m == nil {
		return []byte("null"), nil
	}
	return m, nil
}

// UnmarshalJSON sets *m to a copy of data.
func (m *RawMessage) UnmarshalJSON(data []byte) error {
	if m == nil {
		return errors.New("json.RawMessage: UnmarshalJSON on nil pointer")
	}
	*m = append((*m)[0:0], data...)
	return nil
}

var _ json.Marshaler = (*RawMessage)(nil)
var _ json.Unmarshaler = (*RawMessage)(nil)
