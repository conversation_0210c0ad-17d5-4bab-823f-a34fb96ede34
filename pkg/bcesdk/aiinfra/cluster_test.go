package aiinfra

import "testing"

// TestMarshalJSON_NonEmpty 是用于测试 MarshalJSON_NonEmpty
// generated by Comate
func TestMarshalJSON_NonEmpty(t *testing.T) {
	m := RawMessage("{\"key\":\"value\"}")
	data, err := m.<PERSON>()
	if err != nil {
		t.<PERSON><PERSON>("Expected no error, got %v", err)
	}
	if string(data) != "{\"key\":\"value\"}" {
		t.<PERSON><PERSON>("Expected '{\"key\":\"value\"}', got %s", string(data))
	}
}
