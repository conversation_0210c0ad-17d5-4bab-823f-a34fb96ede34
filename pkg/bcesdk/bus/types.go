package bus

import (
	"context"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// http://bce.console.baidu-int.com/bce-doc/doc/view/143e21ea-9a5e-4d10-a09d-bd5cc38acd23
//
//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bus Interface
type Interface interface {
	SetDebug(debug bool)

	RegisterService(ctx context.Context, bceService BceService, option *bce.SignOption) error
	UnregisterService(ctx context.Context, serviceType, serviceID, endpoint string, option *bce.SignOption) error
}

type RegisterConfig struct {
	TagPath string `json:"tagPath"`
}

type BceService struct {
	ServiceType   string          `json:"type"`
	ServiceID     string          `json:"serviceId"`
	Endpoint      string          `json:"endpoint"`
	Region        string          `json:"region"`
	Configuration *RegisterConfig `json:"configuration"`
}
