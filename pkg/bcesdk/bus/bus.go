package bus

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func (c *Client) RegisterService(ctx context.Context, bceService BceService, option *bce.SignOption) error {
	putContent, err := json.Marshal(bceService)
	if err != nil {
		return err
	}

	path := fmt.Sprintf("/v1/bus")
	req, err := bce.NewRequest("PUT", c.GetURL(path, nil), bytes.NewBuffer(putContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	return err
}

func (c *Client) UnregisterService(ctx context.Context, serviceType, serviceID, endpoint string, option *bce.SignOption) error {
	params := map[string]string{
		"type":      serviceType,
		"serviceId": serviceID,
		"endpoint":  endpoint,
	}

	path := fmt.Sprintf("/v1/bus")
	req, err := bce.NewRequest("DELETE", c.GetURL(path, params), nil)
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	return err
}
