package bcm

import (
	"context"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bcm Interface
type Interface interface {
	SetDebug(debug bool)

	PushEvent(ctx context.Context, accountID string, event *Event, option *bce.SignOption) (*PushEventResponse, error)
	ListEvents(ctx context.Context, opt *ListOption, signOpt *bce.SignOption) (*ListEventsResponse, error)
}

type Event struct {
	Region       string       `json:"region"`
	ResourceID   string       `json:"resourceId"`
	ResourceType ResourceType `json:"resourceType"`
	EventID      string       `json:"eventId"`
	EventType    EventType    `json:"eventType"`
	EventLevel   EventLevel   `json:"eventLevel"`
	EventAlias   EventAlias   `json:"eventAlias"`
	EventAliasEn EventAliasEn `json:"eventAliasEn"`
	Content      string       `json:"content"`
	Timestamp    string       `json:"timestamp"`

	AccountID   string `json:"accountId,omitempty"`
	ServiceName string `json:"serviceName,omitempty"`
}

type ResourceType string
type EventType string
type EventLevel string
type EventAlias string
type EventAliasEn string

type Content struct {
	// Info 为必须字段
	Info string `json:"info"`
	// Advice 为必须字段
	Advice string `json:"advice"`
}

const (
	ResourceTypeInstance ResourceType = "INSTANCE" // 目前只有一个固定值

	EventLevelNotice   EventLevel = "NOTICE"
	EventLevelWarning  EventLevel = "WARNING"
	EventLevelMajor    EventLevel = "MAJOR"
	EventLevelCritical EventLevel = "CRITICAL"

	EventTypeCCEAbnormalEvent       EventType = "CCE_ABNORMAL_EVENT"
	EventTypeCCENodeNotReady        EventType = "CCE_NODE_NOT_READY"
	EventTypeCCENodeAbnormalEvent   EventType = "CCE_NODE_ABNORMAL_EVENT"
	EventTypeCCEPodAbnormalEvent    EventType = "CCE_POD_ABNORMAL_EVENT"
	EventTypeCCENodeScaleDownFailed EventType = "ScaleDownFailed"
	EventTypeCCENodeScaleUpFailed   EventType = "FailedToScaleUpGroup"

	EventAliasCCEAbnormalEvent       EventAlias = "CCE 集群异常事件"
	EventAliasCCENodeNotReady        EventAlias = "CCE 集群节点 NotReady"
	EventAliasCCENodeAbnormalEvent   EventAlias = "CCE 节点异常事件"
	EventAliasCCEPodAbnormalEvent    EventAlias = "CCE Pod 异常事件"
	EventAliasCCENodeScaleUpFailed   EventAlias = "CCE 集群扩容失败"
	EventAliasCCENodeScaleDownFailed EventAlias = "CCE 集群缩容失败"

	EventAliasEnCCEAbnormalEvent       EventAliasEn = "Abnormal event of CCE cluster"
	EventAliasEnCCENodeNotReady        EventAliasEn = "Node not ready in CCE cluster"
	EventAliasEnCCENodeAbnormalEvent   EventAliasEn = "Abnormal event of CCE node"
	EventAliasEnCCEPodAbnormalEvent    EventAliasEn = "Abnormal event of CCE pod"
	EventAliasEnCCENodeScaleUpFailed   EventAliasEn = "Failed to scale up CCE cluster"
	EventAliasEnCCENodeScaleDownFailed EventAliasEn = "Failed to scale down CCE cluster"
)

type PushEventResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	EventID string `json:"eventId"`
}
