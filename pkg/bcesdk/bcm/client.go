package bcm

import (
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/util"
)

var Endpoint = map[string]string{
	"bj":      "bcm.bj.baidubce.com",
	"gz":      "bcm.gz.baidubce.com",
	"su":      "bcm.su.baidubce.com",
	"hkg":     "bcm.hkg.baidubce.com",
	"fwh":     "bcm.fwh.baidubce.com",
	"bd":      "bcm.bd.baidubce.com",
	"sandbox": "*************:8869",
}

type client struct {
	*bce.Client
}

func NewClient(config *bce.Config) Interface {
	bceClient := bce.NewClient(config)
	return &client{bceClient}
}

func (c *client) GetURL(objectKey string, params map[string]string) string {
	host := c.Endpoint

	if host == "" {
		host = Endpoint[c.GetRegion()]
	}

	uriPath := objectKey

	return c.Client.GetURL(host, uriPath, params)
}

func NewSignOption() *bce.SignOption {
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")

	return option
}

func (c *client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}
