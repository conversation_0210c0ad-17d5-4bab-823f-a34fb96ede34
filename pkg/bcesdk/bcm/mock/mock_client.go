// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bcm (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	bcm "icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bcm"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// ListEvents mocks base method.
func (m *MockInterface) ListEvents(arg0 context.Context, arg1 *bcm.ListOption, arg2 *bce.SignOption) (*bcm.ListEventsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListEvents", arg0, arg1, arg2)
	ret0, _ := ret[0].(*bcm.ListEventsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListEvents indicates an expected call of ListEvents.
func (mr *MockInterfaceMockRecorder) ListEvents(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListEvents", reflect.TypeOf((*MockInterface)(nil).ListEvents), arg0, arg1, arg2)
}

// PushEvent mocks base method.
func (m *MockInterface) PushEvent(arg0 context.Context, arg1 string, arg2 *bcm.Event, arg3 *bce.SignOption) (*bcm.PushEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushEvent", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*bcm.PushEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PushEvent indicates an expected call of PushEvent.
func (mr *MockInterfaceMockRecorder) PushEvent(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushEvent", reflect.TypeOf((*MockInterface)(nil).PushEvent), arg0, arg1, arg2, arg3)
}

// SetDebug mocks base method.
func (m *MockInterface) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug.
func (mr *MockInterfaceMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), arg0)
}
