package bcm

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/utils"
)

func (c *client) PushEvent(ctx context.Context, accountID string, event *Event, option *bce.SignOption) (*PushEventResponse, error) {
	params := map[string]string{}

	postContent, err := json.Marshal(event)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("event-api/v1/accounts/%s/services/BCE_CCE/events", accountID)
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	data, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	pushResp := new(PushEventResponse)
	if err := json.Unmarshal(data, pushResp); err != nil {
		return nil, err
	}

	return pushResp, nil
}

type ListOption struct {
	PageNo     int64     `valid:"Required"`
	PageSize   int64     `valid:"Required"`
	StartTime  time.Time `valid:"Required"`
	EndTime    time.Time `valid:"Required"`
	Scope      string
	Region     string
	AccountID  string `valid:"Required"`
	EventLevel EventLevel
	EventName  string
	EventAlias string
}

func NewListOption(pageNo, pageSize int64, start, end time.Time, scope, region, accountID string, level EventLevel, name, alias string) *ListOption {
	return &ListOption{
		PageNo:     pageNo,
		PageSize:   pageSize,
		StartTime:  start,
		EndTime:    end,
		Scope:      scope,
		Region:     region,
		AccountID:  accountID,
		EventLevel: level,
		EventName:  name,
		EventAlias: alias,
	}
}

func (opt *ListOption) validate() error {
	if opt == nil {
		return errors.New("opt cannot be nil")
	}
	return utils.Valid(opt)
}

func (opt *ListOption) ToQueries() map[string]string {
	if opt == nil {
		return nil
	}

	return map[string]string{
		"pageNo":     strconv.FormatInt(opt.PageNo, 10),
		"pageSize":   strconv.FormatInt(opt.PageSize, 10),
		"startTime":  opt.StartTime.UTC().Format(time.RFC3339),
		"endTime":    opt.EndTime.UTC().Format(time.RFC3339),
		"scope":      opt.Scope,
		"region":     opt.Region,
		"accountId":  opt.AccountID,
		"eventLevel": string(opt.EventLevel),
		"eventName":  opt.EventName,
		"eventAlias": opt.EventAlias,
	}
}

type ListEventsResponse struct {
	Content       []*Event `json:"content"`
	PageNo        int64    `json:"pageNo"`
	PageSize      int64    `json:"pageSize"`
	PageElements  int64    `json:"pageElements"`
	Last          bool     `json:"last"`
	First         bool     `json:"first"`
	TotalPages    int64    `json:"totalPages"`
	TotalElements int64    `json:"totalElements"`
}

func (c *client) ListEvents(ctx context.Context, opt *ListOption, signOpt *bce.SignOption) (*ListEventsResponse, error) {
	ctx = logger.EnsureRequestIDInCtx(ctx)
	if err := opt.validate(); err != nil {
		return nil, err
	}

	params := opt.ToQueries()

	req, err := bce.NewRequest("GET", c.GetURL("event-api/v1/bce-event/list", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	listEventsResponse := new(ListEventsResponse)
	err = json.Unmarshal(bodyContent, listEventsResponse)
	if err != nil {
		return nil, err
	}

	return listEventsResponse, nil
}
