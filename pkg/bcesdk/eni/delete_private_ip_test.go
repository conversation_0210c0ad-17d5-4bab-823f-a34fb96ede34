package eni

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/gorilla/mux"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
)

func handleDeletePrivateIP(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	eniID, ok := vars["eniId"]
	if !ok {
		fmt.Fprintf(w, `{"Code":"BadParameters","Message":"eniID is invalid,"RequestID":%s}`, util.GetRequestID())
		return
	}
	privateIP, ok := vars["privateIpAddress"]
	if !ok {
		fmt.Fprintf(w, `{"Code":"BadParameters","Message":"privateIP is invalid,"RequestID":%s}`, util.GetRequestID())
		return
	}

	if eniID == "" || privateIP == "" {
		fmt.Fprintf(w, `{"Code":"BadParameters","Message":"eniID or privateIP is invalid,"RequestID":%s}`, util.GetRequestID())
		return
	}

	w.WriteHeader(http.StatusOK)
}

func TestDeletePrivateIP(t *testing.T) {
	setupTestEnv(newENIHandler())
	defer tearDownTestEnv()

	type args struct {
		args    *DeletePrivateIPArgs
		signOpt *bce.SignOption
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "normal case",
			args: args{
				args: &DeletePrivateIPArgs{
					ENIID:     "eni-xxxxx",
					PrivateIP: "***********",
				},
			},
			wantErr: false,
		},
		{
			name: "empty ip case",
			args: args{
				args: &DeletePrivateIPArgs{
					ENIID:     "eni-xxxxx",
					PrivateIP: "",
				},
			},
			wantErr: true,
		},
		{
			name: "empty eni id case",
			args: args{
				args: &DeletePrivateIPArgs{
					ENIID:     "",
					PrivateIP: "",
				},
			},
			wantErr: true,
		},
		{
			name: "illegal ip case",
			args: args{
				args: &DeletePrivateIPArgs{
					ENIID:     "eni-xxxxx",
					PrivateIP: "abcd",
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			if err := eniClient.DeletePrivateIP(ctx, tt.args.args, tt.args.signOpt); (err != nil) != tt.wantErr {
				t.Errorf("DeletePrivateIP() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
