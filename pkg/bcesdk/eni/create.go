package eni

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

// CreateENIArgs defines CreateENI args
type CreateENIArgs struct {
	Name             string       `json:"name"`
	SubnetID         string       `json:"subnetId"`
	SecurityGroupIDs []string     `json:"securityGroupIds"`
	PrivateIPSet     []*PrivateIP `json:"privateIpSet"`
	Description      string       `json:"description,omitempty"`
	//接口添加参数支持创建eri，如果是eni填"standard"，eri填"highPerformance"
	NetworkInterfaceTrafficMode string `json:"networkInterfaceTrafficMode,omitempty"`
}

// CreateENIResponse defines CreateENI response
type CreateENIResponse struct {
	ENIID string `json:"eniId"`
}

func (args *CreateENIArgs) validate() error {
	if args == nil {
		return errors.New("CreateENI validate failed: args cannot be nil")
	}

	if args.Name == "" {
		return errors.New("CreateENI validate failed: ENI name cannot be empty")
	}
	if args.SubnetID == "" {
		return errors.New("CreateENI validate failed: ENI subnetId cannot be empty")
	}

	if args.SecurityGroupIDs == nil || len(args.SecurityGroupIDs) == 0 {
		return errors.New("CreateENI validate failed: ENI securityGroupIds cannot be empty")
	}
	if args.PrivateIPSet == nil || len(args.PrivateIPSet) == 0 {
		return errors.New("CreateENI validate failed: ENI privateIpSet cannot be empty")
	}

	err := args.validatePrivateIPSet()
	if err != nil {
		return err
	}

	return nil
}

func (args *CreateENIArgs) validatePrivateIPSet() error {
	// 主IP只能有一个
	primaryIPCount := 0
	for _, ip := range args.PrivateIPSet {
		if ip.Primary {
			primaryIPCount++
		}
	}
	if primaryIPCount != 1 {
		return errors.New("CreateENI validate failed: ENI should have one primary IP")
	}
	return nil
}

// CreateENI creates an ENI
func (c *Client) CreateENI(ctx context.Context, args *CreateENIArgs, signOpt *bce.SignOption) (string, error) {
	ctx = logger.EnsureRequestIDInCtx(ctx)

	err := args.validate()
	if err != nil {
		return "", err
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return "", err
	}
	req, err := bce.NewRequest("POST", c.GetURL("v1/eni", nil), bytes.NewBuffer(postContent))
	if err != nil {
		return "", err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return "", err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}

	createENIResponse := &CreateENIResponse{}
	err = json.Unmarshal(bodyContent, createENIResponse)
	if err != nil {
		return "", err
	}

	return createENIResponse.ENIID, nil
}
