package eni

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/gorilla/mux"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
)

func handleDeleteENI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	vars := mux.Vars(r)
	eniID, ok := vars["eniId"]
	if !ok {
		fmt.Fprintf(w, `{"Code":"BadParameters","Message":"eniID is invalid,"RequestID":%s}`, util.GetRequestID())
		return
	}

	if eniID == "" || eniID == "invalid-eni" {
		w.WriteHeader(http.StatusBadRequest)
		fmt.Fprintf(w, `{"Code":"BadParameters","Message":"eniID is invalid,"RequestID":%s}`, util.GetRequestID())
		return
	}

	w.<PERSON><PERSON><PERSON>(http.StatusOK)
}

func TestDeleteENI(t *testing.T) {
	setupTestEnv(newENIHandler())
	defer tearDownTestEnv()

	type args struct {
		eniID   string
		signOpt *bce.SignOption
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "normal case",
			args:    args{eniID: "eni-xxxxxx"},
			wantErr: false,
		},
		{
			name:    "empty eni id case",
			args:    args{eniID: ""},
			wantErr: true,
		},
		{
			name:    "invalid eni id case",
			args:    args{eniID: "invalid-eni"},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			if err := eniClient.DeleteENI(ctx, tt.args.eniID, tt.args.signOpt); (err != nil) != tt.wantErr {
				t.Errorf("DeleteENI() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
