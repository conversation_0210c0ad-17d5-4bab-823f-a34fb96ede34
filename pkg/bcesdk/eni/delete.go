package eni

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

// DeleteENI delete eni via eniID
func (c *Client) DeleteENI(ctx context.Context, eniID string, signOpt *bce.SignOption) error {
	ctx = logger.EnsureRequestIDInCtx(ctx)

	if eniID == "" {
		return errors.New("DeleteENI failed: eniID cannot be empty")
	}

	req, err := bce.NewRequest("DELETE", c.GetURL("v1/eni/"+eniID, nil), nil)
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return err
	}
	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}

	return nil
}
