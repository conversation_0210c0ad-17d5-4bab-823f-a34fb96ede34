package eni

import (
	"context"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eni Interface

// Interface defines all methods to manipulate an ENI
type Interface interface {
	CreateENI(ctx context.Context, args *CreateENIArgs, signOpt *bce.SignOption) (string, error)
	DeleteENI(ctx context.Context, eniID string, signOpt *bce.SignOption) error
	AttachENI(ctx context.Context, args *AttachENIArgs, signOpt *bce.SignOption) error
	DetachENI(ctx context.Context, args *DetachENIArgs, signOpt *bce.SignOption) error
	ListENIs(ctx context.Context, args *ListENIsArgs, signOpt *bce.SignOption) (*ListENIsResponse, error)
	StatENI(ctx context.Context, eniID string, signOpt *bce.SignOption) (*StatENIResponse, error)
	AddPrivateIP(ctx context.Context, args *AddPrivateIPArgs, signOpt *bce.SignOption) (string, error)
	DeletePrivateIP(ctx context.Context, args *DeletePrivateIPArgs, signOpt *bce.SignOption) error
	UpdateSecurityGroup(ctx context.Context, args *UpdateSecurityGroupArgs, signOpt *bce.SignOption) error
}

// ENI defines ENI for Baidu Cloud
type ENI struct {
	ENIID        string       `json:"eniId"`
	Name         string       `json:"name"`
	ZoneName     string       `json:"zoneName"`
	Description  string       `json:"description"`
	InstanceID   string       `json:"instanceId"`
	MacAddress   string       `json:"macAddress"`
	VPCID        string       `json:"vpcId"`
	SubnetID     string       `json:"subnetId"`
	Status       ENIStatus    `json:"status"`
	PrivateIPSet []*PrivateIP `json:"privateIpSet"`
}

type ENIStatus string

const (
	ENIStatusInuse     ENIStatus = "inuse"
	ENIStatusAvailable ENIStatus = "available"
	ENIStatusAttaching ENIStatus = "attaching"
	ENIStatusDetaching ENIStatus = "detaching"
	ENIStatusDeleting  ENIStatus = "deleting"
)

// PrivateIP defines privateIP for ENI
type PrivateIP struct {
	Primary          bool   `json:"primary"`
	PublicIPAddress  string `json:"publicIpAddress,omitempty"` // EIP
	PrivateIPAddress string `json:"privateIpAddress"`
}

const ENINameLengthMax = 65
