package eni

import (
	"context"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

// DeletePrivateIPArgs defines args to delete a private IP
type DeletePrivateIPArgs struct {
	ENIID     string
	PrivateIP string
}

func (args *DeletePrivateIPArgs) validate() error {
	if args == nil {
		return errors.New("DeletePrivateIP validate failed: args cannot be nil")
	}

	if args.ENIID == "" {
		return errors.New("DeletePrivateIP validate failed: ENIID cannot be empty")
	}

	if args.PrivateIP == "" {
		return errors.New("DeletePrivateIP validate failed: PrivateIP cannot be empty")
	}

	// check ip format
	err := isIPLegal(args.PrivateIP)
	if err != nil {
		return err
	}

	return nil
}

// DeletePrivateIP deletes a private IP from an eni
func (c *Client) DeletePrivateIP(ctx context.Context, args *DeletePrivateIPArgs, signOpt *bce.SignOption) error {
	ctx = logger.EnsureRequestIDInCtx(ctx)

	err := args.validate()
	if err != nil {
		return err
	}

	path := fmt.Sprintf("v1/eni/%s/privateIp/%s", args.ENIID, args.PrivateIP)
	req, err := bce.NewRequest("DELETE", c.GetURL(path, nil), nil)
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return err
	}
	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}

	return nil
}
