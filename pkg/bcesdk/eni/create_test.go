package eni

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"reflect"
	"testing"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
)

const createENITemplate = `
{
	"eniId":"eni-w2d4kgc3x0y1"
}
`

func handleCreateENI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	bodyContent, _ := io.ReadAll(r.Body)
	defer r.Body.Close()

	createENIArgs := &CreateENIArgs{}
	err := json.Unmarshal(bodyContent, createENIArgs)

	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		fmt.Fprintf(w, `{"Code":"BadParameters","Message":"createENIArgs is invalid,"RequestID":%s}`, util.GetRequestID())
		return
	}

	if createENIArgs.validate() != nil {
		w.WriteHeader(http.StatusBadRequest)
		fmt.Fprintf(w, `{"Code":"BadParameters","Message":"createENIArgs is invalid,"RequestID":%s}`, util.GetRequestID())
		return
	}

	w.WriteHeader(http.StatusOK)
	w.Write([]byte(createENITemplate))
}

func TestCreateENI(t *testing.T) {
	setupTestEnv(newENIHandler())
	defer tearDownTestEnv()

	type args struct {
		args    *CreateENIArgs
		signOpt *bce.SignOption
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "normal case 1",
			args: args{
				args: &CreateENIArgs{
					Name:             "eni_test",
					SubnetID:         "sbn-abcdefg",
					PrivateIPSet:     []*PrivateIP{{Primary: true}},
					SecurityGroupIDs: []string{"g-SY5smEG9"},
					Description:      "",
				},
			},
			wantErr: false,
		},
		{
			name: "normal case 2",
			args: args{
				args: &CreateENIArgs{
					Name:             "eni_test",
					SubnetID:         "sbn-abcdefg",
					PrivateIPSet:     []*PrivateIP{{Primary: true}, {Primary: false}},
					SecurityGroupIDs: []string{"g-SY5smEG9"},
				},
			},
			wantErr: false,
		},
		{
			name: "normal case 3",
			args: args{
				args: &CreateENIArgs{
					Name:             "eni_test",
					SubnetID:         "sbn-abcdefg",
					PrivateIPSet:     []*PrivateIP{{Primary: true, PrivateIPAddress: "***********", PublicIPAddress: "***********"}, {Primary: false}},
					SecurityGroupIDs: []string{"g-SY5smEG9"},
				},
			},
			wantErr: false,
		},
		{
			name: "empty case 1",
			args: args{
				args: &CreateENIArgs{
					Name:             "eni_test",
					SubnetID:         "",
					PrivateIPSet:     []*PrivateIP{{Primary: true, PrivateIPAddress: "***********", PublicIPAddress: "***********"}, {Primary: false}},
					SecurityGroupIDs: []string{"g-SY5smEG9"},
				},
			},
			wantErr: true,
		},
		{
			name: "empty case 2",
			args: args{
				args: &CreateENIArgs{
					Name:             "eni_test",
					SubnetID:         "sbn-abcdefg",
					PrivateIPSet:     nil,
					SecurityGroupIDs: []string{},
				},
			},
			wantErr: true,
		},
		{
			name: "multiple primary ip case",
			args: args{
				args: &CreateENIArgs{
					Name:             "eni_test",
					SubnetID:         "sbn-abcdefg",
					PrivateIPSet:     []*PrivateIP{{Primary: true, PrivateIPAddress: "***********", PublicIPAddress: "***********"}, {Primary: true}},
					SecurityGroupIDs: []string{"g-SY5smEG9"},
				},
			},
			wantErr: true,
		},
		{
			name: "no primary ip case",
			args: args{
				args: &CreateENIArgs{
					Name:             "eni_test",
					SubnetID:         "sbn-abcdefg",
					PrivateIPSet:     []*PrivateIP{{Primary: false, PrivateIPAddress: "***********", PublicIPAddress: "***********"}, {Primary: false}},
					SecurityGroupIDs: []string{"g-SY5smEG9"},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			if !tt.wantErr {
				createENIResponse := &CreateENIResponse{}
				if err := json.Unmarshal([]byte(createENITemplate), createENIResponse); err != nil {
					t.Errorf("CreateENI() error = %v", err)
					return
				}
				tt.want = createENIResponse.ENIID
			}

			got, err := eniClient.CreateENI(ctx, tt.args.args, tt.args.signOpt)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateENI() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CreateENI() got = %v, want %v", got, tt.want)
			}
		})
	}
}
