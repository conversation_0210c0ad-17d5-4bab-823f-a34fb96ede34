package eni

import (
	"context"
	"testing"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func TestDetachENI(t *testing.T) {
	setupTestEnv(newENIHandler())
	defer tearDownTestEnv()

	type args struct {
		args    *DetachENIArgs
		signOpt *bce.SignOption
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "normal case",
			args: args{
				args: &DetachENIArgs{
					InstanceID: "i-abcdedf",
					ENIID:      "eni-123456",
				},
			},
			wantErr: false,
		},
		{
			name: "empty instance id case",
			args: args{
				args: &DetachENIArgs{
					InstanceID: "",
					ENIID:      "eni-123456",
				},
			},
			wantErr: true,
		},

		{
			name: "empty eni id case",
			args: args{
				args: &DetachENIArgs{
					InstanceID: "i-abcdedf",
					ENIID:      "",
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			if err := eniClient.DetachENI(ctx, tt.args.args, tt.args.signOpt); (err != nil) != tt.wantErr {
				t.Errorf("DetachENI() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
