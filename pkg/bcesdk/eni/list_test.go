package eni

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"reflect"
	"testing"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

const listENIsTemplate = `
{
    "enis":[
        {
            "zoneName":"zoneA",
            "createdTime":"2019-04-12 18:53:07.0",
            "description":"",
            "instanceId":"i-Z2iJfB90",
            "eniId":"eni-w2d4kgc3x0y1",
            "privateIpSet":[
                {
                    "publicIpAddress":"**************",
                    "primary":true,
                    "privateIpAddress":"*************"
                }
            ],
            "macAddress":"fa:16:3e:c0:e4:3d",
            "name":"test_eni",
            "status":"available",
            "subnetId":"sbn-i4d47zb73ztx",
            "vpcId":"%s"
        }
    ],
   "marker":"eni-w2d4kgc3x0y1",
   "isTruncated": true,
   "nextMarker": "eni-wcd4jgc3x0y1",
   "maxKeys": 1
}`

func handleListENIs(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	vpcID := r.URL.Query().Get("vpcId")
	if vpcID == "" || vpcID == "invalid-vpc" {
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(`{"maxKeys":1000,"isTruncated":false,"enis":[]}`))
		return
	}

	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, listENIsTemplate, vpcID)
}

func TestListENIs(t *testing.T) {
	setupTestEnv(newENIHandler())
	defer tearDownTestEnv()

	type args struct {
		args    *ListENIsArgs
		signOpt *bce.SignOption
	}
	tests := []struct {
		name    string
		args    args
		want    *ListENIsResponse
		wantErr bool
	}{
		{
			name:    "normal case",
			args:    args{args: &ListENIsArgs{VPCID: "vpc-sr94x4txxxxx"}},
			wantErr: false,
		},
		{
			name:    "empty vpcId case",
			args:    args{args: &ListENIsArgs{VPCID: ""}},
			wantErr: true,
		},
		{
			name:    "invalid vpcId case",
			args:    args{args: &ListENIsArgs{VPCID: "invalid-vpc"}},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			if !tt.wantErr {
				if err := json.Unmarshal([]byte(fmt.Sprintf(listENIsTemplate, tt.args.args.VPCID)), &tt.want); err != nil {
					t.Errorf("ListENIs() error = %v", err)
					return
				}
			}

			got, err := eniClient.ListENIs(ctx, tt.args.args, tt.args.signOpt)
			if (err != nil) != tt.wantErr {
				t.Errorf("ListENIs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ListENIs() got = %v, want %v", got, tt.want)
			}
		})
	}
}
