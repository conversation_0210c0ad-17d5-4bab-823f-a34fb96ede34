// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eni (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	eni "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eni"
	reflect "reflect"
)

// MockInterface is a mock of Interface interface
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// AddPrivateIP mocks base method
func (m *MockInterface) AddPrivateIP(arg0 context.Context, arg1 *eni.AddPrivateIPArgs, arg2 *bce.SignOption) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPrivateIP", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPrivateIP indicates an expected call of AddPrivateIP
func (mr *MockInterfaceMockRecorder) AddPrivateIP(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPrivateIP", reflect.TypeOf((*MockInterface)(nil).AddPrivateIP), arg0, arg1, arg2)
}

// AttachENI mocks base method
func (m *MockInterface) AttachENI(arg0 context.Context, arg1 *eni.AttachENIArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AttachENI", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AttachENI indicates an expected call of AttachENI
func (mr *MockInterfaceMockRecorder) AttachENI(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AttachENI", reflect.TypeOf((*MockInterface)(nil).AttachENI), arg0, arg1, arg2)
}

// CreateENI mocks base method
func (m *MockInterface) CreateENI(arg0 context.Context, arg1 *eni.CreateENIArgs, arg2 *bce.SignOption) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateENI", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateENI indicates an expected call of CreateENI
func (mr *MockInterfaceMockRecorder) CreateENI(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateENI", reflect.TypeOf((*MockInterface)(nil).CreateENI), arg0, arg1, arg2)
}

// DeleteENI mocks base method
func (m *MockInterface) DeleteENI(arg0 context.Context, arg1 string, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteENI", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteENI indicates an expected call of DeleteENI
func (mr *MockInterfaceMockRecorder) DeleteENI(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteENI", reflect.TypeOf((*MockInterface)(nil).DeleteENI), arg0, arg1, arg2)
}

// DeletePrivateIP mocks base method
func (m *MockInterface) DeletePrivateIP(arg0 context.Context, arg1 *eni.DeletePrivateIPArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePrivateIP", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePrivateIP indicates an expected call of DeletePrivateIP
func (mr *MockInterfaceMockRecorder) DeletePrivateIP(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePrivateIP", reflect.TypeOf((*MockInterface)(nil).DeletePrivateIP), arg0, arg1, arg2)
}

// DetachENI mocks base method
func (m *MockInterface) DetachENI(arg0 context.Context, arg1 *eni.DetachENIArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DetachENI", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DetachENI indicates an expected call of DetachENI
func (mr *MockInterfaceMockRecorder) DetachENI(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DetachENI", reflect.TypeOf((*MockInterface)(nil).DetachENI), arg0, arg1, arg2)
}

// ListENIs mocks base method
func (m *MockInterface) ListENIs(arg0 context.Context, arg1 *eni.ListENIsArgs, arg2 *bce.SignOption) (*eni.ListENIsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListENIs", arg0, arg1, arg2)
	ret0, _ := ret[0].(*eni.ListENIsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListENIs indicates an expected call of ListENIs
func (mr *MockInterfaceMockRecorder) ListENIs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListENIs", reflect.TypeOf((*MockInterface)(nil).ListENIs), arg0, arg1, arg2)
}

// StatENI mocks base method
func (m *MockInterface) StatENI(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*eni.StatENIResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StatENI", arg0, arg1, arg2)
	ret0, _ := ret[0].(*eni.StatENIResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StatENI indicates an expected call of StatENI
func (mr *MockInterfaceMockRecorder) StatENI(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StatENI", reflect.TypeOf((*MockInterface)(nil).StatENI), arg0, arg1, arg2)
}

// UpdateSecurityGroup mocks base method
func (m *MockInterface) UpdateSecurityGroup(arg0 context.Context, arg1 *eni.UpdateSecurityGroupArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSecurityGroup", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSecurityGroup indicates an expected call of UpdateSecurityGroup
func (mr *MockInterfaceMockRecorder) UpdateSecurityGroup(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSecurityGroup", reflect.TypeOf((*MockInterface)(nil).UpdateSecurityGroup), arg0, arg1, arg2)
}
