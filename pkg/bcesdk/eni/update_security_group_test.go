package eni

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"testing"
	"time"

	"github.com/gorilla/mux"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
)

func handleUpdateSecurityGroup(w http.ResponseWriter, r *http.Request) {
	w.<PERSON>er().Set("Content-Type", "application/json")

	vars := mux.Vars(r)
	eniID, ok := vars["eniId"]
	if !ok {
		fmt.Fprintf(w, `{"Code":"BadParameters","Message":"eniID is invalid,"RequestID":%s}`, util.GetRequestID())
		return
	}

	if eniID == "" || eniID == "invalid-eni" {
		fmt.Fprintf(w, `{"Code":"BadParameters","Message":"eniID is invalid,"RequestID":%s}`, util.GetRequestID())
		return
	}

	bodyContent, _ := io.ReadAll(r.Body)
	defer r.Body.Close()

	updateSecurityGroupArgs := &UpdateSecurityGroupArgs{}
	err := json.Unmarshal(bodyContent, updateSecurityGroupArgs)
	if err != nil || len(updateSecurityGroupArgs.SecurityGroupIDs) == 0 {
		fmt.Fprintf(w, `{"Code":"BadParameters","Message":"UpdateSecurityGroupArgs is invalid,"RequestID":%s}`, util.GetRequestID())
		return
	}

	w.WriteHeader(http.StatusOK)
}

func TestUpdateSecurityGroup(t *testing.T) {
	setupTestEnv(newENIHandler())
	defer tearDownTestEnv()

	type args struct {
		ctx     context.Context
		args    *UpdateSecurityGroupArgs
		signOpt *bce.SignOption
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "normal case",
			args: args{
				args: &UpdateSecurityGroupArgs{
					SecurityGroupIDs: []string{"g-1", "g-2"},
					ENIID:            "eni-123456",
				},
			},
			wantErr: false,
		},
		{
			name: "empty eni id case",
			args: args{
				args: &UpdateSecurityGroupArgs{
					SecurityGroupIDs: []string{"g-1", "g-2"},
					ENIID:            "",
				},
			},
			wantErr: true,
		},
		{
			name: "empty security group case",
			args: args{
				args: &UpdateSecurityGroupArgs{
					SecurityGroupIDs: []string{},
					ENIID:            "eni-123456",
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			if err := eniClient.UpdateSecurityGroup(ctx, tt.args.args, tt.args.signOpt); (err != nil) != tt.wantErr {
				t.Errorf("UpdateSecurityGroup() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
