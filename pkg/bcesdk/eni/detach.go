package eni

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

// DetachENIArgs defines DetachENI args
type DetachENIArgs struct {
	InstanceID string `json:"instanceId"`
	ENIID      string `json:"-"`
}

func (args *DetachENIArgs) validate() error {
	if args == nil {
		return errors.New("DetachENI validate failed: args cannot be nil")
	}

	if args.InstanceID == "" {
		return errors.New("DetachENI validate failed: InstanceID cannot be empty")
	}

	if args.ENIID == "" {
		return errors.New("DetachENI validate failed: ENIID cannot be empty")
	}

	return nil
}

// DetachENI Detaches an ENI from a bcc instance
func (c *Client) DetachENI(ctx context.Context, args *DetachENIArgs, signOpt *bce.SignOption) error {
	ctx = logger.EnsureRequestIDInCtx(ctx)

	err := args.validate()
	if err != nil {
		return err
	}

	params := map[string]string{
		"detach": "",
	}

	bodyContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("v1/eni/"+args.ENIID, params), bytes.NewBuffer(bodyContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return err
	}
	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}

	return nil
}
