package blb

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func (c *Client) SwitchBLBType(ctx context.Context, blbID string, option *bce.SignOption) error {
	if blbID == "" {
		return errors.New("blbID is empty")
	}

	req, err := bce.NewRequest("PUT", c.GetURL("v1/blb/upgradeType/"+blbID, nil), nil)
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}
