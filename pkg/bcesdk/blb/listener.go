package blb

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"strconv"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// CreateLoadBalancerHTTPListener create HTTP listener on loadbalancer
// You can read doc at https://cloud.baidu.com/doc/BLB/API.html#.C0.F3.F3.ED.5C.D8.4D.66.19.FF.DA.7A.0F.75.05.7C
func (c *Client) CreateTCPListener(ctx context.Context, args *CreateTCPListenerArgs, option *bce.SignOption) (err error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	if args == nil {
		return errors.New("CreateTCPListener need args")
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}
	req, err := bce.NewRequest("POST", c.GetURL("v1/blb"+"/"+args.LoadBalancerId+"/TCPlistener", params), bytes.<PERSON>uffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

// CreateLoadBalancerUDPListener create UDP listener on loadbalancer
//
// You can read doc at https://cloud.baidu.com/doc/BLB/API.html#.D7.A3.9B.E1.45.BD.9E.FA.B0.2F.60.12.B3.39.E8.9D
func (c *Client) CreateUDPListener(ctx context.Context, args *CreateUDPListenerArgs, option *bce.SignOption) (err error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	if args == nil {
		return errors.New("CreateUDPListener need args")
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}
	req, err := bce.NewRequest("POST", c.GetURL("v1/blb"+"/"+args.LoadBalancerId+"/UDPlistener", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

// CreateHTTPListener create HTTP listener on loadbalancer
//
// You can read doc at https://cloud.baidu.com/doc/BLB/API.html#.D7.A3.9B.E1.45.BD.9E.FA.B0.2F.60.12.B3.39.E8.9D
func (c *Client) CreateHTTPListener(ctx context.Context, args *CreateHTTPListenerArgs, option *bce.SignOption) (err error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	if args == nil {
		return errors.New("CreateHTTPListener need args")
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}
	req, err := bce.NewRequest("POST", c.GetURL("v1/blb"+"/"+args.LoadBalancerId+"/HTTPlistener", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

type DescribeTCPListenerArgs struct {
	LoadBalancerId string
	ListenerPort   int
}

type DescribeTCPListenerResponse struct {
	Marker          string        `json:"marker"`
	IsTruncated     bool          `json:"isTruncated"`
	NextMarker      string        `json:"nextMarker"`
	MaxKeys         int           `json:"maxKeys"`
	TCPListenerList []TCPListener `json:"listenerList"`
}

// DescribeTCPListener Describe TCPListener
// TODO: args need to validate
func (c *Client) DescribeTCPListener(ctx context.Context, args *DescribeTCPListenerArgs, option *bce.SignOption) (
	[]TCPListener, error) {
	if args == nil {
		return nil, errors.New("DescribeTCPListener need args")
	}
	var params map[string]string
	if args.ListenerPort != 0 {
		params = map[string]string{
			"listenerPort": strconv.Itoa(args.ListenerPort),
		}
	}
	req, err := bce.NewRequest("GET", c.GetURL("v1/blb"+"/"+args.LoadBalancerId+"/TCPlistener", params), nil)

	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)

	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}
	var blbsResp *DescribeTCPListenerResponse
	err = json.Unmarshal(bodyContent, &blbsResp)

	if err != nil {
		return nil, err
	}
	return blbsResp.TCPListenerList, nil
}

type DescribeUDPListenerArgs struct {
	LoadBalancerId string
	ListenerPort   int
}

type DescribeUDPListenerResponse struct {
	Marker          string        `json:"marker"`
	IsTruncated     bool          `json:"isTruncated"`
	NextMarker      string        `json:"nextMarker"`
	MaxKeys         int           `json:"maxKeys"`
	UDPListenerList []UDPListener `json:"listenerList"`
}

// DescribeUDPListeners Describe UDPListeners
// TODO: args need to validate
func (c *Client) DescribeUDPListener(ctx context.Context, args *DescribeUDPListenerArgs, option *bce.SignOption) (
	[]UDPListener, error) {
	if args == nil {
		return nil, errors.New("DescribeUDPListeners need args")
	}
	var params map[string]string
	if args.ListenerPort != 0 {
		params = map[string]string{
			"listenerPort": strconv.Itoa(args.ListenerPort),
		}
	}
	req, err := bce.NewRequest("GET", c.GetURL("v1/blb"+"/"+args.LoadBalancerId+"/UDPlistener", params), nil)

	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)

	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}
	var blbsResp *DescribeUDPListenerResponse
	err = json.Unmarshal(bodyContent, &blbsResp)

	if err != nil {
		return nil, err
	}
	return blbsResp.UDPListenerList, nil
}

type UpdateTCPListenerArgs struct {
	LoadBalancerId             string `json:"-"`
	ListenerPort               int    `json:"-"`
	BackendPort                int    `json:"backendPort,omitempty"`
	Scheduler                  string `json:"scheduler,omitempty"`
	HealthCheckTimeoutInSecond int    `json:"healthCheckTimeoutInSecond,omitempty"`
	HealthCheckInterval        int    `json:"healthCheckInterval,omitempty"`
	UnhealthyThreshold         int    `json:"unhealthyThreshold,omitempty"`
	HealthyThreshold           int    `json:"healthyThreshold,omitempty"`
}

// UpdateTCPListener update a TCPListener
// TODO: args need to validate
func (c *Client) UpdateTCPListener(ctx context.Context, args *UpdateTCPListenerArgs, option *bce.SignOption) error {
	if args == nil || args.LoadBalancerId == "" || args.ListenerPort == 0 {
		return errors.New("UpdateTCPListener need args")
	}
	var params map[string]string
	if args.ListenerPort != 0 {
		params = map[string]string{
			"listenerPort": strconv.Itoa(args.ListenerPort),
		}
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}
	req, err := bce.NewRequest("PUT", c.GetURL("v1/blb"+"/"+args.LoadBalancerId+"/TCPlistener", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

type UpdateUDPListenerArgs struct {
	LoadBalancerId             string `json:"-"`
	ListenerPort               int    `json:"listenerPort"`
	BackendPort                int    `json:"backendPort"`
	Scheduler                  string `json:"scheduler"`
	HealthCheckTimeoutInSecond int    `json:"healthCheckTimeoutInSecond,omitempty"`
	HealthCheckInterval        int    `json:"healthCheckInterval,omitempty"`
	UnhealthyThreshold         int    `json:"unhealthyThreshold,omitempty"`
	HealthyThreshold           int    `json:"healthyThreshold,omitempty"`
	HealthCheckString          string `json:"healthCheckString"`
}

func (args *UpdateUDPListenerArgs) validate() error {
	if args.LoadBalancerId == "" {
		return errors.New("UpdateUDPListener need LoadBalancerId")
	}
	if args.ListenerPort == 0 {
		return errors.New("UpdateUDPListener need ListenerPort")
	}
	if args.BackendPort == 0 {
		return errors.New("UpdateUDPListener need BackendPort")
	}
	if args.Scheduler == "" {
		return errors.New("UpdateUDPListener need Scheduler")
	}
	if args.HealthCheckString == "" {
		return errors.New("UpdateUDPListener need HealthCheckString")
	}
	return nil
}

// UpdateUDPListener update a UDPListener
func (c *Client) UpdateUDPListener(ctx context.Context, args *UpdateUDPListenerArgs, option *bce.SignOption) error {
	err := args.validate()
	if err != nil {
		return err
	}
	params := map[string]string{
		"listenerPort": strconv.Itoa(args.ListenerPort),
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}
	req, err := bce.NewRequest("PUT", c.GetURL("v1/blb"+"/"+args.LoadBalancerId+"/UDPlistener", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

type DeleteListenersArgs struct {
	LoadBalancerId string `json:"-"`

	// action         string `json:"-"`
	PortList []int `json:"portList"`
}

func (args *DeleteListenersArgs) validate() error {
	if args.LoadBalancerId == "" {
		return errors.New("DeleteListenersArgs need LoadBalancerId")
	}
	if args.PortList == nil {
		return errors.New("DeleteListenersArgs need PortList")
	}
	return nil
}

// UpdateUDPListener update a UDPListener
func (c *Client) DeleteListeners(ctx context.Context, args *DeleteListenersArgs, option *bce.SignOption) error {
	err := args.validate()
	if err != nil {
		return err
	}
	params := map[string]string{
		"batchdelete": "",
		"clientToken": c.GenerateClientToken(),
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}
	// url := "http://" + Endpoint[c.GetRegion()] + "/v1/blb" + "/" + args.LoadBalancerId + "/listener?" + "batchdelete=&" + "clientToken=" + c.GenerateClientToken()
	req, err := bce.NewRequest("PUT", c.GetURL("v1/blb"+"/"+args.LoadBalancerId+"/listener", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}
