// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/blb (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	blb "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/blb"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// AddBackendServers mocks base method.
func (m *MockInterface) AddBackendServers(arg0 context.Context, arg1 *blb.AddBackendServersArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddBackendServers", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddBackendServers indicates an expected call of AddBackendServers.
func (mr *MockInterfaceMockRecorder) AddBackendServers(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBackendServers", reflect.TypeOf((*MockInterface)(nil).AddBackendServers), arg0, arg1, arg2)
}

// CreateHTTPListener mocks base method.
func (m *MockInterface) CreateHTTPListener(arg0 context.Context, arg1 *blb.CreateHTTPListenerArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateHTTPListener", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateHTTPListener indicates an expected call of CreateHTTPListener.
func (mr *MockInterfaceMockRecorder) CreateHTTPListener(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateHTTPListener", reflect.TypeOf((*MockInterface)(nil).CreateHTTPListener), arg0, arg1, arg2)
}

// CreateLoadBalancer mocks base method.
func (m *MockInterface) CreateLoadBalancer(arg0 context.Context, arg1 *blb.CreateLoadBalancerArgs, arg2 *bce.SignOption) (*blb.CreateLoadBalancerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLoadBalancer", arg0, arg1, arg2)
	ret0, _ := ret[0].(*blb.CreateLoadBalancerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLoadBalancer indicates an expected call of CreateLoadBalancer.
func (mr *MockInterfaceMockRecorder) CreateLoadBalancer(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLoadBalancer", reflect.TypeOf((*MockInterface)(nil).CreateLoadBalancer), arg0, arg1, arg2)
}

// CreateTCPListener mocks base method.
func (m *MockInterface) CreateTCPListener(arg0 context.Context, arg1 *blb.CreateTCPListenerArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTCPListener", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTCPListener indicates an expected call of CreateTCPListener.
func (mr *MockInterfaceMockRecorder) CreateTCPListener(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTCPListener", reflect.TypeOf((*MockInterface)(nil).CreateTCPListener), arg0, arg1, arg2)
}

// CreateUDPListener mocks base method.
func (m *MockInterface) CreateUDPListener(arg0 context.Context, arg1 *blb.CreateUDPListenerArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateUDPListener", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateUDPListener indicates an expected call of CreateUDPListener.
func (mr *MockInterfaceMockRecorder) CreateUDPListener(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUDPListener", reflect.TypeOf((*MockInterface)(nil).CreateUDPListener), arg0, arg1, arg2)
}

// DeleteListeners mocks base method.
func (m *MockInterface) DeleteListeners(arg0 context.Context, arg1 *blb.DeleteListenersArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteListeners", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteListeners indicates an expected call of DeleteListeners.
func (mr *MockInterfaceMockRecorder) DeleteListeners(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteListeners", reflect.TypeOf((*MockInterface)(nil).DeleteListeners), arg0, arg1, arg2)
}

// DeleteLoadBalancer mocks base method.
func (m *MockInterface) DeleteLoadBalancer(arg0 context.Context, arg1 *blb.DeleteLoadBalancerArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteLoadBalancer", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteLoadBalancer indicates an expected call of DeleteLoadBalancer.
func (mr *MockInterfaceMockRecorder) DeleteLoadBalancer(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLoadBalancer", reflect.TypeOf((*MockInterface)(nil).DeleteLoadBalancer), arg0, arg1, arg2)
}

// DescribeBackendServers mocks base method.
func (m *MockInterface) DescribeBackendServers(arg0 context.Context, arg1 *blb.DescribeBackendServersArgs, arg2 *bce.SignOption) ([]blb.BackendServer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeBackendServers", arg0, arg1, arg2)
	ret0, _ := ret[0].([]blb.BackendServer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeBackendServers indicates an expected call of DescribeBackendServers.
func (mr *MockInterfaceMockRecorder) DescribeBackendServers(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeBackendServers", reflect.TypeOf((*MockInterface)(nil).DescribeBackendServers), arg0, arg1, arg2)
}

// DescribeLoadBalancerDetail mocks base method.
func (m *MockInterface) DescribeLoadBalancerDetail(arg0 context.Context, arg1, arg2 string, arg3 *bce.SignOption) (*blb.LoadBalancer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeLoadBalancerDetail", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*blb.LoadBalancer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeLoadBalancerDetail indicates an expected call of DescribeLoadBalancerDetail.
func (mr *MockInterfaceMockRecorder) DescribeLoadBalancerDetail(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeLoadBalancerDetail", reflect.TypeOf((*MockInterface)(nil).DescribeLoadBalancerDetail), arg0, arg1, arg2, arg3)
}

// DescribeLoadBalancers mocks base method.
func (m *MockInterface) DescribeLoadBalancers(arg0 context.Context, arg1 *blb.DescribeLoadBalancersArgs, arg2 *bce.SignOption) ([]blb.LoadBalancer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeLoadBalancers", arg0, arg1, arg2)
	ret0, _ := ret[0].([]blb.LoadBalancer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeLoadBalancers indicates an expected call of DescribeLoadBalancers.
func (mr *MockInterfaceMockRecorder) DescribeLoadBalancers(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeLoadBalancers", reflect.TypeOf((*MockInterface)(nil).DescribeLoadBalancers), arg0, arg1, arg2)
}

// DescribeLoadBalancersByName mocks base method.
func (m *MockInterface) DescribeLoadBalancersByName(arg0 context.Context, arg1 string, arg2 *bce.SignOption) ([]blb.LoadBalancer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeLoadBalancersByName", arg0, arg1, arg2)
	ret0, _ := ret[0].([]blb.LoadBalancer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeLoadBalancersByName indicates an expected call of DescribeLoadBalancersByName.
func (mr *MockInterfaceMockRecorder) DescribeLoadBalancersByName(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeLoadBalancersByName", reflect.TypeOf((*MockInterface)(nil).DescribeLoadBalancersByName), arg0, arg1, arg2)
}

// DescribeTCPListener mocks base method.
func (m *MockInterface) DescribeTCPListener(arg0 context.Context, arg1 *blb.DescribeTCPListenerArgs, arg2 *bce.SignOption) ([]blb.TCPListener, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTCPListener", arg0, arg1, arg2)
	ret0, _ := ret[0].([]blb.TCPListener)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTCPListener indicates an expected call of DescribeTCPListener.
func (mr *MockInterfaceMockRecorder) DescribeTCPListener(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTCPListener", reflect.TypeOf((*MockInterface)(nil).DescribeTCPListener), arg0, arg1, arg2)
}

// DescribeUDPListener mocks base method.
func (m *MockInterface) DescribeUDPListener(arg0 context.Context, arg1 *blb.DescribeUDPListenerArgs, arg2 *bce.SignOption) ([]blb.UDPListener, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeUDPListener", arg0, arg1, arg2)
	ret0, _ := ret[0].([]blb.UDPListener)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeUDPListener indicates an expected call of DescribeUDPListener.
func (mr *MockInterfaceMockRecorder) DescribeUDPListener(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeUDPListener", reflect.TypeOf((*MockInterface)(nil).DescribeUDPListener), arg0, arg1, arg2)
}

// RemoveBackendServers mocks base method.
func (m *MockInterface) RemoveBackendServers(arg0 context.Context, arg1 *blb.RemoveBackendServersArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveBackendServers", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveBackendServers indicates an expected call of RemoveBackendServers.
func (mr *MockInterfaceMockRecorder) RemoveBackendServers(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveBackendServers", reflect.TypeOf((*MockInterface)(nil).RemoveBackendServers), arg0, arg1, arg2)
}

// SetDebug mocks base method.
func (m *MockInterface) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug.
func (mr *MockInterfaceMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), arg0)
}

// UpdateBackendServers mocks base method.
func (m *MockInterface) UpdateBackendServers(arg0 context.Context, arg1 *blb.UpdateBackendServersArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBackendServers", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateBackendServers indicates an expected call of UpdateBackendServers.
func (mr *MockInterfaceMockRecorder) UpdateBackendServers(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBackendServers", reflect.TypeOf((*MockInterface)(nil).UpdateBackendServers), arg0, arg1, arg2)
}

// UpdateLoadBalancer mocks base method.
func (m *MockInterface) UpdateLoadBalancer(arg0 context.Context, arg1 *blb.UpdateLoadBalancerArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLoadBalancer", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateLoadBalancer indicates an expected call of UpdateLoadBalancer.
func (mr *MockInterfaceMockRecorder) UpdateLoadBalancer(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLoadBalancer", reflect.TypeOf((*MockInterface)(nil).UpdateLoadBalancer), arg0, arg1, arg2)
}

// UpdateTCPListener mocks base method.
func (m *MockInterface) UpdateTCPListener(arg0 context.Context, arg1 *blb.UpdateTCPListenerArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTCPListener", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTCPListener indicates an expected call of UpdateTCPListener.
func (mr *MockInterfaceMockRecorder) UpdateTCPListener(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTCPListener", reflect.TypeOf((*MockInterface)(nil).UpdateTCPListener), arg0, arg1, arg2)
}

// UpdateUDPListener mocks base method.
func (m *MockInterface) UpdateUDPListener(arg0 context.Context, arg1 *blb.UpdateUDPListenerArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUDPListener", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUDPListener indicates an expected call of UpdateUDPListener.
func (mr *MockInterfaceMockRecorder) UpdateUDPListener(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUDPListener", reflect.TypeOf((*MockInterface)(nil).UpdateUDPListener), arg0, arg1, arg2)
}
