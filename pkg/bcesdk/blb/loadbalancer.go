package blb

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
)

// DescribeLoadBalancers Describe loadbalancers
// TODO: args need to validate
func (c *Client) DescribeLoadBalancers(ctx context.Context, args *DescribeLoadBalancersArgs, option *bce.SignOption) (
	[]LoadBalancer, error) {
	var params map[string]string
	if args != nil {
		exactlyMatch := "true"
		if !args.ExactlyMatch {
			exactlyMatch = "false"
		}
		params = map[string]string{
			"blbId":        args.BLBID,
			"name":         args.Name,
			"bccId":        args.BCCID,
			"address":      args.Address,
			"exactlyMatch": exactlyMatch,
			"type":         args.Type,
		}
	}
	req, err := bce.NewRequest("GET", c.GetURL("v1/blb", params), nil)

	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)

	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}
	var blbsResp *DescribeLoadBalancersResponse
	err = json.Unmarshal(bodyContent, &blbsResp)

	if err != nil {
		return nil, err
	}
	return blbsResp.BLBList, nil
}

// DescribeAppLoadBalancersByName describe APP BLBs by blbName
// If blbName not exist, return err == nil
func (c *Client) DescribeLoadBalancersByName(ctx context.Context, blbName string, option *bce.SignOption) ([]LoadBalancer, error) {
	if blbName == "" {
		return nil, errors.New("DescribeAppLoadBalancerByName failed: blbName is nil")
	}

	args := &DescribeLoadBalancersArgs{
		Name:         blbName,
		ExactlyMatch: true,
	}

	blbsResp, err := c.DescribeLoadBalancers(ctx, args, option)
	if err != nil || blbsResp == nil {
		return nil, err
	}

	// If BLB not exist, err == nil
	if len(blbsResp) == 0 {
		return []LoadBalancer{}, nil
	}

	return blbsResp, nil
}

func (c *Client) DescribeLoadBalancerDetail(ctx context.Context, blbID string, blbType string, option *bce.SignOption) (*LoadBalancer, error) {
	params := map[string]string{
		"type": blbType,
	}

	url := fmt.Sprintf("v1/blb/%s", blbID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)

	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)

	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}
	var blbsResp *LoadBalancer
	err = json.Unmarshal(bodyContent, &blbsResp)

	if err != nil {
		return nil, err
	}
	return blbsResp, nil
}

// CreateLoadBalancer Create a  loadbalancer
// TODO: args need to validate
func (c *Client) CreateLoadBalancer(ctx context.Context, args *CreateLoadBalancerArgs, option *bce.SignOption) (
	*CreateLoadBalancerResponse, error) {
	var params map[string]string
	if args != nil {
		params = map[string]string{
			"clientToken": c.GenerateClientToken(),
		}
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}
	req, err := bce.NewRequest("POST", c.GetURL("v1/blb", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var blbsResp *CreateLoadBalancerResponse
	err = json.Unmarshal(bodyContent, &blbsResp)

	if err != nil {
		return nil, err
	}
	return blbsResp, nil
}

// UpdateLoadBalancer update a loadbalancer
// TODO: args need to validate
func (c *Client) UpdateLoadBalancer(ctx context.Context, args *UpdateLoadBalancerArgs, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	if args == nil {
		return errors.New("UpdateLoadBalancer need args")
	}
	postContent, err := util.ToJson(args, "desc", "name")
	// postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}
	req, err := bce.NewRequest("PUT", c.GetURL("v1/blb"+"/"+args.BLBID, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

// DeleteLoadBalancer delete a loadbalancer
func (c *Client) DeleteLoadBalancer(ctx context.Context, args *DeleteLoadBalancerArgs, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	if args == nil {
		return errors.New("DeleteLoadBalancer need args")
	}
	req, err := bce.NewRequest("DELETE", c.GetURL("v1/blb"+"/"+args.BLBID, params), nil)
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}
