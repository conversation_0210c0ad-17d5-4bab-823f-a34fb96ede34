// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/11/26 15:30:00, by <EMAIL>, create
*/
/*
文件定义 BLB OpenAPI 相关接口
*/

package blb

import (
	"context"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/blb Interface

// Interface 定义 BLB OpenAPI 相关 Interface
type Interface interface {
	SetDebug(bool)

	CreateLoadBalancer(ctx context.Context, args *CreateLoadBalancerArgs, option *bce.SignOption) (*CreateLoadBalancerResponse, error)
	DescribeLoadBalancers(ctx context.Context, args *DescribeLoadBalancersArgs, option *bce.SignOption) ([]LoadBalancer, error)
	DescribeLoadBalancerDetail(ctx context.Context, blbID string, blbType string, option *bce.SignOption) (*LoadBalancer, error)
	DescribeLoadBalancersByName(ctx context.Context, blbName string, option *bce.SignOption) ([]LoadBalancer, error)
	UpdateLoadBalancer(ctx context.Context, args *UpdateLoadBalancerArgs, option *bce.SignOption) error
	DeleteLoadBalancer(ctx context.Context, args *DeleteLoadBalancerArgs, option *bce.SignOption) error

	CreateTCPListener(ctx context.Context, args *CreateTCPListenerArgs, option *bce.SignOption) (err error)
	CreateUDPListener(ctx context.Context, args *CreateUDPListenerArgs, option *bce.SignOption) (err error)
	CreateHTTPListener(ctx context.Context, args *CreateHTTPListenerArgs, option *bce.SignOption) (err error)
	DescribeTCPListener(ctx context.Context, args *DescribeTCPListenerArgs, option *bce.SignOption) ([]TCPListener, error)
	DescribeUDPListener(ctx context.Context, args *DescribeUDPListenerArgs, option *bce.SignOption) ([]UDPListener, error)
	UpdateTCPListener(ctx context.Context, args *UpdateTCPListenerArgs, option *bce.SignOption) error
	UpdateUDPListener(ctx context.Context, args *UpdateUDPListenerArgs, option *bce.SignOption) error
	DeleteListeners(ctx context.Context, args *DeleteListenersArgs, option *bce.SignOption) error

	AddBackendServers(ctx context.Context, args *AddBackendServersArgs, option *bce.SignOption) error
	DescribeBackendServers(ctx context.Context, args *DescribeBackendServersArgs, option *bce.SignOption) ([]BackendServer, error)
	UpdateBackendServers(ctx context.Context, args *UpdateBackendServersArgs, option *bce.SignOption) error
	RemoveBackendServers(ctx context.Context, args *RemoveBackendServersArgs, option *bce.SignOption) error
}

const (
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/KzHUM_sAtc/8YYDzrTwHn/MlmvryIiNJq4Hu
	ResourceHeaderHexKey = "Hvw1j33tecGBBkVY"
)

type LoadBalancer struct {
	BLBID       string `json:"blbId"`
	Name        string `json:"name"`
	Desc        string `json:"desc"`
	Address     string `json:"address"`
	Status      string `json:"status"`
	UnderlayVIP string `json:"underlayVip"`
	PublicIP    string `json:"publicIp"`
	VPCID       string `json:"vpcId"`
	SubnetID    string `json:"subnetId"`
}

// NormalBLBStatus for Normal BLB Status
type NormalBLBStatus string

const (
	NormalBLBStatusCreating    NormalBLBStatus = "creating"
	NormalBLBStatusAvailable   NormalBLBStatus = "available"
	NormalBLBStatusUpdating    NormalBLBStatus = "updating"
	NormalBLBStatusPaused      NormalBLBStatus = "paused"
	NormalBLBStatusUnavailable NormalBLBStatus = "unavailable"
)

type LoadBalancerDetail struct {
	BLBID   string `json:"blbId"`
	Status  string `json:"status"`
	Name    string `json:"name"`
	Desc    string `json:"desc"`
	Address string `json:"address"`

	PublicIP string `json:"publicIp"`
	VPCID    string `json:"vpcId"`
	SubnetID string `json:"subnetId"`

	Listener    []ListenerModel `json:"listener"`
	AllowDelete bool            `json:"allowDelete"`
}

type ListenerModel struct {
	Port string `json:"port"`
	Type string `json:"type"`
}

type DescribeLoadBalancersArgs struct {
	BLBID        string `json:"blbId,omitempty"`
	Name         string `json:"name,omitempty"`
	BCCID        string `json:"bccId,omitempty"`
	Address      string `json:"address,omitempty"`
	ExactlyMatch bool   `json:"exactlyMatch,omitempty"`
	Type         string `json:"type,omitempty"`
}

type DescribeLoadBalancersResponse struct {
	Marker      string         `json:"marker"`
	IsTruncated bool           `json:"isTruncated"`
	NextMarker  string         `json:"nextMarker"`
	MaxKeys     int            `json:"maxKeys"`
	BLBList     []LoadBalancer `json:"blbList"`
}

// CreateLoadBalancerArgs create blb args
type CreateLoadBalancerArgs struct {
	Desc        string `json:"desc,omitempty"`
	Name        string `json:"name,omitempty"`
	VPCID       string `json:"vpcId,omitempty"`
	SubnetID    string `json:"subnetId,omitempty"`
	AllocateVIP bool   `json:"allocateVip,omitempty"`
	Type        string `json:"type,omitempty"`
}

type CreateLoadBalancerResponse struct {
	BLBID   string `json:"blbId"`
	Address string `json:"address"`
	Desc    string `json:"desc,omitempty"`
	Name    string `json:"name"`
}

type UpdateLoadBalancerArgs struct {
	BLBID string `json:"blbId"`
	Desc  string `json:"desc,omitempty"`
	Name  string `json:"name,omitempty"`
}

type DeleteLoadBalancerArgs struct {
	BLBID string `json:"blbId"`
}

type TCPListener struct {
	ListenerPort               int    `json:"listenerPort"`
	BackendPort                int    `json:"backendPort"`
	Scheduler                  string `json:"scheduler"`
	HealthCheckTimeoutInSecond int    `json:"healthCheckTimeoutInSecond"`
	HealthCheckInterval        int    `json:"healthCheckInterval"`
	UnhealthyThreshold         int    `json:"unhealthyThreshold"`
	HealthyThreshold           int    `json:"healthyThreshold"`
}

type UDPListener struct {
	ListenerPort               int    `json:"listenerPort"`
	BackendPort                int    `json:"backendPort"`
	Scheduler                  string `json:"scheduler"`
	HealthCheckTimeoutInSecond int    `json:"healthCheckTimeoutInSecond"`
	HealthCheckInterval        int    `json:"healthCheckInterval"`
	UnhealthyThreshold         int    `json:"unhealthyThreshold"`
	HealthyThreshold           int    `json:"healthyThreshold"`
	HealthCheckString          string `json:"healthCheckString"`
}

type HTTPListener struct {
	ListenerPort               int    `json:"listenerPort"`
	BackendPort                int    `json:"backendPort"`
	Scheduler                  string `json:"scheduler"`
	KeepSession                bool   `json:"keepSession"`
	KeepSessionType            string `json:"keepSessionType"`
	KeepSessionDuration        int    `json:"keepSessionDuration"`
	KeepSessionCookieName      int    `json:"keepSessionCookieName"`
	XForwardFor                bool   `json:"xForwardFor"`
	HealthCheckType            string `json:"healthCheckType"`
	HealthCheckPort            int    `json:"healthCheckPort"`
	HealthCheckURI             string `json:"healthCheckURI"`
	HealthCheckTimeoutInSecond int    `json:"healthCheckTimeoutInSecond"`
	HealthCheckInterval        int    `json:"healthCheckInterval"`
	UnhealthyThreshold         int    `json:"unhealthyThreshold"`
	HealthyThreshold           int    `json:"healthyThreshold"`
	HealthCheckNormalStatus    string `json:"healthCheckNormalStatus"`
	ServerTimeout              int    `json:"serverTimeout"`
	RedirectPort               int    `json:"redirectPort"`
}

type HTTPSListener struct {
	ListenerPort               int      `json:"listenerPort"`
	BackendPort                int      `json:"backendPort"`
	Scheduler                  string   `json:"scheduler"`
	KeepSession                bool     `json:"keepSession"`
	KeepSessionType            string   `json:"keepSessionType"`
	KeepSessionDuration        int      `json:"keepSessionDuration"`
	KeepSessionCookieName      int      `json:"keepSessionCookieName"`
	XForwardFor                bool     `json:"xForwardFor"`
	HealthCheckType            string   `json:"healthCheckType"`
	HealthCheckPort            int      `json:"healthCheckPort"`
	HealthCheckURI             string   `json:"healthCheckURI"`
	HealthCheckTimeoutInSecond int      `json:"healthCheckTimeoutInSecond"`
	HealthCheckInterval        int      `json:"healthCheckInterval"`
	UnhealthyThreshold         int      `json:"unhealthyThreshold"`
	HealthyThreshold           int      `json:"healthyThreshold"`
	HealthCheckNormalStatus    string   `json:"healthCheckNormalStatus"`
	ServerTimeout              int      `json:"serverTimeout"`
	CertIds                    []string `json:"certIds"`
	Ie6Compatible              bool     `json:"ie6Compatible"`
}

type CreateTCPListenerArgs struct {
	LoadBalancerId             string `json:"-"`
	ListenerPort               int    `json:"listenerPort"`
	BackendPort                int    `json:"backendPort"`
	Scheduler                  string `json:"scheduler"`
	HealthCheckTimeoutInSecond int    `json:"healthCheckTimeoutInSecond,omitempty"`
	HealthCheckInterval        int    `json:"healthCheckInterval,omitempty"`
	UnhealthyThreshold         int    `json:"unhealthyThreshold,omitempty"`
	HealthyThreshold           int    `json:"healthyThreshold,omitempty"`
}

type CreateUDPListenerArgs struct {
	LoadBalancerId             string `json:"-"`
	ListenerPort               int    `json:"listenerPort"`
	BackendPort                int    `json:"backendPort"`
	Scheduler                  string `json:"scheduler"`
	HealthCheckTimeoutInSecond int    `json:"healthCheckTimeoutInSecond,omitempty"`
	HealthCheckInterval        int    `json:"healthCheckInterval,omitempty"`
	UnhealthyThreshold         int    `json:"unhealthyThreshold,omitempty"`
	HealthyThreshold           int    `json:"healthyThreshold,omitempty"`
	HealthCheckString          string `json:"healthCheckString"`
}

type CreateHTTPListenerArgs struct {
	LoadBalancerId             string `json:"-"`
	ListenerPort               int    `json:"listenerPort"`
	BackendPort                int    `json:"backendPort"`
	Scheduler                  string `json:"scheduler"`
	KeepSession                bool   `json:"keepSession,omitempty"`
	KeepSessionType            string `json:"keepSessionType,omitempty"`
	KeepSessionDuration        int    `json:"keepSessionDuration,omitempty"`
	KeepSessionCookieName      int    `json:"keepSessionCookieName,omitempty"`
	XForwardFor                bool   `json:"xForwardFor,omitempty"`
	HealthCheckType            string `json:"healthCheckType,omitempty"`
	HealthCheckPort            int    `json:"healthCheckPort,omitempty"`
	HealthCheckURI             string `json:"healthCheckURI,omitempty"`
	HealthCheckTimeoutInSecond int    `json:"healthCheckTimeoutInSecond,omitempty"`
	HealthCheckInterval        int    `json:"healthCheckInterval,omitempty"`
	UnhealthyThreshold         int    `json:"unhealthyThreshold,omitempty"`
	HealthyThreshold           int    `json:"healthyThreshold,omitempty"`
	HealthCheckNormalStatus    string `json:"healthCheckNormalStatus,omitempty"`
	ServerTimeout              int    `json:"serverTimeout,omitempty"`
	RedirectPort               int    `json:"redirectPort,omitempty"`
}

type BackendServer struct {
	InstanceId string `json:"instanceId"`
	Weight     int    `json:"weight,omitempty"`
}

type BackendServerStatus struct {
	InstanceId string `json:"instanceId"`
	Weight     int    `json:"weight"`
	Status     string `json:"status"`
}

type AddBackendServersArgs struct {
	LoadBalancerId    string          `json:"-"`
	BackendServerList []BackendServer `json:"backendServerList"`
}

// SchedulerType for HTTP Listener scheduler type

const (
	// SchedulerTypeRoundRobin scheduler type = "RoundRobin"
	SchedulerTypeRoundRobin string = "RoundRobin"

	// SchedulerTypeLeastConnection scheduler type = "LeastConnection"
	SchedulerTypeLeastConnection string = "LeastConnection"

	// SchedulerTypeHash scheduler type = "Hash"
	SchedulerTypeHash string = "Hash"
)
