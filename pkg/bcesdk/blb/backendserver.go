package blb

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func (args *AddBackendServersArgs) validate() error {
	if args == nil {
		return errors.New("AddBackendServersArgs need args")
	}
	if args.LoadBalancerId == "" {
		return errors.New("AddBackendServersArgs need LoadBalancerId")
	}
	if args.BackendServerList == nil {
		return errors.New("UpdateUDPListener need BackendServerList")
	}

	return nil
}

func (c *Client) AddBackendServers(ctx context.Context, args *AddBackendServersArgs, option *bce.SignOption) error {
	err := args.validate()
	if err != nil {
		return err
	}
	params := map[string]string{
		"clientToken": c.<PERSON>oken(),
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}
	req, err := bce.NewRequest("POST", c.GetURL("v1/blb"+"/"+args.LoadBalancerId+"/backendserver", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

type DescribeBackendServersArgs struct {
	LoadBalancerId string `json:"-"`
}

type DescribeBackendServersResponse struct {
	Marker            string          `json:"marker"`
	IsTruncated       bool            `json:"isTruncated"`
	NextMarker        string          `json:"nextMarker"`
	MaxKeys           int             `json:"maxKeys"`
	BackendServerList []BackendServer `json:"backendServerList"`
}

func (args *DescribeBackendServersArgs) validate() error {
	if args == nil {
		return errors.New("DescribeBackendServersArgs need args")
	}
	if args.LoadBalancerId == "" {
		return errors.New("DescribeBackendServersArgs need LoadBalancerId")
	}
	return nil
}

func (c *Client) DescribeBackendServers(ctx context.Context, args *DescribeBackendServersArgs, option *bce.SignOption) (
	[]BackendServer, error) {
	err := args.validate()
	if err != nil {
		return nil, err
	}
	req, err := bce.NewRequest("GET", c.GetURL("v1/blb"+"/"+args.LoadBalancerId+"/backendserver", nil), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)

	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}
	var blbsResp *DescribeBackendServersResponse
	err = json.Unmarshal(bodyContent, &blbsResp)

	if err != nil {
		return nil, err
	}
	return blbsResp.BackendServerList, nil
}

type UpdateBackendServersArgs struct {
	LoadBalancerId    string          `json:"-"`
	BackendServerList []BackendServer `json:"backendServerList"`
}

func (args *UpdateBackendServersArgs) validate() error {
	if args == nil {
		return errors.New("UpdateBackendServersArgs need args")
	}
	if args.LoadBalancerId == "" {
		return errors.New("UpdateBackendServersArgs need LoadBalancerId")
	}
	if len(args.BackendServerList) == 0 {
		return errors.New("UpdateBackendServersArgs need BackendServerList")
	}
	return nil
}

// UpdateBackendServers update  BackendServers
func (c *Client) UpdateBackendServers(ctx context.Context, args *UpdateBackendServersArgs, option *bce.SignOption) error {
	err := args.validate()
	if err != nil {
		return err
	}
	params := map[string]string{
		"update":      "",
		"clientToken": c.GenerateClientToken(),
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}
	req, err := bce.NewRequest("PUT", c.GetURL("v1/blb"+"/"+args.LoadBalancerId+"/backendserver", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

type RemoveBackendServersArgs struct {
	LoadBalancerId    string   `json:"-"`
	BackendServerList []string `json:"backendServerList"`
}

func (args *RemoveBackendServersArgs) validate() error {
	if args == nil {
		return errors.New("UpdateBackendServersArgs need args")
	}
	if args.LoadBalancerId == "" {
		return errors.New("UpdateBackendServersArgs need LoadBalancerId")
	}
	if len(args.BackendServerList) == 0 {
		return errors.New("UpdateBackendServersArgs need BackendServerList")
	}
	return nil
}

// RemoveBackendServers remove a BackendServers
func (c *Client) RemoveBackendServers(ctx context.Context, args *RemoveBackendServersArgs, option *bce.SignOption) error {
	err := args.validate()
	if err != nil {
		return err
	}
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}
	req, err := bce.NewRequest("PUT", c.GetURL("v1/blb"+"/"+args.LoadBalancerId+"/backendserver", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}
