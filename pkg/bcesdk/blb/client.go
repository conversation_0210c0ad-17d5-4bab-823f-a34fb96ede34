package blb

import (
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// Endpoint contains all endpoints of Baidu Cloud BCC.
var Endpoint = map[string]string{
	"bj":  "blb.bj.baidubce.com",
	"gz":  "blb.gz.baidubce.com",
	"su":  "blb.su.baidubce.com",
	"hkg": "blb.hkg.baidubce.com",
	"fwh": "blb.fwh.baidubce.com",
	"bd":  "blb.bd.baidubce.com",
}

// Client is the bos client implemention for Baidu Cloud BOS API.
type Client struct {
	*bce.Client
}

// NewClient 初始化 BLB Client
func NewClient(config *bce.Config) *Client {
	return &Client{
		Client: bce.NewClient(config),
	}
}

// GetURL generates the full URL of http request for Baidu Cloud BOS API.
func (c *Client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoint[c.GetRegion()]
	}
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}

// SetDebug 是否开启 Debug 模式
func (c *Client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}
