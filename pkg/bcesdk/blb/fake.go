package blb

import (
	"context"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
)

// FakeClient implement of vpc.Interface
type BlbFakeClient struct {
	LoadBalancerMap  map[string]LoadBalancer
	TCPListenerMap   map[string][]TCPListener
	UDPListenerMap   map[string][]UDPListener
	HTTPListenerMap  map[string][]HTTPListener
	BackendServerMap map[string][]BackendServer
}

// NewFakeClient for BLB fake client
func NewFakeClient() *BlbFakeClient {
	return &BlbFakeClient{
		LoadBalancerMap:  map[string]LoadBalancer{},
		TCPListenerMap:   map[string][]TCPListener{},
		UDPListenerMap:   map[string][]UDPListener{},
		HTTPListenerMap:  map[string][]HTTPListener{},
		BackendServerMap: map[string][]BackendServer{},
	}
}

func (f *BlbFakeClient) SetDebug(debug bool) {
	return
}

func (f *BlbFakeClient) DescribeLoadBalancerDetail(ctx context.Context, blbID string, blbType string, option *bce.SignOption) (*LoadBalancer, error) {
	for loadBalancerID, LoadBalancer := range f.LoadBalancerMap {
		if loadBalancerID != "" && blbID != "" && loadBalancerID == blbID {
			return &LoadBalancer, nil
		}
	}
	return nil, nil
}

// LoadBalance fake func
func (f *BlbFakeClient) DescribeLoadBalancers(ctx context.Context, args *DescribeLoadBalancersArgs, option *bce.SignOption) ([]LoadBalancer, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}
	loadbalancers := []LoadBalancer{}
	for loadBalancerID, LoadBalancer := range f.LoadBalancerMap {
		if loadBalancerID != "" && args.BLBID != "" && loadBalancerID == args.BLBID ||
			LoadBalancer.Name != "" && args.Name != "" && LoadBalancer.Name == args.Name ||
			LoadBalancer.Address != "" && args.Address != "" && LoadBalancer.Address == args.Address {
			loadbalancers = append(loadbalancers, LoadBalancer)
		}
	}
	return loadbalancers, nil
}

func (f *BlbFakeClient) CreateLoadBalancer(ctx context.Context, args *CreateLoadBalancerArgs, option *bce.SignOption) (*CreateLoadBalancerResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}
	resp := &CreateLoadBalancerResponse{
		Desc:    args.Desc,
		Name:    args.Name,
		Address: "0.0.0.0",
	}
	loadbalancer := LoadBalancer{
		Name:    args.Name,
		Desc:    args.Desc,
		Status:  "available",
		Address: "0.0.0.0",
	}
	for {
		loadbalancerID := util.GenerateBCEShortID("lb")
		if _, ok := f.LoadBalancerMap[loadbalancerID]; !ok {
			loadbalancer.BLBID = loadbalancerID
			resp.BLBID = loadbalancerID
			f.LoadBalancerMap[loadbalancerID] = loadbalancer
			break
		}
	}
	if resp.BLBID == "" {
		return nil, fmt.Errorf("CreateLoadBalancer error: can not create LoadBalancer from args: %v", args)
	}
	return resp, nil
}

func (f *BlbFakeClient) UpdateLoadBalancer(ctx context.Context, args *UpdateLoadBalancerArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("args is nil")
	}
	if _, ok := f.LoadBalancerMap[args.BLBID]; ok {
		loadblanace := f.LoadBalancerMap[args.BLBID]
		loadblanace.Desc = args.Desc
		loadblanace.Name = args.Name
		f.LoadBalancerMap[args.BLBID] = loadblanace
		return nil
	}
	return errors.New("blbID does not exist")
}

func (f *BlbFakeClient) DeleteLoadBalancer(ctx context.Context, args *DeleteLoadBalancerArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("args is nil")
	}
	if _, ok := f.LoadBalancerMap[args.BLBID]; ok {
		delete(f.LoadBalancerMap, args.BLBID)
		return nil
	}
	return errors.New("LoadBalancerId does not exist")
}

// Listenr fake func
func (f *BlbFakeClient) CreateTCPListener(ctx context.Context, args *CreateTCPListenerArgs, option *bce.SignOption) (err error) {
	if args == nil {
		return errors.New("args is nil")
	}
	tcp := TCPListener{
		ListenerPort:               args.ListenerPort,
		BackendPort:                args.BackendPort,
		Scheduler:                  args.Scheduler,
		HealthCheckTimeoutInSecond: args.HealthCheckTimeoutInSecond,
		HealthCheckInterval:        args.HealthCheckInterval,
		UnhealthyThreshold:         args.UnhealthyThreshold,
		HealthyThreshold:           args.HealthyThreshold,
	}
	// check LoadBalancerId if nil
	argsLb := &DescribeLoadBalancersArgs{
		BLBID: args.LoadBalancerId,
	}
	lbs, err := f.DescribeLoadBalancers(ctx, argsLb, nil)
	if err != nil || len(lbs) == 0 {
		return fmt.Errorf("can not get lb according to args’ BlbID err: %v", err)
	}
	f.TCPListenerMap[args.LoadBalancerId] = append(f.TCPListenerMap[args.LoadBalancerId], tcp)
	return nil
}

func (f *BlbFakeClient) CreateUDPListener(ctx context.Context, args *CreateUDPListenerArgs, option *bce.SignOption) (err error) {
	if args == nil {
		return errors.New("args is nil")
	}
	udp := UDPListener{
		ListenerPort:               args.ListenerPort,
		BackendPort:                args.BackendPort,
		Scheduler:                  args.Scheduler,
		HealthCheckTimeoutInSecond: args.HealthCheckTimeoutInSecond,
		HealthCheckInterval:        args.HealthCheckInterval,
		UnhealthyThreshold:         args.UnhealthyThreshold,
		HealthyThreshold:           args.HealthyThreshold,
		HealthCheckString:          args.HealthCheckString,
	}
	// check LoadBalancerId if nil
	// check LoadBalancerId if nil
	argsLb := &DescribeLoadBalancersArgs{
		BLBID: args.LoadBalancerId,
	}
	lbs, err := f.DescribeLoadBalancers(ctx, argsLb, nil)
	if err != nil || len(lbs) == 0 {
		return fmt.Errorf("can not get lb according to args’ BlbID err: %v", err)
	}
	f.UDPListenerMap[args.LoadBalancerId] = append(f.UDPListenerMap[args.LoadBalancerId], udp)
	return nil
}

func (f *BlbFakeClient) CreateHTTPListener(ctx context.Context, args *CreateHTTPListenerArgs, option *bce.SignOption) (err error) {
	if args == nil {
		return errors.New("args is nil")
	}
	http := HTTPListener{
		ListenerPort:               args.ListenerPort,
		BackendPort:                args.BackendPort,
		Scheduler:                  args.Scheduler,
		KeepSession:                args.KeepSession,
		KeepSessionType:            args.KeepSessionType,
		KeepSessionDuration:        args.KeepSessionDuration,
		KeepSessionCookieName:      args.KeepSessionCookieName,
		XForwardFor:                args.XForwardFor,
		HealthCheckType:            args.HealthCheckType,
		HealthCheckURI:             args.HealthCheckURI,
		HealthCheckTimeoutInSecond: args.HealthCheckTimeoutInSecond,
		UnhealthyThreshold:         args.UnhealthyThreshold,
		HealthyThreshold:           args.HealthyThreshold,
		HealthCheckNormalStatus:    args.HealthCheckNormalStatus,
		ServerTimeout:              args.ServerTimeout,
		RedirectPort:               args.RedirectPort,
	}
	f.HTTPListenerMap[args.LoadBalancerId] = append(f.HTTPListenerMap[args.LoadBalancerId], http)
	return nil
}

func (f *BlbFakeClient) DescribeTCPListener(ctx context.Context, args *DescribeTCPListenerArgs, option *bce.SignOption) ([]TCPListener, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}
	tcpListeners := []TCPListener{}
	if _, ok := f.TCPListenerMap[args.LoadBalancerId]; ok {
		for _, t := range f.TCPListenerMap[args.LoadBalancerId] {
			tcpListeners = append(tcpListeners, t)
		}
		return tcpListeners, nil
	}
	return nil, fmt.Errorf("DescribeTCPListener failed, can not get tcpListeners from args %v", args)
}

func (f *BlbFakeClient) DescribeUDPListener(ctx context.Context, args *DescribeUDPListenerArgs, option *bce.SignOption) ([]UDPListener, error) {
	if args == nil {
		return nil, errors.New("DescribeUDPListeners need args")
	}
	if args.LoadBalancerId == "" {
		return nil, errors.New("DescribeUDPListeners args need loadbalancerId")
	}
	udpListenerList, found := f.UDPListenerMap[args.LoadBalancerId]
	if !found {
		return nil, fmt.Errorf("Sepcified BLB %s not found", args.LoadBalancerId)
	}
	result := make([]UDPListener, 0)
	for _, u := range udpListenerList {
		if args.ListenerPort != 0 && u.ListenerPort != args.ListenerPort {
			continue
		}
		result = append(result, u)
	}
	return result, nil
}

func (f *BlbFakeClient) UpdateTCPListener(ctx context.Context, args *UpdateTCPListenerArgs, option *bce.SignOption) error {
	if args == nil || args.LoadBalancerId == "" || args.ListenerPort == 0 {
		return errors.New("UpdateTCPListener need args")
	}
	rawTcpList, found := f.TCPListenerMap[args.LoadBalancerId]
	if !found {
		return fmt.Errorf("Specified BLB %s not found", args.LoadBalancerId)
	}
	tcpList := make([]TCPListener, 0)
	for _, t := range rawTcpList {
		if t.ListenerPort != args.ListenerPort {
			tcpList = append(tcpList, t)
		}
	}
	newTcpListner := TCPListener{
		ListenerPort:               args.ListenerPort,
		HealthCheckInterval:        args.HealthCheckInterval,
		HealthCheckTimeoutInSecond: args.HealthCheckTimeoutInSecond,
		HealthyThreshold:           args.HealthyThreshold,
		UnhealthyThreshold:         args.UnhealthyThreshold,
		BackendPort:                args.BackendPort,
		Scheduler:                  args.Scheduler,
	}
	tcpList = append(tcpList, newTcpListner)
	return nil
}

func (f *BlbFakeClient) UpdateUDPListener(ctx context.Context, args *UpdateUDPListenerArgs, option *bce.SignOption) error {
	err := validateUpdateUDPListenerArgs(args)
	if err != nil {
		return err
	}
	rawUdpList, found := f.UDPListenerMap[args.LoadBalancerId]
	if !found {
		return fmt.Errorf("Specified BLB %s not found", args.LoadBalancerId)
	}
	udpList := make([]UDPListener, 0)
	for _, u := range rawUdpList {
		if u.ListenerPort != args.ListenerPort {
			udpList = append(udpList, u)
		}
	}
	newUdpListener := UDPListener{
		ListenerPort:               args.ListenerPort,
		HealthCheckInterval:        args.HealthCheckInterval,
		HealthCheckString:          args.HealthCheckString,
		HealthCheckTimeoutInSecond: args.HealthCheckTimeoutInSecond,
		HealthyThreshold:           args.HealthyThreshold,
		UnhealthyThreshold:         args.UnhealthyThreshold,
		BackendPort:                args.BackendPort,
		Scheduler:                  args.Scheduler,
	}
	udpList = append(udpList, newUdpListener)
	return nil
}

func (f *BlbFakeClient) DeleteListeners(ctx context.Context, args *DeleteListenersArgs, option *bce.SignOption) error {
	err := validateDeleteListenersArgs(args)
	if err != nil {
		return err
	}
	// listener port to remove
	listenerToRemove := make(map[int]int, len(args.PortList))
	for _, p := range args.PortList {
		listenerToRemove[p] = p
	}
	// tcp
	rawTcpList, found := f.TCPListenerMap[args.LoadBalancerId]
	if found {
		tcpList := make([]TCPListener, 0)
		for _, t := range rawTcpList {
			if _, in := listenerToRemove[t.ListenerPort]; !in {
				tcpList = append(tcpList, t)
			}
		}
		f.TCPListenerMap[args.LoadBalancerId] = tcpList
	}
	// udp
	rawUdpList, found := f.UDPListenerMap[args.LoadBalancerId]
	if found {
		udpList := make([]UDPListener, 0)
		for _, u := range rawUdpList {
			if _, in := listenerToRemove[u.ListenerPort]; !in {
				udpList = append(udpList, u)
			}
		}
		f.UDPListenerMap[args.LoadBalancerId] = udpList
	}
	// http
	rawHttpList, found := f.HTTPListenerMap[args.LoadBalancerId]
	if found {
		httpList := make([]HTTPListener, 0)
		for _, h := range rawHttpList {
			if _, in := listenerToRemove[h.ListenerPort]; !in {
				httpList = append(httpList, h)
			}
		}
		f.HTTPListenerMap[args.LoadBalancerId] = httpList
	}
	// TODO: https
	return nil
}

// backendserver fake func
func (f *BlbFakeClient) AddBackendServers(ctx context.Context, args *AddBackendServersArgs, option *bce.SignOption) error {
	if err := validateAddBackendServersArgs(args); err != nil {
		return err
	}
	_, found := f.LoadBalancerMap[args.LoadBalancerId]
	if !found {
		return fmt.Errorf("Specified BLB %s not found", args.LoadBalancerId)
	}
	backendList := make([]BackendServer, 0)
	for _, rs := range args.BackendServerList {
		backendList = append(backendList, BackendServer{
			InstanceId: rs.InstanceId,
			Weight:     rs.Weight,
		})
	}
	f.BackendServerMap[args.LoadBalancerId] = append(f.BackendServerMap[args.LoadBalancerId], backendList...)
	return nil
}

func (f *BlbFakeClient) DescribeBackendServers(ctx context.Context, args *DescribeBackendServersArgs, option *bce.SignOption) ([]BackendServer, error) {
	err := validateDescribeBackendServersArgs(args)
	if err != nil {
		return nil, err
	}
	result := make([]BackendServer, 0)
	rsList := f.BackendServerMap[args.LoadBalancerId]
	for _, rs := range rsList {
		result = append(result, BackendServer{
			InstanceId: rs.InstanceId,
			Weight:     rs.Weight,
		})
	}
	return result, nil
}

func (f *BlbFakeClient) UpdateBackendServers(ctx context.Context, args *UpdateBackendServersArgs, option *bce.SignOption) error {
	err := validateUpdateBackendServersArgs(args)
	if err != nil {
		return err
	}
	rawBackendList, found := f.BackendServerMap[args.LoadBalancerId]
	if !found {
		return fmt.Errorf("Specified BLB %s not found", args.LoadBalancerId)
	}
	backendList := make([]BackendServer, 0)
	backendsToUpdate := make(map[string]string)
	for _, rs := range args.BackendServerList {
		backendsToUpdate[rs.InstanceId] = rs.InstanceId
		backendList = append(backendList, BackendServer{
			InstanceId: rs.InstanceId,
			Weight:     rs.Weight,
		})
	}
	for _, b := range rawBackendList {
		if _, found := backendsToUpdate[b.InstanceId]; !found {
			backendList = append(backendList, b)
		}
	}
	f.BackendServerMap[args.LoadBalancerId] = backendList
	return nil
}

func (f *BlbFakeClient) RemoveBackendServers(ctx context.Context, args *RemoveBackendServersArgs, option *bce.SignOption) error {
	err := validateRemoveBackendServersArgs(args)
	if err != nil {
		return err
	}
	rsList, found := f.BackendServerMap[args.LoadBalancerId]
	if !found {
		return fmt.Errorf("BLB %s not found", args.LoadBalancerId)
	}
	rsToRemove := make(map[string]string, len(args.BackendServerList))
	for _, instanceID := range args.BackendServerList {
		rsToRemove[instanceID] = instanceID
	}
	leftRs := make([]BackendServer, 0)
	for _, rs := range rsList {
		if _, found := rsToRemove[rs.InstanceId]; !found {
			leftRs = append(leftRs, rs)
		}
	}
	f.BackendServerMap[args.LoadBalancerId] = leftRs
	return nil
}

func validateUpdateUDPListenerArgs(args *UpdateUDPListenerArgs) error {
	if args.LoadBalancerId == "" {
		return errors.New("UpdateUDPListener need LoadBalancerId")
	}
	if args.ListenerPort == 0 {
		return errors.New("UpdateUDPListener need ListenerPort")
	}
	if args.BackendPort == 0 {
		return errors.New("UpdateUDPListener need BackendPort")
	}
	if args.Scheduler == "" {
		return errors.New("UpdateUDPListener need Scheduler")
	}
	if args.HealthCheckString == "" {
		return errors.New("UpdateUDPListener need HealthCheckString")
	}
	return nil
}

func validateDeleteListenersArgs(args *DeleteListenersArgs) error {
	if args.LoadBalancerId == "" {
		return errors.New("DeleteListenersArgs need LoadBalancerId")
	}
	if args.PortList == nil {
		return errors.New("DeleteListenersArgs need PortList")
	}
	return nil
}

func validateAddBackendServersArgs(args *AddBackendServersArgs) error {
	if args == nil {
		return errors.New("AddBackendServersArgs need args")
	}
	if args.LoadBalancerId == "" {
		return errors.New("AddBackendServersArgs need LoadBalancerId")
	}
	if args.BackendServerList == nil {
		return errors.New("UpdateUDPListener need BackendServerList")
	}
	return nil
}

func validateDescribeBackendServersArgs(args *DescribeBackendServersArgs) error {
	if args == nil {
		return errors.New("DescribeBackendServersArgs need args")
	}
	if args.LoadBalancerId == "" {
		return errors.New("DescribeBackendServersArgs need LoadBalancerId")
	}
	return nil
}

func validateUpdateBackendServersArgs(args *UpdateBackendServersArgs) error {
	if args == nil {
		return errors.New("UpdateBackendServersArgs need args")
	}
	if args.LoadBalancerId == "" {
		return errors.New("UpdateBackendServersArgs need LoadBalancerId")
	}
	if len(args.BackendServerList) == 0 {
		return errors.New("UpdateBackendServersArgs need BackendServerList")
	}
	return nil
}

func validateRemoveBackendServersArgs(args *RemoveBackendServersArgs) error {
	if args == nil {
		return errors.New("UpdateBackendServersArgs need args")
	}
	if args.LoadBalancerId == "" {
		return errors.New("UpdateBackendServersArgs need LoadBalancerId")
	}
	if len(args.BackendServerList) == 0 {
		return errors.New("UpdateBackendServersArgs need BackendServerList")
	}
	return nil
}

func (f *BlbFakeClient) DescribeLoadBalancersByName(ctx context.Context, blbName string, option *bce.SignOption) ([]LoadBalancer, error) {
	return nil, nil
}
