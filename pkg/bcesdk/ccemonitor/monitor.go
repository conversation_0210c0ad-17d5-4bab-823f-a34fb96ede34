// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/09/01 , by z<PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
实现 CCE V2 SDK Monitor 相关方法
*/
package ccemonitor

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/inspect"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

// GetClusterMaster - 获取集群Master节点
// PARAMS:
//  - ctx: The context to trace request
//  - clusterID: string 集群ID
//  - option: *bce.SignOption 代签名方法
//
// RETURNS:
//   GetClusterMasterResponse: result
//   error: nil if succeed, error if fail

func (c *Client) GetClusterMaster(ctx context.Context, clusterID string, option *bce.SignOption) ([]*GetClusterMasterResponse, error) {
	if clusterID == "" {
		return nil, errors.New("clusterID is empty")
	}
	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := fmt.Sprintf("monitor/clustermaster")
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取集群Master节点",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	logger.Infof(ctx, "bodyContent: %v", string(bodyContent))

	var result []*GetClusterMasterResponse
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// GetK8sEvent - 获取资源的事件
// PARAMS:
//   - ctx: The context to trace request
//   - args: 输入参数
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	K8sEventResults: result
//	error: nil if succeed, error if fail
func (c *Client) GetK8sEvent(ctx context.Context, args *EventQueryArg, option *bce.SignOption) (*K8sEventResults, error) {
	if args == nil {
		return nil, errors.New("args is empty")
	}
	params := map[string]string{}

	if args.PageSize == 0 {
		args.PageSize = 10
	}

	if args.PageNo == 0 {
		args.PageNo = 1
	}

	postContent, err := json.Marshal(args)

	url := fmt.Sprintf("event")
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取节点事件",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}

	var eventResults *K8sEventResults
	err = json.Unmarshal(bodyContent, &eventResults)
	if err != nil {
		return nil, err
	}

	return eventResults, nil
}

// OpenClusterAuditDeploy 校验服务画像
func (c *Client) OpenClusterAuditDeploy(ctx context.Context, clusterID string, option *bce.SignOption) (*AuditDeployResponse, error) {
	if clusterID == "" {
		return nil, errors.New("clusterID is empty")
	}

	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := fmt.Sprintf("audit/deploy")
	req, err := bce.NewRequest("POST", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "开启集群审计",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}

	var deployResults *AuditDeployResponse
	err = json.Unmarshal(bodyContent, &deployResults)
	if err != nil {
		return nil, err
	}

	return deployResults, nil

}

// GetClusterAuditDeploy 查询集群是否开启审计
func (c *Client) GetClusterAuditDeploy(ctx context.Context, clusterID string, option *bce.SignOption) (*AuditDeployResponse, error) {
	if clusterID == "" {
		return nil, errors.New("clusterID is empty")
	}

	params := map[string]string{
		"clusterUuid": clusterID,
	}
	url := fmt.Sprintf("audit/deploy")
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "查询审计是否开启",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}

	var deployResults *AuditDeployResponse
	err = json.Unmarshal(bodyContent, &deployResults)
	if err != nil {
		return nil, err
	}

	return deployResults, nil
}

func (c *Client) GetClusterReport(ctx context.Context, clusterID string, args *ClusterReportRequest, option *bce.SignOption) (*ClusterReportResponse, error) {
	if clusterID == "" {
		return nil, errors.New("clusterID is empty")
	}

	params := map[string]string{
		"clusterUuid": clusterID,
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("clusterreport/report")
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    fmt.Sprintf("查询服务画像(%v)", args.Category),
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}

	var reportResults *ClusterReportResponse
	err = json.Unmarshal(bodyContent, &reportResults)
	if err != nil {
		return nil, err
	}

	return reportResults, nil
}

func (c *Client) ToggleEvents(ctx context.Context, clusterID string, eventStatus EventStatus, option *bce.SignOption) (bool, error) {
	if clusterID == "" {
		return false, errors.New("clusterID is empty")
	}

	params := map[string]string{
		"clusterUuid": clusterID,
	}

	req, err := bce.NewRequest("PUT", c.GetURL(fmt.Sprintf("event/%s", eventStatus), params), nil)

	if err != nil {
		return false, err
	}

	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "开关事件持久化",
		inspect.InspectLatency: "2000",
	})

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return false, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return false, err
	}

	r, err := strconv.ParseBool(string(bodyContent))

	return r, err

}
