// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/09/01, by z<PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
CCE V2 版本 GO SDK, monitor 定义
*/
package ccemonitor

import (
	"context"
	"time"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/cli-runtime/pkg/resource"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/logicbcc"
)

type Interface interface {
	GetClusterMaster(ctx context.Context, clusterID string, option *bce.SignOption) ([]*GetClusterMasterResponse, error)
	GetK8sEvent(ctx context.Context, args *EventQueryArg, option *bce.SignOption) (*K8sEventResults, error)
	OpenClusterAuditDeploy(ctx context.Context, clusterID string, option *bce.SignOption) (*AuditDeployResponse, error)
	GetClusterAuditDeploy(ctx context.Context, clusterID string, option *bce.SignOption) (*AuditDeployResponse, error)
	GetClusterReport(ctx context.Context, clusterID string, args *ClusterReportRequest, option *bce.SignOption) (*ClusterReportResponse, error)
	ToggleEvents(ctx context.Context, clusterID string, eventStatus EventStatus, option *bce.SignOption) (bool, error)
}

type GetClusterMasterResponse struct {
	UserID      string                            `json:"userID"`
	AccountID   string                            `json:"accountID"`
	ClusterID   string                            `json:"ClusterUuid"`
	ClusterRole models.ServiceInstanceClusterRole `json:"role"`

	InstanceID   string `json:"instanceShortId"`
	InstanceUUID string `json:"instanceUuid"`
	InstanceName string `json:"instanceName"`

	InstanceType string `json:"instanceType"`
	ChargeType   string `json:"chargeType"`

	HostName      string `json:"hostName"`
	AdminPass     string `json:"adminPass"`
	AvailableZone string `json:"availableZone"`

	BCCStatus  logicbcc.ServerStatus        `json:"bccStatus"`
	Status     models.ServiceInstanceStatus `json:"stepStatus"`
	CreateTime time.Time                    `json:"createTime"`

	CPU     int `json:"cpu"`
	Memory  int `json:"memory"`
	SysDisk int `json:"sysDisk"`

	EIP          string `json:"eip"`
	EIPBandwidth int    `json:"eipBandwidth"`
	VPCIP        string `json:"fixIP"`
	VPCCIDR      string `json:"vpcCIDR"`
	VPCID        string `json:"vpcID"`
	FloatingIP   string `json:"floatingIP"`
	SubnetID     string `json:"subnetID"`

	TransactionID string `json:"transactionID"`

	K8SStatus string `json:"k8s_status"`
}

type EventQueryArg struct {
	StartTime    string `json:"startTime,omitempty"`
	EndTime      string `json:"endTime,omitempty"`
	ClusterUuid  string `json:"clusterUuid"`
	Namespace    string `json:"namespace,omitempty"`
	ResourceName string `json:"resourceName,omitempty"`
	ResourceKind string `json:"resourceKind,omitempty"`
	Message      string `json:"message,omitempty"`
	EventType    string `json:"eventType,omitempty"`
	PageNo       int    `json:"pageNo"`
	PageSize     int    `json:"pageSize"`
	OrderBy      string `json:"orderBy,omitempty"`
	Order        string `json:"order,omitempty"`
}

type K8sEvent struct {
	Count                    int32              `json:"count"`
	EventTags                EventTags          `json:"EventTags"`
	FirstOccurrenceTimestamp time.Time          `json:"FirstOccurrenceTimestamp"`
	InvolvedObject           v1.ObjectReference `json:"InvolvedObject"`
	LastOccurrenceTimestamp  time.Time          `json:"LastOccurrenceTimestamp"`
	Message                  string             `json:"Message"`
	Metadata                 metav1.ObjectMeta  `json:"Metadata"`
	Reason                   string             `json:"Reason"`
	Source                   v1.EventSource     `json:"Source"`
	Type                     string             `json:"Type"`
}

type EventTags struct {
	EventID   string `json:"eventID"`
	ClusterID string `json:"clusterID"`
}

type K8sEventResults struct {
	Result     []*K8sEvent `json:"result"`
	Order      string      `json:"order"`
	OrderBy    string      `json:"orderBy"`
	PageNo     int         `json:"pageNo"`
	PageSize   int         `json:"pageSize"`
	TotalCount int64       `json:"totalCount"`
}

type AuditDeployResponse struct {
	HasDeploy bool   `json:"hasDeploy"`
	Version   string `json:"version"`
}

// ClusterReportRequest 获取集群健康报表的请求体
type ClusterReportRequest struct {
	ClusterID    string             `json:"clusterID"`
	Namespace    string             `json:"namespace,omitempty"`
	ResourceName string             `json:"resourceName,omitempty"`
	ResourceType ReportResourceType `json:"resourceType,omitempty"`
	Category     Category           `json:"category,omitempty"`
	PageNo       int                `json:"pageNo"`
	PageSize     int                `json:"pageSize"`
	OrderBy      string             `json:"orderBy,omitempty"`
	Order        string             `json:"order,omitempty"`
}

type ReportResourceType string

const (
	Deployment ReportResourceType = "Deployment"

	DaemonSet ReportResourceType = "DaemonSet"

	StatefulSet ReportResourceType = "StatefulSet"

	Summary ReportResourceType = "Summary"

	Service ReportResourceType = "Service"

	CronJob ReportResourceType = "CronJob"

	Job ReportResourceType = "Job"
)

// Category 检查类别
type Category string

const (
	StandardImageCheck    Category = "Images"
	StandardAppCheck      Category = "Apps"
	StandardNetworkCheck  Category = "Networking"
	StandardSecurityCheck Category = "Security"
)

// ClusterReportResponse 集群健康报表的返回
type ClusterReportResponse struct {
	Result     []*HealthInfo `json:"result"`
	Score      float32       `json:"score"` // 得分
	PageNo     int           `json:"pageNo"`
	PageSize   int           `json:"pageSize"`
	OrderBy    string        `json:"orderBy,omitempty"`
	Order      string        `json:"order,omitempty"`
	TotalCount int64         `json:"totalCount"`
}

// HealthInfo 集群健康单条详细信息
type HealthInfo struct {
	resource.Result                    // 指定资源的检查结果
	Namespace       string             `json:"namespace,omitempty"`    // 命名空间 eg: default
	ResourceName    string             `json:"resourceName,omitempty"` // 资源名称 eg: job-nginx
	ResourceType    ReportResourceType `json:"resourceType,omitempty"` // 资源类型 eg: Deployment
}

const (
	WarningType string = "Warning"
	NormalType  string = "Normal"
)

type EventStatus string

const (
	EventStatusOpen  EventStatus = "open"
	EventStatusClose EventStatus = "close"
)
