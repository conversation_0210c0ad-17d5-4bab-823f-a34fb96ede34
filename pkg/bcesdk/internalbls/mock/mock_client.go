// Code generated by MockGen. DO NOT EDIT.
// Source: ./types.go

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	internalbls "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/internalbls"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// Activate mocks base method.
func (m *MockInterface) Activate(ctx context.Context, opt *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Activate", ctx, opt)
	ret0, _ := ret[0].(error)
	return ret0
}

// Activate indicates an expected call of Activate.
func (mr *MockInterfaceMockRecorder) Activate(ctx, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Activate", reflect.TypeOf((*MockInterface)(nil).Activate), ctx, opt)
}

// BindingTask mocks base method.
func (m *MockInterface) BindingTask(ctx context.Context, args *internalbls.BindingTaskRequest, opt *bce.SignOption) (*internalbls.BindingTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindingTask", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.BindingTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BindingTask indicates an expected call of BindingTask.
func (mr *MockInterfaceMockRecorder) BindingTask(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindingTask", reflect.TypeOf((*MockInterface)(nil).BindingTask), ctx, args, opt)
}

// CreateCollectorGroup mocks base method.
func (m *MockInterface) CreateCollectorGroup(ctx context.Context, args *internalbls.CreateCollectorGroupRequest, opt *bce.SignOption) (*internalbls.CreateCollectorGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCollectorGroup", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.CreateCollectorGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCollectorGroup indicates an expected call of CreateCollectorGroup.
func (mr *MockInterfaceMockRecorder) CreateCollectorGroup(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCollectorGroup", reflect.TypeOf((*MockInterface)(nil).CreateCollectorGroup), ctx, args, opt)
}

// CreateIndex mocks base method.
func (m *MockInterface) CreateIndex(ctx context.Context, args *internalbls.CreateIndexRequest, opt *bce.SignOption) (*internalbls.CreateIndexResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateIndex", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.CreateIndexResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateIndex indicates an expected call of CreateIndex.
func (mr *MockInterfaceMockRecorder) CreateIndex(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIndex", reflect.TypeOf((*MockInterface)(nil).CreateIndex), ctx, args, opt)
}

// CreateLogStore mocks base method.
func (m *MockInterface) CreateLogStore(ctx context.Context, args *internalbls.CreateLogStoreRequest, opt *bce.SignOption) (*internalbls.CreateLogStoreResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLogStore", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.CreateLogStoreResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLogStore indicates an expected call of CreateLogStore.
func (mr *MockInterfaceMockRecorder) CreateLogStore(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLogStore", reflect.TypeOf((*MockInterface)(nil).CreateLogStore), ctx, args, opt)
}

// CreateTask mocks base method.
func (m *MockInterface) CreateTask(ctx context.Context, args *internalbls.CreateTaskRequest, opt *bce.SignOption) (*internalbls.CreateTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTask", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.CreateTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTask indicates an expected call of CreateTask.
func (mr *MockInterfaceMockRecorder) CreateTask(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTask", reflect.TypeOf((*MockInterface)(nil).CreateTask), ctx, args, opt)
}

// CreateToken mocks base method.
func (m *MockInterface) CreateToken(ctx context.Context, args *internalbls.CreateTokenRequest, opt *bce.SignOption) (*internalbls.CreateTokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateToken", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.CreateTokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateToken indicates an expected call of CreateToken.
func (mr *MockInterfaceMockRecorder) CreateToken(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateToken", reflect.TypeOf((*MockInterface)(nil).CreateToken), ctx, args, opt)
}

// DeleteCollectorGroup mocks base method.
func (m *MockInterface) DeleteCollectorGroup(ctx context.Context, args *internalbls.DeleteCollectorGroupRequest, opt *bce.SignOption) (*internalbls.DeleteCollectorGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteCollectorGroup", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.DeleteCollectorGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteCollectorGroup indicates an expected call of DeleteCollectorGroup.
func (mr *MockInterfaceMockRecorder) DeleteCollectorGroup(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCollectorGroup", reflect.TypeOf((*MockInterface)(nil).DeleteCollectorGroup), ctx, args, opt)
}

// DeleteLogStore mocks base method.
func (m *MockInterface) DeleteLogStore(ctx context.Context, args *internalbls.DeleteLogStoreRequest, opt *bce.SignOption) (*internalbls.DeleteLogStoreResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteLogStore", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.DeleteLogStoreResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteLogStore indicates an expected call of DeleteLogStore.
func (mr *MockInterfaceMockRecorder) DeleteLogStore(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLogStore", reflect.TypeOf((*MockInterface)(nil).DeleteLogStore), ctx, args, opt)
}

// DeleteTask mocks base method.
func (m *MockInterface) DeleteTask(ctx context.Context, args *internalbls.DeleteTaskRequest, opt *bce.SignOption) (*internalbls.DeleteTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTask", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.DeleteTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTask indicates an expected call of DeleteTask.
func (mr *MockInterfaceMockRecorder) DeleteTask(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTask", reflect.TypeOf((*MockInterface)(nil).DeleteTask), ctx, args, opt)
}

// DeleteToken mocks base method.
func (m *MockInterface) DeleteToken(ctx context.Context, args *internalbls.DeleteTokenRequest, opt *bce.SignOption) (*internalbls.DeleteTokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteToken", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.DeleteTokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteToken indicates an expected call of DeleteToken.
func (mr *MockInterfaceMockRecorder) DeleteToken(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteToken", reflect.TypeOf((*MockInterface)(nil).DeleteToken), ctx, args, opt)
}

// GetCollectorGroup mocks base method.
func (m *MockInterface) GetCollectorGroup(ctx context.Context, args *internalbls.GetCollectorGroupRequest, opt *bce.SignOption) (*internalbls.GetCollectorGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCollectorGroup", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.GetCollectorGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCollectorGroup indicates an expected call of GetCollectorGroup.
func (mr *MockInterfaceMockRecorder) GetCollectorGroup(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCollectorGroup", reflect.TypeOf((*MockInterface)(nil).GetCollectorGroup), ctx, args, opt)
}

// GetLogStore mocks base method.
func (m *MockInterface) GetLogStore(ctx context.Context, args *internalbls.GetLogStoreRequest, opt *bce.SignOption) (*internalbls.GetLogStoreResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLogStore", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.GetLogStoreResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLogStore indicates an expected call of GetLogStore.
func (mr *MockInterfaceMockRecorder) GetLogStore(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLogStore", reflect.TypeOf((*MockInterface)(nil).GetLogStore), ctx, args, opt)
}

// GetTask mocks base method.
func (m *MockInterface) GetTask(ctx context.Context, args *internalbls.GetTaskRequest, opt *bce.SignOption) (*internalbls.GetTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTask", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.GetTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTask indicates an expected call of GetTask.
func (mr *MockInterfaceMockRecorder) GetTask(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTask", reflect.TypeOf((*MockInterface)(nil).GetTask), ctx, args, opt)
}

// GroupAddItem mocks base method.
func (m *MockInterface) GroupAddItem(ctx context.Context, args *internalbls.GroupAddItemRequest, opt *bce.SignOption) (*internalbls.GroupAddItemResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GroupAddItem", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.GroupAddItemResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GroupAddItem indicates an expected call of GroupAddItem.
func (mr *MockInterfaceMockRecorder) GroupAddItem(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GroupAddItem", reflect.TypeOf((*MockInterface)(nil).GroupAddItem), ctx, args, opt)
}

// GroupDeleteItem mocks base method.
func (m *MockInterface) GroupDeleteItem(ctx context.Context, args *internalbls.GroupDeleteItemRequest, opt *bce.SignOption) (*internalbls.GroupDeleteItemResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GroupDeleteItem", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.GroupDeleteItemResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GroupDeleteItem indicates an expected call of GroupDeleteItem.
func (mr *MockInterfaceMockRecorder) GroupDeleteItem(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GroupDeleteItem", reflect.TypeOf((*MockInterface)(nil).GroupDeleteItem), ctx, args, opt)
}

// IsActivate mocks base method.
func (m *MockInterface) IsActivate(ctx context.Context, opt *bce.SignOption) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsActivate", ctx, opt)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsActivate indicates an expected call of IsActivate.
func (mr *MockInterfaceMockRecorder) IsActivate(ctx, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsActivate", reflect.TypeOf((*MockInterface)(nil).IsActivate), ctx, opt)
}

// ListCollector mocks base method.
func (m *MockInterface) ListCollector(ctx context.Context, args internalbls.ListCollectorRequest, opt *bce.SignOption) (*internalbls.ListCollectorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListCollector", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.ListCollectorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCollector indicates an expected call of ListCollector.
func (mr *MockInterfaceMockRecorder) ListCollector(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCollector", reflect.TypeOf((*MockInterface)(nil).ListCollector), ctx, args, opt)
}

// ListCollectorGroup mocks base method.
func (m *MockInterface) ListCollectorGroup(ctx context.Context, args internalbls.ListCollectorGroupRequest, opt *bce.SignOption) (*internalbls.ListCollectorGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListCollectorGroup", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.ListCollectorGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCollectorGroup indicates an expected call of ListCollectorGroup.
func (mr *MockInterfaceMockRecorder) ListCollectorGroup(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCollectorGroup", reflect.TypeOf((*MockInterface)(nil).ListCollectorGroup), ctx, args, opt)
}

// ListGroupCollector mocks base method.
func (m *MockInterface) ListGroupCollector(ctx context.Context, args *internalbls.ListGroupCollectorRequest, opt *bce.SignOption) (*internalbls.ListGroupCollectorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListGroupCollector", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.ListGroupCollectorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListGroupCollector indicates an expected call of ListGroupCollector.
func (mr *MockInterfaceMockRecorder) ListGroupCollector(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListGroupCollector", reflect.TypeOf((*MockInterface)(nil).ListGroupCollector), ctx, args, opt)
}

// ListTask mocks base method.
func (m *MockInterface) ListTask(ctx context.Context, args internalbls.ListTaskRequest, opt *bce.SignOption) (*internalbls.ListTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTask", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.ListTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTask indicates an expected call of ListTask.
func (mr *MockInterfaceMockRecorder) ListTask(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTask", reflect.TypeOf((*MockInterface)(nil).ListTask), ctx, args, opt)
}

// ListTaskBindingHost mocks base method.
func (m *MockInterface) ListTaskBindingHost(ctx context.Context, args *internalbls.ListTaskBindingHostRequest, opt *bce.SignOption) (*internalbls.ListTaskBindingHostResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTaskBindingHost", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.ListTaskBindingHostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTaskBindingHost indicates an expected call of ListTaskBindingHost.
func (mr *MockInterfaceMockRecorder) ListTaskBindingHost(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTaskBindingHost", reflect.TypeOf((*MockInterface)(nil).ListTaskBindingHost), ctx, args, opt)
}

// ListToken mocks base method.
func (m *MockInterface) ListToken(ctx context.Context, args internalbls.ListTokenRequest, opt *bce.SignOption) (*internalbls.ListTokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListToken", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.ListTokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListToken indicates an expected call of ListToken.
func (mr *MockInterfaceMockRecorder) ListToken(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListToken", reflect.TypeOf((*MockInterface)(nil).ListToken), ctx, args, opt)
}

// SetDebug mocks base method.
func (m *MockInterface) SetDebug(debug bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", debug)
}

// SetDebug indicates an expected call of SetDebug.
func (mr *MockInterfaceMockRecorder) SetDebug(debug interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), debug)
}

// UnBindingTask mocks base method.
func (m *MockInterface) UnBindingTask(ctx context.Context, args *internalbls.UnBindingTaskRequest, opt *bce.SignOption) (*internalbls.UnBindingTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnBindingTask", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.UnBindingTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnBindingTask indicates an expected call of UnBindingTask.
func (mr *MockInterfaceMockRecorder) UnBindingTask(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnBindingTask", reflect.TypeOf((*MockInterface)(nil).UnBindingTask), ctx, args, opt)
}

// UpdateTask mocks base method.
func (m *MockInterface) UpdateTask(ctx context.Context, args *internalbls.UpdateTaskRequest, opt *bce.SignOption) (*internalbls.UpdateTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTask", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.UpdateTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTask indicates an expected call of UpdateTask.
func (mr *MockInterfaceMockRecorder) UpdateTask(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTask", reflect.TypeOf((*MockInterface)(nil).UpdateTask), ctx, args, opt)
}

// MockLogStoreInterface is a mock of LogStoreInterface interface.
type MockLogStoreInterface struct {
	ctrl     *gomock.Controller
	recorder *MockLogStoreInterfaceMockRecorder
}

// MockLogStoreInterfaceMockRecorder is the mock recorder for MockLogStoreInterface.
type MockLogStoreInterfaceMockRecorder struct {
	mock *MockLogStoreInterface
}

// NewMockLogStoreInterface creates a new mock instance.
func NewMockLogStoreInterface(ctrl *gomock.Controller) *MockLogStoreInterface {
	mock := &MockLogStoreInterface{ctrl: ctrl}
	mock.recorder = &MockLogStoreInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLogStoreInterface) EXPECT() *MockLogStoreInterfaceMockRecorder {
	return m.recorder
}

// CreateIndex mocks base method.
func (m *MockLogStoreInterface) CreateIndex(ctx context.Context, args *internalbls.CreateIndexRequest, opt *bce.SignOption) (*internalbls.CreateIndexResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateIndex", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.CreateIndexResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateIndex indicates an expected call of CreateIndex.
func (mr *MockLogStoreInterfaceMockRecorder) CreateIndex(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIndex", reflect.TypeOf((*MockLogStoreInterface)(nil).CreateIndex), ctx, args, opt)
}

// CreateLogStore mocks base method.
func (m *MockLogStoreInterface) CreateLogStore(ctx context.Context, args *internalbls.CreateLogStoreRequest, opt *bce.SignOption) (*internalbls.CreateLogStoreResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLogStore", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.CreateLogStoreResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLogStore indicates an expected call of CreateLogStore.
func (mr *MockLogStoreInterfaceMockRecorder) CreateLogStore(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLogStore", reflect.TypeOf((*MockLogStoreInterface)(nil).CreateLogStore), ctx, args, opt)
}

// DeleteLogStore mocks base method.
func (m *MockLogStoreInterface) DeleteLogStore(ctx context.Context, args *internalbls.DeleteLogStoreRequest, opt *bce.SignOption) (*internalbls.DeleteLogStoreResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteLogStore", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.DeleteLogStoreResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteLogStore indicates an expected call of DeleteLogStore.
func (mr *MockLogStoreInterfaceMockRecorder) DeleteLogStore(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLogStore", reflect.TypeOf((*MockLogStoreInterface)(nil).DeleteLogStore), ctx, args, opt)
}

// GetLogStore mocks base method.
func (m *MockLogStoreInterface) GetLogStore(ctx context.Context, args *internalbls.GetLogStoreRequest, opt *bce.SignOption) (*internalbls.GetLogStoreResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLogStore", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.GetLogStoreResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLogStore indicates an expected call of GetLogStore.
func (mr *MockLogStoreInterfaceMockRecorder) GetLogStore(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLogStore", reflect.TypeOf((*MockLogStoreInterface)(nil).GetLogStore), ctx, args, opt)
}

// MockTaskInterface is a mock of TaskInterface interface.
type MockTaskInterface struct {
	ctrl     *gomock.Controller
	recorder *MockTaskInterfaceMockRecorder
}

// MockTaskInterfaceMockRecorder is the mock recorder for MockTaskInterface.
type MockTaskInterfaceMockRecorder struct {
	mock *MockTaskInterface
}

// NewMockTaskInterface creates a new mock instance.
func NewMockTaskInterface(ctrl *gomock.Controller) *MockTaskInterface {
	mock := &MockTaskInterface{ctrl: ctrl}
	mock.recorder = &MockTaskInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTaskInterface) EXPECT() *MockTaskInterfaceMockRecorder {
	return m.recorder
}

// BindingTask mocks base method.
func (m *MockTaskInterface) BindingTask(ctx context.Context, args *internalbls.BindingTaskRequest, opt *bce.SignOption) (*internalbls.BindingTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindingTask", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.BindingTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BindingTask indicates an expected call of BindingTask.
func (mr *MockTaskInterfaceMockRecorder) BindingTask(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindingTask", reflect.TypeOf((*MockTaskInterface)(nil).BindingTask), ctx, args, opt)
}

// CreateTask mocks base method.
func (m *MockTaskInterface) CreateTask(ctx context.Context, args *internalbls.CreateTaskRequest, opt *bce.SignOption) (*internalbls.CreateTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTask", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.CreateTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTask indicates an expected call of CreateTask.
func (mr *MockTaskInterfaceMockRecorder) CreateTask(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTask", reflect.TypeOf((*MockTaskInterface)(nil).CreateTask), ctx, args, opt)
}

// DeleteTask mocks base method.
func (m *MockTaskInterface) DeleteTask(ctx context.Context, args *internalbls.DeleteTaskRequest, opt *bce.SignOption) (*internalbls.DeleteTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTask", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.DeleteTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTask indicates an expected call of DeleteTask.
func (mr *MockTaskInterfaceMockRecorder) DeleteTask(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTask", reflect.TypeOf((*MockTaskInterface)(nil).DeleteTask), ctx, args, opt)
}

// GetTask mocks base method.
func (m *MockTaskInterface) GetTask(ctx context.Context, args *internalbls.GetTaskRequest, opt *bce.SignOption) (*internalbls.GetTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTask", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.GetTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTask indicates an expected call of GetTask.
func (mr *MockTaskInterfaceMockRecorder) GetTask(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTask", reflect.TypeOf((*MockTaskInterface)(nil).GetTask), ctx, args, opt)
}

// ListTask mocks base method.
func (m *MockTaskInterface) ListTask(ctx context.Context, args internalbls.ListTaskRequest, opt *bce.SignOption) (*internalbls.ListTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTask", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.ListTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTask indicates an expected call of ListTask.
func (mr *MockTaskInterfaceMockRecorder) ListTask(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTask", reflect.TypeOf((*MockTaskInterface)(nil).ListTask), ctx, args, opt)
}

// ListTaskBindingHost mocks base method.
func (m *MockTaskInterface) ListTaskBindingHost(ctx context.Context, args *internalbls.ListTaskBindingHostRequest, opt *bce.SignOption) (*internalbls.ListTaskBindingHostResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTaskBindingHost", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.ListTaskBindingHostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTaskBindingHost indicates an expected call of ListTaskBindingHost.
func (mr *MockTaskInterfaceMockRecorder) ListTaskBindingHost(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTaskBindingHost", reflect.TypeOf((*MockTaskInterface)(nil).ListTaskBindingHost), ctx, args, opt)
}

// UnBindingTask mocks base method.
func (m *MockTaskInterface) UnBindingTask(ctx context.Context, args *internalbls.UnBindingTaskRequest, opt *bce.SignOption) (*internalbls.UnBindingTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnBindingTask", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.UnBindingTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnBindingTask indicates an expected call of UnBindingTask.
func (mr *MockTaskInterfaceMockRecorder) UnBindingTask(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnBindingTask", reflect.TypeOf((*MockTaskInterface)(nil).UnBindingTask), ctx, args, opt)
}

// UpdateTask mocks base method.
func (m *MockTaskInterface) UpdateTask(ctx context.Context, args *internalbls.UpdateTaskRequest, opt *bce.SignOption) (*internalbls.UpdateTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTask", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.UpdateTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTask indicates an expected call of UpdateTask.
func (mr *MockTaskInterfaceMockRecorder) UpdateTask(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTask", reflect.TypeOf((*MockTaskInterface)(nil).UpdateTask), ctx, args, opt)
}

// MockCollectorInterface is a mock of CollectorInterface interface.
type MockCollectorInterface struct {
	ctrl     *gomock.Controller
	recorder *MockCollectorInterfaceMockRecorder
}

// MockCollectorInterfaceMockRecorder is the mock recorder for MockCollectorInterface.
type MockCollectorInterfaceMockRecorder struct {
	mock *MockCollectorInterface
}

// NewMockCollectorInterface creates a new mock instance.
func NewMockCollectorInterface(ctrl *gomock.Controller) *MockCollectorInterface {
	mock := &MockCollectorInterface{ctrl: ctrl}
	mock.recorder = &MockCollectorInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCollectorInterface) EXPECT() *MockCollectorInterfaceMockRecorder {
	return m.recorder
}

// CreateCollectorGroup mocks base method.
func (m *MockCollectorInterface) CreateCollectorGroup(ctx context.Context, args *internalbls.CreateCollectorGroupRequest, opt *bce.SignOption) (*internalbls.CreateCollectorGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCollectorGroup", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.CreateCollectorGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCollectorGroup indicates an expected call of CreateCollectorGroup.
func (mr *MockCollectorInterfaceMockRecorder) CreateCollectorGroup(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCollectorGroup", reflect.TypeOf((*MockCollectorInterface)(nil).CreateCollectorGroup), ctx, args, opt)
}

// DeleteCollectorGroup mocks base method.
func (m *MockCollectorInterface) DeleteCollectorGroup(ctx context.Context, args *internalbls.DeleteCollectorGroupRequest, opt *bce.SignOption) (*internalbls.DeleteCollectorGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteCollectorGroup", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.DeleteCollectorGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteCollectorGroup indicates an expected call of DeleteCollectorGroup.
func (mr *MockCollectorInterfaceMockRecorder) DeleteCollectorGroup(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCollectorGroup", reflect.TypeOf((*MockCollectorInterface)(nil).DeleteCollectorGroup), ctx, args, opt)
}

// GetCollectorGroup mocks base method.
func (m *MockCollectorInterface) GetCollectorGroup(ctx context.Context, args *internalbls.GetCollectorGroupRequest, opt *bce.SignOption) (*internalbls.GetCollectorGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCollectorGroup", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.GetCollectorGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCollectorGroup indicates an expected call of GetCollectorGroup.
func (mr *MockCollectorInterfaceMockRecorder) GetCollectorGroup(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCollectorGroup", reflect.TypeOf((*MockCollectorInterface)(nil).GetCollectorGroup), ctx, args, opt)
}

// GroupAddItem mocks base method.
func (m *MockCollectorInterface) GroupAddItem(ctx context.Context, args *internalbls.GroupAddItemRequest, opt *bce.SignOption) (*internalbls.GroupAddItemResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GroupAddItem", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.GroupAddItemResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GroupAddItem indicates an expected call of GroupAddItem.
func (mr *MockCollectorInterfaceMockRecorder) GroupAddItem(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GroupAddItem", reflect.TypeOf((*MockCollectorInterface)(nil).GroupAddItem), ctx, args, opt)
}

// GroupDeleteItem mocks base method.
func (m *MockCollectorInterface) GroupDeleteItem(ctx context.Context, args *internalbls.GroupDeleteItemRequest, opt *bce.SignOption) (*internalbls.GroupDeleteItemResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GroupDeleteItem", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.GroupDeleteItemResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GroupDeleteItem indicates an expected call of GroupDeleteItem.
func (mr *MockCollectorInterfaceMockRecorder) GroupDeleteItem(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GroupDeleteItem", reflect.TypeOf((*MockCollectorInterface)(nil).GroupDeleteItem), ctx, args, opt)
}

// ListCollector mocks base method.
func (m *MockCollectorInterface) ListCollector(ctx context.Context, args internalbls.ListCollectorRequest, opt *bce.SignOption) (*internalbls.ListCollectorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListCollector", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.ListCollectorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCollector indicates an expected call of ListCollector.
func (mr *MockCollectorInterfaceMockRecorder) ListCollector(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCollector", reflect.TypeOf((*MockCollectorInterface)(nil).ListCollector), ctx, args, opt)
}

// ListCollectorGroup mocks base method.
func (m *MockCollectorInterface) ListCollectorGroup(ctx context.Context, args internalbls.ListCollectorGroupRequest, opt *bce.SignOption) (*internalbls.ListCollectorGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListCollectorGroup", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.ListCollectorGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCollectorGroup indicates an expected call of ListCollectorGroup.
func (mr *MockCollectorInterfaceMockRecorder) ListCollectorGroup(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCollectorGroup", reflect.TypeOf((*MockCollectorInterface)(nil).ListCollectorGroup), ctx, args, opt)
}

// ListGroupCollector mocks base method.
func (m *MockCollectorInterface) ListGroupCollector(ctx context.Context, args *internalbls.ListGroupCollectorRequest, opt *bce.SignOption) (*internalbls.ListGroupCollectorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListGroupCollector", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.ListGroupCollectorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListGroupCollector indicates an expected call of ListGroupCollector.
func (mr *MockCollectorInterfaceMockRecorder) ListGroupCollector(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListGroupCollector", reflect.TypeOf((*MockCollectorInterface)(nil).ListGroupCollector), ctx, args, opt)
}

// MockTokenInterface is a mock of TokenInterface interface.
type MockTokenInterface struct {
	ctrl     *gomock.Controller
	recorder *MockTokenInterfaceMockRecorder
}

// MockTokenInterfaceMockRecorder is the mock recorder for MockTokenInterface.
type MockTokenInterfaceMockRecorder struct {
	mock *MockTokenInterface
}

// NewMockTokenInterface creates a new mock instance.
func NewMockTokenInterface(ctrl *gomock.Controller) *MockTokenInterface {
	mock := &MockTokenInterface{ctrl: ctrl}
	mock.recorder = &MockTokenInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTokenInterface) EXPECT() *MockTokenInterfaceMockRecorder {
	return m.recorder
}

// CreateToken mocks base method.
func (m *MockTokenInterface) CreateToken(ctx context.Context, args *internalbls.CreateTokenRequest, opt *bce.SignOption) (*internalbls.CreateTokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateToken", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.CreateTokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateToken indicates an expected call of CreateToken.
func (mr *MockTokenInterfaceMockRecorder) CreateToken(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateToken", reflect.TypeOf((*MockTokenInterface)(nil).CreateToken), ctx, args, opt)
}

// DeleteToken mocks base method.
func (m *MockTokenInterface) DeleteToken(ctx context.Context, args *internalbls.DeleteTokenRequest, opt *bce.SignOption) (*internalbls.DeleteTokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteToken", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.DeleteTokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteToken indicates an expected call of DeleteToken.
func (mr *MockTokenInterfaceMockRecorder) DeleteToken(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteToken", reflect.TypeOf((*MockTokenInterface)(nil).DeleteToken), ctx, args, opt)
}

// ListToken mocks base method.
func (m *MockTokenInterface) ListToken(ctx context.Context, args internalbls.ListTokenRequest, opt *bce.SignOption) (*internalbls.ListTokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListToken", ctx, args, opt)
	ret0, _ := ret[0].(*internalbls.ListTokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListToken indicates an expected call of ListToken.
func (mr *MockTokenInterfaceMockRecorder) ListToken(ctx, args, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListToken", reflect.TypeOf((*MockTokenInterface)(nil).ListToken), ctx, args, opt)
}
