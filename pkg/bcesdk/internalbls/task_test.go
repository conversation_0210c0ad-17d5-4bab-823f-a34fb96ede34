package internalbls

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"reflect"
	"testing"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	v1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/internalbls/apis/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/retrypolicy"
)

func Test_Client_UnBindingTask(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		args   *UnBindingTaskRequest
		fields fields
		want   *UnBindingTaskResponse
	}{
		{
			name: "Delete Task",
			args: &UnBindingTaskRequest{TaskID: "0000001", Hosts: []v1.Host{{
				HostID:                  "01",
				HostName:                "",
				IP:                      "",
				UpdatedTime:             "",
				IsAvailableForNewConfig: false,
				Status:                  "",
			}}},
			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						return &http.Response{
							StatusCode: 200,
						}, nil
					}),
				}
			}(),
			want: &UnBindingTaskResponse{},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.UnBindingTask(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}

func Test_Client_BindingTask(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		args   *BindingTaskRequest
		fields fields
		want   *BindingTaskResponse
	}{
		{
			name: "Delete Task",
			args: &BindingTaskRequest{TaskID: "0000001", Hosts: []v1.Host{{
				HostID:                  "01",
				HostName:                "",
				IP:                      "",
				UpdatedTime:             "",
				IsAvailableForNewConfig: false,
				Status:                  "",
			}}},
			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						return &http.Response{
							StatusCode: 200,
						}, nil
					}),
				}
			}(),
			want: &BindingTaskResponse{},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.BindingTask(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}

func Test_Client_DeleteTask(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		args   *DeleteTaskRequest
		fields fields
		want   *DeleteTaskResponse
	}{
		{
			name: "Delete Task",
			args: &DeleteTaskRequest{TaskID: "0000001"},
			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						return &http.Response{
							StatusCode: 200,
						}, nil
					}),
				}
			}(),
			want: &DeleteTaskResponse{},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.DeleteTask(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}

func Test_Client_GetTask(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		args   *GetTaskRequest
		fields fields
		want   *GetTaskResponse
	}{
		{
			name: "Get Task",
			args: &GetTaskRequest{TaskID: "0000001"},
			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						r := GetTaskResponse{Task: v1.TaskDetailGroup{
							Status: v1.TaskDetail{
								ID:               "00000001",
								Name:             "zihua-test",
								Type:             "Log",
								Status:           "runnning",
								CreationDateTime: "2022/10/24",
								TaskVersion:      "1.15",
							},
							Config: v1.Config{
								SrcConfig:  v1.SrcConfig{},
								DestConfig: v1.DestConfig{},
							},
						}}
						header := http.Header{}
						header.Set("Content-Type", "application/json")

						body, err := json.Marshal(r)
						if err != nil {
							return nil, err
						}

						return &http.Response{
							StatusCode: 200,
							Body:       io.NopCloser(bytes.NewReader(body)),
							Header:     header,
						}, nil
					}),
				}
			}(),
			want: &GetTaskResponse{Task: v1.TaskDetailGroup{
				Status: v1.TaskDetail{
					ID:               "00000001",
					Name:             "zihua-test",
					Type:             "Log",
					Status:           "runnning",
					CreationDateTime: "2022/10/24",
					TaskVersion:      "1.15",
				},
				Config: v1.Config{
					SrcConfig:  v1.SrcConfig{},
					DestConfig: v1.DestConfig{},
				},
			}},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.GetTask(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}

func Test_Client_ListTaskBindingHost(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		args   *ListTaskBindingHostRequest
		fields fields
		want   *ListTaskBindingHostResponse
	}{
		{
			name: "List Task",
			args: &ListTaskBindingHostRequest{
				TaskID: "test",
				Query:  nil,
			},
			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						r := ListTaskBindingHostResponse{
							TotalSize: 2,
							Offset:    0,
							Size:      0,
						}
						header := http.Header{}
						header.Set("Content-Type", "application/json")

						body, err := json.Marshal(r)
						if err != nil {
							return nil, err
						}

						return &http.Response{
							StatusCode: 200,
							Body:       io.NopCloser(bytes.NewReader(body)),
							Header:     header,
						}, nil
					}),
				}
			}(),
			want: &ListTaskBindingHostResponse{
				TotalSize: 2,
				Offset:    0,
				Size:      0,
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.ListTaskBindingHost(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}

func Test_Client_ListTask(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		args   ListTaskRequest
		fields fields
		want   *ListTaskResponse
	}{
		{
			name: "List Task",
			args: ListTaskRequest{},
			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						r := ListTaskResponse{
							TotalSize: 2,
							Offset:    0,
							Size:      0,
							Tasks: []v1.TaskDetail{
								{
									ID:               "00000001",
									Name:             "zihua-test1",
									Type:             "debug",
									Status:           "running",
									CreationDateTime: "2022/10/24",
									TaskVersion:      "1.15",
								},
								{
									ID:               "00000002",
									Name:             "zihua-test2",
									Type:             "debug",
									Status:           "running",
									CreationDateTime: "2022/10/24",
									TaskVersion:      "1.15",
								},
							},
						}
						header := http.Header{}
						header.Set("Content-Type", "application/json")

						body, err := json.Marshal(r)
						if err != nil {
							return nil, err
						}

						return &http.Response{
							StatusCode: 200,
							Body:       io.NopCloser(bytes.NewReader(body)),
							Header:     header,
						}, nil
					}),
				}
			}(),
			want: &ListTaskResponse{
				TotalSize: 2,
				Offset:    0,
				Size:      0,
				Tasks: []v1.TaskDetail{
					{
						ID:               "00000001",
						Name:             "zihua-test1",
						Type:             "debug",
						Status:           "running",
						CreationDateTime: "2022/10/24",
						TaskVersion:      "1.15",
					},
					{
						ID:               "00000002",
						Name:             "zihua-test2",
						Type:             "debug",
						Status:           "running",
						CreationDateTime: "2022/10/24",
						TaskVersion:      "1.15",
					},
				},
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.ListTask(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}

func Test_Client_CreateTask(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		args   *CreateTaskRequest
		fields fields
		want   *CreateTaskResponse
	}{
		{
			name: "Create Task",
			args: &CreateTaskRequest{
				Name: "zihua-test",
				Config: v1.Config{
					SrcConfig: v1.SrcConfig{
						SrcType:      "container",
						LogType:      "stdout",
						TTL:          3,
						UseMultiline: false,
						ProcessType:  "none",
						LogTime:      "logTime",
						DateFormat:   "dateFormat",
						LabelWhite:   []v1.Label{{Key: "io.kubernetes.container.name", Value: "*"}, {Key: "io.kubernetes.container.namespace", Value: "*"}},
						EnvWhite:     []v1.Label{{Key: "app", Value: "control-plane"}},
					},
					DestConfig: v1.DestConfig{
						LogStore:  "BCI-log-gz",
						DestType:  "BLS",
						RateLimit: 10,
					},
				},
				Tags: []v1.Tag{{TagKey: "clusterID", TagValue: "cce-nbdzyfsb"}},
			},

			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						r := CreateTaskResponse{TaskID: "12345678"}
						header := http.Header{}
						header.Set("Content-Type", "application/json")

						body, err := json.Marshal(r)
						if err != nil {
							return nil, err
						}

						return &http.Response{
							StatusCode: 200,
							Body:       io.NopCloser(bytes.NewReader(body)),
							Header:     header,
						}, nil
					}),
				}
			}(),
			want: &CreateTaskResponse{TaskID: "12345678"},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.CreateTask(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}

func Test_Client_UpdateTask(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		args   *UpdateTaskRequest
		fields fields
		want   *UpdateTaskResponse
	}{
		{
			name: "Update Task",
			args: &UpdateTaskRequest{
				TaskID: "01",
				Name:   "zihua-test",
				Config: v1.Config{
					SrcConfig: v1.SrcConfig{
						SrcType:      "container",
						LogType:      "stdout",
						TTL:          3,
						UseMultiline: false,
						ProcessType:  "none",
						LogTime:      "logTime",
						DateFormat:   "dateFormat",
						LabelWhite:   []v1.Label{{Key: "io.kubernetes.container.name", Value: "*"}, {Key: "io.kubernetes.container.namespace", Value: "*"}},
						EnvWhite:     []v1.Label{{Key: "app", Value: "control-plane"}},
					},
					DestConfig: v1.DestConfig{
						LogStore:  "BCI-log-gz",
						DestType:  "BLS",
						RateLimit: 10,
					},
				},
			},

			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						return &http.Response{
							StatusCode: 200,
						}, nil
					}),
				}
			}(),
			want: &UpdateTaskResponse{},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.UpdateTask(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}
