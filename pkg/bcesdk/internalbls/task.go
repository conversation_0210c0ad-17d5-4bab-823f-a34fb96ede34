package internalbls

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func (c *Client) UpdateTask(ctx context.Context, args *UpdateTaskRequest, opt *bce.SignOption) (*UpdateTaskResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL(fmt.Sprintf("task/%s", args.TaskID), nil), bytes.<PERSON>uffer(postContent))
	if err != nil {
		return nil, err
	}

	if _, err := c.SendRequest(ctx, req, opt); err != nil {
		return nil, err
	}

	return &UpdateTaskResponse{}, nil
}

func (c *Client) CreateTask(ctx context.Context, args *CreateTaskRequest, opt *bce.SignOption) (*CreateTaskResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("task", nil), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r CreateTaskResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}

	return &r, nil
}

func (c *Client) GetTask(ctx context.Context, args *GetTaskRequest, opt *bce.SignOption) (*GetTaskResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("task/%s", args.TaskID), nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r GetTaskResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}

	return &r, nil
}

func (c *Client) DeleteTask(ctx context.Context, args *DeleteTaskRequest, opt *bce.SignOption) (*DeleteTaskResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	req, err := bce.NewRequest("POST", c.GetURL(fmt.Sprintf("task/%s/stop", args.TaskID), nil), nil)
	if err != nil {
		return nil, err
	}

	if _, err := c.SendRequest(ctx, req, opt); err != nil {
		return nil, err
	}

	return &DeleteTaskResponse{}, nil
}

func (c *Client) ListTaskBindingHost(ctx context.Context, args *ListTaskBindingHostRequest, opt *bce.SignOption) (*ListTaskBindingHostResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("task/%v/host", args.TaskID), args.Query), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListTaskBindingHostResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}
	return &r, nil
}

func (c *Client) ListTask(ctx context.Context, args ListTaskRequest, opt *bce.SignOption) (*ListTaskResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("task"), args), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListTaskResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}
	return &r, nil
}

func (c *Client) BindingTask(ctx context.Context, args *BindingTaskRequest, opt *bce.SignOption) (*BindingTaskResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL(fmt.Sprintf("task/%s/host", args.TaskID), nil), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	if _, err := c.SendRequest(ctx, req, opt); err != nil {
		return nil, err
	}

	return &BindingTaskResponse{}, nil
}

func (c *Client) UnBindingTask(ctx context.Context, args *UnBindingTaskRequest, opt *bce.SignOption) (*UnBindingTaskResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL(fmt.Sprintf("task/%s/host/delete", args.TaskID), nil), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	if _, err := c.SendRequest(ctx, req, opt); err != nil {
		return nil, err
	}

	return &UnBindingTaskResponse{}, nil
}
