// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
// Client 实现 internalbls.Interface

package internalbls

import (
	"context"
	"encoding/json"
	"net/http"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

var _ Interface = &Client{}

// bls internal 各个地域 endpoint
//	bj:  bls.bj.baidubce.com:8085
//	gz:  bls.gz.baidubce.com:8085
//	su:  bls.su.baidubce.com:8085
//	nj:  bls.nj.baidubce.com:8085
//	hkg: bls.hkg.baidubce.com:8085
//	fwh: bls.fwh.baidubce.com:8085
//	bd:  bls.bd.baidubce.com:8085

// Client 实现 internalbls.Interface
type Client struct {
	*bce.Client
	openClient *bce.Client
}

func (c *Client) ClientName() string {
	// TODO implement me
	return ""
}

func (c *Client) Push(ctx context.Context, args *SendLogRecordRequest, opt *bce.SignOption) error {
	_, err := c.SendLogRecord(ctx, args, opt)
	if err != nil {
		return err
	}
	return nil
}

// NewClient client
func NewClient(config *bce.Config, oc ...*bce.Config) *Client {
	var openClient *bce.Client
	if len(oc) > 0 {
		openClient = bce.NewClient(oc[0])
	}

	return &Client{
		Client:     bce.NewClient(config),
		openClient: openClient,
	}
}

func (c *Client) SetOpenClient(client *bce.Client) {
	c.openClient = client
}

func (c *Client) SetHttpClient(client *http.Client) {
	if c.Client != nil {
		c.Client.SetHttpClient(client)
	}

	if c.openClient != nil {
		c.openClient.SetHttpClient(client)
	}
}

// SetDebug 是否开启 debug 模式
func (c *Client) SetDebug(debug bool) {
	if c.Client != nil {
		c.Client.SetDebug(debug)
	}

	if c.openClient != nil {
		c.openClient.SetDebug(debug)
	}
}

// GetOpenAPIURL generates the full URL of http request for Baidu Cloud BOS API.
func (c *Client) GetOpenAPIURL(path string, params map[string]string) string {
	host := c.openClient.Endpoint
	uriPath := path
	return c.Client.GetURL(host, uriPath, params)
}

// GetURL generates the full URL of http request for Baidu Cloud BOS API.
func (c *Client) GetURL(path string, params map[string]string) string {
	host := c.Endpoint
	uriPath := path
	return c.Client.GetURL(host, uriPath, params)
}

func (c *Client) Activate(ctx context.Context, opt *bce.SignOption) error {
	req, err := bce.NewRequest("POST", c.GetURL("activate", nil), nil)
	if err != nil {
		return err
	}

	if _, err := c.SendRequest(ctx, req, opt); err != nil {
		return err
	}

	return nil
}

func (c *Client) IsActivate(ctx context.Context, opt *bce.SignOption) (bool, error) {
	req, err := bce.NewRequest("GET", c.GetURL("activate", nil), nil)
	if err != nil {
		return false, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return false, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return false, err
	}

	var r = struct {
		IsActivated bool `json:"isActivated"`
	}{}

	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return false, err
	}
	return r.IsActivated, nil
}
