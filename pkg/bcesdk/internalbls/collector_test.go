package internalbls

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"reflect"
	"testing"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	v1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/internalbls/apis/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/retrypolicy"
)

func Test_Client_DeleteCollectorGroup(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		args   *DeleteCollectorGroupRequest
		fields fields
		want   *DeleteCollectorGroupResponse
	}{
		{
			name: "Delete CollectorGroup",
			args: &DeleteCollectorGroupRequest{CollectorGroupID: "0000001"},
			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						return &http.Response{
							StatusCode: 200,
						}, nil
					}),
				}
			}(),
			want: &DeleteCollectorGroupResponse{},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.DeleteCollectorGroup(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}

func Test_Client_GetCollectorGroup(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		args   *GetCollectorGroupRequest
		fields fields
		want   *GetCollectorGroupResponse
	}{
		{
			name: "Get CollectorGroup",
			args: &GetCollectorGroupRequest{CollectorGroupID: "0000001"},
			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						r := GetCollectorGroupResponse{
							ID:               "00000001",
							Name:             "zihua-test-group",
							CreationDateTime: time.Time{},
							Description:      "zihua-test",
							AgentCount:       0,
						}
						header := http.Header{}
						header.Set("Content-Type", "application/json")

						body, err := json.Marshal(r)
						if err != nil {
							return nil, err
						}

						return &http.Response{
							StatusCode: 200,
							Body:       io.NopCloser(bytes.NewReader(body)),
							Header:     header,
						}, nil
					}),
				}
			}(),
			want: &GetCollectorGroupResponse{
				ID:               "00000001",
				Name:             "zihua-test-group",
				CreationDateTime: time.Time{},
				Description:      "zihua-test",
				AgentCount:       0,
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.GetCollectorGroup(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}

func Test_Client_ListGroupCollector(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		args   *ListGroupCollectorRequest
		fields fields
		want   *ListGroupCollectorResponse
	}{
		{
			name: "List CollectorGroup",
			args: &ListGroupCollectorRequest{},
			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						r := ListCollectorResponse{
							TotalSize: 1,
							Offset:    0,
							Size:      0,

							Hosts: []v1.Host{{
								HostID:                  "0001",
								HostName:                "",
								IP:                      "",
								UpdatedTime:             "",
								IsAvailableForNewConfig: false,
								Status:                  "",
							}},
						}
						header := http.Header{}
						header.Set("Content-Type", "application/json")

						body, err := json.Marshal(r)
						if err != nil {
							return nil, err
						}

						return &http.Response{
							StatusCode: 200,
							Body:       io.NopCloser(bytes.NewReader(body)),
							Header:     header,
						}, nil
					}),
				}
			}(),
			want: &ListGroupCollectorResponse{
				TotalSize: 1,
				Offset:    0,
				Size:      0,

				Hosts: []v1.Host{{
					HostID:                  "0001",
					HostName:                "",
					IP:                      "",
					UpdatedTime:             "",
					IsAvailableForNewConfig: false,
					Status:                  "",
				}},
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.ListGroupCollector(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}

func Test_Client_ListCollector(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		args   ListCollectorRequest
		fields fields
		want   *ListCollectorResponse
	}{
		{
			name: "List CollectorGroup",
			args: ListCollectorRequest{},
			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						r := ListCollectorResponse{
							TotalSize: 1,
							Offset:    0,
							Size:      0,

							Hosts: []v1.Host{{
								HostID:                  "0001",
								HostName:                "",
								IP:                      "",
								UpdatedTime:             "",
								IsAvailableForNewConfig: false,
								Status:                  "",
							}},
						}
						header := http.Header{}
						header.Set("Content-Type", "application/json")

						body, err := json.Marshal(r)
						if err != nil {
							return nil, err
						}

						return &http.Response{
							StatusCode: 200,
							Body:       io.NopCloser(bytes.NewReader(body)),
							Header:     header,
						}, nil
					}),
				}
			}(),
			want: &ListCollectorResponse{
				TotalSize: 1,
				Offset:    0,
				Size:      0,

				Hosts: []v1.Host{{
					HostID:                  "0001",
					HostName:                "",
					IP:                      "",
					UpdatedTime:             "",
					IsAvailableForNewConfig: false,
					Status:                  "",
				}},
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.ListCollector(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}

func Test_Client_ListCollectorGroup(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		args   ListCollectorGroupRequest
		fields fields
		want   *ListCollectorGroupResponse
	}{
		{
			name: "List CollectorGroup",
			args: ListCollectorGroupRequest{},
			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						r := ListCollectorGroupResponse{
							TotalSize: 1,
							Offset:    0,
							Size:      0,

							Groups: []v1.CollectorGroup{{
								ID:               "00000001",
								Name:             "zihua-group-test1",
								CreationDateTime: time.Time{},
								Description:      "zihua-test",
								AgentCount:       0,
							}},
						}
						header := http.Header{}
						header.Set("Content-Type", "application/json")

						body, err := json.Marshal(r)
						if err != nil {
							return nil, err
						}

						return &http.Response{
							StatusCode: 200,
							Body:       io.NopCloser(bytes.NewReader(body)),
							Header:     header,
						}, nil
					}),
				}
			}(),
			want: &ListCollectorGroupResponse{
				TotalSize: 1,
				Offset:    0,
				Size:      0,

				Groups: []v1.CollectorGroup{{
					ID:               "00000001",
					Name:             "zihua-group-test1",
					CreationDateTime: time.Time{},
					Description:      "zihua-test",
					AgentCount:       0,
				}},
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.ListCollectorGroup(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}

func Test_Client_CreateCollectorGroup(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		args   *CreateCollectorGroupRequest
		fields fields
		want   *CreateCollectorGroupResponse
	}{
		{
			name: "Create CollectorGroup",
			args: &CreateCollectorGroupRequest{
				Name:        "zihua-test-group1",
				Description: "zihua-test",
				Hosts:       nil,
			},

			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						r := CreateCollectorGroupResponse{CollectorGroupID: "00000001"}
						header := http.Header{}
						header.Set("Content-Type", "application/json")

						body, err := json.Marshal(r)
						if err != nil {
							return nil, err
						}

						return &http.Response{
							StatusCode: 200,
							Body:       io.NopCloser(bytes.NewReader(body)),
							Header:     header,
						}, nil
					}),
				}
			}(),
			want: &CreateCollectorGroupResponse{CollectorGroupID: "00000001"},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.CreateCollectorGroup(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}

func Test_Client_GroupAddItem(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		args   *GroupAddItemRequest
		fields fields
		want   *GroupAddItemResponse
	}{
		{
			name: "Group Add Item",
			args: &GroupAddItemRequest{
				CollectorGroupID: "00000001",
				AgentList: struct {
					Hosts []v1.Host `json:"hosts"`
				}{Hosts: []v1.Host{{HostID: "01"}}},
			},

			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						return &http.Response{
							StatusCode: 200,
						}, nil
					}),
				}
			}(),
			want: &GroupAddItemResponse{},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.GroupAddItem(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}

func Test_Client_GroupDeleteItem(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		args   *GroupDeleteItemRequest
		fields fields
		want   *GroupDeleteItemResponse
	}{
		{
			name: "Group Add Item",
			args: &GroupDeleteItemRequest{
				CollectorGroupID: "00000001",
				AgentList: struct {
					Hosts []v1.Host `json:"hosts"`
				}{Hosts: []v1.Host{{HostID: "01"}}},
			},

			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						return &http.Response{
							StatusCode: 200,
						}, nil
					}),
				}
			}(),
			want: &GroupDeleteItemResponse{},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.GroupDeleteItem(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}
