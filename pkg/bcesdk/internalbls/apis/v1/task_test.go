package v1

import "testing"

func Test_Task(t *testing.T) {
	tg := TaskDetailGroup{
		Status: TaskDetail{
			ID:               "",
			Name:             "",
			Type:             "",
			Status:           "",
			CreationDateTime: "",
			TaskVersion:      "",
		},
		Config: Config{
			SrcConfig: SrcConfig{
				TaskType:       "",
				SrcType:        "",
				SrcDir:         "",
				MatchedPattern: "",
				IgnorePattern:  "",
				TimeFormat:     "",
				TTL:            0,
				UseMultiline:   false,
				MultilineRegex: "",
				RecursiveDir:   false,
				ProcessType:    "",
				ProcessConfig: ProcessConfig{
					Regex:            "",
					Separator:        "",
					Quote:            "",
					SampleLog:        "",
					Keys:             "",
					DataType:         "",
					DiscardOnFailure: false,
					KeepOriginal:     false,
				},
				LogTime:      "",
				TimestampKey: "",
				DateFormat:   "",
				FilterExpr:   "",
				LabelWhite:   nil,
				LabelBlack:   nil,
				EnvWhite:     nil,
				EnvBlack:     nil,
				Units:        nil,
			},
			DestConfig: DestConfig{
				CodeC:               "",
				DestDir:             "",
				DestType:            "",
				LogAggrByHost:       "",
				LogAggrByTime:       "",
				LogAggrByTimeFormat: "",
				NeedNotify:          false,
				RateLimit:           0,
			},
		},
	}

	host := Host{
		HostID:                  "",
		HostName:                "",
		IP:                      "",
		UpdatedTime:             "",
		IsAvailableForNewConfig: false,
		Status:                  "",
	}

	tag := Tag{
		TagValue: "",
		TagKey:   "",
	}

	_, _, _ = tg, host, tag
}
