package internalbls

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

func (c *Client) DeleteLogStore(ctx context.Context, args *DeleteLogStoreRequest, opt *bce.SignOption) (*DeleteLogStoreResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	req, err := bce.NewRequest("DELETE", c.GetOpenAPIURL(fmt.Sprintf("v1/logstore/%s", args.LogStore), nil), nil)
	if err != nil {
		return nil, err
	}

	if _, err := c.openClient.SendRequest(ctx, req, opt); err != nil {
		return nil, err
	}

	return &DeleteLogStoreResponse{}, nil
}

func (c *Client) CreateLogStore(ctx context.Context, args *CreateLogStoreRequest, opt *bce.SignOption) (*CreateLogStoreResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}
	logger.Infof(ctx, "CreateLogStore args: %v", args)
	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}
	logger.Infof(ctx, "CreateLogStore postContent: %s", string(postContent))
	req, err := bce.NewRequest("POST", c.GetOpenAPIURL("v1/logstore", nil), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	logger.Infof(ctx, "send request")
	if _, err := c.SendRequest(ctx, req, opt); err != nil {
		return nil, err
	}

	return &CreateLogStoreResponse{}, nil
}

func (c *Client) CreateAuditLogStore(ctx context.Context, args *CreateLogStoreRequest, opt *bce.SignOption) (*CreateLogStoreResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}
	logger.Infof(ctx, "CreateLogStore args: %v", args)
	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetOpenAPIURL("v1/logstore", nil), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	logger.Infof(ctx, "send request")
	if _, err := c.SendRequest(ctx, req, opt); err != nil {
		return nil, err
	}

	return &CreateLogStoreResponse{}, nil
}

func (c *Client) SendLogRecord(ctx context.Context, args *SendLogRecordRequest, opt *bce.SignOption) (*SendLogRecordResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	logger.Infof(ctx, "CreateLogStore postContent: %s", string(postContent))
	req, err := bce.NewRequest("POST",
		c.GetOpenAPIURL(fmt.Sprintf("v1/logstore/%s/logrecord",
			decodeClusterName("test")), nil), bytes.NewBuffer(postContent))
	if err != nil {
		logger.Errorf(ctx, "create request error: %v", err)
	}
	logger.Infof(ctx, "send request")
	if _, err := c.SendRequest(ctx, req, opt); err != nil {
		logger.Errorf(ctx, "send request error: %v", err)
	}
	logger.Infof(ctx, "send request end")

	if err != nil {
		return nil, err
	}
	return &SendLogRecordResponse{}, nil
}

func (c *Client) CreateIndex(ctx context.Context, args *CreateIndexRequest, opt *bce.SignOption) (*CreateIndexResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	logger.Infof(ctx, "CreateLogStore postContent: %s", string(postContent))
	req, err := bce.NewRequestWithTimeout("POST",
		c.GetOpenAPIURL(fmt.Sprintf("v1/logstore/%s/index", args.LogStore), nil),
		600, bytes.NewBuffer(postContent))

	if err != nil {
		return nil, err
	}

	if _, err := c.SendRequest(ctx, req, opt); err != nil {
		return nil, err
	}

	return &CreateIndexResponse{}, nil
}

func (c *Client) GetLogStore(ctx context.Context, args *GetLogStoreRequest, opt *bce.SignOption) (*GetLogStoreResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	req, err := bce.NewRequest("GET", c.GetOpenAPIURL(fmt.Sprintf("v1/logstore/%s", args.LogStore), nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.openClient.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r GetLogStoreResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}

	return &r, nil
}

func (c *Client) CreateLogStoreWithSts(ctx context.Context, args *CreateLogStoreRequest, opt *bce.SignOption) (*CreateLogStoreResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v1/logstore", nil), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	_, err = c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

func decodeClusterName(data string) string {
	params := strings.Split(data, "-")
	return fmt.Sprintf("%s-%s", params[2], params[3])
}
