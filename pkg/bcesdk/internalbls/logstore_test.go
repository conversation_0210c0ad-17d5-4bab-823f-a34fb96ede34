package internalbls

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"reflect"
	"testing"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/retrypolicy"
)

func TestClient_DeleteLogStore(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
		cfg1       *bce.Config
	}

	cases := []struct {
		name   string
		args   *DeleteLogStoreRequest
		fields fields
		want   *DeleteLogStoreResponse
	}{
		{
			name: "test1",
			args: &DeleteLogStoreRequest{
				LogStore: "test-log-store",
			},
			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					cfg1: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls-log.gz.baidubce.com",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						return &http.Response{
							StatusCode: 200,
						}, nil
					}),
				}
			}(),
			want: &DeleteLogStoreResponse{},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg, c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.DeleteLogStore(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}

func TestClient_CreateLogStore(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
		cfg1       *bce.Config
	}

	cases := []struct {
		name   string
		args   *CreateLogStoreRequest
		fields fields
		want   *CreateLogStoreResponse
	}{
		{
			name: "test1",
			args: &CreateLogStoreRequest{
				LogStore:  "test-log-store",
				Retention: 1,
			},
			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					cfg1: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls-log.gz.baidubce.com",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						return &http.Response{
							StatusCode: 200,
						}, nil
					}),
				}
			}(),
			want: &CreateLogStoreResponse{},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg, c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.CreateLogStore(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}

func TestClient_GetLogStore(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
		cfg1       *bce.Config
	}

	cases := []struct {
		name   string
		args   *GetLogStoreRequest
		fields fields
		want   *GetLogStoreResponse
	}{
		{
			name: "test1",
			args: &GetLogStoreRequest{LogStore: "test-log-store"},
			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					cfg1: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls-log.gz.baidubce.com",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						r := GetLogStoreResponse{
							CreationDateTime: "",
							LastModifiedTime: "",
							LogStoreName:     "test-log-store",
							Retention:        7,
						}
						header := http.Header{}
						header.Set("Content-Type", "application/json")

						body, err := json.Marshal(r)
						if err != nil {
							return nil, err
						}
						return &http.Response{
							StatusCode: 200,
							Body:       io.NopCloser(bytes.NewReader(body)),
						}, nil
					}),
				}
			}(),
			want: &GetLogStoreResponse{
				CreationDateTime: "",
				LastModifiedTime: "",
				LogStoreName:     "test-log-store",
				Retention:        7,
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg, c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.GetLogStore(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}
