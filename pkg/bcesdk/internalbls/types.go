// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
// CCE V2 版本 GO SDK, Interface 定义

package internalbls

import (
	"context"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bls/api"
	internalbls "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/internalbls/apis/v1"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock -source=./types.go

// Interface 定义 InternalBLS SDK
// 新增 Interface 后注意更新 doc.go !!!!!!!
type Interface interface {
	SetDebug(debug bool)
	Activate(ctx context.Context, opt *bce.SignOption) error
	IsActivate(ctx context.Context, opt *bce.SignOption) (bool, error)
	LogStoreInterface
	TaskInterface
	CollectorInterface
	TokenInterface
}

type LogStoreInterface interface {
	CreateLogStore(ctx context.Context, args *CreateLogStoreRequest, opt *bce.SignOption) (*CreateLogStoreResponse, error)
	GetLogStore(ctx context.Context, args *GetLogStoreRequest, opt *bce.SignOption) (*GetLogStoreResponse, error)
	DeleteLogStore(ctx context.Context, args *DeleteLogStoreRequest, opt *bce.SignOption) (*DeleteLogStoreResponse, error)
	CreateIndex(ctx context.Context, args *CreateIndexRequest, opt *bce.SignOption) (*CreateIndexResponse, error)
}

type DeleteLogStoreRequest struct {
	LogStore string `json:"logStoreName"`
}

type DeleteLogStoreResponse struct {
}

type CreateLogStoreRequest struct {
	LogStore  string `json:"logStoreName"`
	Retention int    `json:"retention"`
}

type CreateLogStoreResponse struct {
}

type CreateIndexRequest struct {
	LogStore string `json:"logStoreName"`
	FullText bool   `json:"fulltext"`
	Fields   any    `json:"fields"`
}

type SendLogRecordRequest struct {
	LogStreamName string          `json:"logStreamName"`
	Type          string          `json:"type"`
	LogRecords    []api.LogRecord `json:"logRecords"`
}

type SendLogRecordResponse struct {
}

type CreateIndexResponse struct {
}

type GetLogStoreRequest struct {
	LogStore string
}

type GetLogStoreResponse struct {
	CreationDateTime string `json:"creationDateTime"`
	LastModifiedTime string `json:"lastModifiedTime"`
	LogStoreName     string `json:"logStoreName"`
	Retention        int    `json:"retention"`
}

type TaskInterface interface {
	CreateTask(ctx context.Context, args *CreateTaskRequest, opt *bce.SignOption) (*CreateTaskResponse, error)
	GetTask(ctx context.Context, args *GetTaskRequest, opt *bce.SignOption) (*GetTaskResponse, error)
	UpdateTask(ctx context.Context, args *UpdateTaskRequest, opt *bce.SignOption) (*UpdateTaskResponse, error)
	DeleteTask(ctx context.Context, args *DeleteTaskRequest, opt *bce.SignOption) (*DeleteTaskResponse, error)
	ListTask(ctx context.Context, args ListTaskRequest, opt *bce.SignOption) (*ListTaskResponse, error)
	ListTaskBindingHost(ctx context.Context, args *ListTaskBindingHostRequest, opt *bce.SignOption) (*ListTaskBindingHostResponse, error)
	BindingTask(ctx context.Context, args *BindingTaskRequest, opt *bce.SignOption) (*BindingTaskResponse, error)
	UnBindingTask(ctx context.Context, args *UnBindingTaskRequest, opt *bce.SignOption) (*UnBindingTaskResponse, error)
}

type CreateTaskRequest struct {
	Name   string             `json:"name"`
	Config internalbls.Config `json:"config"`
	Hosts  []internalbls.Host `json:"hosts"`
	Tags   []internalbls.Tag  `json:"tags"`
}

type CreateTaskResponse struct {
	TaskID string `json:"taskId"`
}

type GetTaskRequest struct {
	TaskID string `json:"taskId"`
}

type GetTaskResponse struct {
	Task internalbls.TaskDetailGroup `json:"task"`
}

type UpdateTaskRequest struct {
	TaskID string             `json:"taskId,omitempty"`
	Name   string             `json:"name,omitempty"`
	Config internalbls.Config `json:"config,omitempty"`
	Tags   []internalbls.Tag  `json:"tags,omitempty"`
}

type UpdateTaskResponse struct{}

type DeleteTaskRequest struct {
	TaskID string `json:"taskId"`
}

type DeleteTaskResponse struct{}

type ListTaskBindingHostRequest struct {
	TaskID string `json:"taskId"`
	Query  map[string]string
}

type ListTaskBindingHostResponse struct {
	TotalSize int                `json:"totalSize"`
	Offset    int                `json:"offset"`
	Size      int                `json:"size"`
	Hosts     []internalbls.Host `json:"hosts"`
}

type ListTaskRequest map[string]string

type ListTaskResponse struct {
	TotalSize int                      `json:"totalSize"`
	Offset    int                      `json:"offset"`
	Size      int                      `json:"size"`
	Tasks     []internalbls.TaskDetail `json:"tasks"`
}

type BindingTaskRequest struct {
	TaskID string             `json:"taskId"`
	Hosts  []internalbls.Host `json:"hosts"`
}

type BindingTaskResponse struct{}

type UnBindingTaskRequest struct {
	TaskID string             `json:"taskId"`
	Hosts  []internalbls.Host `json:"hosts"`
}

type UnBindingTaskResponse struct{}

type CollectorInterface interface {
	CreateCollectorGroup(ctx context.Context, args *CreateCollectorGroupRequest, opt *bce.SignOption) (*CreateCollectorGroupResponse, error)
	GetCollectorGroup(ctx context.Context, args *GetCollectorGroupRequest, opt *bce.SignOption) (*GetCollectorGroupResponse, error)
	DeleteCollectorGroup(ctx context.Context, args *DeleteCollectorGroupRequest, opt *bce.SignOption) (*DeleteCollectorGroupResponse, error)
	ListCollectorGroup(ctx context.Context, args ListCollectorGroupRequest, opt *bce.SignOption) (*ListCollectorGroupResponse, error)
	GroupAddItem(ctx context.Context, args *GroupAddItemRequest, opt *bce.SignOption) (*GroupAddItemResponse, error)
	GroupDeleteItem(ctx context.Context, args *GroupDeleteItemRequest, opt *bce.SignOption) (*GroupDeleteItemResponse, error)
	ListCollector(ctx context.Context, args ListCollectorRequest, opt *bce.SignOption) (*ListCollectorResponse, error)
	ListGroupCollector(ctx context.Context, args *ListGroupCollectorRequest, opt *bce.SignOption) (*ListGroupCollectorResponse, error)
}

type CreateCollectorGroupRequest struct {
	Name        string             `json:"name"`
	Description string             `json:"description"`
	Hosts       []internalbls.Host `json:"hosts"`
}

type CreateCollectorGroupResponse struct {
	CollectorGroupID string `json:"instanceGroupId"`
}

type GetCollectorGroupRequest struct {
	CollectorGroupID string `json:"groupId"`
}

type GetCollectorGroupResponse internalbls.CollectorGroup

type DeleteCollectorGroupRequest struct {
	CollectorGroupID string `json:"groupId"`
}

type DeleteCollectorGroupResponse struct{}

type ListCollectorGroupRequest map[string]string

type ListCollectorGroupResponse struct {
	TotalSize int                          `json:"totalSize"`
	Offset    int                          `json:"offset"`
	Size      int                          `json:"size"`
	Groups    []internalbls.CollectorGroup `json:"groups"`
}

type GroupAddItemRequest struct {
	CollectorGroupID string `json:"groupId"`
	AgentList        struct {
		Hosts []internalbls.Host `json:"hosts"`
	}
}

type GroupAddItemResponse struct{}

type GroupDeleteItemRequest GroupAddItemRequest

type GroupDeleteItemResponse struct{}

type ListCollectorRequest map[string]string

type ListCollectorResponse struct {
	TotalSize int                `json:"totalSize"`
	Offset    int                `json:"offset"`
	Size      int                `json:"size"`
	Hosts     []internalbls.Host `json:"hosts"`
}

type ListGroupCollectorRequest struct {
	GroupID string
	Query   map[string]string
}

type ListGroupCollectorResponse struct {
	TotalSize int                `json:"totalSize"`
	Offset    int                `json:"offset"`
	Size      int                `json:"size"`
	Hosts     []internalbls.Host `json:"hosts"`
}

type TokenInterface interface {
	CreateToken(ctx context.Context, args *CreateTokenRequest, opt *bce.SignOption) (*CreateTokenResponse, error)
	DeleteToken(ctx context.Context, args *DeleteTokenRequest, opt *bce.SignOption) (*DeleteTokenResponse, error)
	ListToken(ctx context.Context, args ListTokenRequest, opt *bce.SignOption) (*ListTokenResponse, error)
}

type CreateTokenRequest struct {
	Description string `json:"description"`
}

type CreateTokenResponse struct {
	TokenID string `json:"userTokenId"`
}

type DeleteTokenRequest struct {
	TokenID string `json:"tokenID"`
}

type DeleteTokenResponse struct{}

type ListTokenRequest map[string]string

type ListTokenResponse struct {
	TotalSize int                       `json:"totalSize"`
	Offset    int                       `json:"offset"`
	Size      int                       `json:"size"`
	Tokens    []internalbls.TokenDetail `json:"userTokens"`
}

type SignFn func() *bce.SignOption
