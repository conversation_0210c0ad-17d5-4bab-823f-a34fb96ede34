package internalbls

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func (c *Client) CreateToken(ctx context.Context, args *CreateTokenRequest, opt *bce.SignOption) (*CreateTokenResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("userToken", nil), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r CreateTokenResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}

	return &r, nil
}

func (c *Client) DeleteToken(ctx context.Context, args *DeleteTokenRequest, opt *bce.SignOption) (*DeleteTokenResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	req, err := bce.NewRequest("DELETE", c.GetURL(fmt.Sprintf("userToken/%s", args.TokenID), nil), nil)
	if err != nil {
		return nil, err
	}

	if _, err := c.SendRequest(ctx, req, opt); err != nil {
		return nil, err
	}

	return &DeleteTokenResponse{}, nil
}

func (c *Client) ListToken(ctx context.Context, args ListTokenRequest, opt *bce.SignOption) (*ListTokenResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("userToken"), args), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListTokenResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}
	return &r, nil
}
