package internalbls

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"reflect"
	"testing"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	v1 "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/internalbls/apis/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/retrypolicy"
)

func Test_Client_DeleteToken(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		args   *DeleteTokenRequest
		fields fields
		want   *DeleteTokenResponse
	}{
		{
			name: "Delete Token",
			args: &DeleteTokenRequest{TokenID: "0000001"},
			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						return &http.Response{
							StatusCode: 200,
						}, nil
					}),
				}
			}(),
			want: &DeleteTokenResponse{},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.DeleteToken(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}

func Test_Client_ListToken(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		args   ListTokenRequest
		fields fields
		want   *ListTokenResponse
	}{
		{
			name: "List Token",
			args: ListTokenRequest{},
			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						r := ListTokenResponse{
							TotalSize: 2,
							Offset:    0,
							Size:      0,
							Tokens: []v1.TokenDetail{
								{
									ID:               "1",
									Token:            "",
									Description:      "",
									CreationDateTime: "",
									InstallCommands:  nil,
								},
								{
									ID:               "2",
									Token:            "",
									Description:      "",
									CreationDateTime: "",
									InstallCommands:  nil,
								},
							},
						}
						header := http.Header{}
						header.Set("Content-Type", "application/json")

						body, err := json.Marshal(r)
						if err != nil {
							return nil, err
						}

						return &http.Response{
							StatusCode: 200,
							Body:       io.NopCloser(bytes.NewReader(body)),
							Header:     header,
						}, nil
					}),
				}
			}(),
			want: &ListTokenResponse{
				TotalSize: 2,
				Offset:    0,
				Size:      0,
				Tokens: []v1.TokenDetail{
					{
						ID:               "1",
						Token:            "",
						Description:      "",
						CreationDateTime: "",
						InstallCommands:  nil,
					},
					{
						ID:               "2",
						Token:            "",
						Description:      "",
						CreationDateTime: "",
						InstallCommands:  nil,
					},
				},
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.ListToken(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}

func Test_Client_CreateToken(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		args   *CreateTokenRequest
		fields fields
		want   *CreateTokenResponse
	}{
		{
			name: "Create Token",
			args: &CreateTokenRequest{Description: "cce-log-operator auto create for cce-nbdzyfsb"},

			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						r := CreateTokenResponse{TokenID: "12345678"}
						header := http.Header{}
						header.Set("Content-Type", "application/json")

						body, err := json.Marshal(r)
						if err != nil {
							return nil, err
						}

						return &http.Response{
							StatusCode: 200,
							Body:       io.NopCloser(bytes.NewReader(body)),
							Header:     header,
						}, nil
					}),
				}
			}(),
			want: &CreateTokenResponse{TokenID: "12345678"},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			resp, err := cli.CreateToken(context.TODO(), c.args, nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(resp, c.want) {
				t.Errorf("want: %v, got: %v", c.want, resp)
			}
		})
	}
}
