package internalbls

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"reflect"
	"testing"
	"time"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/retrypolicy"
)

// transportFunc allows us to inject a mock transport for testing. We define it
// here so we can detect the tlsconfig and return nil for only this type.
type transportFunc func(*http.Request) (*http.Response, error)

func (tf transportFunc) RoundTrip(req *http.Request) (*http.Response, error) {
	return tf(req)
}

func newMockClient(doer func(*http.Request) (*http.Response, error)) *http.Client {
	return &http.Client{
		Transport: transportFunc(doer),
	}
}

func Test_Client(t *testing.T) {
	cfg := bce.Config{
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      "gztest",
		Endpoint:    "bls.gz.baidubce.com:8085",
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
	}
	c := NewClient(&cfg)
	if c == nil {
		t.Errorf("got nil client")
		return
	}

	c.SetDebug(true)

	if c.GetURL("task", nil) != "http://bls.gz.baidubce.com:8085/task" {
		t.Errorf("not equal")
		return
	}
}

func Test_Client_Activate(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		fields fields
		want   error
	}{
		{
			name: "Activate BLS",
			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						return &http.Response{
							StatusCode: 200,
						}, nil
					}),
				}
			}(),
			want: nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			err := cli.Activate(context.TODO(), nil)

			if !reflect.DeepEqual(err, c.want) {
				t.Errorf("want: %v, got: %v", c.want, err)
			}
		})
	}
}

func Test_Client_IsActivate(t *testing.T) {
	type fields struct {
		httpClient *http.Client
		cfg        *bce.Config
	}

	cases := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "Activate BLS",
			fields: func() fields {
				return fields{
					cfg: &bce.Config{
						Credentials: bce.NewCredentials("sk", "ak"),
						Checksum:    true,
						Timeout:     30 * time.Second,
						Region:      "gztest",
						Endpoint:    "bls.gz.baidubce.com:8085",
						RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
					},
					httpClient: newMockClient(func(request *http.Request) (*http.Response, error) {
						r := struct {
							IsActivated bool `json:"isActivated"`
						}{IsActivated: true}
						header := http.Header{}
						header.Set("Content-Type", "application/json")

						body, err := json.Marshal(r)
						if err != nil {
							return nil, err
						}

						return &http.Response{
							StatusCode: 200,
							Body:       io.NopCloser(bytes.NewReader(body)),
							Header:     header,
						}, nil
					}),
				}
			}(),
			want: true,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			cli := NewClient(c.fields.cfg)
			if cli == nil {
				t.Errorf("got nil client")
				return
			}

			cli.SetHttpClient(c.fields.httpClient)

			result, err := cli.IsActivate(context.TODO(), nil)
			if err != nil {
				t.Errorf("got err: %v", err)
				return
			}

			if !reflect.DeepEqual(result, c.want) {
				t.Errorf("want: %v, got: %v", c.want, result)
			}
		})
	}
}
