package main

import (
	"context"
	"flag"
	"fmt"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/ccegateway"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/internalbls"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/internalbls/apis/v1"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/retrypolicy"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/utils"
)

var helper ccegateway.Helper

func NewInternalBLSClient(clusterID, region string) *internalbls.Client {
	helper = ccegateway.NewHelper(region, clusterID)

	cfg := bce.Config{
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      "gz",
		Endpoint:    "bls.gz.baidubce.com:8085",
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
	}

	cfg.ProxyHost, cfg.ProxyPort = helper.GetHostAndPort()

	return internalbls.NewClient(&cfg)
}

func NewK8SClient() kubernetes.Interface {
	k8sClient, err := utils.NewK8SClient(context.TODO(), "")
	if err != nil {
		panic(err)
	}

	return k8sClient
}

var (
	clusterID string
	region    string
)

func init() {
	flag.StringVar(&clusterID, "cluster-id", "", "CCE Cluster ID")
	flag.StringVar(&region, "region", "gztest", "CCE Cluster region")

	flag.Parse()
}

func main() {
	if clusterID == "" || region == "" {
		panic("clusterID and region must not be empty")
	}

	name := "zihua-test-bls-task1"
	ctx := context.WithValue(context.Background(), logger.RequestID, logger.GetUUID())
	client := NewInternalBLSClient(clusterID, region)

	signFn := func() *bce.SignOption {
		opt, err := helper.NewSignOptionFromSecret(ctx,
			func(ns, name string) (*corev1.Secret, error) {
				k8sClient := NewK8SClient()
				return k8sClient.CoreV1().Secrets(ns).Get(ctx, name, metav1.GetOptions{})
			})

		if err != nil {
			panic(err)
		}
		return opt
	}

	// create instance
	createResp, err := client.CreateTask(ctx, &internalbls.CreateTaskRequest{
		Name: name,
		Config: v1.Config{
			SrcConfig: v1.SrcConfig{
				SrcType:      "container",
				LogType:      "stdout",
				TTL:          3,
				UseMultiline: false,
				ProcessType:  "none",
				LogTime:      "logTime",
				DateFormat:   "dateFormat",
				LabelWhite:   []v1.Label{{Key: "io.kubernetes.container.name", Value: "*"}, {Key: "io.kubernetes.container.namespace", Value: "*"}},
				EnvWhite:     []v1.Label{{Key: "app", Value: "control-plane"}},
			},
			DestConfig: v1.DestConfig{
				LogStore:  "BCI-log-gz",
				DestType:  "BLS",
				RateLimit: 10,
			},
		},
		Tags: []v1.Tag{{TagKey: "clusterID", TagValue: "cce-nbdzyfsb"}},
	}, signFn())
	if err != nil {
		panic(err)
	}

	fmt.Printf("create response: %v\n", utils.ToJSON(createResp))

	time.Sleep(time.Second * 3)

	listArgs := internalbls.ListTaskRequest{}

	listResp, err := client.ListTask(ctx, listArgs, signFn())
	if err != nil {
		panic(err)
	}

	fmt.Printf("list response: %v\n", utils.ToJSON(listResp))
	time.Sleep(time.Second * 3)

	var taskID string

	for _, item := range listResp.Tasks {
		if item.Name == name {
			getResp, err := client.GetTask(ctx, &internalbls.GetTaskRequest{TaskID: item.ID}, signFn())
			if err != nil {
				panic(err)
			}

			taskID = item.ID

			fmt.Printf("get detail response: %v\n", utils.ToJSON(getResp))
			break
		}
	}

	if taskID == "" {
		fmt.Print("not found taskID, exit.\n")
		return
	}

	// delete agent
	time.Sleep(time.Second * 60)
	deleteAgentResp, err := client.DeleteTask(ctx, &internalbls.DeleteTaskRequest{
		TaskID: taskID,
	}, signFn())
	if err != nil {
		panic(err)
	}

	fmt.Printf("delete agent response: %v", utils.ToJSON(deleteAgentResp))

	fmt.Println("run internal bls sdk e2e success!")
}
