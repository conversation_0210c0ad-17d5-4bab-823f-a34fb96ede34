---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: internal-bls-e2e
rules:
  - apiGroups: [""]
    resources:
      - secrets
    verbs:
      - get
      - list
      - watch
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: internal-bls-e2e
  namespace: kube-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: internal-bls-e2e
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: internal-bls-e2e
subjects:
  - kind: ServiceAccount
    name: internal-bls-e2e
    namespace: kube-system
---
apiVersion: batch/v1
kind: Job
metadata:
  name: internal-bls-e2e
  namespace: kube-system
spec:
  template:
    metadata:
      labels:
        app: internal-bls-e2e
    spec:
      serviceAccountName: internal-bls-e2e
      restartPolicy: Never
      containers:
        - name: internal-bls-e2e
          args:
            - --cluster-id=cce-nbdzyfsb
            - --region=gz
          image: registry.baidubce.com/cce-plugin-dev/internal-bls-e2e:v0.2.0
          imagePullPolicy: Always
          resources:
            limits:
              memory: 500Mi
              cpu: 1000m
            requests:
              memory: 10Mi
              cpu: 100m
---