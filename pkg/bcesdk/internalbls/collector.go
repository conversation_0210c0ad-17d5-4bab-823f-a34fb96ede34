package internalbls

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func (c *Client) CreateCollectorGroup(ctx context.Context, args *CreateCollectorGroupRequest, opt *bce.SignOption) (*CreateCollectorGroupResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("instanceGroup", nil), bytes.<PERSON>(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r CreateCollectorGroupResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}

	return &r, nil
}

func (c *Client) GetCollectorGroup(ctx context.Context, args *GetCollectorGroupRequest, opt *bce.SignOption) (*GetCollectorGroupResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("instanceGroup/%s/detail", args.CollectorGroupID), nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r GetCollectorGroupResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}

	return &r, nil
}

func (c *Client) DeleteCollectorGroup(ctx context.Context, args *DeleteCollectorGroupRequest, opt *bce.SignOption) (*DeleteCollectorGroupResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	req, err := bce.NewRequest("POST", c.GetURL(fmt.Sprintf("instanceGroup/%s/delete", args.CollectorGroupID), nil), nil)
	if err != nil {
		return nil, err
	}

	if _, err := c.SendRequest(ctx, req, opt); err != nil {
		return nil, err
	}

	return &DeleteCollectorGroupResponse{}, nil
}

func (c *Client) ListCollectorGroup(ctx context.Context, args ListCollectorGroupRequest, opt *bce.SignOption) (*ListCollectorGroupResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	req, err := bce.NewRequest("GET", c.GetURL("instanceGroup", args), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListCollectorGroupResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}

	return &r, nil
}

func (c *Client) ListGroupCollector(ctx context.Context, args *ListGroupCollectorRequest, opt *bce.SignOption) (*ListGroupCollectorResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("instanceGroup/%v", args.GroupID), args.Query), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListGroupCollectorResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}

	return &r, nil
}

func (c *Client) ListCollector(ctx context.Context, args ListCollectorRequest, opt *bce.SignOption) (*ListCollectorResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	req, err := bce.NewRequest("GET", c.GetURL("host", args), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListCollectorResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}

	return &r, nil
}

func (c *Client) GroupAddItem(ctx context.Context, args *GroupAddItemRequest, opt *bce.SignOption) (*GroupAddItemResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	postContent, err := json.Marshal(args.AgentList)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL(fmt.Sprintf("instanceGroup/%s/add", args.CollectorGroupID), nil), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	if _, err := c.SendRequest(ctx, req, opt); err != nil {
		return nil, err
	}

	return &GroupAddItemResponse{}, nil
}

func (c *Client) GroupDeleteItem(ctx context.Context, args *GroupDeleteItemRequest, opt *bce.SignOption) (*GroupDeleteItemResponse, error) {
	if args == nil {
		return nil, errors.New("args is nil")
	}

	postContent, err := json.Marshal(args.AgentList)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL(fmt.Sprintf("instanceGroup/%s/delete", args.CollectorGroupID), nil), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	if _, err := c.SendRequest(ctx, req, opt); err != nil {
		return nil, err
	}

	return &GroupDeleteItemResponse{}, nil
}
