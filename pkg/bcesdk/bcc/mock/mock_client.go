// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	api "github.com/baidubce/bce-sdk-go/services/bcc/api"
	gomock "github.com/golang/mock/gomock"
	bcc "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bcc"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	tag "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/tag"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// AttachCDSVolume mocks base method.
func (m *MockInterface) AttachCDSVolume(arg0 context.Context, arg1 *bcc.AttachCDSVolumeArgs, arg2 *bce.SignOption) (*bcc.VolumeAttachment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AttachCDSVolume", arg0, arg1, arg2)
	ret0, _ := ret[0].(*bcc.VolumeAttachment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AttachCDSVolume indicates an expected call of AttachCDSVolume.
func (mr *MockInterfaceMockRecorder) AttachCDSVolume(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AttachCDSVolume", reflect.TypeOf((*MockInterface)(nil).AttachCDSVolume), arg0, arg1, arg2)
}

// BatchDeleteInstance mocks base method.
func (m *MockInterface) BatchDeleteInstance(arg0 context.Context, arg1 *bcc.BatchDeleteInstanceArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteInstance", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDeleteInstance indicates an expected call of BatchDeleteInstance.
func (mr *MockInterfaceMockRecorder) BatchDeleteInstance(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteInstance", reflect.TypeOf((*MockInterface)(nil).BatchDeleteInstance), arg0, arg1, arg2)
}

// BatchRebuildInstances mocks base method.
func (m *MockInterface) BatchRebuildInstances(arg0 context.Context, arg1 *api.RebuildBatchInstanceArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchRebuildInstances", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchRebuildInstances indicates an expected call of BatchRebuildInstances.
func (mr *MockInterfaceMockRecorder) BatchRebuildInstances(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchRebuildInstances", reflect.TypeOf((*MockInterface)(nil).BatchRebuildInstances), arg0, arg1, arg2)
}

// StopInstanceWithNoCharge mocks base method.
func (m *MockInterface) StopInstanceWithNoCharge(arg0 context.Context, arg1 string, arg2 *api.StopInstanceArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StopInstanceWithNoCharge", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// StopInstanceWithNoCharge indicates an expected call of StopInstanceWithNoCharge.
func (mr *MockInterfaceMockRecorder) StopInstanceWithNoCharge(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopInstanceWithNoCharge", reflect.TypeOf((*MockInterface)(nil).StopInstanceWithNoCharge), arg0, arg1, arg2, arg3)
}

// StartInstance mocks base method.
func (m *MockInterface) StartInstance(arg0 context.Context, arg1 string, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartInstance", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartInstance indicates an expected call of StartInstance.
func (mr *MockInterfaceMockRecorder) StartInstance(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartInstance", reflect.TypeOf((*MockInterface)(nil).StartInstance), arg0, arg1, arg2)
}

// BindSecurityGroup mocks base method.
func (m *MockInterface) BindSecurityGroup(arg0 context.Context, arg1, arg2 string, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindSecurityGroup", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// BindSecurityGroup indicates an expected call of BindSecurityGroup.
func (mr *MockInterfaceMockRecorder) BindSecurityGroup(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindSecurityGroup", reflect.TypeOf((*MockInterface)(nil).BindSecurityGroup), arg0, arg1, arg2, arg3)
}

// BindTags mocks base method.
func (m *MockInterface) BindTags(arg0 context.Context, arg1 string, arg2 []tag.Tag, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindTags", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// BindTags indicates an expected call of BindTags.
func (mr *MockInterfaceMockRecorder) BindTags(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindTags", reflect.TypeOf((*MockInterface)(nil).BindTags), arg0, arg1, arg2, arg3)
}

// CreateBidInstance mocks base method.
func (m *MockInterface) CreateBidInstance(arg0 context.Context, arg1 *api.CreateInstanceArgs, arg2 string, arg3 *bce.SignOption) (*api.CreateInstanceResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBidInstance", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*api.CreateInstanceResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateBidInstance indicates an expected call of CreateBidInstance.
func (mr *MockInterfaceMockRecorder) CreateBidInstance(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBidInstance", reflect.TypeOf((*MockInterface)(nil).CreateBidInstance), arg0, arg1, arg2, arg3)
}

// CreateInstance mocks base method.
func (m *MockInterface) CreateInstance(arg0 context.Context, arg1 *api.CreateInstanceArgs, arg2 *bce.SignOption) (*api.CreateInstanceResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstance", arg0, arg1, arg2)
	ret0, _ := ret[0].(*api.CreateInstanceResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInstance indicates an expected call of CreateInstance.
func (mr *MockInterfaceMockRecorder) CreateInstance(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstance", reflect.TypeOf((*MockInterface)(nil).CreateInstance), arg0, arg1, arg2)
}

// CreateInstanceBySpec mocks base method.
func (m *MockInterface) CreateInstanceBySpec(arg0 context.Context, arg1 *bcc.CreateInstanceBySpecArgsShell, arg2 string, arg3 *bce.SignOption) (*api.CreateInstanceBySpecResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstanceBySpec", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*api.CreateInstanceBySpecResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInstanceBySpec indicates an expected call of CreateInstanceBySpec.
func (mr *MockInterfaceMockRecorder) CreateInstanceBySpec(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstanceBySpec", reflect.TypeOf((*MockInterface)(nil).CreateInstanceBySpec), arg0, arg1, arg2, arg3)
}

// CreateSecurityGroup mocks base method.
func (m *MockInterface) CreateSecurityGroup(arg0 context.Context, arg1 *bcc.CreateSecurityGroupRequest, arg2 *bce.SignOption) (*bcc.CreateSecurityGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSecurityGroup", arg0, arg1, arg2)
	ret0, _ := ret[0].(*bcc.CreateSecurityGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSecurityGroup indicates an expected call of CreateSecurityGroup.
func (mr *MockInterfaceMockRecorder) CreateSecurityGroup(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSecurityGroup", reflect.TypeOf((*MockInterface)(nil).CreateSecurityGroup), arg0, arg1, arg2)
}

// CreateSecurityGroupRule mocks base method.
func (m *MockInterface) CreateSecurityGroupRule(arg0 context.Context, arg1 *bcc.CreateSecurityGroupRuleRequest, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSecurityGroupRule", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateSecurityGroupRule indicates an expected call of CreateSecurityGroupRule.
func (mr *MockInterfaceMockRecorder) CreateSecurityGroupRule(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSecurityGroupRule", reflect.TypeOf((*MockInterface)(nil).CreateSecurityGroupRule), arg0, arg1, arg2)
}

// CreateSnapshot mocks base method.
func (m *MockInterface) CreateSnapshot(arg0 context.Context, arg1 *bcc.CreateSnapShotArgs, arg2 *bce.SignOption) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSnapshot", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSnapshot indicates an expected call of CreateSnapshot.
func (mr *MockInterfaceMockRecorder) CreateSnapshot(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSnapshot", reflect.TypeOf((*MockInterface)(nil).CreateSnapshot), arg0, arg1, arg2)
}

// CreateVolumes mocks base method.
func (m *MockInterface) CreateVolumes(arg0 context.Context, arg1 *bcc.CreateVolumeArgs, arg2 *bce.SignOption) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateVolumes", arg0, arg1, arg2)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateVolumes indicates an expected call of CreateVolumes.
func (mr *MockInterfaceMockRecorder) CreateVolumes(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateVolumes", reflect.TypeOf((*MockInterface)(nil).CreateVolumes), arg0, arg1, arg2)
}

// DeleteInstance mocks base method.
func (m *MockInterface) DeleteInstance(arg0 context.Context, arg1 string, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteInstance", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteInstance indicates an expected call of DeleteInstance.
func (mr *MockInterfaceMockRecorder) DeleteInstance(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInstance", reflect.TypeOf((*MockInterface)(nil).DeleteInstance), arg0, arg1, arg2)
}

// DeleteInstanceWithArgs mocks base method.
func (m *MockInterface) DeleteInstanceWithArgs(arg0 context.Context, arg1 string, arg2 *bcc.DeleteInstanceArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteInstanceWithArgs", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteInstanceWithArgs indicates an expected call of DeleteInstanceWithArgs.
func (mr *MockInterfaceMockRecorder) DeleteInstanceWithArgs(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInstanceWithArgs", reflect.TypeOf((*MockInterface)(nil).DeleteInstanceWithArgs), arg0, arg1, arg2, arg3)
}

// DeleteSecurityGroup mocks base method.
func (m *MockInterface) DeleteSecurityGroup(arg0 context.Context, arg1 string, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteSecurityGroup", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteSecurityGroup indicates an expected call of DeleteSecurityGroup.
func (mr *MockInterfaceMockRecorder) DeleteSecurityGroup(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSecurityGroup", reflect.TypeOf((*MockInterface)(nil).DeleteSecurityGroup), arg0, arg1, arg2)
}

// DeleteSnapshot mocks base method.
func (m *MockInterface) DeleteSnapshot(arg0 context.Context, arg1 string, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteSnapshot", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteSnapshot indicates an expected call of DeleteSnapshot.
func (mr *MockInterfaceMockRecorder) DeleteSnapshot(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSnapshot", reflect.TypeOf((*MockInterface)(nil).DeleteSnapshot), arg0, arg1, arg2)
}

// DeleteVolume mocks base method.
func (m *MockInterface) DeleteVolume(arg0 context.Context, arg1 string, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteVolume", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteVolume indicates an expected call of DeleteVolume.
func (mr *MockInterfaceMockRecorder) DeleteVolume(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteVolume", reflect.TypeOf((*MockInterface)(nil).DeleteVolume), arg0, arg1, arg2)
}

// DescribeInstance mocks base method.
func (m *MockInterface) DescribeInstance(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*bcc.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeInstance", arg0, arg1, arg2)
	ret0, _ := ret[0].(*bcc.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeInstance indicates an expected call of DescribeInstance.
func (mr *MockInterfaceMockRecorder) DescribeInstance(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeInstance", reflect.TypeOf((*MockInterface)(nil).DescribeInstance), arg0, arg1, arg2)
}

// DescribeInstanceWithDeploySet mocks base method.
func (m *MockInterface) DescribeInstanceWithDeploySet(arg0 context.Context, arg1 string, arg2 bool, arg3 *bce.SignOption) (*bcc.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeInstanceWithDeploySet", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*bcc.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeInstanceWithDeploySet indicates an expected call of DescribeInstanceWithDeploySet.
func (mr *MockInterfaceMockRecorder) DescribeInstanceWithDeploySet(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeInstanceWithDeploySet", reflect.TypeOf((*MockInterface)(nil).DescribeInstanceWithDeploySet), arg0, arg1, arg2, arg3)
}

// DescribeSnapshot mocks base method.
func (m *MockInterface) DescribeSnapshot(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*bcc.Snapshot, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeSnapshot", arg0, arg1, arg2)
	ret0, _ := ret[0].(*bcc.Snapshot)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeSnapshot indicates an expected call of DescribeSnapshot.
func (mr *MockInterfaceMockRecorder) DescribeSnapshot(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeSnapshot", reflect.TypeOf((*MockInterface)(nil).DescribeSnapshot), arg0, arg1, arg2)
}

// DescribeVolume mocks base method.
func (m *MockInterface) DescribeVolume(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*bcc.Volume, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeVolume", arg0, arg1, arg2)
	ret0, _ := ret[0].(*bcc.Volume)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeVolume indicates an expected call of DescribeVolume.
func (mr *MockInterfaceMockRecorder) DescribeVolume(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeVolume", reflect.TypeOf((*MockInterface)(nil).DescribeVolume), arg0, arg1, arg2)
}

// DetachCDSVolume mocks base method.
func (m *MockInterface) DetachCDSVolume(arg0 context.Context, arg1 *bcc.AttachCDSVolumeArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DetachCDSVolume", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DetachCDSVolume indicates an expected call of DetachCDSVolume.
func (mr *MockInterfaceMockRecorder) DetachCDSVolume(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DetachCDSVolume", reflect.TypeOf((*MockInterface)(nil).DetachCDSVolume), arg0, arg1, arg2)
}

// GetSecurityGroups mocks base method.
func (m *MockInterface) GetSecurityGroups(arg0 context.Context, arg1 *bcc.GetSecurityGroupsRequest, arg2 *bce.SignOption) (*bcc.GetSecurityGroupsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSecurityGroups", arg0, arg1, arg2)
	ret0, _ := ret[0].(*bcc.GetSecurityGroupsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSecurityGroups indicates an expected call of GetSecurityGroups.
func (mr *MockInterfaceMockRecorder) GetSecurityGroups(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSecurityGroups", reflect.TypeOf((*MockInterface)(nil).GetSecurityGroups), arg0, arg1, arg2)
}

// GetSnapshotList mocks base method.
func (m *MockInterface) GetSnapshotList(arg0 context.Context, arg1 string, arg2 *bce.SignOption) ([]bcc.Snapshot, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSnapshotList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]bcc.Snapshot)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSnapshotList indicates an expected call of GetSnapshotList.
func (mr *MockInterfaceMockRecorder) GetSnapshotList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSnapshotList", reflect.TypeOf((*MockInterface)(nil).GetSnapshotList), arg0, arg1, arg2)
}

// ListFlavorSpec mocks base method.
func (m *MockInterface) ListFlavorSpec(arg0 context.Context, arg1 *api.ListFlavorSpecArgs, arg2 *bce.SignOption) (*api.ListFlavorSpecResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListFlavorSpec", arg0, arg1, arg2)
	ret0, _ := ret[0].(*api.ListFlavorSpecResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListFlavorSpec indicates an expected call of ListFlavorSpec.
func (mr *MockInterfaceMockRecorder) ListFlavorSpec(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListFlavorSpec", reflect.TypeOf((*MockInterface)(nil).ListFlavorSpec), arg0, arg1, arg2)
}

// ListInstanceByInstanceIds mocks base method.
func (m *MockInterface) ListInstanceByInstanceIds(arg0 context.Context, arg1 *api.ListInstanceByInstanceIdArgs, arg2 *bce.SignOption) (*api.ListInstancesResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstanceByInstanceIds", arg0, arg1, arg2)
	ret0, _ := ret[0].(*api.ListInstancesResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstanceByInstanceIds indicates an expected call of ListInstanceByInstanceIds.
func (mr *MockInterfaceMockRecorder) ListInstanceByInstanceIds(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstanceByInstanceIds", reflect.TypeOf((*MockInterface)(nil).ListInstanceByInstanceIds), arg0, arg1, arg2)
}

// ListInstanceTypes mocks base method.
func (m *MockInterface) ListInstanceTypes(arg0 context.Context, arg1 *api.ListInstanceTypeArgs, arg2 *bce.SignOption) (*api.ListInstanceTypeResults, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstanceTypes", arg0, arg1, arg2)
	ret0, _ := ret[0].(*api.ListInstanceTypeResults)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstanceTypes indicates an expected call of ListInstanceTypes.
func (mr *MockInterfaceMockRecorder) ListInstanceTypes(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstanceTypes", reflect.TypeOf((*MockInterface)(nil).ListInstanceTypes), arg0, arg1, arg2)
}

// ListInstances mocks base method.
func (m *MockInterface) ListInstances(arg0 context.Context, arg1 *bce.SignOption) ([]bcc.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstances", arg0, arg1)
	ret0, _ := ret[0].([]bcc.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstances indicates an expected call of ListInstances.
func (mr *MockInterfaceMockRecorder) ListInstances(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstances", reflect.TypeOf((*MockInterface)(nil).ListInstances), arg0, arg1)
}

// ListInstancesByIP mocks base method.
func (m *MockInterface) ListInstancesByIP(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*bcc.ListInstancesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstancesByIP", arg0, arg1, arg2)
	ret0, _ := ret[0].(*bcc.ListInstancesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstancesByIP indicates an expected call of ListInstancesByIP.
func (mr *MockInterfaceMockRecorder) ListInstancesByIP(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstancesByIP", reflect.TypeOf((*MockInterface)(nil).ListInstancesByIP), arg0, arg1, arg2)
}

// ListVolumes mocks base method.
func (m *MockInterface) ListVolumes(arg0 context.Context, arg1, arg2 string, arg3 *bce.SignOption) (*bcc.ListVolumesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListVolumes", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*bcc.ListVolumesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListVolumes indicates an expected call of ListVolumes.
func (mr *MockInterfaceMockRecorder) ListVolumes(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListVolumes", reflect.TypeOf((*MockInterface)(nil).ListVolumes), arg0, arg1, arg2, arg3)
}

// ModifyInstanceDesc mocks base method.
func (m *MockInterface) ModifyInstanceDesc(arg0 context.Context, arg1 string, arg2 *bcc.ModifyInstanceDescArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyInstanceDesc", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// ModifyInstanceDesc indicates an expected call of ModifyInstanceDesc.
func (mr *MockInterfaceMockRecorder) ModifyInstanceDesc(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyInstanceDesc", reflect.TypeOf((*MockInterface)(nil).ModifyInstanceDesc), arg0, arg1, arg2, arg3)
}

// ModifyInstanceName mocks base method.
func (m *MockInterface) ModifyInstanceName(arg0 context.Context, arg1 string, arg2 *bcc.ModifyInstanceNameArgs, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyInstanceName", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// ModifyInstanceName indicates an expected call of ModifyInstanceName.
func (mr *MockInterfaceMockRecorder) ModifyInstanceName(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyInstanceName", reflect.TypeOf((*MockInterface)(nil).ModifyInstanceName), arg0, arg1, arg2, arg3)
}

// SetDebug mocks base method.
func (m *MockInterface) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug.
func (mr *MockInterfaceMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), arg0)
}

// UnbindSecurityGroup mocks base method.
func (m *MockInterface) UnbindSecurityGroup(arg0 context.Context, arg1, arg2 string, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnbindSecurityGroup", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnbindSecurityGroup indicates an expected call of UnbindSecurityGroup.
func (mr *MockInterfaceMockRecorder) UnbindSecurityGroup(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnbindSecurityGroup", reflect.TypeOf((*MockInterface)(nil).UnbindSecurityGroup), arg0, arg1, arg2, arg3)
}

// UnbindTags mocks base method.
func (m *MockInterface) UnbindTags(arg0 context.Context, arg1 string, arg2 []tag.Tag, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnbindTags", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnbindTags indicates an expected call of UnbindTags.
func (mr *MockInterfaceMockRecorder) UnbindTags(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnbindTags", reflect.TypeOf((*MockInterface)(nil).UnbindTags), arg0, arg1, arg2, arg3)
}
