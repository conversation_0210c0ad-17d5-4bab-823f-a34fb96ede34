package bcc

import (
	"context"
	"fmt"
	"testing"
)

func testListVpc(t *testing.T) {
	// ts := httptest.NewServer(EipHandler())
	// defer ts.Close()
	// eipClient.Endpoint = ts.URL
	// eips, err := eipClient.GetEips(nil)
	// bccClient.Endpoint = "bcc.bce-api.baidu.com"
	args := ListVpcArgs{
		IsDefault: false,
	}
	vpcs, err := bccClient.ListVpc(context.Background(), &args)
	if err != nil {
		t.Error(err)
	}
	for _, vpc := range vpcs {
		fmt.Println(vpc.VpcID)
		fmt.Println(vpc.Name)
	}
}
