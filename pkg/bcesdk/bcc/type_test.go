package bcc

import (
	"encoding/json"
	"testing"
)

func TestSecurityGroupRule(t *testing.T) {
	jsonStr := "{\n" +
		"    \"marker\":\"\",\n" +
		"    \"isTruncated\":false,\n" +
		"    \"maxKeys\":1000,\n" +
		"    \"securityGroups\":[\n" +
		"        {\n" +
		"            \"id\":\"g-0013dd4jifzp\",\n" +
		"            \"name\":\"rule_生产全网开UDP5050\",\n" +
		"            \"vpcId\":\"vpc-ky18hzkt66ex\",\n" +
		"            \"desc\":\"UDP5050端口\n全网开放\",\n" +
		"            \"createdTime\":\"2019-11-22T02:45:39Z\",\n" +
		"            \"sgVersion\":0,\n" +
		"            \"rules\":[\n" +
		"                {\n" +
		"                    \"remark\":\"UDP5050端口\t全网开放\",\n" +
		"                    \"direction\":\"ingress\",\n" +
		"                    \"ethertype\":\"IPv4\",\n" +
		"                    \"portRange\":\"5050\",\n" +
		"                    \"sourceGroupId\":\"\",\n" +
		"                    \"sourceIp\":\"0.0.0.0/0\",\n" +
		"                    \"securityGroupId\":\"g-0013dd4jifzp\",\n" +
		"                    \"securityGroupRuleId\":\"r-ytp48qqjafcx\",\n" +
		"                    \"createdTime\":\"2019-12-10T08:38:28Z\",\n" +
		"                    \"updatedTime\":\"2019-12-10T08:38:28Z\",\n" +
		"                    \"protocol\":\"udp\"\n" +
		"                }\n" +
		"            ]\n" +
		"        }\n" +
		"    ]\n" +
		"}"

	response := new(GetSecurityGroupsResponse)

	// t.Logf("json: %v", jsonStr)

	err := json.Unmarshal(removeInvalidChar([]byte(jsonStr)), response)
	if err != nil {
		t.Errorf("error: %s", err.Error())
	}
	t.Logf("response: %v", response)
}
