package bcc

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/tag"
)

func (c *Client) BindTags(ctx context.Context, instanceID string, tags []tag.Tag, option *bce.SignOption) error {
	params := map[string]string{
		"bind": "",
	}

	changeTags := map[string][]tag.Tag{
		"changeTags": tags,
	}
	putContent, err := json.Marshal(changeTags)
	if err != nil {
		return err
	}

	path := fmt.Sprintf("/v2/instance/%s/tag", instanceID)
	req, err := bce.NewRequest("PUT", c.GetURL(path, params), bytes.NewBuffer(putContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	return err
}

func (c *Client) UnbindTags(ctx context.Context, instanceID string, tags []tag.Tag, option *bce.SignOption) error {
	params := map[string]string{
		"unbind": "",
	}

	changeTags := map[string][]tag.Tag{
		"changeTags": tags,
	}
	putContent, err := json.Marshal(changeTags)
	if err != nil {
		return err
	}

	path := fmt.Sprintf("/v2/instance/%s/tag", instanceID)
	req, err := bce.NewRequest("PUT", c.GetURL(path, params), bytes.NewBuffer(putContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	return err
}
