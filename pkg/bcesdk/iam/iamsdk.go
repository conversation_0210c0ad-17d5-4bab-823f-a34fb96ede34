package iam

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	http "net/http"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/karlseguin/ccache"
	iamsdk "icode.baidu.com/baidu/bce-iam/sdk-go/iam"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"
)

// cache 缓存 &bcests.Credential, 参考：https://github.com/karlseguin/ccache
// iam accesskey Cache，容量和IAM SDK保持一致
var akCache = ccache.New(ccache.Configure().MaxSize(10000).ItemsToPrune(500).GetsPerPromote(10))

// iam sub User Cache，非IAM SDK实现，考虑到User列表元素数量比较多，缓存容量暂定5000
var subUserCache = ccache.New(ccache.Configure().MaxSize(5000).ItemsToPrune(500).GetsPerPromote(10))

func (c *SDKClient) GetToken(ctx context.Context, serviceName string, password string,
	option *bce.SignOption) (*Token, error) {
	logger.Infof(ctx, "GetToken by IAM SDK: call GetConsoleToken to get ConsoleToken, serviceName: %s", serviceName)

	// TODO: 使用IAM SDK，ServiceName和ServicePassword应该在NewIAMSDKClient初始化，而不是从接口传参获取
	c.IAMBCEClient.Config.UserName = serviceName
	c.IAMBCEClient.Config.Password = password

	tokenResponse, err := c.IAMBCEClient.GetConsoleToken()
	if err != nil {
		logger.Errorf(ctx, "call GetConcoleToken failed: %s", err)
		return nil, err
	}

	token := ConvertIAMSDKTokenToCCEToken(tokenResponse, password)

	logger.Infof(ctx, "GetConsoleToken Result: tokenID:%s, UserID:%s, UserName:%s, UserDomainID:%s, UserDomainName:%s",
		token.ID, token.User.ID, token.User.Name, token.User.Domain.ID, token.User.Domain.Name)

	return token, nil
}

func (c *SDKClient) GetAkSkByToken(ctx context.Context, serviceName string, password string,
	option *bce.SignOption) (*AccessKey, error) {
	token, err := c.GetToken(ctx, serviceName, password, bce.NewSignOptionWithoutAuth())
	if err != nil {
		logger.Errorf(ctx, "call GetToken failed: %s", err)
		return nil, err
	}

	var akInCache AccessKey
	var ok bool
	cacheItem := akCache.Get(token.User.ID)
	if cacheItem != nil {
		akInCache, ok = cacheItem.Value().(AccessKey)
		if !ok {
			cacheItem = nil
		}
	}

	logger.Infof(ctx, "GetAccessKeys by IAM SDK, userID: %s", token.User.ID)
	accessKeysResp, err := c.IAMBCEClient.GetAccessKeys(token.User.ID, true)
	if err != nil {
		if cacheItem != nil {
			logger.Errorf(ctx, "get accesskeys from remote IAM failed, return from cache instead: %s", err)
			return &akInCache, nil
		}
		logger.Errorf(ctx, "get accesskeys failed and no alternative in cache: %s", err)
		return nil, err
	}

	iamAccessKey := accessKeysResp.AccessKeys[0]
	accessKey := AccessKey{
		Access:       iamAccessKey.Access,
		Secret:       iamAccessKey.Secret,
		CredentialID: iamAccessKey.CredentialId,
		ProjectID:    iamAccessKey.ProjectId,
		UserID:       iamAccessKey.UserId,
		ModifyTime:   iamAccessKey.CreateTime,
	}

	// Update Cache
	if cacheItem == nil || cacheItem.TTL() < time.Duration(CacheUpdateTime)*time.Second || !reflect.DeepEqual(accessKey, akInCache) {
		akCache.Set(token.User.ID, accessKey, time.Duration(CacheExpiration)*time.Second)
	}

	return &accessKey, nil
}

func (c *SDKClient) BatchVerifyPermissionByToken(ctx context.Context, verifyReq *VerifyRequest, subUserID, serviceName,
	password string, option *bce.SignOption) (*VerifyResponse, error) {
	if verifyReq == nil || len(verifyReq.VerifyList) == 0 {
		logger.Errorf(ctx, "call BatchVerifyPermission failed: verifyReq or verifyReq.VerifyList is nil")
		return nil, errors.New("verifyReq is nil")
	}

	if subUserID == "" {
		logger.Errorf(ctx, "call BatchVerifyPermission failed: subUserID  is empty")
		return nil, errors.New("subUserID  is empty")
	}

	permissionRequests := iamsdk.BatchPermissionRequest{
		VerifyList: make([]iamsdk.PermissionRequestV2, 0),
	}

	for _, req := range verifyReq.VerifyList {
		permissionRequest := iamsdk.PermissionRequestV2{
			Service:       req.Service,
			Region:        req.Region,
			Resource:      req.Resource,
			ResourceOwner: req.ResourceOwner,
			Permission:    req.Permission,
			RequestContext: iamsdk.RequestContext{
				IPAddress: req.RequestContext.IPAddress,
				Referer:   req.RequestContext.Referer,
				Variables: make(map[string]any),
			},
		}
		for k, v := range req.RequestContext.Variables {
			permissionRequest.RequestContext.Variables[k] = v
		}
		permissionRequests.VerifyList = append(permissionRequests.VerifyList, permissionRequest)
	}

	netRequest := &http.Request{
		Header: make(http.Header),
	}
	netRequest.Header.Add("X-Baidu-Int-User", subUserID)

	// TODO: 使用IAM SDK，ServiceName和ServicePassword应该在NewIAMSDKClient初始化，而不是从接口传参获取
	c.IAMBCEClient.Config.UserName = serviceName
	c.IAMBCEClient.Config.Password = password

	// IAM SDK BatchVerify
	logger.Infof(ctx, "BatchVerify by IAM SDK, subUserID: %s", subUserID)
	results, err := c.IAMBCEClient.BatchVerify(netRequest, permissionRequests)
	if err != nil {
		logger.Errorf(ctx, "call BatchVerifyPermission failed: %s", err)
		return nil, err
	}

	verifyResp := &VerifyResponse{
		VerifyResults: make([]VerifyResult, 0),
	}

	for _, result := range results.VerifyResults {
		verifyResult := VerifyResult{
			Result: make([]EffectEntity, 0),
		}
		for _, effectResult := range result.Effect {
			effectEntity := EffectEntity{
				Effect: Effect(effectResult.Effect),
			}
			verifyResult.Result = append(verifyResult.Result, effectEntity)
		}
		verifyResp.VerifyResults = append(verifyResp.VerifyResults, verifyResult)
	}

	return verifyResp, nil
}

// IAM SDK未实现该方法, 使用API
func (c *SDKClient) GetSubUser(ctx context.Context, request *UserListRequest, serviceName, password string,
	option *bce.SignOption) (*UserListResponse, error) {
	if request == nil {
		logger.Errorf(ctx, "call GetSubUser Failed: request is nil")
		return nil, errors.New("request is nil")
	}

	token, err := c.GetToken(ctx, serviceName, password, bce.NewSignOptionWithoutAuth())
	if err != nil {
		logger.Errorf(ctx, "call GetToken failed: %s", err)
		return nil, err
	}

	option.AddHeader("X-Auth-Token", token.ID)
	option.AddHeader("X-Subuser-Support", "true")
	params := map[string]string{}

	if request.DomainID != "" {
		params["domain_id"] = request.DomainID
	}

	key := token.ID + request.DomainID

	var userRespInCache UserListResponse
	var ok bool
	cacheItem := subUserCache.Get(key)
	if cacheItem != nil {
		userRespInCache, ok = cacheItem.Value().(UserListResponse)
		if !ok {
			cacheItem = nil
		}
	}

	params["subuser"] = strconv.FormatBool(request.SubUser)

	urlPath := "/users"
	if c.Endpoint == "" || !strings.HasSuffix(c.Endpoint, "/v3") {
		urlPath = "/v3" + urlPath
	}
	req, err := bce.NewRequestWithTimeout("GET", c.GetURL(urlPath, params), Timeout, bytes.NewBuffer(nil))
	if err != nil {
		if cacheItem != nil {
			logger.Errorf(ctx, "failed to new request, return from cache instead: %v", err)
			return &userRespInCache, nil
		}
		logger.Errorf(ctx, "failed to new request and no alternative in cache: %v", err)
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		if cacheItem != nil {
			logger.Errorf(ctx, "failed to send request, return from cache instead: %v", err)
			return &userRespInCache, nil
		}
		logger.Errorf(ctx, "failed to send request and no alternative in cache: %v", err)
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		if cacheItem != nil {
			logger.Errorf(ctx, "failed to send request, return from cache instead: %v", err)
			return &userRespInCache, nil
		}
		logger.Errorf(ctx, "failed to get body content and no alternative in cache: %v", err)
		return nil, err
	}

	var userListResp UserListResponse
	err = json.Unmarshal(bodyContent, &userListResp)
	if err != nil {
		if cacheItem != nil {
			logger.Errorf(ctx, "failed to unmarshal body content, return from cache instead: %v", err)
			return &userRespInCache, nil
		}
		logger.Errorf(ctx, "failed to unmarshal body content and no alternative in cache: %v", err)
		return nil, err
	}

	// Update Cache
	if cacheItem == nil || cacheItem.TTL() < time.Duration(CacheUpdateTime)*time.Second || !reflect.DeepEqual(userListResp, userRespInCache) {
		subUserCache.Set(key, userListResp, time.Duration(CacheExpiration)*time.Second)
	}

	return &userListResp, nil
}

// 未实现容灾，影响范围仅限HPAS
// IAM SDK未实现该方法，使用原有实现
// IAMEncrypt 函数用于调用百度云的IAM服务进行加密操作
// 参数：
// ctx：上下文对象，用于控制请求的取消和超时
// request：IAMEncryptRequest类型的指针，包含加密请求的参数
// serviceName：服务名称，用于获取认证token
// password：服务密码，用于获取认证token
// option：bce.SignOption类型的指针，包含请求签名选项
// 返回值：
// *EncryptResponse：EncryptResponse类型的指针，包含加密响应的结果
// error：如果发生错误，则返回非零的错误码；否则返回nil
func (c *SDKClient) IAMEncrypt(ctx context.Context, request *EncryptRequest, serviceName, password string,
	option *bce.SignOption) (*EncryptResponse, error) {
	if request == nil {
		return nil, errors.New("request is nil")
	}

	token, err := c.GetToken(ctx, serviceName, password, bce.NewSignOptionWithoutAuth())
	if err != nil {
		return nil, err
	}
	option.AddHeader("X-Auth-Token", token.ID)

	logger.Infof(ctx, "X-Auth-Token %v", token.ID)

	params := map[string]string{
		"encrypt": "null",
	}

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	urlPath := "/BCE-CRED/ciphers"
	if c.Endpoint == "" || !strings.HasSuffix(c.Endpoint, "/v3") {
		urlPath = "/v3" + urlPath
	}

	req, err := bce.NewRequest("POST", c.GetURL(urlPath, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	logger.Infof(ctx, "IAMEncryptReq %v", req)
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var EncryptResp EncryptResponse
	err = json.Unmarshal(bodyContent, &EncryptResp)
	if err != nil {
		return nil, err
	}

	return &EncryptResp, nil
}

// IAMDecrypt 函数用于调用百度云的IAM服务进行解密操作
// 参数：
// ctx：上下文对象，用于控制请求的取消和超时
// request：IAMDecryptRequest类型的指针，包含加密请求的参数
// serviceName：服务名称，用于获取认证token
// password：服务密码，用于获取认证token
// option：bce.SignOption类型的指针，包含请求签名选项
// 返回值：
// *DecryptResponse：DecryptResponse，包含加密响应的结果
// error：如果发生错误，则返回非零的错误码；否则返回nil
func (c *SDKClient) IAMDecrypt(ctx context.Context, request *DecryptRequest, serviceName, password string,
	option *bce.SignOption) (*DecryptResponse, error) {
	if request == nil {
		return nil, errors.New("request is nil")
	}

	token, err := c.GetToken(ctx, serviceName, password, bce.NewSignOptionWithoutAuth())
	if err != nil {
		return nil, err
	}
	option.AddHeader("X-Auth-Token", token.ID)

	logger.Infof(ctx, "X-Auth-Token %v", token.ID)

	params := map[string]string{
		"decrypt": "null",
	}

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	urlPath := "/BCE-CRED/ciphers"
	if c.Endpoint == "" || !strings.HasSuffix(c.Endpoint, "/v3") {
		urlPath = "/v3" + urlPath
	}

	req, err := bce.NewRequest("POST", c.GetURL(urlPath, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	logger.Infof(ctx, "IAMDecryptReq %v", req)
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var EncryptResp DecryptResponse
	err = json.Unmarshal(bodyContent, &EncryptResp)
	if err != nil {
		return nil, err
	}

	return &EncryptResp, nil
}

// Convert IAMSDK Token  to CCE iam.Token, differnt from auth.Token
func ConvertIAMSDKTokenToCCEToken(tokenResponse *iamsdk.Token, password string) *Token {
	token := &Token{
		ID: tokenResponse.ID,
		User: User{
			Domain: Domain{
				Name: tokenResponse.User.Domain.Name,
				ID:   tokenResponse.User.Domain.ID,
			},
			ID:       tokenResponse.User.ID,
			Name:     tokenResponse.User.Name,
			Password: password,
		},
	}
	return token
}

func (c *SDKClient) GetUserBcePolicy(ctx context.Context, userID string, serviceName, password string, option *bce.SignOption) (
	*UserBcePolicyListResponse, error) {
	logger.Infof(ctx, "START TO GET: X-Auth-Token")
	token, err := c.GetToken(ctx, serviceName, password, bce.NewSignOptionWithoutAuth())
	if err != nil {
		return nil, err
	}
	// option.AddHeader("X-Auth-Token", token.ID)
	// option.AddHeader("X-Subuser-Support", "true")
	logger.Infof(ctx, "X-Auth-Token %v", token.ID)
	param := map[string]string{
		"domain_id":   "default",
		"policy_type": "System",
	}

	urlPath := fmt.Sprintf("/users/%s/bcepolicy", userID)
	if !strings.HasSuffix(c.Endpoint, "/v3") {
		urlPath = "/v3" + urlPath
	}
	req, err := bce.NewRequest(http.MethodGet, c.GetURL(urlPath, param), bytes.NewBuffer(nil))
	if err != nil {
		return nil, err
	}
	reqHeader := map[string]string{
		"X-Auth-Token":      token.ID,
		"X-Subuser-Support": "true",
	}
	// req.AddHeaders(reqHeader)
	req.SetHeaders(reqHeader)

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	logger.Infof(ctx, "GetUserBcePolicy bodyContent: %v", string(bodyContent))

	listResp := UserBcePolicyListResponse{}
	if err = json.Unmarshal(bodyContent, &listResp); err != nil {
		return nil, err
	}
	return &listResp, nil
}
