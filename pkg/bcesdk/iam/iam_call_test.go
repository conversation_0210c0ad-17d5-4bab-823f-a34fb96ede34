package iam

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
)

var (
	region      = "sandbox"
	serviceName = "cce"
	password    = "********************************"
)

func testGetTokenByServiceNameAndPassword(t *testing.T) {
	client := NewClient(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	})

	client.SetDebug(true)

	token, err := client.GetToken(context.TODO(), serviceName, password, bce.NewSignOptionWithoutAuth())
	if err != nil {
		t.Errorf("get token failed: %v", err)
	}

	if responseByte, err := json.Marshal(token); err == nil {
		t.Logf("get token succeeded: %s", string(responseByte))
	}
}

func testGetAkSkByToken(t *testing.T) {
	client := NewClient(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	})

	client.SetDebug(true)

	accessKey, err := client.GetAkSkByToken(context.TODO(), serviceName, password, bce.NewSignOptionWithoutAuth())
	if err != nil {
		t.Errorf("get ak sk failed: %v", err)
	}

	if responseByte, err := json.Marshal(accessKey); err == nil {
		t.Logf("get ak sk succeeded: %s", string(responseByte))
	}
}

func testBatchVerifyPermission(t *testing.T) {
	client := NewClient(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "bj",
	})

	client.SetDebug(true)

	verifyReq := &VerifyRequest{
		VerifyList: []VerifyEntity{
			{
				Service: IAMService,
				Region:  "global",
				Resource: []string{
					"role/" + "fd3926f8d4df424c9e435b30d13f9715",
				},
				ResourceOwner: "eca97e148cb74e9683d7b7240829d1ff",
				Permission: []string{
					"AssumeRole",
				},
			},
		},
	}

	resp, err := client.BatchVerifyPermissionByToken(context.TODO(), verifyReq, "26551c8ca85c48baa5cf8dcda18b5170",
		"cce", "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt", bce.NewSignOptionWithoutAuth())
	if err != nil {
		t.Errorf("verify failed: %v", err)
	}

	if responseByte, err := json.Marshal(resp); err == nil {
		t.Logf("verify result: %s", string(responseByte))
	}
}

func testUserList(t *testing.T) {
	client := NewClient(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "bj",
	})

	client.SetDebug(true)

	userListReq := &UserListRequest{
		DomainID: "eca97e148cb74e9683d7b7240829d1ff",
		SubUser:  true,
	}

	resp, err := client.GetSubUser(context.TODO(), userListReq, "cce", "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt", bce.NewSignOptionWithoutAuth())
	if err != nil {
		t.Errorf("list user failed: %v", err)
	}

	if responseByte, err := json.Marshal(resp); err == nil {
		t.Logf("user result: %s", string(responseByte))
	}
}
