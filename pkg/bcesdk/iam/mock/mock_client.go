// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/iam (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	iam "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/iam"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// BatchVerifyPermissionByToken mocks base method.
func (m *MockInterface) BatchVerifyPermissionByToken(arg0 context.Context, arg1 *iam.VerifyRequest, arg2, arg3, arg4 string, arg5 *bce.SignOption) (*iam.VerifyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchVerifyPermissionByToken", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*iam.VerifyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchVerifyPermissionByToken indicates an expected call of BatchVerifyPermissionByToken.
func (mr *MockInterfaceMockRecorder) BatchVerifyPermissionByToken(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchVerifyPermissionByToken", reflect.TypeOf((*MockInterface)(nil).BatchVerifyPermissionByToken), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetAkSkByToken mocks base method.
func (m *MockInterface) GetAkSkByToken(arg0 context.Context, arg1, arg2 string, arg3 *bce.SignOption) (*iam.AccessKey, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAkSkByToken", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*iam.AccessKey)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAkSkByToken indicates an expected call of GetAkSkByToken.
func (mr *MockInterfaceMockRecorder) GetAkSkByToken(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAkSkByToken", reflect.TypeOf((*MockInterface)(nil).GetAkSkByToken), arg0, arg1, arg2, arg3)
}

// GetSubUser mocks base method.
func (m *MockInterface) GetSubUser(arg0 context.Context, arg1 *iam.UserListRequest, arg2, arg3 string, arg4 *bce.SignOption) (*iam.UserListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSubUser", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*iam.UserListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSubUser indicates an expected call of GetSubUser.
func (mr *MockInterfaceMockRecorder) GetSubUser(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSubUser", reflect.TypeOf((*MockInterface)(nil).GetSubUser), arg0, arg1, arg2, arg3, arg4)
}

// GetToken mocks base method.
func (m *MockInterface) GetToken(arg0 context.Context, arg1, arg2 string, arg3 *bce.SignOption) (*iam.Token, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetToken", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*iam.Token)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetToken indicates an expected call of GetToken.
func (mr *MockInterfaceMockRecorder) GetToken(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetToken", reflect.TypeOf((*MockInterface)(nil).GetToken), arg0, arg1, arg2, arg3)
}

// SetDebug mocks base method.
func (m *MockInterface) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug.
func (mr *MockInterfaceMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), arg0)
}
