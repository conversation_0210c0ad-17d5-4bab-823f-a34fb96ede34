package iam

import (
	"context"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/iam Interface
type Interface interface {
	SetDebug(debug bool)

	GetToken(ctx context.Context, serviceName string, password string, option *bce.SignOption) (*Token, error)
	GetAkSkByToken(ctx context.Context, serviceName string, password string, option *bce.SignOption) (*AccessKey, error)
	BatchVerifyPermissionByToken(ctx context.Context, verifyReq *VerifyRequest, subUserID, serviceName, password string, option *bce.SignOption) (*VerifyResponse, error)
	GetSubUser(ctx context.Context, request *UserListRequest, serviceName, password string, option *bce.SignOption) (*UserListResponse, error)
}

type TokenResponse struct {
	Token Token `json:"token"`
}

type Token struct {
	ID   string `json:"id"`
	User User   `json:"user"`
}

type TokenRequest struct {
	Auth Authentication `json:"auth"`
}

type Authentication struct {
	Identity Identity `json:"identity"`
	Scope    Scope    `json:"scope"`
}

type Identity struct {
	Methods  []string `json:"methods"`
	Password Password `json:"password"`
}

type Password struct {
	User User `json:"user"`
}

type User struct {
	Domain   Domain `json:"domain"`
	ID       string `json:"id"`
	Name     string `json:"name"`
	Password string `json:"password"`
}

type Domain struct {
	Name string `json:"name"`
	ID   string `json:"id"`
}

type Scope struct {
	Domain Domain `json:"domain"`
}

type AccessKeyResponse struct {
	AccessKeys []AccessKey `json:"accesskeys"`
}

type AccessKey struct {
	Access       string `json:"access"`
	Secret       string `json:"secret"`
	CredentialID string `json:"credential_id"`
	ProjectID    string `json:"project_id"`
	UserID       string `json:"user_id"`
	ModifyTime   string `json:"modify_time"`
}

type VerifyRequest struct {
	VerifyList    []VerifyEntity `json:"verify_list"`
	SecurityToken string         `json:"security_token"`
}

type VerifyEntity struct {
	Service        string         `json:"service"`
	Region         string         `json:"region"`
	Resource       []string       `json:"resource"`
	Permission     []string       `json:"permission"`
	ResourceOwner  string         `json:"resource_owner"`
	RequestContext RequestContext `json:"request_context"`
}

type RequestContext struct {
	IPAddress string            `json:"ipAddress"`
	Referer   string            `json:"referer"`
	Variables map[string]string `json:"variables"`
}

const IAMService = "bce:iam"

type VerifyResponse struct {
	VerifyResults []VerifyResult `json:"verify_results"`
}

type VerifyResult struct {
	Result []EffectEntity `json:"result"`
}

type EffectEntity struct {
	Effect Effect `json:"effect"`
}

type Effect string

const (
	EFFECT_ALLOW         Effect = "ALLOW"
	EFFECT_EXPLICIT_DENY Effect = "EXPLICIT_DENY"
	EFFECT_DEFAULT_DENY  Effect = "DEFAULT_DENY"
)

type UserListRequest struct {
	DomainID     string `json:"domainId"`
	Name         string `json:"name"`
	SubUser      bool   `json:"subuser"`
	Email        string `json:"email"`
	Provider     string `json:"provider"`
	RoleType     string `json"roleType"`
	Enabled      bool   `json:"enabled"`
	NameContians string `json:"name_contains"`
	Status       string `json:"status"`
}

type UserListResponse struct {
	UserList []UserInfo `json:"users"`
}

type UserInfo struct {
	ID                string   `json:"id"`
	Name              string   `json:"name"`
	Email             string   `json:"email"`
	Mobile            string   `json:"mobile"`
	Enabled           bool     `json:"enabled"`
	Provider          string   `json:"provider"`
	Roles             []string `json:"roles"`
	Account           string   `json:"account"`
	Subuser           bool     `json:"subuser"`
	Mobileverified    bool     `json:"mobileVerified"`
	Emailverified     bool     `json:"emailVerified"`
	Needresetpassword bool     `json:"needResetPassword"`
	Enabledlogin      bool     `json:"enabledLogin"`
	Enabledmfa        bool     `json:"enabledMfa"`
	Status            string   `json:"status"`
	DomainID          string   `json:"domain_id"`
	DefaultProjectID  string   `json:"default_project_id"`
	PublicID          string   `json:"public_id"`
}
