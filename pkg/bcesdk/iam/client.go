package iam

import (
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
)

// Endpoints IAM 相关 Endpoint
var Endpoints = map[string]string{
	"bj":      "iam.bj.bce-internal.baidu.com/v3",
	"gz":      "iam.gz.bce-internal.baidu.com/v3",
	"su":      "iam.su.bce-internal.baidu.com/v3",
	"nj":      "iam.nj.bce.baidu-int.com/v3",
	"hkg":     "iam.hkg.bce.baidu-int.com/v3",
	"fwh":     "iam.fwh.bce.baidu-int.com/v3",
	"bd":      "iam.bdbl.bce.baidu-int.com/v3",
	"sandbox": "iam.bj.internal-qasandbox.baidu-int.com/v3",
}

var _ Interface = &Client{}

// Client is the IAM client implementation for Interface.
type Client struct {
	*bce.Client
}

// NewClient return client
func NewClient(config *bce.Config) *Client {
	return &Client{
		Client: bce.NewClient(config),
	}
}

// GetURL generates the full URL of http request for Baidu Cloud  API.
func (c *Client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoints[c.GetRegion()]
	}
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}

// SetDebug open or close Debug Mode
func (c *Client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

// NewSignOption return a app IAM specified sign option
func NewSignOption() *bce.SignOption {
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")

	return option
}
