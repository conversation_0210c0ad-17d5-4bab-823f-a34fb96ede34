package iam

import (
	"strings"

	iamsdk "icode.baidu.com/baidu/bce-iam/sdk-go/iam"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/retrypolicy"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/util"
)

// Endpoints IAM 相关 Endpointh默认值，不含v3版本号
var Endpoints = map[string]string{
	"bj":      "iam.bj.bce-internal.baidu.com",
	"gz":      "iam.gz.bce-internal.baidu.com",
	"su":      "iam.su.bce-internal.baidu.com",
	"nj":      "iam.nj.bce.baidu-int.com",
	"hkg":     "iam.hkg.bce.baidu-int.com",
	"fwh":     "iam.fwh.bce.baidu-int.com",
	"bd":      "iam.bdbl.bce.baidu-int.com",
	"yq":      "iam.yq.bce-internal.sdns.baidu.com",
	"cd":      "iam.cd.bce-internal.sdns.baidu.com",
	"sandbox": "iam.bj.internal-qasandbox.baidu-int.com",
}

// AgentEndpoints IAM 前置机 相关 Endpoint默认值, 不含v3版本号
var AgentEndpoints = map[string]string{
	"bj":      "agent-iam.bj.sdns.baidu.com",
	"gz":      "agent-iam.gz.sdns.baidu.com",
	"su":      "agent-iam.su.sdns.baidu.com",
	"nj":      "agent.nj.bce.baidu.com",
	"hkg":     "agent-iam.hkg.sdns.baidu.com",
	"fwh":     "agent-iam.fwh.sdns.baidu.com",
	"bd":      "agent-iam.bd.sdns.baidu.com",
	"yq":      "agent-iam.yq.sdns.baidu.com",
	"cd":      "agent-iam.cd.sdns.baidu.com",
	"sandbox": "gzbh-sandbox19-6271.gzbh.baidu.com:8236",
}

// IAgentEnabled 该地域是否开启容灾，默认值
var AgentEnabled = map[string]bool{
	"bj":      true,
	"gz":      true,
	"su":      true,
	"nj":      true,
	"hkg":     true,
	"fwh":     true,
	"bd":      true,
	"yq":      true,
	"cd":      true,
	"sandbox": false,
}

// Client 原IAM Client，使用IAM API, 无IAM容灾
type Client struct {
	*bce.Client
}

var _ Interface = &SDKClient{}

var _ Interface = &Client{}

const (
	// Timeout 一般情况下，IAM接口P99 < 1s，IAM 前置机接口超时时间为5s
	// IAM SDK Client的非前置机接口应当增加超时控制，在IAM失效时保证cluster-service整体请求不超时（<28s）
	// IAM SDK Client的前置机接口超时时间为5s，只在cluster-service获取集群列表时使用，一般不影响整体请求时间
	Timeout int = 5

	// CacheExpiration 缓存有效时间(s)
	CacheExpiration int = 7200

	// CacheUpdateTime 缓存更新时间(s)
	CacheUpdateTime int = 3600

	ServiceDomain string = "Default"
)

// NewClient return IAMClient/IAMSDKClient
// IAMClient: 原IAM Client,使用IAM API, 无IAM容灾
// IAMSDKClient: IAM SDK Client,使用IAM SDK, 启用IAM容灾
func NewClient(config *bce.Config) Interface {
	region := config.Region
	var defaultDisasterToleranceEnabled bool = false

	// init IAM Disaster Tolerance Config
	// iamDisasterToleranceEnabled: bce.Config传参的容灾开关
	// defaultDisasterToleranceEnabled: 容灾开关默认值
	iamDisasterToleranceEnabled := config.IAMDisasterToleranceEnabled
	if region != "" {
		defaultDisasterToleranceEnabled = AgentEnabled[region]
	}

	// 传入参数和默认值只要有一个为true，开启IAM容灾
	// cce-stack涉及IAM Client调用较多，分布零散，避免部分存量或增量调用没有传入IAM容灾参数，使用默认值兜底，默认值为true开启IAM容灾
	if iamDisasterToleranceEnabled || defaultDisasterToleranceEnabled {
		agentEndpoint := config.IAMAgentEndpoint

		// ensure agentEndpoint is not empty
		if agentEndpoint == "" && region != "" {
			agentEndpoint = AgentEndpoints[region]
		}

		agentConfig := &iamsdk.AgentConfiguration{
			Endpoint: AddHTTPProtocol(agentEndpoint),
			IsEnable: true,
		}

		return NewSDKClient(config, agentConfig)
	}
	return NewIAMClient(config)
}

func NewIAMClient(config *bce.Config) *Client {
	// 原IAM Client需要增加v3版本号
	config.Endpoint = AddVersionV3(config.Endpoint)
	return &Client{
		Client: bce.NewClient(config),
	}
}

// GetURL generates the full URL of http request for Baidu Cloud  API.
func (c *Client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoints[c.GetRegion()]
	}
	// 原IAM Client需要增加v3版本号
	host = AddVersionV3(host)
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}

// SetDebug open or close Debug Mode
func (c *Client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

// NewSignOption return a app IAM specified sign option
func NewSignOption() *bce.SignOption {
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")

	return option
}

// IAMSDKClient is the IAM client implementation for Interface using IAM SDK with disaster tolerance
type SDKClient struct {
	*bce.Client
	IAMBCEClient *iamsdk.BceClient
}

// NewIAMSDKClient return IAM SDK client
func NewSDKClient(config *bce.Config, agentConfig *iamsdk.AgentConfiguration) *SDKClient {
	// convert retrypolicy.RetryPolicy to iamsdk.Retry
	var retry iamsdk.RetryPolicy = iamsdk.NewNoRetryPolicy()
	if config.RetryPolicy != nil {
		maxErrorRetry := config.RetryPolicy.GetMaxErrorRetry()
		maxDelay := config.RetryPolicy.GetMaxDelayInSeconds()
		_, isIntervalRetry := config.RetryPolicy.(*retrypolicy.IntervalRetryPolicy)

		if maxErrorRetry > 0 {
			if isIntervalRetry {
				// fixed delay
				retry = iamsdk.NewBackOffRetryPolicy(maxErrorRetry, int64(maxDelay*1000), int64(maxDelay*1000))
			} else {
				// increasing delay, from baseDelay to maxDelay
				retry = iamsdk.NewBackOffRetryPolicy(maxErrorRetry, int64(maxDelay*1000), int64(300))
			}
		}
	}

	region := config.Region
	iamEndpoint := config.Endpoint

	// ensure IAMEndpoint is not empty
	if iamEndpoint == "" && region != "" {
		iamEndpoint = Endpoints[region]
	}

	// ensure IAMEndpoint is without v3  version suffix
	iamEndpoint = RemoveVersionV3(iamEndpoint)

	// init IAM SDK client Config
	iamConfig := &iamsdk.BceClientConfiguration{
		Endpoint: AddHTTPProtocol(iamEndpoint),
		Version:  "/v3",
		Domain:   ServiceDomain,
		Retry:    retry,
		// IAM SDK Client的非前置机接口超时时间最多为5s，在IAM失效时保证cluster-service整体请求不超时（<28s）
		Timeout:  Timeout,
		UserName: config.ServiceName,
		Password: config.ServicePassword,
	}

	return &SDKClient{
		Client:       bce.NewClient(config),
		IAMBCEClient: iamsdk.NewBceClientWithAgent(iamConfig, agentConfig),
	}
}

// GetURL generates the full URL of http request for Baidu Cloud  API.
func (c *SDKClient) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = AddHTTPProtocol(Endpoints[c.GetRegion()])
	}
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}

// SetDebug open or close Debug Mode
func (c *SDKClient) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

// AddHTTPProtocol 添加 http 前缀
func AddHTTPProtocol(endpoint string) string {
	if strings.HasPrefix(endpoint, "https://") ||
		strings.HasPrefix(endpoint, "http://") {
		return endpoint
	}

	return "http://" + endpoint
}

// AddVersionV3 添加 /v3 版本号后缀
func AddVersionV3(endpoint string) string {
	if strings.HasSuffix(endpoint, "/v3/") {
		return endpoint[:len(endpoint)-1]
	}
	if strings.HasSuffix(endpoint, "/v3") {
		return endpoint
	}
	if strings.HasSuffix(endpoint, "/") {
		return endpoint + "v3"
	}
	return endpoint + "/v3"
}

// RemoveV3Version 删除 /v3 版本号后缀
func RemoveVersionV3(endpoint string) string {
	// special case of aihc
	if strings.HasSuffix(endpoint, "%2Fv3") {
		return endpoint[:len(endpoint)-5]
	}
	if strings.HasSuffix(endpoint, "/v3/") {
		return endpoint[:len(endpoint)-4]
	}
	if strings.HasSuffix(endpoint, "/v3") {
		return endpoint[:len(endpoint)-3]
	}

	return endpoint
}
