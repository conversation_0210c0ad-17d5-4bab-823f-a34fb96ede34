package iam

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/pkg/logger"
)

func (c *Client) GetToken(ctx context.Context, serviceName string, password string, option *bce.SignOption) (*Token, error) {
	params := map[string]string{}

	tokenRequest := TokenRequest{
		Auth: Authentication{
			Identity: Identity{
				Methods: []string{
					"password",
				},
				Password: Password{
					User: User{
						Domain: Domain{
							Name: "Default",
						},
						Name:     serviceName,
						Password: password,
					},
				},
			},
			Scope: Scope{
				Domain: Domain{
					ID: "default",
				},
			},
		},
	}

	postContent, err := json.Marshal(tokenRequest)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("auth/tokens", params), bytes.<PERSON><PERSON>er(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var tokenResponse *TokenResponse
	err = json.Unmarshal(bodyContent, &tokenResponse)
	if err != nil {
		return nil, err
	}
	token := tokenResponse.Token
	token.ID = resp.Header.Get("X-Subject-Token")

	return &token, nil
}

func (c *Client) GetAkSkByToken(ctx context.Context, serviceName string, password string, option *bce.SignOption) (*AccessKey, error) {
	token, err := c.GetToken(ctx, serviceName, password, bce.NewSignOptionWithoutAuth())
	if err != nil {
		return nil, err
	}
	option.AddHeader("X-Auth-Token", token.ID)

	params := map[string]string{}
	url := fmt.Sprintf("users/%s/accesskeys", token.User.ID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), bytes.NewBuffer(nil))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var accessKeyResp *AccessKeyResponse
	err = json.Unmarshal(bodyContent, &accessKeyResp)
	if err != nil {
		return nil, err
	}

	return &accessKeyResp.AccessKeys[0], nil
}

func (c *Client) BatchVerifyPermissionByToken(ctx context.Context, verifyReq *VerifyRequest, subUserID, serviceName, password string,
	option *bce.SignOption) (*VerifyResponse, error) {
	if verifyReq == nil || len(verifyReq.VerifyList) == 0 {
		return nil, errors.New("verifyReq is nil")
	}

	token, err := c.GetToken(ctx, serviceName, password, bce.NewSignOptionWithoutAuth())
	if err != nil {
		return nil, err
	}
	option.AddHeader("X-Auth-Token", token.ID)

	params := map[string]string{}
	url := fmt.Sprintf("users/%s/batch_permissions", subUserID)

	postContent, err := json.Marshal(verifyReq)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var verifyResp VerifyResponse
	err = json.Unmarshal(bodyContent, &verifyResp)
	if err != nil {
		return nil, err
	}

	return &verifyResp, nil
}

func (c *Client) GetSubUser(ctx context.Context, request *UserListRequest, serviceName, password string, option *bce.SignOption) (*UserListResponse, error) {
	if request == nil {
		return nil, errors.New("request is nil")
	}

	token, err := c.GetToken(ctx, serviceName, password, bce.NewSignOptionWithoutAuth())
	if err != nil {
		return nil, err
	}
	option.AddHeader("X-Auth-Token", token.ID)

	option.AddHeader("X-Subuser-Support", "true")
	params := map[string]string{}

	if request.DomainID != "" {
		params["domain_id"] = request.DomainID
	}

	params["subuser"] = strconv.FormatBool(request.SubUser)

	req, err := bce.NewRequest("GET", c.GetURL("/users", params), bytes.NewBuffer(nil))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var userListResp UserListResponse
	err = json.Unmarshal(bodyContent, &userListResp)
	if err != nil {
		return nil, err
	}

	return &userListResp, nil
}

// IAMEncrypt 函数用于调用百度云的IAM服务进行加密操作
// 参数：
// ctx：上下文对象，用于控制请求的取消和超时
// request：IAMEncryptRequest类型的指针，包含加密请求的参数
// serviceName：服务名称，用于获取认证token
// password：服务密码，用于获取认证token
// option：bce.SignOption类型的指针，包含请求签名选项
// 返回值：
// *EncryptResponse：EncryptResponse类型的指针，包含加密响应的结果
// error：如果发生错误，则返回非零的错误码；否则返回nil
func (c *Client) IAMEncrypt(ctx context.Context, request *EncryptRequest, serviceName, password string, option *bce.SignOption) (*EncryptResponse, error) {
	if request == nil {
		return nil, errors.New("request is nil")
	}

	token, err := c.GetToken(ctx, serviceName, password, bce.NewSignOptionWithoutAuth())
	if err != nil {
		return nil, err
	}
	option.AddHeader("X-Auth-Token", token.ID)

	logger.Infof(ctx, "X-Auth-Token %v", token.ID)

	params := map[string]string{
		"encrypt": "null",
	}

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("/BCE-CRED/ciphers", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	logger.Infof(ctx, "IAMEncryptReq %v", req)
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var EncryptResp EncryptResponse
	err = json.Unmarshal(bodyContent, &EncryptResp)
	if err != nil {
		return nil, err
	}

	return &EncryptResp, nil
}

// IAMDecrypt 函数用于调用百度云的IAM服务进行解密操作
// 参数：
// ctx：上下文对象，用于控制请求的取消和超时
// request：IAMDecryptRequest类型的指针，包含加密请求的参数
// serviceName：服务名称，用于获取认证token
// password：服务密码，用于获取认证token
// option：bce.SignOption类型的指针，包含请求签名选项
// 返回值：
// *DecryptResponse：DecryptResponse，包含加密响应的结果
// error：如果发生错误，则返回非零的错误码；否则返回nil
func (c *Client) IAMDecrypt(ctx context.Context, request *DecryptRequest, serviceName, password string, option *bce.SignOption) (*DecryptResponse, error) {
	if request == nil {
		return nil, errors.New("request is nil")
	}

	token, err := c.GetToken(ctx, serviceName, password, bce.NewSignOptionWithoutAuth())
	if err != nil {
		return nil, err
	}
	option.AddHeader("X-Auth-Token", token.ID)

	logger.Infof(ctx, "X-Auth-Token %v", token.ID)

	params := map[string]string{
		"decrypt": "null",
	}

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("/BCE-CRED/ciphers", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	logger.Infof(ctx, "IAMDecryptReq %v", req)
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var EncryptResp DecryptResponse
	err = json.Unmarshal(bodyContent, &EncryptResp)
	if err != nil {
		return nil, err
	}

	return &EncryptResp, nil
}

func (c *Client) GetUserBcePolicy(ctx context.Context, userID string, serviceName, password string, option *bce.SignOption) (
	*UserBcePolicyListResponse, error) {
	logger.Infof(ctx, "START TO GET: X-Auth-Token")

	token, err := c.GetToken(ctx, serviceName, password, bce.NewSignOptionWithoutAuth())
	if err != nil {
		return nil, err
	}
	// option.AddHeader("X-Auth-Token", token.ID)
	// option.AddHeader("X-Subuser-Support", "true")
	logger.Infof(ctx, "X-Auth-Token %v", token.ID)
	param := map[string]string{
		"domain_id":   "default",
		"policy_type": "System",
	}

	urlPath := fmt.Sprintf("/users/%s/bcepolicy", userID)
	if !strings.HasSuffix(c.Endpoint, "/v3") {
		urlPath = "/v3" + urlPath
	}
	req, err := bce.NewRequest(http.MethodGet, c.GetURL(urlPath, param), bytes.NewBuffer(nil))
	if err != nil {
		return nil, err
	}
	reqHeader := map[string]string{
		"X-Auth-Token":      token.ID,
		"X-Subuser-Support": "true",
	}
	// req.AddHeaders(reqHeader)
	req.SetHeaders(reqHeader)

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	logger.Infof(ctx, "GetUserBcePolicy bodyContent: %v", string(bodyContent))

	listResp := UserBcePolicyListResponse{}
	if err = json.Unmarshal(bodyContent, &listResp); err != nil {
		return nil, err
	}
	return &listResp, nil
}
