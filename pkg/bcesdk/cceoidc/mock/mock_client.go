// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/cceoidc (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	cceoidc "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/cceoidc"
	reflect "reflect"
)

// MockInterface is a mock of Interface interface
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// NewToken mocks base method
func (m *MockInterface) NewToken(arg0 context.Context, arg1 *cceoidc.GetTokenRequest, arg2 *bce.SignOption) (*cceoidc.Token, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewToken", arg0, arg1, arg2)
	ret0, _ := ret[0].(*cceoidc.Token)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewToken indicates an expected call of NewToken
func (mr *MockInterfaceMockRecorder) NewToken(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewToken", reflect.TypeOf((*MockInterface)(nil).NewToken), arg0, arg1, arg2)
}

// RefreshToken mocks base method
func (m *MockInterface) RefreshToken(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*cceoidc.Token, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RefreshToken", arg0, arg1, arg2)
	ret0, _ := ret[0].(*cceoidc.Token)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RefreshToken indicates an expected call of RefreshToken
func (mr *MockInterfaceMockRecorder) RefreshToken(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshToken", reflect.TypeOf((*MockInterface)(nil).RefreshToken), arg0, arg1, arg2)
}
