package cceoidc

import (
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/util"
)

var Endpoints = map[string]string{
	"bj":      "",
	"gz":      "*************:8848",
	"su":      "",
	"hkg":     "",
	"fwh":     "",
	"bd":      "",
	"sandbox": "",
}

var _ Interface = &Client{}

type Client struct {
	*bce.Client
}

func NewClient(config *bce.Config) *Client {
	bceClient := bce.NewClient(config)
	return &Client{Client: bceClient}
}

func (c *Client) GetURL(objectKey string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoints[c.GetRegion()]
	}
	uriPath := objectKey

	return c.Client.GetURL(host, uriPath, params)
}

func NewSignOption() *bce.SignOption {
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")

	return option
}
