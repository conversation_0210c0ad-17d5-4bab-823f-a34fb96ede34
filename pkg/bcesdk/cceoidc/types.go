package cceoidc

import (
	"context"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/cceoidc Interface

type Interface interface {
	NewToken(ctx context.Context, request *GetTokenRequest, option *bce.SignOption) (*Token, error)
	RefreshToken(ctx context.Context, refreshToken string, option *bce.SignOption) (*Token, error)
}

type GetTokenRequest struct {
	// ClientID 对应集群ID
	ClientID string `json:"client_id"`

	UserID string `json:"user_id"`

	// RoleID 请求者扮演的角色, 可为空
	RoleID string `json:"role_id,omitempty"`

	// RoleOwnerID 角色的所有者, 创建角色的主用户, 可为空
	RoleOwnerID string `json:"role_owner_id,omitempty"`

	Scope Scope `json:"scope"`
}

type Scope string

const (
	ScopeUser  Scope = "user"
	ScopeGroup Scope = "group"
)

type Token struct {
	IDToken      string `json:"id_token"`
	TokenType    string `json:"token_type"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    string `json:"expires_in"`
	ClientSecret string `json:"client_secret"`
}
