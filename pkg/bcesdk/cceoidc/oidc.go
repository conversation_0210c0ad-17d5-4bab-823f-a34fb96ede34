package cceoidc

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/url"
	"strings"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

func (c *Client) NewToken(ctx context.Context, request *GetTokenRequest, option *bce.SignOption) (*Token, error) {
	if request == nil {
		return nil, errors.New("GetTokenRequest is nil")
	}

	params := map[string]string{}

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("/oidc/new_token", params), bytes.New<PERSON>uffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	token := new(Token)
	err = json.Unmarshal(bodyContent, token)
	if err != nil {
		return nil, err
	}

	return token, nil
}

func (c *Client) RefreshToken(ctx context.Context, refreshToken string, option *bce.SignOption) (*Token, error) {
	if refreshToken == "" {
		return nil, errors.New("refreshToken is empty")
	}

	if option == nil {
		return nil, errors.New("option is nil")
	}

	params := map[string]string{}

	data := url.Values{}
	data.Set("grant_type", "refresh_token")
	data.Set("refresh_token", refreshToken)
	req, err := bce.NewRequest("POST", c.GetURL("/oidc/token", params), strings.NewReader(data.Encode()))
	if err != nil {
		return nil, err
	}

	option.PostForm = true
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	token := new(Token)
	err = json.Unmarshal(bodyContent, token)
	if err != nil {
		return nil, err
	}

	return token, nil
}
