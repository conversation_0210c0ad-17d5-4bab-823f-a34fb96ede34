package eccr

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

type InstanceInterface interface {
	GetInstanceDetail(ctx context.Context, instanceID string, opt *bce.SignOption) (*GetInstanceDetailResponse, error)
}

// GetInstanceDetail - get a specific instance detail info
//
// PARAMS:
//   - instanceID: the specific instance ID
//
// RETURNS:
//   - *GetInstanceDetailResponse: the result of get instance detail info
//   - error: nil if success otherwise the specific error
func (c *Client) GetInstanceDetail(ctx context.Context, instanceID string, opt *bce.SignOption) (*GetInstanceDetailResponse, error) {
	if instanceID == "" {
		return nil, errors.New("instance is empty")
	}

	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("instances/%s", instanceID), nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r GetInstanceDetailResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}

	return &r, nil
}
