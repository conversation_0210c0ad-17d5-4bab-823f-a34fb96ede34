package eccr

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

type NetworkInterface interface {
	ListPrivateNetworks(ctx context.Context, instanceID string, opt *bce.SignOption) (*ListPrivateNetworksResponse, error)
}

// ListPrivateNetworks - list all Privatelinks in an instance with the specific parameters
//
// PARAMS:
//   - instanceID: the specific instance ID
//
// RETURNS:
//   - *ListPrivateNetworksResponse: the result of list Privatelinks
//   - error: nil if success otherwise the specific error
func (c *Client) ListPrivateNetworks(ctx context.Context, instanceID string, opt *bce.SignOption) (*ListPrivateNetworksResponse, error) {
	if instanceID == "" {
		return nil, errors.New("instance is empty")
	}

	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("instances/%s/privatelinks", instanceID), nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListPrivateNetworksResponse
	if err := json.Unmarshal(bodyContent, &r); err != nil {
		return nil, err
	}

	return &r, nil
}
