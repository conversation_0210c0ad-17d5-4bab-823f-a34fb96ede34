package eccr

import "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"

var _ Interface = &Client{}

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eccr Interface

// Interface 定义 ECCR SDK
// 新增 Interface 后注意更新 doc.go !!!!!!!
type Interface interface {
	SetDebug(debug bool)

	InstanceInterface
	NetworkInterface
}

// Endpoints - CCR 各地域 Endpoints
var Endpoints = map[string]string{
	"bj":  "ccr.bj.baidubce.com",
	"gz":  "ccr.gz.baidubce.com",
	"su":  "ccr.su.baidubce.com",
	"hkg": "ccr.hkg.baidubce.com",
	"fwh": "ccr.fwh.baidubce.com",
	"bd":  "ccr.bd.baidubce.com",
}

// Client 实现 ccr.Interface
type Client struct {
	*bce.Client
}

// NewClient client of CCE
func NewClient(config *bce.Config) *Client {
	return &Client{
		Client: bce.NewClient(config),
	}
}

// SetDebug 是否开启 debug 模式
func (c *Client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

// GetURL generates the full URL of http request for Baidu Cloud BOS API.
func (c *Client) GetURL(uriPath string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoints[c.GetRegion()]
	}
	return c.Client.GetURL(host, uriPath, params)
}
