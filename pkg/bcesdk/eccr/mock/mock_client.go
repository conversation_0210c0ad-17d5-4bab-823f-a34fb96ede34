// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eccr (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	eccr "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/eccr"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// GetInstanceDetail mocks base method.
func (m *MockInterface) GetInstanceDetail(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*eccr.GetInstanceDetailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceDetail", arg0, arg1, arg2)
	ret0, _ := ret[0].(*eccr.GetInstanceDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceDetail indicates an expected call of GetInstanceDetail.
func (mr *MockInterfaceMockRecorder) GetInstanceDetail(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceDetail", reflect.TypeOf((*MockInterface)(nil).GetInstanceDetail), arg0, arg1, arg2)
}

// ListPrivateNetworks mocks base method.
func (m *MockInterface) ListPrivateNetworks(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*eccr.ListPrivateNetworksResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPrivateNetworks", arg0, arg1, arg2)
	ret0, _ := ret[0].(*eccr.ListPrivateNetworksResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPrivateNetworks indicates an expected call of ListPrivateNetworks.
func (mr *MockInterfaceMockRecorder) ListPrivateNetworks(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPrivateNetworks", reflect.TypeOf((*MockInterface)(nil).ListPrivateNetworks), arg0, arg1, arg2)
}

// SetDebug mocks base method.
func (m *MockInterface) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug.
func (mr *MockInterfaceMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), arg0)
}
