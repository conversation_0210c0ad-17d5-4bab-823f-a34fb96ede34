// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/11/05 , by <EMAIL>, create
*/
/*
实现 CCE V2 SDK Helm account 相关方法
*/

package helm

import (
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/inspect"
)

// CheckUserNameExist - 检查用户名是否存在
// PARAMS:
//   - ctx: The context to trace request
//   - userName: 用户名
//
// RETURNS:
//
//	ExistResponse: existResponse
//	error: nil if succeed, error if fail
func (c *Client) CheckUserNameExist(ctx context.Context, userName string) (bool, error) {
	if userName == "" {
		return false, errors.New("userName is nil")
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/account/"+userName, nil), nil)
	if err != nil {
		return false, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "检查用户名是否存在",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return false, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return false, err
	}

	var existResponse ExistResponse
	err = json.Unmarshal(bodyContent, &existResponse)
	if err != nil {
		return false, err
	}
	return existResponse.IsExist, nil
}

// GetPrivateRepoURL - 获取当前账号私有仓库地址
// PARAMS:
//   - ctx: The context to trace request
//
// RETURNS:
//
//	string: repo url
//	error: nil if succeed, error if fail
func (c *Client) GetPrivateRepoURL(ctx context.Context) (string, error) {
	req, err := bce.NewRequest("GET", c.GetURL("v1/account", nil), nil)
	if err != nil {
		return "", err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取当前账号私有仓库地址",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return "", err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}

	var privateRepoResponse PrivateRepoResponse
	err = json.Unmarshal(bodyContent, &privateRepoResponse)
	if err != nil {
		return "", err
	}
	return privateRepoResponse.PrivateRepoURL, nil
}
