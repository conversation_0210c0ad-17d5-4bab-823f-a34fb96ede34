// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/11/05, by <EMAIL>, create
*/
/*
CCE V2 版本 GO SDK, helm 定义
*/
package helm

import (
	"context"
)

// Interface - 定义 Helm SDK
type Interface interface {
	// account
	CheckUserNameExist(ctx context.Context, userName string) (bool, error)

	GetPrivateRepoURL(ctx context.Context) (string, error)

	// chart
	GetPublicRepoChartList(ctx context.Context, pageNo string, pageSize string, keyword string) (*ChartList, error)

	GetPrivateRepoChartList(ctx context.Context, pageNo string, pageSize string, keyword string) (*ChartList, error)
	GetPublicRepoChartDetail(ctx context.Context, chartName string) (*ChartDetail, error)
	GetPrivateRepoChartDetail(ctx context.Context, chartName string) (*ChartDetail, error)
	GetPublicRepoChartVersionDetail(ctx context.Context, chartName string, version string) (*ChartVersionDetail, error)
	GetPrivateRepoChartVersionDetail(ctx context.Context, chartName string, version string) (*ChartVersionDetail, error)
	GetPublicRepoChartReadme(ctx context.Context, chartName string, version string) (string, error)
	GetPrivateRepoChartReadme(ctx context.Context, chartName string, version string) (string, error)
	GetPublicRepoChartYaml(ctx context.Context, chartName string, version string) (string, error)
	GetPrivateRepoChartYaml(ctx context.Context, chartName string, version string) (string, error)
	CheckVersionOfChartExist(ctx context.Context, chartName string, version string) (bool, error)
	UploadPrivateChart(ctx context.Context, request []byte) (bool, error)
	DeletePrivateRepoChartVersion(ctx context.Context, chartName string, version string) (bool, error)

	// release
	InstallRelease(ctx context.Context, clusterUuid string, namespace string, request *ReleaseInstallRequest) error

	GetUserReleaseList(ctx context.Context, clusterUuid string, namespace string, pageNo string, pageSize string, keyword string) (*ReleaseList, error)
	GetReleaseDetail(ctx context.Context, clusterUuid string, namespace string, name string) (*ReleaseDetail, error)
	GetReleaseYaml(ctx context.Context, clusterUuid string, namespace string, name string, revision string) (string, error)
	UpgradeRelease(ctx context.Context, clusterUuid string, namespace string, name string, request *ReleaseUpgradeRequest) error
	DeleteRelease(ctx context.Context, clusterUuid string, namespace string, name string) error
	RollbackRelease(ctx context.Context, clusterUuid string, namespace string, name string, revision string) error

	// tiller
	InstallHelmService(ctx context.Context, clusterUuid string) error
}

type ExistResponse struct {
	IsExist bool `json:"exist"`
}

type PrivateRepoResponse struct {
	PrivateRepoURL string `json:"privateRepoURL"`
}

type MaintainerInfo struct {
	Email string `json:"email"`
	Name  string `json:"name"`
	Url   string `json:"url"`
}

type ChartInfo struct {
	Name        string           `json:"name"`
	Home        string           `json:"home"`
	Icon        string           `json:"icon"`
	Created     string           `json:"created"`
	AppVersion  string           `json:"appVersion"`
	Version     string           `json:"version"`
	Description string           `json:"description"`
	Maintainers []MaintainerInfo `json:"maintainers"`
}

type ChartList struct {
	Total int         `json:"total"`
	List  []ChartInfo `json:"list"`
}

type HistoryVersion struct {
	Created string `json:"created"`
	Version string `json:"version"`
}

type ChartDetail struct {
	Name        string           `json:"name"`
	Home        string           `json:"home"`
	Icon        string           `json:"icon"`
	AppVersion  string           `json:"appVersion"`
	Description string           `json:"description"`
	Maintainers []MaintainerInfo `json:"maintainers"`
	History     []HistoryVersion `json:"history"`
}

type ChartVersionDetail struct {
	Name          string           `json:"name"`
	Home          string           `json:"home"`
	Icon          string           `json:"icon"`
	AppVersion    string           `json:"appVersion"`
	Description   string           `json:"description"`
	Condition     string           `json:"condition"`
	Created       string           `json:"created"`
	Digest        string           `json:"digest"`
	Engine        string           `json:"engine"`
	KubeVersion   string           `json:"kubeVersion"`
	Tags          string           `json:"tags"`
	TillerVersion string           `json:"tillerVersion"`
	Version       string           `json:"version"`
	Deprecated    bool             `json:"deprecated"`
	Keywords      []string         `json:"keywords"`
	Source        []string         `json:"source"`
	Maintainers   []MaintainerInfo `json:"maintainers"`
}

type DataResponse struct {
	Data string `json:"data"`
}

type UploadResponse struct {
	Saved bool `json:"saved"`
}

type DeleteResponse struct {
	Deleted bool `json:"deleted"`
}

type ReleaseInstallRequest struct {
	ChartName    string `json:"chartName"`
	ChartVersion string `json:"chartVersion"`
	Description  string `json:"description"`
	Name         string `json:"name"`
	Values       string `json:"values"`
	IsPublic     bool   `json:"public"`
}

type ReleaseInfo struct {
	AppVersion   string `json:"AppVersion"`
	Chart        string `json:"Chart"`
	ChartName    string `json:"ChartName"`
	ChartVersion string `json:"ChartVersion"`
	Name         string `json:"Name"`
	Namespace    string `json:"Namespace"`
	Status       string `json:"Status"`
	Updated      string `json:"Updated"`
	Revision     int    `json:"Revision"`
}

type ReleaseList struct {
	Total int           `json:"total"`
	List  []ReleaseInfo `json:"list"`
}

type ReleaseHistory struct {
	Chart       string `json:"Chart"`
	Description string `json:"Description"`
	Status      string `json:"Status"`
	Updated     string `json:"Updated"`
	Revision    int    `json:"Revision"`
}

type ReleaseManifest struct {
	Kind      string `json:"Kind"`
	Link      string `json:"Link"`
	Literal   string `json:"Literal"`
	Name      string `json:"Name"`
	Namespace string `json:"Namespace"`
}

type ReleaseDetail struct {
	Description     string            `json:"Description"`
	Values          string            `json:"Values"`
	FromPublicChart bool              `json:"FromPublicChart"`
	Basic           ReleaseInfo       `json:"Basic"`
	History         []ReleaseHistory  `json:"History"`
	Manifest        []ReleaseManifest `json:"Manifest"`
}

type ReleaseUpgradeRequest struct {
	ChartName    string `json:"chartName"`
	ChartVersion string `json:"chartVersion"`
	Description  string `json:"description"`
	Values       string `json:"values"`
	IsPublic     bool   `json:"public"`
}
