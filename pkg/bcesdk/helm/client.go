// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/11/04, by <EMAIL>, create
*/
/*
helm
*/

package helm

import (
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

var _ Interface = &Client{}

// Endpoint contains all endpoints of Baidu Cloud CCE helm.
var Endpoint = map[string]string{
	"bj":      "http://10.180.114.235:8086",
	"gz":      "http://10.169.25.216:8086",
	"gz_test": "http://10.164.32.142:8086",
	"su":      "http://10.191.105.71:8086",
	"hkg":     "http://10.70.8.45:8086",
	"fwh":     "http://***********:8086",
	"bd":      "http://***********:8086",
}

// Client 实现 helm.Interface
type Client struct {
	*bce.Client
}

// NewClient client
func NewClient(config *bce.Config) *Client {
	return &Client{
		Client: bce.NewClient(config),
	}
}

// SetDebug 是否开启 debug 模式
func (c *Client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

// GetURL generates the full URL of http request for Baidu Cloud API.
func (c *Client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	uriPath := version
	c.APIVersion = ""
	return c.Client.GetURL(host, uriPath, params)
}
