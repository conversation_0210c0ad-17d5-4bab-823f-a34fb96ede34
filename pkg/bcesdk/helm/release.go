// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/11/05 , by <EMAIL>, create
*/
/*
实现 CCE V2 SDK Helm release 相关方法
*/

package helm

import (
	"bytes"
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/inspect"
)

// InstallRelease - 安装release
// PARAMS:
//   - ctx: The context to trace request
//   - clusterUuid: release所在集群
//   - namespace: release所在命名空间
//   - request: release安装请求
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) InstallRelease(ctx context.Context, clusterUuid string, namespace string, request *ReleaseInstallRequest) error {
	params := map[string]string{
		"clusterUuid": clusterUuid,
		"namespace":   namespace,
	}

	postContent, err := json.Marshal(&request)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v1/release", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "安装release",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return err
	}
	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}
	return nil
}

// GetUserReleaseList - 获取用户release列表
// PARAMS:
//   - ctx: The context to trace request
//   - clusterUuid: release所在集群
//   - namespace: release所在命名空间
//   - pageNo: 页码
//   - pageSize: 单页大小
//   - keyword: 关键字
//
// RETURNS:
//
//	ReleaseList: release列表
//	error: nil if succeed, error if fail
func (c *Client) GetUserReleaseList(ctx context.Context, clusterUuid string, namespace string, pageNo string, pageSize string, keyword string) (*ReleaseList, error) {
	params := map[string]string{
		"clusterUuid": clusterUuid,
		"namespace":   namespace,
		"pageNo":      pageNo,
		"pageSize":    pageSize,
		"keyword":     keyword,
	}
	if keyword != "" {
		params["keywordType"] = "Name"
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/release", params), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取用户release列表",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var releaseList ReleaseList
	err = json.Unmarshal(bodyContent, &releaseList)
	if err != nil {
		return nil, err
	}
	return &releaseList, nil
}

// GetReleaseDetail - 获取指定release详情
// PARAMS:
//   - ctx: The context to trace request
//   - clusterUuid: release所在集群
//   - namespace: release所在命名空间
//   - name: release名字
//
// RETURNS:
//
//	ReleaseDetail: release详情
//	error: nil if succeed, error if fail
func (c *Client) GetReleaseDetail(ctx context.Context, clusterUuid string, namespace string, name string) (*ReleaseDetail, error) {
	params := map[string]string{
		"clusterUuid": clusterUuid,
		"namespace":   namespace,
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/release/"+name, params), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var releaseDetail ReleaseDetail
	err = json.Unmarshal(bodyContent, &releaseDetail)
	if err != nil {
		return nil, err
	}
	return &releaseDetail, nil
}

// GetReleaseYaml - 获取指定release yaml
// PARAMS:
//   - ctx: The context to trace request
//   - clusterUuid: release所在集群
//   - namespace: release所在命名空间
//   - name: release名字
//   - revision: release版本
//
// RETURNS:
//
//	string: release yaml
//	error: nil if succeed, error if fail
func (c *Client) GetReleaseYaml(ctx context.Context, clusterUuid string, namespace string, name string, revision string) (string, error) {
	params := map[string]string{
		"clusterUuid": clusterUuid,
		"namespace":   namespace,
		"revision":    revision,
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/release/"+name+"/values", params), nil)
	if err != nil {
		return "", err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return "", err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}

	var dataResponse DataResponse
	err = json.Unmarshal(bodyContent, &dataResponse)
	if err != nil {
		return "", err
	}
	return dataResponse.Data, nil
}

// UpgradeRelease - 更新release
// PARAMS:
//   - ctx: The context to trace request
//   - clusterUuid: release所在集群
//   - namespace: release所在命名空间
//   - name: release名字
//   - request: release更新请求
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) UpgradeRelease(ctx context.Context, clusterUuid string, namespace string, name string, request *ReleaseUpgradeRequest) error {
	params := map[string]string{
		"clusterUuid": clusterUuid,
		"namespace":   namespace,
	}

	postContent, err := json.Marshal(&request)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("v1/release/"+name, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return err
	}
	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}
	return nil
}

// DeleteRelease - 删除release
// PARAMS:
//   - ctx: The context to trace request
//   - clusterUuid: release所在集群
//   - namespace: release所在命名空间
//   - name: release名字
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) DeleteRelease(ctx context.Context, clusterUuid string, namespace string, name string) error {
	params := map[string]string{
		"clusterUuid": clusterUuid,
		"namespace":   namespace,
	}

	req, err := bce.NewRequest("DELETE", c.GetURL("v1/release/"+name, params), nil)
	if err != nil {
		return err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "删除release",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return err
	}
	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}
	return nil
}

// RollbackRelease - 回滚release
// PARAMS:
//   - ctx: The context to trace request
//   - clusterUuid: release所在集群
//   - namespace: release所在命名空间
//   - name: release名字
//   - revision: release版本
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) RollbackRelease(ctx context.Context, clusterUuid string, namespace string, name string, revision string) error {
	params := map[string]string{
		"clusterUuid": clusterUuid,
		"namespace":   namespace,
		"revision":    revision,
	}

	req, err := bce.NewRequest("POST", c.GetURL("v1/release/"+name+"/rollback", params), nil)
	if err != nil {
		return err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return err
	}
	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}
	return nil
}
