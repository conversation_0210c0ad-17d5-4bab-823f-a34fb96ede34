// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/11/05 , by <EMAIL>, create
*/
/*
实现 CCE V2 SDK Helm tiller 相关方法
*/

package helm

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// InstallHelmService - 部署helm客户端（旧集群需要，新集群不需要）
// PARAMS:
//   - ctx: The context to trace request
//   - clusterUuid: 集群名
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) InstallHelmService(ctx context.Context, clusterUuid string) error {
	if clusterUuid == "" {
		return errors.New("clusterUuid is nil")
	}

	params := map[string]string{
		"clusterUuid": clusterUuid,
	}

	req, err := bce.NewRequest("POST", c.GetURL("v1/tiller/", params), nil)
	if err != nil {
		return err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return err
	}
	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}
	return nil
}
