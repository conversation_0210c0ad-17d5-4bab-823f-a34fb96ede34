// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/11/05 , by <EMAIL>, create
*/
/*
实现 CCE V2 SDK Helm chart 相关方法
*/

package helm

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/inspect"
)

// GetPublicRepoChartList - 获取共有仓库chart列表
// PARAMS:
//   - ctx: The context to trace request
//   - pageNo: 页码
//   - pageSize: 单页大小
//   - keyword: 关键字
//
// RETURNS:
//
//	ChartList: chart列表
//	error: nil if succeed, error if fail
func (c *Client) GetPublicRepoChartList(ctx context.Context, pageNo string, pageSize string, keyword string) (*ChartList, error) {
	params := map[string]string{
		"pageNo":   pageNo,
		"pageSize": pageSize,
		"keyword":  keyword,
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/charts", params), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var chartList ChartList
	err = json.Unmarshal(bodyContent, &chartList)
	if err != nil {
		return nil, err
	}
	return &chartList, nil
}

// GetPrivateRepoChartList - 获取私有仓库chart列表
// PARAMS:
//   - ctx: The context to trace request
//   - pageNo: 页码
//   - pageSize: 单页大小
//   - keyword: 关键字
//
// RETURNS:
//
//	ChartList: chart列表
//	error: nil if succeed, error if fail
func (c *Client) GetPrivateRepoChartList(ctx context.Context, pageNo string, pageSize string, keyword string) (*ChartList, error) {
	params := map[string]string{
		"pageNo":   pageNo,
		"pageSize": pageSize,
		"keyword":  keyword,
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/charts/private", params), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var chartList ChartList
	err = json.Unmarshal(bodyContent, &chartList)
	if err != nil {
		return nil, err
	}
	return &chartList, nil
}

// GetPublicRepoChartDetail - 获取共有仓库指定chart详情
// PARAMS:
//   - ctx: The context to trace request
//   - chartName: chart名字
//
// RETURNS:
//
//	ChartDetail: chart详情
//	error: nil if succeed, error if fail
func (c *Client) GetPublicRepoChartDetail(ctx context.Context, chartName string) (*ChartDetail, error) {
	req, err := bce.NewRequest("GET", c.GetURL("v1/charts/"+chartName, nil), nil)
	if err != nil {
		return nil, err
	}
	req.AddHeaders(map[string]string{
		inspect.InspectDesc:    "获取共有仓库指定chart详情",
		inspect.InspectLatency: "2000",
	})
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var chartDetail ChartDetail
	err = json.Unmarshal(bodyContent, &chartDetail)
	if err != nil {
		return nil, err
	}
	return &chartDetail, nil
}

// GetPrivateRepoChartDetail - 获取私有仓库指定chart详情
// PARAMS:
//   - ctx: The context to trace request
//   - chartName: chart名字
//
// RETURNS:
//
//	ChartDetail: chart详情
//	error: nil if succeed, error if fail
func (c *Client) GetPrivateRepoChartDetail(ctx context.Context, chartName string) (*ChartDetail, error) {
	req, err := bce.NewRequest("GET", c.GetURL("v1/charts/private/"+chartName, nil), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var chartDetail ChartDetail
	err = json.Unmarshal(bodyContent, &chartDetail)
	if err != nil {
		return nil, err
	}
	return &chartDetail, nil
}

// GetPublicRepoChartVersionDetail - 获取共有仓库指定chart指定版本的详情
// PARAMS:
//   - ctx: The context to trace request
//   - chartName: chart名字
//   - version: chart版本
//
// RETURNS:
//
//	ChartVersionDetail: chart版本详情
//	error: nil if succeed, error if fail
func (c *Client) GetPublicRepoChartVersionDetail(ctx context.Context, chartName string, version string) (*ChartVersionDetail, error) {
	req, err := bce.NewRequest("GET", c.GetURL("v1/charts/"+chartName+"/"+version, nil), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var chartVersionDetail ChartVersionDetail
	err = json.Unmarshal(bodyContent, &chartVersionDetail)
	if err != nil {
		return nil, err
	}
	return &chartVersionDetail, nil
}

// GetPrivateRepoChartVersionDetail - 获取私有仓库指定chart指定版本的详情
// PARAMS:
//   - ctx: The context to trace request
//   - chartName: chart名字
//   - version: chart版本
//
// RETURNS:
//
//	ChartVersionDetail: chart版本详情
//	error: nil if succeed, error if fail
func (c *Client) GetPrivateRepoChartVersionDetail(ctx context.Context, chartName string, version string) (*ChartVersionDetail, error) {
	req, err := bce.NewRequest("GET", c.GetURL("v1/charts/private/"+chartName+"/"+version, nil), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var chartVersionDetail ChartVersionDetail
	err = json.Unmarshal(bodyContent, &chartVersionDetail)
	if err != nil {
		return nil, err
	}
	return &chartVersionDetail, nil
}

// GetPublicRepoChartReadme - 获取公有仓库指定chart指定版本的readme
// PARAMS:
//   - ctx: The context to trace request
//   - chartName: chart名字
//   - version: chart版本
//
// RETURNS:
//
//	string: readme
//	error: nil if succeed, error if fail
func (c *Client) GetPublicRepoChartReadme(ctx context.Context, chartName string, version string) (string, error) {
	req, err := bce.NewRequest("GET", c.GetURL("v1/charts/"+chartName+"/"+version+"/readme", nil), nil)
	if err != nil {
		return "", err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return "", err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}
	var dataResponse DataResponse
	err = json.Unmarshal(bodyContent, &dataResponse)
	if err != nil {
		return "", err
	}
	return dataResponse.Data, nil
}

// GetPrivateRepoChartReadme - 获取私有仓库指定chart指定版本的readme
// PARAMS:
//   - ctx: The context to trace request
//   - chartName: chart名字
//   - version: chart版本
//
// RETURNS:
//
//	string: readme
//	error: nil if succeed, error if fail
func (c *Client) GetPrivateRepoChartReadme(ctx context.Context, chartName string, version string) (string, error) {
	req, err := bce.NewRequest("GET", c.GetURL("v1/charts/private/"+chartName+"/"+version+"/readme", nil), nil)
	if err != nil {
		return "", err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return "", err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}
	var dataResponse DataResponse
	err = json.Unmarshal(bodyContent, &dataResponse)
	if err != nil {
		return "", err
	}
	return dataResponse.Data, nil
}

// GetPublicRepoChartYaml - 获取公有仓库指定chart指定版本的yaml
// PARAMS:
//   - ctx: The context to trace request
//   - chartName: chart名字
//   - version: chart版本
//
// RETURNS:
//
//	string: yaml
//	error: nil if succeed, error if fail
func (c *Client) GetPublicRepoChartYaml(ctx context.Context, chartName string, version string) (string, error) {
	req, err := bce.NewRequest("GET", c.GetURL("v1/charts/"+chartName+"/"+version+"/values", nil), nil)
	if err != nil {
		return "", err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return "", err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}
	var dataResponse DataResponse
	err = json.Unmarshal(bodyContent, &dataResponse)
	if err != nil {
		return "", err
	}
	return dataResponse.Data, nil
}

// GetPrivateRepoChartYaml - 获取私有仓库指定chart指定版本的yaml
// PARAMS:
//   - ctx: The context to trace request
//   - chartName: chart名字
//   - version: chart版本
//
// RETURNS:
//
//	string: yaml
//	error: nil if succeed, error if fail
func (c *Client) GetPrivateRepoChartYaml(ctx context.Context, chartName string, version string) (string, error) {
	req, err := bce.NewRequest("GET", c.GetURL("v1/charts/private/"+chartName+"/"+version+"/values", nil), nil)
	if err != nil {
		return "", err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return "", err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}
	var dataResponse DataResponse
	err = json.Unmarshal(bodyContent, &dataResponse)
	if err != nil {
		return "", err
	}
	return dataResponse.Data, nil
}

// CheckVersionOfChartExist - 检查指定chart指定版本是否存在
// PARAMS:
//   - ctx: The context to trace request
//   - chartName: chart名字
//   - version: chart版本
//
// RETURNS:
//
//	bool: 是否存在
//	error: nil if succeed, error if fail
func (c *Client) CheckVersionOfChartExist(ctx context.Context, chartName string, version string) (bool, error) {
	req, err := bce.NewRequest("GET", c.GetURL("v1/charts/private/"+chartName+"/"+version+"/check", nil), nil)
	if err != nil {
		return false, err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return false, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return false, err
	}
	var existResponse ExistResponse
	err = json.Unmarshal(bodyContent, &existResponse)
	if err != nil {
		return false, err
	}
	return existResponse.IsExist, nil
}

// UploadPrivateChart - 上传chart包
// PARAMS:
//   - ctx: The context to trace request
//   - request: chart包数据
//
// RETURNS:
//
//	bool: 是否上传成功
//	error: nil if succeed, error if fail
func (c *Client) UploadPrivateChart(ctx context.Context, request []byte) (bool, error) {
	if request == nil {
		return false, errors.New("request is nil")
	}

	req, err := bce.NewRequest("POST", c.GetURL("v1/charts/private", nil), bytes.NewBuffer(request))
	if err != nil {
		return false, err
	}

	option := &bce.SignOption{}
	option.AddHeader("Content-Type", "multipart/form-data;boundary=---------------------------974767299852498929531610575")
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return false, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return false, err
	}
	var uploadResponse UploadResponse
	err = json.Unmarshal(bodyContent, &uploadResponse)
	if err != nil {
		return false, err
	}
	return uploadResponse.Saved, nil
}

// DeletePrivateRepoChartVersion - 删除私有仓库指定chart指定版本
// PARAMS:
//   - ctx: The context to trace request
//   - chartName: chart名字
//   - version: chart版本
//
// RETURNS:
//
//	bool: 是否删除成功
//	error: nil if succeed, error if fail
func (c *Client) DeletePrivateRepoChartVersion(ctx context.Context, chartName string, version string) (bool, error) {
	req, err := bce.NewRequest("DELETE", c.GetURL("v1/charts/private/"+chartName+"/"+version, nil), nil)
	if err != nil {
		return false, err
	}
	resp, err := c.SendRequest(ctx, req, nil)
	if err != nil {
		return false, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return false, err
	}
	var deleteResponse DeleteResponse
	err = json.Unmarshal(bodyContent, &deleteResponse)
	if err != nil {
		return false, err
	}
	return deleteResponse.Deleted, nil
}
