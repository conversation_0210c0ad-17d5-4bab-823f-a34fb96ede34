package bec

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

type CreateAppBLBParams struct {
	Name         string `json:"name"`
	NeedPublicIP bool   `json:"needPublicIp"`
	RegionID     string `json:"regionId"`
	VPCID        string `json:"vpcId"`
	SubnetID     string `json:"subnetId"`
	Desc         string `json:"desc"`
}

type CreateAppBLBResult struct {
	BLBID string `json:"blbId"`
	Name  string `json:"name"`
	Desc  string `json:"desc"`
}

type Listener struct {
	ListenerPort      int          `json:"listenerPort"`
	Scheduler         SchedulerAlg `json:"scheduler"`
	TCPSessionTimeout int          `json:"tcpSessionTimeout"`
}

type CreateListenerPolicyArgs struct {
	AppPolicyVos []AppPolicyVo `json:"appPolicyVos"`
	ListenerPort int           `json:"listenerPort"`
	Type         string        `json:"type"`
}

type ListListener struct {
	Marker       string     `json:"marker"`
	IsTruncated  bool       `json:"isTruncated"`
	NextMarker   string     `json:"nextMarker"`
	MaxKeys      int        `json:"maxKeys"`
	ListenerList []Listener `json:"listenerList"`
}

type AppPolicyVo struct {
	AppIPGroupID string `json:"appIpGroupId"`
	Desc         string `json:"desc"`
	Priority     int    `json:"priority"`
}

type ListListenerPolicyResponse struct {
	Marker      string   `json:"marker"`
	IsTruncated bool     `json:"isTruncated"`
	NextMarker  string   `json:"nextMarker"`
	MaxKeys     int      `json:"maxKeys"`
	PolicyList  []Policy `json:"policyList"`
}

type Policy struct {
	ID             string `json:"id"`
	Desc           string `json:"desc"`
	AppIPGroupID   string `json:"appIpGroupId"`
	AppIPGroupName string `json:"appIpGroupName"`
	FrontendPort   int    `json:"frontendPort"`
	Type           string `json:"type"`
	Priority       int    `json:"priority"`
	RuleList       []Rule `json:"ruleList"`
}

type Rule struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type SchedulerAlg string

const (
	RoundRobin      SchedulerAlg = "RoundRobin"
	LeastConnection SchedulerAlg = "LeastConnection"
	Hash            SchedulerAlg = "Hash"
)

type CreateIPGroupArgs struct {
	Desc       string   `json:"desc"`
	MemberList []Member `json:"memberList"`
	Name       string   `json:"name"`
}

type Member struct {
	MemberID string `json:"memberId"`
	IP       string `json:"ip"`
	Port     int    `json:"port"`
	Weight   int    `json:"weight"`
}

type CreateIPGroupResponse struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Desc string `json:"desc"`
}

type IPGroupList struct {
	IsTruncated    bool         `json:"isTruncated"`
	MaxKeys        int          `json:"maxKeys"`
	AppIPGroupList []AppIPGroup `json:"appIpGroupList"`
}

type AppIPGroup struct {
	ID                string          `json:"id"`
	Name              string          `json:"name"`
	Desc              string          `json:"desc"`
	BackendPolicyList []BackendPolicy `json:"backendPolicyList"`
}

type BackendPolicy struct {
	ID                          string `json:"id"`
	Type                        string `json:"type"`
	HealthCheck                 string `json:"healthCheck"`
	HealthCheckPort             int    `json:"healthCheckPort"`
	HealthCheckURLPath          string `json:"healthCheckUrlPath"`
	HealthCheckTimeoutInSecond  int    `json:"healthCheckTimeoutInSecond"`
	HealthCheckIntervalInSecond int    `json:"healthCheckIntervalInSecond"`
	HealthCheckDownRetry        int    `json:"healthCheckDownRetry"`
	HealthCheckUpRetry          int    `json:"healthCheckUpRetry"`
}

type ListIPGroupMemberResponse struct {
	IsTruncated bool     `json:"isTruncated"`
	MaxKeys     int      `json:"maxKeys"`
	MemberList  []Member `json:"memberList"`
}

type DeleteIPGroupMemberArgs struct {
	IPGroupID    string   `json:"ipGroupId"`
	MemberIDList []string `json:"memberIdList"`
}

type AddIPGroupMemberArgs struct {
	IPGroupID  string   `json:"ipGroupId"`
	MemberList []Member `json:"memberList"`
}

type CreateBackendPolicyArgs struct {
	HealthCheck                 string `json:"healthCheck"`
	HealthCheckDownRetry        int    `json:"healthCheckDownRetry,omitempty"`
	HealthCheckHost             string `json:"healthCheckHost,omitempty"`
	HealthCheckIntervalInSecond int    `json:"healthCheckIntervalInSecond,omitempty"`
	HealthCheckNormalStatus     string `json:"healthCheckNormalStatus,omitempty"`
	HealthCheckPort             int    `json:"healthCheckPort,omitempty"`
	HealthCheckTimeoutInSecond  int    `json:"healthCheckTimeoutInSecond,omitempty"`
	HealthCheckUpRetry          int    `json:"healthCheckUpRetry,omitempty"`
	HealthCheckURLPath          string `json:"healthCheckUrlPath,omitempty"`
	IPGroupID                   string `json:"ipGroupId"`
	Type                        string `json:"type"`
	UDPHealthCheckString        string `json:"udpHealthCheckString,omitempty"`
}

func (c *client) CreateAppBLB(ctx context.Context, args *CreateAppBLBParams, option *bce.SignOption) (string, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return "", err
	}

	url := "/v2/appblb"
	req, err := bce.NewRequest("POST", c.GetURL(c.Endpoint, url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return "", err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return "", err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}

	var result CreateAppBLBResult
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return "", err
	}

	return result.BLBID, nil
}

func (c *client) CreateAppBLBListener(ctx context.Context, blbID string, args *Listener, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("/v2/appblb/%s/TCPlistener", blbID)
	req, err := bce.NewRequest("POST", c.GetURL(c.Endpoint, url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

func (c *client) ListAppBLBListener(ctx context.Context, blbID string, listenerPort int, option *bce.SignOption) (*ListListener, error) {
	params := map[string]string{
		"listenerPort": strconv.Itoa(listenerPort),
	}

	url := fmt.Sprintf("/v2/appblb/%s/TCPlistener", blbID)
	req, err := bce.NewRequest("GET", c.GetURL(c.Endpoint, url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result ListListener
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (c *client) CreateListenerPolicy(ctx context.Context, blbID string, args *CreateListenerPolicyArgs, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("/v2/appblb/%s/policys", blbID)
	req, err := bce.NewRequest("POST", c.GetURL(c.Endpoint, url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

func (c *client) ListAppBLBListenerPolicy(ctx context.Context, blbID string, port int, ProtocalType string,
	option *bce.SignOption) (*ListListenerPolicyResponse, error) {
	params := map[string]string{
		"port": strconv.Itoa(port),
		"type": ProtocalType,
	}

	url := fmt.Sprintf("/v2/appblb/%s/policys", blbID)
	req, err := bce.NewRequest("GET", c.GetURL(c.Endpoint, url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result ListListenerPolicyResponse
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (c *client) CreateIPGroup(ctx context.Context, blbID string, args *CreateIPGroupArgs, option *bce.SignOption) (*CreateIPGroupResponse, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("/v2/appblb/%s/ipgroup", blbID)
	req, err := bce.NewRequest("POST", c.GetURL(c.Endpoint, url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result CreateIPGroupResponse
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (c *client) ListIPGroup(ctx context.Context, blbID string, name string, exactlyMatch bool, option *bce.SignOption) (*IPGroupList, error) {
	params := map[string]string{
		"name":         name,
		"exactlyMatch": strconv.FormatBool(exactlyMatch),
		"clientToken":  c.GenerateClientToken(),
	}

	url := fmt.Sprintf("/v2/appblb/%s/ipgroup", blbID)
	req, err := bce.NewRequest("GET", c.GetURL(c.Endpoint, url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result IPGroupList
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (c *client) ListIPGroupMember(ctx context.Context, blbID string, ipGrouPID string, option *bce.SignOption) (*ListIPGroupMemberResponse, error) {
	params := map[string]string{
		"ipGroupId": ipGrouPID,
	}

	url := fmt.Sprintf("/v2/appblb/%s/ipgroup/member", blbID)
	req, err := bce.NewRequest("GET", c.GetURL(c.Endpoint, url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result ListIPGroupMemberResponse
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (c *client) DeleteIPGroupMember(ctx context.Context, blbID string, args *DeleteIPGroupMemberArgs, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("/v2/appblb/%s/ipgroup/member", blbID)
	req, err := bce.NewRequest("PUT", c.GetURL(c.Endpoint, url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

func (c *client) AddIPGroupMember(ctx context.Context, blbID string, args *AddIPGroupMemberArgs, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("/v2/appblb/%s/ipgroup/member", blbID)
	req, err := bce.NewRequest("POST", c.GetURL(c.Endpoint, url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

func (c *client) CreateBackendPolicy(ctx context.Context, blbID string, args *CreateBackendPolicyArgs, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("/v2/appblb/%s/ipgroup/backendpolicy", blbID)
	req, err := bce.NewRequest("POST", c.GetURL(c.Endpoint, url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}
