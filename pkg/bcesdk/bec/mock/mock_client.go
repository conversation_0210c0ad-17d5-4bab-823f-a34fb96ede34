// Code generated by MockGen. DO NOT EDIT.
// Source: types.go

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
	bec "icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bec"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// AddIPGroupMember mocks base method.
func (m *MockInterface) AddIPGroupMember(ctx context.Context, blbID string, args *bec.AddIPGroupMemberArgs, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddIPGroupMember", ctx, blbID, args, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddIPGroupMember indicates an expected call of AddIPGroupMember.
func (mr *MockInterfaceMockRecorder) AddIPGroupMember(ctx, blbID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddIPGroupMember", reflect.TypeOf((*MockInterface)(nil).AddIPGroupMember), ctx, blbID, args, option)
}

// BindSecurityGroup mocks base method.
func (m *MockInterface) BindSecurityGroup(ctx context.Context, instanceID, securityGroupID string, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindSecurityGroup", ctx, instanceID, securityGroupID, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// BindSecurityGroup indicates an expected call of BindSecurityGroup.
func (mr *MockInterfaceMockRecorder) BindSecurityGroup(ctx, instanceID, securityGroupID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindSecurityGroup", reflect.TypeOf((*MockInterface)(nil).BindSecurityGroup), ctx, instanceID, securityGroupID, option)
}

// CreateAppBLB mocks base method.
func (m *MockInterface) CreateAppBLB(ctx context.Context, args *bec.CreateAppBLBParams, option *bce.SignOption) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAppBLB", ctx, args, option)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAppBLB indicates an expected call of CreateAppBLB.
func (mr *MockInterfaceMockRecorder) CreateAppBLB(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAppBLB", reflect.TypeOf((*MockInterface)(nil).CreateAppBLB), ctx, args, option)
}

// CreateAppBLBListener mocks base method.
func (m *MockInterface) CreateAppBLBListener(ctx context.Context, blbID string, args *bec.Listener, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAppBLBListener", ctx, blbID, args, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAppBLBListener indicates an expected call of CreateAppBLBListener.
func (mr *MockInterfaceMockRecorder) CreateAppBLBListener(ctx, blbID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAppBLBListener", reflect.TypeOf((*MockInterface)(nil).CreateAppBLBListener), ctx, blbID, args, option)
}

// CreateBLB mocks base method.
func (m *MockInterface) CreateBLB(ctx context.Context, args *bec.CreateBLBParams, option *bce.SignOption) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBLB", ctx, args, option)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateBLB indicates an expected call of CreateBLB.
func (mr *MockInterfaceMockRecorder) CreateBLB(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBLB", reflect.TypeOf((*MockInterface)(nil).CreateBLB), ctx, args, option)
}

// CreateBLBBackend mocks base method.
func (m *MockInterface) CreateBLBBackend(ctx context.Context, blbID string, args *bec.CreateBLBBackendParams, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBLBBackend", ctx, blbID, args, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBLBBackend indicates an expected call of CreateBLBBackend.
func (mr *MockInterfaceMockRecorder) CreateBLBBackend(ctx, blbID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBLBBackend", reflect.TypeOf((*MockInterface)(nil).CreateBLBBackend), ctx, blbID, args, option)
}

// CreateBLBListener mocks base method.
func (m *MockInterface) CreateBLBListener(ctx context.Context, blbID string, args *bec.CreateBLBListenerParams, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBLBListener", ctx, blbID, args, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBLBListener indicates an expected call of CreateBLBListener.
func (mr *MockInterfaceMockRecorder) CreateBLBListener(ctx, blbID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBLBListener", reflect.TypeOf((*MockInterface)(nil).CreateBLBListener), ctx, blbID, args, option)
}

// CreateBackendPolicy mocks base method.
func (m *MockInterface) CreateBackendPolicy(ctx context.Context, blbID string, args *bec.CreateBackendPolicyArgs, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBackendPolicy", ctx, blbID, args, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBackendPolicy indicates an expected call of CreateBackendPolicy.
func (mr *MockInterfaceMockRecorder) CreateBackendPolicy(ctx, blbID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBackendPolicy", reflect.TypeOf((*MockInterface)(nil).CreateBackendPolicy), ctx, blbID, args, option)
}

// CreateIPGroup mocks base method.
func (m *MockInterface) CreateIPGroup(ctx context.Context, blbID string, args *bec.CreateIPGroupArgs, option *bce.SignOption) (*bec.CreateIPGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateIPGroup", ctx, blbID, args, option)
	ret0, _ := ret[0].(*bec.CreateIPGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateIPGroup indicates an expected call of CreateIPGroup.
func (mr *MockInterfaceMockRecorder) CreateIPGroup(ctx, blbID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIPGroup", reflect.TypeOf((*MockInterface)(nil).CreateIPGroup), ctx, blbID, args, option)
}

// CreateInternalBLB mocks base method.
func (m *MockInterface) CreateInternalBLB(ctx context.Context, args *bec.CreateInternalBLBParam, option *bce.SignOption) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInternalBLB", ctx, args, option)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInternalBLB indicates an expected call of CreateInternalBLB.
func (mr *MockInterfaceMockRecorder) CreateInternalBLB(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInternalBLB", reflect.TypeOf((*MockInterface)(nil).CreateInternalBLB), ctx, args, option)
}

// CreateListenerPolicy mocks base method.
func (m *MockInterface) CreateListenerPolicy(ctx context.Context, blbID string, args *bec.CreateListenerPolicyArgs, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateListenerPolicy", ctx, blbID, args, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateListenerPolicy indicates an expected call of CreateListenerPolicy.
func (mr *MockInterfaceMockRecorder) CreateListenerPolicy(ctx, blbID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateListenerPolicy", reflect.TypeOf((*MockInterface)(nil).CreateListenerPolicy), ctx, blbID, args, option)
}

// CreateRouteRule mocks base method.
func (m *MockInterface) CreateRouteRule(ctx context.Context, args *bec.CreateRouteRuleArgs, option *bce.SignOption) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRouteRule", ctx, args, option)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRouteRule indicates an expected call of CreateRouteRule.
func (mr *MockInterfaceMockRecorder) CreateRouteRule(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRouteRule", reflect.TypeOf((*MockInterface)(nil).CreateRouteRule), ctx, args, option)
}

// CreateSecurityGroup mocks base method.
func (m *MockInterface) CreateSecurityGroup(ctx context.Context, request *bec.CreateSecurityGroupRequest, option *bce.SignOption) (*bec.CreateSecurityGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSecurityGroup", ctx, request, option)
	ret0, _ := ret[0].(*bec.CreateSecurityGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSecurityGroup indicates an expected call of CreateSecurityGroup.
func (mr *MockInterfaceMockRecorder) CreateSecurityGroup(ctx, request, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSecurityGroup", reflect.TypeOf((*MockInterface)(nil).CreateSecurityGroup), ctx, request, option)
}

// CreateVMService mocks base method.
func (m *MockInterface) CreateVMService(ctx context.Context, args *bec.CreateVMServiceParams, option *bce.SignOption) (*bec.CreateVMServiceResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateVMService", ctx, args, option)
	ret0, _ := ret[0].(*bec.CreateVMServiceResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateVMService indicates an expected call of CreateVMService.
func (mr *MockInterfaceMockRecorder) CreateVMService(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateVMService", reflect.TypeOf((*MockInterface)(nil).CreateVMService), ctx, args, option)
}

// DeleteBLB mocks base method.
func (m *MockInterface) DeleteBLB(ctx context.Context, blbID string, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBLB", ctx, blbID, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBLB indicates an expected call of DeleteBLB.
func (mr *MockInterfaceMockRecorder) DeleteBLB(ctx, blbID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBLB", reflect.TypeOf((*MockInterface)(nil).DeleteBLB), ctx, blbID, option)
}

// DeleteBLBBackends mocks base method.
func (m *MockInterface) DeleteBLBBackends(ctx context.Context, blbID string, args *bec.DeleteBLBBackendsParam, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBLBBackends", ctx, blbID, args, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBLBBackends indicates an expected call of DeleteBLBBackends.
func (mr *MockInterfaceMockRecorder) DeleteBLBBackends(ctx, blbID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBLBBackends", reflect.TypeOf((*MockInterface)(nil).DeleteBLBBackends), ctx, blbID, args, option)
}

// DeleteIPGroupMember mocks base method.
func (m *MockInterface) DeleteIPGroupMember(ctx context.Context, blbID string, args *bec.DeleteIPGroupMemberArgs, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteIPGroupMember", ctx, blbID, args, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteIPGroupMember indicates an expected call of DeleteIPGroupMember.
func (mr *MockInterfaceMockRecorder) DeleteIPGroupMember(ctx, blbID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteIPGroupMember", reflect.TypeOf((*MockInterface)(nil).DeleteIPGroupMember), ctx, blbID, args, option)
}

// DeleteRouteRule mocks base method.
func (m *MockInterface) DeleteRouteRule(ctx context.Context, routeID string, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRouteRule", ctx, routeID, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteRouteRule indicates an expected call of DeleteRouteRule.
func (mr *MockInterfaceMockRecorder) DeleteRouteRule(ctx, routeID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRouteRule", reflect.TypeOf((*MockInterface)(nil).DeleteRouteRule), ctx, routeID, option)
}

// DeleteVMInstance mocks base method.
func (m *MockInterface) DeleteVMInstance(ctx context.Context, vmID string, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteVMInstance", ctx, vmID, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteVMInstance indicates an expected call of DeleteVMInstance.
func (mr *MockInterfaceMockRecorder) DeleteVMInstance(ctx, vmID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteVMInstance", reflect.TypeOf((*MockInterface)(nil).DeleteVMInstance), ctx, vmID, option)
}

// DeleteVMService mocks base method.
func (m *MockInterface) DeleteVMService(ctx context.Context, serviceID string, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteVMService", ctx, serviceID, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteVMService indicates an expected call of DeleteVMService.
func (mr *MockInterfaceMockRecorder) DeleteVMService(ctx, serviceID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteVMService", reflect.TypeOf((*MockInterface)(nil).DeleteVMService), ctx, serviceID, option)
}

// GetBLB mocks base method.
func (m *MockInterface) GetBLB(ctx context.Context, blbID string, option *bce.SignOption) (*bec.BLB, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBLB", ctx, blbID, option)
	ret0, _ := ret[0].(*bec.BLB)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBLB indicates an expected call of GetBLB.
func (mr *MockInterfaceMockRecorder) GetBLB(ctx, blbID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBLB", reflect.TypeOf((*MockInterface)(nil).GetBLB), ctx, blbID, option)
}

// GetBLBBackends mocks base method.
func (m *MockInterface) GetBLBBackends(ctx context.Context, blbID string, option *bce.SignOption) (*bec.GetBLBBackendsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBLBBackends", ctx, blbID, option)
	ret0, _ := ret[0].(*bec.GetBLBBackendsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBLBBackends indicates an expected call of GetBLBBackends.
func (mr *MockInterfaceMockRecorder) GetBLBBackends(ctx, blbID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBLBBackends", reflect.TypeOf((*MockInterface)(nil).GetBLBBackends), ctx, blbID, option)
}

// GetSecurityGroups mocks base method.
func (m *MockInterface) GetSecurityGroups(ctx context.Context, request *bec.GetSecurityGroupsRequest, option *bce.SignOption) (*bec.GetSecurityGroupsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSecurityGroups", ctx, request, option)
	ret0, _ := ret[0].(*bec.GetSecurityGroupsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSecurityGroups indicates an expected call of GetSecurityGroups.
func (mr *MockInterfaceMockRecorder) GetSecurityGroups(ctx, request, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSecurityGroups", reflect.TypeOf((*MockInterface)(nil).GetSecurityGroups), ctx, request, option)
}

// GetVMInstance mocks base method.
func (m *MockInterface) GetVMInstance(ctx context.Context, vmID string, option *bce.SignOption) (*bec.VMInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVMInstance", ctx, vmID, option)
	ret0, _ := ret[0].(*bec.VMInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVMInstance indicates an expected call of GetVMInstance.
func (mr *MockInterfaceMockRecorder) GetVMInstance(ctx, vmID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVMInstance", reflect.TypeOf((*MockInterface)(nil).GetVMInstance), ctx, vmID, option)
}

// GetVMInstanceV2 mocks base method.
func (m *MockInterface) GetVMInstanceV2(ctx context.Context, vmID string, option *bce.SignOption) (*bec.VMInstanceV2, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVMInstanceV2", ctx, vmID, option)
	ret0, _ := ret[0].(*bec.VMInstanceV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVMInstanceV2 indicates an expected call of GetVMInstanceV2.
func (mr *MockInterfaceMockRecorder) GetVMInstanceV2(ctx, vmID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVMInstanceV2", reflect.TypeOf((*MockInterface)(nil).GetVMInstanceV2), ctx, vmID, option)
}

// GetVMService mocks base method.
func (m *MockInterface) GetVMService(ctx context.Context, serviceID string, option *bce.SignOption) (*bec.VMService, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVMService", ctx, serviceID, option)
	ret0, _ := ret[0].(*bec.VMService)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVMService indicates an expected call of GetVMService.
func (mr *MockInterfaceMockRecorder) GetVMService(ctx, serviceID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVMService", reflect.TypeOf((*MockInterface)(nil).GetVMService), ctx, serviceID, option)
}

// IDMapping mocks base method.
func (m *MockInterface) IDMapping(ctx context.Context, args *bec.IDMappingArgs, option *bce.SignOption) (*bec.IDMappingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IDMapping", ctx, args, option)
	ret0, _ := ret[0].(*bec.IDMappingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IDMapping indicates an expected call of IDMapping.
func (mr *MockInterfaceMockRecorder) IDMapping(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IDMapping", reflect.TypeOf((*MockInterface)(nil).IDMapping), ctx, args, option)
}

// LabelAsCCEClusterNode mocks base method.
func (m *MockInterface) LabelAsCCEClusterNode(ctx context.Context, vmID, clusterID string, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LabelAsCCEClusterNode", ctx, vmID, clusterID, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// LabelAsCCEClusterNode indicates an expected call of LabelAsCCEClusterNode.
func (mr *MockInterfaceMockRecorder) LabelAsCCEClusterNode(ctx, vmID, clusterID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LabelAsCCEClusterNode", reflect.TypeOf((*MockInterface)(nil).LabelAsCCEClusterNode), ctx, vmID, clusterID, option)
}

// ListAppBLBListener mocks base method.
func (m *MockInterface) ListAppBLBListener(ctx context.Context, blbID string, listenerPort int, option *bce.SignOption) (*bec.ListListener, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAppBLBListener", ctx, blbID, listenerPort, option)
	ret0, _ := ret[0].(*bec.ListListener)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAppBLBListener indicates an expected call of ListAppBLBListener.
func (mr *MockInterfaceMockRecorder) ListAppBLBListener(ctx, blbID, listenerPort, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAppBLBListener", reflect.TypeOf((*MockInterface)(nil).ListAppBLBListener), ctx, blbID, listenerPort, option)
}

// ListAppBLBListenerPolicy mocks base method.
func (m *MockInterface) ListAppBLBListenerPolicy(ctx context.Context, blbID string, port int, ProtocalType string, option *bce.SignOption) (*bec.ListListenerPolicyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAppBLBListenerPolicy", ctx, blbID, port, ProtocalType, option)
	ret0, _ := ret[0].(*bec.ListListenerPolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAppBLBListenerPolicy indicates an expected call of ListAppBLBListenerPolicy.
func (mr *MockInterfaceMockRecorder) ListAppBLBListenerPolicy(ctx, blbID, port, ProtocalType, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAppBLBListenerPolicy", reflect.TypeOf((*MockInterface)(nil).ListAppBLBListenerPolicy), ctx, blbID, port, ProtocalType, option)
}

// ListAvailableDeploymentForBLB mocks base method.
func (m *MockInterface) ListAvailableDeploymentForBLB(ctx context.Context, blbID string, args *bec.ListAvailableDeploymentForBLBParams, option *bce.SignOption) (*bec.ListAvailableDeploymentForBLBResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAvailableDeploymentForBLB", ctx, blbID, args, option)
	ret0, _ := ret[0].(*bec.ListAvailableDeploymentForBLBResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAvailableDeploymentForBLB indicates an expected call of ListAvailableDeploymentForBLB.
func (mr *MockInterfaceMockRecorder) ListAvailableDeploymentForBLB(ctx, blbID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAvailableDeploymentForBLB", reflect.TypeOf((*MockInterface)(nil).ListAvailableDeploymentForBLB), ctx, blbID, args, option)
}

// ListBLBs mocks base method.
func (m *MockInterface) ListBLBs(ctx context.Context, args *bec.ListBLBParams, option *bce.SignOption) (*bec.ListBLBResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBLBs", ctx, args, option)
	ret0, _ := ret[0].(*bec.ListBLBResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBLBs indicates an expected call of ListBLBs.
func (mr *MockInterfaceMockRecorder) ListBLBs(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBLBs", reflect.TypeOf((*MockInterface)(nil).ListBLBs), ctx, args, option)
}

// ListIPGroup mocks base method.
func (m *MockInterface) ListIPGroup(ctx context.Context, blbID, name string, exactlyMatch bool, option *bce.SignOption) (*bec.IPGroupList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListIPGroup", ctx, blbID, name, exactlyMatch, option)
	ret0, _ := ret[0].(*bec.IPGroupList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListIPGroup indicates an expected call of ListIPGroup.
func (mr *MockInterfaceMockRecorder) ListIPGroup(ctx, blbID, name, exactlyMatch, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListIPGroup", reflect.TypeOf((*MockInterface)(nil).ListIPGroup), ctx, blbID, name, exactlyMatch, option)
}

// ListIPGroupMember mocks base method.
func (m *MockInterface) ListIPGroupMember(ctx context.Context, blbID, ipGrouPID string, option *bce.SignOption) (*bec.ListIPGroupMemberResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListIPGroupMember", ctx, blbID, ipGrouPID, option)
	ret0, _ := ret[0].(*bec.ListIPGroupMemberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListIPGroupMember indicates an expected call of ListIPGroupMember.
func (mr *MockInterfaceMockRecorder) ListIPGroupMember(ctx, blbID, ipGrouPID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListIPGroupMember", reflect.TypeOf((*MockInterface)(nil).ListIPGroupMember), ctx, blbID, ipGrouPID, option)
}

// ListRouteTable mocks base method.
func (m *MockInterface) ListRouteTable(ctx context.Context, args *bec.ListRouteArgs, option *bce.SignOption) (*bec.ListRouteResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRouteTable", ctx, args, option)
	ret0, _ := ret[0].(*bec.ListRouteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRouteTable indicates an expected call of ListRouteTable.
func (mr *MockInterfaceMockRecorder) ListRouteTable(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRouteTable", reflect.TypeOf((*MockInterface)(nil).ListRouteTable), ctx, args, option)
}

// ListVMInstances mocks base method.
func (m *MockInterface) ListVMInstances(ctx context.Context, args *bec.ListVMInstanceParams, option *bce.SignOption) (*bec.ListVMInstanceResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListVMInstances", ctx, args, option)
	ret0, _ := ret[0].(*bec.ListVMInstanceResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListVMInstances indicates an expected call of ListVMInstances.
func (mr *MockInterfaceMockRecorder) ListVMInstances(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListVMInstances", reflect.TypeOf((*MockInterface)(nil).ListVMInstances), ctx, args, option)
}

// ListVMServices mocks base method.
func (m *MockInterface) ListVMServices(ctx context.Context, args *bec.ListVMServiceParams, option *bce.SignOption) (*bec.ListVMServiceResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListVMServices", ctx, args, option)
	ret0, _ := ret[0].(*bec.ListVMServiceResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListVMServices indicates an expected call of ListVMServices.
func (mr *MockInterfaceMockRecorder) ListVMServices(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListVMServices", reflect.TypeOf((*MockInterface)(nil).ListVMServices), ctx, args, option)
}

// ReinstallVMInstanceOS mocks base method.
func (m *MockInterface) ReinstallVMInstanceOS(ctx context.Context, vmID string, args *bec.ReinstallVMInstanceOSParams, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReinstallVMInstanceOS", ctx, vmID, args, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReinstallVMInstanceOS indicates an expected call of ReinstallVMInstanceOS.
func (mr *MockInterfaceMockRecorder) ReinstallVMInstanceOS(ctx, vmID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReinstallVMInstanceOS", reflect.TypeOf((*MockInterface)(nil).ReinstallVMInstanceOS), ctx, vmID, args, option)
}

// RemoveLabelOfCCEClusterNode mocks base method.
func (m *MockInterface) RemoveLabelOfCCEClusterNode(ctx context.Context, vmID string, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveLabelOfCCEClusterNode", ctx, vmID, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveLabelOfCCEClusterNode indicates an expected call of RemoveLabelOfCCEClusterNode.
func (mr *MockInterfaceMockRecorder) RemoveLabelOfCCEClusterNode(ctx, vmID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveLabelOfCCEClusterNode", reflect.TypeOf((*MockInterface)(nil).RemoveLabelOfCCEClusterNode), ctx, vmID, option)
}
