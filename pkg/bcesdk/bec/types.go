/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  types
 * @Version: 1.0.0
 * @Date: 2020/10/23 11:50 上午
 */
package bec

import (
	"context"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

const (
	BEC_DOMAIN = "bec.baidubce.com"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock -source=types.go

type Interface interface {
	CreateVMService(ctx context.Context, args *CreateVMServiceParams, option *bce.SignOption) (*CreateVMServiceResult, error)
	GetVMService(ctx context.Context, serviceID string, option *bce.SignOption) (*VMService, error)
	ListVMServices(ctx context.Context, args *ListVMServiceParams, option *bce.SignOption) (*ListVMServiceResult, error)
	DeleteVMService(ctx context.Context, serviceID string, option *bce.SignOption) error

	ListVMInstances(ctx context.Context, args *ListVMInstanceParams, option *bce.SignOption) (*ListVMInstanceResult, error)
	GetVMInstance(ctx context.Context, vmID string, option *bce.SignOption) (*VMInstance, error)
	GetVMInstanceV2(ctx context.Context, vmID string, option *bce.SignOption) (*VMInstanceV2, error)
	DeleteVMInstance(ctx context.Context, vmID string, option *bce.SignOption) error
	ReinstallVMInstanceOS(ctx context.Context, vmID string, args *ReinstallVMInstanceOSParams, option *bce.SignOption) error
	LabelAsCCEClusterNode(ctx context.Context, vmID, clusterID string, option *bce.SignOption) error
	RemoveLabelOfCCEClusterNode(ctx context.Context, vmID string, option *bce.SignOption) error
	IDMapping(ctx context.Context, args *IDMappingArgs, option *bce.SignOption) (*IDMappingResponse, error)

	ListRouteTable(ctx context.Context, args *ListRouteArgs, option *bce.SignOption) (*ListRouteResponse, error)
	DeleteRouteRule(ctx context.Context, routeID string, option *bce.SignOption) error
	CreateRouteRule(ctx context.Context, args *CreateRouteRuleArgs, option *bce.SignOption) (string, error)

	CreateBLB(ctx context.Context, args *CreateBLBParams, option *bce.SignOption) (string, error)
	ListBLBs(ctx context.Context, args *ListBLBParams, option *bce.SignOption) (*ListBLBResult, error)
	GetBLB(ctx context.Context, blbID string, option *bce.SignOption) (*BLB, error)
	CreateBLBListener(ctx context.Context, blbID string, args *CreateBLBListenerParams, option *bce.SignOption) error
	CreateBLBBackend(ctx context.Context, blbID string, args *CreateBLBBackendParams, option *bce.SignOption) error
	GetBLBBackends(ctx context.Context, blbID string, option *bce.SignOption) (*GetBLBBackendsResult, error)
	DeleteBLB(ctx context.Context, blbID string, option *bce.SignOption) error
	DeleteBLBBackends(ctx context.Context, blbID string, args *DeleteBLBBackendsParam, option *bce.SignOption) error
	ListAvailableDeploymentForBLB(ctx context.Context, blbID string, args *ListAvailableDeploymentForBLBParams, option *bce.SignOption) (*ListAvailableDeploymentForBLBResult, error)

	// appblb
	CreateAppBLBListener(ctx context.Context, blbID string, args *Listener, option *bce.SignOption) error

	ListAppBLBListener(ctx context.Context, blbID string, listenerPort int, option *bce.SignOption) (*ListListener, error)
	CreateListenerPolicy(ctx context.Context, blbID string, args *CreateListenerPolicyArgs, option *bce.SignOption) error
	ListAppBLBListenerPolicy(ctx context.Context, blbID string, port int, ProtocalType string, option *bce.SignOption) (*ListListenerPolicyResponse, error)
	CreateAppBLB(ctx context.Context, args *CreateAppBLBParams, option *bce.SignOption) (string, error)
	CreateIPGroup(ctx context.Context, blbID string, args *CreateIPGroupArgs, option *bce.SignOption) (*CreateIPGroupResponse, error)
	ListIPGroup(ctx context.Context, blbID string, name string, exactlyMatch bool, option *bce.SignOption) (*IPGroupList, error)
	ListIPGroupMember(ctx context.Context, blbID string, ipGrouPID string, option *bce.SignOption) (*ListIPGroupMemberResponse, error)
	DeleteIPGroupMember(ctx context.Context, blbID string, args *DeleteIPGroupMemberArgs, option *bce.SignOption) error
	AddIPGroupMember(ctx context.Context, blbID string, args *AddIPGroupMemberArgs, option *bce.SignOption) error
	CreateBackendPolicy(ctx context.Context, blbID string, args *CreateBackendPolicyArgs, option *bce.SignOption) error

	// internal blb
	CreateInternalBLB(ctx context.Context, args *CreateInternalBLBParam, option *bce.SignOption) (string, error)

	// sg
	GetSecurityGroups(ctx context.Context, request *GetSecurityGroupsRequest, option *bce.SignOption) (*GetSecurityGroupsResponse, error)

	CreateSecurityGroup(ctx context.Context, request *CreateSecurityGroupRequest, option *bce.SignOption) (*CreateSecurityGroupResponse, error)
	BindSecurityGroup(ctx context.Context, instanceID, securityGroupID string, option *bce.SignOption) error
}

var _ Interface = &client{}

type client struct {
	*bce.Client
}

func NewClient(config *bce.Config) *client {
	bceClient := bce.NewClient(config)

	client := &client{
		Client: bceClient,
	}
	if client.Endpoint == "" {
		client.Endpoint = "http://" + BEC_DOMAIN
	}
	return client
}
