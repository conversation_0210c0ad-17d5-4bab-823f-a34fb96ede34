package bec

import (
	"bytes"
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

type CreateInternalBLBParam struct {
	Desc                string            `json:"desc,omitempty"`
	LbType              LBBackendType     `json:"lbType"`
	Name                string            `json:"name"`
	NeedPublicIP        bool              `json:"needPublicIp"`
	RegionID            string            `json:"regionId"`
	SubServiceProviders []ServiceProvider `json:"subServiceProviders,omitempty"`
	SubnetID            string            `json:"subnetId,omitempty"`
	Type                string            `json:"type,omitempty"`
	VpcID               string            `json:"vpcId,omitempty"`
}

type CreateInternalBLBResult struct {
	BLBID string `json:"blbId"`
	Desc  string `json:"desc"`
	Name  string `json:"name"`
}

// CreateInternalBLB - 内部接口创建BLB，返回BLB id
// http://************:8799/doc.html#/BEC%20V2/%E3%80%90%E5%86%85%E9%83%A8%E3%80%91BEC%E8%B4%9F%E8%BD%BD%E5%9D%87%E8%A1%A1%20%E6%99%AE%E9%80%9A%E5%9E%8B/createBlbV2UsingPOST_1
func (c *client) CreateInternalBLB(ctx context.Context, args *CreateInternalBLBParam, option *bce.SignOption) (string, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return "", err
	}

	url := "/v2/internal/blb"
	req, err := bce.NewRequest("POST", c.GetURL(c.Endpoint, url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return "", err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return "", err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}

	var result CreateInternalBLBResult
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return "", err
	}

	return result.BLBID, nil
}
