/**
 * @Author: chenya<PERSON>01
 * @Description:
 * @File:  route.go
 * @Version: 1.0.0
 * @Date: 2023/09/19
 */

package bec

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

// RouteRule define route
type RouteRule struct {
	RouteRuleID        string `json:"routeRuleId"`
	RouteTableID       string `json:"routeTableId"`
	SourceAddress      string `json:"sourceAddress"`
	DestinationAddress string `json:"destinationAddress"`
	NexthopID          string `json:"nexthopId"`
	NexthopType        string `json:"nexthopType"`
	Description        string `json:"description"`
}

// ListRouteArgs define listroute args
type ListRouteArgs struct {
	RouteTableID string `json:"routeTableId,omitempty"`
	VpcID        string `json:"vpcId,omitempty"`
}

// ListRouteResponse define response of list route
type ListRouteResponse struct {
	RouteTableID string      `json:"routeTableId"`
	VpcID        string      `json:"vpcId"`
	RouteRules   []RouteRule `json:"routeRules"`
}

// CreateRouteRuleArgs define args create route
// http://gollum.baidu.com/Logical-Network-API#创建路由规则
type CreateRouteRuleArgs struct {
	RouteTableID  string `json:"routeTableId"`
	SourceAddress string `json:"sourceAddress"`

	// 源地址，源地址可以是0.0.0.0/0，
	// 否则匹配路由表的流量源必须属于该VPC下某子网，
	// 源地址选择自定义时，自定义网段需在已有子网范围内
	DestinationAddress string `json:"destinationAddress"`

	// 目的地址，目的地址可以是0.0.0.0/0，
	// 否则目的地址不能与本VPC cidr重叠
	// （目的网段或本VPC cidr为0.0.0.0/0时例外）
	NexthopID string `json:"nexthopId,omitempty"`

	// 下一跳id，当nexthopType是本地网关类型时，
	// 该字段可以为空
	NexthopType string `json:"nexthopType"`

	// 路由类型。Bcc类型是"custom"；
	// VPN类型是"vpn"；NAT类型是"nat"；本地网关类型是"defaultGateway"
	Description string `json:"description"`

	// 取值 4、6，表示 IPv4 还是 IPv6 的路由，默认为 4
	IPVersion int `json:"ipVersion"`
}

// CreateRouteResponse define response of creating route
type CreateRouteResponse struct {
	RouteRuleID string `json:"routeRuleId"`
}

func (args *ListRouteArgs) validate() error {
	if args == nil {
		return errors.New("ListRouteArgs need args")
	}
	if args.RouteTableID == "" && args.VpcID == "" {
		return errors.New("ListRouteArgs need RouteTableID or VpcID")
	}

	return nil
}

// ListRouteTable list all routes
func (c *client) ListRouteTable(ctx context.Context, args *ListRouteArgs, option *bce.SignOption) (*ListRouteResponse, error) {
	err := args.validate()
	if err != nil {
		return nil, err
	}

	params := make(map[string]string)
	if args.RouteTableID != "" {
		params["routeTableId"] = args.RouteTableID
	}
	if args.VpcID != "" {
		params["vpcId"] = args.VpcID
	}

	req, err := bce.NewRequest("GET", c.GetURL(c.Endpoint, "v2/route", params), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}
	var routesResp ListRouteResponse
	err = json.Unmarshal(bodyContent, &routesResp)

	if err != nil {
		return nil, err
	}
	return &routesResp, nil
}

// DeleteRouteRule delete a route
// http://gollum.baidu.com/Logical-Network-API#删除路由规则
func (c *client) DeleteRouteRule(ctx context.Context, routeID string, option *bce.SignOption) error {
	if routeID == "" {
		return errors.New("DeleteRouteRule need routeID")
	}
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	req, err := bce.NewRequest("DELETE", c.GetURL(c.Endpoint, "v2/route/rule"+"/"+routeID, params), nil)
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

func (args *CreateRouteRuleArgs) validate() error {
	if args == nil {
		return errors.New("CreateRouteRuleArgs need args")
	}
	if args.RouteTableID == "" {
		return errors.New("CreateRouteRuleArgs need RouteTableID")
	}
	if args.SourceAddress == "" || args.DestinationAddress == "" {
		return errors.New("CreateRouteRuleArgs need address")
	}
	if args.NexthopID == "" || args.NexthopType == "" {
		return errors.New("CreateRouteRuleArgs need NexthopID and NexthopType")
	}
	return nil
}

// CreateRouteRule create a route rule
func (c *client) CreateRouteRule(ctx context.Context, args *CreateRouteRuleArgs, option *bce.SignOption) (string, error) {
	err := args.validate()
	if err != nil {
		return "", err
	}
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return "", err
	}
	req, err := bce.NewRequest("POST", c.GetURL(c.Endpoint, "v2/route/rule", params), bytes.NewBuffer(postContent))
	if err != nil {
		return "", err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return "", err
	}
	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return "", err
	}
	var crResp *CreateRouteResponse
	err = json.Unmarshal(bodyContent, &crResp)

	if err != nil {
		return "", err
	}
	return crResp.RouteRuleID, nil
}
