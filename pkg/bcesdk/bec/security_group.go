package bec

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"strconv"

	uuid "github.com/satori/go.uuid"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

type GetSecurityGroupsRequest struct {
	InstanceID string `json:"instanceId"`
	Marker     string `json:"marker"`
	MaxKeys    int64  `json:"maxKeys"`
}

type GetSecurityGroupsResponse struct {
	NextMarker     string          `json:"nextMarker"`
	Marker         string          `json:"marker"`
	MaxKeys        int64           `json:"maxKeys"`
	IsTruncated    bool            `json:"isTruncated"`
	SecurityGroups []SecurityGroup `json:"securityGroups"`
}

type SecurityGroup struct {
	ID    string              `json:"id"`
	Name  string              `json:"name"`
	Desc  string              `json:"desc"`
	Rules []SecurityGroupRule `json:"rules"`
}

type SecurityGroupRule struct {
	SecurityGroupRuleID string `json:"securityGroupRuleId"`
	Direction           string `json:"direction"`
	Protocol            string `json:"protocol"`
	PortRange           string `json:"portRange"`
	Remark              string `json:"remark"`
	EtherType           string `json:"ethertype"`
	DestIP              string `json:"destIp"`
	SourceIP            string `json:"sourceIp"`
}

type CreateSecurityGroupRequest struct {
	Name        string              `json:"name"`
	Description string              `json:"desc"`
	SGRules     []SecurityGroupRule `json:"rules"`
}

type CreateSecurityGroupResponse struct {
	SecurityGroupID string `json:"securityGroupId"`
}

const (
	defaultMaxKeys = 1000 // 请求中默认每页包含的最大数量
)

// GetSecurityGroups 根据 InstanceID 查询安全组
func (c *client) GetSecurityGroups(ctx context.Context, request *GetSecurityGroupsRequest, option *bce.SignOption) (*GetSecurityGroupsResponse, error) {
	if request == nil {
		return nil, errors.New("request is nil")
	}

	if request.MaxKeys <= 0 {
		request.MaxKeys = defaultMaxKeys
	}

	params := map[string]string{
		"maxKeys": strconv.FormatInt(request.MaxKeys, 10),
	}

	if request.Marker != "" {
		params["marker"] = request.Marker
	}

	if request.InstanceID != "" {
		params["instanceId"] = request.InstanceID
	}

	req, err := bce.NewRequest("GET", c.GetURL(c.Endpoint, "v2/securityGroup", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	getResp := new(GetSecurityGroupsResponse)
	err = json.Unmarshal(removeInvalidChar(bodyContent), getResp)
	if err != nil {
		return nil, err
	}

	return getResp, nil
}

// CreateSecurityGroup 创建安全组
func (c *client) CreateSecurityGroup(ctx context.Context, request *CreateSecurityGroupRequest, option *bce.SignOption) (*CreateSecurityGroupResponse, error) {
	if request == nil {
		return nil, errors.New("request is nil")
	}

	if request.Name == "" {
		return nil, errors.New("request.Name is empty")
	}

	if len(request.SGRules) <= 0 {
		return nil, errors.New("request.SGRules length not greater than 0")
	}

	params := map[string]string{
		"clientToken": uuid.NewV4().String(),
	}

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL(c.Endpoint, "v2/securityGroup", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	createResp := new(CreateSecurityGroupResponse)
	err = json.Unmarshal(bodyContent, createResp)
	if err != nil {
		return nil, err
	}

	return createResp, nil
}

func (c *client) BindSecurityGroup(ctx context.Context, instanceID, securityGroupID string, option *bce.SignOption) error {
	if instanceID == "" || securityGroupID == "" {
		return errors.New("instanceID or securityGroupID is empty")
	}

	params := map[string]string{
		"bind": "",
	}

	body := struct {
		SecurityGroupId string `json:"securityGroupId"`
	}{
		SecurityGroupId: securityGroupID,
	}
	postContent, err := json.Marshal(body)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL(c.Endpoint, "v2/vm/instance/"+instanceID, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}
