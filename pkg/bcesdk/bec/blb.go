/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  blb
 * @Version: 1.0.0
 * @Date: 2020/10/27 4:37 下午
 */
package bec

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/bcesdk/bce"
)

type LBBackendType string

const (
	LBBackendTypeVM        LBBackendType = "vm"
	LBBackendTypeContainer LBBackendType = "container"
)

type CreateBLBParams struct {
	PaymentMethod        PaymentMethod   `json:"paymentMethod"`
	LBType               LBBackendType   `json:"lbType"`
	Region               Region          `json:"region"`
	ServiceProvider      ServiceProvider `json:"serviceProvider"`
	City                 City            `json:"city"`
	BLBName              string          `json:"blbName"`
	NeedPublicIp         bool            `json:"needPublicIp"`
	BandwidthInMbpsLimit int             `json:"bandwidthInMbpsLimit"` // 单位 Mbps
}

type CreateBLBResult struct {
	Result  bool   `json:"result"`
	Action  string `json:"action"`
	Details BLB    `json:"details"`
}

// CreateBLB - CreateBLB 创建BLB，返回BLB id
func (c *client) CreateBLB(ctx context.Context, args *CreateBLBParams, option *bce.SignOption) (string, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return "", err
	}

	url := "/v1/blb"
	req, err := bce.NewRequest("POST", c.GetURL(c.Endpoint, url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return "", err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return "", err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}

	var result CreateBLBResult
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return "", err
	}

	return result.Details.BLBID, nil
}

type ListBLBParams struct {
	LBType   LBBackendType
	PageNo   int
	PageSize int
}

type ListBLBResult struct {
	PageNo     int    `json:"pageNo"`
	PageSize   int    `json:"pageSize"`
	TotalCount int    `json:"totalCount"`
	Result     []*BLB `json:"result"`
}

type BLBStatus string

const (
	BLBStatusStarting   BLBStatus = "STARTING"
	BLBStatusRunning    BLBStatus = "RUNNING"
	BLBStatusPending    BLBStatus = "PENDING"
	BLBStatusException  BLBStatus = "EXCEPTION"
	BLBStatusFailed     BLBStatus = "FAILED"
	BLBStatusUnknown    BLBStatus = "UNKNOWN"
	BLBStatusTerminated BLBStatus = "TERMINATED"
	BLBStatusWaiting    BLBStatus = "WAITING"
	BLBStatusStop       BLBStatus = "STOP"
	BLBStatusBinding    BLBStatus = "BINDING"
	BLBStatusStopping   BLBStatus = "STOPPING"
)

type BLB struct {
	BLBID                string          `json:"blbId"`
	BLBName              string          `json:"blbName"`
	Status               BLBStatus       `json:"status"`
	LBType               LBBackendType   `json:"lbType"`
	Region               Region          `json:"region"`
	ServiceProvider      ServiceProvider `json:"serviceProvider"`
	City                 City            `json:"city"`
	PublicIP             string          `json:"publicIp"`
	InternalIP           string          `json:"internalIp"`
	Ports                []BLBListener   `json:"ports"`
	PodCount             int             `json:"podCount"`
	BandwidthInMbpsLimit int             `json:"bandwidthInMbpsLimit"`
	CreateTime           string          `json:"createTime"`
}

type Protocol string

const (
	ProtocolTCP   Protocol = "TCP"
	ProtocolUDP   Protocol = "UDP"
	ProtocolHTTP  Protocol = "HTTP"
	ProtocolHTTPS Protocol = "HTTPS"
	ProtocolSSL   Protocol = "SSL"
)

type LBForwardMode string

const (
	LBForwardModeWRR     LBForwardMode = "wrr"
	LBForwardModeMinConn LBForwardMode = "minconn"
	LBForwardModeSrcH    LBForwardMode = "srch"
)

type BLBListener struct {
	Protocol            Protocol      `json:"protocol"`
	Port                int           `json:"port"`
	BackendPort         int           `json:"backendPort"`
	Scheduler           LBForwardMode `json:"scheduler"`
	HealthCheckInterval int           `json:"healthCheckInterval"`
	HealthCheckRetry    int           `json:"healthCheckRetry"`
	HealthCheckTimeout  int           `json:"healthCheckTimeout"`
}

func (c *client) ListBLBs(ctx context.Context, args *ListBLBParams, option *bce.SignOption) (*ListBLBResult, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	if args != nil {
		if args.PageNo > 0 {
			params["pageNo"] = strconv.Itoa(args.PageNo)
		}
		if args.PageSize > 0 {
			params["pageSize"] = strconv.Itoa(args.PageSize)
		}
		switch args.LBType {
		case LBBackendTypeContainer:
			params["lbType"] = string(LBBackendTypeContainer)
		case LBBackendTypeVM:
			params["lbType"] = string(LBBackendTypeVM)
		}
	}

	url := "/v1/blb"
	req, err := bce.NewRequest("GET", c.GetURL(c.Endpoint, url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result ListBLBResult
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (c *client) GetBLB(ctx context.Context, blbID string, option *bce.SignOption) (*BLB, error) {
	if blbID == "" {
		return nil, errors.New("empty blb id")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	url := fmt.Sprintf("/v1/blb/%s", blbID)
	req, err := bce.NewRequest("GET", c.GetURL(c.Endpoint, url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result BLB
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

type CreateBLBListenerParams struct {
	FrontendPort FrontendPort  `json:"frontendPort"`
	BackendPort  int           `json:"backendPort"`
	LBMode       LBForwardMode `json:"lbMode"`
	HealthCheck  HealthCheck   `json:"healthCheck"`
}

type FrontendPort struct {
	Protocol Protocol `json:"protocol"`
	Port     int      `json:"port"`
}

type HealthCheck struct {
	TimeoutInSeconds   int    `json:"timeoutInSeconds"`
	IntervalInSeconds  int    `json:"intervalInSeconds"`
	UnhealthyThreshold int    `json:"unhealthyThreshold"`
	HealthyThreshold   int    `json:"healthyThreshold"`
	HealthCheckString  string `json:"healthCheckString"`
	HealthCheck        bool   `json:"healthcheck"`
}

func (c *client) CreateBLBListener(ctx context.Context, blbID string, args *CreateBLBListenerParams, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("/v1/blb/%s/monitor", blbID)
	req, err := bce.NewRequest("POST", c.GetURL(c.Endpoint, url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

type Backend struct {
	Name   string `json:"name"`
	IP     string `json:"ip"`
	Weight int    `json:"weight"`
}

type CreateBLBBackendParams struct {
	BindingForms []*BLBBackendDeployment `json:"bindingForms"`
}

type BLBBackendDeployment struct {
	DeploymentID string    `json:"deploymentId"`
	PodWeight    []Backend `json:"podWeight"`
}

func (c *client) CreateBLBBackend(ctx context.Context, blbID string, args *CreateBLBBackendParams, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("/v1/blb/%s/binding", blbID)
	req, err := bce.NewRequest("POST", c.GetURL(c.Endpoint, url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

type BackendStats struct {
	Health   bool     `json:"health"`
	Port     int      `json:"port"`
	Protocol Protocol `json:"protocol"`
}

type BLBBackend struct {
	PodName      string         `json:"podName"`
	PodStatus    string         `json:"podStatus"`
	PodIP        string         `json:"podIp"`
	BackendPorts []BackendStats `json:"backendPort"`
	Weight       int            `json:"weight"`
}

type GetBLBBackendsResult struct {
	PageNo     int           `json:"pageNo"`
	PageSize   int           `json:"pageSize"`
	TotalCount int           `json:"totalCount"`
	Result     []*BLBBackend `json:"result"`
}

func (c *client) GetBLBBackends(ctx context.Context, blbID string, option *bce.SignOption) (*GetBLBBackendsResult, error) {
	if blbID == "" {
		return nil, errors.New("empty blb id")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	url := fmt.Sprintf("/v1/blb/%s/binded", blbID)
	req, err := bce.NewRequest("GET", c.GetURL(c.Endpoint, url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result GetBLBBackendsResult
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (c *client) DeleteBLB(ctx context.Context, blbID string, option *bce.SignOption) error {
	if blbID == "" {
		return errors.New("empty blb id")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	url := fmt.Sprintf("/v1/blb/%s", blbID)
	req, err := bce.NewRequest("DELETE", c.GetURL(c.Endpoint, url, params), nil)
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

type DeleteBLBBackendsParam struct {
	ToBeDeletedBLBBackends []*ToBeDeletedBLBBackend `json:"podWeightList"`
}

type ToBeDeletedBLBBackend struct {
	IP     string `json:"ip"`
	Name   string `json:"name"`
	Weight int    `json:"weight"`
}

func (c *client) DeleteBLBBackends(ctx context.Context, blbID string, args *DeleteBLBBackendsParam, option *bce.SignOption) error {
	if blbID == "" {
		return errors.New("empty blb id")
	}

	if args == nil || len(args.ToBeDeletedBLBBackends) == 0 {
		return nil
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("/v1/blb/%s/binded", blbID)
	req, err := bce.NewRequest("DELETE", c.GetURL(c.Endpoint, url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

type ListAvailableDeploymentForBLBParams struct {
	Keyword  string
	PageNo   int
	PageSize int
}

type LBDeployment struct {
	ServiceName     string          `json:"serviceName"`
	DeploymentName  string          `json:"deploymentName"`
	CustomOrigName  string          `json:"customOrigName"`
	ServiceID       string          `json:"serviceId"`
	DeploymentType  string          `json:"deploymentType"` // StatefulSet or VmReplicas
	Region          Region          `json:"region"`
	ServiceProvider ServiceProvider `json:"serviceProvider"`
	City            City            `json:"city"`
	Replicas        int             `json:"replicas"`
	PodCPU          int             `json:"podCpu"`
	PodMemory       int             `json:"podMemory"`
	PodGPU          int             `json:"podGpu"`
	PodDataStorage  string          `json:"podDataStorage"`
	PodIpRequired   bool            `json:"podIpRequired"`
	CreateTime      int64           `json:"createTime"` // timestamp
	UpdateTime      int64           `json:"updateTime"` // timestamp
	Sata            int             `json:"sata"`
	Nvme            int             `json:"nvme"`
	DataDiskNum     int             `json:"dataDiskNum"`
}

type ListAvailableDeploymentForBLBResult struct {
	PageNo     int             `json:"pageNo"`
	PageSize   int             `json:"pageSize"`
	TotalCount int             `json:"totalCount"`
	Result     []*LBDeployment `json:"result"`
}

func (c *client) ListAvailableDeploymentForBLB(ctx context.Context, blbID string, args *ListAvailableDeploymentForBLBParams, option *bce.SignOption) (*ListAvailableDeploymentForBLBResult, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
		"blbId":       blbID,
	}

	if args != nil {
		if args.PageNo > 0 {
			params["pageNo"] = strconv.Itoa(args.PageNo)
		}
		if args.PageSize > 0 {
			params["pageSize"] = strconv.Itoa(args.PageSize)
		}
		if args.Keyword != "" {
			params["keyword"] = args.Keyword
		}
	}

	url := fmt.Sprintf("/v1/blb/%s/binding", blbID)
	req, err := bce.NewRequest("GET", c.GetURL(c.Endpoint, url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result ListAvailableDeploymentForBLBResult
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}
