/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description:
 * @File:  types
 * @Version: 1.0.0
 * @Date: 2020-07-22 16:21
 */

package alert

import (
	"context"
)

// Alerter - 定义发送报警方法
type Alerter interface {
	SendMarkDownMessage(ctx context.Context, msg string) error
	SendMessage(ctx context.Context, msg string) error
	SendMessageAtUsers(ctx context.Context, msg string, atUserList []string) error
}
