/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description:
 * @File:  alert
 * @Version: 1.0.0
 * @Date: 2020-07-22 13:45
 */

package alert

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
)

const (
	DefaultURL = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d7958421ff743d032925d204b1a4ca8b4"
)

// hi - 百度 Hi 实现 alert.Interface
type hi struct {
	WebhookURL string
}

// NewHiClient - 初始化 HiClient
func NewHiClient(webhookURL string) Alerter {
	if webhookURL == "" {
		webhookURL = DefaultURL
	}
	return &hi{
		WebhookURL: webhookURL,
	}
}

// HiMessage - Hi 消息
type HiMessage struct {
	HMessage HMessage `json:"message"`
}

// HMessage - Hi 消息
type HMessage struct {
	BodyList []Body `json:"body"`
}

// Body - 消息类型
type Body struct {
	Type    string   `json:"type"`
	Content string   `json:"content"`
	ATUser  []string `json:"atuserids"`
	ATAll   bool     `json:"atall"`
}

// SendMarkDownMessage 将 markdown 消息发送到飞书机器人
// 参数 ctx 上下文对象，msg markdown 消息字符串
// 返回值 error 当发送失败时返回错误信息
func (h *hi) SendMarkDownMessage(ctx context.Context, msg string) error {
	// 构建消息
	bl := []Body{
		{
			Type:    "MD",
			Content: msg,
		},
	}

	hm := HiMessage{
		HMessage: HMessage{
			BodyList: bl,
		},
	}

	return Send(ctx, h.WebhookURL, hm)
}

// SendMessage 使用给定的 webhook URL 和消息发送一个 Hi 消息到钉钉机器人。
// 如果发送成功，返回 nil；如果发送失败，则返回相应的错误信息
func (h *hi) SendMessage(ctx context.Context, msg string) error {
	// 构建消息
	bl := []Body{
		{
			Type:    "TEXT",
			Content: msg,
		},
	}

	hm := HiMessage{
		HMessage: HMessage{
			BodyList: bl,
		},
	}

	return Send(ctx, h.WebhookURL, hm)
}

// SendMessageAtUsers 通过atUserList将消息发送到webhookURL的Hi机器人。
// 参数：
// ctx：上下文对象，用于取消请求或超时控制等功能。
// msg：要发送的文本消息内容。
// atUserList：待@用户列表，以字符串数组形式传递。
// 返回值：
// 当成功发送消息时返回nil；当发生错误时返回对应的error对象。
func (h *hi) SendMessageAtUsers(ctx context.Context, msg string, atUserList []string) error {
	bl := []Body{
		{
			Type:    "TEXT",
			Content: msg,
		},
		{
			Type:   "AT",
			ATUser: atUserList,
			ATAll:  false,
		},
	}
	hm := HiMessage{
		HMessage: HMessage{
			BodyList: bl,
		},
	}

	return Send(ctx, h.WebhookURL, hm)
}

// Send 用于向指定 URL 发送 HTTP 请求，并将 HiMessage 对象序列化为 JSON 字符串
// 参数 ctx：上下文对象，用于取消请求和记录日志
// 参数 url：目标 URL
// 参数 hm：待发送的 HiMessage 对象
// 返回值：错误信息
func Send(ctx context.Context, url string, hm HiMessage) error {
	data, err := json.Marshal(hm)
	if err != nil {
		logger.Errorf(ctx, "json Marshal failed: %s", err)
		return err
	}

	// 发送消息
	var jsonStr = []byte(fmt.Sprintf("%s", data))
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		logger.Errorf(ctx, "http post failed: %s", err)
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logger.Errorf(ctx, "http post failed: %s", err)
		return err
	}
	defer resp.Body.Close()

	// 接收响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Errorf(ctx, "read  resp body failed: %s", err)
		return err
	}

	logger.Infof(ctx, "Response.Body: %s", string(body))
	return nil
}
