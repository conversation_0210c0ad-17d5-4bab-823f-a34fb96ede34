---
description: 
globs: 
alwaysApply: false
---
 # 容器网络相关e2e rules

 本文档适用于编写e2e框架下的容器网络相关的自动化测试case，用于积累已知知识方便编辑case的编写一致性与迁移的易用性。

 ## 通用条件

 >本段适用于描述对于所有e2e框架下network相关的case的编写工作

- 请使用 go 语言编写代码，并要关注本文档提到的内容
- 请提供完整的测试用例实现，包括必要的初始化步骤、测试执行流程、验证方法和清理过程。
- 测试用例应当遵循现有的CCE测试框架结构，使用合适的API和工具来验证测试结果。
- 确认工作目录为e2e-test，这样才可以使用下面提到的相对路径
- 用 go 语言编写代码到目录 ./internal/cases/network 中，并且编码风格保持一致，生成的 go 文件中框架函数不可缺失且函数签名不可变
- 对于集群中已经定义的 crd 的访问获取方法在目录 ./internal/logic/kube 下实现
- 对于集群中的 crd 的定义在目录 ./pkg/types 下
- 如果遇到了导入包问题，可以参考 ./internal/cases 中的其他实现
- case过程中创建deploy的方式，参考 ./internal/cases/network中的代码的实现
- case过程中对节点组的操作，参考 ./internal/cases/network中的代码的实现
- case运行过程可能出现失败，要保证case的健壮性，即重复执行时，不应因上次回归测试失败的残留而导致本次测试失败。这也就是说需要在创建资源前执行清理已存在资源，并且在框架的Clean函数中执行全量兜底清理
- 请给出较详细的中文注释
- 编写的代码要可以通过 staticcheck
- 每次编写代码后，要展示：
    - 你进行了什么操作
    - 操作的预期结果是什么，实际执行结果是什么
    - 这里的验证是否完善，是否还有边界值没有考虑到
    - 如果不够完美，请继续完善上面的代码，使最后验证时这三个内容能够达到完美
- 符合框架的测试用例模板（这里是一个现有可用的测试用例的代码片段，有截取）
```
//其中调用到的函数在同文件中实现这里并未截取
const (
    // CheckCRDResourcesCaseName - case 名字
    CheckCRDResourcesCaseName cases.CaseName = "CheckCRDResources"
)

func init() {
    // 注册 CaseName
    cases.AddCase(context.TODO(), CheckCRDResourcesCaseName, NewCheckCRDResources)
}

type checkCRDResources struct {
    base *cases.BaseClient
}

// NewCheckCRDResources - 测试案例
func NewCheckCRDResources(ctx context.Context) cases.Interface {
    return &checkCRDResources{}
}

func (c *checkCRDResources) Init(ctx context.Context, config []byte,
    base *cases.BaseClient) error {
    if base == nil {
        return fmt.Errorf("base is nil")
    }

    c.base = base
    return nil
}

func (c *checkCRDResources) Name() cases.CaseName {
    return CheckCRDResourcesCaseName
}

func (c *checkCRDResources) Desc() string {
    return "测试集群中自定义CRD资源的注册与CR资源的获取"
}

func (c *checkCRDResources) Check(ctx context.Context) ([]cases.Resource, error) {
    // 检查所有网络相关CRD是否注册成功
    logger.Infof(ctx, "开始检查CRD注册情况...")

    // 测试 ENI CRD
    logger.Infof(ctx, "检查 ENI CRD...")
    if err := c.checkENICR(ctx); err != nil {
        logger.Warnf(ctx, "检查 ENI CR 失败: %v", err)
    }

    // 测试 NRS CRD
    logger.Infof(ctx, "检查 NetworkResourceSet CRD...")
    if err := c.checkNetworkResourceSetCR(ctx); err != nil {
        logger.Warnf(ctx, "检查 NetworkResourceSet CR 失败: %v", err)
    }

    // 测试 CCEEndpoint CRD
    logger.Infof(ctx, "检查 CCEEndpoint CRD...")
    if err := c.checkCCEEndpointCR(ctx); err != nil {
        logger.Warnf(ctx, "检查 CCEEndpoint CR 失败: %v", err)
    }

    // 测试 ClusterPodSubnetTopologySpread CRD
    logger.Infof(ctx, "检查 ClusterPodSubnetTopologySpread CRD...")
    if err := c.checkClusterPodSubnetTopologySpreadCR(ctx); err != nil {
        logger.Warnf(ctx, "检查 ClusterPodSubnetTopologySpread CR 失败: %v", err)
    }

    // 测试 NetResourceConfigSet CRD
    logger.Infof(ctx, "检查 NetResourceConfigSet CRD...")
    if err := c.checkNetResourceConfigSetCR(ctx); err != nil {
        logger.Warnf(ctx, "检查 NetResourceConfigSet CR 失败: %v", err)
    }

    // 测试 PodSubnetTopologySpread CRD
    logger.Infof(ctx, "检查 PodSubnetTopologySpread CRD...")
    if err := c.checkPodSubnetTopologySpreadCR(ctx); err != nil {
        logger.Warnf(ctx, "检查 PodSubnetTopologySpread CR 失败: %v", err)
    }

    // 测试 Subnet CRD
    logger.Infof(ctx, "检查 Subnet CRD...")
    if err := c.checkSubnetCR(ctx); err != nil {
        logger.Warnf(ctx, "检查 Subnet CR 失败: %v", err)
    }

    logger.Infof(ctx, "CRD资源检查完成")
    return nil, nil
}

func (c *checkCRDResources) Clean(ctx context.Context) error {
    // 不需要清理任何资源
    return nil
}

func (c *checkCRDResources) Continue(ctx context.Context) bool {
    return true
}

func (c *checkCRDResources) ConfigFormat() string {
    return ""
}
```
## 业务逻辑理解

>本段用于详细描述可能用到的具体业务逻辑
>需不断通过不同case遇到的实际情况人为积累补充

**注意：本段是按需取用，如果prompt中有提到需重点关注的相关业务逻辑，请关注，否则根据prompt内容自行判断逻辑是否相关。无需全部学习一次以避免理解偏差或冗余。**

### 1. configmap

- 容器网络相关的configmap指的是集群中的kube-system命名空间下的configmap对象cce-network-v2-config
- 可以通过客户端标准访问集群的方式获得本configmap
- 要关注的configmap内容是在其data.cced字段下的内容
- 对于configmap的修改操作要保持其格式不变，不然会导致集群产生不可预期的错误
- 对于configmap的添加操作是因为有默认不显示的内容，添加时候也要保持原有内容不变，添加内容也要加到其data.cced字段下
- 以下是一个运行中集群的configmap示例，这里提供以便参考
```
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    addonmanager.kubernetes.io/mode: EnsureExists
  name: cce-network-v2-config
  namespace: kube-system
data:
  cced: |
    annotate-k8s-node: true
    api-rate-limit:
      bcecloud/apis/v1/AttachENI: rate-limit:5/1s,rate-burst:5,max-wait-duration:30s,parallel-requests:5,log:true
      bcecloud/apis/v1/BatchAddPrivateIP: rate-limit:5/1s,rate-burst:10,max-wait-duration:15s,parallel-requests:5,log:true
      bcecloud/apis/v1/BatchDeletePrivateIP: rate-limit:5/1s,rate-burst:10,max-wait-duration:15s,parallel-requests:5,log:true
      bcecloud/apis/v1/CreateENI: rate-limit:5/1s,rate-burst:5,max-wait-duration:30s,parallel-requests:5,log:true
      bcecloud/apis/v1/DescribeSubnet: rate-limit:5/1s,rate-burst:5,max-wait-duration:30s,parallel-requests:5
      bcecloud/apis/v1/StatENI: rate-limit:10/1s,rate-burst:15,max-wait-duration:30s,parallel-requests:10
    auto-create-network-resource-set-resource: true
    bbcEndpoint: bbc.gz.baidubce.com
    bccEndpoint: bcc.gz.baidubce.com
    bce-cloud-access-key: ""
    bce-cloud-country: cn
    bce-cloud-host: cce-gateway-pre.gz.baidubce.com
    bce-cloud-region: gz
    bce-cloud-secure-key: ""
    bce-cloud-vpc-id: vpc-5023zzq9t850
    burstable-mehrfach-eni: 0
    cce-cluster-id: cce-zgdmqsba
    cce-endpoint-gc-interval: 20s
    cluster-pool-ipv4-cidr:
    - ***********/16
    cluster-pool-ipv4-mask-size: 25
    clusterip-ipv4-cidrs:
    - **********/16
    debug: false
    default-api-burst: 1000
    default-api-qps: 500
    default-api-timeout: 30s
    disable-eni-crd: false
    eipEndpoint: eip.gz.baidubce.com
    enable-ipv4: true
    enable-ipv6: false
    enable-metrics: true
    enable-monitor: false
    enable-rdma: true
    enable-remote-fixed-ip-gc: false
    endpoint-gc-interval: 30s
    eni-enterprise-security-group-ids: ""
    eni-install-source-based-routing: true
    eni-pre-allocate-num: 1
    eni-resource-resync-workers: 100
    eni-route-table-offset: 127
    eni-security-group-ids:
    - g-enmeedzgcdg3
    eni-subnet-ids:
    - sbn-r9tzqttqy8rk
    eni-use-mode: Secondary
    excess-ip-release-delay: 2
    ext-cni-plugins:
    - endpoint-probe
    fixed-ip-allocate-timeout: 30s
    fixed-ip-ttl-duration: 87600h
    gops-port: 19891
    health-port: 19879
    ip-allocation-timeout: 30s
    ipam: vpc-eni
    ippool-max-above-watermark: 1
    ippool-min-allocate-ips: 10
    ippool-pre-allocate: 2
    k8s-api-discovery: false
    k8s-client-burst: 100
    k8s-client-qps: 50
    leader-election-lease-duration: 60s
    leader-election-renew-deadline: 30s
    log-driver: syslog
    log-opt: |
      {"syslog.level":"info","syslog.facility":"local5"}
    mtu: 1500
    net-device-driver: veth-pair
    nodes-gc-interval: 30s
    nrs-resource-resync-workers: 500
    operator-api-serve-addr: :19234
    operator-prometheus-serve-addr: :19965
    pprof: false
    pprof-port: 14386
    prometheus-serve-addr: :19962
    rdma-resource-resync-workers: 500
    read-cni-conf: /etc/cce/cni-config-template/cce-ptp.template
    release-excess-ips: true
    resource-resync-interval: 20s
    resource-resync-workers: 64
    skip-crd-creation: true
    skip-manager-node-labels:
      type: virtual-kubelet
    subnet-resource-resync-workers: 10
    vpc-cidrs:
    - ***********/16
    write-cni-conf-when-ready: /etc/cni/net.d/00-cce-cni.conflist
```

### 2. 重启网络组件

- 这里的重启逻辑指的是重启实际在集群中运行的cni相关的网络组件，包括configmap也需要重启网络组件才生效，因为configmap的读入是在网络组件启动时候一次完成。
- 如果提到只需要重启其中一个网络组件，则无需重启以下两个。
- **重启agent**：重启集群中命名空间kube-system下面的Daemonset cce-network-agent，重启可以通过增加annotation 并update来实现，通过访问集群获取这个daemonset观察重启后pod是否就绪，等待就绪并确认后才算完成一次网络组件重启。
- **重启operator**：重启集群中命名空间kube-system下的Deployment cce-network-operator,重启可以通过增加annotation并update来实现，通过访问集群获取这个deployment观察重启后pod是否就绪，等待就绪并确认后才算完成一次网络组件重启。

### 3. ip回收与ip释放

- 这里是为了明确两个不同概念。
- **ip释放**：容器网络中对于ip释放的解释为当容器被删除时，其分配的ip会正确地回到节点资源池中，可以通过检查nrs中可用资源有这个ip并且已占用字段中无这个ip来确认ip完成了释放。
- **ip回收**：而对于ip回收的概念的理解应为，当集群配置了相关参数时候，其各节点资源池的符合回收条件的数量的ip会被回收到底层平台中，相关的configmap中的参数为：
    - ippool-max-above-watermark
    - ippool-pre-allocate
    - release-excess-ips  #需要为true
    - burstable-mehrfach-eni #需要为0
    - excess-ip-release-delay 
- 可以通过追踪相对路径为./baiducloud-cce-cni-driver下的相关参数的使用逻辑代码来完整理解ip回收的相关概念

### 4. nrcs

- 首先提供一个nrcs的运行中集群的示例：
```
apiVersion: cce.baidubce.com/v2alpha1
kind: NetResourceConfigSet
metadata:
  name: nrcs-example
spec:
    priority: 0
    selector:
        matchLabels:
            instance-group-id: cce-ig-i3ezo81s
    agent:
        eni-subnet-ids:
        - sbn-v5ze9se70289 # 不在集群eni子网中
        burstable-mehrfach-eni: 0 # 区别于cce-network-v2-config
        ippool-pre-allocate-eni: 2 # 区别于cce-network-v2-config
        ippool-min-allocate: 9 # 区别于cce-network-v2-config
        ippool-pre-allocate: 1 # 区别于cce-network-v2-config
        ippool-max-above-watermark: 23 # 区别于cce-network-v2-config
```
- nrcs对于新增节点可以直接生效，但是对于存量节点需要重启agent才会生效。
- nrcs生效的表现为对应节点的nrs中对应参数被修改为nrcs中提供的对应内容，具体nrs中的对应字段为
    - eni.preAllocateENI
    - eni.subnet-ids
    - eni.preAllocateENI
    - ipam.max-above-watermark
    - ipam.max-allocate
    - ipam.min-allocate
- 当有多个nrcs选中同一个节点时候，其priority高的会生效
- 其具体生效方式以及作用原理在./baiducloud-cce-cni-driver中实现，简述为以crd的形式注册，cr的形式创建，对应informor机制用声明式控制器原理实现功能。

### 5. 节点与节点组

- 节点组是一种可以指定节点规格进行扩缩容节点的方式。
- 在本代码库中有详细的调用api操作节点组伸缩的方法。
- 集群同时支持非节点组形式添加节点，且较为常见。也就是说集群中有在节点组中的节点，也有不在节点组的节点。

### 6. psts 

- psts是网络功能中的高级能力，用于提供对于特定ip（或ip段）的网络编排能力。
- 其具体生效方式以及作用原理在./baiducloud-cce-cni-driver中实现，简述为以crd的形式注册，cr的形式创建，对应informor机制用声明式控制器原理实现功能。
- 以下是几个集群中运行的psts的示例：
```
apiVersion: cce.baidubce.com/v2
kind: PodSubnetTopologySpread
metadata:
    name: default
    namespace: default
spec:
      # 多个子网拓扑约束之间的优先级，排序越靠前，优先级越大
      priority: 0
      name: default-psts
      strategy:
        releaseStrategy: TTL
        type: Elastic
      subnets:
          sbn-e8evz4b81h05: []
      selector: 
          matchLabels:
              app: foo
```
```
apiVersion: cce.baidubce.com/v2
kind: PodSubnetTopologySpread
metadata:
    name: example-fixed-topology
    namespace: default
spec:
    priority: 0
    subnets:
      sbn-cpqgw1ebdjwi:
        - family: "4"
          range:
          - start: ***********
            end: ************
    strategy:
      releaseStrategy: Never
      type: Fixed
      enableReuseIPAddress: true
    selector:
      matchLabels:
        app: fixedIPApp
```
- 额外提示，通过阅读./baiducloud-cce-cni-driver中相关代码可以知道，psts的作用原理中可以分析出来，psts使用的子网是要隐式使用，而不能创建集群时或者添加子网时显示添加到任意集群，不然会出现分配重建固定ip的pod ip时候，可能发生的ip已经被本集群或者其他任意集群的pod占用的情况，导致分配失败pod创建失败。
- 上述的检测方法就是psts中选定的子网并不能在集群的cr中查询得到（同时也包含同vpc内其他任意集群都不能查询到），这个需要手动控制测试用例的config提供的子网内容，或者想办法用到psts相关内容时候执行一次查询验证，可以在相关测试用例的编辑需求提供的prompt中关注这一条。

### 7. 容器扩缩容

- 由于网络相关的测试都需要结合容器来验证，所以容器扩缩容是一个必要步骤。
- 由于是在私有网络环境上，并非所有的容器镜像都能正确获取，所以在涉及容器扩缩容验证某些网络特性或功能时候，使用这里给出的一个可用样例，（当然在使用过程中要结合例如psts等更改对应标签信息，或者节点选择信息）：
```
apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment-example
  labels:
    app: nginx
spec:
  replicas: 2
  minReadySeconds: 0
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      nodeSelector:
        kubernetes.io/hostname: *************
      restartPolicy: Always
      containers:
        - name: nginx
          image: registry.baidubce.com/cce/nginx-alpine-go:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 80
          livenessProbe: 
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 20
            timeoutSeconds: 5
            periodSeconds: 5
          readinessProbe: 
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 5
```
- 注意这里的镜像是一定不能替换的，否则会出现容器创建失败的情况。包括镜像的地址和tag 

## 自然语言对照

>本段适用于对prompt的一致性提升，以期在每次chat的过程中，对prompt中同样的表述大模型可以获得相同的理解。
>结合业务逻辑段落，可以达到较好的表述效果。