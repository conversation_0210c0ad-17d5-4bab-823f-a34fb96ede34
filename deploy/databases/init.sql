/*!40101 SET @OLD_CHARACTER_SET_CLIENT = @@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS = @@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION = @@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS = @@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS = 0 */;
/*!40101 SET @OLD_SQL_MODE = @@SQL_MODE, SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES = @@SQL_NOTES, SQL_NOTES = 0 */;

CREATE DATABASE `cloud_native_test` CHARACTER SET utf8;

# Dump of table cce_test_job
# ------------------------------------------------------------

DROP TABLE IF EXISTS `cce_test_job`;

CREATE TABLE `cce_test_job`
(
    `id`            bigint(11) unsigned NOT NULL AUTO_INCREMENT,
    `batch_id`      char(15)            NOT NULL DEFAULT '',
    `region`        varchar(10)         NOT NULL DEFAULT '',
    `cluster_id`    char(12)            NOT NULL DEFAULT '',
    `cluster_name`  varchar(128)        NOT NULL DEFAULT '',
    `name`          varchar(128)        NOT NULL DEFAULT '',
    `success_count` int(11)             NOT NULL,
    `failed_count`  int(11)             NOT NULL,
    `cost`          bigint(20)          NOT NULL DEFAULT '0',
    `finished_at`   datetime            NOT NULL DEFAULT '1970-01-01 08:00:00',
    `created_at`    datetime            NOT NULL DEFAULT '1970-01-01 08:00:00',
    `updated_at`    datetime            NOT NULL DEFAULT '1970-01-01 08:00:00',
    PRIMARY KEY (`id`),
    KEY `idx_batch_id_region` (`batch_id`, `region`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

# Dump of table cce_cluster_combo
# ------------------------------------------------------------

DROP TABLE IF EXISTS `cce_cluster_combo`;

CREATE TABLE `cce_cluster_combo`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT,
    `region`          varchar(10)      NOT NULL DEFAULT '',
    `cluster_id`      char(12)         NOT NULL DEFAULT '',
    `master_type`     varchar(20)      NOT NULL DEFAULT '',
    `k8s_version`     varchar(20)      NOT NULL DEFAULT '',
    `runtime_type`    varchar(20)      NOT NULL DEFAULT '',
    `runtime_version` varchar(20)      NOT NULL DEFAULT '',
    `network_mode`    varchar(128)     NOT NULL DEFAULT '',
    `kube_proxy`      varchar(20)      NOT NULL DEFAULT '',
    `created_at`      datetime         NOT NULL DEFAULT '1970-01-01 08:00:00',
    `updated_at`      datetime         NOT NULL DEFAULT '1970-01-01 08:00:00',
    PRIMARY KEY (`id`),
    KEY `idx_region_cluster_id` (`region`, `cluster_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;



# Dump of table cce_cluster_perf
# ------------------------------------------------------------

DROP TABLE IF EXISTS `cce_cluster_perf`;

CREATE TABLE `cce_cluster_perf`
(
    `id`                              int(11) unsigned NOT NULL AUTO_INCREMENT,
    `region`                          varchar(10)      NOT NULL DEFAULT '',
    `cluster_id`                      char(12)         NOT NULL DEFAULT '',
    `create_cost`                     bigint(20)       NOT NULL DEFAULT '0',
    `create_cluster_lb_cost`          bigint(20)       NOT NULL DEFAULT '0',
    `create_cluster_eip_cost`         bigint(20)       NOT NULL DEFAULT '0',
    `wait_master_infrastructure_cost` bigint(20)       NOT NULL DEFAULT '0',
    `wait_api_server_access_cost`     bigint(20)       NOT NULL DEFAULT '0',
    `deploy_k8s_plugin_cost`          bigint(20)       NOT NULL DEFAULT '0',
    `created_at`                      datetime         NOT NULL DEFAULT '1970-01-01 08:00:00',
    `updated_at`                      datetime         NOT NULL DEFAULT '1970-01-01 08:00:00',
    PRIMARY KEY (`id`),
    KEY `idx_region_cluster_id` (`region`, `cluster_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;



# Dump of table cce_cni_pod
# ------------------------------------------------------------

DROP TABLE IF EXISTS `cce_cni_pod`;

CREATE TABLE `cce_cni_pod`
(
    `id`         int(11) unsigned NOT NULL AUTO_INCREMENT,
    `region`     varchar(10)      NOT NULL DEFAULT '',
    `cluster_id` char(12)         NOT NULL DEFAULT '',
    `ip_enough`  tinyint(1)       NOT NULL DEFAULT '0',
    `success`    tinyint(1)       NOT NULL DEFAULT '0',
    `err_msg`    text             NOT NULL,
    `cost`       bigint(20)       NOT NULL DEFAULT '0',
    `created_at` datetime         NOT NULL DEFAULT '1970-01-01 08:00:00',
    `updated_at` datetime         NOT NULL DEFAULT '1970-01-01 08:00:00',
    PRIMARY KEY (`id`),
    KEY `idx_region_cluster_id` (`region`, `cluster_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;



# Dump of table cce_event
# ------------------------------------------------------------

DROP TABLE IF EXISTS `cce_event`;

CREATE TABLE `cce_event`
(
    `id`                int(11) unsigned NOT NULL AUTO_INCREMENT,
    `region`            varchar(10)      NOT NULL DEFAULT '',
    `cluster_id`        char(12)         NOT NULL DEFAULT '',
    `cluster_name`      varchar(128)     NOT NULL DEFAULT '',
    `operation_type`    varchar(20)      NOT NULL DEFAULT '',
    `operation_success` tinyint(1)       NOT NULL DEFAULT '0',
    `analysis_data`     varchar(256)     NOT NULL DEFAULT '',
    `analysis_dig`      text             NOT NULL,
    `created_at`        datetime         NOT NULL DEFAULT '1970-01-01 08:00:00',
    `updated_at`        datetime         NOT NULL DEFAULT '1970-01-01 08:00:00',
    PRIMARY KEY (`id`),
    KEY `idx_region_cluster_id` (`region`, `cluster_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;



# Dump of table cce_lb_service
# ------------------------------------------------------------

DROP TABLE IF EXISTS `cce_lb_service`;

CREATE TABLE `cce_lb_service`
(
    `id`                int(11) unsigned NOT NULL AUTO_INCREMENT,
    `region`            varchar(10)      NOT NULL DEFAULT '',
    `cluster_id`        char(12)         NOT NULL DEFAULT '',
    `operation_type`    varchar(20)      NOT NULL DEFAULT '',
    `operation_success` tinyint(1)       NOT NULL DEFAULT '0',
    `cost`              bigint(20)       NOT NULL DEFAULT '0',
    `created_at`        datetime         NOT NULL DEFAULT '1970-01-01 08:00:00',
    `updated_at`        datetime         NOT NULL DEFAULT '1970-01-01 08:00:00',
    PRIMARY KEY (`id`),
    KEY `idx_region_cluster_id` (`region`, `cluster_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;



# Dump of table cce_node_combo
# ------------------------------------------------------------

DROP TABLE IF EXISTS `cce_node_combo`;

CREATE TABLE `cce_node_combo`
(
    `id`                int(11) unsigned NOT NULL AUTO_INCREMENT,
    `region`            varchar(10)      NOT NULL DEFAULT '',
    `cluster_id`        char(12)         NOT NULL DEFAULT '',
    `cluster_role`      varchar(20)      NOT NULL DEFAULT '',
    `cce_instance_id`   char(20)         NOT NULL DEFAULT '',
    `instance_uuid`     char(36)         NOT NULL DEFAULT '',
    `order_id`          char(32)         NOT NULL DEFAULT '',
    `available_zone`    varchar(20)      NOT NULL DEFAULT '',
    `machine_type`      varchar(20)      NOT NULL DEFAULT '',
    `instance_type`     varchar(20)      NOT NULL DEFAULT '',
    `instance_spec`     varchar(128)     NOT NULL DEFAULT '',
    `creation_type`     varchar(20)      NOT NULL DEFAULT '',
    `instance_group_id` varchar(128)     NOT NULL DEFAULT '',
    `os_name`           varchar(20)      NOT NULL DEFAULT '',
    `os_version`        varchar(128)     NOT NULL DEFAULT '',
    `created_at`        datetime         NOT NULL DEFAULT '1970-01-01 08:00:00',
    `updated_at`        datetime         NOT NULL DEFAULT '1970-01-01 08:00:00',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq` (`cce_instance_id`),
    KEY `idx_region_cluster_id` (`region`, `cluster_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;



# Dump of table cce_storage_pod
# ------------------------------------------------------------

DROP TABLE IF EXISTS `cce_storage_pod`;

CREATE TABLE `cce_storage_pod`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT,
    `region`      varchar(10)      NOT NULL DEFAULT '',
    `cluster_id`  char(12)         NOT NULL DEFAULT '',
    `name`        varchar(50)      NOT NULL DEFAULT '',
    `model`       varchar(20)      NOT NULL DEFAULT '',
    `success`     tinyint(1)       NOT NULL DEFAULT '0',
    `err_msg`     text             NOT NULL,
    `attach_cost` bigint(20)       NOT NULL DEFAULT '0',
    `created_at`  datetime         NOT NULL DEFAULT '1970-01-01 08:00:00',
    `updated_at`  datetime         NOT NULL DEFAULT '1970-01-01 08:00:00',
    PRIMARY KEY (`id`),
    KEY `idx_region_cluster_id` (`region`, `cluster_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;



# Dump of table cce_test_result
# ------------------------------------------------------------

DROP TABLE IF EXISTS `cce_test_result`;

CREATE TABLE `cce_test_result`
(
    `id`          bigint(11) unsigned NOT NULL AUTO_INCREMENT,
    `job_id`      bigint(11)          NOT NULL,
    `name`        varchar(128)        NOT NULL DEFAULT '',
    `success`     tinyint(1)          NOT NULL DEFAULT '0',
    `err_message` text                NOT NULL,
    `cost`        bigint(20)          NOT NULL DEFAULT '0',
    `finished_at` datetime            NOT NULL DEFAULT '1970-01-01 08:00:00',
    `created_at`  datetime            NOT NULL DEFAULT '1970-01-01 08:00:00',
    `updated_at`  datetime            NOT NULL DEFAULT '1970-01-01 08:00:00',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_job_id_name` (`job_id`, `name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;



/*!40111 SET SQL_NOTES = @OLD_SQL_NOTES */;
/*!40101 SET SQL_MODE = @OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS = @OLD_FOREIGN_KEY_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT = @OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS = @OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION = @OLD_COLLATION_CONNECTION */;
