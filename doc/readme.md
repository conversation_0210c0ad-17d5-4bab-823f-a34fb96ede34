# CCE 网络测试文档

## 测试用例：ReleaseExcessIPsTest

### 功能概述
本测试用例用于验证当开启`release-excess-ips`参数后，容器缩容时辅助IP资源是否能够被正确释放。这是网络组件cce-network-v2的一个重要功能，能够提高IP资源利用率，减少不必要的IP资源占用。

### 测试环境准备
1. 已部署的CCE集群
2. 安装了cce-network-v2网络组件
3. 集群中至少有一个可用节点

### 测试参数说明
测试用例中涉及以下主要参数：

1. **burstable-mehrfach-eni**：
   - 说明：控制弹性ENI功能
   - 测试值：设置为0，禁用弹性ENI功能

2. **release-excess-ips**：
   - 说明：控制是否释放多余IP
   - 测试值：设置为true，开启IP释放功能

3. **ippool-max-above-watermark**：
   - 说明：IP池高水位线以上保留的最大IP数量
   - 测试值：设置为2

4. **ippool-min-allocate-ips**：
   - 说明：IP池最小分配IP数量
   - 测试值：设置为2

### 测试流程
1. **环境准备**：
   - 修改ConfigMap `kube-system/cce-network-v2-config`，设置相关参数
   - 重启网络组件（cce-network-agent和cce-network-operator）使配置生效

2. **扩容测试**：
   - 在测试命名空间中创建一个带有多副本的Deployment
   - 等待所有Pod创建成功并分配IP地址
   - 记录初始状态下各节点的IP分配情况

3. **缩容测试**：
   - 缩减Deployment的副本数量
   - 监控节点上的IP分配情况变化
   - 验证不再使用的IP是否通过openAPI被释放

4. **清理恢复**：
   - 删除测试用的Deployment和命名空间
   - 恢复ConfigMap的原始配置

### 测试验证点
1. ConfigMap修改后，网络组件能够正常重启
2. Deployment扩容时，能够正常为Pod分配IP
3. Deployment缩容后，不再使用的IP能够被正确释放
4. IP释放后，相应节点的IP资源数量减少
5. 释放过程在合理的时间范围内完成

### 预期结果
- Pod缩容后，节点上的IP数量会减少
- 通过观察NetResourceSet资源的Status.Ipam.Used字段，可以确认IP释放情况
- 资源能够被及时、有效地回收

### 执行方法
通过以下命令执行测试：
```bash
./cce-e2e-test exec -g internal/casegroup/check-e2e-test.yaml
```

可以查看日志输出中的验证桩标记，了解测试过程中的关键步骤和状态变化。

### 注意事项
1. 测试过程中会修改集群配置，请在非生产环境执行
2. 测试完成后会自动恢复环境，但如果测试中断可能需要手动恢复
3. 测试结果可能受到集群负载、网络状态等因素影响

### 诊断和故障排除
1. 如果测试失败，首先检查是否有足够的节点资源
2. 检查网络组件是否成功重启，可以查看cce-network-agent和cce-network-operator的Pod状态
3. 查看NetResourceSet资源的状态，检查IP分配情况
4. 如果IP没有释放，检查ConfigMap的参数是否正确生效

---

*更新日期：2024-04-09* 