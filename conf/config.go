package conf

import (
	"context"
	"errors"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/spf13/viper"

	"icode.baidu.com/baidu/cce-test/e2e-test/internal/models"
	"icode.baidu.com/baidu/cce-test/e2e-test/pkg/logger"
	pluginclients "icode.baidu.com/baidu/cce-test/e2e-test/pkg/plugin/clients"
)

const (
	EnvBatchID                         = "BATCH_ID"
	DefaultCreateClusterTimeoutSeconds = 900
	DefaultReportWaitTimeoutSeconds    = 30
	DefaultBosJobLogPrefix             = "job-log"
)

const (
	defaultConfigDir  = "conf"
	defaultConfigType = "yaml"
)

// Config 定义 cluster-client 依赖配置
type Config struct {
	BatchID string `json:"BatchID"` // 数据库中标识本次运行批次信息

	Env      string   `json:"Env"`
	Region   string   `json:"Region"`
	Endpoint Endpoint `json:"Endpoint"`

	AccessKey          string `json:"AccessKey"`
	AccessKeySecret    string `json:"AccessKeySecret"`
	ResAccessKey       string `json:"ResAccessKey"`
	ResAccessKeySecret string `json:"ResAccessKeySecret"`

	MySQLEndpoint       string                `json:"MySQLEndpoint"` // 如果为空, 结果写本地
	UserKubeConfigType  models.KubeConfigType `json:"UserKubeConfigType"`
	DisableKubeProtobuf bool                  `json:"DisableKubeProtobuf"`
	KubeClientTimeout   time.Duration         `json:"KubeClientTimeout"` // request 超时，单位秒

	CreateClusterTimeoutSeconds int `json:"CreateClusterTimeoutSeconds"`

	PluginConfig *pluginclients.Config `json:"PluginConfig"` // 插件管理配置

	UserID                   string `json:"UserID"` // UserID
	BosBucket                string `json:"BosBucket"`
	UploadJobLog             bool   `json:"UploadJobLog"`
	ReportWaitTimeoutSeconds int    `json:"ReportWaitTimeoutSeconds"`

	QAMySQLEndpoint string `json:"QAMySQLEndpoint"`

	EnableInspect bool `json:"EnableInspect"`

	SkipCheckClusterInstances bool `json:"SkipCheckClusterInstances"` // 集群创建后是否跳过检查集群实例
}

// Endpoint 定义 cluster-client 依赖 Endpoint
type Endpoint struct {
	AIInfraEndpoint            string `json:"AIInfraEndpoint"`
	BLBEndpoint                string `json:"BLBEndpoint"`
	EIPEndpoint                string `json:"EIPEndpoint"`
	VPCEndpoint                string `json:"VPCEndpoint"`
	BCCEndpoint                string `json:"BCCEndpoint"`
	BOSEndpoint                string `json:"BOSEndpoint"`
	BECEndpoint                string `json:"BECEndpoint"`
	BLSEndpoint                string `json:"BLSEndpoint"`
	CFSEndpoint                string `json:"CFSEndpoint"`
	CCEV2Endpoint              string `json:"CCEV2Endpoint"`
	CCEV2HostEndpoint          string `json:"CCEV2HostEndpoint"`
	CCEProbeHostEndpoint       string `json:"CCEProbeHostEndpoint"`
	CCEServiceInternalEndpoint string `json:"CCEServiceInternalEndpoint"`
	AppBLBEndpoint             string `json:"AppBLBEndpoint"`
	CCEGatewayEndpoint         string `json:"CCEGatewayEndpoint"`
	MonitorEndpoint            string `json:"MonitorEndpoint"`
	AppServiceEndpoint         string `json:"AppServiceEndpoint"`
	HelmAccountEndpoint        string `json:"HelmAccountEndpoint"`
	HelmEndpoint               string `json:"HelmEndpoint"`
}

// LoadConfig 加载配置文件
func LoadConfig(ctx context.Context, configFilePath string, targetConfig any) (err error) {
	configFileDir := filepath.Dir(configFilePath)
	configFileName := filepath.Base(configFilePath)
	configFileExt := strings.TrimLeft(filepath.Ext(configFilePath), ".")

	// 如果没有指定配置文件目录，则使用默认的配置文件目录
	if configFileDir == "." {
		configFileDir = defaultConfigDir
	}
	// 如果配置文件缺少类型后缀，则使用默认的配置文件类型
	if configFileExt == "" {
		configFileExt = defaultConfigType
	}

	newViper := viper.New()
	newViper.AddConfigPath(configFileDir)
	newViper.SetConfigName(configFileName)
	newViper.SetConfigType(defaultConfigType)
	if err = newViper.ReadInConfig(); err != nil {
		return
	}
	if err = newViper.Unmarshal(targetConfig); err != nil {
		return
	}

	logger.Infof(ctx, "load config file success: %s", newViper.ConfigFileUsed())
	return
}

func CheckConfig(ctx context.Context, config *Config) error {
	// 默认等待 15 min
	if config.CreateClusterTimeoutSeconds == 0 {
		config.CreateClusterTimeoutSeconds = DefaultCreateClusterTimeoutSeconds
	}
	if config.BatchID == "" {
		config.BatchID = GetBatchID(ctx, true)
	}

	if config.Region == "" {
		return errors.New("Region is empty")
	}

	if config.AccessKey == "" {
		return errors.New("AccessKey is empty")
	}

	if config.AccessKeySecret == "" {
		return errors.New("AccessToken is empty")
	}

	if config.UserKubeConfigType == "" {
		return errors.New("UserKubeConfigType is empty")
	}

	if config.Endpoint.BLBEndpoint == "" {
		return errors.New("Endpoint.BLBEndpoint is empty")
	}

	if config.Endpoint.EIPEndpoint == "" {
		return errors.New("Endpoint.EIPEndpoint is empty")
	}

	if config.Endpoint.VPCEndpoint == "" {
		return errors.New("Endpoint.VPCEndpoint is empty")
	}

	if config.Endpoint.BCCEndpoint == "" {
		return errors.New("Endpoint.BCCEndpoint is empty")
	}

	if config.Endpoint.CCEV2Endpoint == "" {
		return errors.New("Endpoint.CCEV2Endpoint is empty")
	}

	if config.Endpoint.CCEV2HostEndpoint == "" {
		return errors.New("Endpoint.CCEV2HostEndpoint is empty")
	}

	if config.Endpoint.CCEProbeHostEndpoint == "" {
		return errors.New("Endpoint.CCEProbeHostEndpoint is empty")
	}

	if config.Endpoint.CCEServiceInternalEndpoint == "" {
		return errors.New("Endpoint.CCEServiceInternalEndpoint is empty")
	}

	if config.Endpoint.AppBLBEndpoint == "" {
		return errors.New("Endpoint.AppBLBEndpoint is empty")
	}

	if config.Endpoint.MonitorEndpoint == "" {
		return errors.New("Endpoint.MonitorEndpoint is empty")
	}

	if config.Endpoint.AppServiceEndpoint == "" {
		return errors.New("Endpoint.AppServiceEndpoint is empty")
	}

	if config.Endpoint.HelmAccountEndpoint == "" {
		return errors.New("Endpoint.HelmAccountEndpoint is empty")
	}

	if config.Endpoint.HelmEndpoint == "" {
		return errors.New("Endpoint.HelmEndpoint is empty")
	}

	if config.QAMySQLEndpoint == "" {
		return errors.New("QAMySQLEndpoint is empty")
	}

	if config.BosBucket == "" {
		return errors.New("BosBucket is empty")
	}

	return nil
}

func GetBatchID(ctx context.Context, renew bool) (batchID string) {
	envBatchID := os.Getenv(EnvBatchID)
	if envBatchID != "" {
		batchID = envBatchID
		return
	}
	if renew {
		batchID = time.Now().Format("20060102_150405")
		logger.Infof(ctx, "BatchID not set, use env `%s` or current time [%s] instead", EnvBatchID, batchID)
	}
	return
}
