package options

const WebhookCertDit = "/etc/webhook-certs"

type WebhookServerRunOptions struct {
	WebhookServer   bool
	VKPodToleration string
	VKNodeSelector  string
	IsVKManaged     bool // vk是否托管
	ProbeAddr       string
	Port            int
	CertDir         string
	Debug           bool
}

func NewWebhookServerRunOptions() *WebhookServerRunOptions {
	return &WebhookServerRunOptions{
		WebhookServer:   true,
		VKPodToleration: "{\"key\":\"virtual-kubelet.io/provider\",\"operator\":\"Equal\",\"value\":\"baidu\",\"effect\":\"NoSchedule\"}",
		VKNodeSelector:  "type=virtual-kubelet",
		IsVKManaged:     false,
		ProbeAddr:       ":8081",
		Port:            9443,
		CertDir:         WebhookCertDit,
		Debug:           false,
	}
}
