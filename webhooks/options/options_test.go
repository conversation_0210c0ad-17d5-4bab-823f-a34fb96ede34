package options

import "testing"

// TestNewWebhookServerRunOptions 是用于测试 NewWebhookServerRunOptions
// generated by Comate
func TestNewWebhookServerRunOptions(t *testing.T) {
	options := NewWebhookServerRunOptions()

	if !options.WebhookServer {
		t.<PERSON><PERSON>("Expected WebhookServer to be true, got %v", options.WebhookServer)
	}

	expectedToleration := "{\"key\":\"virtual-kubelet.io/provider\",\"operator\":\"Equal\",\"value\":\"baidu\",\"effect\":\"NoSchedule\"}"
	if options.VKPodToleration != expectedToleration {
		t.<PERSON>rf("Expected VKPodToleration to be %v, got %v", expectedToleration, options.VKPodToleration)
	}

	expectedSelector := "type=virtual-kubelet"
	if options.VKNodeSelector != expectedSelector {
		t.<PERSON>("Expected VKNodeSelector to be %v, got %v", expectedSelector, options.VKNodeSelector)
	}

	expectedProbeAddr := ":8081"
	if options.ProbeAddr != expectedProbeAddr {
		t.<PERSON>("Expected ProbeAddr to be %v, got %v", expectedProbeAddr, options.ProbeAddr)
	}

	expectedPort := 9443
	if options.Port != expectedPort {
		t.Errorf("Expected Port to be %v, got %v", expectedPort, options.Port)
	}

	expectedCertDir := "/etc/webhook-certs"
	if options.CertDir != expectedCertDir {
		t.Errorf("Expected CertDir to be %v, got %v", expectedCertDir, options.CertDir)
	}

	if options.Debug {
		t.Errorf("Expected Debug to be false, got %v", options.Debug)
	}
}
