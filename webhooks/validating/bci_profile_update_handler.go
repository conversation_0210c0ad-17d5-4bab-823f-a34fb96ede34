package validating

import (
	"context"
	"encoding/json"
	"net/http"
	"sync"
	"time"

	"github.com/virtual-kubelet/virtual-kubelet/log"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/baidubci/types"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/webhooks/options"
	corev1 "k8s.io/api/core/v1"
	corev1_listers "k8s.io/client-go/listers/core/v1"
	toolscache "k8s.io/client-go/tools/cache"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/webhook/admission"
)

type BciProfileValidateHandler struct {
	sync.Mutex
	mgr             manager.Manager
	decoder         *admission.Decoder
	namespaceLister corev1_listers.NamespaceLister
	configmapLister corev1_listers.ConfigMapLister
	debug           bool
}

func newBciProfileValidateHandler(options *options.WebhookServerRunOptions, mgr manager.Manager, decoder *admission.Decoder) (admission.Handler, error) {
	cache := mgr.GetCache()
	namespaceInformer, err := cache.GetInformer(context.TODO(), &corev1.Namespace{})
	if err != nil {
		log.G(context.TODO()).Errorf("BciProfileValidateHandler get namespace informer error %+v", err)
		return nil, err
	}
	namespaceLister := corev1_listers.NewNamespaceLister(namespaceInformer.(toolscache.SharedIndexInformer).GetIndexer())
	configmapInformer, err := cache.GetInformer(context.TODO(), &corev1.ConfigMap{})
	if err != nil {
		log.G(context.TODO()).Errorf("BciProfileValidateHandler get configmap informer error %+v", err)
		return nil, err
	}
	configmapLister := corev1_listers.NewConfigMapLister(configmapInformer.(toolscache.SharedIndexInformer).GetIndexer())
	return &BciProfileValidateHandler{
		mgr:             mgr,
		decoder:         decoder,
		namespaceLister: namespaceLister,
		configmapLister: configmapLister,
		debug:           options.Debug,
	}, nil
}

var _ admission.Handler = &BciProfileValidateHandler{}

func (bh *BciProfileValidateHandler) namespaceFn(namespaceName string) (*corev1.Namespace, error) {
	return bh.namespaceLister.Get(namespaceName)
}

func (bh *BciProfileValidateHandler) configMapFn(ns string, configmapName string) (*corev1.ConfigMap, error) {
	return bh.configmapLister.ConfigMaps(ns).Get(configmapName)
}

func (bh *BciProfileValidateHandler) Handle(ctx context.Context, req admission.Request) admission.Response {
	if req.Operation == "UPDATE" {
		// 逻辑实现
		if req.Kind.Kind == "ConfigMap" {
			startTime := time.Now()
			bh.Lock()
			defer func() {
				costTime := time.Since(startTime)
				log.G(ctx).Info("BciProfileValidateHandler handle configmap update cost %+v", costTime)
				bh.Unlock()
			}()
			cm := &corev1.ConfigMap{}
			err := bh.decoder.Decode(req, cm)
			if err != nil {
				log.G(ctx).Error("BciProfileValidateHandler decode comfigmap error %+v", err)
				return admission.Errored(http.StatusBadRequest, err)
			}
			if cm.Namespace != types.BciProfileConfigMapName && cm.GetName() != types.BciProfileConfigMapName {
				return admission.ValidationResponse(true, "pass")
			}

			log.G(ctx).Info("BciProfileValidateHandler handle comfigmap %+v", cm)
			bciProfileData := types.BCIProfileConfig{}
			if err := json.Unmarshal([]byte(cm.Data["config"]), &bciProfileData); err != nil {
				return admission.Errored(http.StatusBadRequest, err)
			}

			return admission.ValidationResponse(true, "pass")
		}
	}
	return admission.Allowed("")
}

func (bh *BciProfileValidateHandler) InjectDecoder(d *admission.Decoder) error {
	bh.decoder = d
	return nil
}
