package mutating

import (
	"context"
	"encoding/json"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/virtual-kubelet/virtual-kubelet/log"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/webhooks/options"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/webhooks/util"
	corev1 "k8s.io/api/core/v1"
	corev1_listers "k8s.io/client-go/listers/core/v1"
	toolscache "k8s.io/client-go/tools/cache"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/webhook/admission"
)

type VKPodCreateHandler struct {
	sync.Mutex
	mgr             manager.Manager
	decoder         *admission.Decoder
	namespaceLister corev1_listers.NamespaceLister
	nodeLister      corev1_listers.NodeLister
	debug           bool
	handleTopology  bool
	handleAffinity  bool
}

func newVKPodCreateHandler(options *options.WebhookServerRunOptions, mgr manager.Manager, decoder *admission.Decoder) (admission.Handler, error) {
	cache := mgr.GetCache()
	namespaceInformer, err := cache.GetInformer(context.TODO(), &corev1.Namespace{})
	if err != nil {
		log.G(context.TODO()).Errorf("VKPodCreateHAndler get namespace informer error %+v", err)
		return nil, err
	}
	namespaceLister := corev1_listers.NewNamespaceLister(namespaceInformer.(toolscache.SharedIndexInformer).GetIndexer())
	nodeInformer, err := cache.GetInformer(context.TODO(), &corev1.Node{})
	if err != nil {
		log.G(context.TODO()).Errorf("VKPodCreateHAndler get node informer error %+v", err)
		return nil, err
	}
	nodeLister := corev1_listers.NewNodeLister(nodeInformer.(toolscache.SharedIndexInformer).GetIndexer())
	handleTopology := strings.ToLower(util.GetEnv("HANDLE_TOPOLOGY", "false")) == "true"
	log.G(context.TODO()).Infof("mutating webhook handleTopology switch is %v", handleTopology)
	handleAffinity := strings.ToLower(util.GetEnv("HANDLE_AFFINITY", "false")) == "true"
	log.G(context.TODO()).Infof("mutating webhook handleAffinity switch is %v", handleTopology)
	return &VKPodCreateHandler{
		mgr:             mgr,
		decoder:         decoder,
		namespaceLister: namespaceLister,
		nodeLister:      nodeLister,
		debug:           options.Debug,
		handleTopology:  handleTopology,
		handleAffinity:  handleAffinity,
	}, nil
}

var _ admission.Handler = &VKPodCreateHandler{}

func (vh *VKPodCreateHandler) namespaceFn(namespaceName string) (*corev1.Namespace, error) {
	return vh.namespaceLister.Get(namespaceName)
}

func (vh *VKPodCreateHandler) nodeFn(nodeName string) (*corev1.Node, error) {
	return vh.nodeLister.Get(nodeName)
}

func (vh *VKPodCreateHandler) Handle(ctx context.Context, req admission.Request) admission.Response {
	if req.Operation == "CREATE" {
		// 逻辑实现
		if req.Kind.Kind == "Pod" {
			startTime := time.Now()
			vh.Lock()
			defer func() {
				costTime := time.Since(startTime)
				log.G(ctx).Info("VKPodCreateHAndler handle pod create cost %+v", costTime)
				vh.Unlock()
			}()
			pod := &corev1.Pod{}
			err := vh.decoder.Decode(req, pod)
			if err != nil {
				log.G(ctx).Error("VKPodCreateHAndler decode pod error %+v", err)
				return admission.Errored(http.StatusBadRequest, err)
			}
			log.G(ctx).Info("VKPodCreateHAndler handle pod %+v", pod)
			if util.IsBciPod(pod, vh.nodeFn) {
				// 如果是bci pod，则去掉tolerationSeconds字段
				util.MutateBciPodTolerations(ctx, pod)
			} else if util.IsBciPodWithTag(pod, vh.namespaceFn) {
				// 如果是bci tag pod，则添加nodeselector、tolerations字段，将pod调度到虚拟节点
				util.MutateBciPodTolerations(ctx, pod)
				util.AddBciNodeSelector(pod)
				util.AddBciTolerations(pod)
			}
			if vh.handleTopology {
				util.MutateTopologySpreadConstraints(ctx, pod)
			}
			if vh.handleAffinity {
				util.MutateAffinity(ctx, pod)
			}

			marshalled, err := json.Marshal(pod)
			if err != nil {
				return admission.Errored(http.StatusInternalServerError, err)
			}
			return admission.PatchResponseFromRaw(req.Object.Raw, marshalled)
		}
	}
	return admission.Allowed("")
}

func (vh *VKPodCreateHandler) InjectDecoder(d *admission.Decoder) error {
	vh.decoder = d
	return nil
}
