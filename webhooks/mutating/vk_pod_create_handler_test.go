package mutating

import (
	"context"
	"net/http"
	"testing"

	admissionv1 "k8s.io/api/admission/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/webhook/admission"

	"github.com/stretchr/testify/assert"
)

// TestInjectDecoder 是用于测试 InjectDecoder
// generated by Comate
func TestInjectDecoder(t *testing.T) {
	// 创建一个VKPodCreateHandler实例
	vh := &VKPodCreateHandler{}

	// 创建一个Decoder实例
	decoder := &admission.Decoder{}

	// 调用InjectDecoder方法
	err := vh.InjectDecoder(decoder)

	// 验证InjectDecoder方法是否正确地设置了decoder
	if vh.decoder != decoder {
		t.<PERSON><PERSON>("Expected decoder to be %v, but got %v", decoder, vh.decoder)
	}

	// 验证InjectDecoder方法是否返回了nil错误
	if err != nil {
		t.E<PERSON>rf("Expected err to be nil, but got %v", err)
	}
}

// TestHandle_CreatePod_InvalidPod 是用于测试 Handle_CreatePod_InvalidPod
// generated by Comate
func TestHandle_CreatePod_InvalidPod(t *testing.T) {
	vh := &VKPodCreateHandler{
		decoder: admission.NewDecoder(runtime.NewScheme()),
	}
	req := admission.Request{
		AdmissionRequest: admissionv1.AdmissionRequest{
			Operation: admissionv1.Operation("CREATE"),
			Kind: metav1.GroupVersionKind{
				Group:   "",
				Version: "v1",
				Kind:    "Pod",
			},
			Object: runtime.RawExtension{Raw: []byte("invalid-pod")},
		},
	}
	resp := vh.Handle(context.Background(), req)
	assert.Equal(t, int32(http.StatusBadRequest), resp.Result.Code)
}

// TestHandle_OtherOperations 是用于测试 Handle_OtherOperations
// generated by Comate
func TestHandle_OtherOperations(t *testing.T) {
	vh := &VKPodCreateHandler{
		decoder: &admission.Decoder{},
	}
	req := admission.Request{
		AdmissionRequest: admissionv1.AdmissionRequest{
			Operation: admissionv1.Operation("UPDATE"),
			Kind: metav1.GroupVersionKind{
				Group:   "",
				Version: "v1",
				Kind:    "Pod",
			},
			Object: runtime.RawExtension{Raw: []byte("{}")},
		},
	}
	resp := vh.Handle(context.Background(), req)
	assert.Equal(t, int32(http.StatusOK), resp.Result.Code)
}
