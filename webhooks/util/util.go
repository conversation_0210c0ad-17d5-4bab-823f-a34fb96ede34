package util

import (
	"context"
	"encoding/json"
	"github.com/virtual-kubelet/virtual-kubelet/log"
	"os"
	"reflect"

	corev1 "k8s.io/api/core/v1"
)

const (
	BCIPodTag = "baidubce.com/bci"
)

var (
	DefaultVKToleration = corev1.Toleration{
		Key:               "virtual-kubelet.io/provider",
		Value:             "baidu",
		Effect:            corev1.TaintEffectNoSchedule,
		Operator:          corev1.TolerationOpEqual,
		TolerationSeconds: nil,
	}
	VKTolerationKeys = []string{
		"virtual-kubelet.io/provider",
		"node.kubernetes.io/not-ready",
		"node.kubernetes.io/unreachable",
	}
	DefaultVKNodeSelectorKey   = "type"
	DefaultVKNodeSelectorValue = "virtual-kubelet"
)

func IsVKTolerationKey(str string) bool {
	for _, key := range VKTolerationKeys {
		if str == key {
			return true
		}
	}
	return false
}

func IsBciNode(node *corev1.Node) bool {
	if node.Spec.Taints != nil {
		for _, t := range node.Spec.Taints {
			if t.Key == DefaultVKToleration.Key && t.Value == DefaultVKToleration.Value {
				return true
			}
		}
	}
	return false
}

func IsBciPod(pod *corev1.Pod, getNodefn func(node string) (*corev1.Node, error)) bool {
	// 判断是否包含node selector
	if len(pod.Spec.NodeSelector) > 0 {
		value := pod.Spec.NodeSelector[DefaultVKNodeSelectorKey]
		if value == DefaultVKNodeSelectorValue {
			return true
		}
	}
	if pod.Spec.NodeName != "" {
		v1Node, err := getNodefn(pod.Spec.NodeName)
		if err == nil && IsBciNode(v1Node) {
			return true
		}
		return false
	}
	copyPod := pod.DeepCopy()
	for _, toleration := range copyPod.Spec.Tolerations {
		toleration.TolerationSeconds = nil
		if reflect.DeepEqual(toleration, DefaultVKToleration) {
			return true
		}
	}
	return false
}

func IsBciPodWithTag(pod *corev1.Pod, getNamespaceFn func(namespace string) (*corev1.Namespace, error)) bool {
	if pod.Labels[BCIPodTag] == "true" {
		return true
	}
	v1Namespace, err := getNamespaceFn(pod.Namespace)
	if err == nil && v1Namespace.Labels[BCIPodTag] == "true" {
		return true
	}
	return false
}

func AddBciTolerations(pod *corev1.Pod) {
	pod.Spec.Tolerations = append(pod.Spec.Tolerations, DefaultVKToleration)
}

func AddBciNodeSelector(pod *corev1.Pod) {
	pod.Spec.NodeSelector = map[string]string{DefaultVKNodeSelectorKey: DefaultVKNodeSelectorValue}
}

// MutateTopologySpreadConstraints 将 Pod 的 TopologySpreadConstraints 封装到 Annotation，否则 pod 无法调度到虚拟节点
//
// 参数：
//
//	pod *corev1.Pod: 需要修改 TopologySpreadConstraints 的 Pod 对象指针。
func MutateTopologySpreadConstraints(ctx context.Context, pod *corev1.Pod) {
	constraints := pod.Spec.TopologySpreadConstraints
	if constraints == nil || len(constraints) == 0 {
		return
	}
	if pod.Annotations == nil {
		pod.Annotations = make(map[string]string)
	}
	constraintsBytes, err := json.Marshal(constraints)
	if err != nil {
		log.G(ctx).Errorf("VKPodCreateHAndler marshal pod %s topologySpreadConstraints error %+v", pod.Name, err)
		return
	}
	pod.Spec.TopologySpreadConstraints = nil
	pod.Annotations["bci.virtual-kubelet.io/topology-spread-constraints"] = string(constraintsBytes)
}

// MutateAffinity 将 pod 的亲和性封装到 Annotation，否则 pod 无法调度到虚拟节点
//
// 参数：
//
//	ctx: 上下文信息，用于控制函数执行的上下文
//	pod: 需要修改亲和性规则的Pod对象
func MutateAffinity(ctx context.Context, pod *corev1.Pod) {
	affinity := pod.Spec.Affinity
	if affinity == nil {
		return
	}
	if pod.Annotations == nil {
		pod.Annotations = make(map[string]string)
	}
	affinity.PodAntiAffinity = nil
	if affinity.PodAffinity != nil {
		// 需要把 podAffinity 下的 namespace 设置为空，因为虚拟节点上的 pod 和 BCI 集群的 pod namespace 不一致
		for i := range affinity.PodAffinity.RequiredDuringSchedulingIgnoredDuringExecution {
			affinity.PodAffinity.RequiredDuringSchedulingIgnoredDuringExecution[i].Namespaces = []string{}
		}
	}
	affinityByte, err := json.Marshal(affinity)
	if err != nil {
		log.G(ctx).Errorf("VKPodCreateHAndler marshal pod %s affinity error %+v", pod.Name, err)
		return
	}
	pod.Spec.Affinity = nil
	pod.Annotations["bci.virtual-kubelet.io/affinity"] = string(affinityByte)
}

func MutateBciPodTolerations(ctx context.Context, pod *corev1.Pod) {
	for i := range pod.Spec.Tolerations {
		if IsVKTolerationKey(pod.Spec.Tolerations[i].Key) {
			// TolerationSeconds设置为nil，防止由于vk crash导致pod被迁移重建
			pod.Spec.Tolerations[i].TolerationSeconds = nil
		}
	}
}

func GetEnv(key, defaultValue string) string {
	value, found := os.LookupEnv(key)
	if found {
		return value
	}
	return defaultValue
}
