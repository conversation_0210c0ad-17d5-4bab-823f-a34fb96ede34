package util

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// TestIsVKTolerationKey 是用于测试 IsVKTolerationKey
// generated by Comate
func TestIsVKTolerationKey(t *testing.T) {
	testCases := []struct {
		input    string
		expected bool
	}{
		{"virtual-kubelet.io/provider", true},
		{"node.kubernetes.io/not-ready", true},
		{"node.kubernetes.io/unreachable", true},
		{"invalid-key", false},
		{"node.kubernetes.io/not-exist", false},
		{"", false},
	}

	for _, tc := range testCases {
		t.Run(tc.input, func(t *testing.T) {
			result := IsVKTolerationKey(tc.input)
			if result != tc.expected {
				t.Errorf("Expected %v, but got %v", tc.expected, result)
			}
		})
	}
}

// TestAddBciTolerations 是用于测试 AddBciTolerations
// generated by Comate
func TestAddBciTolerations(t *testing.T) {
	// 测试用例1：pod.Spec.Tolerations为空
	pod1 := &corev1.Pod{
		Spec: corev1.PodSpec{
			Tolerations: []corev1.Toleration{},
		},
	}
	AddBciTolerations(pod1)
	expectedTolerations1 := []corev1.Toleration{DefaultVKToleration}
	if !reflect.DeepEqual(pod1.Spec.Tolerations, expectedTolerations1) {
		t.Errorf("Expected tolerations to be %v, but got %v", expectedTolerations1, pod1.Spec.Tolerations)
	}

	// 测试用例2：pod.Spec.Tolerations已经有一些toleration
	pod2 := &corev1.Pod{
		Spec: corev1.PodSpec{
			Tolerations: []corev1.Toleration{
				{
					Key:   "existing-toleration",
					Value: "value",
				},
			},
		},
	}
	AddBciTolerations(pod2)
	expectedTolerations2 := []corev1.Toleration{
		{
			Key:   "existing-toleration",
			Value: "value",
		},
		DefaultVKToleration,
	}
	if !reflect.DeepEqual(pod2.Spec.Tolerations, expectedTolerations2) {
		t.Errorf("Expected tolerations to be %v, but got %v", expectedTolerations2, pod2.Spec.Tolerations)
	}

	// 测试用例3：pod.Spec.Tolerations包含默认的toleration
	pod3 := &corev1.Pod{
		Spec: corev1.PodSpec{
			Tolerations: []corev1.Toleration{
				{
					Key:   "virtual-kubelet.io/provider",
					Value: "baidu",
				},
			},
		},
	}
	AddBciTolerations(pod3)
	expectedTolerations3 := []corev1.Toleration{
		{
			Key:   "virtual-kubelet.io/provider",
			Value: "baidu",
		},
		DefaultVKToleration,
	}
	if !reflect.DeepEqual(pod3.Spec.Tolerations, expectedTolerations3) {
		t.Errorf("Expected tolerations to be %v, but got %v", expectedTolerations3, pod3.Spec.Tolerations)
	}
}

// TestMutateBciPodTolerations 是用于测试 MutateBciPodTolerations
// generated by Comate
func TestMutateBciPodTolerations(t *testing.T) {
	testCases := []struct {
		name     string
		pod      *corev1.Pod
		expected *corev1.Pod
	}{
		{
			name: "NoTolerations",
			pod: &corev1.Pod{
				Spec: corev1.PodSpec{
					Tolerations: []corev1.Toleration{},
				},
			},
			expected: &corev1.Pod{
				Spec: corev1.PodSpec{
					Tolerations: []corev1.Toleration{},
				},
			},
		},
		{
			name: "WithTolerations",
			pod: &corev1.Pod{
				Spec: corev1.PodSpec{
					Tolerations: []corev1.Toleration{
						{
							Key:    "virtual-kubelet.io/provider",
							Effect: corev1.TaintEffect("NoSchedule"),
						},
						{
							Key:    "node.kubernetes.io/not-ready",
							Effect: corev1.TaintEffect("NoExecute"),
						},
						{
							Key:    "node.kubernetes.io/unreachable",
							Effect: corev1.TaintEffect("NoExecute"),
						},
						{
							Key:    "other-toleration",
							Effect: corev1.TaintEffect("NoSchedule"),
						},
					},
				},
			},
			expected: &corev1.Pod{
				Spec: corev1.PodSpec{
					Tolerations: []corev1.Toleration{
						{
							Key:    "virtual-kubelet.io/provider",
							Effect: corev1.TaintEffect("NoSchedule"),
						},
						{
							Key:    "node.kubernetes.io/not-ready",
							Effect: corev1.TaintEffect("NoExecute"),
						},
						{
							Key:    "node.kubernetes.io/unreachable",
							Effect: corev1.TaintEffect("NoExecute"),
						},
						{
							Key:    "other-toleration",
							Effect: corev1.TaintEffect("NoSchedule"),
						},
					},
				},
			},
		},
		{
			name: "WithEmptyTolerationSeconds",
			pod: &corev1.Pod{
				Spec: corev1.PodSpec{
					Tolerations: []corev1.Toleration{
						{
							Key:    "virtual-kubelet.io/provider",
							Effect: corev1.TaintEffect("NoSchedule"),
						},
						{
							Key:    "node.kubernetes.io/not-ready",
							Effect: corev1.TaintEffect("NoExecute"),
						},
						{
							Key:    "node.kubernetes.io/unreachable",
							Effect: corev1.TaintEffect("NoExecute"),
						},
					},
				},
			},
			expected: &corev1.Pod{
				Spec: corev1.PodSpec{
					Tolerations: []corev1.Toleration{
						{
							Key:    "virtual-kubelet.io/provider",
							Effect: corev1.TaintEffect("NoSchedule"),
						},
						{
							Key:    "node.kubernetes.io/not-ready",
							Effect: corev1.TaintEffect("NoExecute"),
						},
						{
							Key:    "node.kubernetes.io/unreachable",
							Effect: corev1.TaintEffect("NoExecute"),
						},
					},
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			MutateBciPodTolerations(context.Background(), tc.pod)
			if !reflect.DeepEqual(tc.pod, tc.expected) {
				t.Errorf("Expected %v, but got %v", tc.expected, tc.pod)
			}
		})
	}
}

// TestIsBciPod 是用于测试 IsBciPod
// generated by Comate
func TestIsBciPod(t *testing.T) {
	getNodefn := func(node string) (*corev1.Node, error) {
		switch node {
		case "bci-node":
			return &corev1.Node{
				Spec: corev1.NodeSpec{
					Taints: []corev1.Taint{
						{
							Key:   DefaultVKToleration.Key,
							Value: DefaultVKToleration.Value,
						},
					},
				},
			}, nil
		default:
			return nil, fmt.Errorf("node not found")
		}
	}
	testCases := []struct {
		name   string
		pod    *corev1.Pod
		result bool
	}{
		{
			name: "Pod with node selector",
			pod: &corev1.Pod{
				Spec: corev1.PodSpec{
					NodeSelector: map[string]string{
						DefaultVKNodeSelectorKey: DefaultVKNodeSelectorValue,
					},
				},
			},
			result: true,
		},
		{
			name: "Pod with specific node name",
			pod: &corev1.Pod{
				Spec: corev1.PodSpec{
					NodeName: "bci-node",
				},
			},
			result: true,
		},
		{
			name: "Pod with toleration",
			pod: &corev1.Pod{
				Spec: corev1.PodSpec{
					Tolerations: []corev1.Toleration{
						DefaultVKToleration,
					},
				},
			},
			result: true,
		},
		{
			name: "Pod without node selector, node name or toleration",
			pod: &corev1.Pod{
				Spec: corev1.PodSpec{
					NodeSelector: map[string]string{
						"other-key": "other-value",
					},
				},
			},
			result: false,
		},
		{
			name: "Pod with node name but not a bci node",
			pod: &corev1.Pod{
				Spec: corev1.PodSpec{
					NodeName: "not-bci-node",
				},
			},
			result: false,
		},
		{
			name: "Pod with toleration but not a bci node",
			pod: &corev1.Pod{
				Spec: corev1.PodSpec{
					Tolerations: []corev1.Toleration{
						{
							Key:   "other-key",
							Value: "other-value",
						},
					},
				},
			},
			result: false,
		},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := IsBciPod(tc.pod, getNodefn)
			if result != tc.result {
				t.Errorf("Expected %v, got %v", tc.result, result)
			}
		})
	}
}

// TestIsBciPodWithTag 是用于测试 IsBciPodWithTag
// generated by Comate
func TestIsBciPodWithTag(t *testing.T) {
	testCases := []struct {
		name           string
		pod            *corev1.Pod
		getNamespaceFn func(namespace string) (*corev1.Namespace, error)
		expected       bool
	}{
		{
			name: "Pod with BCIPodTag label",
			pod: &corev1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						BCIPodTag: "true",
					},
				},
			},
			getNamespaceFn: func(namespace string) (*corev1.Namespace, error) {
				return &corev1.Namespace{
					ObjectMeta: metav1.ObjectMeta{
						Labels: map[string]string{
							BCIPodTag: "false",
						},
					},
				}, nil
			},
			expected: true,
		},
		{
			name: "Pod without BCIPodTag label",
			pod: &corev1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": "test",
					},
				},
			},
			getNamespaceFn: func(namespace string) (*corev1.Namespace, error) {
				return &corev1.Namespace{
					ObjectMeta: metav1.ObjectMeta{
						Labels: map[string]string{
							BCIPodTag: "false",
						},
					},
				}, nil
			},
			expected: false,
		},
		{
			name: "Pod with BCIPodTag label in namespace",
			pod: &corev1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": "test",
					},
				},
			},
			getNamespaceFn: func(namespace string) (*corev1.Namespace, error) {
				return &corev1.Namespace{
					ObjectMeta: metav1.ObjectMeta{
						Labels: map[string]string{
							BCIPodTag: "true",
						},
					},
				}, nil
			},
			expected: true,
		},
		{
			name: "Pod with BCIPodTag label in namespace with error",
			pod: &corev1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": "test",
					},
				},
			},
			getNamespaceFn: func(namespace string) (*corev1.Namespace, error) {
				return nil, fmt.Errorf("error")
			},
			expected: false,
		},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := IsBciPodWithTag(tc.pod, tc.getNamespaceFn)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v", tc.expected, result)
			}
		})
	}
}
