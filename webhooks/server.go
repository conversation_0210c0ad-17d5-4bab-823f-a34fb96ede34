package webhooks

import (
	"sync"

	log "k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/webhook"
	"sigs.k8s.io/controller-runtime/pkg/webhook/admission"

	"icode.baidu.com/baidu/bci2/virtual-kubelet/webhooks/mutating"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/webhooks/options"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/webhooks/runtime"
	"icode.baidu.com/baidu/bci2/virtual-kubelet/webhooks/validating"
)

var (
	handlerMap  = make(map[string]runtime.NewWebhookServer)
	handlerLock sync.RWMutex
)

func init() {
	addAdmissionHandlers(mutating.HandlerMap)
	addAdmissionHandlers(validating.HandlerMap)
}

func addAdmissionHandlers(m map[string]runtime.NewWebhookServer) {
	handlerLock.Lock()
	defer handlerLock.Unlock()
	for path, handler := range m {
		if len(path) == 0 {
			log.Warning("Skip handler with empty path")
			continue
		}
		if path[0] != '/' {
			path = "/" + path
		}
		_, found := handlerMap[path]
		if found {
			log.Warningf("conflicting webhook buildr path %v in handler map", path)
			continue
		}
		log.Infof("webhook builder path %v in handler map", path)
		handlerMap[path] = handler
	}
}

func SetupWithManager(serverOptions *options.WebhookServerRunOptions, mgr manager.Manager) error {
	handlerLock.RLock()
	defer handlerLock.RUnlock()

	hookServer := mgr.GetWebhookServer()

	decoder := admission.NewDecoder(mgr.GetScheme())
	for path, newHandler := range handlerMap {
		handler, err := newHandler(serverOptions, mgr, decoder)
		if err != nil {
			log.Errorf("failed to create webhook handler for path %v: %v", path, err)
			return err
		}
		hookServer.Register(path, &webhook.Admission{Handler: handler})
	}
	return nil
}
